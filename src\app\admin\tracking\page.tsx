"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { getSampleRequestFileUrl, getVisionBuilderImageUrl } from "@/lib/r2-upload";
import {
  Eye,
  Mail,
  Filter,
  Search,
  Calendar,
  User,
  FileText,
  Image,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Download,
  ExternalLink,
  MessageSquare,
  Tag,
  Users,
  BarChart3,
  ChevronDown,
  MoreHorizontal,
  Send,
  Paperclip,
  X,
  Check,
  Trash2,
  Archive,
  UserPlus,
  Settings
} from "lucide-react";

interface TrackingRequest {
  id: string;
  tracking_number: string;
  request_type: 'sample_request' | 'vision_builder';
  status: 'submitted' | 'processing' | 'completed' | 'cancelled';
  internal_status: 'new' | 'assigned' | 'in_progress' | 'review' | 'completed' | 'cancelled';
  name: string;
  email: string;
  project_type: string;
  description: string;
  vision_prompt: string;
  service_category: string;
  sample_type: string;
  selected_style: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  image_url: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  assigned_to: string | null;
  assigned_to_profile: { full_name: string; email: string } | null;
  admin_notes: string;
  estimated_completion_date: string;
  follow_up_required: boolean;
  next_follow_up_date: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  completed_at: string;
}

interface CommunicationModalProps {
  request: TrackingRequest | null;
  isOpen: boolean;
  onClose: () => void;
  onSent: () => void;
}

const CommunicationModal = ({ request, isOpen, onClose, onSent }: CommunicationModalProps) => {
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [sending, setSending] = useState(false);
  const [updateStatus, setUpdateStatus] = useState('');

  useEffect(() => {
    if (request && isOpen) {
      setSubject(`Update on Your ${request.request_type === 'vision_builder' ? 'Vision Builder' : 'Sample'} Request`);
      setContent(`Dear ${request.name},\n\nWe have an update on your ${request.request_type === 'vision_builder' ? 'Vision Builder' : 'Sample'} request (Tracking #${request.tracking_number}).\n\n[Your message here]\n\nBest regards,\nSeniors Architecture Firm Team`);
    }
  }, [request, isOpen]);

  const handleSend = async () => {
    if (!request || !subject.trim() || !content.trim()) return;

    setSending(true);
    try {
      const response = await fetch(`/api/admin/tracking/${request.id}/communicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subject: subject.trim(),
          content: content.trim(),
          communication_type: 'email',
          update_status: updateStatus || undefined
        })
      });

      if (response.ok) {
        onSent();
        onClose();
        setSubject('');
        setContent('');
        setUpdateStatus('');
      }
    } catch (error) {
      console.error('Error sending communication:', error);
    } finally {
      setSending(false);
    }
  };

  if (!isOpen || !request) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Send Update to {request.name}</h3>
          <Button variant="ghost" onClick={onClose}>
            <XCircle className="h-5 w-5" />
          </Button>
        </div>

        {/* Status Update */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Update Status (Optional)</label>
          <select
            value={updateStatus}
            onChange={(e) => setUpdateStatus(e.target.value)}
            className="w-full p-2 border rounded-md"
          >
            <option value="">No status change</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Subject */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Subject</label>
          <input
            type="text"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            className="w-full p-2 border rounded-md"
            placeholder="Email subject..."
          />
        </div>

        {/* Content */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Message</label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={8}
            className="w-full p-2 border rounded-md"
            placeholder="Your message..."
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSend} 
            disabled={sending || !subject.trim() || !content.trim()}
          >
            {sending ? 'Sending...' : 'Send Email'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default function TrackingManagement() {
  const { user } = useAuth();
  const [requests, setRequests] = useState<TrackingRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<TrackingRequest | null>(null);
  const [filter, setFilter] = useState<'all' | 'new' | 'assigned' | 'in_progress' | 'completed'>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | 'vision_builder' | 'sample_request'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCommunicationModal, setShowCommunicationModal] = useState(false);
  const [communicationRequest, setCommunicationRequest] = useState<TrackingRequest | null>(null);

  // New state for enhanced functionality
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewRequest, setViewRequest] = useState<TrackingRequest | null>(null);
  const [selectedRequests, setSelectedRequests] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Bulk action states
  const [showBulkConfirmation, setShowBulkConfirmation] = useState(false);
  const [bulkAction, setBulkAction] = useState<'assign' | 'archive' | 'delete' | null>(null);
  const [bulkLoading, setBulkLoading] = useState(false);
  const [assignToUser, setAssignToUser] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');

  // Assignable users state
  const [assignableUsers, setAssignableUsers] = useState<Array<{
    id: string;
    full_name: string;
    email: string;
    role: string;
    avatar_url?: string;
  }>>([]);

  // Status update states
  const [showStatusUpdate, setShowStatusUpdate] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [newPriority, setNewPriority] = useState('');
  const [adminNotes, setAdminNotes] = useState('');
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);

  const [stats, setStats] = useState({
    total: 0,
    new: 0,
    in_progress: 0,
    completed: 0,
    vision_builder: 0,
    sample_requests: 0
  });

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    fetchRequests();
    fetchStats();
    fetchAssignableUsers();
  }, [filter, typeFilter]);

  useEffect(() => {
    if (searchTerm) {
      const filtered = requests.filter(request =>
        request.tracking_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setRequests(filtered);
    } else {
      fetchRequests();
    }
  }, [searchTerm]);

  // Bulk action handlers
  const handleSelectAll = () => {
    if (selectedRequests.size === requests.length) {
      setSelectedRequests(new Set());
    } else {
      setSelectedRequests(new Set(requests.map(r => r.id)));
    }
  };

  const handleSelectRequest = (requestId: string) => {
    const newSelected = new Set(selectedRequests);
    if (newSelected.has(requestId)) {
      newSelected.delete(requestId);
    } else {
      newSelected.add(requestId);
    }
    setSelectedRequests(newSelected);
  };

  const handleBulkAction = (action: 'assign' | 'archive' | 'delete') => {
    setBulkAction(action);
    setShowBulkConfirmation(true);
  };

  const executeBulkAction = async () => {
    if (!bulkAction || selectedRequests.size === 0) return;

    setBulkLoading(true);
    try {
      const requestIds = Array.from(selectedRequests);
      const payload: any = {
        action: bulkAction,
        requestIds
      };

      if (bulkAction === 'assign' && assignToUser) {
        payload.assignTo = assignToUser;
      }

      const response = await fetch('/api/admin/tracking/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        showToastMessage(`Successfully ${bulkAction}ed ${requestIds.length} request(s)`, 'success');
        setSelectedRequests(new Set());
        fetchRequests(); // Refresh data
      } else {
        showToastMessage(result.error || `Failed to ${bulkAction} requests`, 'error');
      }
    } catch (error) {
      console.error('Bulk action error:', error);
      showToastMessage(`Error performing ${bulkAction} action`, 'error');
    } finally {
      setBulkLoading(false);
      setShowBulkConfirmation(false);
      setBulkAction(null);
      setAssignToUser('');
    }
  };

  const showToastMessage = (message: string, type: 'success' | 'error') => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 5000);
  };

  // Helper function to get proper file URL
  const getFileUrl = (request: TrackingRequest, isImage: boolean = false) => {
    if (isImage && request.image_url) {
      // For images, check if it's already a full URL or needs conversion
      if (request.image_url.startsWith('http')) {
        return request.image_url;
      }
      return getVisionBuilderImageUrl(request.image_url);
    }

    if (request.file_path) {
      // For files, check if it's already a full URL or needs conversion
      if (request.file_path.startsWith('http')) {
        return request.file_path;
      }
      return getSampleRequestFileUrl(request.file_path);
    }

    return null;
  };

  // View modal handlers
  const handleViewRequest = (request: TrackingRequest) => {
    setViewRequest(request);
    setNewStatus(request.internal_status || request.status);
    setNewPriority(request.priority || 'normal');
    setAdminNotes(request.admin_notes || '');
    setShowViewModal(true);
  };

  // Status update handler
  const handleStatusUpdate = async () => {
    if (!viewRequest) return;

    setStatusUpdateLoading(true);
    try {
      const response = await fetch(`/api/admin/tracking/${viewRequest.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          priority: newPriority,
          adminNotes: adminNotes,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        // Update the viewRequest state
        setViewRequest({
          ...viewRequest,
          internal_status: newStatus,
          priority: newPriority,
          admin_notes: adminNotes,
        });

        showToastMessage('Status updated successfully', 'success');
        setShowStatusUpdate(false);
        fetchRequests(); // Refresh the main table
      } else {
        showToastMessage(result.error || 'Failed to update status', 'error');
      }
    } catch (error) {
      console.error('Status update error:', error);
      showToastMessage('Error updating status', 'error');
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  const fetchRequests = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (filter !== 'all') params.append('status', filter);
      if (typeFilter !== 'all') params.append('request_type', typeFilter);

      console.log('Fetching tracking requests with params:', params.toString());
      const response = await fetch(`/api/admin/tracking?${params.toString()}`);
      const data = await response.json();

      console.log('API response:', { status: response.status, data });

      if (response.ok) {
        console.log(`Setting ${data.requests?.length || 0} requests`);
        setRequests(data.requests || []);

        // If no requests found and we're looking for all, show a helpful message
        if ((!data.requests || data.requests.length === 0) && filter === 'all' && typeFilter === 'all') {
          console.log('No tracking requests found - this might indicate the database table is empty or doesn\'t exist');
        }
      } else {
        console.error('API error:', data);
        setRequests([]);

        // If it's a database error, show mock data for demonstration
        if (data.message && data.message.includes('Database table not found')) {
          console.log('Database table not found, showing mock data for demonstration');
          const mockRequests: TrackingRequest[] = [
            {
              id: 'mock-1',
              tracking_number: 'TR-DEMO-MOCK-0001',
              request_type: 'sample_request',
              status: 'submitted',
              internal_status: 'new',
              name: 'Demo User',
              email: '<EMAIL>',
              project_type: 'Creative Design & Branding',
              description: 'This is a demo sample request to show the admin interface',
              vision_prompt: '',
              service_category: 'Creative Design & Branding',
              sample_type: 'Brand concept visualization',
              selected_style: '',
              file_path: '',
              file_name: '',
              file_type: '',
              file_size: 0,
              image_url: '',
              priority: 'normal',
              assigned_to: null,
              assigned_to_profile: null,
              admin_notes: '',
              estimated_completion_date: '',
              follow_up_required: false,
              next_follow_up_date: '',
              tags: [],
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              completed_at: ''
            }
          ];
          setRequests(mockRequests);
        }
      }
    } catch (error) {
      console.error('Error fetching tracking requests:', error);
      setRequests([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      console.log('Fetching tracking stats...');
      const response = await fetch('/api/admin/tracking/stats');
      const data = await response.json();

      if (response.ok) {
        console.log('Stats fetched successfully:', data);
        setStats({
          total: data.total || 0,
          new: data.new || 0,
          in_progress: data.in_progress || 0,
          completed: data.completed || 0,
          vision_builder: data.vision_builder || 0,
          sample_requests: data.sample_requests || 0
        });
      } else {
        console.error('Error fetching stats:', data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchAssignableUsers = async () => {
    try {
      console.log('Fetching assignable users...');
      const response = await fetch('/api/admin/users/assignable');
      const data = await response.json();

      if (response.ok) {
        console.log('Assignable users fetched successfully:', data.users);
        setAssignableUsers(data.users || []);
      } else {
        console.error('Error fetching assignable users:', data);
        // Fallback to empty array if fetch fails
        setAssignableUsers([]);
      }
    } catch (error) {
      console.error('Error fetching assignable users:', error);
      setAssignableUsers([]);
    }
  };



  const handleCommunicate = (request: TrackingRequest) => {
    setCommunicationRequest(request);
    setShowCommunicationModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
      case 'submitted': // Treat submitted as new
        return 'bg-blue-100 text-blue-800 border border-blue-200';
      case 'assigned':
        return 'bg-amber-100 text-amber-800 border border-amber-200';
      case 'in_progress':
      case 'processing': // Handle processing status
        return 'bg-orange-100 text-orange-800 border border-orange-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border border-orange-200';
      case 'normal':
        return 'bg-amber-100 text-amber-800 border border-amber-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border border-gray-200';
      default:
        return 'bg-amber-100 text-amber-800 border border-amber-200';
    }
  };

  const filteredRequests = requests.filter(request =>
    request.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.tracking_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.project_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-amber-900 mb-2">Tracking Management</h1>
          <p className="text-amber-700">Monitor and manage all tracking requests</p>
        </div>

        {/* Stats Cards - Vertical Layout for Better Text Display */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-6 mb-8">
          {/* Total Requests Card */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 lg:p-5 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-center">
              <div className="w-12 h-12 lg:w-14 lg:h-14 bg-amber-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <BarChart3 className="h-6 w-6 lg:h-7 lg:w-7 text-amber-600" />
              </div>
              <div>
                <p className="text-xs lg:text-sm font-medium text-amber-600 mb-1">Total Requests</p>
                <p className="text-xl lg:text-2xl xl:text-3xl font-bold text-amber-900">{stats.total}</p>
              </div>
            </div>
          </motion.div>

          {/* New Requests Card */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 lg:p-5 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-center">
              <div className="w-12 h-12 lg:w-14 lg:h-14 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Clock className="h-6 w-6 lg:h-7 lg:w-7 text-blue-600" />
              </div>
              <div>
                <p className="text-xs lg:text-sm font-medium text-blue-600 mb-1">New Requests</p>
                <p className="text-xl lg:text-2xl xl:text-3xl font-bold text-blue-900">{stats.new}</p>
              </div>
            </div>
          </motion.div>

          {/* In Progress Card */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 lg:p-5 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-center">
              <div className="w-12 h-12 lg:w-14 lg:h-14 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <AlertTriangle className="h-6 w-6 lg:h-7 lg:w-7 text-orange-600" />
              </div>
              <div>
                <p className="text-xs lg:text-sm font-medium text-orange-600 mb-1">In Progress</p>
                <p className="text-xl lg:text-2xl xl:text-3xl font-bold text-orange-900">{stats.in_progress}</p>
              </div>
            </div>
          </motion.div>

          {/* Completed Card */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 lg:p-5 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-center">
              <div className="w-12 h-12 lg:w-14 lg:h-14 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="h-6 w-6 lg:h-7 lg:w-7 text-green-600" />
              </div>
              <div>
                <p className="text-xs lg:text-sm font-medium text-green-600 mb-1">Completed</p>
                <p className="text-xl lg:text-2xl xl:text-3xl font-bold text-green-900">{stats.completed}</p>
              </div>
            </div>
          </motion.div>

          {/* Vision Builder Card */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 lg:p-5 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-center">
              <div className="w-12 h-12 lg:w-14 lg:h-14 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <Image className="h-6 w-6 lg:h-7 lg:w-7 text-purple-600" />
              </div>
              <div>
                <p className="text-xs lg:text-sm font-medium text-purple-600 mb-1">Vision Builder</p>
                <p className="text-xl lg:text-2xl xl:text-3xl font-bold text-purple-900">{stats.vision_builder}</p>
              </div>
            </div>
          </motion.div>

          {/* Sample Requests Card */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 lg:p-5 hover:shadow-md transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-center">
              <div className="w-12 h-12 lg:w-14 lg:h-14 bg-indigo-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <FileText className="h-6 w-6 lg:h-7 lg:w-7 text-indigo-600" />
              </div>
              <div>
                <p className="text-xs lg:text-sm font-medium text-indigo-600 mb-1">Sample Requests</p>
                <p className="text-xl lg:text-2xl xl:text-3xl font-bold text-indigo-900">{stats.sample_requests}</p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bulk Actions Bar */}
        <AnimatePresence>
          {selectedRequests.size > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-amber-100 border border-amber-200 rounded-xl p-4 mb-6"
            >
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="flex items-center gap-3">
                  <span className="text-amber-800 font-medium">
                    {selectedRequests.size} request{selectedRequests.size !== 1 ? 's' : ''} selected
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedRequests(new Set())}
                    className="text-amber-700 border-amber-300 hover:bg-amber-200"
                  >
                    Clear Selection
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleBulkAction('assign')}
                    className="bg-amber-600 hover:bg-amber-700 text-white"
                  >
                    <UserPlus className="h-4 w-4 mr-1" />
                    Assign
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => handleBulkAction('archive')}
                    variant="outline"
                    className="text-amber-700 border-amber-300 hover:bg-amber-200"
                  >
                    <Archive className="h-4 w-4 mr-1" />
                    Archive
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => handleBulkAction('delete')}
                    variant="outline"
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Responsive Filters and Search */}
        <div className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 sm:p-6 mb-6">
          {/* Status Filter Buttons */}
          <div className="mb-4 sm:mb-6">
            <h3 className="text-sm font-medium text-amber-900 mb-3">Filter by Status</h3>
            <div className="flex flex-wrap gap-2">
              {['all', 'new', 'assigned', 'in_progress', 'completed'].map((status) => (
                <Button
                  key={status}
                  variant={filter === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter(status as any)}
                  className={filter === status
                    ? 'bg-amber-600 hover:bg-amber-700 text-white border-amber-600'
                    : 'text-amber-700 border-amber-300 hover:bg-amber-50'
                  }
                >
                  {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
                </Button>
              ))}
            </div>
          </div>

          {/* Type Filter and Search */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex-shrink-0">
              <label className="block text-sm font-medium text-amber-900 mb-2">Request Type</label>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as any)}
                className="px-3 py-2 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white text-amber-900"
              >
                <option value="all">All Types</option>
                <option value="vision_builder">Vision Builder</option>
                <option value="sample_request">Sample Request</option>
              </select>
            </div>

            <div className="flex-1 w-full sm:w-auto">
              <label className="block text-sm font-medium text-amber-900 mb-2">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search by tracking number, name, or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 bg-white text-amber-900 placeholder-amber-400"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Responsive Table/Cards */}
        {isMobile ? (
          /* Mobile Card Layout */
          <div className="space-y-4">
            {filteredRequests.map((request) => (
              <motion.div
                key={request.id}
                className="bg-white rounded-xl shadow-sm border border-amber-100 p-4 hover:shadow-md transition-shadow"
                whileHover={{ scale: 1.01 }}
                transition={{ duration: 0.2 }}
              >
                {/* Card Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedRequests.has(request.id)}
                      onChange={() => handleSelectRequest(request.id)}
                      className="rounded border-amber-300 text-amber-600 focus:ring-amber-500"
                    />
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                        {request.request_type === 'vision_builder' ? (
                          <Image className="h-5 w-5 text-amber-600" />
                        ) : (
                          <FileText className="h-5 w-5 text-amber-600" />
                        )}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-amber-900">
                        {request.name}
                      </div>
                      <div className="text-xs text-amber-600">
                        #{request.tracking_number}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button
                      onClick={() => handleViewRequest(request)}
                      variant="outline"
                      size="sm"
                      className="text-amber-700 border-amber-300 hover:bg-amber-50"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={() => handleCommunicate(request)}
                      variant="outline"
                      size="sm"
                      className="text-amber-700 border-amber-300 hover:bg-amber-50"
                    >
                      <Mail className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Card Content */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-amber-600">Type</span>
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      {request.request_type === 'vision_builder' ? 'Vision Builder' : 'Sample Request'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-amber-600">Status</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.internal_status || request.status)}`}>
                      {(() => {
                        const displayStatus = request.internal_status || request.status;
                        if (displayStatus === 'submitted' && !request.internal_status) {
                          return 'new';
                        }
                        return displayStatus.replace('_', ' ');
                      })()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-amber-600">Priority</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(request.priority || 'normal')}`}>
                      {request.priority || 'normal'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-amber-600">Created</span>
                    <span className="text-xs text-amber-800">
                      {new Date(request.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          /* Desktop Table Layout */
          <div className="bg-white rounded-xl shadow-sm border border-amber-100 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-amber-200">
                <thead className="bg-amber-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedRequests.size === requests.length && requests.length > 0}
                        onChange={handleSelectAll}
                        className="rounded border-amber-300 text-amber-600 focus:ring-amber-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-amber-700 uppercase tracking-wider">
                      Request
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-amber-700 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-amber-700 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-amber-700 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-amber-700 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-amber-700 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-amber-100">
                  {filteredRequests.map((request) => (
                    <motion.tr
                      key={request.id}
                      className="hover:bg-amber-50 transition-colors"
                      whileHover={{ backgroundColor: "rgb(254 243 199)" }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedRequests.has(request.id)}
                          onChange={() => handleSelectRequest(request.id)}
                          className="rounded border-amber-300 text-amber-600 focus:ring-amber-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                              {request.request_type === 'vision_builder' ? (
                                <Image className="h-5 w-5 text-amber-600" />
                              ) : (
                                <FileText className="h-5 w-5 text-amber-600" />
                              )}
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-amber-900">
                              {request.name}
                            </div>
                            <div className="text-sm text-amber-600">
                              #{request.tracking_number}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {request.request_type === 'vision_builder' ? 'Vision Builder' : 'Sample Request'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.internal_status || request.status)}`}>
                          {(() => {
                            const displayStatus = request.internal_status || request.status;
                            if (displayStatus === 'submitted' && !request.internal_status) {
                              return 'new';
                            }
                            return displayStatus.replace('_', ' ');
                          })()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(request.priority || 'normal')}`}>
                          {request.priority || 'normal'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-amber-700">
                        {new Date(request.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <Button
                            onClick={() => handleViewRequest(request)}
                            variant="outline"
                            size="sm"
                            className="text-amber-700 border-amber-300 hover:bg-amber-50"
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>

                          <Button
                            onClick={() => handleCommunicate(request)}
                            variant="outline"
                            size="sm"
                            className="text-amber-700 border-amber-300 hover:bg-amber-50"
                          >
                            <Mail className="h-4 w-4 mr-1" />
                            Contact
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Bulk Action Confirmation Modal */}
        <AnimatePresence>
          {showBulkConfirmation && bulkAction && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
              onClick={() => setShowBulkConfirmation(false)}
            >
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                className="bg-white rounded-xl shadow-xl w-full max-w-md"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                      bulkAction === 'delete' ? 'bg-red-100' :
                      bulkAction === 'archive' ? 'bg-amber-100' : 'bg-blue-100'
                    }`}>
                      {bulkAction === 'delete' ? (
                        <Trash2 className={`h-6 w-6 text-red-600`} />
                      ) : bulkAction === 'archive' ? (
                        <Archive className={`h-6 w-6 text-amber-600`} />
                      ) : (
                        <UserPlus className={`h-6 w-6 text-blue-600`} />
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-amber-900">
                        {bulkAction === 'delete' ? 'Delete Requests' :
                         bulkAction === 'archive' ? 'Archive Requests' : 'Assign Requests'}
                      </h3>
                      <p className="text-amber-600">
                        {selectedRequests.size} request{selectedRequests.size !== 1 ? 's' : ''} selected
                      </p>
                    </div>
                  </div>

                  <div className="mb-6">
                    {bulkAction === 'delete' && (
                      <p className="text-red-700 bg-red-50 p-3 rounded-lg">
                        <strong>Warning:</strong> This action cannot be undone. The selected requests will be permanently deleted.
                      </p>
                    )}
                    {bulkAction === 'archive' && (
                      <p className="text-amber-700 bg-amber-50 p-3 rounded-lg">
                        The selected requests will be archived and moved out of the active queue.
                      </p>
                    )}
                    {bulkAction === 'assign' && (
                      <div className="space-y-3">
                        <p className="text-blue-700 bg-blue-50 p-3 rounded-lg">
                          Assign the selected requests to a team member.
                        </p>
                        <div>
                          <label className="block text-sm font-medium text-amber-900 mb-2">
                            Assign to:
                          </label>
                          <select
                            value={assignToUser}
                            onChange={(e) => setAssignToUser(e.target.value)}
                            className="w-full px-3 py-2 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                            required
                          >
                            <option value="">Select team member...</option>
                            {assignableUsers.map((user) => (
                              <option key={user.id} value={user.id}>
                                {user.full_name} ({user.role})
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowBulkConfirmation(false)}
                      className="flex-1 text-amber-700 border-amber-300 hover:bg-amber-50"
                      disabled={bulkLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={executeBulkAction}
                      disabled={bulkLoading || (bulkAction === 'assign' && !assignToUser)}
                      className={`flex-1 ${
                        bulkAction === 'delete'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-amber-600 hover:bg-amber-700 text-white'
                      }`}
                    >
                      {bulkLoading ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Processing...
                        </div>
                      ) : (
                        `${bulkAction === 'delete' ? 'Delete' :
                           bulkAction === 'archive' ? 'Archive' : 'Assign'}
                         ${selectedRequests.size} Request${selectedRequests.size !== 1 ? 's' : ''}`
                      )}
                    </Button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* View Request Modal */}
        <AnimatePresence>
          {showViewModal && viewRequest && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
              onClick={() => setShowViewModal(false)}
            >
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Modal Header */}
                <div className="bg-amber-50 border-b border-amber-200 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-xl font-semibold text-amber-900">Request Details</h2>
                      <p className="text-amber-600">#{viewRequest.tracking_number}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowViewModal(false)}
                      className="text-amber-700 border-amber-300 hover:bg-amber-100"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Modal Content */}
                <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
                  <div className="p-6 space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-amber-900 border-b border-amber-200 pb-2">
                          Client Information
                        </h3>
                        <div className="space-y-3">
                          <div>
                            <label className="text-sm font-medium text-amber-700">Name</label>
                            <p className="text-amber-900">{viewRequest.name}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-amber-700">Email</label>
                            <p className="text-amber-900">{viewRequest.email}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-amber-700">Project Type</label>
                            <p className="text-amber-900">{viewRequest.project_type}</p>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-amber-900 border-b border-amber-200 pb-2">
                          Request Status
                        </h3>
                        <div className="space-y-3">
                          <div>
                            <label className="text-sm font-medium text-amber-700">Current Status</label>
                            <div className="mt-1">
                              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(viewRequest.internal_status || viewRequest.status)}`}>
                                {(() => {
                                  const displayStatus = viewRequest.internal_status || viewRequest.status;
                                  if (displayStatus === 'submitted' && !viewRequest.internal_status) {
                                    return 'new';
                                  }
                                  return displayStatus.replace('_', ' ');
                                })()}
                              </span>
                            </div>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-amber-700">Priority</label>
                            <div className="mt-1">
                              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getPriorityColor(viewRequest.priority || 'normal')}`}>
                                {viewRequest.priority || 'normal'}
                              </span>
                            </div>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-amber-700">Created</label>
                            <p className="text-amber-900">{new Date(viewRequest.created_at).toLocaleString()}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Request Details */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-amber-900 border-b border-amber-200 pb-2">
                        Request Details
                      </h3>
                      {viewRequest.description && (
                        <div>
                          <label className="text-sm font-medium text-amber-700">Description</label>
                          <p className="text-amber-900 mt-1 p-3 bg-amber-50 rounded-lg">{viewRequest.description}</p>
                        </div>
                      )}
                      {viewRequest.vision_prompt && (
                        <div>
                          <label className="text-sm font-medium text-amber-700">Vision Prompt</label>
                          <p className="text-amber-900 mt-1 p-3 bg-amber-50 rounded-lg">{viewRequest.vision_prompt}</p>
                        </div>
                      )}
                      {viewRequest.service_category && (
                        <div>
                          <label className="text-sm font-medium text-amber-700">Service Category</label>
                          <p className="text-amber-900">{viewRequest.service_category}</p>
                        </div>
                      )}
                    </div>

                    {/* Files and Images */}
                    {(viewRequest.file_name || viewRequest.image_url) && (
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-amber-900 border-b border-amber-200 pb-2">
                          Attachments
                        </h3>

                        {/* File Attachments */}
                        {viewRequest.file_name && (
                          <div className="bg-amber-50 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center">
                                  {viewRequest.file_type?.includes('pdf') ? (
                                    <FileText className="h-5 w-5 text-red-600" />
                                  ) : viewRequest.file_type?.includes('image') ? (
                                    <Image className="h-5 w-5 text-green-600" />
                                  ) : (
                                    <Paperclip className="h-5 w-5 text-amber-600" />
                                  )}
                                </div>
                                <div>
                                  <p className="text-amber-900 font-medium">{viewRequest.file_name}</p>
                                  <div className="flex items-center space-x-2 text-xs text-amber-600">
                                    {viewRequest.file_type && (
                                      <span>{viewRequest.file_type.toUpperCase()}</span>
                                    )}
                                    {viewRequest.file_size && (
                                      <span>• {(viewRequest.file_size / 1024 / 1024).toFixed(2)} MB</span>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                {viewRequest.file_path && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-amber-700 border-amber-300 hover:bg-amber-100"
                                    onClick={() => {
                                      const fileUrl = getFileUrl(viewRequest);
                                      if (fileUrl) {
                                        const link = document.createElement('a');
                                        link.href = fileUrl;
                                        link.download = viewRequest.file_name!;
                                        link.click();
                                      }
                                    }}
                                  >
                                    <Download className="h-4 w-4 mr-1" />
                                    Download
                                  </Button>
                                )}
                                {viewRequest.file_type?.includes('image') && viewRequest.file_path && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-amber-700 border-amber-300 hover:bg-amber-100"
                                    onClick={() => {
                                      const fileUrl = getFileUrl(viewRequest);
                                      if (fileUrl) {
                                        window.open(fileUrl, '_blank');
                                      }
                                    }}
                                  >
                                    <Eye className="h-4 w-4 mr-1" />
                                    Preview
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Image Gallery */}
                        {viewRequest.image_url && (
                          <div className="space-y-3">
                            <label className="text-sm font-medium text-amber-700">Generated Vision</label>
                            <div className="relative group">
                              <img
                                src={getFileUrl(viewRequest, true) || viewRequest.image_url}
                                alt="Generated vision"
                                className="w-full max-w-md h-auto rounded-lg border border-amber-200 cursor-pointer hover:shadow-lg transition-shadow"
                                onClick={() => {
                                  const imageUrl = getFileUrl(viewRequest, true) || viewRequest.image_url;
                                  if (imageUrl) {
                                    window.open(imageUrl, '_blank');
                                  }
                                }}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = '/placeholder-image.png';
                                  target.alt = 'Image not available';
                                  target.className = 'w-full max-w-md h-48 rounded-lg border border-amber-200 bg-amber-50 flex items-center justify-center text-amber-600';
                                }}
                              />
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div className="bg-white rounded-full p-2 shadow-lg">
                                  <ExternalLink className="h-5 w-5 text-amber-600" />
                                </div>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-amber-700 border-amber-300 hover:bg-amber-100"
                                onClick={() => {
                                  const imageUrl = getFileUrl(viewRequest, true) || viewRequest.image_url;
                                  if (imageUrl) {
                                    window.open(imageUrl, '_blank');
                                  }
                                }}
                              >
                                <ExternalLink className="h-4 w-4 mr-1" />
                                View Full Size
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-amber-700 border-amber-300 hover:bg-amber-100"
                                onClick={() => {
                                  const imageUrl = getFileUrl(viewRequest, true) || viewRequest.image_url;
                                  if (imageUrl) {
                                    const link = document.createElement('a');
                                    link.href = imageUrl;
                                    link.download = `vision-${viewRequest.tracking_number}.jpg`;
                                    link.click();
                                  }
                                }}
                              >
                                <Download className="h-4 w-4 mr-1" />
                                Download
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Quick Actions */}
                    <div className="flex flex-wrap gap-3 pt-4 border-t border-amber-200">
                      <Button
                        onClick={() => {
                          setShowViewModal(false);
                          handleCommunicate(viewRequest);
                        }}
                        className="bg-amber-600 hover:bg-amber-700 text-white"
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Send Message
                      </Button>
                      <Button
                        onClick={() => setShowStatusUpdate(!showStatusUpdate)}
                        variant="outline"
                        className="text-amber-700 border-amber-300 hover:bg-amber-50"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        {showStatusUpdate ? 'Cancel Update' : 'Update Status'}
                      </Button>
                      <Button
                        variant="outline"
                        className="text-amber-700 border-amber-300 hover:bg-amber-50"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Export Details
                      </Button>
                    </div>

                    {/* Status Update Form */}
                    <AnimatePresence>
                      {showStatusUpdate && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mt-6 pt-6 border-t border-amber-200"
                        >
                          <h4 className="text-lg font-medium text-amber-900 mb-4">Update Request Status</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                              <label className="block text-sm font-medium text-amber-700 mb-2">
                                Status
                              </label>
                              <select
                                value={newStatus}
                                onChange={(e) => setNewStatus(e.target.value)}
                                className="w-full px-3 py-2 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                              >
                                <option value="new">New</option>
                                <option value="assigned">Assigned</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-amber-700 mb-2">
                                Priority
                              </label>
                              <select
                                value={newPriority}
                                onChange={(e) => setNewPriority(e.target.value)}
                                className="w-full px-3 py-2 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                              >
                                <option value="low">Low</option>
                                <option value="normal">Normal</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                              </select>
                            </div>
                          </div>
                          <div className="mb-4">
                            <label className="block text-sm font-medium text-amber-700 mb-2">
                              Admin Notes
                            </label>
                            <textarea
                              value={adminNotes}
                              onChange={(e) => setAdminNotes(e.target.value)}
                              rows={3}
                              className="w-full px-3 py-2 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                              placeholder="Add notes about this status update..."
                            />
                          </div>
                          <div className="flex space-x-3">
                            <Button
                              onClick={() => setShowStatusUpdate(false)}
                              variant="outline"
                              className="text-amber-700 border-amber-300 hover:bg-amber-50"
                              disabled={statusUpdateLoading}
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={handleStatusUpdate}
                              disabled={statusUpdateLoading}
                              className="bg-amber-600 hover:bg-amber-700 text-white"
                            >
                              {statusUpdateLoading ? (
                                <div className="flex items-center">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                  Updating...
                                </div>
                              ) : (
                                <>
                                  <Check className="h-4 w-4 mr-2" />
                                  Update Status
                                </>
                              )}
                            </Button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Communication Modal */}
        <CommunicationModal
          request={communicationRequest}
          isOpen={showCommunicationModal}
          onClose={() => {
            setShowCommunicationModal(false);
            setCommunicationRequest(null);
          }}
          onSent={() => {
            fetchRequests();
            console.log('Communication sent successfully');
          }}
        />

        {/* Toast Notification */}
        <AnimatePresence>
          {showToast && (
            <motion.div
              initial={{ opacity: 0, y: 50, x: '-50%' }}
              animate={{ opacity: 1, y: 0, x: '-50%' }}
              exit={{ opacity: 0, y: 50, x: '-50%' }}
              className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 px-6 py-4 rounded-lg shadow-lg max-w-md w-full mx-4 ${
                toastType === 'success'
                  ? 'bg-green-600 text-white'
                  : 'bg-red-600 text-white'
              }`}
            >
              <div className="flex items-center">
                {toastType === 'success' ? (
                  <CheckCircle className="h-5 w-5 mr-3 flex-shrink-0" />
                ) : (
                  <XCircle className="h-5 w-5 mr-3 flex-shrink-0" />
                )}
                <p className="text-sm font-medium">{toastMessage}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowToast(false)}
                  className="ml-auto text-white hover:bg-white hover:bg-opacity-20 p-1"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
