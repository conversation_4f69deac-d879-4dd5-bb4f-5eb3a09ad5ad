export type DisputeStatus = 'open' | 'under_review' | 'resolved' | 'closed';

export interface Dispute {
  id: string;
  project_id: string;
  client_id: string;
  designer_id: string;
  created_by: string;
  status: DisputeStatus;
  title: string;
  description: string;
  resolution_notes: string | null;
  resolved_by: string | null;
  resolved_at: string | null;
  created_at: string;
  updated_at: string;
  projects: {
    title: string;
  };
  client: {
    full_name: string;
    avatar_url: string | null;
  };
  designer: {
    full_name: string;
    avatar_url: string | null;
  };
  creator: {
    full_name: string;
    role: string;
  };
  resolver?: {
    full_name: string;
    role: string;
  } | null;
}

export interface DisputeMessage {
  id: string;
  dispute_id: string;
  sender_id: string;
  content: string;
  is_read: boolean;
  attachment_url: string | null;
  attachment_name: string | null;
  attachment_type: string | null;
  created_at: string;
  profiles: {
    full_name: string;
    avatar_url: string | null;
    role: string;
  };
}

export interface DisputeAttachment {
  id: string;
  dispute_id: string;
  file_url: string;
  file_name: string;
  file_type: string | null;
  file_size: number | null;
  uploaded_by: string;
  created_at: string;
  profiles: {
    full_name: string;
    role: string;
  };
}

export interface DisputeWithDetails {
  dispute: Dispute;
  messages: DisputeMessage[];
  attachments: DisputeAttachment[];
}

export interface CreateDisputeParams {
  projectId: string;
  title: string;
  description: string;
}

export interface UpdateDisputeParams {
  status?: DisputeStatus;
  resolutionNotes?: string;
}

export interface CreateDisputeMessageParams {
  content: string;
  attachmentUrl?: string;
  attachmentName?: string;
  attachmentType?: string;
}

export interface CreateDisputeAttachmentParams {
  fileUrl: string;
  fileName: string;
  fileType?: string;
  fileSize?: number;
}
