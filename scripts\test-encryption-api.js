#!/usr/bin/env node

/**
 * Test the encryption API endpoint
 * This script tests the /api/encrypt-payment-data endpoint to ensure it works correctly
 * 
 * Usage:
 *   node scripts/test-encryption-api.js
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:3001';

async function makeRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const requestOptions = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

async function testEncryptionAPI() {
  console.log('🧪 Testing Encryption API');
  console.log('=' .repeat(40));
  console.log();

  try {
    // Test 1: Check encryption status (no auth required)
    console.log('1. Testing encryption status endpoint...');
    const statusResponse = await makeRequest('/api/encrypt-payment-data', {
      method: 'GET'
    });

    if (statusResponse.status === 200) {
      console.log('✅ Status endpoint working');
      console.log('   Encryption configured:', statusResponse.data.encryption_configured);
      console.log('   Backup key configured:', statusResponse.data.backup_key_configured);
      console.log('   Status:', statusResponse.data.status);
    } else {
      console.log('❌ Status endpoint failed:', statusResponse.status);
      console.log('   Response:', statusResponse.data);
    }

    console.log();

    // Test 2: Test encryption without auth (should fail)
    console.log('2. Testing encryption without auth (should fail)...');
    const noAuthResponse = await makeRequest('/api/encrypt-payment-data', {
      method: 'POST',
      body: {
        action: 'encrypt',
        data: { account_number: '**********' }
      }
    });

    if (noAuthResponse.status === 401) {
      console.log('✅ Properly rejected unauthorized request');
    } else {
      console.log('❌ Should have rejected unauthorized request');
      console.log('   Status:', noAuthResponse.status);
      console.log('   Response:', noAuthResponse.data);
    }

    console.log();

    // Test 3: Test with invalid action
    console.log('3. Testing with invalid action...');
    const invalidActionResponse = await makeRequest('/api/encrypt-payment-data', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer fake-token'
      },
      body: {
        action: 'invalid',
        data: { account_number: '**********' }
      }
    });

    if (invalidActionResponse.status === 401 || invalidActionResponse.status === 400) {
      console.log('✅ Properly handled invalid request');
    } else {
      console.log('❌ Should have rejected invalid action');
      console.log('   Status:', invalidActionResponse.status);
      console.log('   Response:', invalidActionResponse.data);
    }

    console.log();
    console.log('📋 API Test Summary:');
    console.log('- Encryption API endpoint is accessible');
    console.log('- Authentication is properly enforced');
    console.log('- Error handling is working');
    console.log();
    console.log('🔐 To test with real authentication:');
    console.log('1. Log in to your application');
    console.log('2. Try adding a payment method');
    console.log('3. Check browser network tab for API calls');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log();
    console.log('💡 Make sure your development server is running on port 3001');
    console.log('   Run: npm run dev');
  }
}

if (require.main === module) {
  testEncryptionAPI();
}

module.exports = { testEncryptionAPI };
