-- =====================================================
-- FIX API ISSUES SCRIPT
-- Addresses authentication and foreign key issues
-- =====================================================

-- 1. CHECK AND FIX FOREIGN KEY RELATIONSHIPS
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔧 FIXING API ISSUES...';
    RAISE NOTICE '';
    
    -- Check if foreign key constraints exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_escrow_releases_hold'
    ) THEN
        -- Add missing foreign key constraint
        ALTER TABLE escrow_releases 
        ADD CONSTRAINT fk_escrow_releases_hold 
        FOREIGN KEY (escrow_hold_id) REFERENCES escrow_holds(id);
        
        RAISE NOTICE '✅ Added foreign key constraint: escrow_releases -> escrow_holds';
    ELSE
        RAISE NOTICE '✅ Foreign key constraint already exists: escrow_releases -> escrow_holds';
    END IF;
    
    -- Check if project_assignments table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'project_assignments'
    ) THEN
        -- Create project_assignments table
        CREATE TABLE project_assignments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
            manager_id UUID NOT NULL REFERENCES profiles(id),
            status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'completed')),
            priority VARCHAR(50) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
            assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            notes TEXT,
            created_by UUID REFERENCES profiles(id),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            UNIQUE(project_id, status) -- Only one active assignment per project
        );
        
        -- Create indexes
        CREATE INDEX idx_project_assignments_project_id ON project_assignments(project_id);
        CREATE INDEX idx_project_assignments_manager_id ON project_assignments(manager_id);
        CREATE INDEX idx_project_assignments_status ON project_assignments(status);
        
        -- Enable RLS
        ALTER TABLE project_assignments ENABLE ROW LEVEL SECURITY;
        
        RAISE NOTICE '✅ Created project_assignments table';
    ELSE
        RAISE NOTICE '✅ project_assignments table already exists';
    END IF;
    
    -- Check if manager_activities table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'manager_activities'
    ) THEN
        -- Create manager_activities table
        CREATE TABLE manager_activities (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            manager_id UUID NOT NULL REFERENCES profiles(id),
            project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
            activity_type VARCHAR(50) NOT NULL,
            description TEXT NOT NULL,
            participants UUID[] DEFAULT '{}',
            outcome VARCHAR(100),
            time_spent_minutes INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            metadata JSONB DEFAULT '{}'
        );
        
        -- Create indexes
        CREATE INDEX idx_manager_activities_manager_id ON manager_activities(manager_id);
        CREATE INDEX idx_manager_activities_project_id ON manager_activities(project_id);
        CREATE INDEX idx_manager_activities_created_at ON manager_activities(created_at);
        CREATE INDEX idx_manager_activities_type ON manager_activities(activity_type);
        
        -- Enable RLS
        ALTER TABLE manager_activities ENABLE ROW LEVEL SECURITY;
        
        RAISE NOTICE '✅ Created manager_activities table';
    ELSE
        RAISE NOTICE '✅ manager_activities table already exists';
    END IF;
    
    -- Check if negotiation_sessions table exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'negotiation_sessions'
    ) THEN
        -- Create negotiation_sessions table
        CREATE TABLE negotiation_sessions (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
            manager_id UUID NOT NULL REFERENCES profiles(id),
            client_id UUID NOT NULL REFERENCES profiles(id),
            designer_id UUID NOT NULL REFERENCES profiles(id),
            session_type VARCHAR(50) NOT NULL CHECK (session_type IN ('pricing', 'timeline', 'scope', 'terms')),
            status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
            initial_terms JSONB DEFAULT '{}',
            final_terms JSONB DEFAULT '{}',
            manager_notes TEXT,
            started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            completed_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            metadata JSONB DEFAULT '{}'
        );
        
        -- Create indexes
        CREATE INDEX idx_negotiation_sessions_project_id ON negotiation_sessions(project_id);
        CREATE INDEX idx_negotiation_sessions_manager_id ON negotiation_sessions(manager_id);
        CREATE INDEX idx_negotiation_sessions_status ON negotiation_sessions(status);
        CREATE INDEX idx_negotiation_sessions_type ON negotiation_sessions(session_type);
        
        -- Enable RLS
        ALTER TABLE negotiation_sessions ENABLE ROW LEVEL SECURITY;
        
        RAISE NOTICE '✅ Created negotiation_sessions table';
    ELSE
        RAISE NOTICE '✅ negotiation_sessions table already exists';
    END IF;
    
END $$;

-- 2. CREATE SAMPLE PROJECT ASSIGNMENTS FOR TESTING
-- =====================================================

DO $$
DECLARE
    manager_user_id UUID;
    sample_project_id UUID;
BEGIN
    -- Get a manager user
    SELECT id INTO manager_user_id 
    FROM profiles 
    WHERE role = 'manager' 
    LIMIT 1;
    
    -- Get a sample project
    SELECT id INTO sample_project_id 
    FROM projects 
    LIMIT 1;
    
    IF manager_user_id IS NOT NULL AND sample_project_id IS NOT NULL THEN
        -- Create sample assignment if it doesn't exist
        INSERT INTO project_assignments (project_id, manager_id, status, priority, notes)
        SELECT sample_project_id, manager_user_id, 'active', 'normal', 'Sample assignment for testing'
        WHERE NOT EXISTS (
            SELECT 1 FROM project_assignments 
            WHERE project_id = sample_project_id AND status = 'active'
        );
        
        -- Update project with manager assignment
        UPDATE projects 
        SET assigned_manager_id = manager_user_id 
        WHERE id = sample_project_id AND assigned_manager_id IS NULL;
        
        RAISE NOTICE '✅ Created sample project assignment for testing';
    ELSE
        RAISE NOTICE '⚠️ No manager users or projects found for sample assignment';
    END IF;
    
EXCEPTION
    WHEN others THEN
        RAISE NOTICE '⚠️ Could not create sample assignment: %', SQLERRM;
END $$;

-- 3. ADD MISSING COLUMNS TO EXISTING TABLES
-- =====================================================

DO $$
BEGIN
    -- Add assigned_manager_id to projects table if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'assigned_manager_id'
    ) THEN
        ALTER TABLE projects ADD COLUMN assigned_manager_id UUID REFERENCES profiles(id);
        CREATE INDEX idx_projects_assigned_manager ON projects(assigned_manager_id);
        RAISE NOTICE '✅ Added assigned_manager_id column to projects table';
    ELSE
        RAISE NOTICE '✅ assigned_manager_id column already exists in projects table';
    END IF;
    
    -- Add quality_status to projects table if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'quality_status'
    ) THEN
        ALTER TABLE projects ADD COLUMN quality_status VARCHAR(50) DEFAULT 'pending' 
            CHECK (quality_status IN ('pending', 'in_review', 'approved', 'rejected', 'revision_required'));
        CREATE INDEX idx_projects_quality_status ON projects(quality_status);
        RAISE NOTICE '✅ Added quality_status column to projects table';
    ELSE
        RAISE NOTICE '✅ quality_status column already exists in projects table';
    END IF;
    
END $$;

-- 4. CREATE BASIC RLS POLICIES FOR NEW TABLES
-- =====================================================

DO $$
BEGIN
    -- Project assignments policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_assignments') THEN
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "project_assignments_select_policy" ON project_assignments;
        DROP POLICY IF EXISTS "project_assignments_insert_policy" ON project_assignments;
        DROP POLICY IF EXISTS "project_assignments_update_policy" ON project_assignments;
        
        -- Create new policies
        CREATE POLICY "project_assignments_select_policy" ON project_assignments
            FOR SELECT USING (
                auth.uid() = manager_id OR 
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        CREATE POLICY "project_assignments_insert_policy" ON project_assignments
            FOR INSERT WITH CHECK (
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        CREATE POLICY "project_assignments_update_policy" ON project_assignments
            FOR UPDATE USING (
                auth.uid() = manager_id OR 
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        RAISE NOTICE '✅ Created RLS policies for project_assignments';
    END IF;
    
    -- Manager activities policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'manager_activities') THEN
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "manager_activities_select_policy" ON manager_activities;
        DROP POLICY IF EXISTS "manager_activities_insert_policy" ON manager_activities;
        
        -- Create new policies
        CREATE POLICY "manager_activities_select_policy" ON manager_activities
            FOR SELECT USING (
                auth.uid() = manager_id OR 
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        CREATE POLICY "manager_activities_insert_policy" ON manager_activities
            FOR INSERT WITH CHECK (
                auth.uid() = manager_id OR 
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        RAISE NOTICE '✅ Created RLS policies for manager_activities';
    END IF;
    
    -- Negotiation sessions policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'negotiation_sessions') THEN
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "negotiation_sessions_select_policy" ON negotiation_sessions;
        DROP POLICY IF EXISTS "negotiation_sessions_insert_policy" ON negotiation_sessions;
        DROP POLICY IF EXISTS "negotiation_sessions_update_policy" ON negotiation_sessions;
        
        -- Create new policies
        CREATE POLICY "negotiation_sessions_select_policy" ON negotiation_sessions
            FOR SELECT USING (
                auth.uid() = manager_id OR 
                auth.uid() = client_id OR 
                auth.uid() = designer_id OR
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        CREATE POLICY "negotiation_sessions_insert_policy" ON negotiation_sessions
            FOR INSERT WITH CHECK (
                auth.uid() = manager_id OR 
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        CREATE POLICY "negotiation_sessions_update_policy" ON negotiation_sessions
            FOR UPDATE USING (
                auth.uid() = manager_id OR 
                auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin')
            );
            
        RAISE NOTICE '✅ Created RLS policies for negotiation_sessions';
    END IF;
    
END $$;

-- 5. FINAL SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 API ISSUES FIXED SUCCESSFULLY!';
    RAISE NOTICE '';
    RAISE NOTICE 'Fixed issues:';
    RAISE NOTICE '✅ Foreign key relationships';
    RAISE NOTICE '✅ Missing tables created';
    RAISE NOTICE '✅ Missing columns added';
    RAISE NOTICE '✅ RLS policies configured';
    RAISE NOTICE '✅ Sample data for testing';
    RAISE NOTICE '';
    RAISE NOTICE 'The manager dashboard should now work correctly!';
    RAISE NOTICE '';
END $$;
