'use client';

import { supabase } from '@/lib/supabase';

/**
 * Quality Workflow Management System
 * Handles automated review assignment, SLA tracking, and workflow integration
 */

export interface QualityReview {
  id: string;
  project_id: string;
  submission_id?: string;
  milestone_id?: string;
  designer_id: string;
  reviewer_id?: string;
  review_type: 'milestone' | 'final' | 'revision' | 'emergency';
  status: 'pending' | 'assigned' | 'in_review' | 'approved' | 'needs_revision' | 'rejected';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  sla_deadline: string;
  overall_score?: number;
  feedback?: string;
  revision_count: number;
  created_at: string;
  assigned_at?: string;
  reviewed_at?: string;
  escalated_at?: string;
}

export interface QualityTeamMember {
  id: string;
  full_name: string;
  email: string;
  specializations: string[];
  current_workload: number;
  max_workload: number;
  is_available: boolean;
  last_assignment: string;
}

export interface ReviewAssignmentCriteria {
  reviewType: 'milestone' | 'final' | 'revision' | 'emergency';
  projectComplexity: 'low' | 'medium' | 'high';
  designerExperience: 'junior' | 'mid' | 'senior';
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  specialization?: string;
}

/**
 * Quality Workflow Manager Class
 */
export class QualityWorkflowManager {
  /**
   * Create a new quality review when designer submits work
   */
  static async createQualityReview(params: {
    projectId: string;
    submissionId?: string;
    milestoneId?: string;
    designerId: string;
    reviewType: 'milestone' | 'final' | 'revision' | 'emergency';
    priority?: 'low' | 'medium' | 'high' | 'urgent';
  }): Promise<{ success: boolean; reviewId?: string; error?: string }> {
    try {
      const { projectId, submissionId, milestoneId, designerId, reviewType, priority = 'medium' } = params;

      // Calculate SLA deadline based on review type and priority
      const slaDeadline = this.calculateSLADeadline(reviewType, priority);

      // Create the quality review record
      const { data: review, error } = await supabase
        .from('quality_reviews_new')
        .insert({
          project_id: projectId,
          submission_id: submissionId,
          milestone_id: milestoneId,
          designer_id: designerId,
          review_type: reviewType,
          status: 'pending',
          priority,
          sla_deadline: slaDeadline,
          revision_count: 0
        })
        .select()
        .single();

      if (error) throw error;

      // Attempt automatic assignment
      const assignmentResult = await this.autoAssignReview(review.id, {
        reviewType,
        projectComplexity: 'medium', // TODO: Get from project data
        designerExperience: 'mid', // TODO: Get from designer profile
        urgency: priority
      });

      // Create workflow notification
      await this.createWorkflowNotification({
        type: 'quality_review_created',
        reviewId: review.id,
        projectId,
        designerId,
        reviewerId: assignmentResult.reviewerId
      });

      return { success: true, reviewId: review.id };
    } catch (error) {
      console.error('Error creating quality review:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Auto-assign review to best available quality team member
   */
  static async autoAssignReview(
    reviewId: string, 
    criteria: ReviewAssignmentCriteria
  ): Promise<{ success: boolean; reviewerId?: string; error?: string }> {
    try {
      // Get available quality team members
      const availableReviewers = await this.getAvailableReviewers();
      
      if (availableReviewers.length === 0) {
        // No reviewers available - escalate to admin
        await this.escalateToAdmin(reviewId, 'no_reviewers_available');
        return { success: false, error: 'No reviewers available' };
      }

      // Find best reviewer based on criteria
      const bestReviewer = this.selectBestReviewer(availableReviewers, criteria);

      // Assign the review
      const { error } = await supabase
        .from('quality_reviews_new')
        .update({
          reviewer_id: bestReviewer.id,
          status: 'assigned',
          assigned_at: new Date().toISOString()
        })
        .eq('id', reviewId);

      if (error) throw error;

      // Update reviewer workload
      await this.updateReviewerWorkload(bestReviewer.id, 1);

      // Send notification to reviewer
      await this.notifyReviewer(bestReviewer.id, reviewId);

      return { success: true, reviewerId: bestReviewer.id };
    } catch (error) {
      console.error('Error auto-assigning review:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Calculate SLA deadline based on review type and priority
   */
  static calculateSLADeadline(reviewType: string, priority: string): string {
    const now = new Date();
    let hoursToAdd = 24; // Default 24 hours

    // Adjust based on review type
    switch (reviewType) {
      case 'emergency':
        hoursToAdd = 4;
        break;
      case 'revision':
        hoursToAdd = 12;
        break;
      case 'milestone':
        hoursToAdd = 24;
        break;
      case 'final':
        hoursToAdd = 48;
        break;
    }

    // Adjust based on priority
    switch (priority) {
      case 'urgent':
        hoursToAdd = Math.max(2, hoursToAdd / 2);
        break;
      case 'high':
        hoursToAdd = Math.max(4, hoursToAdd * 0.75);
        break;
      case 'low':
        hoursToAdd = hoursToAdd * 1.5;
        break;
    }

    now.setHours(now.getHours() + hoursToAdd);
    return now.toISOString();
  }

  /**
   * Get available quality team members
   */
  static async getAvailableReviewers(): Promise<QualityTeamMember[]> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          quality_specializations,
          quality_current_workload,
          quality_max_workload,
          quality_is_available,
          quality_last_assignment
        `)
        .eq('role', 'quality_team')
        .eq('quality_is_available', true)
        .lt('quality_current_workload', supabase.raw('quality_max_workload'));

      if (error) throw error;

      return (data || []).map(member => ({
        id: member.id,
        full_name: member.full_name,
        email: member.email,
        specializations: member.quality_specializations || [],
        current_workload: member.quality_current_workload || 0,
        max_workload: member.quality_max_workload || 5,
        is_available: member.quality_is_available || false,
        last_assignment: member.quality_last_assignment || new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error getting available reviewers:', error);
      return [];
    }
  }

  /**
   * Select best reviewer based on criteria
   */
  static selectBestReviewer(
    reviewers: QualityTeamMember[], 
    criteria: ReviewAssignmentCriteria
  ): QualityTeamMember {
    // Score each reviewer
    const scoredReviewers = reviewers.map(reviewer => {
      let score = 0;

      // Workload factor (prefer less loaded reviewers)
      const workloadRatio = reviewer.current_workload / reviewer.max_workload;
      score += (1 - workloadRatio) * 40; // 40 points max for low workload

      // Specialization match
      if (criteria.specialization && reviewer.specializations.includes(criteria.specialization)) {
        score += 30; // 30 points for specialization match
      }

      // Time since last assignment (prefer balanced distribution)
      const daysSinceLastAssignment = Math.floor(
        (Date.now() - new Date(reviewer.last_assignment).getTime()) / (1000 * 60 * 60 * 24)
      );
      score += Math.min(daysSinceLastAssignment * 2, 20); // Up to 20 points for time distribution

      // Random factor to prevent always picking the same reviewer
      score += Math.random() * 10; // Up to 10 points random

      return { reviewer, score };
    });

    // Sort by score and return the best reviewer
    scoredReviewers.sort((a, b) => b.score - a.score);
    return scoredReviewers[0].reviewer;
  }

  /**
   * Update reviewer workload
   */
  static async updateReviewerWorkload(reviewerId: string, increment: number): Promise<void> {
    try {
      const { error } = await supabase.rpc('increment_reviewer_workload', {
        reviewer_id: reviewerId,
        increment_by: increment
      });

      if (error) throw error;
    } catch (error) {
      console.error('Error updating reviewer workload:', error);
    }
  }

  /**
   * Create workflow notification
   */
  static async createWorkflowNotification(params: {
    type: string;
    reviewId: string;
    projectId: string;
    designerId: string;
    reviewerId?: string;
  }): Promise<void> {
    try {
      const notifications = [];

      // Notify designer
      notifications.push({
        recipient_id: params.designerId,
        notification_type: params.type,
        title: 'Quality Review Started',
        message: 'Your submission has been sent for quality review',
        priority: 'medium',
        metadata: {
          review_id: params.reviewId,
          project_id: params.projectId
        }
      });

      // Notify reviewer if assigned
      if (params.reviewerId) {
        notifications.push({
          recipient_id: params.reviewerId,
          notification_type: 'review_assigned',
          title: 'New Review Assigned',
          message: 'A new quality review has been assigned to you',
          priority: 'high',
          metadata: {
            review_id: params.reviewId,
            project_id: params.projectId
          }
        });
      }

      // Insert notifications
      const { error } = await supabase
        .from('workflow_notifications')
        .insert(notifications);

      if (error) throw error;
    } catch (error) {
      console.error('Error creating workflow notification:', error);
    }
  }

  /**
   * Notify reviewer of new assignment
   */
  static async notifyReviewer(reviewerId: string, reviewId: string): Promise<void> {
    // This would integrate with email notification system
    console.log(`Notifying reviewer ${reviewerId} of new review ${reviewId}`);
  }

  /**
   * Escalate to admin when issues occur
   */
  static async escalateToAdmin(reviewId: string, reason: string): Promise<void> {
    try {
      // Get admin users
      const { data: admins } = await supabase
        .from('profiles')
        .select('id')
        .eq('role', 'admin');

      if (!admins || admins.length === 0) return;

      // Create escalation notifications
      const notifications = admins.map(admin => ({
        recipient_id: admin.id,
        notification_type: 'quality_escalation',
        title: 'Quality Review Escalation',
        message: `Quality review ${reviewId} requires admin attention: ${reason}`,
        priority: 'urgent',
        metadata: {
          review_id: reviewId,
          escalation_reason: reason
        }
      }));

      const { error } = await supabase
        .from('workflow_notifications')
        .insert(notifications);

      if (error) throw error;
    } catch (error) {
      console.error('Error escalating to admin:', error);
    }
  }
}

// Export convenience functions
export const createQualityReview = QualityWorkflowManager.createQualityReview;
export const autoAssignReview = QualityWorkflowManager.autoAssignReview;
