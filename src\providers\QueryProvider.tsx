"use client";

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

// Optimized stale time configuration based on data type
const getOptimizedStaleTime = (queryKey: any[]): number => {
  const keyString = JSON.stringify(queryKey);

  // Authentication data - REDUCED cache time to prevent role persistence issues
  if (keyString.includes('auth') || keyString.includes('session') || keyString.includes('profile')) {
    return 5 * 60 * 1000; // 5 minutes (reduced from 30 minutes)
  }

  // Dashboard stats - moderately stable, cache for 15 minutes
  if (keyString.includes('stats') || keyString.includes('analytics')) {
    return 15 * 60 * 1000; // 15 minutes
  }

  // Messages - frequently changing, cache for 2 minutes
  if (keyString.includes('messages') || keyString.includes('notifications')) {
    return 2 * 60 * 1000; // 2 minutes
  }

  // Projects and proposals - moderately changing, cache for 10 minutes
  if (keyString.includes('projects') || keyString.includes('proposals')) {
    return 10 * 60 * 1000; // 10 minutes
  }

  // Navigation and static data - very stable, cache for 30 minutes
  if (keyString.includes('navigation') || keyString.includes('routes')) {
    return 30 * 60 * 1000; // 30 minutes
  }

  // Default cache time for other data
  return 10 * 60 * 1000; // 10 minutes
};

// Optimized garbage collection time based on data importance
const getOptimizedGcTime = (queryKey: any[]): number => {
  const keyString = JSON.stringify(queryKey);

  // Critical data - keep in memory longer
  if (keyString.includes('auth') || keyString.includes('dashboard') || keyString.includes('profile')) {
    return 60 * 60 * 1000; // 1 hour
  }

  // Important data - moderate memory retention
  if (keyString.includes('projects') || keyString.includes('proposals') || keyString.includes('stats')) {
    return 30 * 60 * 1000; // 30 minutes
  }

  // Less critical data - shorter memory retention
  return 15 * 60 * 1000; // 15 minutes
};

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Dynamic stale time based on query type
            staleTime: (query) => getOptimizedStaleTime(query.queryKey),

            // Dynamic garbage collection time based on data importance
            gcTime: (query) => getOptimizedGcTime(query.queryKey),

            // Enhanced retry logic with exponential backoff
            retry: (failureCount, error: any) => {
              // Don't retry on auth errors
              if (error?.status === 401 || error?.status === 403) {
                return false;
              }
              // Don't retry on client errors (4xx)
              if (error?.status >= 400 && error?.status < 500) {
                return false;
              }
              // Retry up to 3 times for server errors
              return failureCount < 3;
            },

            // Optimized retry delay with exponential backoff
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

            // Enhanced performance optimizations for tab switching (FIXED - much more conservative)
            refetchOnWindowFocus: (query) => {
              // Only refetch auth queries on window focus, and only if very stale
              const queryKey = JSON.stringify(query.queryKey);
              const isAuth = queryKey.includes('auth') || queryKey.includes('session');

              if (!isAuth) return false; // Never refetch non-auth queries on focus

              // Only refetch auth if it's very stale (10+ minutes)
              const age = Date.now() - (query.state.dataUpdatedAt || 0);
              return age > 10 * 60 * 1000;
            },
            refetchOnMount: (query) => {
              // Use cached data when available, only refetch if EXTREMELY stale or no data
              const age = Date.now() - (query.state.dataUpdatedAt || 0);
              const isExtremelyStale = age > 30 * 60 * 1000; // 30 minutes (increased from 10)
              return !query.state.data || isExtremelyStale;
            },
            refetchOnReconnect: true,

            // Enable structural sharing for memory optimization
            structuralSharing: true,

            // Network mode optimization
            networkMode: 'online',

            // Enable query deduplication
            queryKeyHashFn: (queryKey) => JSON.stringify(queryKey),

            // Enhanced error handling for tab switching scenarios
            useErrorBoundary: false,

            // Prevent queries from being marked as stale too quickly (INCREASED TIMES)
            staleTime: (query) => {
              const queryKey = JSON.stringify(query.queryKey);
              if (queryKey.includes('availability')) return 30 * 60 * 1000; // 30 minutes (increased from 10)
              if (queryKey.includes('auth') || queryKey.includes('profile')) return 15 * 60 * 1000; // 15 minutes (increased from 5)
              if (queryKey.includes('dashboard') || queryKey.includes('stats')) return 10 * 60 * 1000; // 10 minutes (increased from 3)
              return 5 * 60 * 1000; // 5 minutes default (increased from 2)
            },
          },
          mutations: {
            // Optimized mutation retry
            retry: (failureCount, error: any) => {
              if (error?.status === 401 || error?.status === 403) {
                return false;
              }
              return failureCount < 2;
            },

            // Mutation retry delay
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),

            // Network mode for mutations
            networkMode: 'online',
          },
        },

        // Enhanced query cache configuration
        queryCache: undefined, // Use default with our optimizations
        mutationCache: undefined, // Use default
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-right"
          toggleButtonProps={{
            style: {
              marginLeft: '5px',
              transform: 'scale(0.8)',
            },
          }}
        />
      )}
    </QueryClientProvider>
  );
}
