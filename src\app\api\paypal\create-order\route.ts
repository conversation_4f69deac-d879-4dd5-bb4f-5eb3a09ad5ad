import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// PayPal API base URL - use sandbox for development, change to live for production
const PAYPAL_API_BASE = process.env.NODE_ENV === 'production'
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

// Token cache for PayPal access tokens
let cachedToken: string | null = null;
let tokenExpiry: number = 0;

// Optimized function to get PayPal access token with caching
async function getPayPalAccessToken() {
  // Return cached token if still valid
  if (cachedToken && Date.now() < tokenExpiry) {
    return cachedToken;
  }

  const clientId = process.env.PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('PayPal credentials are not configured');
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

  const response = await fetch(`${PAYPAL_API_BASE}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${auth}`
    },
    body: 'grant_type=client_credentials'
  });

  if (!response.ok) {
    const errorData = await response.json();
    console.error('PayPal auth error:', errorData);
    throw new Error('Failed to authenticate with PayPal');
  }

  const data = await response.json();

  // Cache the token (expires in 9 hours, we'll cache for 8 hours for safety)
  cachedToken = data.access_token;
  tokenExpiry = Date.now() + (8 * 60 * 60 * 1000); // 8 hours

  return cachedToken;
}

export async function POST(request: Request) {
  try {
    // Get request data
    const {
      amount,
      description,
      projectId,
      milestoneId,
      clientId,
      paymentType
    } = await request.json();

    // Validate required fields
    if (!amount || !description || !projectId || !clientId || !paymentType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Initialize Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          persistSession: false
        }
      }
    );

    // Run database queries and PayPal auth in parallel for better performance
    const [
      { data: clientProfile, error: clientError },
      { data: projectData, error: projectError },
      accessToken
    ] = await Promise.all([
      supabase
        .from('profiles')
        .select('full_name, email')
        .eq('id', clientId)
        .single(),
      supabase
        .from('projects')
        .select('title, designer_id')
        .eq('id', projectId)
        .single(),
      getPayPalAccessToken()
    ]);

    if (clientError) {
      console.error('Error fetching client profile:', clientError);
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      );
    }

    if (projectError) {
      console.error('Error fetching project:', projectError);
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Get designer profile if available (optional, don't block on this)
    let designerProfile = null;
    if (projectData.designer_id) {
      try {
        const { data: designer } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('id', projectData.designer_id)
          .single();
        designerProfile = designer;
      } catch (error) {
        // Designer profile is optional, continue without it
        console.warn('Could not fetch designer profile:', error);
      }
    }

    // Create PayPal order
    const orderResponse = await fetch(`${PAYPAL_API_BASE}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        intent: 'CAPTURE',
        purchase_units: [
          {
            reference_id: milestoneId || projectId,
            description: description,
            custom_id: JSON.stringify({
              projectId,
              milestoneId,
              clientId,
              paymentType
            }),
            amount: {
              currency_code: 'USD',
              value: (amount / 100).toFixed(2) // Convert cents to dollars
            }
          }
        ],
        application_context: {
          brand_name: 'Seniors Archi Firm',
          shipping_preference: 'NO_SHIPPING',
          user_action: 'PAY_NOW',
          return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/client/payments?success=true`,
          cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/client/payments?canceled=true`
        }
      })
    });

    if (!orderResponse.ok) {
      const errorData = await orderResponse.json();
      console.error('PayPal order creation error:', errorData);
      return NextResponse.json(
        { error: 'Failed to create PayPal order' },
        { status: 500 }
      );
    }

    const orderData = await orderResponse.json();
    
    // Return the order ID and approval URL
    return NextResponse.json({
      id: orderData.id,
      status: orderData.status,
      links: orderData.links
    });
  } catch (error: unknown) {
    console.error('Error creating PayPal order:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
