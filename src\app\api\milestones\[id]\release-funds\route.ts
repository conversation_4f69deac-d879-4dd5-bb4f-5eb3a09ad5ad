import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/milestones/[id]/release-funds
 * Releases funds to the designer for a completed milestone
 * 
 * Request body:
 * {
 *   userId: string; // ID of the admin releasing the funds
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const milestoneId = params.id;
    const { userId } = await request.json();
    
    if (!milestoneId) {
      return NextResponse.json(
        { error: 'Milestone ID is required' },
        { status: 400 }
      );
    }
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Check if the user is an admin
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();
    
    if (userError) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    if (userData.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only admins can release funds' },
        { status: 403 }
      );
    }
    
    // Get the milestone details
    const { data: milestone, error: milestoneError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        title,
        amount,
        status,
        project_id,
        projects (
          designer_id,
          client_id
        )
      `)
      .eq('id', milestoneId)
      .single();
    
    if (milestoneError) {
      return NextResponse.json(
        { error: 'Milestone not found' },
        { status: 404 }
      );
    }
    
    // Check if the milestone is in the correct status
    if (milestone.status !== 'approved') {
      return NextResponse.json(
        { error: 'Milestone must be approved before funds can be released' },
        { status: 400 }
      );
    }
    
    // Get the transaction for this milestone
    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .select('id, transaction_id, amount, status')
      .eq('milestone_id', milestoneId)
      .eq('status', 'completed')
      .single();
    
    if (transactionError) {
      return NextResponse.json(
        { error: 'No completed payment found for this milestone' },
        { status: 404 }
      );
    }
    
    // Create a payout record
    const { data: payout, error: payoutError } = await supabase
      .from('payouts')
      .insert({
        transaction_id: transaction.id,
        designer_id: milestone.projects.designer_id,
        amount: transaction.amount,
        status: 'processed',
        processed_by: userId,
        processed_at: new Date().toISOString(),
        notes: `Funds released for milestone: ${milestone.title}`
      })
      .select()
      .single();
    
    if (payoutError) {
      console.error('Error creating payout record:', payoutError);
      return NextResponse.json(
        { error: 'Failed to create payout record' },
        { status: 500 }
      );
    }
    
    // Update the milestone status
    const { data: updatedMilestone, error: updateError } = await supabase
      .from('project_milestones')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        completed_by: userId
      })
      .eq('id', milestoneId)
      .select()
      .single();
    
    if (updateError) {
      console.error('Error updating milestone:', updateError);
      return NextResponse.json(
        { error: 'Failed to update milestone status' },
        { status: 500 }
      );
    }
    
    // Create notifications
    // Notification for the designer
    const { error: designerNotificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: milestone.projects.designer_id,
        type: 'payment',
        title: 'Payment Released',
        content: `Funds for milestone "${milestone.title}" have been released to your account.`,
        link: `/designer/projects/${milestone.project_id}`,
        read: false
      });
    
    if (designerNotificationError) {
      console.error('Error creating designer notification:', designerNotificationError);
    }
    
    // Notification for the client
    const { error: clientNotificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: milestone.projects.client_id,
        type: 'info',
        title: 'Milestone Completed',
        content: `Milestone "${milestone.title}" has been marked as completed.`,
        link: `/client/projects/${milestone.project_id}`,
        read: false
      });
    
    if (clientNotificationError) {
      console.error('Error creating client notification:', clientNotificationError);
    }
    
    return NextResponse.json({ 
      success: true, 
      milestone: updatedMilestone,
      payout
    }, { status: 200 });
  } catch (error) {
    console.error('Error releasing funds:', error);
    return NextResponse.json(
      { error: 'Failed to release funds' },
      { status: 500 }
    );
  }
}
