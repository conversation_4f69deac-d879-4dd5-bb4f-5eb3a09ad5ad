"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  DollarSign,
  Calendar,
  User,
  MessageSquare,
  Download,
  Upload
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface ProjectAgreement {
  id: string;
  project_id: string;
  project_title: string;
  client_name: string;
  client_avatar: string | null;
  status: 'pending' | 'accepted' | 'rejected' | 'revision_requested';
  budget: number | null;
  deadline: string | null;
  description: string;
  requirements: string | null;
  proposal_template: string | null;
  created_at: string;
  response_deadline: string | null;
}

export function ProjectAgreementFlow() {
  const { user } = useOptimizedAuth();
  const [agreements, setAgreements] = useState<ProjectAgreement[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgreement, setSelectedAgreement] = useState<ProjectAgreement | null>(null);
  const [showProposal, setShowProposal] = useState(false);

  useEffect(() => {
    if (user) {
      fetchProjectAgreements();
    }
  }, [user]);

  const fetchProjectAgreements = async () => {
    try {
      // Fetch projects that need designer response
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          requirements,
          budget,
          deadline,
          status,
          created_at,
          assigned_at,
          client_id,
          profiles!client_id(full_name, avatar_url)
        `)
        .eq('designer_id', user?.id)
        .in('status', ['draft', 'submitted', 'pending_designer_response'])
        .not('assigned_at', 'is', null)
        .order('created_at', { ascending: false });

      if (projectsError) throw projectsError;

      // Transform data to agreement format
      const agreementData = (projectsData || []).map(project => ({
        id: `agreement-${project.id}`,
        project_id: project.id,
        project_title: project.title,
        client_name: project.profiles?.full_name || 'Unknown Client',
        client_avatar: project.profiles?.avatar_url || null,
        status: project.status === 'draft' ? 'pending' as const : 
                project.status === 'submitted' ? 'accepted' as const : 'pending' as const,
        budget: project.budget,
        deadline: project.deadline,
        description: project.description || '',
        requirements: project.requirements,
        proposal_template: null, // This would come from proposal_templates table
        created_at: project.assigned_at || project.created_at,
        response_deadline: project.deadline
      }));

      setAgreements(agreementData);
    } catch (error) {
      console.error('Error fetching project agreements:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAgreementResponse = async (agreementId: string, response: 'accept' | 'reject' | 'request_revision') => {
    const agreement = agreements.find(a => a.id === agreementId);
    if (!agreement) return;

    try {
      let newStatus = 'submitted';
      if (response === 'reject') newStatus = 'cancelled';
      if (response === 'request_revision') newStatus = 'revision_requested';

      const { error } = await supabase
        .from('projects')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', agreement.project_id);

      if (error) throw error;

      // Update local state
      setAgreements(prev => 
        prev.map(a => 
          a.id === agreementId 
            ? { ...a, status: response === 'accept' ? 'accepted' : response === 'reject' ? 'rejected' : 'revision_requested' }
            : a
        )
      );

      console.log(`Project ${response}ed successfully`);
    } catch (error) {
      console.error(`Error ${response}ing project:`, error);
    }
  };

  const getStatusColor = (status: ProjectAgreement['status']) => {
    switch (status) {
      case 'accepted':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'revision_requested':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getStatusIcon = (status: ProjectAgreement['status']) => {
    switch (status) {
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'revision_requested':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No deadline';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return 'Budget TBD';
    return `$${amount.toLocaleString()}`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2].map(i => (
              <div key={i} className="h-24 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Project Agreements</h3>
          <span className="text-sm text-gray-500">{agreements.length} pending</span>
        </div>
      </div>
      
      <div className="p-6">
        {agreements.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Pending Agreements</h4>
            <p className="text-gray-600">You'll see project agreements here when clients or admins assign projects to you.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {agreements.map((agreement) => (
              <motion.div
                key={agreement.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                      {agreement.client_avatar ? (
                        <img
                          src={agreement.client_avatar}
                          alt={agreement.client_name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{agreement.project_title}</h4>
                      <p className="text-sm text-gray-600">from {agreement.client_name}</p>
                    </div>
                  </div>
                  
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(agreement.status)}`}>
                    {getStatusIcon(agreement.status)}
                    <span className="ml-1 capitalize">{agreement.status.replace('_', ' ')}</span>
                  </span>
                </div>

                <p className="text-sm text-gray-700 mb-3 line-clamp-2">{agreement.description}</p>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <DollarSign className="h-4 w-4 mr-2" />
                    <span>{formatCurrency(agreement.budget)}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>{formatDate(agreement.deadline)}</span>
                  </div>
                </div>

                {agreement.status === 'pending' && (
                  <div className="flex items-center space-x-2 pt-3 border-t">
                    <Button
                      onClick={() => handleAgreementResponse(agreement.id, 'accept')}
                      className="bg-green-600 hover:bg-green-700 text-white text-sm px-4 py-2"
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Accept
                    </Button>
                    <Button
                      onClick={() => handleAgreementResponse(agreement.id, 'request_revision')}
                      variant="outline"
                      className="text-sm px-4 py-2"
                    >
                      <MessageSquare className="h-4 w-4 mr-1" />
                      Request Changes
                    </Button>
                    <Button
                      onClick={() => handleAgreementResponse(agreement.id, 'reject')}
                      variant="outline"
                      className="text-red-600 border-red-300 hover:bg-red-50 text-sm px-4 py-2"
                    >
                      <XCircle className="h-4 w-4 mr-1" />
                      Decline
                    </Button>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}
