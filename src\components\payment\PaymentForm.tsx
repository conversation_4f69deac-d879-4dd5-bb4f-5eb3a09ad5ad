"use client";

import { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import {
  CardElement,
  useStripe,
  useElements,
  PaymentElement,
  Elements
} from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from "framer-motion";
import {
  createPaymentIntent,
  PaymentIntent,
  StripePaymentOptions,
  getSavedPaymentMethods,
  processPaymentWithSavedMethod
} from '@/lib/stripe';
import { PayPalButton } from './PayPalButton';
import {
  CreditCard,
  CheckCircle,
  AlertCircle,
  Loader2,
  Plus,
  CreditCard as CardIcon
} from 'lucide-react';

// Make sure to set your Stripe publishable key in the environment variables
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

interface PaymentFormProps {
  projectId: string;
  milestoneId?: string;
  amount: number;
  description: string;
  designerId?: string;
  paymentType: 'deposit' | 'milestone' | 'final';
  onSuccess: () => void;
  onCancel: () => void;
}

// Removed PaymentFormWrapperProps as it was redundant

interface SavedPaymentMethod {
  id: string;
  brand: string;
  last4: string;
  expMonth: number;
  expYear: number;
}

const PaymentFormContent = ({
  projectId,
  milestoneId,
  amount,
  description,
  designerId,
  paymentType,
  onSuccess,
  onCancel
}: PaymentFormProps) => {
  const { user } = useOptimizedAuth();
  const stripe = useStripe();
  const elements = useElements();

  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(null);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [saveCard, setSaveCard] = useState(false);
  const [savedPaymentMethods, setSavedPaymentMethods] = useState<SavedPaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [showNewCardForm, setShowNewCardForm] = useState(false);
  const [paymentMethodType, setPaymentMethodType] = useState<'card' | 'paypal'>('card');

  useEffect(() => {
    if (user) {
      // Create a payment intent when the component mounts
      createIntent();
      // Fetch saved payment methods
      fetchSavedPaymentMethods();
    }
  }, [user]);

  const createIntent = async () => {
    if (!user) return;

    const options: StripePaymentOptions = {
      projectId,
      milestoneId,
      amount,
      description,
      clientId: user.id,
      designerId,
      paymentType
    };

    const intent = await createPaymentIntent(options);
    if (intent) {
      setPaymentIntent(intent);
    } else {
      setError('Failed to initialize payment. Please try again.');
    }
  };

  const fetchSavedPaymentMethods = async () => {
    if (!user) return;

    const methods = await getSavedPaymentMethods(user.id);
    if (methods && methods.length > 0) {
      setSavedPaymentMethods(methods);
      setSelectedPaymentMethod(methods[0].id);
    } else {
      setShowNewCardForm(true);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !paymentIntent) {
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      if (selectedPaymentMethod && !showNewCardForm) {
        // Process payment with saved payment method
        const options: StripePaymentOptions = {
          projectId,
          milestoneId,
          amount,
          description,
          clientId: user!.id,
          designerId,
          paymentType
        };

        const result = await processPaymentWithSavedMethod(selectedPaymentMethod, options);

        if (result.success) {
          setSuccess(true);
          setTimeout(() => {
            onSuccess();
          }, 2000);
        } else {
          setError(result.error || 'Payment failed. Please try again.');
        }
      } else {
        // Process payment with new card
        const cardElement = elements.getElement(CardElement);

        if (!cardElement) {
          setError('Card element not found. Please refresh and try again.');
          setProcessing(false);
          return;
        }

        const { error, paymentIntent: updatedIntent } = await stripe.confirmCardPayment(
          paymentIntent.client_secret,
          {
            payment_method: {
              card: cardElement,
              billing_details: {
                email: user?.email || undefined,
              },
            },
            setup_future_usage: saveCard ? 'off_session' : undefined,
          }
        );

        if (error) {
          setError(error.message || 'Payment failed. Please try again.');
        } else if (updatedIntent.status === 'succeeded') {
          setSuccess(true);
          setTimeout(() => {
            onSuccess();
          }, 2000);
        } else {
          setError('Payment is pending or requires additional action.');
        }
      }
    } catch (err) {
      console.error('Payment error:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount / 100); // Stripe amounts are in cents
  };

  const getCardBrandIcon = (brand: string) => {
    // In a real implementation, you would use actual card brand icons
    return <CreditCard className="h-5 w-5" />;
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white border border-gray-200 p-6"
    >
      <h2 className="text-xl font-semibold mb-4">Payment Details</h2>

      <AnimatePresence mode="wait">
        {success ? (
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="text-center py-8"
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mx-auto w-16 h-16 bg-green-50 border border-green-200 flex items-center justify-center mb-4"
            >
              <CheckCircle className="h-8 w-8 text-green-600" />
            </motion.div>
            <h3 className="text-lg font-medium mb-2">Payment Successful!</h3>
            <p className="text-gray-500 mb-6">
              Your payment of {formatCurrency(amount)} has been processed successfully.
            </p>
          </motion.div>
        ) : (
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium">Payment Amount</h3>
              <span className="text-xl font-bold">{formatCurrency(amount)}</span>
            </div>
            <p className="text-gray-500 text-sm">{description}</p>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 flex items-start"
            >
              <AlertCircle className="h-5 w-5 mr-3 mt-0.5 flex-shrink-0" />
              <span>{error}</span>
            </motion.div>
          )}

          {/* Payment Method Selection */}
          <div className="mb-6">
            <h3 className="font-medium mb-3">Payment Method</h3>
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div
                className={`border rounded-md p-4 flex items-center cursor-pointer ${
                  paymentMethodType === 'card' ? 'border-brown-600 bg-brown-50' : 'hover:bg-gray-50'
                }`}
                onClick={() => setPaymentMethodType('card')}
              >
                <CardIcon className="h-5 w-5 mr-3 text-gray-600" />
                <div>
                  <p className="font-medium">Credit Card</p>
                  <p className="text-xs text-gray-500">Pay with Visa, Mastercard, etc.</p>
                </div>
              </div>
              <div
                className={`border rounded-md p-4 flex items-center cursor-pointer ${
                  paymentMethodType === 'paypal' ? 'border-brown-600 bg-brown-50' : 'hover:bg-gray-50'
                }`}
                onClick={() => setPaymentMethodType('paypal')}
              >
                <div className="mr-3 text-[#003087] font-bold text-lg">
                  Pay<span className="text-[#009cde]">Pal</span>
                </div>
                <p className="text-xs text-gray-500">Fast, secure payments</p>
              </div>
            </div>
          </div>

          {paymentMethodType === 'card' && (
            <>
              {savedPaymentMethods.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-medium mb-3">Saved Payment Methods</h3>
                  <div className="space-y-2">
                    {savedPaymentMethods.map((method) => (
                      <div
                        key={method.id}
                        className={`border rounded-md p-3 flex items-center cursor-pointer ${
                          selectedPaymentMethod === method.id && !showNewCardForm
                            ? 'border-brown-600 bg-brown-50'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => {
                          setSelectedPaymentMethod(method.id);
                          setShowNewCardForm(false);
                        }}
                      >
                        <div className="mr-3">
                          {getCardBrandIcon(method.brand)}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">
                            {method.brand.charAt(0).toUpperCase() + method.brand.slice(1)} •••• {method.last4}
                          </p>
                          <p className="text-sm text-gray-500">
                            Expires {method.expMonth.toString().padStart(2, '0')}/{method.expYear.toString().slice(-2)}
                          </p>
                        </div>
                        <div className="ml-3">
                          <div className={`w-5 h-5 rounded-full border ${
                            selectedPaymentMethod === method.id && !showNewCardForm
                              ? 'border-brown-600'
                              : 'border-gray-300'
                          } flex items-center justify-center`}>
                            {selectedPaymentMethod === method.id && !showNewCardForm && (
                              <div className="w-3 h-3 rounded-full bg-brown-600"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}

                    <div
                      className={`border rounded-md p-3 flex items-center cursor-pointer ${
                        showNewCardForm
                          ? 'border-brown-600 bg-brown-50'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => {
                        setShowNewCardForm(true);
                      }}
                    >
                      <div className="mr-3">
                        <Plus className="h-5 w-5 text-gray-400" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Add a new card</p>
                      </div>
                      <div className="ml-3">
                        <div className={`w-5 h-5 rounded-full border ${
                          showNewCardForm
                            ? 'border-brown-600'
                            : 'border-gray-300'
                        } flex items-center justify-center`}>
                          {showNewCardForm && (
                            <div className="w-3 h-3 rounded-full bg-brown-600"></div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {(showNewCardForm || savedPaymentMethods.length === 0) && (
                <div className="mb-6">
                  <h3 className="font-medium mb-3">Card Details</h3>
                  <div className="border rounded-md p-4">
                    <CardElement
                      options={{
                        style: {
                          base: {
                            fontSize: '16px',
                            color: '#424770',
                            '::placeholder': {
                              color: '#aab7c4',
                            },
                          },
                          invalid: {
                            color: '#9e2146',
                          },
                        },
                      }}
                    />
                  </div>

                  <div className="mt-3 flex items-center">
                    <input
                      type="checkbox"
                      id="saveCard"
                      checked={saveCard}
                      onChange={(e) => setSaveCard(e.target.checked)}
                      className="h-4 w-4 text-brown-600 border-gray-300"
                    />
                    <label htmlFor="saveCard" className="ml-2 block text-sm text-gray-700">
                      Save this card for future payments
                    </label>
                  </div>
                </div>
              )}
            </>
          )}

          {paymentMethodType === 'paypal' && (
            <div className="mb-6">
              <PayPalButton
                amount={amount}
                description={description}
                projectId={projectId}
                milestoneId={milestoneId}
                paymentType={paymentType}
                onSuccess={(details) => {
                  setSuccess(true);
                  setTimeout(() => {
                    onSuccess();
                  }, 2000);
                }}
                onError={(error) => {
                  setError(error.message || 'PayPal payment failed');
                }}
                onCancel={() => {
                  // Do nothing, user can try again
                }}
              />
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={processing}
            >
              Cancel
            </Button>
            {paymentMethodType === 'card' && (
              <Button
                type="submit"
                disabled={!stripe || !elements || processing || !paymentIntent}
                className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
              >
                {processing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />
                    Pay {formatCurrency(amount)}
                  </>
                )}
              </Button>
            )}
          </div>
        </form>
      )}
      </AnimatePresence>
    </motion.div>
  );
};

export const PaymentForm = (props: PaymentFormProps) => {
  return (
    <Elements stripe={stripePromise}>
      <PaymentFormContent {...props} />
    </Elements>
  );
};

export default PaymentForm;
