"use client";

import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import NewsletterSignup from './NewsletterSignup';
import {
  faCcVisa,
  faCcMastercard,
  faCcAmex,
  faCcPaypal
} from '@fortawesome/free-brands-svg-icons';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-black text-white">
      <div className="container mx-auto px-4">
        {/* Newsletter CTA Section - Full Width */}
        <motion.div
          className="py-16 border-b border-gray-800"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Stay Ahead of Design Trends
              </h3>
              <p className="text-lg text-gray-400 mb-8 max-w-2xl mx-auto leading-relaxed">
                Get exclusive insights, project updates, and architectural inspiration delivered to your inbox. 
                Join our community of design enthusiasts.
              </p>
            </motion.div>
            
            <motion.div
              className="max-w-md mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <NewsletterSignup variant="footer" />
            </motion.div>
            
            <motion.p
              className="text-xs text-gray-500 mt-4"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
            >
              No spam, unsubscribe at any time. We respect your privacy.
            </motion.p>
          </div>
        </motion.div>

        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            {/* Company Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="lg:col-span-1"
            >
              <Image
                src="/seniors-logo.svg"
                alt="Senior's Archi-Firm"
                width={200}
                height={64}
                className="h-16 w-auto mb-6 filter brightness-0 invert"
                priority
              />
              <div className="h-1 w-12 bg-primary mb-4"></div>
              <p className="text-sm text-gray-400 mb-6 leading-relaxed">
                Designing the Future, One Structure at a Time.
              </p>
              
              {/* Social Media */}
              <div className="flex space-x-3">
                {[
                  {
                    name: "Facebook",
                    icon: (
                      <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                    )
                  },
                  {
                    name: "Twitter",
                    icon: (
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                    )
                  },
                  {
                    name: "LinkedIn",
                    icon: (
                      <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                    )
                  },
                  {
                    name: "Instagram",
                    icon: (
                      <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                    )
                  }
                ].map((social) => (
                  <motion.a
                    key={social.name}
                    href="#"
                    className="w-10 h-10 bg-white/5 hover:bg-primary/20 text-gray-400 hover:text-primary rounded-full flex items-center justify-center transition-all duration-300 border border-white/10 hover:border-primary/30"
                    whileHover={{ y: -2, scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span className="sr-only">{social.name}</span>
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      {social.icon}
                    </svg>
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="lg:col-span-1"
            >
              <h4 className="text-lg font-semibold mb-4 text-white">Quick Links</h4>
              <div className="h-1 w-8 bg-primary mb-6"></div>
              <ul className="space-y-3">
                {[
                  { name: "Home", path: "/" },
                  { name: "About Us", path: "/about" },
                  { name: "Projects", path: "/projects" },
                  { name: "Services", path: "/services" },
                  { name: "Tracker", path: "/track" },
                  { name: "FAQ", path: "/faq" },
                  { name: "Join Us", path: "/join-us" },
                  { name: "Contact", path: "/contact" },
                  { name: "Login", path: "/auth/login" }
                ].map((link) => (
                  <li key={link.path}>
                    <Link
                      href={link.path}
                      className="text-gray-400 hover:text-primary transition-all duration-300 flex items-center group text-sm"
                    >
                      <span className="w-0 group-hover:w-2 h-[1px] bg-primary mr-0 group-hover:mr-2 transition-all duration-300"></span>
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Services */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="lg:col-span-1"
            >
              <h4 className="text-lg font-semibold mb-4 text-white">Services</h4>
              <div className="h-1 w-8 bg-primary mb-6"></div>
              <ul className="space-y-3">
                {[
                  "Creative Design & Branding",
                  "Architectural Design",
                  "Interior Design",
                  "Urban Planning",
                  "Residential Projects",
                  "Commercial Projects",
                  "Landscape Integration",
                  "Community Spaces"
                ].map((service, index) => (
                  <li key={index} className="text-gray-400 flex items-start text-sm">
                    <span className="w-1 h-1 bg-primary rounded-full mr-3 mt-2 flex-shrink-0"></span>
                    <span className="leading-relaxed">{service}</span>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Contact */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
                            viewport={{ once: true }}
              className="lg:col-span-1"
            >
              <h4 className="text-lg font-semibold mb-4 text-white">Contact</h4>
              <div className="h-1 w-8 bg-primary mb-6"></div>
              
              <address className="not-italic text-gray-400 space-y-4">
                <p className="text-sm leading-relaxed">
                  Global HQ – Riyadh, Saudi Arabia
                </p>
                
                <a
                  href="mailto:<EMAIL>"
                  className="hover:text-primary transition-colors duration-300 flex items-center text-sm group"
                >
                  <div className="w-8 h-8 bg-white/5 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary/10 transition-colors duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 group-hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span className="break-all"><EMAIL></span>
                </a>
                
                <a
                  href="tel:+966552552260"
                  className="hover:text-primary transition-colors duration-300 flex items-center text-sm group"
                >
                  <div className="w-8 h-8 bg-white/5 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary/10 transition-colors duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 group-hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  +966 55 255 2260
                </a>
              </address>

              {/* Office Hours */}
              <div className="mt-6 p-4 bg-white/5 rounded-lg border border-white/10">
                <h5 className="text-white font-medium mb-2 text-sm">Office Hours</h5>
                <div className="text-xs text-gray-400 space-y-1">
                  <p>Mon - Thu: 9:00 AM - 6:00 PM</p>
                  <p>Fri: 9:00 AM - 1:00 PM</p>
                  <p>Sat - Sun: Closed</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Payment Methods Section */}
        <motion.div
          className="py-12 border-t border-gray-800"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="text-center">
            <h4 className="text-lg font-semibold mb-8 text-white">Secure Payment Methods</h4>
            <div className="flex flex-wrap justify-center items-center gap-6">
              {[
                { icon: faCcVisa, name: "Visa", color: "hover:text-blue-400" },
                { icon: faCcMastercard, name: "Mastercard", color: "hover:text-red-400" },
                { icon: faCcAmex, name: "American Express", color: "hover:text-blue-300" },
                { icon: faCcPaypal, name: "PayPal", color: "hover:text-blue-500" }
              ].map((payment, index) => (
                <motion.div
                  key={payment.name}
                  className="group cursor-pointer"
                  whileHover={{ y: -3, scale: 1.05 }}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="bg-white/5 hover:bg-white/10 p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300">
                    <FontAwesomeIcon
                      icon={payment.icon}
                      className={`h-10 w-auto text-white ${payment.color} transition-colors duration-300`}
                      size="2x"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2 group-hover:text-gray-400 transition-colors duration-300">
                    {payment.name}
                  </p>
                </motion.div>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-6 max-w-md mx-auto">
              All transactions are secured with 256-bit SSL encryption
            </p>
          </div>
        </motion.div>

        {/* Bottom Footer */}
        <motion.div
          className="border-t border-gray-800 py-8"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-6">
              <p className="text-gray-400 text-sm text-center lg:text-left">
                © {currentYear} Senior's Archi-firm. All Rights Reserved.
              </p>
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                <span>Made with</span>
                <motion.span
                  className="text-red-400"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity, repeatDelay: 2 }}
                >
                  ♥
                </motion.span>
                <span>in Saudi Arabia</span>
              </div>
            </div>

            <div className="flex flex-wrap justify-center lg:justify-end gap-6">
              {[
                { name: "Privacy Policy", path: "/privacy-policy" },
                { name: "Terms of Service", path: "/terms-of-service" },
                { name: "Cookie Policy", path: "/cookie-policy" }
              ].map((link) => (
                <Link
                  key={link.path}
                  href={link.path}
                  className="text-gray-400 hover:text-primary text-sm transition-colors duration-300 relative group"
                >
                  {link.name}
                  <span className="absolute bottom-0 left-0 w-0 h-px bg-primary group-hover:w-full transition-all duration-300"></span>
                </Link>
              ))}
            </div>
          </div>

          {/* Back to Top Button */}
          <div className="flex justify-center mt-8">
            <motion.button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="bg-gradient-to-r from-primary/10 to-primary/20 hover:from-primary/20 hover:to-primary/30 text-primary p-4 rounded-full transition-all duration-300 border border-primary/20 hover:border-primary/40 group shadow-lg hover:shadow-primary/20"
              whileHover={{ y: -3, scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label="Back to top"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
              </svg>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
