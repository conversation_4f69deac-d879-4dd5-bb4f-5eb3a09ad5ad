"use client";

import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { Button } from "@/components/ui/button";

export function AuthSystemTest() {
  const { user, profile, loading, signOut } = useOptimizedAuth();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50">
      <h3 className="font-bold text-sm mb-2">🔧 Auth System Test</h3>
      
      <div className="space-y-2 text-xs">
        <div>
          <strong>Loading:</strong> {loading ? '✅ Yes' : '❌ No'}
        </div>
        
        <div>
          <strong>User:</strong> {user ? '✅ Authenticated' : '❌ Not authenticated'}
        </div>
        
        <div>
          <strong>Profile:</strong> {profile ? `✅ ${profile.role}` : '❌ No profile'}
        </div>
        
        {user && (
          <div>
            <strong>Email:</strong> {user.email}
          </div>
        )}
        
        {profile && (
          <div>
            <strong>Role:</strong> {profile.role}
          </div>
        )}
        
        <div className="pt-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => window.location.reload()}
            className="mr-2"
          >
            Reload
          </Button>
          
          {user && (
            <Button 
              size="sm" 
              variant="destructive" 
              onClick={() => signOut()}
            >
              Logout
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
