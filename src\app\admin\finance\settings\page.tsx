"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Save,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Percent,
  CreditCard,
  Clock,
  Shield,
  Settings as SettingsIcon
} from "lucide-react";

type PaymentSettings = {
  platform_fee_percentage: number;
  minimum_project_amount: number;
  default_deposit_percentage: number;
  default_payment_terms: number;
  enable_automatic_payouts: boolean;
  payout_schedule: string;
  require_milestone_approval: boolean;
  allow_custom_milestones: boolean;
  default_milestone_structure: string;
  payment_methods: string[];
  currency: string;
  tax_rate: number;
  enable_tax_collection: boolean;
};

export default function PaymentSettings() {
  const { user } = useOptimizedAuth();
  const [settings, setSettings] = useState<PaymentSettings>({
    platform_fee_percentage: 15,
    minimum_project_amount: 500,
    default_deposit_percentage: 30,
    default_payment_terms: 30,
    enable_automatic_payouts: true,
    payout_schedule: 'weekly',
    require_milestone_approval: true,
    allow_custom_milestones: true,
    default_milestone_structure: 'deposit_progress_final',
    payment_methods: ['paypal', 'credit_card', 'bank_transfer'], // ADDED PayPal as primary
    currency: 'USD',
    tax_rate: 0,
    enable_tax_collection: false
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchPaymentSettings();
    }
  }, [user]);

  const fetchPaymentSettings = async () => {
    setLoading(true);
    try {
      // In a real implementation, you would fetch these settings from your database
      // For now, we'll just simulate a successful fetch with default values
      await new Promise(resolve => setTimeout(resolve, 500));

      // If you have a settings table, you would fetch from there
      // const { data, error } = await supabase
      //   .from('system_settings')
      //   .select('*')
      //   .eq('category', 'payment')
      //   .single();

      // if (error) throw error;
      // setSettings(data);

      // For now, we'll just use the default values
      setSettings({
        platform_fee_percentage: 15,
        minimum_project_amount: 500,
        default_deposit_percentage: 30,
        default_payment_terms: 30,
        enable_automatic_payouts: true,
        payout_schedule: 'weekly',
        require_milestone_approval: true,
        allow_custom_milestones: true,
        default_milestone_structure: 'deposit_progress_final',
        payment_methods: ['paypal', 'credit_card', 'bank_transfer'], // ADDED PayPal as primary
        currency: 'USD',
        tax_rate: 0,
        enable_tax_collection: false
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching payment settings:', error);
        setError(error.message || 'Failed to load payment settings');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      setSettings({
        ...settings,
        [name]: (e.target as HTMLInputElement).checked
      });
    } else if (type === 'number') {
      setSettings({
        ...settings,
        [name]: parseFloat(value)
      });
    } else {
      setSettings({
        ...settings,
        [name]: value
      });
    }
  };

  const handlePaymentMethodChange = (method: string) => {
    const currentMethods = [...settings.payment_methods];

    if (currentMethods.includes(method)) {
      // Remove the method if it's already selected
      setSettings({
        ...settings,
        payment_methods: currentMethods.filter(m => m !== method)
      });
    } else {
      // Add the method if it's not selected
      setSettings({
        ...settings,
        payment_methods: [...currentMethods, method]
      });
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // In a real implementation, you would save these settings to your database
      // For now, we'll just simulate a successful save
      await new Promise(resolve => setTimeout(resolve, 1000));

      // If you have a settings table, you would update it
      // const { error } = await supabase
      //   .from('system_settings')
      //   .update(settings)
      //   .eq('category', 'payment');

      // if (error) throw error;

      setSuccess('Payment settings saved successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error saving payment settings:', error);
        setError(error.message || 'Failed to save payment settings');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading payment settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center">
        <Link href="/admin/finance" className="mr-4">
          <Button variant="ghost" className="p-0 h-auto">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Payment Settings</h1>
          <p className="text-gray-500">Configure payment and payout settings for the platform</p>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold flex items-center">
            <SettingsIcon className="h-5 w-5 mr-2 text-gray-400" />
            Payment Configuration
          </h2>
        </div>
        <div className="p-6">
          <div className="space-y-8">
            {/* Platform Fees */}
            <div>
              <h3 className="text-md font-medium mb-4 flex items-center">
                <Percent className="h-5 w-5 mr-2 text-gray-400" />
                Platform Fees
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="platform_fee_percentage" className="block text-sm font-medium text-gray-700 mb-1">
                    Platform Fee Percentage
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      id="platform_fee_percentage"
                      name="platform_fee_percentage"
                      value={settings.platform_fee_percentage}
                      onChange={handleChange}
                      min="0"
                      max="100"
                      step="0.1"
                      className="w-full px-4 py-2 border rounded-md pr-8"
                    />
                    <span className="absolute right-3 top-2 text-gray-500">%</span>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Percentage fee charged on each transaction
                  </p>
                </div>

                <div>
                  <label htmlFor="minimum_project_amount" className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Project Amount
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">$</span>
                    <input
                      type="number"
                      id="minimum_project_amount"
                      name="minimum_project_amount"
                      value={settings.minimum_project_amount}
                      onChange={handleChange}
                      min="0"
                      className="w-full px-4 py-2 border rounded-md pl-8"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Minimum amount allowed for new projects
                  </p>
                </div>
              </div>
            </div>

            {/* Payment Structure */}
            <div>
              <h3 className="text-md font-medium mb-4 flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-gray-400" />
                Payment Structure
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="default_deposit_percentage" className="block text-sm font-medium text-gray-700 mb-1">
                    Default Deposit Percentage
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      id="default_deposit_percentage"
                      name="default_deposit_percentage"
                      value={settings.default_deposit_percentage}
                      onChange={handleChange}
                      min="0"
                      max="100"
                      className="w-full px-4 py-2 border rounded-md pr-8"
                    />
                    <span className="absolute right-3 top-2 text-gray-500">%</span>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Default percentage for initial project deposits
                  </p>
                </div>

                <div>
                  <label htmlFor="default_payment_terms" className="block text-sm font-medium text-gray-700 mb-1">
                    Default Payment Terms (days)
                  </label>
                  <input
                    type="number"
                    id="default_payment_terms"
                    name="default_payment_terms"
                    value={settings.default_payment_terms}
                    onChange={handleChange}
                    min="1"
                    className="w-full px-4 py-2 border rounded-md"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Number of days clients have to pay invoices
                  </p>
                </div>

                <div>
                  <label htmlFor="default_milestone_structure" className="block text-sm font-medium text-gray-700 mb-1">
                    Default Milestone Structure
                  </label>
                  <select
                    id="default_milestone_structure"
                    name="default_milestone_structure"
                    value={settings.default_milestone_structure}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  >
                    <option value="deposit_final">Deposit + Final Payment (2 milestones)</option>
                    <option value="deposit_progress_final">Deposit + Progress + Final (3 milestones)</option>
                    <option value="equal_installments">Equal Installments (4 milestones)</option>
                    <option value="custom">Custom (Project-specific)</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    Default milestone structure for new projects
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="allow_custom_milestones"
                    name="allow_custom_milestones"
                    checked={settings.allow_custom_milestones}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                  />
                  <label htmlFor="allow_custom_milestones" className="ml-2 block text-sm text-gray-700">
                    Allow custom milestone structures
                  </label>
                </div>
              </div>
            </div>

            {/* Payout Settings */}
            <div>
              <h3 className="text-md font-medium mb-4 flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-gray-400" />
                Payout Settings
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="payout_schedule" className="block text-sm font-medium text-gray-700 mb-1">
                    Payout Schedule
                  </label>
                  <select
                    id="payout_schedule"
                    name="payout_schedule"
                    value={settings.payout_schedule}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="biweekly">Bi-weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    How often automatic payouts are processed
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enable_automatic_payouts"
                    name="enable_automatic_payouts"
                    checked={settings.enable_automatic_payouts}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                  />
                  <label htmlFor="enable_automatic_payouts" className="ml-2 block text-sm text-gray-700">
                    Enable automatic payouts to designers
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="require_milestone_approval"
                    name="require_milestone_approval"
                    checked={settings.require_milestone_approval}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                  />
                  <label htmlFor="require_milestone_approval" className="ml-2 block text-sm text-gray-700">
                    Require admin approval for milestone completion
                  </label>
                </div>
              </div>
            </div>

            {/* Payment Methods */}
            <div>
              <h3 className="text-md font-medium mb-4 flex items-center">
                <Shield className="h-5 w-5 mr-2 text-gray-400" />
                Payment Methods
              </h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="payment_method_credit_card"
                    checked={settings.payment_methods.includes('credit_card')}
                    onChange={() => handlePaymentMethodChange('credit_card')}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                  />
                  <label htmlFor="payment_method_credit_card" className="ml-2 block text-sm text-gray-700">
                    Credit Card / Debit Card
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="payment_method_bank_transfer"
                    checked={settings.payment_methods.includes('bank_transfer')}
                    onChange={() => handlePaymentMethodChange('bank_transfer')}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                  />
                  <label htmlFor="payment_method_bank_transfer" className="ml-2 block text-sm text-gray-700">
                    Bank Transfer / ACH
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="payment_method_paypal"
                    checked={settings.payment_methods.includes('paypal')}
                    onChange={() => handlePaymentMethodChange('paypal')}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                  />
                  <label htmlFor="payment_method_paypal" className="ml-2 block text-sm text-gray-700">
                    PayPal
                  </label>
                </div>

                {settings.payment_methods.includes('paypal') && (
                  <div className="ml-6 mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                      <div className="text-sm text-blue-700">
                        <p className="font-medium mb-1">PayPal Integration via Stripe</p>
                        <p>
                          PayPal payments are processed through Stripe using the PayPal payment method.
                          Make sure you have enabled PayPal in your Stripe Dashboard under Settings &gt; Payment Methods.
                        </p>
                        <a
                          href="https://stripe.com/docs/payments/paypal"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline mt-1 inline-block"
                        >
                          Learn more about Stripe PayPal integration
                        </a>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Tax Settings */}
            <div>
              <h3 className="text-md font-medium mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2 text-gray-400" />
                Tax Settings
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enable_tax_collection"
                    name="enable_tax_collection"
                    checked={settings.enable_tax_collection}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary border-gray-300 rounded"
                  />
                  <label htmlFor="enable_tax_collection" className="ml-2 block text-sm text-gray-700">
                    Enable tax collection
                  </label>
                </div>

                {settings.enable_tax_collection && (
                  <div>
                    <label htmlFor="tax_rate" className="block text-sm font-medium text-gray-700 mb-1">
                      Default Tax Rate
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="tax_rate"
                        name="tax_rate"
                        value={settings.tax_rate}
                        onChange={handleChange}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-full px-4 py-2 border rounded-md pr-8"
                      />
                      <span className="absolute right-3 top-2 text-gray-500">%</span>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      Default tax rate applied to transactions
                    </p>
                  </div>
                )}

                <div>
                  <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                    Default Currency
                  </label>
                  <select
                    id="currency"
                    name="currency"
                    value={settings.currency}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                    <option value="CAD">CAD - Canadian Dollar</option>
                    <option value="AUD">AUD - Australian Dollar</option>
                    <option value="SAR">SAR - Saudi Riyal</option>
                    <option value="AED">AED - UAE Dirham</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <Button
              onClick={saveSettings}
              disabled={saving}
              className="flex items-center"
            >
              {saving ? (
                <span className="flex items-center">
                  <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                  Saving...
                </span>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
