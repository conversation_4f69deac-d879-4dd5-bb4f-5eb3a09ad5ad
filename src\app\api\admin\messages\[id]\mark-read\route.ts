import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the message to check if user is authorized to mark it as read
    const { data: message, error: messageError } = await supabase
      .from('admin_messages')
      .select(`
        id,
        recipient_id,
        recipient_role,
        title,
        content,
        message_type,
        priority,
        read_at,
        action_required,
        action_url,
        expires_at,
        created_by,
        created_at
      `)
      .eq('id', params.id)
      .single();

    if (messageError || !message) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    // Check if user is authorized to mark this message as read
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Check if this message is for this user
    const isForUser = 
      message.recipient_id === user.id || // Specific user
      message.recipient_role === 'all' || // All users
      message.recipient_role === profile.role; // User's role

    if (!isForUser) {
      return NextResponse.json(
        { error: 'Not authorized to mark this message as read' },
        { status: 403 }
      );
    }

    // For individual messages, update the read_at field
    if (message.recipient_id === user.id) {
      const { error: updateError } = await supabase
        .from('admin_messages')
        .update({ read_at: new Date().toISOString() })
        .eq('id', params.id);

      if (updateError) {
        console.error('Error marking message as read:', updateError);
        return NextResponse.json(
          { error: 'Failed to mark message as read' },
          { status: 500 }
        );
      }
    } else {
      // For broadcast messages (role-based or all users), we need a separate read status table
      // For now, we'll create a simple read status record
      // In a production system, you'd want a dedicated admin_message_reads table
      
      // Check if already marked as read by this user
      const { data: existingRead } = await supabase
        .from('admin_message_reads')
        .select('id')
        .eq('message_id', params.id)
        .eq('user_id', user.id)
        .single();

      if (!existingRead) {
        // Create read status record
        const { error: readError } = await supabase
          .from('admin_message_reads')
          .insert({
            message_id: params.id,
            user_id: user.id,
            read_at: new Date().toISOString()
          });

        if (readError) {
          console.error('Error creating read status:', readError);
          // Don't fail if the table doesn't exist yet - this is for future enhancement
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Message marked as read'
    });

  } catch (error) {
    console.error('Error in mark-read API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
