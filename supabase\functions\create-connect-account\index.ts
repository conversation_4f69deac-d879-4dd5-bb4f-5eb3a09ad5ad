import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { designerId, email, country = 'US', businessType = 'individual' } = await req.json()

    if (!designerId || !email) {
      return new Response(
        JSON.stringify({ error: 'Designer ID and email are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get designer profile
    const { data: designer, error: designerError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', designerId)
      .single()

    if (designerError || !designer) {
      return new Response(
        JSON.stringify({ error: 'Designer not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if designer already has a Stripe Connect account
    const { data: existingAccount } = await supabase
      .from('designer_stripe_accounts')
      .select('*')
      .eq('designer_id', designerId)
      .single()

    if (existingAccount && existingAccount.stripe_account_id) {
      return new Response(
        JSON.stringify({ 
          accountId: existingAccount.stripe_account_id,
          status: existingAccount.account_status,
          existing: true
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create Stripe Connect account
    const account = await stripe.accounts.create({
      type: 'express',
      country: country,
      email: email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: businessType,
      metadata: {
        designer_id: designerId,
        platform: 'architecture-platform'
      }
    })

    // Store account information in database
    const { error: insertError } = await supabase
      .from('designer_stripe_accounts')
      .upsert({
        designer_id: designerId,
        stripe_account_id: account.id,
        account_status: 'pending',
        country: country,
        business_type: businessType,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (insertError) {
      console.error('Error storing Stripe account:', insertError)
      return new Response(
        JSON.stringify({ error: 'Failed to store account information' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${Deno.env.get('FRONTEND_URL')}/designer/settings/payment?refresh=true`,
      return_url: `${Deno.env.get('FRONTEND_URL')}/designer/settings/payment?success=true`,
      type: 'account_onboarding',
    })

    return new Response(
      JSON.stringify({
        accountId: account.id,
        onboardingUrl: accountLink.url,
        status: 'pending'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error creating Stripe Connect account:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
