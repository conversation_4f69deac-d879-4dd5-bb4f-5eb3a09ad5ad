import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      persistSession: false
    }
  }
);

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

/**
 * API route to fix designer application file paths
 * This corrects the mismatch between database paths and actual R2 file keys
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      return NextResponse.json(
        { error: 'Service role key not configured' },
        { status: 500 }
      );
    }

    console.log('Starting designer file path migration...');

    // Get all designer applications with file URLs
    const { data: applications, error: fetchError } = await supabaseAdmin
      .from('designer_applications')
      .select('id, resume_url, portfolio_files')
      .not('resume_url', 'is', null)
      .or('portfolio_files.not.is.null');

    if (fetchError) {
      console.error('Error fetching applications:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      );
    }

    console.log(`Found ${applications.length} applications to process`);

    const results = {
      processed: 0,
      fixed: 0,
      errors: 0,
      details: [] as any[]
    };

    for (const app of applications) {
      results.processed++;
      
      try {
        let needsUpdate = false;
        let newResumeUrl = app.resume_url;
        let newPortfolioFiles = app.portfolio_files;

        // Fix resume URL if it exists
        if (app.resume_url) {
          const fixedResumeUrl = await findCorrectFilePath(app.resume_url, 'resume');
          if (fixedResumeUrl && fixedResumeUrl !== app.resume_url) {
            newResumeUrl = fixedResumeUrl;
            needsUpdate = true;
            console.log(`Fixed resume URL for ${app.id}: ${app.resume_url} -> ${fixedResumeUrl}`);
          }
        }

        // Fix portfolio files if they exist
        if (app.portfolio_files && app.portfolio_files.length > 0) {
          const fixedPortfolioFiles = [];
          for (const portfolioFile of app.portfolio_files) {
            const fixedPortfolioUrl = await findCorrectFilePath(portfolioFile, 'portfolio');
            if (fixedPortfolioUrl && fixedPortfolioUrl !== portfolioFile) {
              fixedPortfolioFiles.push(fixedPortfolioUrl);
              needsUpdate = true;
              console.log(`Fixed portfolio URL for ${app.id}: ${portfolioFile} -> ${fixedPortfolioUrl}`);
            } else {
              fixedPortfolioFiles.push(portfolioFile);
            }
          }
          newPortfolioFiles = fixedPortfolioFiles;
        }

        // Update the database if changes were made
        if (needsUpdate) {
          const { error: updateError } = await supabaseAdmin
            .from('designer_applications')
            .update({
              resume_url: newResumeUrl,
              portfolio_files: newPortfolioFiles,
              updated_at: new Date().toISOString()
            })
            .eq('id', app.id);

          if (updateError) {
            console.error(`Error updating application ${app.id}:`, updateError);
            results.errors++;
            results.details.push({
              id: app.id,
              status: 'error',
              error: updateError.message
            });
          } else {
            results.fixed++;
            results.details.push({
              id: app.id,
              status: 'fixed',
              oldResumeUrl: app.resume_url,
              newResumeUrl: newResumeUrl,
              oldPortfolioFiles: app.portfolio_files,
              newPortfolioFiles: newPortfolioFiles
            });
          }
        } else {
          results.details.push({
            id: app.id,
            status: 'no_changes_needed'
          });
        }

      } catch (error) {
        console.error(`Error processing application ${app.id}:`, error);
        results.errors++;
        results.details.push({
          id: app.id,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log('Migration completed:', results);

    return NextResponse.json({
      success: true,
      message: 'Designer file path migration completed',
      results
    }, { status: 200 });

  } catch (error) {
    console.error('Error in file path migration:', error);
    return NextResponse.json(
      { error: 'Migration failed' },
      { status: 500 }
    );
  }
}

/**
 * Find the correct file path in R2 based on the database path
 */
async function findCorrectFilePath(dbPath: string, fileType: 'resume' | 'portfolio'): Promise<string | null> {
  try {
    // Extract the folder (application ID) from the database path
    const pathParts = dbPath.split('/');
    if (pathParts.length < 2) {
      console.warn(`Invalid path format: ${dbPath}`);
      return null;
    }

    const folder = pathParts[0]; // e.g., "temp-1748470033810-bqauz08uedr"
    const dbFileName = pathParts[1]; // e.g., "resume-Starting Project Request.pdf"

    // List all files in the folder
    const command = new ListObjectsV2Command({
      Bucket: 'designer-applications',
      Prefix: `${folder}/`,
      MaxKeys: 100
    });

    const response = await s3Client.send(command);
    
    if (!response.Contents || response.Contents.length === 0) {
      console.warn(`No files found in folder: ${folder}`);
      return null;
    }

    // Find the matching file based on type and name pattern
    for (const object of response.Contents) {
      if (!object.Key) continue;

      const fileName = object.Key.split('/').pop() || '';
      
      // Check if this is the file we're looking for
      if (fileType === 'resume' && fileName.includes('resume-')) {
        // For resume files, match the base name (ignoring timestamp and space/hyphen differences)
        const dbBaseName = dbFileName.replace('resume-', '').replace(/\s+/g, '-');
        const r2BaseName = fileName.replace(/^\d+-resume-/, ''); // Remove timestamp prefix
        
        if (r2BaseName === dbBaseName || 
            r2BaseName.replace(/-/g, ' ') === dbBaseName.replace(/-/g, ' ')) {
          console.log(`Found matching resume file: ${object.Key}`);
          return object.Key;
        }
      } else if (fileType === 'portfolio' && fileName.includes('portfolio-')) {
        // For portfolio files, match the base name (ignoring timestamp and index)
        const dbBaseName = dbFileName.replace(/^portfolio-\d+-/, '').replace(/\s+/g, '-');
        const r2BaseName = fileName.replace(/^\d+-portfolio-\d+-/, ''); // Remove timestamp and index prefix
        
        if (r2BaseName === dbBaseName || 
            r2BaseName.replace(/-/g, ' ') === dbBaseName.replace(/-/g, ' ')) {
          console.log(`Found matching portfolio file: ${object.Key}`);
          return object.Key;
        }
      }
    }

    console.warn(`No matching file found for: ${dbPath}`);
    return null;

  } catch (error) {
    console.error(`Error finding correct file path for ${dbPath}:`, error);
    return null;
  }
}
