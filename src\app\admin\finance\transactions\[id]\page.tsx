"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  ArrowLeft,
  Save,
  AlertCircle,
  CheckCircle,
  CreditCard,
  Calendar,
  Clock,
  User,
  FileText,
  DollarSign,
  Tag,
  ExternalLink,
  Trash,
  Edit
} from "lucide-react";

type Transaction = {
  id: string;
  transaction_id: string;
  amount: number;
  status: string;
  type: string;
  created_at: string;
  processed_at: string | null;
  notes: string | null;
  project_id: string | null;
  project_title: string | null;
  milestone_id: string | null;
  milestone_title: string | null;
  client_id: string | null;
  client_name: string | null;
  client_email: string | null;
  designer_id: string | null;
  designer_name: string | null;
  designer_email: string | null;
};

export default function TransactionDetail() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const transactionId = params.id as string;

  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedTransaction, setEditedTransaction] = useState<Partial<Transaction>>({});
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  useEffect(() => {
    if (user && transactionId) {
      fetchTransactionData();
    }
  }, [user, transactionId]);

  const fetchTransactionData = async () => {
    setLoading(true);
    try {
      // Fetch transaction details
      const { data: transactionData, error: transactionError } = await supabase
        .from('transactions')
        .select(`
          id,
          transaction_id,
          amount,
          status,
          type,
          created_at,
          processed_at,
          notes,
          project_id,
          milestone_id,
          client_id,
          designer_id
        `)
        .eq('id', transactionId)
        .single();

      if (transactionError) throw transactionError;

      // Fetch related data
      const [projectResponse, milestoneResponse, clientResponse, designerResponse] = await Promise.all([
        transactionData.project_id
          ? supabase.from('projects').select('title').eq('id', transactionData.project_id).single()
          : { data: null, error: null },

        transactionData.milestone_id
          ? supabase.from('project_milestones').select('title').eq('id', transactionData.milestone_id).single()
          : { data: null, error: null },

        transactionData.client_id
          ? supabase.from('profiles').select('full_name, email').eq('id', transactionData.client_id).single()
          : { data: null, error: null },

        transactionData.designer_id
          ? supabase.from('profiles').select('full_name, email').eq('id', transactionData.designer_id).single()
          : { data: null, error: null }
      ]);

      // Format transaction with related data
      const formattedTransaction: Transaction = {
        ...transactionData,
        project_title: projectResponse.data?.title || null,
        milestone_title: milestoneResponse.data?.title || null,
        client_name: clientResponse.data?.full_name || null,
        client_email: clientResponse.data?.email || null,
        designer_name: designerResponse.data?.full_name || null,
        designer_email: designerResponse.data?.email || null
      };

      setTransaction(formattedTransaction);
      setEditedTransaction({
        status: formattedTransaction.status,
        notes: formattedTransaction.notes
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching transaction data:', error);
        setError(error.message || 'Failed to load transaction data');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedTransaction({
      ...editedTransaction,
      [name]: value
    });
  };

  const handleSave = async () => {
    if (!transaction) return;

    setUpdating(true);
    setError(null);
    setSuccess(null);

    try {
      // Update transaction in database
      const { error } = await supabase
        .from('transactions')
        .update({
          status: editedTransaction.status,
          notes: editedTransaction.notes,
          processed_at: editedTransaction.status === 'completed' && !transaction.processed_at
            ? new Date().toISOString()
            : transaction.processed_at
        })
        .eq('id', transactionId);

      if (error) throw error;

      // If the transaction is a milestone payment and status changed to completed,
      // update the milestone status as well
      if (
        transaction.milestone_id &&
        editedTransaction.status === 'completed' &&
        transaction.status !== 'completed'
      ) {
        const { error: milestoneError } = await supabase
          .from('project_milestones')
          .update({
            status: 'paid',
            paid_at: new Date().toISOString()
          })
          .eq('id', transaction.milestone_id);

        if (milestoneError) throw milestoneError;
      }

      setSuccess('Transaction updated successfully');
      setIsEditing(false);

      // Refresh transaction data
      await fetchTransactionData();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error updating transaction:', error);
        setError(error.message || 'Failed to update transaction');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!transaction) return;

    if (!deleteConfirm) {
      setDeleteConfirm(true);
      return;
    }

    setUpdating(true);
    setError(null);

    try {
      // Delete transaction from database
      const { error } = await supabase
        .from('transactions')
        .delete()
        .eq('id', transactionId);

      if (error) throw error;

      // Redirect back to transactions list
      router.push('/admin/finance');
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error deleting transaction:', error);
        setError(error.message || 'Failed to delete transaction');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
      setUpdating(false);
      setDeleteConfirm(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not processed';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment':
        return 'bg-brown-100 text-brown-800';
      case 'payout':
        return 'bg-brown-50 text-brown-800';
      case 'refund':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading transaction data...</p>
        </div>
      </div>
    );
  }

  if (error && !transaction) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
          <div className="mt-4">
            <Link href="/admin/finance">
              <Button variant="outline">
                Back to Transactions
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="p-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            Transaction not found
          </p>
          <div className="mt-4">
            <Link href="/admin/finance">
              <Button variant="outline">
                Back to Transactions
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/admin/finance" className="mr-4">
            <Button variant="ghost" className="p-0 h-auto">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Transaction Details</h1>
            <p className="text-gray-500">{transaction.transaction_id}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={() => setIsEditing(false)}
                disabled={updating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={updating}
                className="flex items-center"
              >
                {updating ? (
                  <span className="flex items-center">
                    <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                    Saving...
                  </span>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                onClick={() => setIsEditing(true)}
                className="flex items-center"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button
                variant={deleteConfirm ? "destructive" : "outline"}
                onClick={handleDelete}
                disabled={updating}
                className="flex items-center"
              >
                <Trash className="h-4 w-4 mr-2" />
                {deleteConfirm ? 'Confirm Delete' : 'Delete'}
              </Button>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Transaction Information</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="flex items-start">
                  <DollarSign className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Amount</p>
                    <p className="text-xl font-bold">{formatCurrency(transaction.amount)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Tag className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    {isEditing ? (
                      <select
                        name="status"
                        value={editedTransaction.status}
                        onChange={handleChange}
                        className="mt-1 w-full px-3 py-2 border rounded-md"
                      >
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                        <option value="failed">Failed</option>
                      </select>
                    ) : (
                      <p>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </span>
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-start">
                  <CreditCard className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Type</p>
                    <p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(transaction.type)}`}>
                        {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                      </span>
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Created Date</p>
                    <p className="font-medium">{formatDate(transaction.created_at)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Processed Date</p>
                    <p className="font-medium">{formatDate(transaction.processed_at)}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                {isEditing ? (
                  <textarea
                    name="notes"
                    value={editedTransaction.notes || ''}
                    onChange={handleChange}
                    rows={4}
                    className="w-full px-4 py-2 border rounded-md"
                    placeholder="Add notes about this transaction"
                  ></textarea>
                ) : (
                  <div className="p-4 bg-gray-50 rounded-md">
                    {transaction.notes || 'No notes available for this transaction.'}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Related Information */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mt-8">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Related Information</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {transaction.project_id && transaction.project_title && (
                  <div className="col-span-2">
                    <h3 className="text-md font-medium mb-3">Project</h3>
                    <div className="p-4 bg-gray-50 rounded-md flex justify-between items-center">
                      <div>
                        <p className="font-medium">{transaction.project_title}</p>
                        <p className="text-sm text-gray-500">Project ID: {transaction.project_id}</p>
                      </div>
                      <Link href={`/admin/projects/${transaction.project_id}`}>
                        <Button variant="outline" size="sm" className="flex items-center">
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </Link>
                    </div>
                  </div>
                )}

                {transaction.milestone_id && transaction.milestone_title && (
                  <div className="col-span-2">
                    <h3 className="text-md font-medium mb-3">Milestone</h3>
                    <div className="p-4 bg-gray-50 rounded-md flex justify-between items-center">
                      <div>
                        <p className="font-medium">{transaction.milestone_title}</p>
                        <p className="text-sm text-gray-500">Milestone ID: {transaction.milestone_id}</p>
                      </div>
                      {transaction.project_id && (
                        <Link href={`/admin/projects/${transaction.project_id}/milestones`}>
                          <Button variant="outline" size="sm" className="flex items-center">
                            <ExternalLink className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                )}

                {transaction.client_id && transaction.client_name && (
                  <div>
                    <h3 className="text-md font-medium mb-3">Client</h3>
                    <div className="p-4 bg-gray-50 rounded-md flex justify-between items-center">
                      <div>
                        <p className="font-medium">{transaction.client_name}</p>
                        {transaction.client_email && (
                          <p className="text-sm text-gray-500">{transaction.client_email}</p>
                        )}
                      </div>
                      <Link href={`/admin/users/${transaction.client_id}`}>
                        <Button variant="outline" size="sm" className="flex items-center">
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </Link>
                    </div>
                  </div>
                )}

                {transaction.designer_id && transaction.designer_name && (
                  <div>
                    <h3 className="text-md font-medium mb-3">Designer</h3>
                    <div className="p-4 bg-gray-50 rounded-md flex justify-between items-center">
                      <div>
                        <p className="font-medium">{transaction.designer_name}</p>
                        {transaction.designer_email && (
                          <p className="text-sm text-gray-500">{transaction.designer_email}</p>
                        )}
                      </div>
                      <Link href={`/admin/users/${transaction.designer_id}`}>
                        <Button variant="outline" size="sm" className="flex items-center">
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-8">
          {/* Transaction Summary */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Transaction Summary</h2>
            </div>
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <span className="text-gray-500">Transaction ID</span>
                <span className="font-medium">{transaction.transaction_id}</span>
              </div>

              <div className="flex justify-between items-center mb-4">
                <span className="text-gray-500">Amount</span>
                <span className="font-bold">{formatCurrency(transaction.amount)}</span>
              </div>

              <div className="flex justify-between items-center mb-4">
                <span className="text-gray-500">Status</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                  {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                </span>
              </div>

              <div className="flex justify-between items-center mb-4">
                <span className="text-gray-500">Type</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(transaction.type)}`}>
                  {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-500">Date</span>
                <span className="text-sm">{formatDate(transaction.created_at)}</span>
              </div>

              {transaction.processed_at && (
                <div className="flex justify-between items-center mt-4">
                  <span className="text-gray-500">Processed</span>
                  <span className="text-sm">{formatDate(transaction.processed_at)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Quick Actions</h2>
            </div>
            <div className="p-6 space-y-3">
              {transaction.status === 'pending' && (
                <Button
                  className="w-full justify-between"
                  onClick={() => {
                    setEditedTransaction({...editedTransaction, status: 'completed'});
                    setIsEditing(true);
                  }}
                >
                  Mark as Completed
                  <CheckCircle className="h-4 w-4 ml-2" />
                </Button>
              )}

              {transaction.status === 'pending' && (
                <Button
                  variant="outline"
                  className="w-full justify-between text-red-600 hover:text-red-700"
                  onClick={() => {
                    setEditedTransaction({...editedTransaction, status: 'failed'});
                    setIsEditing(true);
                  }}
                >
                  Mark as Failed
                  <AlertCircle className="h-4 w-4 ml-2" />
                </Button>
              )}

              {transaction.project_id && (
                <Link href={`/admin/projects/${transaction.project_id}`}>
                  <Button variant="outline" className="w-full justify-between">
                    View Project
                    <FileText className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              )}

              {transaction.client_id && (
                <Link href={`/admin/users/${transaction.client_id}`}>
                  <Button variant="outline" className="w-full justify-between">
                    View Client
                    <User className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              )}

              {transaction.designer_id && (
                <Link href={`/admin/users/${transaction.designer_id}`}>
                  <Button variant="outline" className="w-full justify-between">
                    View Designer
                    <User className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
