'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  Dispute,
  DisputeMessage,
  DisputeAttachment,
  DisputeWithDetails,
  DisputeStatus
} from '@/types/dispute';
import { getDispute, updateDispute, createDisputeMessage } from '@/lib/api/disputes';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import { formatDistanceToNow, format } from 'date-fns';
import { FileUploader } from '@/components/shared/FileUploader';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { FileIcon, PaperclipIcon, SendIcon } from 'lucide-react';

interface DisputeDetailProps {
  disputeId: string;
}

export function DisputeDetail({ disputeId }: DisputeDetailProps) {
  const { token, user, profile } = useAuth();
  const router = useRouter();
  const [disputeData, setDisputeData] = useState<DisputeWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [newStatus, setNewStatus] = useState<DisputeStatus | ''>('');
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [activeTab, setActiveTab] = useState('messages');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const isAdmin = profile?.role === 'admin';

  useEffect(() => {
    if (!token) return;

    const fetchDisputeData = async () => {
      setLoading(true);
      try {
        const data = await getDispute(token, disputeId);
        setDisputeData(data);
        if (data.dispute.resolution_notes) {
          setResolutionNotes(data.dispute.resolution_notes);
        }
      } catch (error) {
        console.error('Error fetching dispute:', error);
        toast({
          title: 'Error',
          description: 'Failed to load dispute details',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDisputeData();
  }, [token, disputeId]);

  useEffect(() => {
    // Scroll to bottom of messages when new messages are loaded
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [disputeData?.messages]);

  const handleSendMessage = async () => {
    if (!token || !message.trim()) return;

    setSendingMessage(true);
    try {
      const newMessage = await createDisputeMessage(token, disputeId, {
        content: message.trim(),
      });

      setDisputeData(prev => {
        if (!prev) return null;
        return {
          ...prev,
          messages: [...prev.messages, newMessage],
        };
      });

      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    } finally {
      setSendingMessage(false);
    }
  };

  const handleUpdateStatus = async () => {
    if (!token || !newStatus) return;

    setUpdatingStatus(true);
    try {
      const updatedDispute = await updateDispute(token, disputeId, {
        status: newStatus,
        resolutionNotes: newStatus === 'resolved' ? resolutionNotes : undefined,
      });

      setDisputeData(prev => {
        if (!prev) return null;
        return {
          ...prev,
          dispute: {
            ...prev.dispute,
            status: updatedDispute.status,
            resolution_notes: updatedDispute.resolution_notes,
            resolved_by: updatedDispute.resolved_by,
            resolved_at: updatedDispute.resolved_at,
            resolver: updatedDispute.resolver,
          },
        };
      });

      setShowStatusDialog(false);
      toast({
        title: 'Status Updated',
        description: `Dispute status has been updated to ${newStatus.replace('_', ' ')}`,
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update dispute status',
        variant: 'destructive',
      });
    } finally {
      setUpdatingStatus(false);
    }
  };

  const getStatusBadgeVariant = (status: DisputeStatus) => {
    switch (status) {
      case 'open':
        return 'destructive';
      case 'under_review':
        return 'warning';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'secondary';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-5/6 mb-2" />
            <Skeleton className="h-4 w-4/6" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/4" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex gap-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!disputeData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Dispute Not Found</CardTitle>
          <CardDescription>
            The dispute you are looking for does not exist or you do not have permission to view it.
          </CardDescription>
        </CardHeader>
        <CardFooter>
          <Button asChild>
            <Link href="/disputes">Back to Disputes</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }

  const { dispute, messages, attachments } = disputeData;
  const createdAt = new Date(dispute.created_at);
  const timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });
  const formattedDate = format(createdAt, 'PPP');

  const isParticipant = user?.id === dispute.client_id || user?.id === dispute.designer_id;
  const canSendMessage = isAdmin || (isParticipant && dispute.status !== 'closed');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{dispute.title}</CardTitle>
              <CardDescription>
                Project: <Link href={`/projects/${dispute.project_id}`} className="hover:underline">
                  {dispute.projects.title}
                </Link>
              </CardDescription>
            </div>
            <div className="flex flex-col items-end gap-2">
              <Badge variant={getStatusBadgeVariant(dispute.status)}>
                {dispute.status.replace('_', ' ').toUpperCase()}
              </Badge>
              {isAdmin && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setNewStatus(dispute.status);
                    setShowStatusDialog(true);
                  }}
                >
                  Update Status
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col md:flex-row md:justify-between gap-4">
            <div className="flex items-center gap-4">
              <div>
                <p className="text-sm font-medium">Client</p>
                <div className="flex items-center gap-2 mt-1">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={dispute.client.avatar_url || undefined} alt={dispute.client.full_name} />
                    <AvatarFallback>{dispute.client.full_name.charAt(0).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm">{dispute.client.full_name}</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium">Designer</p>
                <div className="flex items-center gap-2 mt-1">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={dispute.designer.avatar_url || undefined} alt={dispute.designer.full_name} />
                    <AvatarFallback>{dispute.designer.full_name.charAt(0).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm">{dispute.designer.full_name}</span>
                </div>
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              <p>Created by {dispute.creator.full_name}</p>
              <p>Created {timeAgo}</p>
              <p>{formattedDate}</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Description</h3>
            <p className="whitespace-pre-line">{dispute.description}</p>
          </div>

          {dispute.resolution_notes && (
            <div className="bg-muted p-4 rounded-md">
              <h3 className="text-lg font-medium mb-2">Resolution Notes</h3>
              <p className="whitespace-pre-line">{dispute.resolution_notes}</p>
              {dispute.resolver && (
                <p className="text-sm text-muted-foreground mt-2">
                  Resolved by {dispute.resolver.full_name} on {format(new Date(dispute.resolved_at!), 'PPP')}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="messages">
            Messages ({messages.length})
          </TabsTrigger>
          <TabsTrigger value="attachments">
            Attachments ({attachments.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="messages" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Messages</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6 max-h-[500px] overflow-y-auto p-1">
                {messages.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">
                    No messages yet. Start the conversation by sending a message.
                  </p>
                ) : (
                  messages.map((message) => (
                    <div key={message.id} className="flex gap-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={message.profiles.avatar_url || undefined} alt={message.profiles.full_name} />
                        <AvatarFallback>{message.profiles.full_name.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{message.profiles.full_name}</span>
                            {message.profiles.role === 'admin' && (
                              <Badge variant="outline" className="text-xs">Admin</Badge>
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(message.created_at), 'PPp')}
                          </span>
                        </div>
                        <div className="bg-muted p-3 rounded-md">
                          <p className="whitespace-pre-line">{message.content}</p>
                          {message.attachment_url && (
                            <a
                              href={message.attachment_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-2 mt-2 text-sm text-primary hover:underline"
                            >
                              <FileIcon size={16} />
                              {message.attachment_name || 'Attachment'}
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>
            </CardContent>
            {canSendMessage && (
              <CardFooter>
                <div className="w-full space-y-4">
                  <Textarea
                    placeholder="Type your message here..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    className="min-h-[100px]"
                  />
                  <div className="flex justify-between items-center">
                    <FileUploader
                      onUploadComplete={(url, name, type) => {
                        // Handle file upload completion
                        // This would be implemented in the next step
                      }}
                      allowedFileTypes={['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx']}
                      maxSizeMB={10}
                    >
                      <Button type="button" variant="outline" size="sm">
                        <PaperclipIcon className="h-4 w-4 mr-2" />
                        Attach File
                      </Button>
                    </FileUploader>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!message.trim() || sendingMessage}
                    >
                      {sendingMessage ? 'Sending...' : 'Send Message'}
                      <SendIcon className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="attachments" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Attachments</CardTitle>
            </CardHeader>
            <CardContent>
              {attachments.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  No attachments yet. Files can be attached when sending messages.
                </p>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {attachments.map((attachment) => (
                    <Card key={attachment.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-muted p-2 rounded">
                            <FileIcon className="h-8 w-8 text-primary" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{attachment.file_name}</p>
                            <p className="text-xs text-muted-foreground">
                              Uploaded by {attachment.profiles.full_name} • {formatDistanceToNow(new Date(attachment.created_at), { addSuffix: true })}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="bg-muted p-2">
                        <Button asChild variant="ghost" size="sm" className="w-full">
                          <a
                            href={attachment.file_url}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            Download
                          </a>
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
            {canSendMessage && (
              <CardFooter>
                <FileUploader
                  onUploadComplete={(url, name, type) => {
                    // Handle file upload completion
                    // This would be implemented in the next step
                  }}
                  allowedFileTypes={['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx']}
                  maxSizeMB={10}
                >
                  <Button type="button">
                    <PaperclipIcon className="h-4 w-4 mr-2" />
                    Upload New Attachment
                  </Button>
                </FileUploader>
              </CardFooter>
            )}
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Dispute Status</DialogTitle>
            <DialogDescription>
              Change the status of this dispute. If resolving, you can add resolution notes.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={newStatus} onValueChange={(value) => setNewStatus(value as DisputeStatus)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {newStatus === 'resolved' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Resolution Notes</label>
                <Textarea
                  placeholder="Explain how this dispute was resolved..."
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowStatusDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateStatus} disabled={!newStatus || updatingStatus}>
              {updatingStatus ? 'Updating...' : 'Update Status'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
