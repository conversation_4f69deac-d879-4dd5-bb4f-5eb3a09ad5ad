import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/manager/projects
 * Get projects assigned to the authenticated manager
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const priority = searchParams.get('priority') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is manager or admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['manager', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // MANAGERS SEE ALL PROJECTS - Watchdog role
    let query = supabase
      .from('projects')
      .select(`
        *,
        client:profiles!projects_client_id_fkey(full_name, avatar_url, email),
        designer:profiles!projects_designer_id_fkey(full_name, avatar_url, email),
        milestones:project_milestones(id, title, status, due_date),
        assignments:project_assignments(id, manager_id, status, priority, assigned_at)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by project status
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    const { data: assignments, error } = await query;

    if (error) {
      console.error('Error fetching manager projects:', error);
      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 });
    }

    // Get additional data for each project
    const projectsWithMetrics = await Promise.all(
      (assignments || []).map(async (assignment) => {
        const projectId = assignment.project_id;

        // Get quality reviews count
        const { count: qualityReviewsCount } = await supabase
          .from('quality_reviews')
          .select('id', { count: 'exact', head: true })
          .eq('project_id', projectId);

        // Get pending quality reviews
        const { count: pendingReviewsCount } = await supabase
          .from('quality_reviews')
          .select('id', { count: 'exact', head: true })
          .eq('project_id', projectId)
          .eq('status', 'pending');

        // Get negotiation sessions count
        const { count: negotiationSessionsCount } = await supabase
          .from('negotiation_sessions')
          .select('id', { count: 'exact', head: true })
          .eq('project_id', projectId);

        // Get active negotiation sessions
        const { count: activeNegotiationsCount } = await supabase
          .from('negotiation_sessions')
          .select('id', { count: 'exact', head: true })
          .eq('project_id', projectId)
          .eq('status', 'active');

        // Get milestones progress
        const { data: milestones } = await supabase
          .from('project_milestones')
          .select('status')
          .eq('project_id', projectId);

        const totalMilestones = milestones?.length || 0;
        const completedMilestones = milestones?.filter(m => m.status === 'completed').length || 0;
        const progressPercentage = totalMilestones > 0 ? (completedMilestones / totalMilestones) * 100 : 0;

        return {
          ...assignment,
          metrics: {
            quality_reviews_count: qualityReviewsCount || 0,
            pending_reviews_count: pendingReviewsCount || 0,
            negotiation_sessions_count: negotiationSessionsCount || 0,
            active_negotiations_count: activeNegotiationsCount || 0,
            total_milestones: totalMilestones,
            completed_milestones: completedMilestones,
            progress_percentage: Math.round(progressPercentage)
          }
        };
      })
    );

    // Get total count for pagination
    let countQuery = supabase
      .from('projects')
      .select('id', { count: 'exact', head: true });

    if (status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching projects:', error);
      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 });
    }

    const { count } = await countQuery;

    return NextResponse.json({
      projects: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in GET /api/manager/projects:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/manager/projects
 * Assign a project to a manager (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const {
      project_id,
      manager_id,
      priority = 'normal',
      notes
    } = await request.json();

    if (!project_id || !manager_id) {
      return NextResponse.json(
        { error: 'Project ID and Manager ID are required' },
        { status: 400 }
      );
    }

    // Check if project exists
    const { data: project } = await supabase
      .from('projects')
      .select('id, title')
      .eq('id', project_id)
      .single();

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Check if manager exists and has manager role
    const { data: manager } = await supabase
      .from('profiles')
      .select('id, full_name, role')
      .eq('id', manager_id)
      .single();

    if (!manager || manager.role !== 'manager') {
      return NextResponse.json({ error: 'Invalid manager' }, { status: 400 });
    }

    // Check if project is already assigned to a manager
    const { data: existingAssignment } = await supabase
      .from('project_assignments')
      .select('id')
      .eq('project_id', project_id)
      .eq('status', 'active')
      .single();

    if (existingAssignment) {
      return NextResponse.json(
        { error: 'Project is already assigned to a manager' },
        { status: 400 }
      );
    }

    // Create assignment
    const { data: assignment, error } = await supabase
      .from('project_assignments')
      .insert({
        project_id,
        manager_id,
        priority,
        notes,
        created_by: user.id,
        status: 'active'
      })
      .select(`
        *,
        projects:project_id (
          title,
          status
        ),
        manager:manager_id (
          full_name,
          email
        )
      `)
      .single();

    if (error) {
      console.error('Error creating project assignment:', error);
      return NextResponse.json({ error: 'Failed to assign project' }, { status: 500 });
    }

    // Update project with manager assignment
    await supabase
      .from('projects')
      .update({ assigned_manager_id: manager_id })
      .eq('id', project_id);

    // Create notification for manager
    await supabase
      .from('workflow_notifications')
      .insert({
        recipient_id: manager_id,
        notification_type: 'project_assigned',
        title: 'New Project Assignment',
        message: `You have been assigned to manage project: ${project.title}`,
        priority: priority === 'urgent' ? 'urgent' : 'normal',
        metadata: { project_id, assignment_id: assignment.id }
      });

    // Log manager activity
    await supabase
      .from('manager_activities')
      .insert({
        manager_id,
        project_id,
        activity_type: 'project_assignment',
        description: `Assigned to manage project: ${project.title}`,
        participants: [user.id, manager_id],
        outcome: 'assigned'
      });

    return NextResponse.json(assignment, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/manager/projects:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
