"use client";

import { useEffect, useCallback, useRef, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { dashboardKeys } from '@/hooks/useDashboardData';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

interface PollingConfig {
  enabled: boolean;
  tables: string[];
  priority: 'high' | 'medium' | 'low';
  intervals?: {
    critical: number;    // Auth, profile data (30s)
    important: number;   // Dashboard stats (2min)
    normal: number;      // Projects, proposals (5min)
    background: number;  // Messages, notifications (10min)
  };
  batchUpdates?: boolean;
  debounceMs?: number;
}

const DEFAULT_CONFIG: PollingConfig = {
  enabled: true,
  tables: ['projects', 'proposals', 'conversations', 'conversation_messages', 'notifications'],
  priority: 'medium',
  intervals: {
    critical: 30 * 1000,     // 30 seconds
    important: 2 * 60 * 1000, // 2 minutes
    normal: 5 * 60 * 1000,    // 5 minutes
    background: 10 * 60 * 1000 // 10 minutes
  },
  batchUpdates: true,
  debounceMs: 300
};

export function usePollingSync(config: Partial<PollingConfig> = {}) {
  const queryClient = useQueryClient();
  const { user, profile } = useOptimizedAuth();
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const intervalsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const lastSyncRef = useRef<Map<string, number>>(new Map());
  const isActiveRef = useRef(true);

  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  // Track user activity for intelligent polling
  useEffect(() => {
    const handleActivity = () => {
      isActiveRef.current = true;
    };

    const handleInactivity = () => {
      isActiveRef.current = false;
    };

    // Set up activity tracking
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Set inactive after 5 minutes of no activity
    const inactivityTimer = setInterval(() => {
      handleInactivity();
    }, 5 * 60 * 1000);

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      clearInterval(inactivityTimer);
    };
  }, []);

  // Get polling interval based on table priority
  const getPollingInterval = useCallback((table: string): number => {
    const intervals = finalConfig.intervals!;
    
    // Critical data - auth, profile, availability
    if (['profiles', 'auth', 'session'].includes(table)) {
      return intervals.critical;
    }
    
    // Important data - dashboard stats, projects
    if (['projects', 'proposals', 'dashboard_stats'].includes(table)) {
      return intervals.important;
    }
    
    // Background data - messages, notifications
    if (['conversations', 'conversation_messages', 'notifications'].includes(table)) {
      return intervals.background;
    }
    
    // Default to normal interval
    return intervals.normal;
  }, [finalConfig.intervals]);

  // Intelligent query invalidation
  const invalidateQueries = useCallback((table: string) => {
    console.log(`🔄 Polling sync: Refreshing ${table} data`);
    
    // Invalidate specific table queries
    queryClient.invalidateQueries({ queryKey: [table] });
    
    // Invalidate related dashboard stats for critical tables
    if (['projects', 'proposals'].includes(table) && user?.id && profile?.role) {
      queryClient.invalidateQueries({ 
        queryKey: dashboardKeys.batchedStats(user.id, profile.role)
      });
    }

    // Invalidate user-specific queries
    if (user?.id) {
      if (table === 'notifications') {
        queryClient.invalidateQueries({ queryKey: ['notifications', user.id] });
      }
      
      if (table === 'conversations' || table === 'conversation_messages') {
        queryClient.invalidateQueries({ queryKey: ['conversations', user.id] });
        queryClient.invalidateQueries({ queryKey: ['messages'] });
      }
    }
  }, [queryClient, user, profile]);

  // Setup polling for a specific table
  const setupPolling = useCallback((table: string, filter?: string) => {
    if (!user?.id || !finalConfig.enabled) return;

    const pollingKey = `${table}_${filter || 'all'}`;
    const interval = getPollingInterval(table);
    
    // Clear existing interval if any
    const existingInterval = intervalsRef.current.get(pollingKey);
    if (existingInterval) {
      clearInterval(existingInterval);
    }

    console.log(`📊 Setting up polling for ${table} (interval: ${interval / 1000}s)`);

    const pollFunction = () => {
      // Skip polling if user is inactive and it's background data
      if (!isActiveRef.current && interval >= finalConfig.intervals!.background) {
        console.log(`⏭️ Skipping ${table} poll - user inactive`);
        return;
      }

      // Check if enough time has passed since last sync
      const lastSync = lastSyncRef.current.get(pollingKey) || 0;
      const now = Date.now();
      
      if (now - lastSync < interval * 0.8) { // 80% of interval
        return;
      }

      // Update last sync time
      lastSyncRef.current.set(pollingKey, now);
      
      // Invalidate queries to trigger refetch
      invalidateQueries(table);
    };

    // Set up interval
    const intervalId = setInterval(pollFunction, interval);
    intervalsRef.current.set(pollingKey, intervalId);

    // Initial poll after a short delay
    setTimeout(pollFunction, 1000);

    setConnectionState('connected');
  }, [user, finalConfig.enabled, getPollingInterval, invalidateQueries, finalConfig.intervals]);

  // Manual sync trigger (maintains API compatibility)
  const triggerManualSync = useCallback((tables: string[]) => {
    if (!user?.id || !profile) {
      console.log('Cannot sync - user or profile not available');
      return;
    }
    
    console.log('🔄 Manual sync triggered for tables:', tables);
    
    tables.forEach(table => {
      // Force immediate invalidation regardless of timing
      lastSyncRef.current.set(`${table}_manual`, Date.now());
      invalidateQueries(table);
    });
  }, [user, profile, invalidateQueries]);

  // Setup polling based on user role
  useEffect(() => {
    if (!user?.id || !profile || !finalConfig.enabled) {
      console.log('Skipping polling setup - missing requirements', {
        hasUser: !!user?.id,
        hasProfile: !!profile,
        enabled: finalConfig.enabled
      });
      setConnectionState('disconnected');
      return;
    }

    console.log(`🚀 Setting up polling sync for ${profile.role}`);
    setConnectionState('connecting');

    // Clear existing intervals
    intervalsRef.current.forEach(interval => clearInterval(interval));
    intervalsRef.current.clear();

    if (profile.role === 'client') {
      // Client-specific polling
      setupPolling('projects', `client_id=eq.${user.id}`);
      setupPolling('proposals', `client_id=eq.${user.id}`);
      setupPolling('conversations');
      setupPolling('notifications', `user_id=eq.${user.id}`);
    } else if (profile.role === 'designer') {
      // Designer-specific polling
      setupPolling('projects', `designer_id=eq.${user.id}`);
      setupPolling('proposals', `designer_id=eq.${user.id}`);
      setupPolling('conversations');
      setupPolling('notifications', `user_id=eq.${user.id}`);
    } else if (profile.role === 'admin') {
      // Admin polling - no filters needed
      setupPolling('projects');
      setupPolling('proposals');
      setupPolling('conversations');
      setupPolling('notifications');
    }

    // Cleanup function
    return () => {
      console.log('Cleaning up polling intervals');
      intervalsRef.current.forEach(interval => clearInterval(interval));
      intervalsRef.current.clear();
      lastSyncRef.current.clear();
      setConnectionState('disconnected');
    };
  }, [user, profile, finalConfig.enabled, setupPolling]);

  // Reconnect function (maintains API compatibility)
  const reconnect = useCallback(() => {
    console.log('🔄 Reconnecting polling sync...');
    
    // Clear existing intervals
    intervalsRef.current.forEach(interval => clearInterval(interval));
    intervalsRef.current.clear();
    lastSyncRef.current.clear();
    
    setConnectionState('connecting');
    
    // Re-setup polling after a short delay
    setTimeout(() => {
      if (user?.id && profile) {
        if (profile.role === 'client') {
          setupPolling('projects', `client_id=eq.${user.id}`);
          setupPolling('proposals', `client_id=eq.${user.id}`);
          setupPolling('conversations');
          setupPolling('notifications', `user_id=eq.${user.id}`);
        } else if (profile.role === 'designer') {
          setupPolling('projects', `designer_id=eq.${user.id}`);
          setupPolling('proposals', `designer_id=eq.${user.id}`);
          setupPolling('conversations');
          setupPolling('notifications', `user_id=eq.${user.id}`);
        } else if (profile.role === 'admin') {
          setupPolling('projects');
          setupPolling('proposals');
          setupPolling('conversations');
          setupPolling('notifications');
        }
      }
    }, 1000);
  }, [user, profile, setupPolling]);

  return {
    connectionState,
    isConnected: connectionState === 'connected',
    activeSubscriptions: intervalsRef.current.size,
    triggerManualSync,
    reconnect,
    // Additional polling-specific methods
    pausePolling: useCallback(() => {
      intervalsRef.current.forEach(interval => clearInterval(interval));
      setConnectionState('disconnected');
    }, []),
    resumePolling: useCallback(() => {
      reconnect();
    }, [reconnect])
  };
}
