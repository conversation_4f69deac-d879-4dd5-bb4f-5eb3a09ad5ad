-- Check project_messages table data
SELECT 
  COUNT(*) as total_project_messages,
  COUNT(DISTINCT project_id) as unique_projects,
  COUNT(DISTINCT sender_id) as unique_senders,
  MIN(created_at) as oldest_message,
  MAX(created_at) as newest_message
FROM project_messages;

-- Sample project messages
SELECT 
  id,
  project_id,
  sender_id,
  content,
  created_at,
  is_read
FROM project_messages 
ORDER BY created_at DESC 
LIMIT 5;
