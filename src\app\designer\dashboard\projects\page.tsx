"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  FolderKanban,
  Calendar,
  DollarSign,
  User,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
  MessageSquare,
  FileText,
  ArrowRight,
  Filter,
  Search
} from "lucide-react";
import { Button } from "@/components/ui/Button";
import Link from "next/link";

interface Project {
  id: string;
  title: string;
  description: string;
  status: string;
  client_name: string;
  client_id: string;
  deadline?: string;
  budget?: number;
  progress?: number;
  created_at: string;
  assigned_at?: string;
  service_category?: string;
  location?: string;
  requirements?: string;
}

export default function MyProjectsPage() {
  const { user } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user) {
      fetchProjects();
    }
  }, [user, statusFilter]);

  const fetchProjects = async () => {
    if (!user) return;

    setLoading(true);
    try {
      let query = supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          status,
          deadline,
          budget,
          progress,
          created_at,
          assigned_at,
          service_category,
          location,
          requirements,
          profiles!projects_client_id_fkey(id, full_name)
        `)
        .eq('designer_id', user.id)
        .order('assigned_at', { ascending: false });

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      const { data, error } = await query;

      if (error) throw error;

      const formattedProjects = data?.map(project => ({
        id: project.id,
        title: project.title,
        description: project.description,
        status: project.status,
        client_name: project.profiles?.full_name || 'Unknown Client',
        client_id: project.profiles?.id || '',
        deadline: project.deadline,
        budget: project.budget,
        progress: project.progress || 0,
        created_at: project.created_at,
        assigned_at: project.assigned_at,
        service_category: project.service_category,
        location: project.location,
        requirements: project.requirements
      })) || [];

      setProjects(formattedProjects);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'on_hold':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Clock className="h-4 w-4" />;
      case 'in_progress':
        return <AlertCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <FolderKanban className="h-4 w-4" />;
    }
  };

  const filteredProjects = projects.filter(project =>
    project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.service_category?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const statusCounts = {
    all: projects.length,
    assigned: projects.filter(p => p.status === 'assigned').length,
    in_progress: projects.filter(p => p.status === 'in_progress').length,
    completed: projects.filter(p => p.status === 'completed').length,
    on_hold: projects.filter(p => p.status === 'on_hold').length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Projects</h1>
          <p className="text-gray-600">Projects assigned to you by admin</p>
        </div>
        <div className="text-sm text-gray-500">
          {filteredProjects.length} of {projects.length} projects
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {Object.entries(statusCounts).map(([status, count]) => (
          <motion.div
            key={status}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 rounded-lg border cursor-pointer transition-all ${
              statusFilter === status 
                ? 'bg-brown-50 border-brown-200 shadow-md' 
                : 'bg-white border-gray-200 hover:bg-gray-50'
            }`}
            onClick={() => setStatusFilter(status)}
          >
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{count}</p>
              <p className="text-sm text-gray-600 capitalize">
                {status === 'all' ? 'Total' : status.replace('_', ' ')}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search projects, clients, or categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          />
        </div>
        <Button variant="outline" className="flex items-center">
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
      </div>

      {/* Projects List */}
      {filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <FolderKanban className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {projects.length === 0 ? 'No projects assigned yet' : 'No projects match your search'}
          </h3>
          <p className="text-gray-500">
            {projects.length === 0 
              ? 'Projects assigned by admin will appear here' 
              : 'Try adjusting your search terms or filters'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">{project.title}</h3>
                    <span className={`px-3 py-1 text-xs font-medium rounded-full border ${getStatusColor(project.status)}`}>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(project.status)}
                        <span>{project.status.replace('_', ' ').toUpperCase()}</span>
                      </div>
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-4 line-clamp-2">{project.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="h-4 w-4 mr-2" />
                      <span>Client: {project.client_name}</span>
                    </div>
                    {project.service_category && (
                      <div className="flex items-center text-sm text-gray-500">
                        <FolderKanban className="h-4 w-4 mr-2" />
                        <span>{project.service_category}</span>
                      </div>
                    )}
                    {project.location && (
                      <div className="flex items-center text-sm text-gray-500">
                        <span>📍 {project.location}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-6">
                    {project.deadline && (
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-2" />
                        <span>Due: {new Date(project.deadline).toLocaleDateString()}</span>
                      </div>
                    )}
                    {project.budget && (
                      <div className="flex items-center text-sm text-gray-500">
                        <DollarSign className="h-4 w-4 mr-2" />
                        <span>${project.budget.toLocaleString()}</span>
                      </div>
                    )}
                    {project.assigned_at && (
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>Assigned: {new Date(project.assigned_at).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex flex-col items-end space-y-3">
                  <div className="text-right">
                    <div className="flex items-center mb-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className="bg-brown-600 h-2 rounded-full" 
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-500">{project.progress}%</span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link href={`/designer/projects/${project.id}`}>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </Link>
                    <Link href={`/designer/messages?project=${project.id}`}>
                      <Button size="sm" variant="outline">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Message
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
