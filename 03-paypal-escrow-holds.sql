-- =====================================================
-- SCRIPT 3: CREATE PAYPAL ESCROW HOLDS TABLE
-- =====================================================

-- PayPal Escrow Holds Table
CREATE TABLE IF NOT EXISTS paypal_escrow_holds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  paypal_order_id TEXT NOT NULL,
  paypal_capture_id TEXT NOT NULL UNIQUE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  milestone_id UUID REFERENCES project_milestones(id) ON DELETE SET NULL,
  client_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  designer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  gross_amount DECIMAL(10,2) NOT NULL,
  platform_fee DECIMAL(10,2) NOT NULL,
  processing_fee DECIMAL(10,2) NOT NULL,
  designer_amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'held' CHECK (status IN ('held', 'released', 'refunded')),
  hold_reason TEXT NOT NULL DEFAULT 'milestone_payment',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  released_at TIMESTAMP WITH TIME ZONE,
  release_approved_by UUID REFERENCES profiles(id),
  CONSTRAINT positive_amounts CHECK (
    gross_amount > 0 AND 
    platform_fee >= 0 AND 
    processing_fee >= 0 AND 
    designer_amount >= 0
  )
);

-- Verify completion
SELECT 'Script 3 completed: PayPal escrow holds table created' as status;
