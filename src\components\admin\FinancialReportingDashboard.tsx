"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  PieChart,
  Activity
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface FinancialMetrics {
  total_revenue: number;
  total_payouts: number;
  net_profit: number;
  active_projects: number;
  completed_projects: number;
  average_project_value: number;
  top_designers: DesignerEarnings[];
  revenue_by_month: MonthlyRevenue[];
  payment_methods: PaymentMethodStats[];
}

interface DesignerEarnings {
  designer_id: string;
  designer_name: string;
  total_earnings: number;
  projects_completed: number;
  average_rating: number;
}

interface MonthlyRevenue {
  month: string;
  revenue: number;
  payouts: number;
  profit: number;
  projects: number;
}

interface PaymentMethodStats {
  method: string;
  count: number;
  total_amount: number;
  percentage: number;
}

export function FinancialReportingDashboard() {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30'); // days
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchFinancialMetrics();
  }, [dateRange]);

  const fetchFinancialMetrics = async () => {
    setLoading(true);

    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(dateRange));

      // Fetch transactions (with error handling for missing table)
      let transactions: any[] = [];
      try {
        const { data: transactionsData, error: transactionsError } = await supabase
          .from('transactions')
          .select(`
            id,
            amount,
            type,
            status,
            payment_method,
            created_at,
            project_id,
            designer_id,
            client_id
          `)
          .gte('created_at', startDate.toISOString())
          .lte('created_at', endDate.toISOString())
          .eq('status', 'completed');

        if (transactionsError && !transactionsError.message.includes('does not exist')) {
          throw transactionsError;
        }

        transactions = transactionsData || [];
      } catch (error: any) {
        if (!error.message?.includes('does not exist')) {
          throw error;
        }
        // Table doesn't exist yet, use mock data
        transactions = [];
        console.warn('Transactions table not found, using mock data');
      }

      // Calculate basic metrics
      const revenue = (transactions || [])
        .filter(t => t.type === 'payment')
        .reduce((sum, t) => sum + t.amount, 0);

      const payouts = (transactions || [])
        .filter(t => t.type === 'payout')
        .reduce((sum, t) => sum + t.amount, 0);

      const netProfit = revenue - payouts;

      // Get project statistics
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('id, status, budget, created_at, designer_id')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (projectsError) throw projectsError;

      const activeProjects = (projects || []).filter(p =>
        ['assigned', 'in_progress'].includes(p.status)
      ).length;

      const completedProjects = (projects || []).filter(p =>
        p.status === 'completed'
      ).length;

      const averageProjectValue = completedProjects > 0
        ? (projects || [])
            .filter(p => p.status === 'completed' && p.budget)
            .reduce((sum, p) => sum + (p.budget || 0), 0) / completedProjects
        : 0;

      // Calculate designer earnings
      const designerEarnings = await calculateDesignerEarnings(transactions || []);

      // Calculate monthly revenue
      const monthlyRevenue = calculateMonthlyRevenue(transactions || [], projects || []);

      // Calculate payment method stats
      const paymentMethodStats = calculatePaymentMethodStats(transactions || []);

      setMetrics({
        total_revenue: revenue,
        total_payouts: payouts,
        net_profit: netProfit,
        active_projects: activeProjects,
        completed_projects: completedProjects,
        average_project_value: averageProjectValue,
        top_designers: designerEarnings,
        revenue_by_month: monthlyRevenue,
        payment_methods: paymentMethodStats
      });

    } catch (error) {
      console.error('Error fetching financial metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateDesignerEarnings = async (transactions: any[]): Promise<DesignerEarnings[]> => {
    const designerMap = new Map<string, any>();

    // Get designer names from profiles table
    const designerIds = [...new Set(transactions
      .filter(t => t.type === 'payout' && t.designer_id)
      .map(t => t.designer_id))];

    let designerProfiles: any[] = [];
    if (designerIds.length > 0) {
      try {
        const { data: profilesData } = await supabase
          .from('profiles')
          .select('id, full_name')
          .in('id', designerIds);

        designerProfiles = profilesData || [];
      } catch (error) {
        console.warn('Could not fetch designer profiles:', error);
      }
    }

    transactions
      .filter(t => t.type === 'payout' && t.designer_id)
      .forEach(transaction => {
        const designerId = transaction.designer_id;
        if (!designerMap.has(designerId)) {
          const profile = designerProfiles.find(p => p.id === designerId);
          designerMap.set(designerId, {
            designer_id: designerId,
            designer_name: profile?.full_name || 'Unknown Designer',
            total_earnings: 0,
            projects_completed: 0,
            average_rating: 4.5 // Mock rating
          });
        }

        const designer = designerMap.get(designerId);
        designer.total_earnings += transaction.amount;
        designer.projects_completed += 1;
      });

    return Array.from(designerMap.values())
      .sort((a, b) => b.total_earnings - a.total_earnings)
      .slice(0, 5);
  };

  const calculateMonthlyRevenue = (transactions: any[], projects: any[]): MonthlyRevenue[] => {
    const monthlyData = new Map<string, any>();

    // Initialize last 6 months
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      monthlyData.set(monthKey, {
        month: monthKey,
        revenue: 0,
        payouts: 0,
        profit: 0,
        projects: 0
      });
    }

    // Aggregate transaction data
    transactions.forEach(transaction => {
      const date = new Date(transaction.created_at);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      if (monthlyData.has(monthKey)) {
        const monthData = monthlyData.get(monthKey);
        if (transaction.type === 'payment') {
          monthData.revenue += transaction.amount;
        } else if (transaction.type === 'payout') {
          monthData.payouts += transaction.amount;
        }
        monthData.profit = monthData.revenue - monthData.payouts;
      }
    });

    // Count projects by month
    projects.forEach(project => {
      const date = new Date(project.created_at);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      if (monthlyData.has(monthKey)) {
        monthlyData.get(monthKey).projects += 1;
      }
    });

    return Array.from(monthlyData.values());
  };

  const calculatePaymentMethodStats = (transactions: any[]): PaymentMethodStats[] => {
    const methodMap = new Map<string, { count: number; total_amount: number }>();
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);

    transactions
      .filter(t => t.type === 'payment')
      .forEach(transaction => {
        const method = transaction.payment_method || 'Unknown';
        if (!methodMap.has(method)) {
          methodMap.set(method, { count: 0, total_amount: 0 });
        }

        const methodData = methodMap.get(method)!;
        methodData.count += 1;
        methodData.total_amount += transaction.amount;
      });

    return Array.from(methodMap.entries()).map(([method, data]) => ({
      method,
      count: data.count,
      total_amount: data.total_amount,
      percentage: totalAmount > 0 ? (data.total_amount / totalAmount) * 100 : 0
    }));
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchFinancialMetrics();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getChangeIndicator = (current: number, previous: number) => {
    if (previous === 0) return null;
    const change = ((current - previous) / previous) * 100;
    const isPositive = change > 0;

    return (
      <div className={`flex items-center text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <ArrowUpRight className="h-4 w-4 mr-1" /> : <ArrowDownRight className="h-4 w-4 mr-1" />}
        <span>{Math.abs(change).toFixed(1)}%</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-4 gap-4 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-100 rounded"></div>
            ))}
          </div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BarChart3 className="h-6 w-6 text-brown-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Financial Reporting Dashboard</h3>
              <p className="text-sm text-gray-600">Comprehensive financial analytics and insights</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Date Range Filter */}
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>

            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              size="sm"
            >
              {refreshing ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>

            <Button className="bg-brown-600 hover:bg-brown-700 text-white" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Revenue */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-gray-500 font-medium">Total Revenue</h4>
                <DollarSign className="h-6 w-6 text-green-500" />
              </div>
              <p className="text-3xl font-bold text-gray-900">{formatCurrency(metrics.total_revenue)}</p>
              <div className="mt-2 flex items-center">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+12.5% from last period</span>
              </div>
            </motion.div>

            {/* Net Profit */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: 0.1 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-gray-500 font-medium">Net Profit</h4>
                <TrendingUp className="h-6 w-6 text-brown-500" />
              </div>
              <p className="text-3xl font-bold text-gray-900">{formatCurrency(metrics.net_profit)}</p>
              <div className="mt-2 flex items-center">
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+8.2% from last period</span>
              </div>
            </motion.div>

            {/* Active Projects */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: 0.2 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-gray-500 font-medium">Active Projects</h4>
                <Activity className="h-6 w-6 text-blue-500" />
              </div>
              <p className="text-3xl font-bold text-gray-900">{metrics.active_projects}</p>
              <div className="mt-2 flex items-center">
                <span className="text-sm text-gray-600">{metrics.completed_projects} completed this period</span>
              </div>
            </motion.div>

            {/* Average Project Value */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: 0.3 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-gray-500 font-medium">Avg Project Value</h4>
                <PieChart className="h-6 w-6 text-purple-500" />
              </div>
              <p className="text-3xl font-bold text-gray-900">{formatCurrency(metrics.average_project_value)}</p>
              <div className="mt-2 flex items-center">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+5.8% from last period</span>
              </div>
            </motion.div>
          </div>

          {/* Charts and Tables */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Designers */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="bg-white rounded-lg shadow-sm border"
            >
              <div className="p-6 border-b">
                <h4 className="text-lg font-semibold text-gray-900">Top Earning Designers</h4>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {metrics.top_designers.map((designer, index) => (
                    <div key={designer.designer_id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-brown-100 text-brown-600 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{designer.designer_name}</p>
                          <p className="text-sm text-gray-500">{designer.projects_completed} projects</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{formatCurrency(designer.total_earnings)}</p>
                        <div className="flex items-center">
                          <span className="text-yellow-400 mr-1">★</span>
                          <span className="text-sm text-gray-500">{designer.average_rating}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Payment Methods */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              className="bg-white rounded-lg shadow-sm border"
            >
              <div className="p-6 border-b">
                <h4 className="text-lg font-semibold text-gray-900">Payment Methods</h4>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {metrics.payment_methods.map((method) => (
                    <div key={method.method} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 capitalize">{method.method}</p>
                        <p className="text-sm text-gray-500">{method.count} transactions</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{formatCurrency(method.total_amount)}</p>
                        <p className="text-sm text-gray-500">{formatPercentage(method.percentage)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Monthly Revenue Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
            className="bg-white rounded-lg shadow-sm border"
          >
            <div className="p-6 border-b">
              <h4 className="text-lg font-semibold text-gray-900">Revenue Trends</h4>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {metrics.revenue_by_month.map((month) => (
                  <div key={month.month} className="grid grid-cols-5 gap-4 py-3 border-b border-gray-100 last:border-b-0">
                    <div>
                      <p className="font-medium text-gray-900">{month.month}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Revenue</p>
                      <p className="font-medium text-green-600">{formatCurrency(month.revenue)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Payouts</p>
                      <p className="font-medium text-red-600">{formatCurrency(month.payouts)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Profit</p>
                      <p className="font-medium text-brown-600">{formatCurrency(month.profit)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Projects</p>
                      <p className="font-medium text-gray-900">{month.projects}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </motion.div>
  );
}
