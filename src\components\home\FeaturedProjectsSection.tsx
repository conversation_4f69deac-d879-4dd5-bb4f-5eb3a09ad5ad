"use client";

import { useRef } from "react";
import { motion } from "framer-motion";
import { Button } from "../ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

// Added layout property to determine size in mosaic grid
const projects = [
  {
    id: "1",
    title: "The Glass Pavilion",
    category: "Residential",
    location: "Cape Town",
    year: "2023",
    description: "A minimalist glass structure with panoramic ocean views.",
    image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    layout: "featured" // Full width on all screens
  },
  {
    id: "2",
    title: "Urban Heights Tower",
    category: "Commercial",
    location: "London",
    year: "2022",
    description: "Mixed-use high-rise combining office and retail spaces.",
    image: "https://images.unsplash.com/photo-1577493340887-b7bfff550145?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    layout: "normal" // Half width on medium+ screens
  },
  {
    id: "3",
    title: "The Eco Hub",
    category: "Public",
    location: "Amsterdam",
    year: "2023",
    description: "Community center built with sustainable materials.",
    image: "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    layout: "normal" // Half width on medium+ screens
  },
  {
    id: "4",
    title: "Desert Oasis Residence",
    category: "Residential",
    location: "Dubai",
    year: "2022",
    description: "Luxury residence with innovative passive cooling systems.",
    image: "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    layout: "featured" // Full width on all screens
  }
];

interface Project {
  id: string;
  title: string;
  category: string;
  location: string;
  year: string;
  description: string;
  image: string;
  layout: "featured" | "normal";
}

const ProjectCard = ({ project, index }: { project: Project; index: number }) => {
  const isFeatured = project.layout === "featured";
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      className={`group relative overflow-hidden ${
        isFeatured ? 'col-span-full' : 'col-span-full md:col-span-1'
      }`}
    >
      <Link href={`/projects/${project.id}`}>
        <div className={`relative overflow-hidden ${isFeatured ? 'h-[500px]' : 'h-[350px]'}`}>
          {/* Project Image with parallax effect */}
          <div className="absolute inset-0 w-full h-full transform transition-transform duration-700 ease-out group-hover:scale-110">
            <Image
              src={project.image}
              alt={project.title}
              fill
              sizes={isFeatured ? "100vw" : "(max-width: 768px) 100vw, 50vw"}
              className="object-cover"
              priority
            />
          </div>

          {/* Overlay with project info */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent flex flex-col justify-end p-6 md:p-8 transform transition-all duration-500 group-hover:via-black/50">
            {/* Category tag */}
            <div className="mb-auto">
              <span className="inline-block bg-primary/90 text-white text-xs tracking-wider px-3 py-1 uppercase">
                {project.category}
              </span>
            </div>
            
            <div className="transform transition-transform duration-500 group-hover:translate-y-0">
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">{project.title}</h3>
              
              <div className="flex items-center text-white/80 space-x-3 text-sm mb-3">
                <span>{project.location}</span>
                <span>•</span>
                <span>{project.year}</span>
              </div>
              
              <p className="text-white/70 text-sm mb-4 max-w-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                {project.description}
              </p>
              
              <div className="inline-flex items-center text-white text-sm border-b border-white/30 pb-1 group-hover:border-white transition-colors duration-300">
                View Project <ArrowRight className="h-4 w-4 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

const FeaturedProjectsSection = () => {
  const sectionRef = useRef(null);

  return (
    <section
      ref={sectionRef}
      className="py-20 relative overflow-hidden bg-gray-50"
    >
      <div className="container mx-auto px-4 relative z-10">
        {/* Updated section header to match ServicesSection style */}
        <div className="max-w-3xl mx-auto">
          <motion.div
            className="mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6 justify-center">
              <div className="h-[1px] w-12 bg-primary mr-4"></div>
              <span className="text-primary uppercase tracking-widest text-sm font-medium">Our work</span>
            </div>

            <motion.h2
              className="text-4xl md:text-6xl font-bold tracking-tight leading-tight text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Featured Projects
            </motion.h2>

            <motion.p
              className="text-gray-600 mt-6 text-lg text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              Explore our most notable architectural achievements.
            </motion.p>
          </motion.div>
        </div>

        {/* Mosaic Project Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          {projects.map((project, index) => (
            <ProjectCard
              key={index}
              project={{
                ...project,
                layout: project.layout as "featured" | "normal"
              }}
              index={index}
            />
          ))}
        </div>

        <div className="text-center mt-16">
          <Link href="/projects">
            <div className="inline-block group relative overflow-hidden">
              <div className="absolute inset-0 w-full h-full bg-primary transform -translate-y-full transition-transform duration-300 group-hover:translate-y-0"></div>
              <Button
                variant="default"
                size="lg"
                className="relative z-10 px-10 py-6 text-white bg-primary group-hover:bg-transparent transition-colors duration-300 inline-flex items-center"
              >
                <span className="mr-2">View All Projects</span>
                <ArrowRight className="h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          </Link>
        </div>
      </div>
    </section>
  );
};
export default FeaturedProjectsSection;
