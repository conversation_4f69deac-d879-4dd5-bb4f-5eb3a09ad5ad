-- =====================================================
-- DATABASE SCHEMA ENHANCEMENTS FOR MANAGER SYSTEM
-- Run this in Supabase SQL Editor
-- =====================================================

-- 1. DROP CONFLICTING FEE SETTINGS TABLES
-- =====================================================
DROP TABLE IF EXISTS fee_settings CASCADE;

-- 2. CREATE UNIFIED PLATFORM FEE SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS platform_fee_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  platform_commission_rate DECIMAL(5,2) NOT NULL DEFAULT 15.0,
  payment_processing_fee DECIMAL(5,2) NOT NULL DEFAULT 2.9,
  designer_payout_rate DECIMAL(5,2) NOT NULL DEFAULT 82.1,
  minimum_project_value DECIMAL(10,2) NOT NULL DEFAULT 100.0,
  maximum_commission_cap DECIMAL(10,2),
  minimum_payout_amount DECIMAL(10,2) NOT NULL DEFAULT 50.0,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_percentages CHECK (
    ABS(platform_commission_rate + payment_processing_fee + designer_payout_rate - 100.0) < 0.1
  ),
  CONSTRAINT positive_rates CHECK (
    platform_commission_rate >= 0 AND 
    payment_processing_fee >= 0 AND 
    designer_payout_rate >= 0
  )
);

-- Insert default settings with correct math (15 + 2.9 + 82.1 = 100)
INSERT INTO platform_fee_settings (
  platform_commission_rate, 
  payment_processing_fee, 
  designer_payout_rate
) VALUES (15.0, 2.9, 82.1)
ON CONFLICT DO NOTHING;

-- 3. CREATE PAYPAL ESCROW SYSTEM TABLES
-- =====================================================

-- PayPal Escrow Holds Table
CREATE TABLE IF NOT EXISTS paypal_escrow_holds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  paypal_order_id TEXT NOT NULL,
  paypal_capture_id TEXT NOT NULL UNIQUE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  milestone_id UUID REFERENCES project_milestones(id) ON DELETE SET NULL,
  client_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  designer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  gross_amount DECIMAL(10,2) NOT NULL,
  platform_fee DECIMAL(10,2) NOT NULL,
  processing_fee DECIMAL(10,2) NOT NULL,
  designer_amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'held' CHECK (status IN ('held', 'released', 'refunded')),
  hold_reason TEXT NOT NULL DEFAULT 'milestone_payment',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  released_at TIMESTAMP WITH TIME ZONE,
  release_approved_by UUID REFERENCES profiles(id),
  CONSTRAINT positive_amounts CHECK (
    gross_amount > 0 AND 
    platform_fee >= 0 AND 
    processing_fee >= 0 AND 
    designer_amount >= 0
  )
);

-- PayPal Escrow Releases Table
CREATE TABLE IF NOT EXISTS paypal_escrow_releases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  escrow_hold_id UUID REFERENCES paypal_escrow_holds(id) ON DELETE CASCADE,
  release_amount DECIMAL(10,2) NOT NULL,
  release_type TEXT NOT NULL DEFAULT 'milestone_completion' CHECK (
    release_type IN ('milestone_completion', 'project_completion', 'partial_release')
  ),
  requested_by UUID REFERENCES profiles(id) ON DELETE CASCADE,
  manager_approval_status TEXT NOT NULL DEFAULT 'pending' CHECK (
    manager_approval_status IN ('pending', 'approved', 'rejected', 'not_required')
  ),
  quality_approval_status TEXT NOT NULL DEFAULT 'not_required' CHECK (
    quality_approval_status IN ('pending', 'approved', 'rejected', 'not_required')
  ),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (
    status IN ('pending', 'approved', 'processed', 'rejected')
  ),
  notes TEXT,
  manager_approved_at TIMESTAMP WITH TIME ZONE,
  manager_notes TEXT,
  quality_approved_at TIMESTAMP WITH TIME ZONE,
  quality_approved_by UUID REFERENCES profiles(id),
  quality_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  CONSTRAINT positive_release_amount CHECK (release_amount > 0)
);

-- 4. PROJECT BRIEFS TABLE (Already exists - skipping creation)
-- =====================================================
-- Note: project_briefs table already exists with structure:
-- - assigned_designer_id (not assigned_to)
-- - assigned_by (additional field)
-- - preferred_style (additional field)
-- The existing table structure is compatible with manager workflow

-- 5. CREATE PLATFORM REVENUE TRACKING TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS platform_revenue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id TEXT NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  revenue_type TEXT NOT NULL CHECK (
    revenue_type IN ('commission', 'processing_fee', 'subscription', 'other')
  ),
  amount DECIMAL(10,2) NOT NULL,
  source TEXT NOT NULL CHECK (
    source IN ('paypal_escrow', 'stripe_escrow', 'direct_payment', 'subscription')
  ),
  status TEXT NOT NULL DEFAULT 'pending_release' CHECK (
    status IN ('pending_release', 'released', 'refunded')
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  released_at TIMESTAMP WITH TIME ZONE,
  CONSTRAINT positive_revenue CHECK (amount >= 0)
);

-- 6. CREATE DESIGNER PAYOUTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS designer_payouts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  milestone_id UUID REFERENCES project_milestones(id) ON DELETE SET NULL,
  amount DECIMAL(10,2) NOT NULL,
  payout_method TEXT NOT NULL DEFAULT 'paypal' CHECK (
    payout_method IN ('paypal', 'bank_transfer', 'stripe', 'check')
  ),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (
    status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')
  ),
  escrow_release_id UUID REFERENCES paypal_escrow_releases(id),
  transaction_id TEXT,
  payout_date TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT positive_payout_amount CHECK (amount > 0)
);

-- 7. ENHANCE EXISTING TABLES (Broken down into separate commands)
-- =====================================================

-- Add manager_status column
ALTER TABLE project_proposals_enhanced
ADD COLUMN IF NOT EXISTS manager_status TEXT;

-- Add manager_status constraint separately
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'project_proposals_enhanced_manager_status_check'
  ) THEN
    ALTER TABLE project_proposals_enhanced
    ADD CONSTRAINT project_proposals_enhanced_manager_status_check
    CHECK (manager_status IN ('pending', 'approved', 'rejected', 'escalated'));
  END IF;
END $$;

-- Add manager_notes column
ALTER TABLE project_proposals_enhanced
ADD COLUMN IF NOT EXISTS manager_notes TEXT;

-- Add manager_reviewed_at column
ALTER TABLE project_proposals_enhanced
ADD COLUMN IF NOT EXISTS manager_reviewed_at TIMESTAMP WITH TIME ZONE;

-- Add manager_reviewed_by column (without foreign key first)
ALTER TABLE project_proposals_enhanced
ADD COLUMN IF NOT EXISTS manager_reviewed_by UUID;

-- Add foreign key constraint separately
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'project_proposals_enhanced_manager_reviewed_by_fkey'
  ) THEN
    ALTER TABLE project_proposals_enhanced
    ADD CONSTRAINT project_proposals_enhanced_manager_reviewed_by_fkey
    FOREIGN KEY (manager_reviewed_by) REFERENCES profiles(id);
  END IF;
END $$;

-- Add priority column
ALTER TABLE project_proposals_enhanced
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'normal';

-- Add priority constraint separately
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'project_proposals_enhanced_priority_check'
  ) THEN
    ALTER TABLE project_proposals_enhanced
    ADD CONSTRAINT project_proposals_enhanced_priority_check
    CHECK (priority IN ('low', 'normal', 'high', 'urgent'));
  END IF;
END $$;

-- Add PayPal to payment methods in existing settings (if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'payment_settings') THEN
    ALTER TABLE payment_settings
    ALTER COLUMN payment_methods SET DEFAULT ARRAY['paypal', 'credit_card', 'bank_transfer'];
  END IF;
END $$;

-- 8. CREATE PERFORMANCE INDEXES
-- =====================================================

-- PayPal Escrow indexes
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_project_id ON paypal_escrow_holds(project_id);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_designer_id ON paypal_escrow_holds(designer_id);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_status ON paypal_escrow_holds(status);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_created_at ON paypal_escrow_holds(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_status ON paypal_escrow_releases(status);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_hold_id ON paypal_escrow_releases(escrow_hold_id);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_created_at ON paypal_escrow_releases(created_at DESC);

-- Project briefs indexes (using actual column names from existing table)
CREATE INDEX IF NOT EXISTS idx_project_briefs_status ON project_briefs(status);
CREATE INDEX IF NOT EXISTS idx_project_briefs_client_id ON project_briefs(client_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_assigned_designer_id ON project_briefs(assigned_designer_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_assigned_by ON project_briefs(assigned_by);
CREATE INDEX IF NOT EXISTS idx_project_briefs_created_at ON project_briefs(created_at DESC);

-- Platform revenue indexes
CREATE INDEX IF NOT EXISTS idx_platform_revenue_status ON platform_revenue(status);
CREATE INDEX IF NOT EXISTS idx_platform_revenue_project_id ON platform_revenue(project_id);
CREATE INDEX IF NOT EXISTS idx_platform_revenue_created_at ON platform_revenue(created_at DESC);

-- Enhanced proposal indexes (using correct table name)
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_manager_status ON project_proposals_enhanced(manager_status);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_priority ON project_proposals_enhanced(priority);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_status ON project_proposals_enhanced(status);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_designer_id ON project_proposals_enhanced(designer_id);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_brief_id ON project_proposals_enhanced(brief_id);

-- Designer payouts indexes
CREATE INDEX IF NOT EXISTS idx_designer_payouts_designer_id ON designer_payouts(designer_id);
CREATE INDEX IF NOT EXISTS idx_designer_payouts_project_id ON designer_payouts(project_id);
CREATE INDEX IF NOT EXISTS idx_designer_payouts_status ON designer_payouts(status);
CREATE INDEX IF NOT EXISTS idx_designer_payouts_created_at ON designer_payouts(created_at DESC);

-- 9. CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update designer_payout_rate when commission or processing fee changes
CREATE OR REPLACE FUNCTION update_designer_payout_rate()
RETURNS TRIGGER AS $$
BEGIN
  NEW.designer_payout_rate := 100.0 - NEW.platform_commission_rate - NEW.payment_processing_fee;
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_designer_payout_rate
  BEFORE UPDATE ON platform_fee_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_designer_payout_rate();

-- Update project_briefs updated_at timestamp
CREATE OR REPLACE FUNCTION update_project_briefs_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_project_briefs_timestamp
  BEFORE UPDATE ON project_briefs
  FOR EACH ROW
  EXECUTE FUNCTION update_project_briefs_timestamp();

-- Update designer_payouts updated_at timestamp
CREATE OR REPLACE FUNCTION update_designer_payouts_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_designer_payouts_timestamp
  BEFORE UPDATE ON designer_payouts
  FOR EACH ROW
  EXECUTE FUNCTION update_designer_payouts_timestamp();

-- 10. CREATE VIEWS FOR MANAGER OVERSIGHT
-- =====================================================

-- Manager Projects Overview (All Projects)
CREATE OR REPLACE VIEW manager_projects_overview AS
SELECT 
  p.*,
  c.full_name as client_name,
  c.email as client_email,
  d.full_name as designer_name,
  d.email as designer_email,
  d.specialization as designer_specialization,
  COUNT(pm.id) as milestone_count,
  COUNT(CASE WHEN pm.status = 'completed' THEN 1 END) as completed_milestones,
  COUNT(pp.id) as proposal_count,
  MAX(pp.created_at) as latest_proposal_date
FROM projects p
LEFT JOIN profiles c ON p.client_id = c.id
LEFT JOIN profiles d ON p.designer_id = d.id
LEFT JOIN project_milestones pm ON p.id = pm.project_id
LEFT JOIN project_proposals pp ON p.id = pp.project_id
GROUP BY p.id, c.full_name, c.email, d.full_name, d.email, d.specialization;

-- Manager Proposals Overview (All Proposals) - Using correct table
CREATE OR REPLACE VIEW manager_proposals_overview AS
SELECT
  pp.*,
  pb.title as brief_title,
  pb.status as brief_status,
  c.full_name as client_name,
  c.email as client_email,
  d.full_name as designer_name,
  d.email as designer_email,
  d.specialization as designer_specialization
FROM project_proposals_enhanced pp
LEFT JOIN project_briefs pb ON pp.brief_id = pb.id
LEFT JOIN profiles c ON pb.client_id = c.id
LEFT JOIN profiles d ON pp.designer_id = d.id;

-- 11. GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON platform_fee_settings TO authenticated;
GRANT SELECT, INSERT, UPDATE ON paypal_escrow_holds TO authenticated;
GRANT SELECT, INSERT, UPDATE ON paypal_escrow_releases TO authenticated;
GRANT SELECT, INSERT, UPDATE ON project_briefs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON platform_revenue TO authenticated;
GRANT SELECT, INSERT, UPDATE ON designer_payouts TO authenticated;
GRANT SELECT ON manager_projects_overview TO authenticated;
GRANT SELECT ON manager_proposals_overview TO authenticated;

-- Grant sequence permissions
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- SCHEMA ENHANCEMENTS COMPLETE
-- =====================================================

-- Verify the installation
SELECT 'Schema enhancements installed successfully!' as status;
