"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useParams, useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  MapPin,
  Clock,
  User,
  FileText,
  AlertCircle,
  CheckCircle,
  Send,
  Eye,
  Edit,
  Briefcase,
  Target,
  Palette
} from "lucide-react";

interface ProjectBrief {
  id: string;
  title: string;
  description: string;
  requirements: string;
  preferred_style: string;
  budget_range: string;
  timeline_preference: string;
  location: string;
  project_type: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'assigned' | 'proposal_received' | 'accepted' | 'rejected';
  assigned_designer_id: string | null;
  assigned_designer_name: string | null;
  created_at: string;
  updated_at: string;
}

interface Proposal {
  id: string;
  designer_name: string;
  designer_avatar: string | null;
  title: string;
  total_budget: number;
  timeline_weeks: number;
  status: string;
  submitted_at: string;
}

export default function BriefDetails() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const [brief, setBrief] = useState<ProjectBrief | null>(null);
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user && params.id) {
      fetchBriefDetails();
      fetchProposals();
    }
  }, [user, params.id]);

  const fetchBriefDetails = async () => {
    if (!user || !params.id) return;

    try {
      const { data: briefData, error: briefError } = await supabase
        .from('project_briefs')
        .select(`
          *,
          profiles!project_briefs_assigned_designer_id_fkey(full_name)
        `)
        .eq('id', params.id)
        .eq('client_id', user.id)
        .single();

      if (briefError) throw briefError;

      setBrief({
        ...briefData,
        assigned_designer_name: briefData.profiles?.full_name || null
      });
    } catch (error) {
      console.error('Error fetching brief details:', error);
      setError('Failed to load brief details');
    }
  };

  const fetchProposals = async () => {
    if (!params.id) return;

    try {
      const { data: proposalsData, error: proposalsError } = await supabase
        .from('project_proposals_enhanced')
        .select(`
          id,
          title,
          total_budget,
          timeline_weeks,
          status,
          submitted_at,
          profiles!project_proposals_enhanced_designer_id_fkey(full_name, avatar_url)
        `)
        .eq('brief_id', params.id)
        .order('submitted_at', { ascending: false });

      if (proposalsError) throw proposalsError;

      const mappedProposals: Proposal[] = (proposalsData || []).map(proposal => ({
        id: proposal.id,
        designer_name: proposal.profiles?.full_name || 'Unknown Designer',
        designer_avatar: proposal.profiles?.avatar_url || null,
        title: proposal.title,
        total_budget: proposal.total_budget,
        timeline_weeks: proposal.timeline_weeks,
        status: proposal.status,
        submitted_at: proposal.submitted_at
      }));

      setProposals(mappedProposals);
    } catch (error) {
      console.error('Error fetching proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      case 'proposal_received':
        return 'text-brown-600 bg-brown-50 border-brown-200';
      case 'assigned':
        return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'pending':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBudgetDisplay = (budgetRange: string) => {
    return budgetRange.replace(/_/g, ' - $').replace('k', 'K');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (error || !brief) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <p className="text-red-700">{error || 'Brief not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/client/briefs">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Briefs
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{brief.title}</h1>
            <p className="text-gray-600">Brief Details</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Link href={`/client/briefs/${brief.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit Brief
            </Button>
          </Link>
          {proposals.length > 0 && (
            <Link href={`/client/briefs/${brief.id}/proposals`}>
              <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                <FileText className="h-4 w-4 mr-2" />
                Review Proposals ({proposals.length})
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Status and Urgency */}
      <div className="flex items-center space-x-4">
        <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getStatusColor(brief.status)}`}>
          {brief.status.replace('_', ' ').toUpperCase()}
        </span>
        <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getUrgencyColor(brief.urgency)}`}>
          {brief.urgency.toUpperCase()} PRIORITY
        </span>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Brief Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Description</h3>
            <p className="text-gray-700 leading-relaxed">{brief.description}</p>
          </motion.div>

          {/* Requirements */}
          {brief.requirements && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Specific Requirements</h3>
              <p className="text-gray-700 leading-relaxed">{brief.requirements}</p>
            </motion.div>
          )}

          {/* Proposals Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Proposals Received</h3>
              {proposals.length > 0 && (
                <Link href={`/client/briefs/${brief.id}/proposals`}>
                  <Button size="sm" className="bg-brown-600 hover:bg-brown-700 text-white">
                    View All ({proposals.length})
                  </Button>
                </Link>
              )}
            </div>
            
            {proposals.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No proposals received yet</p>
                <p className="text-sm text-gray-400 mt-1">Designers will submit proposals for your review</p>
              </div>
            ) : (
              <div className="space-y-3">
                {proposals.slice(0, 3).map((proposal) => (
                  <div key={proposal.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {proposal.designer_avatar ? (
                          <img
                            src={proposal.designer_avatar}
                            alt={proposal.designer_name}
                            className="h-8 w-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                            <User className="h-4 w-4 text-gray-500" />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{proposal.designer_name}</p>
                        <p className="text-sm text-gray-500">{proposal.title}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">${proposal.total_budget.toLocaleString()}</p>
                      <p className="text-sm text-gray-500">{proposal.timeline_weeks} weeks</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        </div>

        {/* Right Column - Brief Info */}
        <div className="space-y-6">
          {/* Brief Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Brief Information</h3>
            <div className="space-y-4">
              <div className="flex items-center text-sm">
                <DollarSign className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Budget:</span>
                <span className="ml-2 font-medium">{getBudgetDisplay(brief.budget_range)}</span>
              </div>
              <div className="flex items-center text-sm">
                <Clock className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Timeline:</span>
                <span className="ml-2 font-medium">{brief.timeline_preference.replace(/_/g, ' ')}</span>
              </div>
              <div className="flex items-center text-sm">
                <MapPin className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Location:</span>
                <span className="ml-2 font-medium">{brief.location}</span>
              </div>
              <div className="flex items-center text-sm">
                <Target className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Type:</span>
                <span className="ml-2 font-medium">{brief.project_type.replace(/_/g, ' ')}</span>
              </div>
              {brief.preferred_style && (
                <div className="flex items-center text-sm">
                  <Palette className="h-4 w-4 text-gray-400 mr-3" />
                  <span className="text-gray-600">Style:</span>
                  <span className="ml-2 font-medium">{brief.preferred_style}</span>
                </div>
              )}
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Created:</span>
                <span className="ml-2 font-medium">{formatDate(brief.created_at)}</span>
              </div>
            </div>
          </motion.div>

          {/* Assigned Designer */}
          {brief.assigned_designer_name && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Assigned Designer</h3>
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                  <User className="h-5 w-5 text-gray-500" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{brief.assigned_designer_name}</p>
                  <p className="text-sm text-gray-500">Assigned Designer</p>
                </div>
              </div>
              <div className="mt-4">
                <Link href={`/client/messages?otherUserId=${brief.assigned_designer_id}&type=direct`}>
                  <Button size="sm" variant="outline" className="w-full">
                    <Send className="h-4 w-4 mr-2" />
                    Message Designer
                  </Button>
                </Link>
              </div>
            </motion.div>
          )}

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link href={`/client/briefs/${brief.id}/edit`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Edit className="h-4 w-4 mr-3" />
                  Edit Brief
                </Button>
              </Link>
              {proposals.length > 0 && (
                <Link href={`/client/briefs/${brief.id}/proposals`}>
                  <Button size="sm" className="w-full justify-start bg-brown-600 hover:bg-brown-700 text-white">
                    <Eye className="h-4 w-4 mr-3" />
                    Review Proposals
                  </Button>
                </Link>
              )}
              <Link href="/client/briefs/new">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Briefcase className="h-4 w-4 mr-3" />
                  Submit New Brief
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
