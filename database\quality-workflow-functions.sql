-- =====================================================
-- QUALITY WORKFLOW DATABASE FUNCTIONS
-- Functions to support automated quality review workflow
-- Note: Run this as a database administrator or with proper permissions
-- =====================================================

-- Check if we have permission to create functions
DO $$
BEGIN
    -- Test if we can create a simple function
    CREATE OR REPLACE FUNCTION test_permissions() RETURNS BOOLEAN AS $test$
    BEGIN
        RETURN TRUE;
    END;
    $test$ LANGUAGE plpgsql SECURITY DEFINER;

    -- If we get here, we have permissions
    DROP FUNCTION test_permissions();
    RAISE NOTICE '✅ Permissions check passed - proceeding with function creation';
EXCEPTION
    WHEN insufficient_privilege THEN
        RAISE EXCEPTION 'ERROR: Insufficient privileges to create functions. Please run this script as a database administrator or contact your Supabase admin to grant CREATE privileges on the public schema.';
    WHEN others THEN
        RAISE NOTICE 'Permission check failed: %', SQLERRM;
        RAISE EXCEPTION 'Cannot proceed with function creation due to permission issues.';
END $$;

-- Function to increment reviewer workload
CREATE OR REPLACE FUNCTION increment_reviewer_workload(
  reviewer_id UUID,
  increment_by INTEGER DEFAULT 1
)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles 
  SET quality_current_workload = COALESCE(quality_current_workload, 0) + increment_by
  WHERE id = reviewer_id AND role = 'quality_team';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement reviewer workload
CREATE OR REPLACE FUNCTION decrement_reviewer_workload(
  reviewer_id UUID,
  decrement_by INTEGER DEFAULT 1
)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles 
  SET quality_current_workload = GREATEST(0, COALESCE(quality_current_workload, 0) - decrement_by)
  WHERE id = reviewer_id AND role = 'quality_team';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to auto-assign quality review
CREATE OR REPLACE FUNCTION auto_assign_quality_review(
  review_id UUID,
  review_type TEXT DEFAULT 'milestone',
  project_complexity TEXT DEFAULT 'medium',
  urgency TEXT DEFAULT 'medium'
)
RETURNS UUID AS $$
DECLARE
  selected_reviewer_id UUID;
  reviewer_record RECORD;
  best_score NUMERIC := 0;
  current_score NUMERIC;
BEGIN
  -- Find the best available reviewer
  FOR reviewer_record IN 
    SELECT 
      id,
      quality_current_workload,
      quality_max_workload,
      quality_specializations,
      quality_last_assignment
    FROM profiles 
    WHERE role = 'quality_team' 
      AND quality_is_available = true 
      AND COALESCE(quality_current_workload, 0) < COALESCE(quality_max_workload, 5)
  LOOP
    -- Calculate score for this reviewer
    current_score := 0;
    
    -- Workload factor (prefer less loaded reviewers)
    current_score := current_score + (1.0 - (COALESCE(reviewer_record.quality_current_workload, 0)::NUMERIC / COALESCE(reviewer_record.quality_max_workload, 5)::NUMERIC)) * 40;
    
    -- Time since last assignment (prefer balanced distribution)
    current_score := current_score + LEAST(
      EXTRACT(EPOCH FROM (NOW() - COALESCE(reviewer_record.quality_last_assignment, NOW() - INTERVAL '30 days'))) / 86400 * 2, 
      20
    );
    
    -- Random factor
    current_score := current_score + RANDOM() * 10;
    
    -- Check if this is the best reviewer so far
    IF current_score > best_score THEN
      best_score := current_score;
      selected_reviewer_id := reviewer_record.id;
    END IF;
  END LOOP;
  
  -- If we found a reviewer, assign the review
  IF selected_reviewer_id IS NOT NULL THEN
    UPDATE quality_reviews_new 
    SET 
      reviewer_id = selected_reviewer_id,
      status = 'assigned',
      assigned_at = NOW()
    WHERE id = review_id;
    
    -- Update reviewer workload and last assignment
    UPDATE profiles 
    SET 
      quality_current_workload = COALESCE(quality_current_workload, 0) + 1,
      quality_last_assignment = NOW()
    WHERE id = selected_reviewer_id;
  END IF;
  
  RETURN selected_reviewer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete quality review
CREATE OR REPLACE FUNCTION complete_quality_review(
  review_id UUID,
  reviewer_id UUID,
  overall_score INTEGER,
  feedback_text TEXT,
  review_status TEXT DEFAULT 'approved'
)
RETURNS BOOLEAN AS $$
DECLARE
  review_record RECORD;
BEGIN
  -- Get the review record
  SELECT * INTO review_record FROM quality_reviews_new WHERE id = review_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Update the review
  UPDATE quality_reviews_new 
  SET 
    status = review_status,
    overall_score = overall_score,
    feedback = feedback_text,
    reviewed_at = NOW()
  WHERE id = review_id AND reviewer_id = reviewer_id;
  
  -- Decrement reviewer workload
  PERFORM decrement_reviewer_workload(reviewer_id, 1);
  
  -- If revision needed, increment revision count
  IF review_status = 'needs_revision' THEN
    UPDATE quality_reviews_new 
    SET revision_count = revision_count + 1
    WHERE id = review_id;
  END IF;
  
  -- Update submission status if applicable
  IF review_record.submission_id IS NOT NULL THEN
    UPDATE project_submissions 
    SET status = CASE 
      WHEN review_status = 'approved' THEN 'approved'
      WHEN review_status = 'needs_revision' THEN 'needs_revision'
      ELSE 'rejected'
    END
    WHERE id = review_record.submission_id;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to escalate overdue reviews
CREATE OR REPLACE FUNCTION escalate_overdue_reviews()
RETURNS INTEGER AS $$
DECLARE
  escalated_count INTEGER := 0;
  review_record RECORD;
BEGIN
  -- Find reviews that are overdue by more than 24 hours
  FOR review_record IN 
    SELECT id, project_id, designer_id, reviewer_id
    FROM quality_reviews_new 
    WHERE status IN ('pending', 'assigned', 'in_review')
      AND sla_deadline < NOW() - INTERVAL '24 hours'
      AND escalated_at IS NULL
  LOOP
    -- Mark as escalated
    UPDATE quality_reviews_new 
    SET 
      status = 'escalated',
      escalated_at = NOW(),
      priority = 'urgent'
    WHERE id = review_record.id;
    
    -- Create escalation record
    INSERT INTO quality_escalations (
      review_id,
      escalation_reason,
      escalated_at,
      escalation_type
    ) VALUES (
      review_record.id,
      'sla_violation',
      NOW(),
      'automatic'
    );
    
    -- Notify admins
    INSERT INTO workflow_notifications (
      recipient_id,
      notification_type,
      title,
      message,
      priority,
      metadata
    )
    SELECT 
      p.id,
      'quality_escalation',
      'Critical: Review Severely Overdue',
      'Quality review ' || review_record.id || ' requires immediate admin attention',
      'urgent',
      jsonb_build_object(
        'review_id', review_record.id,
        'project_id', review_record.project_id,
        'escalation_reason', 'sla_violation'
      )
    FROM profiles p 
    WHERE p.role = 'admin';
    
    escalated_count := escalated_count + 1;
  END LOOP;
  
  RETURN escalated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get quality team workload stats
CREATE OR REPLACE FUNCTION get_quality_team_workload()
RETURNS TABLE(
  reviewer_id UUID,
  full_name TEXT,
  current_workload INTEGER,
  max_workload INTEGER,
  workload_percentage NUMERIC,
  is_available BOOLEAN,
  specializations TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.full_name,
    COALESCE(p.quality_current_workload, 0),
    COALESCE(p.quality_max_workload, 5),
    ROUND((COALESCE(p.quality_current_workload, 0)::NUMERIC / COALESCE(p.quality_max_workload, 5)::NUMERIC) * 100, 1),
    COALESCE(p.quality_is_available, false),
    COALESCE(p.quality_specializations, ARRAY[]::TEXT[])
  FROM profiles p
  WHERE p.role = 'quality_team'
  ORDER BY p.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get SLA compliance stats
CREATE OR REPLACE FUNCTION get_sla_compliance_stats(
  start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
  end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
  total_reviews INTEGER,
  on_time_reviews INTEGER,
  overdue_reviews INTEGER,
  average_completion_hours NUMERIC,
  sla_compliance_percentage NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_reviews,
    COUNT(CASE WHEN reviewed_at <= sla_deadline THEN 1 END)::INTEGER as on_time_reviews,
    COUNT(CASE WHEN reviewed_at > sla_deadline OR (reviewed_at IS NULL AND sla_deadline < NOW()) THEN 1 END)::INTEGER as overdue_reviews,
    ROUND(AVG(EXTRACT(EPOCH FROM (COALESCE(reviewed_at, NOW()) - created_at)) / 3600), 1) as average_completion_hours,
    ROUND(
      (COUNT(CASE WHEN reviewed_at <= sla_deadline THEN 1 END)::NUMERIC / NULLIF(COUNT(*), 0)) * 100, 
      1
    ) as sla_compliance_percentage
  FROM quality_reviews_new
  WHERE created_at::DATE BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function to auto-assign reviews when created
CREATE OR REPLACE FUNCTION trigger_auto_assign_review()
RETURNS TRIGGER AS $$
BEGIN
  -- Only auto-assign if no reviewer is already assigned
  IF NEW.reviewer_id IS NULL AND NEW.status = 'pending' THEN
    PERFORM auto_assign_quality_review(
      NEW.id,
      NEW.review_type,
      'medium', -- Default complexity
      NEW.priority
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for auto-assignment
DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review ON quality_reviews_new;
CREATE TRIGGER trigger_auto_assign_quality_review
  AFTER INSERT ON quality_reviews_new
  FOR EACH ROW
  EXECUTE FUNCTION trigger_auto_assign_review();

-- Function to sync submission status with quality review
CREATE OR REPLACE FUNCTION sync_submission_quality_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Update submission status based on quality review status
  IF NEW.submission_id IS NOT NULL THEN
    UPDATE project_submissions 
    SET status = CASE 
      WHEN NEW.status = 'approved' THEN 'approved'
      WHEN NEW.status = 'needs_revision' THEN 'needs_revision'
      WHEN NEW.status = 'rejected' THEN 'rejected'
      WHEN NEW.status IN ('pending', 'assigned', 'in_review') THEN 'under_quality_review'
      ELSE status
    END
    WHERE id = NEW.submission_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for submission status sync
DROP TRIGGER IF EXISTS trigger_sync_submission_quality_status ON quality_reviews_new;
CREATE TRIGGER trigger_sync_submission_quality_status
  AFTER UPDATE ON quality_reviews_new
  FOR EACH ROW
  WHEN (OLD.status IS DISTINCT FROM NEW.status)
  EXECUTE FUNCTION sync_submission_quality_status();

-- =====================================================
-- ESCROW SYSTEM DATABASE FUNCTIONS
-- Functions to support escrow hold and release operations
-- =====================================================

-- Function to get escrow account summary
CREATE OR REPLACE FUNCTION get_escrow_account_summary(
  account_id UUID
)
RETURNS TABLE(
  account_id UUID,
  project_title TEXT,
  client_name TEXT,
  designer_name TEXT,
  manager_name TEXT,
  total_held DECIMAL(10,2),
  total_released DECIMAL(10,2),
  active_holds_count INTEGER,
  pending_releases_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ea.id,
    p.title,
    c.full_name,
    d.full_name,
    m.full_name,
    ea.total_held,
    ea.total_released,
    (SELECT COUNT(*)::INTEGER FROM escrow_holds WHERE escrow_account_id = ea.id AND status = 'active'),
    (SELECT COUNT(*)::INTEGER FROM escrow_releases WHERE escrow_account_id = ea.id AND status = 'pending')
  FROM escrow_accounts ea
  JOIN projects p ON ea.project_id = p.id
  JOIN profiles c ON ea.client_id = c.id
  JOIN profiles d ON ea.designer_id = d.id
  LEFT JOIN profiles m ON ea.manager_id = m.id
  WHERE ea.id = account_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if escrow release can be processed
CREATE OR REPLACE FUNCTION can_process_escrow_release(
  release_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  release_record RECORD;
BEGIN
  SELECT
    manager_approval_status,
    quality_approval_status,
    status
  INTO release_record
  FROM escrow_releases
  WHERE id = release_id;

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Check if all required approvals are complete
  RETURN (
    release_record.status = 'pending' AND
    (release_record.manager_approval_status = 'approved' OR release_record.manager_approval_status = 'not_required') AND
    (release_record.quality_approval_status = 'approved' OR release_record.quality_approval_status = 'not_required')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to auto-release overdue escrow holds
CREATE OR REPLACE FUNCTION auto_release_overdue_escrow()
RETURNS INTEGER AS $$
DECLARE
  released_count INTEGER := 0;
  hold_record RECORD;
BEGIN
  -- Find holds that are past their auto-release date
  FOR hold_record IN
    SELECT id, escrow_account_id, project_id, net_amount
    FROM escrow_holds
    WHERE status = 'active'
      AND auto_release_date < NOW()
      AND requires_manager_approval = false
  LOOP
    -- Create automatic release
    INSERT INTO escrow_releases (
      escrow_hold_id,
      escrow_account_id,
      project_id,
      release_amount,
      release_type,
      requested_by,
      manager_approval_status,
      quality_approval_status,
      status
    ) VALUES (
      hold_record.id,
      hold_record.escrow_account_id,
      hold_record.project_id,
      hold_record.net_amount,
      'milestone_completion',
      'system',
      'not_required',
      'not_required',
      'approved'
    );

    -- Update hold status
    UPDATE escrow_holds
    SET
      status = 'pending_release',
      release_requested_at = NOW()
    WHERE id = hold_record.id;

    released_count := released_count + 1;
  END LOOP;

  RETURN released_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get manager escrow dashboard data
CREATE OR REPLACE FUNCTION get_manager_escrow_dashboard(
  manager_id UUID
)
RETURNS TABLE(
  total_projects INTEGER,
  total_held_amount DECIMAL(10,2),
  pending_approvals INTEGER,
  processed_releases INTEGER,
  disputed_holds INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(DISTINCT ea.project_id)::INTEGER,
    COALESCE(SUM(ea.total_held), 0),
    COUNT(CASE WHEN er.manager_approval_status = 'pending' THEN 1 END)::INTEGER,
    COUNT(CASE WHEN er.status = 'processed' THEN 1 END)::INTEGER,
    COUNT(CASE WHEN eh.status = 'disputed' THEN 1 END)::INTEGER
  FROM escrow_accounts ea
  LEFT JOIN escrow_releases er ON ea.id = er.escrow_account_id
  LEFT JOIN escrow_holds eh ON ea.id = eh.escrow_account_id
  WHERE ea.manager_id = manager_id OR er.manager_id = manager_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION increment_reviewer_workload TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_reviewer_workload TO authenticated;
GRANT EXECUTE ON FUNCTION auto_assign_quality_review TO authenticated;
GRANT EXECUTE ON FUNCTION complete_quality_review TO authenticated;
GRANT EXECUTE ON FUNCTION escalate_overdue_reviews TO authenticated;
GRANT EXECUTE ON FUNCTION get_quality_team_workload TO authenticated;
GRANT EXECUTE ON FUNCTION get_sla_compliance_stats TO authenticated;

-- Grant escrow function permissions
GRANT EXECUTE ON FUNCTION get_escrow_account_summary TO authenticated;
GRANT EXECUTE ON FUNCTION can_process_escrow_release TO authenticated;
GRANT EXECUTE ON FUNCTION auto_release_overdue_escrow TO authenticated;
GRANT EXECUTE ON FUNCTION get_manager_escrow_dashboard TO authenticated;
