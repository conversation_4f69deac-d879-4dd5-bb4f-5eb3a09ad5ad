"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Star,
  MessageSquare,
  Send,
  Plus,
  Eye,
  Calendar,
  TrendingUp,
  ThumbsUp,
  ThumbsDown,
  BarChart3,
  Users,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Target
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  client: {
    id: string;
    full_name: string;
    email: string;
  };
  designer: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface ProjectFeedback {
  id: string;
  rating: number;
  communication_rating: number;
  quality_rating: number;
  timeline_rating: number;
  overall_satisfaction: number;
  feedback_text: string;
  would_recommend: boolean;
  created_at: string;
  feedback_type: string;
  client: {
    full_name: string;
  };
  designer: {
    full_name: string;
  };
}

interface SatisfactionSurvey {
  id: string;
  survey_type: string;
  status: string;
  sent_at: string;
  completed_at: string | null;
  reminder_count: number;
  client: {
    full_name: string;
    email: string;
  };
}

export default function ProjectSatisfactionPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [feedback, setFeedback] = useState<ProjectFeedback[]>([]);
  const [surveys, setSurveys] = useState<SatisfactionSurvey[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'feedback' | 'surveys'>('overview');

  useEffect(() => {
    if (user && profile?.role === 'manager' && projectId) {
      fetchProjectSatisfactionData();
    }
  }, [user, profile, projectId]);

  const fetchProjectSatisfactionData = async () => {
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id, title,
          client:profiles!projects_client_id_fkey(id, full_name, email),
          designer:profiles!projects_designer_id_fkey(id, full_name, email)
        `)
        .eq('id', projectId)
        .eq('manager_id', user?.id)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // Fetch project-specific feedback
      const { data: feedbackData, error: feedbackError } = await supabase
        .from('client_feedback')
        .select(`
          *,
          client:profiles!client_feedback_client_id_fkey(full_name),
          designer:profiles!client_feedback_designer_id_fkey(full_name)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (feedbackError) throw feedbackError;
      setFeedback(feedbackData || []);

      // Fetch project satisfaction surveys
      const { data: surveysData, error: surveysError } = await supabase
        .from('satisfaction_surveys')
        .select(`
          *,
          client:profiles!satisfaction_surveys_client_id_fkey(full_name, email)
        `)
        .eq('project_id', projectId)
        .order('sent_at', { ascending: false });

      if (surveysError) throw surveysError;
      setSurveys(surveysData || []);

    } catch (error) {
      console.error('Error fetching project satisfaction data:', error);
      router.push('/manager/projects');
    } finally {
      setLoading(false);
    }
  };

  const sendSatisfactionSurvey = async () => {
    if (!project) return;

    try {
      const { data, error } = await supabase
        .from('satisfaction_surveys')
        .insert({
          project_id: projectId,
          client_id: project.client.id,
          manager_id: user?.id,
          survey_type: 'project_completion',
          status: 'sent',
          sent_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Send notification
      await supabase.from('notifications').insert({
        user_id: project.client.id,
        type: 'satisfaction_survey',
        title: 'Project Satisfaction Survey',
        message: `Please provide feedback for your project: ${project.title}`,
        data: { project_id: projectId, survey_id: data.id }
      });

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: projectId,
        activity_type: 'satisfaction_survey',
        description: 'Sent project satisfaction survey',
        outcome: 'survey_sent'
      });

      fetchProjectSatisfactionData();
      alert('Satisfaction survey sent successfully!');
    } catch (error) {
      console.error('Error sending survey:', error);
      alert('Error sending survey. Please try again.');
    }
  };

  const getAverageRating = (type: 'overall' | 'communication' | 'quality' | 'timeline') => {
    if (feedback.length === 0) return 0;
    
    const field = type === 'overall' ? 'overall_satisfaction' : `${type}_rating`;
    const total = feedback.reduce((sum, f) => sum + (f[field as keyof ProjectFeedback] as number), 0);
    return (total / feedback.length).toFixed(1);
  };

  const getRecommendationRate = () => {
    if (feedback.length === 0) return 0;
    const recommendations = feedback.filter(f => f.would_recommend).length;
    return Math.round((recommendations / feedback.length) * 100);
  };

  const getRatingBadge = (rating: number) => {
    if (rating >= 4.5) return "bg-green-100 text-green-800 border border-green-200";
    if (rating >= 3.5) return "bg-blue-100 text-blue-800 border border-blue-200";
    if (rating >= 2.5) return "bg-yellow-100 text-yellow-800 border border-yellow-200";
    return "bg-red-100 text-red-800 border border-red-200";
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'sent':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'expired':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Project Not Found</h2>
          <p className="text-gray-600 mb-4">The project could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <Star className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Project Satisfaction</h1>
            <p className="text-gray-600 mt-1">{project.title}</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Satisfaction Management</h2>
            <p className="text-gray-600 mt-1">Monitor and improve client satisfaction for this project</p>
          </div>
          <Button
            onClick={sendSatisfactionSurvey}
            className="flex items-center gap-2"
          >
            <Send className="h-4 w-4" />
            Send Survey
          </Button>
        </div>
      </div>

      {/* Satisfaction Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Rating</p>
              <div className="flex items-center gap-2 mt-1">
                <p className={`text-2xl font-bold px-3 py-1 rounded-full ${getRatingBadge(Number(getAverageRating('overall')))}`}>
                  {getAverageRating('overall')}
                </p>
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-4 w-4 ${
                        star <= Number(getAverageRating('overall'))
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
            <Star className="h-8 w-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Communication</p>
              <p className={`text-2xl font-bold px-3 py-1 rounded-full ${getRatingBadge(Number(getAverageRating('communication')))}`}>
                {getAverageRating('communication')}
              </p>
            </div>
            <MessageSquare className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Quality</p>
              <p className={`text-2xl font-bold px-3 py-1 rounded-full ${getRatingBadge(Number(getAverageRating('quality')))}`}>
                {getAverageRating('quality')}
              </p>
            </div>
            <Target className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Would Recommend</p>
              <p className="text-2xl font-bold text-purple-600">{getRecommendationRate()}%</p>
            </div>
            <ThumbsUp className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'feedback', label: 'Feedback', icon: MessageSquare },
              { id: 'surveys', label: 'Surveys', icon: Send }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-brown-500 text-brown-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Satisfaction Breakdown</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-700">Overall Satisfaction</span>
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-gray-900">{getAverageRating('overall')}</span>
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-4 w-4 ${
                                star <= Number(getAverageRating('overall'))
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-700">Communication Rating</span>
                      <span className="font-bold text-gray-900">{getAverageRating('communication')}/5</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-700">Quality Rating</span>
                      <span className="font-bold text-gray-900">{getAverageRating('quality')}/5</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-700">Timeline Rating</span>
                      <span className="font-bold text-gray-900">{getAverageRating('timeline')}/5</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Team</h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg">
                      <Users className="h-8 w-8 text-blue-600" />
                      <div>
                        <h4 className="font-semibold text-blue-900">Client</h4>
                        <p className="text-blue-800">{project.client.full_name}</p>
                        <p className="text-sm text-blue-600">{project.client.email}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg">
                      <Users className="h-8 w-8 text-green-600" />
                      <div>
                        <h4 className="font-semibold text-green-900">Designer</h4>
                        <p className="text-green-800">{project.designer.full_name}</p>
                        <p className="text-sm text-green-600">{project.designer.email}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {feedback.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Feedback Summary</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700">
                      Based on {feedback.length} feedback submission{feedback.length !== 1 ? 's' : ''},
                      this project has an average satisfaction rating of {getAverageRating('overall')} out of 5 stars.
                      {getRecommendationRate() > 0 && ` ${getRecommendationRate()}% of clients would recommend our services.`}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'feedback' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Client Feedback</h3>

              {feedback.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No feedback received yet</p>
                  <Button
                    onClick={sendSatisfactionSurvey}
                    className="mt-4 flex items-center gap-2"
                  >
                    <Send className="h-4 w-4" />
                    Send Satisfaction Survey
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {feedback.map((item) => (
                    <div key={item.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-gray-900">{item.client.full_name}</h4>
                            <div className="flex items-center gap-1">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <Star
                                  key={star}
                                  className={`h-4 w-4 ${
                                    star <= item.overall_satisfaction
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                              <span className="ml-2 text-sm font-medium text-gray-700">
                                {item.overall_satisfaction}/5
                              </span>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600">
                            {new Date(item.created_at).toLocaleDateString()}
                          </p>
                        </div>

                        <div className="flex items-center gap-2">
                          {item.would_recommend ? (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-green-100 text-green-800">
                              <ThumbsUp className="h-3 w-3 mr-1" />
                              Recommends
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-red-100 text-red-800">
                              <ThumbsDown className="h-3 w-3 mr-1" />
                              Won't Recommend
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm font-medium text-blue-700">Communication</p>
                          <p className="text-lg font-bold text-blue-900">{item.communication_rating}/5</p>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <p className="text-sm font-medium text-green-700">Quality</p>
                          <p className="text-lg font-bold text-green-900">{item.quality_rating}/5</p>
                        </div>
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <p className="text-sm font-medium text-purple-700">Timeline</p>
                          <p className="text-lg font-bold text-purple-900">{item.timeline_rating}/5</p>
                        </div>
                      </div>

                      {item.feedback_text && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <p className="text-gray-700">{item.feedback_text}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'surveys' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Satisfaction Surveys</h3>
                <Button
                  onClick={sendSatisfactionSurvey}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Send New Survey
                </Button>
              </div>

              {surveys.length === 0 ? (
                <div className="text-center py-8">
                  <Send className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No surveys sent yet</p>
                  <Button
                    onClick={sendSatisfactionSurvey}
                    className="mt-4 flex items-center gap-2"
                  >
                    <Send className="h-4 w-4" />
                    Send First Survey
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {surveys.map((survey) => (
                    <div key={survey.id} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h4 className="font-semibold text-gray-900">{survey.client.full_name}</h4>
                          <p className="text-sm text-gray-600">{survey.client.email}</p>
                          <p className="text-sm text-gray-500 mt-1">
                            Survey Type: {survey.survey_type.replace('_', ' ').toUpperCase()}
                          </p>
                        </div>

                        <span className={getStatusBadge(survey.status)}>
                          {survey.status.toUpperCase()}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>Sent: {new Date(survey.sent_at).toLocaleDateString()}</span>
                        </div>

                        {survey.completed_at && (
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>Completed: {new Date(survey.completed_at).toLocaleDateString()}</span>
                          </div>
                        )}

                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" />
                          <span>Reminders: {survey.reminder_count}</span>
                        </div>
                      </div>

                      {survey.status === 'sent' && (
                        <div className="mt-4 flex gap-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Send reminder logic
                              alert('Reminder sent!');
                            }}
                            className="flex items-center gap-2"
                          >
                            <Send className="h-4 w-4" />
                            Send Reminder
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/manager/satisfaction/survey/${survey.id}`)}
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Improvement Tips */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <TrendingUp className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Satisfaction Improvement Tips</h3>
            <div className="text-blue-800 space-y-2">
              <p>• <strong>Regular Check-ins:</strong> Schedule periodic satisfaction surveys throughout the project</p>
              <p>• <strong>Quick Response:</strong> Address any concerns or feedback promptly</p>
              <p>• <strong>Clear Communication:</strong> Keep clients informed about project progress and timelines</p>
              <p>• <strong>Quality Focus:</strong> Ensure deliverables meet or exceed client expectations</p>
              <p>• <strong>Follow-up:</strong> Contact clients after project completion to ensure satisfaction</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
