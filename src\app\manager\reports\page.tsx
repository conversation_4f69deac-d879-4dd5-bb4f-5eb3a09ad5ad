"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Download,
  Calendar,
  RefreshCw,
  Target,
  Star,
  FileText
} from "lucide-react";

interface ReportData {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalBudget: number;
  averageProjectValue: number;
  clientSatisfaction: number;
  onTimeDelivery: number;
  qualityScore: number;
  monthlyStats: Array<{
    month: string;
    projects: number;
    revenue: number;
    satisfaction: number;
  }>;
  topClients: Array<{
    name: string;
    projects: number;
    totalValue: number;
  }>;
  topDesigners: Array<{
    name: string;
    projects: number;
    avgRating: number;
  }>;
}

export default function ManagerReportsPage() {
  const { user, profile } = useOptimizedAuth();
  const [reportData, setReportData] = useState<ReportData>({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalBudget: 0,
    averageProjectValue: 0,
    clientSatisfaction: 0,
    onTimeDelivery: 0,
    qualityScore: 0,
    monthlyStats: [],
    topClients: [],
    topDesigners: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<string>('30');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchReportData();
    }
  }, [user, profile, timeRange]);

  const fetchReportData = async () => {
    try {
      // Fetch project assignments for this manager
      const { data: assignments, error } = await supabase
        .from('project_assignments')
        .select(`
          *,
          project:projects(
            title, status, budget, created_at,
            client:profiles!projects_client_id_fkey(full_name),
            designer:profiles!projects_designer_id_fkey(full_name)
          )
        `)
        .eq('manager_id', user?.id);

      if (error) throw error;

      const projects = assignments?.map(a => a.project) || [];
      
      // Calculate basic stats
      const totalProjects = projects.length;
      const activeProjects = projects.filter(p => p.status === 'in_progress').length;
      const completedProjects = projects.filter(p => p.status === 'completed').length;
      const totalBudget = projects.reduce((sum, p) => sum + (p.budget || 0), 0);
      const averageProjectValue = totalProjects > 0 ? totalBudget / totalProjects : 0;

      // Mock data for other metrics (in real app, these would come from actual data)
      const clientSatisfaction = 4.2;
      const onTimeDelivery = 87;
      const qualityScore = 4.1;

      // Monthly stats (mock data)
      const monthlyStats = [
        { month: 'Jan', projects: 8, revenue: 45000, satisfaction: 4.1 },
        { month: 'Feb', projects: 12, revenue: 67000, satisfaction: 4.3 },
        { month: 'Mar', projects: 10, revenue: 58000, satisfaction: 4.0 },
        { month: 'Apr', projects: 15, revenue: 82000, satisfaction: 4.4 },
        { month: 'May', projects: 13, revenue: 71000, satisfaction: 4.2 },
        { month: 'Jun', projects: 18, revenue: 95000, satisfaction: 4.5 }
      ];

      // Top clients (mock data)
      const topClients = [
        { name: 'Tech Corp Inc.', projects: 8, totalValue: 125000 },
        { name: 'Design Studio LLC', projects: 6, totalValue: 89000 },
        { name: 'Marketing Agency', projects: 5, totalValue: 67000 },
        { name: 'Startup Ventures', projects: 4, totalValue: 45000 }
      ];

      // Top designers (mock data)
      const topDesigners = [
        { name: 'Sarah Johnson', projects: 12, avgRating: 4.8 },
        { name: 'Mike Davis', projects: 10, avgRating: 4.6 },
        { name: 'Emily Brown', projects: 8, avgRating: 4.7 },
        { name: 'John Smith', projects: 7, avgRating: 4.5 }
      ];

      setReportData({
        totalProjects,
        activeProjects,
        completedProjects,
        totalBudget,
        averageProjectValue,
        clientSatisfaction,
        onTimeDelivery,
        qualityScore,
        monthlyStats,
        topClients,
        topDesigners
      });
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = () => {
    // In a real app, this would generate and download a PDF/Excel report
    alert('Report export functionality would be implemented here');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600 mt-2">Comprehensive project and performance insights</p>
        </div>
        <div className="flex gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
            <option value="365">Last year</option>
          </select>
          <Button
            variant="outline"
            onClick={exportReport}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Report
          </Button>
          <Button
            onClick={fetchReportData}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-blue-600">{reportData.totalProjects}</p>
              <p className="text-xs text-gray-500 mt-1">
                {reportData.activeProjects} active, {reportData.completedProjects} completed
              </p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-green-600">${reportData.totalBudget.toLocaleString()}</p>
              <p className="text-xs text-gray-500 mt-1">
                Avg: ${Math.round(reportData.averageProjectValue).toLocaleString()}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Client Satisfaction</p>
              <p className="text-2xl font-bold text-purple-600">{reportData.clientSatisfaction.toFixed(1)}/5</p>
              <p className="text-xs text-gray-500 mt-1">
                {reportData.onTimeDelivery}% on-time delivery
              </p>
            </div>
            <Star className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Quality Score</p>
              <p className="text-2xl font-bold text-orange-600">{reportData.qualityScore.toFixed(1)}/5</p>
              <p className="text-xs text-gray-500 mt-1">
                Average quality rating
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Monthly Performance */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Performance</h3>
          <div className="space-y-4">
            {reportData.monthlyStats.map((month) => (
              <div key={month.month} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-900">{month.month}</span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <span className="text-blue-600">{month.projects} projects</span>
                  <span className="text-green-600">${month.revenue.toLocaleString()}</span>
                  <span className="text-purple-600">{month.satisfaction.toFixed(1)} rating</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Clients */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Clients</h3>
          <div className="space-y-4">
            {reportData.topClients.map((client, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">{index + 1}</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{client.name}</p>
                    <p className="text-xs text-gray-500">{client.projects} projects</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">${client.totalValue.toLocaleString()}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Designers */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Top Performing Designers</h2>
          <p className="text-gray-600 mt-1">Designers with highest ratings and project counts</p>
        </div>

        <div className="divide-y divide-gray-200">
          {reportData.topDesigners.map((designer, index) => (
            <div key={index} className="p-6 hover:bg-gray-50 transition-colors duration-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {designer.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{designer.name}</h3>
                    <p className="text-sm text-gray-600">{designer.projects} projects completed</p>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Average Rating</p>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="font-semibold text-gray-900">{designer.avgRating.toFixed(1)}</span>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Performance</p>
                    <div className="flex items-center gap-1">
                      {designer.avgRating >= 4.5 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-orange-500" />
                      )}
                      <span className="font-semibold text-gray-900">
                        {designer.avgRating >= 4.5 ? 'Excellent' : 'Good'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Insights */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <Target className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Key Insights</h3>
            <div className="text-blue-800 space-y-2">
              <p>• <strong>Project Growth:</strong> {reportData.totalProjects > 10 ? 'Strong' : 'Moderate'} project portfolio with ${reportData.totalBudget.toLocaleString()} total value</p>
              <p>• <strong>Client Satisfaction:</strong> {reportData.clientSatisfaction >= 4.0 ? 'Excellent' : 'Good'} satisfaction score of {reportData.clientSatisfaction.toFixed(1)}/5</p>
              <p>• <strong>Quality Performance:</strong> {reportData.qualityScore >= 4.0 ? 'High' : 'Moderate'} quality standards maintained</p>
              <p>• <strong>Delivery Performance:</strong> {reportData.onTimeDelivery}% on-time delivery rate</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
