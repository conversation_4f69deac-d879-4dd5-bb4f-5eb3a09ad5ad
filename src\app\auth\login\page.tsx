"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useLogin } from "@/hooks/useOptimizedAuth";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-hot-toast";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectedFrom = searchParams.get('redirectedFrom');
  const errorParam = searchParams.get('error');

  const loginMutation = useLogin();

  // Show error message from URL params
  useEffect(() => {
    if (errorParam === 'access_denied') {
      toast.error('Access denied. Please check your permissions.');
    }
  }, [errorParam]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    loginMutation.mutate({
      email,
      password,
      redirectedFrom: redirectedFrom || undefined
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-black">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black bg-opacity-70 z-10" aria-hidden="true" />
        <img
          src="https://images.unsplash.com/photo-1487958449943-2429e8be8625"
          alt="Architectural background"
          className="object-cover w-full h-full"
        />
      </div>

      <div className="relative z-10 bg-white p-10 rounded-none shadow-2xl max-w-md w-full">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <h1 className="text-primary font-bold text-2xl tracking-tight">SENIOR'S ARCHI-FIRM</h1>
          </Link>
          <h2 className="text-2xl font-bold mt-6 mb-2">Welcome Back</h2>
          <p className="text-gray-600">Log in to continue your architectural journey</p>
        </div>

        {loginMutation.error && (
          <div className="bg-red-50 text-red-500 p-4 mb-6 border-l-4 border-red-500">
            {loginMutation.error.message}
          </div>
        )}

        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="••••••••"
            />
            <div className="flex justify-end mt-1">
              <Link href="/auth/forgot-password" className="text-sm text-primary hover:underline">
                Forgot password?
              </Link>
            </div>
          </div>

          <Button
            type="submit"
            variant="default"
            size="lg"
            className="w-full"
            disabled={loginMutation.isPending}
          >
            {loginMutation.isPending ? "Logging in..." : "Log In"}
          </Button>
        </form>

        <div className="mt-8 text-center">
          <p className="text-gray-600">
            Don't have an account?{" "}
            <Link href="/auth/signup" className="text-primary font-medium hover:underline">
              Sign Up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
