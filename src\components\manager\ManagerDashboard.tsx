'use client';

import React, { useState, useEffect, Suspense, lazy } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  MessageSquare,
  FileText,
  DollarSign,
  Eye,
  RefreshCw,
  Zap,
  Activity
} from 'lucide-react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';
import { supabase } from '@/lib/supabase';

// Lazy load performance components
const PerformanceMetrics = lazy(() => import('@/components/optimized/PerformanceMetrics'));
const AdvancedAnalytics = lazy(() => import('@/components/optimized/AdvancedAnalytics'));

interface ProjectAssignment {
  id: string;
  project_id: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: string;
  assigned_at: string;
  projects: {
    id: string;
    title: string;
    status: string;
    budget: number;
    client: {
      full_name: string;
      avatar_url?: string;
      email: string;
    };
    designer: {
      full_name: string;
      avatar_url?: string;
      email: string;
    };
  };
  metrics: {
    quality_reviews_count: number;
    pending_reviews_count: number;
    negotiation_sessions_count: number;
    active_negotiations_count: number;
    total_milestones: number;
    completed_milestones: number;
    progress_percentage: number;
  };
}

interface ManagerActivity {
  id: string;
  activity_type: string;
  description: string;
  outcome?: string;
  time_spent_minutes?: number;
  created_at: string;
  projects: {
    title: string;
    status: string;
  };
}

interface ManagerDashboardProps {
  className?: string;
}

export default function ManagerDashboard({ className }: ManagerDashboardProps) {
  const { user, profile } = useOptimizedAuth();
  const [projects, setProjects] = useState<ProjectAssignment[]>([]);
  const [activities, setActivities] = useState<ManagerActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [stats, setStats] = useState({
    total_projects: 0,
    active_projects: 0,
    high_priority: 0,
    pending_reviews: 0,
    active_negotiations: 0,
    avg_progress: 0
  });

  // Initialize performance optimization
  const {
    fetchOptimizedProjects,
    fetchOptimizedDashboard,
    metrics,
    isOptimized,
    getCacheStats
  } = usePerformanceOptimization(user?.id || '', profile?.role || '', {
    enableCaching: true,
    enablePreloading: true,
    enableRealtime: true,
    cacheTimeout: 3 * 60 * 1000 // 3 minutes for manager data
  });

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchProjects();
      fetchActivities();
    }
  }, [user, profile]);

  const fetchProjects = async (forceRefresh = false) => {
    try {
      setLoading(true);
      if (forceRefresh) setRefreshing(true);

      // Try optimized dashboard first (includes projects + stats)
      if (isOptimized) {
        try {
          const dashboardData = await fetchOptimizedDashboard(forceRefresh);

          if (dashboardData && dashboardData.recent_projects) {
            // Map optimized data to existing format
            const projectList = dashboardData.recent_projects.map((project: any) => ({
              id: project.id,
              project_id: project.id,
              priority: project.priority || 'normal',
              status: project.status,
              assigned_at: project.created_at,
              projects: {
                id: project.id,
                title: project.title,
                status: project.status,
                budget: project.budget || 0,
                client: {
                  full_name: project.client_name || 'Unknown Client',
                  avatar_url: project.client_avatar,
                  email: project.client_email || ''
                },
                designer: {
                  full_name: project.designer_name || 'Unknown Designer',
                  avatar_url: project.designer_avatar,
                  email: project.designer_email || ''
                }
              },
              metrics: {
                quality_reviews_count: project.quality_reviews_count || 0,
                pending_reviews_count: project.pending_reviews_count || 0,
                negotiation_sessions_count: project.active_negotiations_count || 0,
                active_negotiations_count: project.active_negotiations_count || 0,
                total_milestones: project.total_milestones || 0,
                completed_milestones: project.completed_milestones || 0,
                progress_percentage: project.progress_percentage || 0
              }
            }));

            setProjects(projectList);

            // Use optimized stats if available
            if (dashboardData.stats) {
              setStats({
                total_projects: dashboardData.stats.total_projects || 0,
                active_projects: dashboardData.stats.active_projects || 0,
                high_priority: dashboardData.stats.high_priority_projects || 0,
                pending_reviews: dashboardData.stats.pending_escrow_approvals || 0,
                active_negotiations: dashboardData.stats.active_negotiations || 0,
                avg_progress: Math.round(dashboardData.stats.average_project_progress || 0)
              });
            }

            setLastRefresh(new Date());
            return;
          }
        } catch (optimizedError) {
          console.warn('Optimized dashboard failed, falling back to regular API:', optimizedError);
        }
      }

      // Fallback to original API
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch('/api/manager/projects?limit=50', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const projectList = data.projects || [];
        setProjects(projectList);

        // Calculate stats
        const totalProjects = projectList.length;
        const activeProjects = projectList.filter((p: any) => p.status === 'active').length;
        const highPriority = projectList.filter((p: any) => p.priority === 'high' || p.priority === 'urgent').length;
        const pendingReviews = projectList.reduce((sum: number, p: any) => sum + (p.metrics?.pending_reviews_count || 0), 0);
        const activeNegotiations = projectList.reduce((sum: number, p: any) => sum + (p.metrics?.active_negotiations_count || 0), 0);
        const avgProgress = totalProjects > 0 ?
          Math.round(projectList.reduce((sum: number, p: any) => sum + (p.metrics?.progress_percentage || 0), 0) / totalProjects) : 0;

        setStats({
          total_projects: totalProjects,
          active_projects: activeProjects,
          high_priority: highPriority,
          pending_reviews: pendingReviews,
          active_negotiations: activeNegotiations,
          avg_progress: avgProgress
        });

        setLastRefresh(new Date());
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchActivities = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch('/api/manager/activities?limit=10', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setActivities(data.activities || []);
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'warning';
      case 'normal': return 'default';
      default: return 'secondary';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'active': return 'default';
      case 'on_hold': return 'warning';
      default: return 'secondary';
    }
  };

  const formatActivityType = (type: string) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const handleStartNegotiation = async (projectId: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch('/api/manager/negotiations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          project_id: projectId,
          session_type: 'pricing',
          manager_notes: 'Initiated pricing negotiation session'
        })
      });

      if (response.ok) {
        fetchProjects();
        // Navigate to negotiation page
        window.location.href = `/manager/negotiations?project_id=${projectId}`;
      }
    } catch (error) {
      console.error('Error starting negotiation:', error);
    }
  };

  if (!profile || profile.role !== 'manager') {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Access denied. Managers only.</p>
      </div>
    );
  }

  // Manual refresh function
  const handleRefresh = () => {
    fetchProjects(true);
    fetchActivities();
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Performance Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manager Dashboard</h1>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            {isOptimized && (
              <>
                <Zap className="h-4 w-4 text-green-500" />
                <span>Performance Optimized</span>
                <span>•</span>
              </>
            )}
            <Activity className="h-4 w-4 text-blue-500" />
            <span>Cache Hit Rate: {Math.round(getCacheStats().hitRate)}%</span>
            {lastRefresh && (
              <>
                <span>•</span>
                <span>Updated: {lastRefresh.toLocaleTimeString()}</span>
              </>
            )}
          </div>
        </div>
        <Button
          onClick={handleRefresh}
          variant="outline"
          size="sm"
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Total Projects</p>
                <p className="text-2xl font-bold">{stats.total_projects}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">Active</p>
                <p className="text-2xl font-bold text-green-600">{stats.active_projects}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">High Priority</p>
                <p className="text-2xl font-bold text-red-600">{stats.high_priority}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="text-sm font-medium">Pending Reviews</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending_reviews}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-sm font-medium">Active Negotiations</p>
                <p className="text-2xl font-bold text-purple-600">{stats.active_negotiations}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-indigo-500" />
              <div>
                <p className="text-sm font-medium">Avg Progress</p>
                <p className="text-2xl font-bold text-indigo-600">{stats.avg_progress}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Project Overview</TabsTrigger>
          <TabsTrigger value="activities">Recent Activities</TabsTrigger>
          <TabsTrigger value="negotiations">Negotiations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : projects.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">No projects assigned yet.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {projects.map((assignment) => (
                <Card key={assignment.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-lg">{assignment.projects.title}</CardTitle>
                        <p className="text-sm text-muted-foreground">
                          Client: {assignment.projects.client.full_name} • 
                          Designer: {assignment.projects.designer.full_name}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getPriorityBadgeColor(assignment.priority)}>
                          {assignment.priority.toUpperCase()}
                        </Badge>
                        <Badge variant={getStatusBadgeColor(assignment.projects.status)}>
                          {assignment.projects.status.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{assignment.metrics.progress_percentage}%</span>
                      </div>
                      <Progress value={assignment.metrics.progress_percentage} className="h-2" />
                      <p className="text-xs text-muted-foreground">
                        {assignment.metrics.completed_milestones} of {assignment.metrics.total_milestones} milestones completed
                      </p>
                    </div>

                    {/* Metrics Grid */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="text-center">
                        <p className="font-medium text-yellow-600">{assignment.metrics.pending_reviews_count}</p>
                        <p className="text-muted-foreground">Pending Reviews</p>
                      </div>
                      <div className="text-center">
                        <p className="font-medium text-purple-600">{assignment.metrics.active_negotiations_count}</p>
                        <p className="text-muted-foreground">Active Negotiations</p>
                      </div>
                      <div className="text-center">
                        <p className="font-medium text-blue-600">{assignment.metrics.quality_reviews_count}</p>
                        <p className="text-muted-foreground">Total Reviews</p>
                      </div>
                      <div className="text-center">
                        <p className="font-medium text-green-600">${assignment.projects.budget?.toLocaleString()}</p>
                        <p className="text-muted-foreground">Budget</p>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-between pt-2">
                      <div className="text-xs text-muted-foreground">
                        Assigned: {new Date(assignment.assigned_at).toLocaleDateString()}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.location.href = `/manager/projects/${assignment.project_id}`}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                        {assignment.metrics.active_negotiations_count === 0 && (
                          <Button 
                            size="sm"
                            onClick={() => handleStartNegotiation(assignment.project_id)}
                            className="bg-purple-600 hover:bg-purple-700"
                          >
                            <MessageSquare className="h-4 w-4 mr-1" />
                            Start Negotiation
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="activities" className="space-y-4">
          {activities.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">No recent activities.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {activities.map((activity) => (
                <Card key={activity.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <p className="font-medium">{formatActivityType(activity.activity_type)}</p>
                        <p className="text-sm text-muted-foreground">{activity.description}</p>
                        <p className="text-xs text-muted-foreground">
                          Project: {activity.projects.title}
                        </p>
                      </div>
                      <div className="text-right text-xs text-muted-foreground">
                        <p>{new Date(activity.created_at).toLocaleDateString()}</p>
                        {activity.time_spent_minutes && (
                          <p>{activity.time_spent_minutes} minutes</p>
                        )}
                        {activity.outcome && (
                          <Badge variant="outline" className="mt-1">
                            {activity.outcome}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="negotiations">
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground">Negotiation management interface coming soon.</p>
              <Button 
                className="mt-4"
                onClick={() => window.location.href = '/manager/negotiations'}
              >
                Go to Negotiations
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Performance Analytics (Lazy Loaded) */}
      {isOptimized && (
        <div className="space-y-4">
          <Suspense fallback={<Skeleton className="h-64 w-full" />}>
            <AdvancedAnalytics
              userRole="manager"
              userId={user?.id || ''}
              dashboardData={{ stats, recent_projects: projects }}
            />
          </Suspense>

          <Suspense fallback={<Skeleton className="h-32 w-full" />}>
            <PerformanceMetrics
              cacheStats={getCacheStats()}
              lastRefresh={lastRefresh}
            />
          </Suspense>
        </div>
      )}
    </div>
  );
}
