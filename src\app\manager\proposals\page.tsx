"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useManagerSettings } from "@/hooks/useRealtimeSettings";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  FileText,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  User,
  DollarSign,
  Calendar,
  Filter,
  Search,
  RefreshCw,
  MessageSquare,
  Flag,
  TrendingUp,
  Star
} from "lucide-react";

interface Proposal {
  id: string;
  project_id: string;
  designer_id: string;
  title: string;
  description: string;
  budget: number;
  timeline: string;
  status: string;
  admin_status: string;
  client_status: string;
  manager_notes?: string;
  manager_reviewed_at?: string;
  created_at: string;
  updated_at: string;
  project: {
    title: string;
    status: string;
    client: {
      full_name: string;
      email: string;
    };
  };
  designer: {
    full_name: string;
    email: string;
    specialization: string;
  };
}

export default function ManagerProposalsPage() {
  const { user, profile } = useOptimizedAuth();
  const settings = useManagerSettings();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchProposals();
    }
  }, [user, profile, filter]);

  const fetchProposals = async () => {
    try {
      setLoading(true);
      
      // MANAGERS SEE ALL PROPOSALS - Watchdog role
      let query = supabase
        .from('project_proposals')
        .select(`
          *,
          project:projects(
            title, status,
            client:profiles!projects_client_id_fkey(full_name, email)
          ),
          designer:profiles!project_proposals_designer_id_fkey(full_name, email, specialization)
        `);

      if (filter !== 'all') {
        if (filter === 'needs_review') {
          query = query.in('status', ['submitted', 'admin_approved']);
        } else {
          query = query.eq('status', filter);
        }
      }

      const { data, error } = await query
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProposals(data || []);
    } catch (error) {
      console.error('Error fetching proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateProposalStatus = async (proposalId: string, action: string, notes?: string) => {
    try {
      const updates: any = {
        updated_at: new Date().toISOString()
      };

      if (action === 'manager_approve') {
        updates.manager_status = 'approved';
        updates.manager_notes = notes;
        updates.manager_reviewed_at = new Date().toISOString();
        updates.manager_reviewed_by = user?.id;
      } else if (action === 'manager_reject') {
        updates.manager_status = 'rejected';
        updates.manager_notes = notes;
        updates.manager_reviewed_at = new Date().toISOString();
        updates.manager_reviewed_by = user?.id;
      } else if (action === 'escalate') {
        updates.manager_status = 'escalated';
        updates.manager_notes = notes;
        updates.priority = 'high';
      }

      const { error } = await supabase
        .from('project_proposals')
        .update(updates)
        .eq('id', proposalId);

      if (error) throw error;

      // Log manager activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: proposals.find(p => p.id === proposalId)?.project_id,
        activity_type: 'proposal_review',
        description: `${action.replace('_', ' ')} proposal`,
        outcome: action,
        notes
      });

      fetchProposals();
    } catch (error) {
      console.error('Error updating proposal:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'draft':
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
      case 'submitted':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'admin_approved':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'client_approved':
        return `${baseClasses} bg-emerald-100 text-emerald-800 border border-emerald-200`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case 'revision_requested':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getManagerActionButtons = (proposal: Proposal) => {
    const canTakeAction = ['submitted', 'admin_approved'].includes(proposal.status);
    
    if (!canTakeAction) return null;

    return (
      <div className="flex gap-2">
        <Button
          size="sm"
          onClick={() => updateProposalStatus(proposal.id, 'manager_approve', 'Approved by manager oversight')}
          className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="h-4 w-4" />
          Approve
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => updateProposalStatus(proposal.id, 'escalate', 'Escalated for admin review')}
          className="flex items-center gap-2 text-amber-600 border-amber-200 hover:bg-amber-50"
        >
          <Flag className="h-4 w-4" />
          Escalate
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => updateProposalStatus(proposal.id, 'manager_reject', 'Rejected by manager oversight')}
          className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
        >
          <XCircle className="h-4 w-4" />
          Reject
        </Button>
      </div>
    );
  };

  const filteredProposals = proposals.filter(proposal =>
    proposal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    proposal.designer.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    proposal.project.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getProposalStats = () => {
    const total = proposals.length;
    const needsReview = proposals.filter(p => ['submitted', 'admin_approved'].includes(p.status)).length;
    const approved = proposals.filter(p => p.status === 'client_approved').length;
    const rejected = proposals.filter(p => p.status === 'rejected').length;

    return { total, needsReview, approved, rejected };
  };

  const stats = getProposalStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Proposal Oversight</h1>
          <p className="text-gray-600 mt-2">Review and manage all designer proposals across the platform</p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={fetchProposals}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Proposals</p>
              <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Needs Review</p>
              <p className="text-2xl font-bold text-amber-600">{stats.needsReview}</p>
            </div>
            <Clock className="h-8 w-8 text-amber-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
            </div>
            <XCircle className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex gap-2">
            {[
              { key: 'all', label: 'All Proposals' },
              { key: 'needs_review', label: 'Needs Review' },
              { key: 'submitted', label: 'Submitted' },
              { key: 'admin_approved', label: 'Admin Approved' },
              { key: 'client_approved', label: 'Client Approved' },
              { key: 'rejected', label: 'Rejected' }
            ].map((filterOption) => (
              <Button
                key={filterOption.key}
                variant={filter === filterOption.key ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter(filterOption.key)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {filterOption.label}
              </Button>
            ))}
          </div>

          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search proposals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Proposals List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Proposals ({filteredProposals.length})
          </h2>
          
          {filteredProposals.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No proposals found</h3>
              <p className="text-gray-600">No proposals match your current filters.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredProposals.map((proposal) => (
                <div key={proposal.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{proposal.title}</h3>
                        <span className={getStatusBadge(proposal.status)}>
                          {proposal.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>{proposal.designer.full_name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          <span>${proposal.budget?.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(proposal.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <p className="text-gray-700 mb-4 line-clamp-2">{proposal.description}</p>
                      
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Project:</span> {proposal.project.title} • 
                        <span className="font-medium"> Client:</span> {proposal.project.client.full_name}
                      </div>
                    </div>

                    <div className="flex flex-col gap-3">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.href = `/manager/proposals/${proposal.id}`}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          View Details
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.href = `/manager/projects/${proposal.project_id}`}
                          className="flex items-center gap-2"
                        >
                          <MessageSquare className="h-4 w-4" />
                          View Project
                        </Button>
                      </div>
                      
                      {getManagerActionButtons(proposal)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
