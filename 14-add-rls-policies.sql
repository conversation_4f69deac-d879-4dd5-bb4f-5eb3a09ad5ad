-- =====================================================
-- SCRIPT 14: ADD RLS POLICIES FOR NEW TABLES
-- =====================================================

-- Enable RLS on platform_fee_settings
ALTER TABLE platform_fee_settings ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read platform fee settings
CREATE POLICY "Allow authenticated users to read platform fee settings"
ON platform_fee_settings FOR SELECT
TO authenticated
USING (true);

-- Policy: Allow admins and managers to update platform fee settings
CREATE POLICY "Allow admins and managers to update platform fee settings"
ON platform_fee_settings FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin', 'manager')
  )
);

-- Enable RLS on payment_settings
ALTER TABLE payment_settings ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read payment settings
CREATE POLICY "Allow authenticated users to read payment settings"
ON payment_settings FOR SELECT
TO authenticated
USING (true);

-- Policy: Allow admins to manage payment settings
CREATE POLICY "Allow admins to manage payment settings"
ON payment_settings FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- Enable RLS on paypal_escrow_holds
ALTER TABLE paypal_escrow_holds ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read escrow holds
CREATE POLICY "Allow authenticated users to read escrow holds"
ON paypal_escrow_holds FOR SELECT
TO authenticated
USING (true);

-- Policy: Allow managers and admins to manage escrow holds
CREATE POLICY "Allow managers and admins to manage escrow holds"
ON paypal_escrow_holds FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin', 'manager')
  )
);

-- Enable RLS on paypal_escrow_releases
ALTER TABLE paypal_escrow_releases ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read escrow releases
CREATE POLICY "Allow authenticated users to read escrow releases"
ON paypal_escrow_releases FOR SELECT
TO authenticated
USING (true);

-- Policy: Allow managers and admins to manage escrow releases
CREATE POLICY "Allow managers and admins to manage escrow releases"
ON paypal_escrow_releases FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin', 'manager')
  )
);

-- Enable RLS on platform_revenue
ALTER TABLE platform_revenue ENABLE ROW LEVEL SECURITY;

-- Policy: Allow admins and managers to read platform revenue
CREATE POLICY "Allow admins and managers to read platform revenue"
ON platform_revenue FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin', 'manager')
  )
);

-- Policy: Allow admins to manage platform revenue
CREATE POLICY "Allow admins to manage platform revenue"
ON platform_revenue FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- Enable RLS on designer_payouts
ALTER TABLE designer_payouts ENABLE ROW LEVEL SECURITY;

-- Policy: Allow designers to read their own payouts
CREATE POLICY "Allow designers to read their own payouts"
ON designer_payouts FOR SELECT
TO authenticated
USING (designer_id = auth.uid());

-- Policy: Allow admins and managers to read all payouts
CREATE POLICY "Allow admins and managers to read all payouts"
ON designer_payouts FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin', 'manager')
  )
);

-- Policy: Allow admins to manage all payouts
CREATE POLICY "Allow admins to manage all payouts"
ON designer_payouts FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- Verify completion
SELECT 'Script 14 completed: RLS policies added for all new tables' as status;
