"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/Button";
import {
  Briefcase,
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  Settings,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  Star,
  MessageSquare
} from "lucide-react";

interface ProjectAssignment {
  id: string;
  project_id: string;
  status: string;
  priority: string;
  assigned_at: string;
  notes: string;
  project: {
    title: string;
    description: string;
    status: string;
    budget: number;
    created_at: string;
    quality_status: string;
    client: {
      full_name: string;
    };
    designer: {
      full_name: string;
    };
  };
}

export default function ManagerProjectsPage() {
  const { user, profile } = useOptimizedAuth();
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('active');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchAssignments();
    }
  }, [user, profile, filter]);

  const fetchAssignments = async () => {
    try {
      // FIXED: Managers see ALL projects, not just assigned ones
      let query = supabase
        .from('projects')
        .select(`
          *,
          client:profiles!projects_client_id_fkey(full_name, email),
          designer:profiles!projects_designer_id_fkey(full_name, email),
          milestones:project_milestones(id, title, status, due_date),
          proposals:project_proposals(id, status, created_at)
        `);

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform data to match existing interface
      const transformedData = data?.map(project => ({
        id: project.id,
        project_id: project.id,
        project: project,
        status: 'active', // Manager oversight is always active
        priority: 'normal',
        assigned_at: project.created_at
      })) || [];

      setAssignments(transformedData);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Briefcase className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'transferred':
        return <Users className="h-4 w-4 text-purple-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'transferred':
        return `${baseClasses} bg-purple-100 text-purple-800 border border-purple-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getQualityStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (status) {
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'in_review':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredAssignments = assignments.filter(assignment =>
    assignment.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.project?.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.project?.designer_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Projects</h1>
          <p className="text-gray-600 mt-2">Manage and coordinate all assigned projects</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => window.location.href = '/manager/projects/assign'}
            className="flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Assign Projects
          </Button>
          <Button
            onClick={fetchAssignments}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Projects</p>
              <p className="text-2xl font-bold text-blue-600">
                {assignments.filter(a => a.status === 'active').length}
              </p>
            </div>
            <Briefcase className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">
                {assignments.filter(a => a.status === 'completed').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">High Priority</p>
              <p className="text-2xl font-bold text-orange-600">
                {assignments.filter(a => a.priority === 'high' || a.priority === 'urgent').length}
              </p>
            </div>
            <AlertTriangle className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Budget</p>
              <p className="text-2xl font-bold text-purple-600">
                ${assignments.reduce((sum, a) => sum + (a.project?.budget || 0), 0).toLocaleString()}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="active">Active Projects</option>
              <option value="completed">Completed Projects</option>
              <option value="transferred">Transferred Projects</option>
              <option value="all">All Projects</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects, clients, or designers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Projects List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Project Assignments</h2>
          <p className="text-gray-600 mt-1">Click on any project to manage and coordinate</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredAssignments.length === 0 ? (
            <div className="p-8 text-center">
              <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No project assignments found</p>
            </div>
          ) : (
            filteredAssignments.map((assignment) => (
              <div key={assignment.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {getStatusIcon(assignment.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {assignment.project?.title || 'Untitled Project'}
                      </h3>
                      <span className={getStatusBadge(assignment.status)}>
                        {assignment.status.toUpperCase()}
                      </span>
                      <span className={getPriorityBadge(assignment.priority)}>
                        {assignment.priority.toUpperCase()}
                      </span>
                      {assignment.project?.quality_status && (
                        <span className={getQualityStatusBadge(assignment.project.quality_status)}>
                          Quality: {assignment.project.quality_status.replace('_', ' ')}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{assignment.project?.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Client:</span> {assignment.project?.client?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Designer:</span> {assignment.project?.designer?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        <span className="font-medium">Budget:</span> ${assignment.project?.budget?.toLocaleString()}
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">Assigned:</span> {new Date(assignment.assigned_at).toLocaleDateString()}
                      </div>
                    </div>

                    {assignment.notes && (
                      <div className="bg-gray-50 rounded-lg p-3 mt-3">
                        <p className="text-sm text-gray-700">
                          <span className="font-medium">Notes:</span> {assignment.notes}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/projects/${assignment.project_id}`}
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/projects/${assignment.project_id}/coordinate`}
                    >
                      <MessageSquare className="h-4 w-4" />
                      Coordinate
                    </Button>
                    
                    {assignment.status === 'active' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/manager/projects/${assignment.project_id}/coordinate`}
                      >
                        <Settings className="h-4 w-4" />
                        Manage
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
