// Script to check the database schema
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSchema() {
  try {
    // Get the profiles table schema
    const { data: profilesSchema, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (profilesError) {
      console.error('Error fetching profiles schema:', profilesError);
      return;
    }

    if (profilesSchema && profilesSchema.length > 0) {
      console.log('Profiles table schema:');
      console.log(Object.keys(profilesSchema[0]));
    } else {
      console.log('No profiles found to check schema');
    }

    // Get the connections table schema
    const { data: connectionsSchema, error: connectionsError } = await supabase
      .from('connections')
      .select('*')
      .limit(1);

    if (connectionsError) {
      console.error('Error fetching connections schema:', connectionsError);
      return;
    }

    if (connectionsSchema && connectionsSchema.length > 0) {
      console.log('\nConnections table schema:');
      console.log(Object.keys(connectionsSchema[0]));
    } else {
      console.log('No connections found to check schema');
    }

    // Check active connections
    const { data: connections, error: connectionsQueryError } = await supabase
      .from('connections')
      .select('*');

    if (connectionsQueryError) {
      console.error('Error fetching connections:', connectionsQueryError);
      return;
    }

    console.log('\nActive connections:', connections.length);
    if (connections.length > 0) {
      console.log(connections);
    }

  } catch (error) {
    console.error('Error checking schema:', error);
  }
}

checkSchema();
