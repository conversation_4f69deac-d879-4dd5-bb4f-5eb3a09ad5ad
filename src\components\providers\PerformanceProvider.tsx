"use client";

import { useEffect } from 'react';
import { PerformanceDebug, PerformanceSummary } from '@/components/debug/PerformanceDebug';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

// Performance provider that safely wraps performance monitoring components
export function PerformanceProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      <SafePerformanceComponents />
    </>
  );
}

// Safe wrapper for performance components that handles errors gracefully
function SafePerformanceComponents() {
  const { user } = useOptimizedAuth();

  // Only render performance components when user is available and we're in the right context
  useEffect(() => {
    // Ensure we're in the browser and have the necessary context
    if (typeof window === 'undefined') return;
    
    // Log performance optimization status
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 Performance monitoring initialized');
    }
  }, [user]);

  try {
    return (
      <>
        <PerformanceDebug />
        <PerformanceSummary />
      </>
    );
  } catch (error) {
    // Gracefully handle any errors in performance components
    if (process.env.NODE_ENV === 'development') {
      console.warn('Performance monitoring components failed to load:', error);
    }
    return null;
  }
}
