import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import Link from "next/link";

// FAQ categories and items
const faqCategories = [
  {
    title: "General Questions",
    items: [
      {
        question: "What is the typical project timeline?",
        answer: "The timeline varies based on the scope and complexity of the project, but most projects take between 4 to 12 months from concept to completion. We'll provide a detailed timeline during our initial consultation."
      },
      {
        question: "How is pricing determined?",
        answer: "Pricing is based on the project type, size, and level of customization. We'll provide a detailed quote after discussing your vision and requirements."
      },
      {
        question: "Do you work internationally?",
        answer: "Yes, we have experience working on projects around the world. Our team can collaborate remotely or arrange site visits as needed."
      }
    ]
  },
  {
    title: "Design Process",
    items: [
      {
        question: "What is your design process like?",
        answer: "Our design process includes an initial consultation, concept development, design refinement, and final delivery, with your input valued at every stage."
      },
      {
        question: "How many revisions are included?",
        answer: "We include two rounds of revisions in our standard package to ensure your vision is accurately brought to life."
      },
      {
        question: "Can you work with existing plans or designs?",
        answer: "Yes, we can collaborate with your existing plans or designs and incorporate them into our design process."
      }
    ]
  },
  {
    title: "Project Management",
    items: [
      {
        question: "Do you handle construction management?",
        answer: "Yes, we offer construction management services to ensure your project is executed according to the design specifications and quality standards."
      },
      {
        question: "How do you handle project delays?",
        answer: "We build buffer time into our project schedules to account for potential delays. If unexpected delays occur, we communicate proactively and adjust the timeline accordingly."
      },
      {
        question: "What is the payment schedule?",
        answer: "Payments are typically divided into three installments, aligned with key project milestones."
      }
    ]
  },
  {
    title: "Technical Questions",
    items: [
      {
        question: "What software do you use for design?",
        answer: "We use industry-leading software including AutoCAD, Revit, SketchUp, and Adobe Creative Suite for our design work."
      },
      {
        question: "Do you provide 3D renderings?",
        answer: "Yes, we provide photorealistic 3D renderings to help you visualize the final result before construction begins."
      },
      {
        question: "How do you handle sustainable design requirements?",
        answer: "We integrate sustainable design principles into all our projects, considering factors like energy efficiency, material selection, and environmental impact."
      }
    ]
  }
];

export default function FAQPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative h-[40vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <div
            className="absolute inset-0 bg-black bg-opacity-50 z-10"
            aria-hidden="true"
          />
          <img
            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
            alt="FAQ Hero"
            className="object-cover w-full h-full"
          />
        </div>
        <div className="container mx-auto px-4 z-20">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-4">Frequently Asked Questions</h1>
          <p className="text-xl text-white max-w-2xl">
            Find answers to common questions about our process, services, and approach.
          </p>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* FAQ Categories */}
            <div className="space-y-16">
              {faqCategories.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <h2 className="text-3xl font-bold mb-8">{category.title}</h2>
                  <div className="space-y-6">
                    {category.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="bg-white shadow-md p-6">
                        <h3 className="text-xl font-bold mb-3">{item.question}</h3>
                        <p className="text-gray-600">{item.answer}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Still Have Questions */}
            <div className="mt-20 text-center">
              <h2 className="text-2xl font-bold mb-4">Still Have Questions?</h2>
              <p className="text-gray-600 mb-8">
                Contact us directly and we'll be happy to assist you with any inquiries.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link href="/contact">
                  <Button variant="default" size="lg">
                    Contact Us
                  </Button>
                </Link>
                <a 
                  href="https://calendly.com/seniorsarchifirm/consultation" 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  <Button variant="outline" size="lg">
                    Schedule a Call
                  </Button>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
