import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('Starting automatic payout scheduler...')

    // Get platform settings
    const { data: settings, error: settingsError } = await supabase
      .from('platform_settings')
      .select('*')
      .single()

    if (settingsError) {
      console.error('Error fetching platform settings:', settingsError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch platform settings' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!settings?.enable_automatic_payouts) {
      console.log('Automatic payouts are disabled')
      return new Response(
        JSON.stringify({ message: 'Automatic payouts are disabled' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if it's the right time to process payouts
    const now = new Date()
    const currentDay = now.getDay() // 0 = Sunday, 1 = Monday, etc.
    const currentDate = now.getDate()
    
    let shouldProcess = false
    
    if (settings.payout_schedule === 'daily') {
      shouldProcess = true
    } else if (settings.payout_schedule === 'weekly') {
      // Check if today is the configured payout day
      shouldProcess = currentDay === (settings.payout_day_of_week || 1) // Default Monday
    } else if (settings.payout_schedule === 'monthly') {
      // Check if today is the configured payout day of month
      shouldProcess = currentDate === (settings.payout_day_of_month || 1) // Default 1st
    }

    if (!shouldProcess) {
      console.log(`Not scheduled for payout today. Schedule: ${settings.payout_schedule}, Current day: ${currentDay}, Current date: ${currentDate}`)
      return new Response(
        JSON.stringify({ 
          message: 'Not scheduled for payout today',
          schedule: settings.payout_schedule,
          nextPayoutDay: settings.payout_day_of_week || settings.payout_day_of_month
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('Processing scheduled payouts...')

    // Get designers ready for payout
    const { data: readyDesigners, error: designersError } = await supabase
      .rpc('get_designers_ready_for_payout')

    if (designersError) {
      console.error('Error fetching designers ready for payout:', designersError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch designers for payout' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!readyDesigners || readyDesigners.length === 0) {
      console.log('No designers ready for payout')
      return new Response(
        JSON.stringify({ message: 'No designers ready for payout' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Found ${readyDesigners.length} designers ready for payout`)

    // Process payouts for each designer
    const results = []
    let totalProcessed = 0
    let totalAmount = 0

    for (const designer of readyDesigners) {
      try {
        console.log(`Processing payout for designer ${designer.designer_id}: $${designer.total_amount}`)

        // Call the automatic payout function for this designer
        const { data: payoutResult, error: payoutError } = await supabase.functions.invoke(
          'process-automatic-payouts',
          {
            body: { 
              designerId: designer.designer_id,
              force: false 
            }
          }
        )

        if (payoutError) {
          console.error(`Error processing payout for designer ${designer.designer_id}:`, payoutError)
          results.push({
            designerId: designer.designer_id,
            status: 'failed',
            error: payoutError.message,
            amount: designer.total_amount
          })
        } else {
          console.log(`Successfully processed payout for designer ${designer.designer_id}`)
          results.push({
            designerId: designer.designer_id,
            status: 'success',
            amount: designer.total_amount,
            itemsProcessed: designer.item_count
          })
          totalProcessed++
          totalAmount += designer.total_amount
        }

      } catch (error) {
        console.error(`Unexpected error processing payout for designer ${designer.designer_id}:`, error)
        results.push({
          designerId: designer.designer_id,
          status: 'failed',
          error: error.message,
          amount: designer.total_amount
        })
      }
    }

    // Create summary notification for admins
    const { data: admins } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'admin')

    const summaryMessage = `Automatic payout completed: ${totalProcessed}/${readyDesigners.length} successful, $${totalAmount.toFixed(2)} total processed`

    for (const admin of admins || []) {
      await supabase
        .from('notifications')
        .insert({
          user_id: admin.id,
          type: 'admin',
          title: 'Automatic Payout Summary',
          content: summaryMessage,
          link: '/admin/finance/payouts',
          read: false
        })
    }

    // Log the summary
    console.log(summaryMessage)

    return new Response(
      JSON.stringify({
        success: true,
        summary: {
          totalDesigners: readyDesigners.length,
          successfulPayouts: totalProcessed,
          failedPayouts: readyDesigners.length - totalProcessed,
          totalAmount: totalAmount,
          schedule: settings.payout_schedule
        },
        results
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in payout scheduler:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
