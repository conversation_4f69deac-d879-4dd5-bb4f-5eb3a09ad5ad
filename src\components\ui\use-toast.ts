// Simplified version using react-hot-toast
import hotToast, { Toast as HotToast } from "react-hot-toast";

type ToastProps = {
  title?: string;
  description?: string;
  variant?: "default" | "destructive" | "success";
  duration?: number;
};

export function toast({
  title,
  description,
  variant = "default",
  duration = 3000,
}: ToastProps) {
  const message = title ? (description ? `${title}: ${description}` : title) : description;

  if (!message) return;

  switch (variant) {
    case "destructive":
      return hotToast.error(message, { duration });
    case "success":
      return hotToast.success(message, { duration });
    default:
      return hotToast(message, { duration });
  }
}

// We don't need to re-export toast since we're already exporting it above
// export { toast };
