import { NextRequest, NextResponse } from 'next/server';

/**
 * Environment health check API
 * Returns the status of required environment variables
 */
export async function GET(request: NextRequest) {
  try {
    const envCheck = {
      supabase: {
        url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        anonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        serviceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      },
      r2: {
        endpoint: !!process.env.CLOUDFLARE_R2_ENDPOINT,
        accessKeyId: !!process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: !!process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
        publicUrl: !!process.env.CLOUDFLARE_R2_PUBLIC_URL,
      },
      email: {
        resendApiKey: !!process.env.RESEND_API_KEY,
      },
      stripe: {
        publishableKey: !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
        secretKey: !!process.env.STRIPE_SECRET_KEY,
        webhookSecret: !!process.env.STRIPE_WEBHOOK_SECRET,
      },
      paypal: {
        clientId: !!process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID,
        clientSecret: !!process.env.PAYPAL_CLIENT_SECRET,
      }
    };

    // Calculate overall health
    const criticalServices = [
      envCheck.supabase.url,
      envCheck.supabase.anonKey,
      envCheck.supabase.serviceRoleKey,
      envCheck.r2.endpoint,
      envCheck.r2.accessKeyId,
      envCheck.r2.secretAccessKey,
      envCheck.email.resendApiKey,
    ];

    const allCriticalConfigured = criticalServices.every(Boolean);
    const missingCritical = [];

    if (!envCheck.supabase.url) missingCritical.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!envCheck.supabase.anonKey) missingCritical.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    if (!envCheck.supabase.serviceRoleKey) missingCritical.push('SUPABASE_SERVICE_ROLE_KEY');
    if (!envCheck.r2.endpoint) missingCritical.push('CLOUDFLARE_R2_ENDPOINT');
    if (!envCheck.r2.accessKeyId) missingCritical.push('CLOUDFLARE_R2_ACCESS_KEY_ID');
    if (!envCheck.r2.secretAccessKey) missingCritical.push('CLOUDFLARE_R2_SECRET_ACCESS_KEY');
    if (!envCheck.email.resendApiKey) missingCritical.push('RESEND_API_KEY');

    const warnings = [];
    if (!envCheck.r2.publicUrl) warnings.push('CLOUDFLARE_R2_PUBLIC_URL (will use proxy fallback)');

    return NextResponse.json({
      status: allCriticalConfigured ? 'healthy' : 'unhealthy',
      environment: process.env.NODE_ENV,
      services: envCheck,
      missing: missingCritical,
      warnings: warnings,
      message: allCriticalConfigured 
        ? 'All critical environment variables are configured'
        : `Missing critical environment variables: ${missingCritical.join(', ')}`,
      timestamp: new Date().toISOString()
    }, { 
      status: allCriticalConfigured ? 200 : 500 
    });

  } catch (error) {
    console.error('Error checking environment:', error);
    return NextResponse.json(
      { 
        status: 'error',
        error: 'Failed to check environment configuration',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
