-- =====================================================
-- MINIMAL ROLE UPDATE - ALTERNATIVE APPROACH
-- If the main migration still fails, run this first
-- =====================================================

-- Option 1: If you want to keep role as text/varchar and add the new roles
-- This is the safest approach if enum conversion is problematic

-- Check current role column type
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' AND column_name = 'role';

-- If role is text/varchar, just ensure it can handle the new role values
-- Update any existing check constraints to include new roles
DO $$
BEGIN
    -- Drop existing check constraint if it exists
    BEGIN
        ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
    EXCEPTION
        WHEN OTHERS THEN NULL;
    END;
    
    -- Add new check constraint with all roles
    ALTER TABLE profiles ADD CONSTRAINT profiles_role_check 
    CHECK (role IN ('client', 'designer', 'admin', 'quality_team', 'manager'));
    
    RAISE NOTICE 'Updated role constraint to include new roles';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not update role constraint: %', SQLERRM;
END $$;

-- =====================================================
-- ALTERNATIVE: Create enum separately and use it later
-- =====================================================

-- Create the enum type separately
DO $$
BEGIN
    -- Create enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role_enum') THEN
        CREATE TYPE user_role_enum AS ENUM ('client', 'designer', 'admin', 'quality_team', 'manager');
        RAISE NOTICE 'Created user_role_enum type';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating enum: %', SQLERRM;
END $$;

-- You can convert to enum later when ready:
-- ALTER TABLE profiles ALTER COLUMN role TYPE user_role_enum USING role::user_role_enum;

-- =====================================================
-- VERIFY CURRENT STATE
-- =====================================================

-- Check what we have now
SELECT 'Current profiles table structure:' as info;
\d profiles;

SELECT 'Current role values in use:' as info;
SELECT role, COUNT(*) as count 
FROM profiles 
GROUP BY role 
ORDER BY role;

SELECT 'Check if enum exists:' as info;
SELECT typname, typtype 
FROM pg_type 
WHERE typname LIKE '%role%';

-- =====================================================
-- INSTRUCTIONS
-- =====================================================

/*
INSTRUCTIONS FOR USING THIS SCRIPT:

1. Run this script first to safely update role constraints
2. Then run the main safe-quality-manager-migration.sql
3. If you still get errors, you can:
   - Keep roles as text/varchar (works fine)
   - Convert to enum later when convenient
   - Or manually create test users with new roles

MANUAL USER CREATION (if needed):
*/

-- Example: Create test users manually
-- INSERT INTO profiles (id, email, full_name, role, is_active) 
-- VALUES 
--   (gen_random_uuid(), '<EMAIL>', 'Quality Team Member', 'quality_team', true),
--   (gen_random_uuid(), '<EMAIL>', 'Project Manager', 'manager', true)
-- ON CONFLICT (email) DO NOTHING;

SELECT 'Minimal role update completed. You can now run the main migration.' as final_status;
