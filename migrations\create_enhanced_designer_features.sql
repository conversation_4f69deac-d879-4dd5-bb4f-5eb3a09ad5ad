-- Enhanced Designer Features Schema
-- This adds all the missing features for the enhanced designer dashboard

-- 1. Project Briefs Table (Client requirements without pricing)
CREATE TABLE IF NOT EXISTS project_briefs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  requirements TEXT,
  preferred_style TEXT,
  budget_range TEXT CHECK (budget_range IN ('under_5k', '5k_10k', '10k_25k', '25k_plus', 'flexible')),
  timeline_preference TEXT,
  location TEXT,
  project_type TEXT,
  urgency TEXT DEFAULT 'medium' CHECK (urgency IN ('low', 'medium', 'high', 'urgent')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'assigned', 'proposal_received', 'accepted', 'rejected')),
  assigned_designer_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  assigned_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  assigned_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Designer Availability Table
CREATE TABLE IF NOT EXISTS designer_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID UNIQUE NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'available' CHECK (status IN ('available', 'busy', 'offline')),
  custom_message TEXT,
  auto_accept_briefs BOOLEAN DEFAULT FALSE,
  max_concurrent_projects INTEGER DEFAULT 5,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Project Reviews/Ratings Table
CREATE TABLE IF NOT EXISTS project_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
  quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
  timeliness_rating INTEGER CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
  would_recommend BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, client_id)
);

-- 4. Admin Messages/Notifications Table
CREATE TABLE IF NOT EXISTS admin_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  recipient_role TEXT CHECK (recipient_role IN ('designer', 'client', 'all')),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'info' CHECK (message_type IN ('info', 'warning', 'success', 'urgent', 'announcement')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  read_at TIMESTAMP WITH TIME ZONE,
  action_required BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Designer Skills and Specializations Table
CREATE TABLE IF NOT EXISTS designer_specializations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  specialization TEXT NOT NULL,
  skill_level TEXT DEFAULT 'intermediate' CHECK (skill_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
  years_experience INTEGER DEFAULT 0,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Portfolio Images Table (for portfolio projects)
CREATE TABLE IF NOT EXISTS portfolio_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  portfolio_project_id UUID NOT NULL REFERENCES portfolio_projects(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  image_name TEXT,
  is_cover BOOLEAN DEFAULT FALSE,
  order_index INTEGER DEFAULT 0,
  alt_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Quick Actions Table (for dashboard quick actions)
CREATE TABLE IF NOT EXISTS designer_quick_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL CHECK (action_type IN ('create_proposal', 'message_client', 'update_portfolio', 'set_availability', 'view_briefs')),
  action_label TEXT NOT NULL,
  action_url TEXT NOT NULL,
  icon_name TEXT,
  is_enabled BOOLEAN DEFAULT TRUE,
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_briefs_client_id ON project_briefs(client_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_assigned_designer_id ON project_briefs(assigned_designer_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_status ON project_briefs(status);
CREATE INDEX IF NOT EXISTS idx_designer_availability_designer_id ON designer_availability(designer_id);
CREATE INDEX IF NOT EXISTS idx_project_reviews_designer_id ON project_reviews(designer_id);
CREATE INDEX IF NOT EXISTS idx_project_reviews_project_id ON project_reviews(project_id);
CREATE INDEX IF NOT EXISTS idx_admin_messages_recipient_id ON admin_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_admin_messages_read_at ON admin_messages(read_at);
CREATE INDEX IF NOT EXISTS idx_portfolio_images_portfolio_project_id ON portfolio_images(portfolio_project_id);

-- Insert default availability for existing designers
INSERT INTO designer_availability (designer_id, status)
SELECT id, 'available'
FROM profiles 
WHERE role = 'designer' 
AND id NOT IN (SELECT designer_id FROM designer_availability WHERE designer_id IS NOT NULL)
ON CONFLICT (designer_id) DO NOTHING;

-- Insert default quick actions for designers
INSERT INTO designer_quick_actions (designer_id, action_type, action_label, action_url, icon_name, order_index)
SELECT 
  p.id,
  action_type,
  action_label,
  action_url,
  icon_name,
  order_index
FROM profiles p
CROSS JOIN (
  VALUES 
    ('view_briefs', 'View New Briefs', '/designer/briefs', 'Briefcase', 1),
    ('create_proposal', 'Create Proposal', '/designer/proposals/new', 'FileText', 2),
    ('message_client', 'Message Clients', '/designer/messages', 'MessageSquare', 3),
    ('update_portfolio', 'Update Portfolio', '/designer/portfolio', 'Image', 4),
    ('set_availability', 'Set Availability', '/designer/settings/availability', 'Settings', 5)
) AS actions(action_type, action_label, action_url, icon_name, order_index)
WHERE p.role = 'designer'
ON CONFLICT DO NOTHING;
