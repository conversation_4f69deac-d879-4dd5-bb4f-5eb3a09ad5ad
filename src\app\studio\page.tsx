import Layout from "@/components/Layout";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const stats = [
  { number: "25+", label: "Years of Experience" },
  { number: "150+", label: "Completed Projects" },
  { number: "15", label: "Design Awards" },
  { number: "30", label: "Team Members" },
];

const values = [
  {
    title: "Research",
    areas: ["Sustainable Design", "Material Innovation", "Digital Fabrication", "Building Performance"]
  },
  {
    title: "Special Services",
    areas: ["BIM Integration", "3D Visualization", "Feasibility Studies", "Heritage Conservation"]
  }
];

const facilities = [
  {
    title: "Design Studio",
    description: "State-of-the-art collaborative workspace equipped with the latest design and visualization tools.",
    image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c"
  },
  {
    title: "Materials Library",
    description: "Comprehensive collection of sustainable and innovative building materials for inspiration and specification.",
    image: "https://images.unsplash.com/photo-1600573472550-8090b5e0745e"
  },
  {
    title: "Digital Lab",
    description: "Advanced 3D printing and prototyping facilities for model making and material testing.",
    image: "https://images.unsplash.com/photo-1600585154526-990dced4db0d"
  },
  {
    title: "Exhibition Space",
    description: "Gallery showcasing our latest work, research findings, and architectural innovations.",
    image: "https://images.unsplash.com/photo-1517245386807-bb43f82c33c4"
  }
];

export default function Studio() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative h-[90vh] flex items-center">
        <Image
          src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c"
          alt="Studio Space"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/60" />
        <div className="container mx-auto px-4 relative z-10 text-white">
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            Where Innovation <br />Meets Craft
          </h1>
          <div className="h-1 w-20 bg-primary mb-8" />
          <p className="text-xl md:text-2xl max-w-2xl">
            Step inside our creative hub where cutting-edge technology meets traditional craftsmanship.
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-black text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-6xl font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-sm md:text-base text-gray-400">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Facilities Section */}
      <section className="py-20 bg-black text-white">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold mb-16">Our Facilities</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {facilities.map((facility, index) => (
              <div key={index} className="group">
                <div className="relative h-64 mb-6 overflow-hidden">
                  <Image
                    src={facility.image}
                    alt={facility.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <h3 className="text-2xl font-bold mb-4">{facility.title}</h3>
                <p className="text-gray-400">{facility.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Research & Innovation Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-bold mb-6">Research & Innovation</h2>
              <div className="h-1 w-20 bg-primary mb-8" />
              <p className="text-gray-600 mb-6">
                Our studio is committed to pushing the boundaries of architectural innovation through dedicated research programs and experimental projects.
              </p>
              <p className="text-gray-600 mb-8">
                From material innovation to computational design, we invest in research that shapes the future of architecture and construction.
              </p>
              <Button className="group">
                View Research Projects
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </div>
            <div className="relative aspect-square">
              <Image
                src="https://images.unsplash.com/photo-1600585154526-990dced4db0d"
                alt="Research Lab"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}

