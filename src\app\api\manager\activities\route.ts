import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/manager/activities
 * Get manager activities for the authenticated manager
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('project_id');
    const activityType = searchParams.get('activity_type') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is manager or admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['manager', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Build query
    let query = supabase
      .from('manager_activities')
      .select(`
        id,
        manager_id,
        project_id,
        activity_type,
        description,
        participants,
        outcome,
        time_spent_minutes,
        created_at,
        projects:project_id (
          title,
          status,
          client_id,
          designer_id,
          client:client_id (
            full_name,
            avatar_url
          ),
          designer:designer_id (
            full_name,
            avatar_url
          )
        ),
        manager:manager_id (
          full_name,
          avatar_url
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by manager if not admin
    if (profile.role === 'manager') {
      query = query.eq('manager_id', user.id);
    }

    // Filter by project if specified
    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    // Filter by activity type if specified
    if (activityType !== 'all') {
      query = query.eq('activity_type', activityType);
    }

    const { data: activities, error } = await query;

    if (error) {
      console.error('Error fetching manager activities:', error);
      return NextResponse.json({ error: 'Failed to fetch activities' }, { status: 500 });
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('manager_activities')
      .select('id', { count: 'exact', head: true });

    if (profile.role === 'manager') {
      countQuery = countQuery.eq('manager_id', user.id);
    }

    if (projectId) {
      countQuery = countQuery.eq('project_id', projectId);
    }

    if (activityType !== 'all') {
      countQuery = countQuery.eq('activity_type', activityType);
    }

    const { count } = await countQuery;

    return NextResponse.json({
      activities,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in GET /api/manager/activities:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/manager/activities
 * Create a new manager activity
 */
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is manager or admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['manager', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const {
      project_id,
      activity_type,
      description,
      participants = [],
      outcome,
      time_spent_minutes
    } = await request.json();

    if (!project_id || !activity_type || !description) {
      return NextResponse.json(
        { error: 'Project ID, activity type, and description are required' },
        { status: 400 }
      );
    }

    // Validate activity type
    const validActivityTypes = [
      'negotiation_guidance',
      'milestone_review',
      'communication',
      'escalation',
      'client_meeting',
      'designer_meeting',
      'quality_review',
      'project_planning',
      'issue_resolution',
      'status_update'
    ];

    if (!validActivityTypes.includes(activity_type)) {
      return NextResponse.json(
        { error: 'Invalid activity type' },
        { status: 400 }
      );
    }

    // Check if project exists and manager has access
    const { data: assignment } = await supabase
      .from('project_assignments')
      .select('manager_id, projects:project_id (title)')
      .eq('project_id', project_id)
      .eq('status', 'active')
      .single();

    if (!assignment) {
      return NextResponse.json({ error: 'Project not found or not assigned' }, { status: 404 });
    }

    // Check if user is the assigned manager or admin
    if (profile.role === 'manager' && assignment.manager_id !== user.id) {
      return NextResponse.json({ error: 'Not assigned to this project' }, { status: 403 });
    }

    // Create activity
    const { data: activity, error } = await supabase
      .from('manager_activities')
      .insert({
        manager_id: profile.role === 'admin' ? assignment.manager_id : user.id,
        project_id,
        activity_type,
        description,
        participants: participants.length > 0 ? participants : [user.id],
        outcome,
        time_spent_minutes
      })
      .select(`
        *,
        projects:project_id (
          title,
          status
        ),
        manager:manager_id (
          full_name,
          avatar_url
        )
      `)
      .single();

    if (error) {
      console.error('Error creating manager activity:', error);
      return NextResponse.json({ error: 'Failed to create activity' }, { status: 500 });
    }

    // Create notifications for participants if needed
    if (activity_type === 'escalation' || activity_type === 'issue_resolution') {
      const notifications = participants
        .filter(participantId => participantId !== user.id)
        .map(participantId => ({
          recipient_id: participantId,
          notification_type: 'manager_activity',
          title: `Manager Activity: ${activity_type.replace('_', ' ').toUpperCase()}`,
          message: description,
          priority: activity_type === 'escalation' ? 'urgent' : 'normal',
          metadata: { 
            activity_id: activity.id, 
            project_id,
            activity_type 
          }
        }));

      if (notifications.length > 0) {
        await supabase
          .from('workflow_notifications')
          .insert(notifications);
      }
    }

    return NextResponse.json(activity, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/manager/activities:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * GET /api/manager/activities/stats
 * Get activity statistics for manager dashboard
 */
export async function GET_STATS(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30'; // days
    const projectId = searchParams.get('project_id');

    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['manager', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(timeframe));

    // Build base query
    let query = supabase
      .from('manager_activities')
      .select('activity_type, time_spent_minutes, created_at')
      .gte('created_at', startDate.toISOString());

    if (profile.role === 'manager') {
      query = query.eq('manager_id', user.id);
    }

    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    const { data: activities, error } = await query;

    if (error) {
      console.error('Error fetching activity stats:', error);
      return NextResponse.json({ error: 'Failed to fetch stats' }, { status: 500 });
    }

    // Calculate statistics
    const stats = {
      total_activities: activities?.length || 0,
      total_time_spent: activities?.reduce((sum, activity) => 
        sum + (activity.time_spent_minutes || 0), 0) || 0,
      activities_by_type: {},
      activities_by_day: {},
      average_time_per_activity: 0
    };

    // Group by activity type
    activities?.forEach(activity => {
      const type = activity.activity_type;
      if (!stats.activities_by_type[type]) {
        stats.activities_by_type[type] = {
          count: 0,
          total_time: 0
        };
      }
      stats.activities_by_type[type].count++;
      stats.activities_by_type[type].total_time += activity.time_spent_minutes || 0;
    });

    // Group by day
    activities?.forEach(activity => {
      const day = new Date(activity.created_at).toISOString().split('T')[0];
      if (!stats.activities_by_day[day]) {
        stats.activities_by_day[day] = 0;
      }
      stats.activities_by_day[day]++;
    });

    // Calculate average time per activity
    if (stats.total_activities > 0) {
      stats.average_time_per_activity = Math.round(stats.total_time_spent / stats.total_activities);
    }

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error in GET /api/manager/activities/stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
