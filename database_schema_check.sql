-- COMPREHENSIVE MESSAGING SYSTEM AUDIT
-- ⚠️ READ-ONLY DIAGNOSTIC SCRIPT - MAKES NO CHANGES ⚠️
-- Run this in your Supabase Dashboard SQL Editor to understand your current messaging setup

-- ========================================
-- PART 1: TABLE EXISTENCE CHECK
-- ========================================

-- Check which messaging tables exist
SELECT
  table_name,
  CASE
    WHEN table_name = 'conversations' THEN 'New Unified System'
    WHEN table_name = 'conversation_participants' THEN 'New Unified System'
    WHEN table_name = 'conversation_messages' THEN 'New Unified System'
    WHEN table_name = 'project_messages' THEN 'Project-Based System'
    WHEN table_name = 'messages' THEN 'Old Legacy System'
    ELSE 'Other'
  END as system_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('conversations', 'conversation_participants', 'conversation_messages', 'project_messages', 'messages')
ORDER BY system_type, table_name;

-- ========================================
-- PART 2: CONVERSATIONS TABLE ANALYSIS
-- ========================================

-- Check conversations table structure
SELECT
  'CONVERSATIONS TABLE STRUCTURE' as analysis_section,
  column_name,
  data_type,
  is_nullable,
  column_default,
  CASE
    WHEN column_name IN ('participant_one_id', 'participant_two_id') THEN '❌ OLD SCHEMA'
    WHEN column_name IN ('type', 'project_id', 'created_by') THEN '✅ NEW SCHEMA'
    ELSE '📋 COMMON'
  END as schema_indicator
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'conversations'
ORDER BY ordinal_position;

-- ========================================
-- PART 3: DATA COUNT ANALYSIS
-- ========================================

-- Count records in each messaging table (if they exist)
DO $$
DECLARE
    conversations_count INTEGER := 0;
    participants_count INTEGER := 0;
    conv_messages_count INTEGER := 0;
    project_messages_count INTEGER := 0;
    old_messages_count INTEGER := 0;
BEGIN
    -- Count conversations
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'conversations') THEN
        SELECT COUNT(*) INTO conversations_count FROM conversations;
    END IF;

    -- Count conversation_participants
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'conversation_participants') THEN
        SELECT COUNT(*) INTO participants_count FROM conversation_participants;
    END IF;

    -- Count conversation_messages
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'conversation_messages') THEN
        SELECT COUNT(*) INTO conv_messages_count FROM conversation_messages;
    END IF;

    -- Count project_messages
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_messages') THEN
        SELECT COUNT(*) INTO project_messages_count FROM project_messages;
    END IF;

    -- Count old messages
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
        SELECT COUNT(*) INTO old_messages_count FROM messages;
    END IF;

    -- Display results
    RAISE NOTICE '========================================';
    RAISE NOTICE 'MESSAGING DATA SUMMARY:';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Conversations (new): % records', conversations_count;
    RAISE NOTICE 'Conversation Participants: % records', participants_count;
    RAISE NOTICE 'Conversation Messages: % records', conv_messages_count;
    RAISE NOTICE 'Project Messages: % records', project_messages_count;
    RAISE NOTICE 'Old Messages: % records', old_messages_count;
    RAISE NOTICE '========================================';

    IF conversations_count > 0 AND participants_count > 0 THEN
        RAISE NOTICE '✅ NEW UNIFIED SYSTEM: Active and has data';
    ELSIF conversations_count > 0 THEN
        RAISE NOTICE '⚠️ NEW UNIFIED SYSTEM: Partially set up';
    ELSE
        RAISE NOTICE '❌ NEW UNIFIED SYSTEM: Not set up';
    END IF;

    IF project_messages_count > 0 THEN
        RAISE NOTICE '⚠️ PROJECT MESSAGES: % records found - NEEDS MIGRATION', project_messages_count;
    END IF;

    IF old_messages_count > 0 THEN
        RAISE NOTICE '❌ OLD MESSAGES: % records found - NEEDS MIGRATION', old_messages_count;
    END IF;

    RAISE NOTICE '========================================';
END $$;

-- ========================================
-- PART 4: SAMPLE DATA INSPECTION
-- ========================================

-- Show sample conversations (if table exists and has data)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'conversations') THEN
        IF EXISTS (SELECT 1 FROM conversations LIMIT 1) THEN
            RAISE NOTICE 'SAMPLE CONVERSATIONS DATA:';
            -- Note: Cannot use dynamic SQL in simple DO blocks, so we'll use a separate query
        END IF;
    END IF;
END $$;

-- Sample conversations data (run separately if conversations table exists)
-- SELECT 'SAMPLE CONVERSATIONS' as data_type, id, type, title, project_id, created_at FROM conversations LIMIT 3;

-- Sample conversation_participants data (run separately if table exists)
-- SELECT 'SAMPLE PARTICIPANTS' as data_type, conversation_id, user_id, role FROM conversation_participants LIMIT 5;

-- Sample project_messages data (run separately if table exists)
-- SELECT 'SAMPLE PROJECT MESSAGES' as data_type, id, project_id, sender_id, content, created_at FROM project_messages LIMIT 3;

-- ========================================
-- PART 5: MIGRATION READINESS ASSESSMENT
-- ========================================

DO $$
DECLARE
    has_old_schema BOOLEAN := FALSE;
    has_new_schema BOOLEAN := FALSE;
    has_project_messages BOOLEAN := FALSE;
    has_old_messages BOOLEAN := FALSE;
    migration_complexity TEXT := 'SIMPLE';
BEGIN
    -- Check for old schema columns
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'conversations'
        AND column_name IN ('participant_one_id', 'participant_two_id')
    ) THEN
        has_old_schema := TRUE;
    END IF;

    -- Check for new schema
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'conversation_participants'
    ) THEN
        has_new_schema := TRUE;
    END IF;

    -- Check for project messages
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'project_messages'
    ) AND EXISTS (SELECT 1 FROM project_messages LIMIT 1) THEN
        has_project_messages := TRUE;
    END IF;

    -- Check for old messages
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'messages'
    ) AND EXISTS (SELECT 1 FROM messages LIMIT 1) THEN
        has_old_messages := TRUE;
    END IF;

    -- Determine migration complexity
    IF has_project_messages OR has_old_messages THEN
        migration_complexity := 'COMPLEX - MULTIPLE SYSTEMS';
    ELSIF has_old_schema AND has_new_schema THEN
        migration_complexity := 'MEDIUM - SCHEMA CONFLICT';
    ELSIF has_old_schema THEN
        migration_complexity := 'SIMPLE - SCHEMA UPDATE';
    ELSIF has_new_schema THEN
        migration_complexity := 'NONE - ALREADY UPDATED';
    END IF;

    RAISE NOTICE '========================================';
    RAISE NOTICE 'MIGRATION READINESS ASSESSMENT:';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Old Schema Detected: %', has_old_schema;
    RAISE NOTICE 'New Schema Present: %', has_new_schema;
    RAISE NOTICE 'Project Messages Found: %', has_project_messages;
    RAISE NOTICE 'Old Messages Found: %', has_old_messages;
    RAISE NOTICE 'Migration Complexity: %', migration_complexity;
    RAISE NOTICE '========================================';

    IF migration_complexity = 'COMPLEX - MULTIPLE SYSTEMS' THEN
        RAISE NOTICE '⚠️ WARNING: Complex migration required!';
        RAISE NOTICE '   Multiple messaging systems detected.';
        RAISE NOTICE '   Data migration planning needed.';
        RAISE NOTICE '   DO NOT proceed with simple schema migration.';
    ELSIF migration_complexity = 'MEDIUM - SCHEMA CONFLICT' THEN
        RAISE NOTICE '⚠️ CAUTION: Schema conflict detected.';
        RAISE NOTICE '   Backup data before proceeding.';
    ELSIF migration_complexity = 'SIMPLE - SCHEMA UPDATE' THEN
        RAISE NOTICE '✅ SAFE: Simple schema update possible.';
    ELSE
        RAISE NOTICE '✅ COMPLETE: System already up to date.';
    END IF;

    RAISE NOTICE '========================================';
END $$;
