-- =====================================================
-- PERFORMANCE OPTIMIZATION TRIGGERS
-- Automated cache refresh and data consistency
-- =====================================================

-- Check if required functions and tables exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_proc
        WHERE proname = 'refresh_project_metrics_cache'
    ) THEN
        RAISE EXCEPTION 'Required function refresh_project_metrics_cache not found. Please run performance-optimization-schema.sql first.';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'project_metrics_cache'
    ) THEN
        RAISE EXCEPTION 'Required table project_metrics_cache not found. Please run performance-optimization-schema.sql first.';
    END IF;

    RAISE NOTICE 'All required functions and tables found. Proceeding with trigger creation.';
END $$;

-- 1. PROJECT METRICS CACHE TRIGGERS
-- =====================================================

-- Function to refresh project cache on project changes
CREATE OR REPLACE FUNCTION trigger_refresh_project_cache()
RETURNS TRIGGER AS $$
BEGIN
    -- Refresh cache for the affected project
    IF TG_OP = 'DELETE'b THEN
        DELETE FROM project_metrics_cache WHERE project_id = OLD.id;
        RETURN OLD;
    ELSE
        PERFORM refresh_project_metrics_cache(COALESCE(NEW.id, NEW.project_id));
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Triggers for project table changes
DROP TRIGGER IF EXISTS trigger_project_cache_refresh ON projects;
CREATE TRIGGER trigger_project_cache_refresh
    AFTER INSERT OR UPDATE OR DELETE ON projects
    FOR EACH ROW EXECUTE FUNCTION trigger_refresh_project_cache();

-- Triggers for milestone changes
DROP TRIGGER IF EXISTS trigger_milestone_cache_refresh ON project_milestones;
CREATE TRIGGER trigger_milestone_cache_refresh
    AFTER INSERT OR UPDATE OR DELETE ON project_milestones
    FOR EACH ROW EXECUTE FUNCTION trigger_refresh_project_cache();

-- Triggers for quality review changes
DROP TRIGGER IF EXISTS trigger_quality_cache_refresh ON quality_reviews_new;
CREATE TRIGGER trigger_quality_cache_refresh
    AFTER INSERT OR UPDATE OR DELETE ON quality_reviews_new
    FOR EACH ROW EXECUTE FUNCTION trigger_refresh_project_cache();

-- Triggers for negotiation changes (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'negotiation_sessions') THEN
        DROP TRIGGER IF EXISTS trigger_negotiation_cache_refresh ON negotiation_sessions;
        CREATE TRIGGER trigger_negotiation_cache_refresh
            AFTER INSERT OR UPDATE OR DELETE ON negotiation_sessions
            FOR EACH ROW EXECUTE FUNCTION trigger_refresh_project_cache();
    END IF;
END $$;

-- Triggers for escrow changes (only if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'escrow_holds') THEN
        DROP TRIGGER IF EXISTS trigger_escrow_holds_cache_refresh ON escrow_holds;
        CREATE TRIGGER trigger_escrow_holds_cache_refresh
            AFTER INSERT OR UPDATE OR DELETE ON escrow_holds
            FOR EACH ROW EXECUTE FUNCTION trigger_refresh_project_cache();
    END IF;
END $$;

DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'escrow_releases') THEN
        DROP TRIGGER IF EXISTS trigger_escrow_releases_cache_refresh ON escrow_releases;
        CREATE TRIGGER trigger_escrow_releases_cache_refresh
            AFTER INSERT OR UPDATE OR DELETE ON escrow_releases
            FOR EACH ROW EXECUTE FUNCTION trigger_refresh_project_cache();
    END IF;
END $$;

-- 2. MANAGER DASHBOARD CACHE TRIGGERS
-- =====================================================

-- Function to refresh manager dashboard cache
CREATE OR REPLACE FUNCTION trigger_refresh_manager_dashboard_cache()
RETURNS TRIGGER AS $$
DECLARE
    affected_manager_id UUID;
BEGIN
    -- Determine which manager to refresh
    IF TG_OP = 'DELETE' THEN
        affected_manager_id := COALESCE(OLD.assigned_manager_id, OLD.manager_id);
    ELSE
        affected_manager_id := COALESCE(NEW.assigned_manager_id, NEW.manager_id);
    END IF;
    
    -- Refresh manager dashboard cache if manager is identified
    IF affected_manager_id IS NOT NULL THEN
        PERFORM refresh_manager_dashboard_cache(affected_manager_id);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Triggers for manager-related changes
DROP TRIGGER IF EXISTS trigger_manager_dashboard_project_refresh ON projects;
CREATE TRIGGER trigger_manager_dashboard_project_refresh
    AFTER INSERT OR UPDATE OR DELETE ON projects
    FOR EACH ROW EXECUTE FUNCTION trigger_refresh_manager_dashboard_cache();

DROP TRIGGER IF EXISTS trigger_manager_dashboard_activity_refresh ON manager_activities;
CREATE TRIGGER trigger_manager_dashboard_activity_refresh
    AFTER INSERT OR UPDATE OR DELETE ON manager_activities
    FOR EACH ROW EXECUTE FUNCTION trigger_refresh_manager_dashboard_cache();

-- 3. MATERIALIZED VIEW REFRESH TRIGGERS
-- =====================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS VOID AS $$
BEGIN
    -- Refresh project overview materialized view
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_project_overview;
    
    -- Refresh manager performance materialized view
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_manager_performance;
    
    -- Log the refresh
    INSERT INTO system_logs (log_type, message, created_at)
    VALUES ('materialized_view_refresh', 'Materialized views refreshed successfully', NOW());
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error
        INSERT INTO system_logs (log_type, message, error_details, created_at)
        VALUES ('materialized_view_refresh_error', 'Failed to refresh materialized views', SQLERRM, NOW());
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- 4. SCHEDULED CACHE MAINTENANCE
-- =====================================================

-- Function for comprehensive cache maintenance
CREATE OR REPLACE FUNCTION perform_cache_maintenance()
RETURNS TABLE(
    operation VARCHAR(50),
    affected_rows INTEGER,
    execution_time_ms INTEGER,
    status VARCHAR(20)
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    rows_affected INTEGER;
BEGIN
    -- 1. Refresh all project metrics cache
    start_time := clock_timestamp();
    SELECT refresh_project_metrics_cache() INTO rows_affected;
    end_time := clock_timestamp();
    
    RETURN QUERY SELECT 
        'project_metrics_refresh'::VARCHAR(50),
        rows_affected,
        EXTRACT(MILLISECONDS FROM end_time - start_time)::INTEGER,
        'completed'::VARCHAR(20);
    
    -- 2. Refresh all manager dashboard cache
    start_time := clock_timestamp();
    SELECT refresh_manager_dashboard_cache() INTO rows_affected;
    end_time := clock_timestamp();
    
    RETURN QUERY SELECT 
        'manager_dashboard_refresh'::VARCHAR(50),
        rows_affected,
        EXTRACT(MILLISECONDS FROM end_time - start_time)::INTEGER,
        'completed'::VARCHAR(20);
    
    -- 3. Clean up old cache entries
    start_time := clock_timestamp();
    DELETE FROM satisfaction_metrics_cache 
    WHERE last_updated < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    end_time := clock_timestamp();
    
    RETURN QUERY SELECT 
        'cache_cleanup'::VARCHAR(50),
        rows_affected,
        EXTRACT(MILLISECONDS FROM end_time - start_time)::INTEGER,
        'completed'::VARCHAR(20);
    
    -- 4. Refresh materialized views
    start_time := clock_timestamp();
    PERFORM refresh_materialized_views();
    end_time := clock_timestamp();
    
    RETURN QUERY SELECT 
        'materialized_views_refresh'::VARCHAR(50),
        1,
        EXTRACT(MILLISECONDS FROM end_time - start_time)::INTEGER,
        'completed'::VARCHAR(20);
    
    -- 5. Update statistics
    start_time := clock_timestamp();
    ANALYZE project_metrics_cache;
    ANALYZE manager_dashboard_cache;
    ANALYZE satisfaction_metrics_cache;
    end_time := clock_timestamp();
    
    RETURN QUERY SELECT 
        'analyze_tables'::VARCHAR(50),
        0,
        EXTRACT(MILLISECONDS FROM end_time - start_time)::INTEGER,
        'completed'::VARCHAR(20);
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT 
            'cache_maintenance_error'::VARCHAR(50),
            0,
            0,
            'failed'::VARCHAR(20);
        RAISE;
END;
$$ LANGUAGE plpgsql;

-- 5. CACHE INVALIDATION FUNCTIONS
-- =====================================================

-- Function to invalidate specific project cache
CREATE OR REPLACE FUNCTION invalidate_project_cache(target_project_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    -- Mark cache as stale
    UPDATE project_metrics_cache 
    SET last_updated = NOW() - INTERVAL '1 day',
        cache_version = cache_version + 1
    WHERE project_id = target_project_id;
    
    -- Refresh immediately
    PERFORM refresh_project_metrics_cache(target_project_id);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to invalidate manager dashboard cache
CREATE OR REPLACE FUNCTION invalidate_manager_cache(target_manager_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    -- Mark cache as stale
    UPDATE manager_dashboard_cache 
    SET last_updated = NOW() - INTERVAL '1 day',
        next_refresh_at = NOW(),
        cache_version = cache_version + 1
    WHERE manager_id = target_manager_id;
    
    -- Refresh immediately
    PERFORM refresh_manager_dashboard_cache(target_manager_id);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 6. PERFORMANCE MONITORING FUNCTIONS
-- =====================================================

-- Function to get cache performance statistics
CREATE OR REPLACE FUNCTION get_cache_performance_stats()
RETURNS TABLE(
    cache_name VARCHAR(50),
    total_entries INTEGER,
    fresh_entries INTEGER,
    stale_entries INTEGER,
    cache_hit_ratio DECIMAL(5,2),
    avg_age_hours DECIMAL(8,2),
    last_refresh TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Project metrics cache stats
    RETURN QUERY
    SELECT 
        'project_metrics'::VARCHAR(50),
        COUNT(*)::INTEGER,
        COUNT(CASE WHEN last_updated > NOW() - INTERVAL '1 hour' THEN 1 END)::INTEGER,
        COUNT(CASE WHEN last_updated <= NOW() - INTERVAL '1 hour' THEN 1 END)::INTEGER,
        ROUND(
            (COUNT(CASE WHEN last_updated > NOW() - INTERVAL '1 hour' THEN 1 END)::DECIMAL / 
             NULLIF(COUNT(*), 0)) * 100, 2
        ),
        ROUND(EXTRACT(EPOCH FROM AVG(NOW() - last_updated)) / 3600, 2),
        MAX(last_updated)
    FROM project_metrics_cache;
    
    -- Manager dashboard cache stats
    RETURN QUERY
    SELECT 
        'manager_dashboard'::VARCHAR(50),
        COUNT(*)::INTEGER,
        COUNT(CASE WHEN last_updated > NOW() - INTERVAL '1 hour' THEN 1 END)::INTEGER,
        COUNT(CASE WHEN last_updated <= NOW() - INTERVAL '1 hour' THEN 1 END)::INTEGER,
        ROUND(
            (COUNT(CASE WHEN last_updated > NOW() - INTERVAL '1 hour' THEN 1 END)::DECIMAL / 
             NULLIF(COUNT(*), 0)) * 100, 2
        ),
        ROUND(EXTRACT(EPOCH FROM AVG(NOW() - last_updated)) / 3600, 2),
        MAX(last_updated)
    FROM manager_dashboard_cache;
    
    -- Satisfaction metrics cache stats
    RETURN QUERY
    SELECT 
        'satisfaction_metrics'::VARCHAR(50),
        COUNT(*)::INTEGER,
        COUNT(CASE WHEN last_updated > NOW() - INTERVAL '24 hours' THEN 1 END)::INTEGER,
        COUNT(CASE WHEN last_updated <= NOW() - INTERVAL '24 hours' THEN 1 END)::INTEGER,
        ROUND(
            (COUNT(CASE WHEN last_updated > NOW() - INTERVAL '24 hours' THEN 1 END)::DECIMAL / 
             NULLIF(COUNT(*), 0)) * 100, 2
        ),
        ROUND(EXTRACT(EPOCH FROM AVG(NOW() - last_updated)) / 3600, 2),
        MAX(last_updated)
    FROM satisfaction_metrics_cache;
END;
$$ LANGUAGE plpgsql;

-- 7. SYSTEM LOGS TABLE FOR MONITORING
-- =====================================================

CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    error_details TEXT,
    execution_time_ms INTEGER,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes separately for system_logs table
CREATE INDEX IF NOT EXISTS idx_system_logs_type_created ON system_logs (log_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_logs_created ON system_logs (created_at DESC);

-- Enable RLS on system logs
ALTER TABLE system_logs ENABLE ROW LEVEL SECURITY;

-- 8. GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions on cache functions
GRANT EXECUTE ON FUNCTION refresh_project_metrics_cache TO authenticated;
GRANT EXECUTE ON FUNCTION refresh_manager_dashboard_cache TO authenticated;
GRANT EXECUTE ON FUNCTION perform_cache_maintenance TO authenticated;
GRANT EXECUTE ON FUNCTION get_cache_performance_stats TO authenticated;
GRANT EXECUTE ON FUNCTION invalidate_project_cache TO authenticated;
GRANT EXECUTE ON FUNCTION invalidate_manager_cache TO authenticated;

-- 9. INITIAL CACHE POPULATION
-- =====================================================

DO $$
DECLARE
    project_count INTEGER;
    manager_count INTEGER;
BEGIN
    -- Populate project metrics cache
    SELECT refresh_project_metrics_cache() INTO project_count;
    
    -- Populate manager dashboard cache
    SELECT refresh_manager_dashboard_cache() INTO manager_count;
    
    -- Refresh materialized views
    PERFORM refresh_materialized_views();
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 PERFORMANCE OPTIMIZATION TRIGGERS CREATED!';
    RAISE NOTICE '';
    RAISE NOTICE 'Cache population completed:';
    RAISE NOTICE '✅ Project metrics cache: % entries', project_count;
    RAISE NOTICE '✅ Manager dashboard cache: % entries', manager_count;
    RAISE NOTICE '✅ Materialized views refreshed';
    RAISE NOTICE '✅ Automated triggers activated';
    RAISE NOTICE '';
    RAISE NOTICE 'Performance monitoring available via:';
    RAISE NOTICE '• SELECT * FROM get_cache_performance_stats();';
    RAISE NOTICE '• SELECT * FROM perform_cache_maintenance();';
    RAISE NOTICE '';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '⚠️ Initial cache population failed: %', SQLERRM;
        RAISE NOTICE 'Triggers created successfully, but manual cache refresh may be needed';
END $$;
