"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { DashboardNotifications } from "@/components/shared/DashboardNotifications";
import ManagerDashboard from "@/components/manager/ManagerDashboard";
import EscrowManagementDashboard from "@/components/manager/EscrowManagementDashboard";
import {
  Users,
  Briefcase,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  MessageSquare,
  Calendar,
  TrendingUp,
  Eye,
  Filter,
  Search,
  RefreshCw,
  Settings,
  Star,
  Target
} from "lucide-react";

interface ProjectAssignment {
  id: string;
  project_id: string;
  status: string;
  priority: string;
  assigned_at: string;
  project: {
    title: string;
    status: string;
    budget: number;
    created_at: string;
    client: {
      full_name: string;
    };
    designer: {
      full_name: string;
    };
  };
}

interface ManagerStats {
  active_projects: number;
  pending_negotiations: number;
  escrow_releases: number;
  client_satisfaction: number;
}

export default function ManagerDashboardPage() {
  const { user, profile } = useOptimizedAuth();
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [stats, setStats] = useState<ManagerStats>({
    active_projects: 0,
    pending_negotiations: 0,
    escrow_releases: 0,
    client_satisfaction: 0
  });
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('active');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchAssignments();
      fetchStats();
    }
  }, [user, profile, filter]);

  const fetchAssignments = async () => {
    try {
      let query = supabase
        .from('project_assignments')
        .select(`
          *,
          project:projects(
            title, status, budget, created_at,
            client:profiles!projects_client_id_fkey(full_name),
            designer:profiles!projects_designer_id_fkey(full_name)
          )
        `)
        .eq('manager_id', user?.id);

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query
        .order('assigned_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      setAssignments(data || []);
    } catch (error) {
      console.error('Error fetching assignments:', error);
    }
  };

  const fetchStats = async () => {
    try {
      // Get active projects count
      const { count: activeCount } = await supabase
        .from('project_assignments')
        .select('*', { count: 'exact', head: true })
        .eq('manager_id', user?.id)
        .eq('status', 'active');

      // Get pending negotiations
      const { count: negotiationCount } = await supabase
        .from('negotiation_sessions')
        .select('*', { count: 'exact', head: true })
        .eq('manager_id', user?.id)
        .eq('status', 'active');

      // Get pending escrow releases
      const { count: escrowCount } = await supabase
        .from('escrow_releases')
        .select('*', { count: 'exact', head: true })
        .eq('manager_id', user?.id)
        .eq('status', 'pending');

      // Get average client satisfaction
      const { data: satisfactionData } = await supabase
        .from('client_satisfaction')
        .select('overall_rating')
        .eq('manager_id', user?.id)
        .not('overall_rating', 'is', null);

      const avgSatisfaction = satisfactionData?.length 
        ? satisfactionData.reduce((sum, item) => sum + (item.overall_rating || 0), 0) / satisfactionData.length
        : 0;

      setStats({
        active_projects: activeCount || 0,
        pending_negotiations: negotiationCount || 0,
        escrow_releases: escrowCount || 0,
        client_satisfaction: avgSatisfaction
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Briefcase className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'transferred':
        return <Users className="h-4 w-4 text-purple-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'transferred':
        return `${baseClasses} bg-purple-100 text-purple-800 border border-purple-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredAssignments = assignments.filter(assignment =>
    assignment.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.project?.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.project?.designer_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Manager Dashboard</h1>
          <p className="text-gray-600 mt-2">Oversee projects and coordinate between teams</p>
        </div>

        <div className="flex items-center space-x-4">
          <DashboardNotifications variant="header" role="manager" />
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => window.location.href = '/manager/negotiations'}
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              Negotiations
            </Button>
            <Button
              onClick={() => {
                fetchAssignments();
                fetchStats();
              }}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Escrow Management Dashboard */}
      <EscrowManagementDashboard />

      {/* New Integrated Manager Dashboard */}
      <ManagerDashboard />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Projects</p>
              <p className="text-3xl font-bold text-blue-600">{stats.active_projects}</p>
            </div>
            <Briefcase className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Negotiations</p>
              <p className="text-3xl font-bold text-orange-600">{stats.pending_negotiations}</p>
            </div>
            <MessageSquare className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Escrow Releases</p>
              <p className="text-3xl font-bold text-green-600">{stats.escrow_releases}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Client Satisfaction</p>
              <p className="text-3xl font-bold text-purple-600">{stats.client_satisfaction.toFixed(1)}</p>
            </div>
            <Star className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/escrow'}
          >
            <DollarSign className="h-5 w-5" />
            Manage Escrow
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/negotiations'}
          >
            <MessageSquare className="h-5 w-5" />
            Active Negotiations
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/satisfaction'}
          >
            <Star className="h-5 w-5" />
            Collect Feedback
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/reports'}
          >
            <TrendingUp className="h-5 w-5" />
            View Reports
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="active">Active Projects</option>
              <option value="completed">Completed Projects</option>
              <option value="transferred">Transferred Projects</option>
              <option value="all">All Projects</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects, clients, or designers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Project Assignments */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Project Assignments</h2>
          <p className="text-gray-600 mt-1">Manage and oversee assigned projects</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredAssignments.length === 0 ? (
            <div className="p-8 text-center">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No project assignments found</p>
            </div>
          ) : (
            filteredAssignments.map((assignment) => (
              <div key={assignment.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(assignment.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {assignment.project?.title || 'Untitled Project'}
                      </h3>
                      <span className={getStatusBadge(assignment.status)}>
                        {assignment.status.toUpperCase()}
                      </span>
                      <span className={getPriorityBadge(assignment.priority)}>
                        {assignment.priority.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Client:</span> {assignment.project?.client?.full_name}
                      </div>
                      <div>
                        <span className="font-medium">Designer:</span> {assignment.project?.designer?.full_name}
                      </div>
                      <div>
                        <span className="font-medium">Budget:</span> ${assignment.project?.budget?.toLocaleString()}
                      </div>
                    </div>

                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Assigned: {new Date(assignment.assigned_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/projects/${assignment.project_id}`}
                    >
                      <Eye className="h-4 w-4" />
                      Manage
                    </Button>
                    
                    {assignment.status === 'active' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/manager/projects/${assignment.project_id}/coordinate`}
                      >
                        <Settings className="h-4 w-4" />
                        Coordinate
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
