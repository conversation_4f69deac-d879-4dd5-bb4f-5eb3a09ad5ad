"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  User,
  DollarSign,
  Calendar,
  FileText,
  MessageSquare,
  AlertCircle
} from "lucide-react";

interface ProposalMilestone {
  id: string;
  title: string;
  description: string;
  amount: number;
  percentage: number;
  estimated_days: number;
  deliverables: string[];
}

interface Proposal {
  id: string;
  title: string;
  description: string;
  scope: string;
  timeline: string;
  total_budget: number;
  status: string;
  created_at: string;
  reviewed_at?: string;
  admin_feedback?: string;
  project_id: string;
  designer_id: string;
  project: {
    title: string;
    description: string;
    client_id: string;
    profiles: {
      full_name: string;
      email: string;
    };
  };
  profiles: {
    full_name: string;
    email: string;
    avatar_url?: string;
  };
  proposal_milestones: ProposalMilestone[];
}

export default function AdminProposalDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user, profile } = useOptimizedAuth();
  const proposalId = params.id as string;
  
  const [proposal, setProposal] = useState<Proposal | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');

  useEffect(() => {
    if (user && profile?.role === 'admin') {
      fetchProposal();
    }
  }, [user, profile, proposalId]);

  const fetchProposal = async () => {
    try {
      const { data, error } = await supabase
        .from('project_proposals')
        .select(`
          id,
          title,
          description,
          scope,
          timeline,
          total_budget,
          status,
          created_at,
          reviewed_at,
          admin_feedback,
          project_id,
          designer_id,
          projects:project_id (
            title,
            description,
            client_id,
            profiles:client_id (
              full_name,
              email
            )
          ),
          profiles:designer_id (
            full_name,
            email,
            avatar_url
          ),
          proposal_milestones (
            id,
            title,
            description,
            amount,
            percentage,
            estimated_days,
            deliverables
          )
        `)
        .eq('id', proposalId)
        .single();

      if (error) throw error;

      setProposal(data);
    } catch (error) {
      console.error('Error fetching proposal:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProposalAction = async () => {
    if (!proposal) return;
    
    setProcessing(true);
    
    try {
      // Update proposal status
      const { error: updateError } = await supabase
        .from('project_proposals')
        .update({
          status: actionType === 'approve' ? 'approved' : 'rejected',
          reviewed_at: new Date().toISOString(),
          reviewed_by: user?.id,
          admin_feedback: feedback || null
        })
        .eq('id', proposalId);

      if (updateError) throw updateError;

      if (actionType === 'approve') {
        // Create notification for client
        const { error: clientNotificationError } = await supabase
          .from('notifications')
          .insert({
            user_id: proposal.project.profiles.client_id,
            type: 'proposal',
            title: 'Proposal Approved',
            content: `Your project proposal for "${proposal.project.title}" has been approved and is ready for review`,
            related_id: proposalId,
            read: false
          });

        if (clientNotificationError) console.error('Error creating client notification:', clientNotificationError);
      }

      // Create notification for designer
      const { error: designerNotificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: proposal.designer_id,
          type: 'proposal',
          title: `Proposal ${actionType === 'approve' ? 'Approved' : 'Rejected'}`,
          content: `Your proposal for "${proposal.project.title}" has been ${actionType}d by admin`,
          related_id: proposalId,
          read: false
        });

      if (designerNotificationError) console.error('Error creating designer notification:', designerNotificationError);

      // Redirect back to proposals list
      router.push('/admin/proposals');
    } catch (error) {
      console.error(`Error ${actionType}ing proposal:`, error);
    } finally {
      setProcessing(false);
      setShowFeedbackModal(false);
    }
  };

  const openFeedbackModal = (action: 'approve' | 'reject') => {
    setActionType(action);
    setShowFeedbackModal(true);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
        </div>
      </div>
    );
  }

  if (!proposal) {
    return (
      <div className="container mx-auto py-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Proposal Not Found</h1>
          <Link href="/admin/proposals" className="text-brown-600 hover:text-brown-700">
            Back to Proposals
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link href="/admin/proposals" className="flex items-center text-gray-600 hover:text-gray-800 mr-4">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Proposals
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900">{proposal.title}</h1>
            <p className="text-gray-600">Proposal Review</p>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            proposal.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
            proposal.status === 'approved' ? 'bg-green-100 text-green-800' :
            'bg-red-100 text-red-800'
          }`}>
            {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Project Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Information</h2>
            <div className="space-y-3">
              <div>
                <h3 className="font-medium text-gray-900">{proposal.project.title}</h3>
                <p className="text-gray-600">{proposal.project.description}</p>
              </div>
              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div>
                  <span className="text-sm font-medium text-gray-500">Client</span>
                  <p className="text-gray-900">{proposal.project.profiles.full_name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-500">Designer</span>
                  <p className="text-gray-900">{proposal.profiles.full_name}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Proposal Details */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Proposal Details</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Description</h3>
                <p className="text-gray-700">{proposal.description}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Scope of Work</h3>
                <p className="text-gray-700 whitespace-pre-wrap">{proposal.scope}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Timeline</h3>
                  <p className="text-gray-700">{proposal.timeline}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Total Budget</h3>
                  <p className="text-2xl font-bold text-green-600">${proposal.total_budget?.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Milestones */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Milestones</h2>
            <div className="space-y-4">
              {proposal.proposal_milestones.map((milestone, index) => (
                <div key={milestone.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-gray-900">{milestone.title}</h3>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900">${milestone.amount.toLocaleString()}</div>
                      <div className="text-sm text-gray-500">{milestone.percentage}%</div>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-2">{milestone.description}</p>
                  <div className="text-sm text-gray-500">
                    Estimated: {milestone.estimated_days} days
                  </div>
                  {milestone.deliverables && milestone.deliverables.length > 0 && (
                    <div className="mt-2">
                      <span className="text-sm font-medium text-gray-700">Deliverables:</span>
                      <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                        {milestone.deliverables.map((deliverable, idx) => (
                          <li key={idx}>{deliverable}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions */}
          {proposal.status === 'pending' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Actions</h3>
              <div className="space-y-3">
                <button
                  onClick={() => openFeedbackModal('approve')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Proposal
                </button>
                <button
                  onClick={() => openFeedbackModal('reject')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Proposal
                </button>
              </div>
            </div>
          )}

          {/* Proposal Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Proposal Information</h3>
            <div className="space-y-3 text-sm">
              <div>
                <span className="font-medium text-gray-500">Submitted:</span>
                <p className="text-gray-900">{new Date(proposal.created_at).toLocaleDateString()}</p>
              </div>
              {proposal.reviewed_at && (
                <div>
                  <span className="font-medium text-gray-500">Reviewed:</span>
                  <p className="text-gray-900">{new Date(proposal.reviewed_at).toLocaleDateString()}</p>
                </div>
              )}
              {proposal.admin_feedback && (
                <div>
                  <span className="font-medium text-gray-500">Admin Feedback:</span>
                  <p className="text-gray-900 mt-1">{proposal.admin_feedback}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {actionType === 'approve' ? 'Approve Proposal' : 'Reject Proposal'}
              </h3>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Feedback {actionType === 'reject' ? '(Required)' : '(Optional)'}
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  rows={4}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-brown-500"
                  placeholder={`Provide feedback for the ${actionType === 'approve' ? 'approval' : 'rejection'}...`}
                />
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={handleProposalAction}
                  disabled={processing || (actionType === 'reject' && !feedback.trim())}
                  className={`flex-1 px-4 py-2 rounded-md text-white font-medium ${
                    actionType === 'approve' 
                      ? 'bg-green-600 hover:bg-green-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  } disabled:opacity-50`}
                >
                  {processing ? 'Processing...' : `${actionType === 'approve' ? 'Approve' : 'Reject'}`}
                </button>
                <button
                  onClick={() => setShowFeedbackModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
