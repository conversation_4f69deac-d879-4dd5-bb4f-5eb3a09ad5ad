import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

interface Milestone {
  id: string
  project_id: string
  title: string
  description: string
  amount: number
  status: string
  projects: {
    id: string
    title: string
    client_id: string
  }
}

serve(async (req) => {
  try {
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the service role for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the JSON request body
    const { milestone_id } = await req.json()

    if (!milestone_id) {
      return new Response(
        JSON.stringify({ error: 'milestone_id is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Get the milestone data
    const { data: milestone, error: milestoneError } = await supabaseAdmin
      .from('project_milestones')
      .select(`
        id,
        project_id,
        title,
        description,
        amount,
        status,
        projects(
          id,
          title,
          client_id
        )
      `)
      .eq('id', milestone_id)
      .single()

    if (milestoneError || !milestone) {
      return new Response(
        JSON.stringify({ error: 'Milestone not found', details: milestoneError }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Generate invoice number
    const generateInvoiceNumber = () => {
      const prefix = 'INV'
      const randomDigits = Math.floor(10000 + Math.random() * 90000) // 5-digit random number
      const timestamp = Date.now().toString().slice(-4) // Last 4 digits of timestamp
      return `${prefix}-${timestamp}${randomDigits}`
    }

    // Create the invoice
    const now = new Date()
    const dueDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days from now

    const invoiceData = {
      invoice_number: generateInvoiceNumber(),
      amount: milestone.amount,
      status: 'pending',
      due_date: dueDate.toISOString(),
      issued_date: now.toISOString(),
      description: `Payment for milestone: ${milestone.title} (${milestone.projects?.title})`,
      project_id: milestone.project_id,
      client_id: milestone.projects?.client_id
    }

    // Insert the invoice
    const { data: invoice, error: invoiceError } = await supabaseAdmin
      .from('invoices')
      .insert(invoiceData)
      .select()
      .single()

    if (invoiceError) {
      return new Response(
        JSON.stringify({ error: 'Failed to create invoice', details: invoiceError }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Invoice created successfully', 
        invoice 
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})
