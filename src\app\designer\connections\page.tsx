"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Users,
  MessageSquare,
  Calendar,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Star,
  Eye,
  Search,
  Filter,
  UserPlus,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react";

interface Connection {
  id: string;
  client_id: string;
  designer_id: string;
  status: 'active' | 'inactive';
  created_at: string;
  created_by: string;
  invitation_id: string | null;
  client: {
    id: string;
    full_name: string;
    email: string;
    avatar_url: string | null;
    phone: string | null;
    location: string | null;
  };
  last_project_date?: string;
  active_projects_count?: number;
  total_projects_count?: number;
  last_message_date?: string;
}

export default function DesignerConnections() {
  const { user } = useOptimizedAuth();
  const [connections, setConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    if (user) {
      fetchConnections();
    }
  }, [user]);

  const fetchConnections = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('connections')
        .select(`
          id,
          client_id,
          designer_id,
          status,
          created_at,
          created_by,
          invitation_id,
          profiles!client_id(
            id,
            full_name,
            email,
            avatar_url,
            phone,
            location
          )
        `)
        .eq('designer_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform the data and add additional stats
      const connectionsWithStats = await Promise.all(
        (data || []).map(async (connection) => {
          const client = Array.isArray(connection.profiles) 
            ? connection.profiles[0] 
            : connection.profiles;

          // Fetch project stats for this client
          const { data: projectsData } = await supabase
            .from('projects')
            .select('id, status, created_at')
            .eq('client_id', connection.client_id)
            .eq('designer_id', user.id);

          const activeProjects = projectsData?.filter(p => 
            ['assigned', 'in_progress'].includes(p.status)
          ).length || 0;

          const totalProjects = projectsData?.length || 0;
          
          const lastProjectDate = projectsData && projectsData.length > 0
            ? projectsData.sort((a, b) => 
                new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
              )[0].created_at
            : null;

          return {
            id: connection.id,
            client_id: connection.client_id,
            designer_id: connection.designer_id,
            status: connection.status,
            created_at: connection.created_at,
            created_by: connection.created_by,
            invitation_id: connection.invitation_id,
            client: {
              id: client?.id || '',
              full_name: client?.full_name || 'Unknown Client',
              email: client?.email || '',
              avatar_url: client?.avatar_url || null,
              phone: client?.phone || null,
              location: client?.location || null,
            },
            active_projects_count: activeProjects,
            total_projects_count: totalProjects,
            last_project_date: lastProjectDate
          };
        })
      );

      setConnections(connectionsWithStats);
    } catch (error) {
      console.error('Error fetching connections:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  const filteredConnections = connections.filter(connection => {
    const matchesSearch = connection.client.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         connection.client.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || connection.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Connections</h1>
          <p className="text-gray-600">Manage your client connections and relationships</p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500">
            {filteredConnections.length} connection{filteredConnections.length !== 1 ? 's' : ''}
          </span>
          <Link href="/designer/dashboard">
            <Button variant="outline">
              <UserPlus className="h-4 w-4 mr-2" />
              Invite New Client
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search connections..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>

      {/* Connections List */}
      {filteredConnections.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {connections.length === 0 ? 'No connections yet' : 'No connections match your filters'}
          </h3>
          <p className="text-gray-500 mb-4">
            {connections.length === 0
              ? 'Start building relationships by inviting clients to connect with you'
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {connections.length === 0 && (
            <Link href="/designer/dashboard">
              <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Your First Client
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredConnections.map((connection) => (
            <motion.div
              key={connection.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  {/* Avatar */}
                  <div className="flex-shrink-0">
                    {connection.client.avatar_url ? (
                      <img
                        src={connection.client.avatar_url}
                        alt={connection.client.full_name}
                        className="h-12 w-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                        <User className="h-6 w-6 text-gray-500" />
                      </div>
                    )}
                  </div>

                  {/* Client Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {connection.client.full_name}
                      </h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(connection.status)}`}>
                        {connection.status.charAt(0).toUpperCase() + connection.status.slice(1)}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <Mail className="h-4 w-4 mr-2" />
                        {connection.client.email}
                      </div>
                      {connection.client.phone && (
                        <div className="flex items-center text-sm text-gray-500">
                          <Phone className="h-4 w-4 mr-2" />
                          {connection.client.phone}
                        </div>
                      )}
                      {connection.client.location && (
                        <div className="flex items-center text-sm text-gray-500">
                          <MapPin className="h-4 w-4 mr-2" />
                          {connection.client.location}
                        </div>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center text-sm">
                        <Briefcase className="h-4 w-4 mr-2 text-blue-500" />
                        <span className="font-medium text-blue-900">
                          {connection.active_projects_count} Active
                        </span>
                        <span className="text-gray-500 ml-1">
                          / {connection.total_projects_count} Total Projects
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-2" />
                        Connected {getTimeAgo(connection.created_at)}
                      </div>
                      {connection.last_project_date && (
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-2" />
                          Last project {getTimeAgo(connection.last_project_date)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 flex-shrink-0">
                  <Link href={`/designer/messages?client=${connection.client_id}`}>
                    <Button variant="outline" size="sm">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Message
                    </Button>
                  </Link>
                  <Link href={`/designer/projects?client=${connection.client_id}`}>
                    <Button size="sm" className="bg-brown-600 hover:bg-brown-700 text-white">
                      <Eye className="h-4 w-4 mr-2" />
                      View Projects
                    </Button>
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
