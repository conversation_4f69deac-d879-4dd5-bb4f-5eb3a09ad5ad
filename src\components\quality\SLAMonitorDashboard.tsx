'use client';

import React, { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  RefreshCw,
  Bell,
  Target,
  BarChart3,
  Calendar,
  User
} from 'lucide-react';
import { SLAMonitor, SLAStatus } from '@/lib/sla-monitor';

interface SLADashboardData {
  totalReviews: number;
  onTime: number;
  approaching: number;
  overdue: number;
  critical: number;
  averageCompletionTime: number;
}

interface SLAMonitorDashboardProps {
  role: 'admin' | 'quality' | 'manager';
  compact?: boolean;
}

export default function SLAMonitorDashboard({ role, compact = false }: SLAMonitorDashboardProps) {
  const { user } = useOptimizedAuth();
  const [dashboardData, setDashboardData] = useState<SLADashboardData>({
    totalReviews: 0,
    onTime: 0,
    approaching: 0,
    overdue: 0,
    critical: 0,
    averageCompletionTime: 0
  });
  const [slaStatuses, setSlaStatuses] = useState<SLAStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    if (user) {
      fetchSLAData();
      
      // Set up periodic refresh every 5 minutes
      const interval = setInterval(fetchSLAData, 5 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [user]);

  const fetchSLAData = async () => {
    try {
      setLoading(true);
      
      // Get dashboard data
      const dashData = await SLAMonitor.getSLADashboardData();
      setDashboardData(dashData);

      // Get detailed SLA statuses
      const statuses = await SLAMonitor.checkAllSLAs();
      setSlaStatuses(statuses);
      
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching SLA data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSLAPercentage = () => {
    if (dashboardData.totalReviews === 0) return 100;
    return Math.round(((dashboardData.onTime + dashboardData.approaching) / dashboardData.totalReviews) * 100);
  };

  const getStatusColor = (status: SLAStatus['status']) => {
    switch (status) {
      case 'on_time':
        return 'bg-green-100 text-green-800';
      case 'approaching':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-orange-100 text-orange-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: SLAStatus['status']) => {
    switch (status) {
      case 'on_time':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'approaching':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTimeRemaining = (hours: number) => {
    if (hours < 0) {
      return `${Math.abs(Math.ceil(hours))}h overdue`;
    } else if (hours < 24) {
      return `${Math.ceil(hours)}h remaining`;
    } else {
      return `${Math.ceil(hours / 24)}d remaining`;
    }
  };

  if (compact) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-500" />
              SLA Monitor
            </CardTitle>
            <Badge className={getSLAPercentage() >= 90 ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}>
              {getSLAPercentage()}% On Time
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{dashboardData.onTime}</div>
              <div className="text-sm text-gray-600">On Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{dashboardData.overdue + dashboardData.critical}</div>
              <div className="text-sm text-gray-600">Overdue</div>
            </div>
          </div>
          
          {(dashboardData.overdue > 0 || dashboardData.critical > 0) && (
            <div className="mt-4 p-3 bg-red-50 rounded-md">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium text-red-800">
                  {dashboardData.critical > 0 ? 'Critical reviews overdue!' : 'Reviews need attention'}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Target className="h-6 w-6 text-blue-500" />
          <h2 className="text-xl font-semibold text-gray-900">SLA Monitor</h2>
        </div>
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchSLAData}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* SLA Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.totalReviews}</p>
              </div>
              <BarChart3 className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">On Time</p>
                <p className="text-2xl font-bold text-green-600">{dashboardData.onTime}</p>
              </div>
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Approaching</p>
                <p className="text-2xl font-bold text-yellow-600">{dashboardData.approaching}</p>
              </div>
              <Clock className="h-6 w-6 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-orange-600">{dashboardData.overdue}</p>
              </div>
              <AlertTriangle className="h-6 w-6 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Critical</p>
                <p className="text-2xl font-bold text-red-600">{dashboardData.critical}</p>
              </div>
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SLA Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-500" />
            SLA Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-3xl font-bold text-gray-900">{getSLAPercentage()}%</div>
              <div className="text-sm text-gray-600">Reviews completed on time</div>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">
                {dashboardData.averageCompletionTime}h
              </div>
              <div className="text-sm text-gray-600">Average completion time</div>
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full ${getSLAPercentage() >= 90 ? 'bg-green-500' : getSLAPercentage() >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
              style={{ width: `${getSLAPercentage()}%` }}
            ></div>
          </div>
        </CardContent>
      </Card>

      {/* Active Reviews */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-orange-500" />
            Active Reviews Requiring Attention
          </CardTitle>
        </CardHeader>
        <CardContent>
          {slaStatuses.filter(s => s.status !== 'on_time').length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-300 mx-auto mb-4" />
              <h4 className="font-medium text-gray-900 mb-2">All Reviews On Track</h4>
              <p className="text-sm text-gray-600">No reviews require immediate attention</p>
            </div>
          ) : (
            <div className="space-y-3">
              {slaStatuses
                .filter(s => s.status !== 'on_time')
                .sort((a, b) => a.hoursRemaining - b.hoursRemaining)
                .map((status) => (
                  <div key={status.reviewId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(status.status)}
                      <div>
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(status.status)}>
                            {status.status.replace('_', ' ')}
                          </Badge>
                          <span className="text-sm font-medium text-gray-900">
                            Review #{status.reviewId.slice(-8)}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatTimeRemaining(status.hoursRemaining)}
                          </span>
                          {status.reviewerId && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              Assigned
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`/quality/reviews/${status.reviewId}`, '_blank')}
                    >
                      View Review
                    </Button>
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
