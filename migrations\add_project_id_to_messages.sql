-- Add project_id to messages table
DO $$
BEGIN
    -- Check if the column exists before adding it
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'messages' AND column_name = 'project_id'
    ) THEN
        -- Add project_id column
        ALTER TABLE messages ADD COLUMN project_id UUID REFERENCES projects(id) ON DELETE CASCADE;
        
        -- Create index for better performance
        CREATE INDEX IF NOT EXISTS idx_messages_project_id ON messages(project_id);
        
        -- Log the change
        RAISE NOTICE 'Added project_id column to messages table';
    ELSE
        RAISE NOTICE 'project_id column already exists in messages table';
    END IF;
END
$$;
