"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import {
  AlertCircle,
  CheckCircle,
  Loader2,
  MessageSquare,
  Edit,
  DollarSign,
  Clock,
  FileText,
} from "lucide-react";
import { motion } from "framer-motion";

type RequestChangesFormProps = {
  proposalId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function RequestChangesForm({
  proposalId,
  onSuccess,
  onCancel,
}: RequestChangesFormProps) {
  const { user } = useOptimizedAuth();
  const [section, setSection] = useState("scope");
  const [description, setDescription] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) return;

    if (!description.trim()) {
      setError("Please provide details about the requested changes");
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      // 1. Create the change request
      const { error: requestError } = await supabase
        .from("proposal_change_requests")
        .insert({
          enhanced_proposal_id: proposalId,
          requested_by: user.id,
          section,
          description,
          status: "pending",
        });

      if (requestError) throw requestError;

      // 2. Update the proposal status to indicate revision requested
      // Try enhanced proposal first
      const { error: enhancedProposalError } = await supabase
        .from("project_proposals_enhanced")
        .update({
          status: "under_review",
        })
        .eq("id", proposalId);

      // If that fails, try regular proposal
      if (enhancedProposalError) {
        const { error: proposalError } = await supabase
          .from("project_proposals")
          .update({
            status: "revision_requested",
            revision_requested_at: new Date().toISOString(),
            revision_requested_by: user.id,
          })
          .eq("id", proposalId);

        if (proposalError) throw proposalError;
      }

      // 3. Get the designer ID to send notification
      const { data: proposalData, error: fetchError } = await supabase
        .from("project_proposals")
        .select("designer_id, project_id, title")
        .eq("id", proposalId)
        .single();

      if (fetchError) throw fetchError;

      // 4. Create notification for designer
      const { error: notificationError } = await supabase
        .from("notifications")
        .insert({
          user_id: proposalData.designer_id,
          type: "proposal",
          title: "Proposal Revision Requested",
          content: `Changes have been requested for your proposal. Please review and update.`,
          related_id: proposalId,
          read: false,
        });

      if (notificationError) throw notificationError;

      // 5. Add a comment to the proposal
      const { error: commentError } = await supabase
        .from("proposal_comments")
        .insert({
          proposal_id: proposalId,
          user_id: user.id,
          content: `Requested changes to ${getSectionName(section)}: ${description}`,
        });

      if (commentError) throw commentError;

      setSuccess(true);
      
      // Call onSuccess callback after a delay
      setTimeout(() => {
        if (onSuccess) onSuccess();
      }, 2000);
    } catch (error) {
      console.error("Error requesting changes:", error);
      setError("Failed to submit change request. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const getSectionName = (sectionKey: string): string => {
    const sections: Record<string, string> = {
      scope: "Scope of Work",
      timeline: "Project Timeline",
      budget: "Budget",
      milestones: "Payment Milestones",
      other: "Other Details",
    };
    return sections[sectionKey] || sectionKey;
  };

  const getSectionIcon = (sectionKey: string) => {
    switch (sectionKey) {
      case "scope":
        return <FileText className="h-5 w-5" />;
      case "timeline":
        return <Clock className="h-5 w-5" />;
      case "budget":
        return <DollarSign className="h-5 w-5" />;
      case "milestones":
        return <Edit className="h-5 w-5" />;
      default:
        return <MessageSquare className="h-5 w-5" />;
    }
  };

  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white p-6 rounded-lg border border-gray-200"
      >
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Changes Requested</h3>
          <p className="text-gray-600 mb-4">
            Your request has been sent to the designer. They will review your
            feedback and update the proposal accordingly.
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200">
      <h2 className="text-lg font-semibold mb-4">Request Changes</h2>
      
      {error && (
        <div className="bg-red-50 border border-red-200 p-3 rounded-md mb-4 flex items-start">
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Section to Change
          </label>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            {["scope", "timeline", "budget", "milestones", "other"].map((s) => (
              <button
                key={s}
                type="button"
                onClick={() => setSection(s)}
                className={`flex flex-col items-center justify-center p-3 border rounded-md transition-colors ${
                  section === s
                    ? "border-brown-500 bg-brown-50 text-brown-700"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                {getSectionIcon(s)}
                <span className="text-xs mt-1">{getSectionName(s)}</span>
              </button>
            ))}
          </div>
        </div>
        
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Describe the Changes Needed
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={4}
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
            placeholder={`Please describe what changes you'd like to see in the ${getSectionName(section)}...`}
          />
        </div>
        
        <div className="flex justify-end space-x-3 pt-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={submitting}
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={submitting}
            className="bg-brown-600 hover:bg-brown-700 text-white"
          >
            {submitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              "Submit Request"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
