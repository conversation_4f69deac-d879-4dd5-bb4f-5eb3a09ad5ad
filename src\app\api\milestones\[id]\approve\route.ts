import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/milestones/[id]/approve
 * Approves a milestone and triggers invoice creation
 * 
 * Request body:
 * {
 *   userId: string; // ID of the user approving the milestone (admin or client)
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const milestoneId = params.id;
    const { userId } = await request.json();
    
    if (!milestoneId) {
      return NextResponse.json(
        { error: 'Milestone ID is required' },
        { status: 400 }
      );
    }
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Get the current milestone
    const { data: milestone, error: milestoneError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        status,
        project_id,
        projects (
          id,
          title,
          client_id,
          designer_id
        )
      `)
      .eq('id', milestoneId)
      .single();
    
    if (milestoneError) {
      return NextResponse.json(
        { error: 'Failed to fetch milestone' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to approve this milestone
    const isAdmin = profile.role === 'admin';
    const isClient = profile.role === 'client' && userId === milestone.projects.client_id;
    
    if (!isAdmin && !isClient) {
      return NextResponse.json(
        { error: 'You do not have permission to approve this milestone' },
        { status: 403 }
      );
    }
    
    // Check if the milestone is in a state that can be approved
    if (milestone.status !== 'completed') {
      return NextResponse.json(
        { error: 'Only completed milestones can be approved' },
        { status: 400 }
      );
    }
    
    // Update the milestone status
    const { data, error } = await supabase
      .from('project_milestones')
      .update({
        status: 'approved',
        approved_at: new Date().toISOString(),
        approved_by: userId
      })
      .eq('id', milestoneId)
      .select()
      .single();
    
    if (error) {
      console.error('Error approving milestone:', error);
      return NextResponse.json(
        { error: 'Failed to approve milestone' },
        { status: 500 }
      );
    }
    
    // The invoice will be created automatically by the database trigger
    // defined in supabase_triggers.sql
    
    return NextResponse.json({ 
      milestone: data,
      message: 'Milestone approved successfully. An invoice has been generated.'
    }, { status: 200 });
  } catch (error) {
    console.error('Error approving milestone:', error);
    return NextResponse.json(
      { error: 'Failed to approve milestone' },
      { status: 500 }
    );
  }
}
