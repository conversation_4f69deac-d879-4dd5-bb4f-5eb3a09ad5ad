"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Search,
  Download,
  CreditCard,
  DollarSign,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  XCircle,
  FileText,
  BarChart3,
  Clock,
  Settings
} from "lucide-react";

type Transaction = {
  id: string;
  transaction_id: string;
  amount: number;
  status: string;
  type: string;
  created_at: string;
  processed_at: string | null;
  notes: string | null;
  project_id: string | null;
  project_title: string | null;
  milestone_id: string | null;
  milestone_title: string | null;
  client_id: string | null;
  client_name: string | null;
  designer_id: string | null;
  designer_name: string | null;
};

type FinancialSummary = {
  total_revenue: number;
  pending_payments: number;
  completed_payments: number;
  failed_payments: number;
};

export default function FinancialManagement() {
  const { user } = useOptimizedAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const transactionsPerPage = 10;

  // Financial summary
  const [financialSummary, setFinancialSummary] = useState<FinancialSummary>({
    total_revenue: 0,
    pending_payments: 0,
    completed_payments: 0,
    failed_payments: 0
  });

  useEffect(() => {
    if (user) {
      fetchTransactions();
    }
  }, [user]);

  useEffect(() => {
    // Apply filters
    let result = transactions;

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        transaction =>
          transaction.transaction_id.toLowerCase().includes(term) ||
          (transaction.client_name && transaction.client_name.toLowerCase().includes(term)) ||
          (transaction.designer_name && transaction.designer_name.toLowerCase().includes(term)) ||
          (transaction.project_title && transaction.project_title.toLowerCase().includes(term))
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      result = result.filter(transaction => transaction.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== "all") {
      result = result.filter(transaction => transaction.type === typeFilter);
    }

    setFilteredTransactions(result);
    setTotalPages(Math.ceil(result.length / transactionsPerPage));
    setCurrentPage(1); // Reset to first page when filters change
  }, [transactions, searchTerm, statusFilter, typeFilter]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      // Fetch transactions with the correct columns from your schema
      const { data: transactionsData, error: transactionsError } = await supabase
        .from('transactions')
        .select(`
          id,
          transaction_id,
          amount,
          status,
          type,
          created_at,
          processed_at,
          notes,
          project_id,
          milestone_id,
          client_id,
          designer_id
        `)
        .order('created_at', { ascending: false });

      if (transactionsError) throw transactionsError;

      // Get all unique IDs for related data
      const projectIds = [...new Set(transactionsData
        .filter(t => t.project_id)
        .map(t => t.project_id))];

      const milestoneIds = [...new Set(transactionsData
        .filter(t => t.milestone_id)
        .map(t => t.milestone_id))];

      const clientIds = [...new Set(transactionsData
        .filter(t => t.client_id)
        .map(t => t.client_id))];

      const designerIds = [...new Set(transactionsData
        .filter(t => t.designer_id)
        .map(t => t.designer_id))];

      // Fetch related data in parallel
      const [projectsResponse, milestonesResponse, clientsResponse, designersResponse] = await Promise.all([
        projectIds.length > 0
          ? supabase.from('projects').select('id, title').in('id', projectIds)
          : { data: [], error: null },

        milestoneIds.length > 0
          ? supabase.from('project_milestones').select('id, title').in('id', milestoneIds)
          : { data: [], error: null },

        clientIds.length > 0
          ? supabase.from('profiles').select('id, full_name').in('id', clientIds)
          : { data: [], error: null },

        designerIds.length > 0
          ? supabase.from('profiles').select('id, full_name').in('id', designerIds)
          : { data: [], error: null }
      ]);

      if (projectsResponse.error) throw projectsResponse.error;
      if (milestonesResponse.error) throw milestonesResponse.error;
      if (clientsResponse.error) throw clientsResponse.error;
      if (designersResponse.error) throw designersResponse.error;

      // Create lookup maps
      const projectMap: Record<string, string> = {};
      (projectsResponse.data || []).forEach(project => {
        projectMap[project.id] = project.title;
      });

      const milestoneMap: Record<string, string> = {};
      (milestonesResponse.data || []).forEach(milestone => {
        milestoneMap[milestone.id] = milestone.title;
      });

      const clientMap: Record<string, string> = {};
      (clientsResponse.data || []).forEach(client => {
        clientMap[client.id] = client.full_name;
      });

      const designerMap: Record<string, string> = {};
      (designersResponse.data || []).forEach(designer => {
        designerMap[designer.id] = designer.full_name;
      });

      // Format transactions with related data
      const formattedTransactions: Transaction[] = transactionsData.map(transaction => ({
        id: transaction.id,
        transaction_id: transaction.transaction_id,
        amount: transaction.amount,
        status: transaction.status,
        type: transaction.type,
        created_at: transaction.created_at,
        processed_at: transaction.processed_at,
        notes: transaction.notes,
        project_id: transaction.project_id,
        project_title: transaction.project_id ? projectMap[transaction.project_id] || null : null,
        milestone_id: transaction.milestone_id,
        milestone_title: transaction.milestone_id ? milestoneMap[transaction.milestone_id] || null : null,
        client_id: transaction.client_id,
        client_name: transaction.client_id ? clientMap[transaction.client_id] || null : null,
        designer_id: transaction.designer_id,
        designer_name: transaction.designer_id ? designerMap[transaction.designer_id] || null : null
      }));

      setTransactions(formattedTransactions);
      setFilteredTransactions(formattedTransactions);
      setTotalPages(Math.ceil(formattedTransactions.length / transactionsPerPage));

      // Calculate financial summary
      const totalRevenue = formattedTransactions
        .filter(t => t.type === 'payment' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      const pendingPayments = formattedTransactions
        .filter(t => t.status === 'pending')
        .reduce((sum, t) => sum + t.amount, 0);

      const completedPayments = formattedTransactions
        .filter(t => t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      const failedPayments = formattedTransactions
        .filter(t => t.status === 'failed')
        .reduce((sum, t) => sum + t.amount, 0);

      setFinancialSummary({
        total_revenue: totalRevenue,
        pending_payments: pendingPayments,
        completed_payments: completedPayments,
        failed_payments: failedPayments
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching transactions:', error);
        setError(error.message || 'Failed to load transaction data');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not processed';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getCurrentPageTransactions = () => {
    const startIndex = (currentPage - 1) * transactionsPerPage;
    const endIndex = startIndex + transactionsPerPage;
    return filteredTransactions.slice(startIndex, endIndex);
  };

  const exportTransactionData = () => {
    const csvData = [
      ['Transaction ID', 'Amount', 'Status', 'Type', 'Created Date', 'Processed Date', 'Project', 'Milestone', 'Client', 'Designer', 'Notes'],
      ...filteredTransactions.map(transaction => [
        transaction.transaction_id,
        transaction.amount.toString(),
        transaction.status,
        transaction.type,
        formatDate(transaction.created_at),
        formatDate(transaction.processed_at),
        transaction.project_title || 'N/A',
        transaction.milestone_title || 'N/A',
        transaction.client_name || 'N/A',
        transaction.designer_name || 'N/A',
        transaction.notes || ''
      ])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', 'transactions_export.csv');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment':
        return 'bg-brown-100 text-brown-800';
      case 'payout':
        return 'bg-brown-50 text-brown-800';
      case 'refund':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && transactions.length === 0) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading financial data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold mb-2">Financial Management</h1>
          <p className="text-gray-500">Manage payments, transactions, and financial reports</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <Link href="/admin/finance/reports">
            <Button className="flex items-center">
              <BarChart3 className="h-4 w-4 mr-2" />
              Financial Reports
            </Button>
          </Link>
        </div>
      </div>

      {/* Financial Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-gray-500 font-medium">Total Revenue</h3>
            <DollarSign className="h-6 w-6 text-green-500" />
          </div>
          <p className="text-3xl font-bold">{formatCurrency(financialSummary.total_revenue)}</p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-gray-500 font-medium">Pending Payments</h3>
            <Clock className="h-6 w-6 text-yellow-500" />
          </div>
          <p className="text-3xl font-bold">{formatCurrency(financialSummary.pending_payments)}</p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-gray-500 font-medium">Completed Payments</h3>
            <CheckCircle className="h-6 w-6 text-green-500" />
          </div>
          <p className="text-3xl font-bold">{formatCurrency(financialSummary.completed_payments)}</p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-gray-500 font-medium">Failed Payments</h3>
            <XCircle className="h-6 w-6 text-red-500" />
          </div>
          <p className="text-3xl font-bold">{formatCurrency(financialSummary.failed_payments)}</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <input
                type="text"
                id="search"
                placeholder="Search transactions"
                className="w-full px-4 py-2 border rounded-md pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              className="w-full px-4 py-2 border rounded-md"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
            </select>
          </div>

          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              id="type"
              className="w-full px-4 py-2 border rounded-md"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="all">All Types</option>
              <option value="payment">Payment</option>
              <option value="payout">Payout</option>
              <option value="refund">Refund</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button variant="outline" onClick={exportTransactionData} className="flex items-center">
              <Download className="h-4 w-4 mr-2" />
              Export Transactions
            </Button>
          </div>
        </div>

        <div className="mt-4">
          <p className="text-sm text-gray-500">
            Showing {filteredTransactions.length} transactions
          </p>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Transaction ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Project
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Parties
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {getCurrentPageTransactions().map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {transaction.transaction_id}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(transaction.amount)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                      {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor(transaction.type)}`}>
                      {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(transaction.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {transaction.project_title || 'N/A'}
                    </div>
                    {transaction.milestone_title && (
                      <div className="text-xs text-gray-500">
                        {transaction.milestone_title}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {transaction.client_name && (
                      <div className="text-sm text-gray-900">
                        Client: {transaction.client_name}
                      </div>
                    )}
                    {transaction.designer_name && (
                      <div className="text-xs text-gray-500">
                        Designer: {transaction.designer_name}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link href={`/admin/finance/transactions/${transaction.id}`}>
                      <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-900">
                        View
                      </Button>
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 flex items-center justify-between border-t">
            <div className="flex-1 flex justify-between sm:hidden">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{(currentPage - 1) * transactionsPerPage + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(currentPage * transactionsPerPage, filteredTransactions.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredTransactions.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeft className="h-5 w-5" />
                  </button>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === currentPage
                          ? 'z-10 bg-primary border-primary text-white'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mt-8">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">Quick Actions</h2>
        </div>
        <div className="p-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link href="/admin/finance/reports">
            <Button className="w-full justify-between">
              Generate Reports
              <FileText className="h-4 w-4 ml-2" />
            </Button>
          </Link>
          <Link href="/admin/finance/payouts">
            <Button variant="outline" className="w-full justify-between">
              Process Payouts
              <CreditCard className="h-4 w-4 ml-2" />
            </Button>
          </Link>
          <Link href="/admin/finance/settings">
            <Button variant="outline" className="w-full justify-between">
              Payment Settings
              <Settings className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

