-- Check what tables currently exist in your database
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check if our new tables exist specifically
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
  'newsletter_subscribers',
  'newsletter_campaigns', 
  'newsletter_campaign_recipients',
  'blog_categories',
  'blog_posts',
  'cookie_consents'
)
ORDER BY table_name;
