"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import { useRef } from "react";

const BrandStorySection = () => {
  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  const y1 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);
  
  return (
    <section 
      ref={sectionRef}
      className="py-24 bg-black text-white relative overflow-hidden"
    >
      {/* Visual connector from previous section */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[1px] h-20 bg-gradient-to-b from-transparent to-white/30" />
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              Led by Vision. Built with Purpose.
            </h2>
            <motion.div 
              className="h-1 w-20 bg-primary mx-auto mb-6"
              initial={{ width: 0 }}
              whileInView={{ width: 80 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            />
          </motion.div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
            <motion.div
              style={{ y: y1, opacity }}
              className="relative"
            >
              <motion.div 
                className="absolute -top-10 -left-10 w-40 h-40 border-2 border-primary opacity-20"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 0.2, scale: 1 }}
                transition={{ duration: 1 }}
                viewport={{ once: true }}
              />
              <motion.img 
                src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                alt="Architectural design process" 
                className="w-full h-auto shadow-2xl"
                initial={{ filter: "grayscale(100%)" }}
                whileInView={{ filter: "grayscale(0%)" }}
                transition={{ duration: 1.5 }}
                viewport={{ once: true }}
              />
              <motion.div 
                className="absolute -bottom-10 -right-10 w-40 h-40 border-2 border-primary opacity-20"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 0.2, scale: 1 }}
                transition={{ duration: 1, delay: 0.3 }}
                viewport={{ once: true }}
              />
            </motion.div>
            
            <motion.div
              style={{ y: y2 }}
              className="space-y-8"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="text-2xl font-bold mb-4 text-primary">Bridging Art and Strategy</h3>
                <p className="text-gray-300 leading-relaxed">
                  At Senior's Archi-firm, we believe that exceptional architecture exists at the intersection of artistic vision and strategic thinking. Every project begins with deep listening and understanding, ensuring that our designs not only inspire but also solve real-world challenges.
                </p>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <h3 className="text-2xl font-bold mb-4 text-primary">Human-Centered Design</h3>
                <p className="text-gray-300 leading-relaxed">
                  We design with people at the center, creating spaces that enhance human experience and foster connection. Our approach balances functionality with beauty, technical excellence with emotional resonance, creating architecture that serves both practical needs and spiritual aspirations.
                </p>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                viewport={{ once: true }}
              >
                <h3 className="text-2xl font-bold mb-4 text-primary">Sustainable Innovation</h3>
                <p className="text-gray-300 leading-relaxed">
                  Our commitment to sustainability goes beyond materials and energy efficiency. We create architecture that stands the test of time—both physically and culturally—by designing spaces that can evolve, adapt, and continue to inspire future generations.
                </p>
              </motion.div>
            </motion.div>
          </div>
          
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            viewport={{ once: true }}
            className="mt-20 text-center"
          >
            <blockquote className="text-2xl md:text-3xl italic font-light text-gray-300 max-w-3xl mx-auto">
              "We don't just build structures; we craft experiences that transform how people live, work, and connect with their environment."
            </blockquote>
            <div className="mt-6">
              <p className="text-primary font-bold">Ahmed Senior</p>
              <p className="text-sm text-gray-400">Founder & Principal Architect</p>
            </div>
          </motion.div>
        </div>
      </div>
      
      {/* Visual connector to next section */}
      <motion.div 
        className="w-[1px] h-20 bg-gradient-to-b from-white/30 to-transparent mx-auto mt-16"
        initial={{ scaleY: 0 }}
        whileInView={{ scaleY: 1 }}
        transition={{ duration: 1, delay: 0.5 }}
        viewport={{ once: true }}
      />
      
      {/* Visual design elements */}
      <motion.div 
        className="absolute top-1/4 right-0 w-64 h-64 bg-primary/5 blur-3xl"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 2 }}
        viewport={{ once: true }}
      />
      
      <motion.div 
        className="absolute bottom-1/4 left-0 w-96 h-96 bg-primary/5 blur-3xl"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 2, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default BrandStorySection;
