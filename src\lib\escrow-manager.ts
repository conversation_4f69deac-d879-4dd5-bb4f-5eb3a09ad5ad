'use client';

import { supabase } from '@/lib/supabase';

/**
 * Escrow Management System
 * Handles fund holding, release approvals, and dispute resolution
 */

export interface EscrowAccount {
  id: string;
  project_id: string;
  client_id: string;
  designer_id: string;
  manager_id?: string;
  account_number: string;
  status: 'active' | 'closed' | 'suspended';
  total_held: number;
  total_released: number;
  created_at: string;
  metadata: any;
}

export interface EscrowHold {
  id: string;
  escrow_account_id: string;
  transaction_id: string;
  milestone_id?: string;
  project_id: string;
  gross_amount: number;
  platform_fee: number;
  processing_fee: number;
  net_amount: number;
  hold_reason: string;
  status: 'active' | 'pending_release' | 'released' | 'disputed' | 'cancelled';
  requires_manager_approval: boolean;
  requires_quality_approval: boolean;
  manager_approved_at?: string;
  manager_approved_by?: string;
  quality_approved_at?: string;
  quality_approved_by?: string;
  held_at: string;
  release_requested_at?: string;
  released_at?: string;
  auto_release_date?: string;
  hold_notes?: string;
  release_notes?: string;
  metadata: any;
}

export interface EscrowRelease {
  id: string;
  escrow_hold_id: string;
  escrow_account_id: string;
  project_id: string;
  milestone_id?: string;
  release_amount: number;
  release_type: 'milestone_completion' | 'project_completion' | 'partial_release' | 'dispute_resolution' | 'cancellation';
  requested_by: string;
  requested_at: string;
  manager_id?: string;
  manager_approval_status: 'pending' | 'approved' | 'rejected' | 'not_required';
  manager_approved_at?: string;
  manager_notes?: string;
  quality_approval_status: 'pending' | 'approved' | 'rejected' | 'not_required';
  quality_approved_at?: string;
  quality_approved_by?: string;
  quality_notes?: string;
  status: 'pending' | 'approved' | 'rejected' | 'processed' | 'failed';
  processed_at?: string;
  processed_by?: string;
  payout_id?: string;
  payout_transaction_id?: string;
  failure_reason?: string;
  retry_count: number;
  metadata: any;
}

/**
 * Escrow Manager Class
 */
export class EscrowManager {
  /**
   * Create or get escrow account for a project
   */
  static async getOrCreateEscrowAccount(params: {
    projectId: string;
    clientId: string;
    designerId: string;
    managerId?: string;
  }): Promise<{ success: boolean; account?: EscrowAccount; error?: string }> {
    try {
      const { projectId, clientId, designerId, managerId } = params;

      // Check if escrow account already exists
      const { data: existingAccount, error: fetchError } = await supabase
        .from('escrow_accounts')
        .select('*')
        .eq('project_id', projectId)
        .eq('client_id', clientId)
        .eq('designer_id', designerId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      if (existingAccount) {
        return { success: true, account: existingAccount };
      }

      // Generate unique account number
      const accountNumber = `ESC-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;

      // Create new escrow account
      const { data: newAccount, error: createError } = await supabase
        .from('escrow_accounts')
        .insert({
          project_id: projectId,
          client_id: clientId,
          designer_id: designerId,
          manager_id: managerId,
          account_number: accountNumber,
          status: 'active'
        })
        .select()
        .single();

      if (createError) throw createError;

      // Log account creation
      await this.logEscrowActivity({
        escrow_account_id: newAccount.id,
        project_id: projectId,
        activity_type: 'account_created',
        description: `Escrow account created for project`,
        performed_by: clientId, // Typically created when client makes first payment
        metadata: { account_number: accountNumber }
      });

      return { success: true, account: newAccount };
    } catch (error) {
      console.error('Error creating escrow account:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Create escrow hold when payment is received
   */
  static async createEscrowHold(params: {
    transactionId: string;
    projectId: string;
    milestoneId?: string;
    grossAmount: number;
    platformFee: number;
    processingFee: number;
    holdReason?: string;
    requiresManagerApproval?: boolean;
    requiresQualityApproval?: boolean;
    autoReleaseDays?: number;
  }): Promise<{ success: boolean; hold?: EscrowHold; error?: string }> {
    try {
      const {
        transactionId,
        projectId,
        milestoneId,
        grossAmount,
        platformFee,
        processingFee,
        holdReason = 'milestone_completion',
        requiresManagerApproval = true,
        requiresQualityApproval = false,
        autoReleaseDays = 30
      } = params;

      // Get transaction details to find client and designer
      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .select('client_id, designer_id')
        .eq('id', transactionId)
        .single();

      if (transactionError || !transaction) {
        throw new Error('Transaction not found');
      }

      // Get or create escrow account
      const accountResult = await this.getOrCreateEscrowAccount({
        projectId,
        clientId: transaction.client_id,
        designerId: transaction.designer_id
      });

      if (!accountResult.success || !accountResult.account) {
        throw new Error(accountResult.error || 'Failed to create escrow account');
      }

      const netAmount = grossAmount - platformFee - processingFee;
      const autoReleaseDate = new Date();
      autoReleaseDate.setDate(autoReleaseDate.getDate() + autoReleaseDays);

      // Create escrow hold
      const { data: hold, error: holdError } = await supabase
        .from('escrow_holds')
        .insert({
          escrow_account_id: accountResult.account.id,
          transaction_id: transactionId,
          milestone_id: milestoneId,
          project_id: projectId,
          gross_amount: grossAmount,
          platform_fee: platformFee,
          processing_fee: processingFee,
          net_amount: netAmount,
          hold_reason: holdReason,
          status: 'active',
          requires_manager_approval: requiresManagerApproval,
          requires_quality_approval: requiresQualityApproval,
          auto_release_date: autoReleaseDate.toISOString()
        })
        .select()
        .single();

      if (holdError) throw holdError;

      // Update transaction with escrow status
      await supabase
        .from('transactions')
        .update({ escrow_status: 'held' })
        .eq('id', transactionId);

      // Update milestone with escrow hold reference
      if (milestoneId) {
        await supabase
          .from('project_milestones')
          .update({ escrow_hold_id: hold.id })
          .eq('id', milestoneId);
      }

      // Update escrow account totals
      await supabase
        .from('escrow_accounts')
        .update({
          total_held: supabase.raw(`total_held + ${netAmount}`)
        })
        .eq('id', accountResult.account.id);

      // Log hold creation
      await this.logEscrowActivity({
        escrow_account_id: accountResult.account.id,
        escrow_hold_id: hold.id,
        project_id: projectId,
        activity_type: 'hold_created',
        description: `Funds held in escrow: $${netAmount.toFixed(2)}`,
        performed_by: transaction.client_id,
        metadata: {
          gross_amount: grossAmount,
          net_amount: netAmount,
          hold_reason: holdReason
        }
      });

      return { success: true, hold };
    } catch (error) {
      console.error('Error creating escrow hold:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Request escrow release (typically when milestone is completed)
   */
  static async requestEscrowRelease(params: {
    escrowHoldId: string;
    requestedBy: string;
    releaseType?: 'milestone_completion' | 'project_completion' | 'partial_release';
    releaseAmount?: number;
    notes?: string;
  }): Promise<{ success: boolean; release?: EscrowRelease; error?: string }> {
    try {
      const {
        escrowHoldId,
        requestedBy,
        releaseType = 'milestone_completion',
        releaseAmount,
        notes
      } = params;

      // Get escrow hold details
      const { data: hold, error: holdError } = await supabase
        .from('escrow_holds')
        .select(`
          *,
          escrow_account:escrow_accounts(*)
        `)
        .eq('id', escrowHoldId)
        .single();

      if (holdError || !hold) {
        throw new Error('Escrow hold not found');
      }

      if (hold.status !== 'active') {
        throw new Error('Escrow hold is not active');
      }

      const finalReleaseAmount = releaseAmount || hold.net_amount;

      if (finalReleaseAmount > hold.net_amount) {
        throw new Error('Release amount cannot exceed held amount');
      }

      // Get project manager for approval workflow
      const { data: projectAssignment } = await supabase
        .from('project_assignments')
        .select('manager_id')
        .eq('project_id', hold.project_id)
        .eq('status', 'active')
        .single();

      // Create release request
      const { data: release, error: releaseError } = await supabase
        .from('escrow_releases')
        .insert({
          escrow_hold_id: escrowHoldId,
          escrow_account_id: hold.escrow_account_id,
          project_id: hold.project_id,
          milestone_id: hold.milestone_id,
          release_amount: finalReleaseAmount,
          release_type: releaseType,
          requested_by: requestedBy,
          manager_id: projectAssignment?.manager_id,
          manager_approval_status: hold.requires_manager_approval ? 'pending' : 'not_required',
          quality_approval_status: hold.requires_quality_approval ? 'pending' : 'not_required',
          status: 'pending',
          metadata: { notes }
        })
        .select()
        .single();

      if (releaseError) throw releaseError;

      // Update hold status
      await supabase
        .from('escrow_holds')
        .update({
          status: 'pending_release',
          release_requested_at: new Date().toISOString()
        })
        .eq('id', escrowHoldId);

      // Log release request
      await this.logEscrowActivity({
        escrow_account_id: hold.escrow_account_id,
        escrow_hold_id: escrowHoldId,
        escrow_release_id: release.id,
        project_id: hold.project_id,
        activity_type: 'release_requested',
        description: `Release requested: $${finalReleaseAmount.toFixed(2)}`,
        performed_by: requestedBy,
        metadata: {
          release_type: releaseType,
          release_amount: finalReleaseAmount,
          notes
        }
      });

      // Send notifications to approvers
      await this.sendReleaseNotifications(release);

      return { success: true, release };
    } catch (error) {
      console.error('Error requesting escrow release:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Approve escrow release (manager approval)
   */
  static async approveEscrowRelease(params: {
    releaseId: string;
    approverId: string;
    approverRole: 'manager' | 'quality' | 'admin';
    notes?: string;
  }): Promise<{ success: boolean; release?: EscrowRelease; error?: string }> {
    try {
      const { releaseId, approverId, approverRole, notes } = params;

      // Get release details
      const { data: release, error: releaseError } = await supabase
        .from('escrow_releases')
        .select('*')
        .eq('id', releaseId)
        .single();

      if (releaseError || !release) {
        throw new Error('Release request not found');
      }

      if (release.status !== 'pending') {
        throw new Error('Release request is not pending');
      }

      // Update approval based on role
      let updateData: any = {};
      
      if (approverRole === 'manager') {
        updateData = {
          manager_approval_status: 'approved',
          manager_approved_at: new Date().toISOString(),
          manager_notes: notes
        };
      } else if (approverRole === 'quality') {
        updateData = {
          quality_approval_status: 'approved',
          quality_approved_at: new Date().toISOString(),
          quality_approved_by: approverId,
          quality_notes: notes
        };
      }

      const { data: updatedRelease, error: updateError } = await supabase
        .from('escrow_releases')
        .update(updateData)
        .eq('id', releaseId)
        .select()
        .single();

      if (updateError) throw updateError;

      // Check if all required approvals are complete
      const allApproved = (
        (updatedRelease.manager_approval_status === 'approved' || updatedRelease.manager_approval_status === 'not_required') &&
        (updatedRelease.quality_approval_status === 'approved' || updatedRelease.quality_approval_status === 'not_required')
      );

      if (allApproved) {
        // Mark release as approved and ready for processing
        await supabase
          .from('escrow_releases')
          .update({ status: 'approved' })
          .eq('id', releaseId);

        // Process the release
        await this.processEscrowRelease(releaseId);
      }

      // Log approval
      await this.logEscrowActivity({
        escrow_account_id: release.escrow_account_id,
        escrow_hold_id: release.escrow_hold_id,
        escrow_release_id: releaseId,
        project_id: release.project_id,
        activity_type: `${approverRole}_approval`,
        description: `${approverRole} approved release: $${release.release_amount.toFixed(2)}`,
        performed_by: approverId,
        metadata: { notes, approver_role: approverRole }
      });

      return { success: true, release: updatedRelease };
    } catch (error) {
      console.error('Error approving escrow release:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Process approved escrow release (integrate with existing payout system)
   */
  static async processEscrowRelease(releaseId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get release details
      const { data: release, error: releaseError } = await supabase
        .from('escrow_releases')
        .select(`
          *,
          escrow_hold:escrow_holds(*),
          escrow_account:escrow_accounts(*)
        `)
        .eq('id', releaseId)
        .single();

      if (releaseError || !release) {
        throw new Error('Release not found');
      }

      if (release.status !== 'approved') {
        throw new Error('Release is not approved');
      }

      // Create payout record (integrates with existing payout system)
      const { data: payout, error: payoutError } = await supabase
        .from('payouts')
        .insert({
          transaction_id: release.escrow_hold.transaction_id,
          designer_id: release.escrow_account.designer_id,
          amount: release.release_amount,
          status: 'pending', // Will be processed by existing payout scheduler
          escrow_release_id: releaseId,
          notes: `Escrow release: ${release.release_type}`
        })
        .select()
        .single();

      if (payoutError) throw payoutError;

      // Update release status
      await supabase
        .from('escrow_releases')
        .update({
          status: 'processed',
          processed_at: new Date().toISOString(),
          payout_id: payout.id
        })
        .eq('id', releaseId);

      // Update hold status
      await supabase
        .from('escrow_holds')
        .update({
          status: 'released',
          released_at: new Date().toISOString()
        })
        .eq('id', release.escrow_hold_id);

      // Update escrow account totals
      await supabase
        .from('escrow_accounts')
        .update({
          total_held: supabase.raw(`total_held - ${release.release_amount}`),
          total_released: supabase.raw(`total_released + ${release.release_amount}`)
        })
        .eq('id', release.escrow_account_id);

      // Update transaction escrow status
      await supabase
        .from('transactions')
        .update({ escrow_status: 'released' })
        .eq('id', release.escrow_hold.transaction_id);

      // Log processing
      await this.logEscrowActivity({
        escrow_account_id: release.escrow_account_id,
        escrow_hold_id: release.escrow_hold_id,
        escrow_release_id: releaseId,
        project_id: release.project_id,
        activity_type: 'release_processed',
        description: `Funds released to designer: $${release.release_amount.toFixed(2)}`,
        performed_by: 'system',
        metadata: { payout_id: payout.id }
      });

      return { success: true };
    } catch (error) {
      console.error('Error processing escrow release:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send notifications for release requests
   */
  static async sendReleaseNotifications(release: EscrowRelease): Promise<void> {
    try {
      const notifications = [];

      // Notify manager if approval required
      if (release.manager_approval_status === 'pending' && release.manager_id) {
        notifications.push({
          recipient_id: release.manager_id,
          notification_type: 'escrow_release_approval',
          title: 'Escrow Release Approval Required',
          message: `A milestone completion requires your approval to release $${release.release_amount.toFixed(2)} from escrow`,
          priority: 'high',
          metadata: {
            release_id: release.id,
            project_id: release.project_id,
            release_amount: release.release_amount
          }
        });
      }

      // Notify quality team if approval required
      if (release.quality_approval_status === 'pending') {
        const { data: qualityTeam } = await supabase
          .from('profiles')
          .select('id')
          .eq('role', 'quality_team');

        if (qualityTeam && qualityTeam.length > 0) {
          qualityTeam.forEach(member => {
            notifications.push({
              recipient_id: member.id,
              notification_type: 'escrow_quality_approval',
              title: 'Quality Approval Required for Escrow Release',
              message: `Quality review required before releasing $${release.release_amount.toFixed(2)} from escrow`,
              priority: 'high',
              metadata: {
                release_id: release.id,
                project_id: release.project_id,
                release_amount: release.release_amount
              }
            });
          });
        }
      }

      if (notifications.length > 0) {
        await supabase
          .from('workflow_notifications')
          .insert(notifications);
      }
    } catch (error) {
      console.error('Error sending release notifications:', error);
    }
  }

  /**
   * Log escrow activity for audit trail
   */
  static async logEscrowActivity(params: {
    escrow_account_id?: string;
    escrow_hold_id?: string;
    escrow_release_id?: string;
    project_id: string;
    activity_type: string;
    description: string;
    performed_by: string;
    metadata?: any;
  }): Promise<void> {
    try {
      await supabase
        .from('escrow_activities')
        .insert({
          escrow_account_id: params.escrow_account_id,
          escrow_hold_id: params.escrow_hold_id,
          escrow_release_id: params.escrow_release_id,
          project_id: params.project_id,
          activity_type: params.activity_type,
          description: params.description,
          performed_by: params.performed_by,
          metadata: params.metadata || {}
        });
    } catch (error) {
      console.error('Error logging escrow activity:', error);
    }
  }
}

// Export convenience functions
export const createEscrowHold = EscrowManager.createEscrowHold;
export const requestEscrowRelease = EscrowManager.requestEscrowRelease;
export const approveEscrowRelease = EscrowManager.approveEscrowRelease;
