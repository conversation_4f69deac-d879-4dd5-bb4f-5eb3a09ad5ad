import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { message_id, reason } = await request.json();

    if (!message_id) {
      return NextResponse.json(
        { error: 'Message ID is required' },
        { status: 400 }
      );
    }

    // Call the database function to toggle flag
    const { data, error } = await supabase.rpc('toggle_message_flag', {
      message_uuid: message_id,
      admin_uuid: user.id,
      flag_reason_text: reason || null
    });

    if (error) {
      console.error('Error toggling message flag:', error);
      return NextResponse.json(
        { error: 'Failed to toggle message flag' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      is_flagged: data,
      message: data ? 'Message flagged successfully' : 'Message unflagged successfully'
    });

  } catch (error) {
    console.error('Error in POST /api/admin/messages/flag:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
