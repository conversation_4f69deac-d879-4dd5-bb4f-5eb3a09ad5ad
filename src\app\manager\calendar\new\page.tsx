"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Calendar,
  Clock,
  Users,
  MapPin,
  Video,
  Phone,
  Save,
  X,
  Plus,
  Trash2,
  AlertTriangle,
  RefreshCw,
  Send,
  Bell
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  client: {
    id: string;
    full_name: string;
    email: string;
  };
  designer: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface Participant {
  id: string;
  full_name: string;
  email: string;
  role: string;
}

export default function NewCalendarEventPage() {
  const { user, profile } = useOptimizedAuth();
  const router = useRouter();
  
  const [projects, setProjects] = useState<Project[]>([]);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  const [eventData, setEventData] = useState({
    title: '',
    description: '',
    event_type: 'meeting',
    start_date: '',
    start_time: '',
    end_date: '',
    end_time: '',
    timezone: 'UTC',
    location: '',
    meeting_url: '',
    project_id: '',
    selected_participants: [] as string[],
    is_recurring: false,
    recurrence_pattern: 'weekly',
    recurrence_end: '',
    reminder_minutes: 15,
    send_invitations: true,
    notes: ''
  });

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchData();
    }
  }, [user, profile]);

  const fetchData = async () => {
    try {
      // Fetch projects
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id, title,
          client:profiles!projects_client_id_fkey(id, full_name, email),
          designer:profiles!projects_designer_id_fkey(id, full_name, email)
        `)
        .eq('manager_id', user?.id)
        .in('status', ['active', 'in_progress'])
        .order('created_at', { ascending: false });

      if (projectsError) throw projectsError;
      setProjects(projectsData || []);

      // Fetch all potential participants (clients, designers, other managers)
      const { data: participantsData, error: participantsError } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .in('role', ['client', 'designer', 'manager', 'quality_team'])
        .neq('id', user?.id)
        .order('full_name', { ascending: true });

      if (participantsError) throw participantsError;
      setParticipants(participantsData || []);

    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!eventData.title.trim() || !eventData.start_date || !eventData.start_time) {
      alert('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    try {
      const startDateTime = new Date(`${eventData.start_date}T${eventData.start_time}`);
      const endDateTime = eventData.end_date && eventData.end_time 
        ? new Date(`${eventData.end_date}T${eventData.end_time}`)
        : new Date(startDateTime.getTime() + 60 * 60 * 1000); // Default 1 hour

      // Create calendar event
      const { data: event, error: eventError } = await supabase
        .from('calendar_events')
        .insert({
          title: eventData.title.trim(),
          description: eventData.description.trim(),
          event_type: eventData.event_type,
          start_datetime: startDateTime.toISOString(),
          end_datetime: endDateTime.toISOString(),
          timezone: eventData.timezone,
          location: eventData.location.trim(),
          meeting_url: eventData.meeting_url.trim(),
          project_id: eventData.project_id || null,
          created_by: user?.id,
          participants: [user?.id, ...eventData.selected_participants],
          is_recurring: eventData.is_recurring,
          recurrence_pattern: eventData.is_recurring ? eventData.recurrence_pattern : null,
          recurrence_end: eventData.is_recurring && eventData.recurrence_end ? eventData.recurrence_end : null,
          reminder_minutes: eventData.reminder_minutes,
          notes: eventData.notes.trim(),
          status: 'scheduled'
        })
        .select()
        .single();

      if (eventError) throw eventError;

      // Send invitations if enabled
      if (eventData.send_invitations && eventData.selected_participants.length > 0) {
        const invitations = eventData.selected_participants.map(participantId => ({
          user_id: participantId,
          type: 'calendar_invitation',
          title: 'Calendar Event Invitation',
          message: `You've been invited to: ${eventData.title}`,
          data: { 
            event_id: event.id,
            start_datetime: startDateTime.toISOString(),
            event_type: eventData.event_type
          }
        }));

        await supabase.from('notifications').insert(invitations);
      }

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: eventData.project_id || null,
        activity_type: 'calendar_event_created',
        description: `Created calendar event: ${eventData.title}`,
        outcome: 'event_created'
      });

      alert('Calendar event created successfully!');
      router.push('/manager/calendar');
    } catch (error) {
      console.error('Error creating calendar event:', error);
      alert('Error creating calendar event. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const addParticipant = (participantId: string) => {
    if (!eventData.selected_participants.includes(participantId)) {
      setEventData(prev => ({
        ...prev,
        selected_participants: [...prev.selected_participants, participantId]
      }));
    }
  };

  const removeParticipant = (participantId: string) => {
    setEventData(prev => ({
      ...prev,
      selected_participants: prev.selected_participants.filter(id => id !== participantId)
    }));
  };

  const getParticipantById = (id: string) => {
    return participants.find(p => p.id === id);
  };

  const getProjectParticipants = () => {
    if (!eventData.project_id) return [];
    const project = projects.find(p => p.id === eventData.project_id);
    return project ? [project.client, project.designer] : [];
  };

  const addProjectParticipants = () => {
    const projectParticipants = getProjectParticipants();
    const newParticipants = projectParticipants
      .map(p => p.id)
      .filter(id => !eventData.selected_participants.includes(id));
    
    setEventData(prev => ({
      ...prev,
      selected_participants: [...prev.selected_participants, ...newParticipants]
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <Calendar className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create Calendar Event</h1>
            <p className="text-gray-600 mt-1">Schedule a new meeting or event</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Event Details</h2>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Event Title *
              </label>
              <input
                type="text"
                value={eventData.title}
                onChange={(e) => setEventData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter event title..."
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={eventData.description}
                onChange={(e) => setEventData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the purpose and agenda of this event..."
                rows={4}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Event Type
                </label>
                <select
                  value={eventData.event_type}
                  onChange={(e) => setEventData(prev => ({ ...prev, event_type: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                >
                  <option value="meeting">Meeting</option>
                  <option value="presentation">Presentation</option>
                  <option value="review">Review Session</option>
                  <option value="planning">Planning Session</option>
                  <option value="training">Training</option>
                  <option value="consultation">Consultation</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Related Project
                </label>
                <select
                  value={eventData.project_id}
                  onChange={(e) => setEventData(prev => ({ ...prev, project_id: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                >
                  <option value="">No specific project</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.title}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Date and Time */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Date & Time</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Date *
              </label>
              <input
                type="date"
                value={eventData.start_date}
                onChange={(e) => setEventData(prev => ({ ...prev, start_date: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Time *
              </label>
              <input
                type="time"
                value={eventData.start_time}
                onChange={(e) => setEventData(prev => ({ ...prev, start_time: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Date
              </label>
              <input
                type="date"
                value={eventData.end_date}
                onChange={(e) => setEventData(prev => ({ ...prev, end_date: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Time
              </label>
              <input
                type="time"
                value={eventData.end_time}
                onChange={(e) => setEventData(prev => ({ ...prev, end_time: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Timezone
            </label>
            <select
              value={eventData.timezone}
              onChange={(e) => setEventData(prev => ({ ...prev, timezone: e.target.value }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Denver">Mountain Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
              <option value="Europe/London">London</option>
              <option value="Europe/Paris">Paris</option>
              <option value="Asia/Tokyo">Tokyo</option>
            </select>
          </div>
        </div>

        {/* Location and Meeting Details */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Location & Meeting Details</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Physical Location
              </label>
              <input
                type="text"
                value={eventData.location}
                onChange={(e) => setEventData(prev => ({ ...prev, location: e.target.value }))}
                placeholder="Enter meeting location..."
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Video className="inline h-4 w-4 mr-1" />
                Meeting URL
              </label>
              <input
                type="url"
                value={eventData.meeting_url}
                onChange={(e) => setEventData(prev => ({ ...prev, meeting_url: e.target.value }))}
                placeholder="https://zoom.us/j/..."
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>
          </div>
        </div>

        {/* Participants */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Participants</h2>
            {eventData.project_id && (
              <Button
                type="button"
                variant="outline"
                onClick={addProjectParticipants}
                className="flex items-center gap-2"
              >
                <Users className="h-4 w-4" />
                Add Project Team
              </Button>
            )}
          </div>

          {/* Selected Participants */}
          {eventData.selected_participants.length > 0 && (
            <div className="mb-6">
              <h3 className="font-medium text-gray-900 mb-3">Selected Participants</h3>
              <div className="flex flex-wrap gap-2">
                {eventData.selected_participants.map((participantId) => {
                  const participant = getParticipantById(participantId);
                  return participant ? (
                    <div key={participantId} className="flex items-center gap-2 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                      <span>{participant.full_name} ({participant.role})</span>
                      <button
                        type="button"
                        onClick={() => removeParticipant(participantId)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
          )}

          {/* Available Participants */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Available Participants</h3>
            <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
              {participants.map((participant) => (
                <div
                  key={participant.id}
                  className={`flex items-center justify-between p-3 border-b border-gray-100 last:border-b-0 ${
                    eventData.selected_participants.includes(participant.id) ? 'bg-gray-50' : 'hover:bg-gray-50'
                  }`}
                >
                  <div>
                    <p className="font-medium text-gray-900">{participant.full_name}</p>
                    <p className="text-sm text-gray-600">{participant.email} • {participant.role}</p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      eventData.selected_participants.includes(participant.id)
                        ? removeParticipant(participant.id)
                        : addParticipant(participant.id)
                    }
                    className={`flex items-center gap-2 ${
                      eventData.selected_participants.includes(participant.id)
                        ? 'text-red-600 border-red-200 hover:bg-red-50'
                        : 'text-green-600 border-green-200 hover:bg-green-50'
                    }`}
                  >
                    {eventData.selected_participants.includes(participant.id) ? (
                      <>
                        <Trash2 className="h-3 w-3" />
                        Remove
                      </>
                    ) : (
                      <>
                        <Plus className="h-3 w-3" />
                        Add
                      </>
                    )}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recurring and Reminders */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recurring & Reminders</h2>

          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="is_recurring"
                checked={eventData.is_recurring}
                onChange={(e) => setEventData(prev => ({ ...prev, is_recurring: e.target.checked }))}
                className="text-brown-600 focus:ring-brown-500 rounded"
              />
              <label htmlFor="is_recurring" className="text-sm font-medium text-gray-700">
                Make this a recurring event
              </label>
            </div>

            {eventData.is_recurring && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Recurrence Pattern
                  </label>
                  <select
                    value={eventData.recurrence_pattern}
                    onChange={(e) => setEventData(prev => ({ ...prev, recurrence_pattern: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Recurrence End Date
                  </label>
                  <input
                    type="date"
                    value={eventData.recurrence_end}
                    onChange={(e) => setEventData(prev => ({ ...prev, recurrence_end: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Bell className="inline h-4 w-4 mr-1" />
                  Reminder (minutes before)
                </label>
                <select
                  value={eventData.reminder_minutes}
                  onChange={(e) => setEventData(prev => ({ ...prev, reminder_minutes: Number(e.target.value) }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                >
                  <option value={0}>No reminder</option>
                  <option value={5}>5 minutes</option>
                  <option value={15}>15 minutes</option>
                  <option value={30}>30 minutes</option>
                  <option value={60}>1 hour</option>
                  <option value={1440}>1 day</option>
                </select>
              </div>

              <div className="flex items-center gap-3 pt-6">
                <input
                  type="checkbox"
                  id="send_invitations"
                  checked={eventData.send_invitations}
                  onChange={(e) => setEventData(prev => ({ ...prev, send_invitations: e.target.checked }))}
                  className="text-brown-600 focus:ring-brown-500 rounded"
                />
                <label htmlFor="send_invitations" className="text-sm font-medium text-gray-700">
                  Send invitations to participants
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Notes</h2>
          <textarea
            value={eventData.notes}
            onChange={(e) => setEventData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Add any additional notes, agenda items, or special instructions..."
            rows={4}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Cancel
          </Button>

          <Button
            type="submit"
            disabled={!eventData.title.trim() || !eventData.start_date || !eventData.start_time || submitting}
            className="flex items-center gap-2"
          >
            {submitting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            Create Event
          </Button>
        </div>
      </form>
    </div>
  );
}
