import { useEffect, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

interface UseRealtimeMessagesProps {
  conversationId?: string;
  onNewMessage?: (message: any) => void;
  onMessageUpdate?: (message: any) => void;
  onConversationUpdate?: (conversation: any) => void;
  enabled?: boolean;
}

export function useRealtimeMessages({
  conversationId,
  onNewMessage,
  onMessageUpdate,
  onConversationUpdate,
  enabled = true
}: UseRealtimeMessagesProps) {
  const channelRef = useRef<RealtimeChannel | null>(null);

  useEffect(() => {
    if (!enabled) return;

    // Subscribe to conversation messages
    if (conversationId) {
      const messageChannel = supabase
        .channel(`conversation:${conversationId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'conversation_messages',
            filter: `conversation_id=eq.${conversationId}`
          },
          (payload) => {
            console.log('New message received:', payload);
            if (onNewMessage) {
              onNewMessage(payload.new);
            }
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'conversation_messages',
            filter: `conversation_id=eq.${conversationId}`
          },
          (payload) => {
            console.log('Message updated:', payload);
            if (onMessageUpdate) {
              onMessageUpdate(payload.new);
            }
          }
        )
        .subscribe();

      channelRef.current = messageChannel;
    }

    // Subscribe to all conversations for admin overview
    const conversationChannel = supabase
      .channel('admin:conversations')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversations'
        },
        (payload) => {
          console.log('Conversation updated:', payload);
          if (onConversationUpdate) {
            onConversationUpdate(payload.new);
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversation_messages'
        },
        (payload) => {
          console.log('Message activity:', payload);
          // Trigger conversation list refresh for message counts
          if (onConversationUpdate) {
            onConversationUpdate({ updated_at: new Date().toISOString() });
          }
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
      supabase.removeChannel(conversationChannel);
    };
  }, [conversationId, onNewMessage, onMessageUpdate, onConversationUpdate, enabled]);

  const cleanup = () => {
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
      channelRef.current = null;
    }
  };

  return { cleanup };
}
