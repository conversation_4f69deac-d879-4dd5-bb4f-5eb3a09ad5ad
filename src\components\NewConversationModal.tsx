"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";
import { X, Search, User, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

type Project = {
  id: string;
  title: string;
  client_id?: string;
  designer_id?: string;
};

type Profile = {
  id: string;
  full_name: string;
  avatar_url: string | null;
  role: string;
};

type NewConversationModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onConversationSelected: (projectId: string, userId: string) => void;
  userRole: 'client' | 'designer';
};

export default function NewConversationModal({
  isOpen,
  onClose,
  onConversationSelected,
  userRole
}: NewConversationModalProps) {
  const { user } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [contacts, setContacts] = useState<Profile[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && user) {
      fetchProjects();
    }
  }, [isOpen, user]);

  const fetchProjects = async () => {
    setLoading(true);
    try {
      let query;
      
      if (userRole === 'designer') {
        // Designers see projects they're assigned to
        query = supabase
          .from('projects')
          .select(`
            id,
            title,
            client_id,
            profiles!client_id(id, full_name, avatar_url, role)
          `)
          .eq('designer_id', user?.id)
          .order('updated_at', { ascending: false });
      } else {
        // Clients see their own projects
        query = supabase
          .from('projects')
          .select(`
            id,
            title,
            designer_id,
            profiles!designer_id(id, full_name, avatar_url, role)
          `)
          .eq('client_id', user?.id)
          .order('updated_at', { ascending: false });
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      setProjects(data || []);
      
      // Extract contacts from projects
      const contactsMap = new Map<string, Profile>();
      
      data?.forEach(project => {
        const profile = userRole === 'designer' 
          ? project.profiles 
          : project.profiles;
          
        if (profile && !contactsMap.has(profile.id)) {
          contactsMap.set(profile.id, profile);
        }
      });
      
      setContacts(Array.from(contactsMap.values()));
      setLoading(false);
    } catch (error) {
      console.error('Error fetching projects:', error);
      setError('Failed to load projects');
      setLoading(false);
    }
  };

  const filteredContacts = contacts.filter(contact => {
    const matchesSearch = contact.full_name.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  const handleProjectSelect = (projectId: string) => {
    setSelectedProject(projectId);
    
    // Find the project
    const project = projects.find(p => p.id === projectId);
    if (!project) return;
    
    // Get the other user's ID based on role
    const otherUserId = userRole === 'designer' ? project.client_id : project.designer_id;
    
    if (otherUserId) {
      onConversationSelected(projectId, otherUserId);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold">Start New Conversation</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-4">
          <div className="mb-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search projects or contacts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 p-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>
          
          {loading ? (
            <div className="py-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-500">Loading projects...</p>
            </div>
          ) : error ? (
            <div className="py-8 text-center">
              <p className="text-red-500">{error}</p>
            </div>
          ) : projects.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-gray-500">No projects found</p>
              <p className="text-sm text-gray-400 mt-1">
                You need to be part of a project to start a conversation
              </p>
            </div>
          ) : (
            <div className="max-h-96 overflow-y-auto">
              <h3 className="font-medium text-sm text-gray-500 mb-2">Select a project:</h3>
              <div className="space-y-2">
                {projects
                  .filter(project => project.title.toLowerCase().includes(searchQuery.toLowerCase()))
                  .map(project => {
                    const contact = userRole === 'designer' 
                      ? project.profiles 
                      : project.profiles;
                      
                    return (
                      <div
                        key={project.id}
                        className={`p-3 border rounded-md cursor-pointer transition-colors ${
                          selectedProject === project.id
                            ? 'border-primary bg-primary/5'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleProjectSelect(project.id)}
                      >
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-3">
                            {contact?.avatar_url ? (
                              <img
                                src={contact.avatar_url}
                                alt={contact?.full_name}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <User className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{project.title}</p>
                            <p className="text-sm text-gray-500">
                              {contact?.full_name || (userRole === 'designer' ? 'Client' : 'Designer')}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          )}
        </div>
        
        <div className="p-4 border-t flex justify-end">
          <Button variant="outline" onClick={onClose} className="mr-2">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
