"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  Plus,
  Edit,
  Trash,
  Copy,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  FileText,
  Search,
  Filter,
} from "lucide-react";
import { motion } from "framer-motion";

type ProposalTemplate = {
  id: string;
  title: string;
  description: string | null;
  project_type: string | null;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  created_by: string | null;
  creator_name?: string;
  milestone_count: number;
};

export default function ProposalTemplates() {
  const { user } = useOptimizedAuth();
  const [templates, setTemplates] = useState<ProposalTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string | null>(null);
  const [projectTypes, setProjectTypes] = useState<string[]>([]);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (user) {
      fetchTemplates();
      fetchProjectTypes();
    }
  }, [user]);

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      // Fetch templates with creator name and milestone count
      const { data, error } = await supabase
        .from('proposal_templates')
        .select(`
          id,
          title,
          description,
          project_type,
          is_default,
          is_active,
          created_at,
          created_by,
          profiles:created_by(full_name)
        `);

      if (error) throw error;

      // Get milestone counts for each template
      const templatesWithCounts = await Promise.all(
        data.map(async (template) => {
          const { count, error: countError } = await supabase
            .from('proposal_template_milestones')
            .select('id', { count: 'exact' })
            .eq('template_id', template.id);

          if (countError) throw countError;

          return {
            ...template,
            creator_name: template.profiles?.full_name || 'Unknown',
            milestone_count: count || 0
          };
        })
      );

      setTemplates(templatesWithCounts);
    } catch (error) {
      console.error('Error fetching templates:', error);
      setError('Failed to load proposal templates');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjectTypes = async () => {
    try {
      // This could be fetched from a lookup table or hardcoded based on your application
      setProjectTypes([
        "Residential - Single Family",
        "Residential - Multi-Family",
        "Commercial - Office",
        "Commercial - Retail",
        "Commercial - Hospitality",
        "Institutional",
        "Industrial",
        "Mixed-Use",
        "Landscape",
        "Interior Design",
        "Other"
      ]);
    } catch (error) {
      console.error('Error fetching project types:', error);
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (deleteConfirm !== templateId) {
      setDeleteConfirm(templateId);
      return;
    }

    setDeleting(true);
    try {
      const { error } = await supabase
        .from('proposal_templates')
        .delete()
        .eq('id', templateId);

      if (error) throw error;

      setTemplates(templates.filter(t => t.id !== templateId));
      setDeleteConfirm(null);
    } catch (error) {
      console.error('Error deleting template:', error);
      setError('Failed to delete template');
    } finally {
      setDeleting(false);
    }
  };

  const handleToggleActive = async (templateId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('proposal_templates')
        .update({ is_active: !currentStatus })
        .eq('id', templateId);

      if (error) throw error;

      setTemplates(templates.map(t => 
        t.id === templateId ? { ...t, is_active: !currentStatus } : t
      ));
    } catch (error) {
      console.error('Error updating template status:', error);
      setError('Failed to update template status');
    }
  };

  const handleDuplicateTemplate = async (templateId: string) => {
    try {
      // 1. Get the template to duplicate
      const { data: templateData, error: templateError } = await supabase
        .from('proposal_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (templateError) throw templateError;

      // 2. Create a new template based on the original
      const { data: newTemplate, error: createError } = await supabase
        .from('proposal_templates')
        .insert({
          title: `${templateData.title} (Copy)`,
          description: templateData.description,
          scope_template: templateData.scope_template,
          timeline_template: templateData.timeline_template,
          project_type: templateData.project_type,
          created_by: user?.id,
          is_default: false,
          is_active: true
        })
        .select()
        .single();

      if (createError) throw createError;

      // 3. Duplicate the milestones
      const { data: milestones, error: milestonesError } = await supabase
        .from('proposal_template_milestones')
        .select('*')
        .eq('template_id', templateId);

      if (milestonesError) throw milestonesError;

      if (milestones && milestones.length > 0) {
        const newMilestones = milestones.map(m => ({
          template_id: newTemplate.id,
          title: m.title,
          description: m.description,
          percentage: m.percentage,
          estimated_days: m.estimated_days,
          deliverables: m.deliverables,
          order_index: m.order_index
        }));

        const { error: insertError } = await supabase
          .from('proposal_template_milestones')
          .insert(newMilestones);

        if (insertError) throw insertError;
      }

      // 4. Duplicate the sections
      const { data: sections, error: sectionsError } = await supabase
        .from('proposal_template_sections')
        .select('*')
        .eq('template_id', templateId);

      if (sectionsError) throw sectionsError;

      if (sections && sections.length > 0) {
        const newSections = sections.map(s => ({
          template_id: newTemplate.id,
          title: s.title,
          content: s.content,
          section_type: s.section_type,
          order_index: s.order_index
        }));

        const { error: insertSectionsError } = await supabase
          .from('proposal_template_sections')
          .insert(newSections);

        if (insertSectionsError) throw insertSectionsError;
      }

      // Refresh the templates list
      fetchTemplates();
    } catch (error) {
      console.error('Error duplicating template:', error);
      setError('Failed to duplicate template');
    }
  };

  // Filter templates based on search query and filter type
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (template.description && template.description.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesType = !filterType || template.project_type === filterType;
    return matchesSearch && matchesType;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Proposal Templates</h1>
        <Link href="/admin/proposal-templates/new">
          <Button className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0">
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 mb-6 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
          <p>{error}</p>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-4 border-b flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 p-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
            />
          </div>
          <div className="relative w-full md:w-64">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <select
              value={filterType || ''}
              onChange={(e) => setFilterType(e.target.value || null)}
              className="pl-10 p-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
            >
              <option value="">All Project Types</option>
              {projectTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
        </div>

        {filteredTemplates.length === 0 ? (
          <div className="p-8 text-center">
            <FileText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">No Templates Found</h3>
            <p className="text-gray-500 mb-6">
              {templates.length === 0
                ? "No proposal templates have been created yet."
                : "No templates match your search criteria."}
            </p>
            <Link href="/admin/proposal-templates/new">
              <Button className="bg-brown-600 hover:bg-brown-700 text-white border-0">
                Create Your First Template
              </Button>
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Template
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Project Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Milestones
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredTemplates.map((template) => (
                  <motion.tr
                    key={template.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-start">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {template.title}
                            {template.is_default && (
                              <span className="ml-2 px-2 py-0.5 text-xs bg-brown-100 text-brown-800 rounded-full">
                                Default
                              </span>
                            )}
                          </div>
                          {template.description && (
                            <div className="text-sm text-gray-500 mt-1 line-clamp-2">
                              {template.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {template.project_type || "Any"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {template.milestone_count}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          template.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {template.is_active ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(template.created_at)}
                      </div>
                      <div className="text-xs text-gray-500">
                        by {template.creator_name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link href={`/admin/proposal-templates/${template.id}`}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center"
                          onClick={() => handleDuplicateTemplate(template.id)}
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Duplicate
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center"
                          onClick={() => handleToggleActive(template.id, template.is_active)}
                        >
                          {template.is_active ? (
                            <>
                              <XCircle className="h-4 w-4 mr-1" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Activate
                            </>
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className={`flex items-center ${
                            deleteConfirm === template.id
                              ? "bg-red-50 text-red-500 border-red-300"
                              : "text-red-500"
                          }`}
                          onClick={() => handleDeleteTemplate(template.id)}
                          disabled={deleting}
                        >
                          {deleting && deleteConfirm === template.id ? (
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          ) : (
                            <Trash className="h-4 w-4 mr-1" />
                          )}
                          {deleteConfirm === template.id ? "Confirm" : "Delete"}
                        </Button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
