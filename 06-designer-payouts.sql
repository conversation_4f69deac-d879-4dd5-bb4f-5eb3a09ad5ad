-- =====================================================
-- SCRIPT 6: CREATE DESIGNER PAYOUTS TABLE
-- =====================================================

-- Designer Payouts Table
CREATE TABLE IF NOT EXISTS designer_payouts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  milestone_id UUID REFERENCES project_milestones(id) ON DELETE SET NULL,
  amount DECIMAL(10,2) NOT NULL,
  payout_method TEXT NOT NULL DEFAULT 'paypal' CHECK (
    payout_method IN ('paypal', 'bank_transfer', 'stripe', 'check')
  ),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (
    status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')
  ),
  escrow_release_id UUID REFERENCES paypal_escrow_releases(id),
  transaction_id TEXT,
  payout_date TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT positive_payout_amount CHECK (amount > 0)
);

-- Verify completion
SELECT 'Script 6 completed: Designer payouts table created' as status;
