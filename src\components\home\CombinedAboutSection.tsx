"use client";

import { motion } from "framer-motion";
import { useRef } from "react";
import Link from "next/link";
import { Button } from "../ui/button";
import { ArrowRight } from "lucide-react";


const values = [
  {
    title: "Creativity",
    description: "We embrace originality, crafting unique and inspiring architectural designs.",
    icon: "✦",
    details: "Our creative process involves deep exploration of possibilities, drawing inspiration from diverse sources including art, nature, culture, and technology to develop truly original architectural solutions."
  },
  {
    title: "Precision",
    description: "Our focus on detail ensures exceptional quality in every project.",
    icon: "◈",
    details: "We believe that excellence lives in the details. Our rigorous approach to precision encompasses everything from conceptual clarity to construction documentation, ensuring that every element is thoughtfully considered and expertly executed."
  },
  {
    title: "Integrity",
    description: "We uphold the highest ethical standards in all our work.",
    icon: "◎",
    details: "Integrity guides all our relationships and decisions. We maintain transparency with clients, collaborators, and communities, ensuring that our work not only meets aesthetic and functional goals but also upholds ethical principles."
  },
  {
    title: "Innovation",
    description: "We strive to push the boundaries of architecture with forward-thinking solutions.",
    icon: "◇",
    details: "Innovation is central to our practice. We continuously research emerging technologies, materials, and methodologies to develop architectural solutions that address contemporary challenges while anticipating future needs."
  },
  {
    title: "Client-Centricity",
    description: "Our clients' needs and aspirations are at the heart of everything we do.",
    icon: "○",
    details: "We believe successful architecture begins with deep listening. Our collaborative approach ensures that client vision, requirements, and constraints are thoroughly understood and thoughtfully integrated throughout the design process."
  }
];

const teamMembers = [
  {
    name: "Ahmed Senior",
    position: "Founder & Principal Architect",
    image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
    bio: "With over 25 years of experience, Ahmed has led award-winning projects across five continents, bringing a global perspective to every design challenge."
  },
  {
    name: "Sarah Chen",
    position: "Design Director",
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=776&q=80",
    bio: "Sarah combines technical expertise with artistic vision, specializing in creating spaces that harmonize with their natural and cultural contexts."
  },
  {
    name: "Michael Okafor",
    position: "Technical Director",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80",
    bio: "Michael's background in structural engineering and sustainable design ensures our projects are as technically sound as they are beautiful."
  }
];

const CombinedAboutSection = () => {
  const sectionRef = useRef(null);

  return (
    <section
      ref={sectionRef}
      className="py-24 bg-black text-white relative overflow-hidden"
    >
      {/* Visual connector from previous section */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[1px] h-20 bg-gradient-to-b from-transparent to-white/30" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              About Us
            </h2>
            <motion.div
              className="h-1 w-20 bg-primary mx-auto mb-6"
              initial={{ width: 0 }}
              whileInView={{ width: 80 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            />
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center mb-20">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <img
                src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                alt="Architectural design process"
                className="w-full h-auto shadow-2xl"
              />
            </motion.div>

            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div>
                <h3 className="text-2xl font-bold mb-4 text-primary">About Senior's Archi-firm</h3>
                <p className="text-gray-300 leading-relaxed">
                  Welcome to Senior's Archi-firm, where we bring creative visions to life with precision, integrity, and innovation. We create spaces that are more than just buildings—each design bridges creativity and strategy, art and purpose.
                </p>
              </div>

              <div className="flex flex-wrap gap-4 mt-8">
                {values.slice(0, 4).map((value, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white/5 p-4 rounded-lg flex items-center"
                  >
                    <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3 text-xl text-primary">
                      {value.icon}
                    </div>
                    <span className="text-white font-medium">{value.title}</span>
                  </motion.div>
                ))}
              </div>

              <div className="mt-8">
                <Link href="/about">
                  <Button variant="outline" className="text-white border-white hover:bg-white hover:text-black group">
                    <span>Learn More About Us</span>
                    <motion.div
                      className="ml-2"
                      initial={{ x: 0 }}
                      whileHover={{ x: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <ArrowRight className="h-4 w-4" />
                    </motion.div>
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            viewport={{ once: true }}
            className="mt-16 text-center"
          >
            <blockquote className="text-2xl md:text-3xl italic font-light text-gray-300 max-w-3xl mx-auto">
              "We don't just build structures; we craft experiences that transform how people live, work, and connect with their environment."
            </blockquote>
            <div className="mt-6">
              <p className="text-primary font-bold">Ahmed Senior</p>
              <p className="text-sm text-gray-400">Founder & Principal Architect</p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Visual connector to next section */}
      <motion.div
        className="w-[1px] h-20 bg-gradient-to-b from-white/30 to-transparent mx-auto mt-16"
        initial={{ scaleY: 0 }}
        whileInView={{ scaleY: 1 }}
        transition={{ duration: 1, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default CombinedAboutSection;
