import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { conversation_id, notes } = await request.json();

    if (!conversation_id) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      );
    }

    // Call the database function to mark messages as reviewed
    const { data, error } = await supabase.rpc('mark_conversation_messages_reviewed', {
      conversation_uuid: conversation_id,
      admin_uuid: user.id,
      admin_notes_text: notes || null
    });

    if (error) {
      console.error('Error marking messages as reviewed:', error);
      return NextResponse.json(
        { error: 'Failed to mark messages as reviewed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      updated_count: data,
      message: `${data} messages marked as reviewed`
    });

  } catch (error) {
    console.error('Error in POST /api/admin/messages/review:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
