import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * API route for scheduling interviews for designer applications
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const {
      interview_scheduled_at,
      interview_type,
      interview_duration,
      meeting_link,
      phone_number,
      interview_address,
      interview_agenda,
      interviewer_name,
      interviewer_email,
      interview_notes
    } = await request.json();

    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Get the application
    const { data: application, error: appError } = await supabase
      .from('designer_applications')
      .select('*')
      .eq('id', id)
      .single();

    if (appError || !application) {
      return NextResponse.json({ error: 'Application not found' }, { status: 404 });
    }

    // Update application with interview details
    const { error: updateError } = await supabase
      .from('designer_applications')
      .update({
        application_status: 'interview_scheduled',
        interview_scheduled_at,
        interview_type,
        interview_duration,
        meeting_link,
        phone_number,
        interview_address,
        interview_agenda,
        interviewer_name,
        interviewer_email,
        interview_notes,
        status_updated_by: user.id,
        status_updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (updateError) {
      console.error('Error updating application:', updateError);
      return NextResponse.json({ error: 'Failed to schedule interview' }, { status: 500 });
    }

    // Format interview date and time
    const interviewDate = new Date(interview_scheduled_at);
    const formattedDate = interviewDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const formattedTime = interviewDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });

    // Prepare meeting details based on interview type
    let meetingDetails = '';
    switch (interview_type) {
      case 'video_call':
        meetingDetails = meeting_link ? 
          `<p><strong>Meeting Link:</strong> <a href="${meeting_link}" style="color: #8B4513;">${meeting_link}</a></p>` :
          '<p><strong>Meeting Link:</strong> Will be provided separately</p>';
        break;
      case 'phone_call':
        meetingDetails = phone_number ? 
          `<p><strong>Phone Number:</strong> ${phone_number}</p>` :
          '<p><strong>Phone Number:</strong> Will be provided separately</p>';
        break;
      case 'in_person':
        meetingDetails = interview_address ? 
          `<p><strong>Address:</strong><br>${interview_address.replace(/\n/g, '<br>')}</p>` :
          '<p><strong>Address:</strong> Will be provided separately</p>';
        break;
    }

    // Send interview invitation email to applicant
    try {
      await resend.emails.send({
        from: 'Seniors Architecture Firm <<EMAIL>>',
        to: [application.email],
        subject: 'Interview Scheduled - Seniors Architecture Firm',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #8B4513;">Interview Scheduled!</h2>
            
            <p>Dear ${application.full_name},</p>
            
            <p>Great news! We would like to invite you for an interview regarding your designer application with Seniors Architecture Firm.</p>
            
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #8B4513;">Interview Details:</h3>
              <p><strong>Date:</strong> ${formattedDate}</p>
              <p><strong>Time:</strong> ${formattedTime}</p>
              <p><strong>Duration:</strong> ${interview_duration} minutes</p>
              <p><strong>Type:</strong> ${interview_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
              ${meetingDetails}
              ${interviewer_name ? `<p><strong>Interviewer:</strong> ${interviewer_name}</p>` : ''}
            </div>
            
            ${interview_agenda ? `
            <div style="background-color: #e8f4f8; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h4 style="margin-top: 0; color: #8B4513;">Interview Agenda:</h4>
              <p style="margin-bottom: 0;">${interview_agenda}</p>
            </div>
            ` : ''}
            
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
              <h4 style="margin-top: 0; color: #856404;">Important Reminders:</h4>
              <ul style="margin-bottom: 0; color: #856404;">
                <li>Please confirm your attendance by replying to this email</li>
                <li>You will receive a reminder email 24 hours before the interview</li>
                <li>If you need to reschedule, please contact us as soon as possible</li>
                <li>Prepare to discuss your portfolio and experience</li>
              </ul>
            </div>
            
            <p>We're looking forward to meeting you and learning more about your background and experience!</p>
            
            <p>If you have any questions, please don't hesitate to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            
            <p>Best regards,<br>
            ${interviewer_name || 'The Seniors Architecture Firm Team'}</p>
            
            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
            <p style="font-size: 12px; color: #666;">
              This interview invitation was sent regarding your designer application submitted on ${new Date(application.created_at).toLocaleDateString()}.
            </p>
          </div>
        `
      });
      console.log('Interview invitation email sent successfully');
    } catch (emailError) {
      console.error('Error sending interview invitation email:', emailError);
      // Don't fail the scheduling if email fails
    }

    // Schedule reminder email (24 hours before)
    try {
      const reminderTime = new Date(interview_scheduled_at);
      reminderTime.setHours(reminderTime.getHours() - 24);

      // Store reminder in database for processing
      await supabase
        .from('interview_reminders')
        .insert({
          application_id: id,
          reminder_scheduled_at: reminderTime.toISOString(),
          interview_scheduled_at,
          applicant_email: application.email,
          applicant_name: application.full_name,
          interview_type,
          meeting_details: meetingDetails,
          status: 'pending'
        });

      console.log('Interview reminder scheduled successfully');
    } catch (reminderError) {
      console.error('Error scheduling interview reminder:', reminderError);
      // Don't fail the scheduling if reminder fails
    }

    // Record communication
    try {
      await supabase
        .from('application_communications')
        .insert({
          application_id: id,
          communication_type: 'email',
          subject: 'Interview Scheduled - Seniors Architecture Firm',
          content: `Interview scheduled for ${formattedDate} at ${formattedTime}`,
          sent_by: user.id,
          recipient_email: application.email,
          status: 'sent'
        });
    } catch (commError) {
      console.error('Error recording communication:', commError);
    }

    return NextResponse.json({
      success: true,
      message: 'Interview scheduled successfully',
      interview_date: formattedDate,
      interview_time: formattedTime
    });

  } catch (error) {
    console.error('Error in interview scheduling API:', error);
    return NextResponse.json(
      { error: 'Failed to schedule interview' },
      { status: 500 }
    );
  }
}
