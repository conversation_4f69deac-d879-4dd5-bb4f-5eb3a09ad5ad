// Test script to create sample admin messages
// Run this after setting up the database to test the admin message system

const { createClient } = require('@supabase/supabase-js');

// You'll need to replace these with your actual Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestAdminMessages() {
  try {
    console.log('Creating test admin messages...');

    // First, get an admin user ID (you'll need to replace this with an actual admin user ID)
    const { data: adminUser, error: adminError } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'admin')
      .limit(1)
      .single();

    if (adminError || !adminUser) {
      console.error('No admin user found. Please create an admin user first.');
      return;
    }

    const adminId = adminUser.id;

    // Sample admin messages to create
    const testMessages = [
      {
        title: 'Welcome to the Platform!',
        content: 'Welcome to our design platform! We\'re excited to have you join our community of talented designers. Please complete your profile and upload your portfolio to start receiving project opportunities.',
        message_type: 'info',
        priority: 'normal',
        recipient_role: 'designer',
        action_required: true,
        created_by: adminId
      },
      {
        title: 'Platform Maintenance Scheduled',
        content: 'We will be performing scheduled maintenance on Sunday, December 15th from 2:00 AM to 6:00 AM EST. During this time, some features may be temporarily unavailable. We apologize for any inconvenience.',
        message_type: 'warning',
        priority: 'normal',
        recipient_role: 'all',
        action_required: false,
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        created_by: adminId
      },
      {
        title: 'New Features Released!',
        content: 'We\'ve just released exciting new features including enhanced project management tools, improved messaging system, and better portfolio showcase options. Check them out!',
        message_type: 'announcement',
        priority: 'normal',
        recipient_role: 'all',
        action_required: false,
        created_by: adminId
      },
      {
        title: 'Payment Method Update Required',
        content: 'Please update your payment method to ensure timely payments for completed projects. Go to Settings > Payment to add your preferred payment method.',
        message_type: 'warning',
        priority: 'high',
        recipient_role: 'designer',
        action_required: true,
        action_url: '/designer/settings/payment',
        created_by: adminId
      },
      {
        title: 'Portfolio Review Complete',
        content: 'Congratulations! Your portfolio has been reviewed and approved. Your work showcases excellent design skills and attention to detail. You can now receive project assignments.',
        message_type: 'success',
        priority: 'normal',
        recipient_role: 'designer',
        action_required: false,
        created_by: adminId
      },
      {
        title: 'Urgent: Project Deadline Reminder',
        content: 'This is a reminder that your project "Modern Kitchen Renovation" has a deadline approaching in 24 hours. Please ensure all deliverables are submitted on time.',
        message_type: 'urgent',
        priority: 'urgent',
        recipient_role: 'designer',
        action_required: true,
        action_url: '/designer/projects',
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
        created_by: adminId
      },
      {
        title: 'Client Feedback Available',
        content: 'Your client has provided feedback on your recent proposal. Please review their comments and make any necessary adjustments.',
        message_type: 'info',
        priority: 'high',
        recipient_role: 'designer',
        action_required: true,
        action_url: '/designer/proposals',
        created_by: adminId
      },
      {
        title: 'Payment Processed Successfully',
        content: 'Your payment of $2,500 for the "Office Space Design" project has been processed and will be deposited to your account within 2-3 business days.',
        message_type: 'success',
        priority: 'normal',
        recipient_role: 'designer',
        action_required: false,
        action_url: '/designer/dashboard/earnings',
        created_by: adminId
      }
    ];

    // Insert the test messages
    const { data, error } = await supabase
      .from('admin_messages')
      .insert(testMessages)
      .select();

    if (error) {
      console.error('Error creating test messages:', error);
      return;
    }

    console.log(`Successfully created ${data.length} test admin messages:`);
    data.forEach((message, index) => {
      console.log(`${index + 1}. ${message.title} (${message.message_type}, ${message.priority})`);
    });

    console.log('\nTest messages created successfully! You can now:');
    console.log('1. Log in as an admin to view and manage messages at /admin/admin-messages');
    console.log('2. Log in as a designer to see the messages in their dashboard');
    console.log('3. Test the mark-as-read functionality');
    console.log('4. Create new messages using the admin interface');

  } catch (error) {
    console.error('Error in createTestAdminMessages:', error);
  }
}

// Run the script
createTestAdminMessages();
