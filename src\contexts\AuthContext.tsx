"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { supabase } from "@/lib/supabase";
import { Session, User } from "@supabase/supabase-js";
import { useRouter } from "next/navigation";

type UserProfile = {
  id: string;
  full_name: string;
  role: 'client' | 'designer' | 'admin' | 'quality_team' | 'manager';
  email: string;
  avatar_url?: string;
  last_sign_in_at?: string | null;
};

type AuthContextType = {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // First get session, then validate user if session exists
    supabase.auth.getSession().then(({ data: { session }, error: sessionError }) => {
      if (sessionError) {
        console.error('Session error:', sessionError);
        setLoading(false);
        return;
      }

      setSession(session);

      if (session?.user) {
        // For initial load, we can trust the session user to avoid validation issues
        // The session is already validated by Supabase
        const user = session.user;

        if (process.env.NODE_ENV === 'development') {
          console.log('AuthContext: Initial session found for user:', user?.email);
        }

        setUser(user);
        if (user) {
          fetchProfile(user.id);
        } else {
          setLoading(false);
        }
      } else {
        setUser(null);
        setProfile(null);
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);

        if (session?.user) {
          // For auth state changes, we trust the session user to avoid AuthSessionMissingError
          // This is a conscious trade-off: we validate on initial load but trust auth state changes
          // The session user is validated by Supabase's auth system during the state change
          const user = session.user;
          setUser(user);

          // If this is a new sign in, update the last_sign_in_at field
          if (event === 'SIGNED_IN' && user) {
            try {
              await supabase
                .from('profiles')
                .update({ last_sign_in_at: new Date().toISOString() })
                .eq('id', user.id);
            } catch (error) {
              console.error('Error updating last sign in time:', error);
              // Continue anyway, this is not critical
            }
          }

          fetchProfile(user.id);
        } else {
          // No session, clear everything
          setUser(null);
          setProfile(null);
          setLoading(false);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile in AuthContext:', error);
        // Don't throw error, just set profile to null and continue
        setProfile(null);
        setLoading(false);
        return;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('AuthContext: Profile fetched successfully:', data?.email, 'Role:', data?.role);
      }
      setProfile(data as UserProfile);
      setLoading(false);

      // Additional debug logging
      console.log('AuthContext: Final state after profile fetch:', {
        user: !!user,
        profile: !!data,
        profileRole: data?.role,
        loading: false
      });
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setProfile(null);
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id);
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  return (
    <AuthContext.Provider value={{ session, user, profile, loading, signOut, refreshProfile }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
