"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  Folder<PERSON><PERSON>ban,
  User,
  Users,
  Calendar,
  DollarSign,
  MapPin,
  Star,
  CheckCircle,
  Clock,
  AlertTriangle,
  Search,
  Filter,
  ArrowRight
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface Project {
  id: string;
  title: string;
  description: string;
  status: string;
  budget: number | null;
  deadline: string | null;
  created_at: string;
  client_id: string;
  designer_id: string | null;
  client_name: string;
  designer_name: string | null;
  service_category: string | null;
  location: string | null;
}

interface Designer {
  id: string;
  full_name: string;
  email: string;
  avatar_url: string | null;
  specialization: string | null;
  skills: string[] | null;
  availability: boolean;
  rating: number;
  completed_projects: number;
  location: string | null;
}

export function ProjectAssignmentInterface() {
  const { user } = useOptimizedAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [designers, setDesigners] = useState<Designer[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [assigningProjectId, setAssigningProjectId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'unassigned' | 'assigned' | 'in_progress'>('unassigned');

  useEffect(() => {
    fetchProjects();
    fetchDesigners();
  }, [statusFilter]);

  const fetchProjects = async () => {
    try {
      let query = supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          status,
          budget,
          deadline,
          created_at,
          client_id,
          designer_id,
          service_category,
          location,
          profiles!projects_client_id_fkey(full_name),
          profiles!projects_designer_id_fkey(full_name)
        `)
        .order('created_at', { ascending: false });

      if (statusFilter === 'unassigned') {
        query = query.is('designer_id', null);
      } else if (statusFilter === 'assigned') {
        query = query.not('designer_id', 'is', null).eq('status', 'assigned');
      } else if (statusFilter === 'in_progress') {
        query = query.eq('status', 'in_progress');
      }

      const { data, error } = await query;

      if (error) throw error;

      const formattedProjects = (data || []).map(project => ({
        id: project.id,
        title: project.title,
        description: project.description,
        status: project.status,
        budget: project.budget,
        deadline: project.deadline,
        created_at: project.created_at,
        client_id: project.client_id,
        designer_id: project.designer_id,
        client_name: project.profiles?.full_name || 'Unknown Client',
        designer_name: project.profiles?.full_name || null,
        service_category: project.service_category,
        location: project.location
      }));

      setProjects(formattedProjects);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDesigners = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'designer')
        .eq('application_status', 'approved')
        .eq('is_active', true)
        .order('full_name');

      if (error) throw error;

      // Get designer statistics
      const designersWithStats = await Promise.all(
        (data || []).map(async (designer) => {
          // Get completed projects count
          const { count: completedCount } = await supabase
            .from('projects')
            .select('*', { count: 'exact', head: true })
            .eq('designer_id', designer.id)
            .eq('status', 'completed');

          // Mock rating for now (would come from reviews)
          const rating = 4.5 + Math.random() * 0.5;

          return {
            id: designer.id,
            full_name: designer.full_name,
            email: designer.email,
            avatar_url: designer.avatar_url,
            specialization: designer.specialization,
            skills: designer.skills,
            availability: designer.availability ?? true,
            rating: Math.round(rating * 10) / 10,
            completed_projects: completedCount || 0,
            location: designer.location
          };
        })
      );

      setDesigners(designersWithStats);
    } catch (error) {
      console.error('Error fetching designers:', error);
    }
  };

  const handleAssignProject = async (projectId: string, designerId: string) => {
    setAssigningProjectId(projectId);
    
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          designer_id: designerId,
          status: 'assigned',
          assigned_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);

      if (error) throw error;

      // Update local state
      setProjects(prev => 
        prev.map(project => 
          project.id === projectId 
            ? { 
                ...project, 
                designer_id: designerId,
                designer_name: designers.find(d => d.id === designerId)?.full_name || null,
                status: 'assigned'
              }
            : project
        )
      );

      setShowAssignModal(false);
      setSelectedProject(null);
      console.log('Project assigned successfully');
    } catch (error) {
      console.error('Error assigning project:', error);
    } finally {
      setAssigningProjectId(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'in_progress':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'assigned':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'cancelled':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'in_progress':
        return <Clock className="h-4 w-4" />;
      case 'assigned':
        return <User className="h-4 w-4" />;
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <FolderKanban className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return 'Budget TBD';
    return `$${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No deadline';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const filteredDesigners = designers.filter(designer => {
    if (!searchTerm) return true;
    return (
      designer.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      designer.specialization?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      designer.skills?.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Project Assignment</h3>
          <div className="flex items-center space-x-2">
            {/* Status Filter */}
            {['all', 'unassigned', 'assigned', 'in_progress'].map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setStatusFilter(filterOption as any)}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${
                  statusFilter === filterOption
                    ? 'bg-brown-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filterOption.charAt(0).toUpperCase() + filterOption.slice(1).replace('_', ' ')}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      <div className="p-6">
        {projects.length === 0 ? (
          <div className="text-center py-8">
            <FolderKanban className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Projects</h4>
            <p className="text-gray-600">No projects found for the selected filter.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {projects.map((project) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-gray-900">{project.title}</h4>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                        {getStatusIcon(project.status)}
                        <span className="ml-1 capitalize">{project.status.replace('_', ' ')}</span>
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{project.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        <span>{project.client_name}</span>
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span>{formatCurrency(project.budget)}</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{formatDate(project.deadline)}</span>
                      </div>
                      {project.designer_name ? (
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-1" />
                          <span>{project.designer_name}</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-yellow-600">
                          <AlertTriangle className="h-4 w-4 mr-1" />
                          <span>Unassigned</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {!project.designer_id && (
                      <Button
                        onClick={() => {
                          setSelectedProject(project);
                          setShowAssignModal(true);
                        }}
                        className="bg-brown-600 hover:bg-brown-700 text-white"
                        size="sm"
                      >
                        <Users className="h-4 w-4 mr-1" />
                        Assign Designer
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Assignment Modal */}
      {showAssignModal && selectedProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Assign Designer to Project</h3>
                <button
                  onClick={() => setShowAssignModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              <p className="text-gray-600 mt-1">Project: {selectedProject.title}</p>
            </div>
            
            <div className="p-6">
              {/* Search */}
              <div className="mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search designers by name, specialization, or skills..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Designers List */}
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {filteredDesigners.map((designer) => (
                  <div
                    key={designer.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                          {designer.avatar_url ? (
                            <img
                              src={designer.avatar_url}
                              alt={designer.full_name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <User className="h-6 w-6 text-gray-400" />
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900">{designer.full_name}</h4>
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-400 mr-1" />
                              <span className="text-sm text-gray-600">{designer.rating}</span>
                            </div>
                            {designer.availability ? (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-800 border border-green-200">
                                Available
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-50 text-red-800 border border-red-200">
                                Busy
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            {designer.specialization && (
                              <span>{designer.specialization}</span>
                            )}
                            <span>{designer.completed_projects} projects completed</span>
                          </div>
                          
                          {designer.skills && designer.skills.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {designer.skills.slice(0, 3).map((skill, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-brown-100 text-brown-800 text-xs rounded-full"
                                >
                                  {skill}
                                </span>
                              ))}
                              {designer.skills.length > 3 && (
                                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                  +{designer.skills.length - 3} more
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        onClick={() => handleAssignProject(selectedProject.id, designer.id)}
                        disabled={assigningProjectId === selectedProject.id}
                        className="bg-brown-600 hover:bg-brown-700 text-white"
                        size="sm"
                      >
                        <ArrowRight className="h-4 w-4 mr-1" />
                        Assign
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </motion.div>
  );
}
