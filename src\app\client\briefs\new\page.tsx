"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter, useSearchParams } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  ArrowRight,
  FileText,
  DollarSign,
  Clock,
  MapPin,
  Palette,
  Target,
  AlertCircle,
  Users,
  User,
  CheckCircle,
  Building
} from "lucide-react";
import Link from "next/link";

interface BriefFormData {
  title: string;
  description: string;
  requirements: string;
  preferred_style: string;
  budget_range: string;
  timeline_preference: string;
  location: string;
  project_type: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  objectives: string[];
}

interface ConnectedDesigner {
  id: string;
  full_name: string;
  avatar_url: string | null;
  specialization: string;
  availability_status: 'available' | 'busy' | 'offline';
  rating: number;
  completed_projects: number;
}

export default function NewBrief() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectedDesigners, setConnectedDesigners] = useState<ConnectedDesigner[]>([]);
  const [selectedDesigners, setSelectedDesigners] = useState<string[]>([]);
  const [assignmentType, setAssignmentType] = useState<'specific' | 'platform' | 'multiple'>('platform');
  const [preSelectedDesignerId, setPreSelectedDesignerId] = useState<string | null>(null);
  const [formData, setFormData] = useState<BriefFormData>({
    title: "",
    description: "",
    requirements: "",
    preferred_style: "",
    budget_range: "",
    timeline_preference: "",
    location: "",
    project_type: "",
    urgency: "medium",
    objectives: [""]
  });

  useEffect(() => {
    if (user) {
      // Check for designerId URL parameter
      const designerId = searchParams.get('designerId');
      if (designerId) {
        setPreSelectedDesignerId(designerId);
        setSelectedDesigners([designerId]);
        setAssignmentType('specific');
      }

      // Fetch connected designers
      fetchConnectedDesigners();
    }
  }, [user, searchParams]);

  const fetchConnectedDesigners = async () => {
    if (!user) return;

    try {
      // Get connections
      const { data: connectionsData, error: connectionsError } = await supabase
        .from('connections')
        .select('designer_id')
        .eq('client_id', user.id)
        .eq('status', 'active');

      if (connectionsError) throw connectionsError;

      if (!connectionsData || connectionsData.length === 0) {
        setConnectedDesigners([]);
        return;
      }

      // Get designer profiles
      const designerIds = connectionsData.map(connection => connection.designer_id);

      const { data: designersData, error: designersError } = await supabase
        .from('profiles')
        .select('id, full_name, avatar_url, skills')
        .in('id', designerIds);

      if (designersError) throw designersError;

      // Get designer availability
      const { data: availabilityData, error: availabilityError } = await supabase
        .from('designer_availability')
        .select('designer_id, status')
        .in('designer_id', designerIds);

      if (availabilityError) console.error('Error fetching availability:', availabilityError);

      // Get designer specializations
      const { data: specializationsData, error: specializationsError } = await supabase
        .from('designer_specializations')
        .select('designer_id, specialization, is_primary')
        .in('designer_id', designerIds)
        .eq('is_primary', true);

      if (specializationsError) console.error('Error fetching specializations:', specializationsError);

      // Map the data
      const designers: ConnectedDesigner[] = designersData.map(designer => {
        const availability = availabilityData?.find(a => a.designer_id === designer.id);
        const specialization = specializationsData?.find(s => s.designer_id === designer.id);

        return {
          id: designer.id,
          full_name: designer.full_name,
          avatar_url: designer.avatar_url,
          specialization: specialization?.specialization || designer.skills?.[0] || 'Interior Design',
          availability_status: availability?.status || 'available',
          rating: 4.8, // TODO: Calculate from reviews
          completed_projects: 12 // TODO: Calculate from projects
        };
      });

      setConnectedDesigners(designers);
    } catch (error) {
      console.error('Error fetching connected designers:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleObjectiveChange = (index: number, value: string) => {
    const newObjectives = [...formData.objectives];
    newObjectives[index] = value;
    setFormData(prev => ({
      ...prev,
      objectives: newObjectives
    }));
  };

  const addObjective = () => {
    setFormData(prev => ({
      ...prev,
      objectives: [...prev.objectives, ""]
    }));
  };

  const removeObjective = (index: number) => {
    if (formData.objectives.length > 1) {
      setFormData(prev => ({
        ...prev,
        objectives: prev.objectives.filter((_, i) => i !== index)
      }));
    }
  };

  const handleDesignerSelection = (designerId: string, checked: boolean) => {
    if (checked) {
      setSelectedDesigners(prev => [...prev, designerId]);
    } else {
      setSelectedDesigners(prev => prev.filter(id => id !== designerId));
    }
  };

  const handleAssignmentTypeChange = (type: 'specific' | 'platform' | 'multiple') => {
    setAssignmentType(type);
    if (type === 'platform') {
      setSelectedDesigners([]);
    } else if (type === 'specific' && preSelectedDesignerId) {
      setSelectedDesigners([preSelectedDesignerId]);
    }
  };

  const nextStep = () => {
    if (validateStep()) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  const validateStep = () => {
    switch (currentStep) {
      case 1:
        return formData.title.trim() !== "" && formData.description.trim() !== "";
      case 2:
        return formData.project_type !== "" && formData.location.trim() !== "";
      case 3:
        return formData.budget_range !== "" && formData.timeline_preference !== "";
      case 4:
        return assignmentType === 'platform' || selectedDesigners.length > 0;
      case 5:
        return true;
      default:
        return true;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (assignmentType === 'platform') {
        // Single brief to platform (admin will assign)
        const { data: briefData, error: briefError } = await supabase
          .from('project_briefs')
          .insert({
            client_id: user?.id,
            title: formData.title,
            description: formData.description,
            requirements: formData.requirements,
            preferred_style: formData.preferred_style,
            budget_range: formData.budget_range,
            timeline_preference: formData.timeline_preference,
            location: formData.location,
            project_type: formData.project_type,
            urgency: formData.urgency,
            status: 'pending',
            assigned_designer_id: null
          })
          .select()
          .single();

        if (briefError) throw briefError;
        router.push(`/client/briefs/${briefData.id}`);
      } else {
        // Multiple briefs for selected designers
        const briefsToInsert = selectedDesigners.map(designerId => ({
          client_id: user?.id,
          title: formData.title,
          description: formData.description,
          requirements: formData.requirements,
          preferred_style: formData.preferred_style,
          budget_range: formData.budget_range,
          timeline_preference: formData.timeline_preference,
          location: formData.location,
          project_type: formData.project_type,
          urgency: formData.urgency,
          status: 'assigned',
          assigned_designer_id: designerId
        }));

        const { data: briefsData, error: briefsError } = await supabase
          .from('project_briefs')
          .insert(briefsToInsert)
          .select();

        if (briefsError) throw briefsError;

        // Redirect to briefs list to show all created briefs
        router.push('/client/briefs');
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while submitting your brief';
      console.error('Error submitting brief:', error);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const totalSteps = 5;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/client/dashboard">
            <Button variant="ghost" size="sm" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Submit Project Brief</h1>
          <p className="text-gray-600 mt-2">
            Tell us about your project and we'll connect you with the perfect designer
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Step {currentStep} of {totalSteps}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round((currentStep / totalSteps) * 100)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-brown-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            ></div>
          </div>
        </div>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
          >
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-red-700">{error}</p>
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="flex items-center mb-6">
                <FileText className="h-6 w-6 text-brown-600 mr-3" />
                <h2 className="text-xl font-semibold">Project Overview</h2>
              </div>

              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Title <span className="text-red-500">*</span>
                </label>
                <input
                  id="title"
                  name="title"
                  type="text"
                  value={formData.title}
                  onChange={handleChange}
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="e.g., Modern Kitchen Renovation"
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  rows={5}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="Describe your vision, goals, and what you hope to achieve..."
                />
              </div>

              <div>
                <label htmlFor="requirements" className="block text-sm font-medium text-gray-700 mb-1">
                  Specific Requirements
                </label>
                <textarea
                  id="requirements"
                  name="requirements"
                  value={formData.requirements}
                  onChange={handleChange}
                  rows={4}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="Any specific requirements, constraints, or must-haves..."
                />
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!validateStep()}
                  className="flex items-center bg-brown-600 hover:bg-brown-700 text-white"
                >
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )}

          {/* Step 2: Project Details */}
          {currentStep === 2 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="flex items-center mb-6">
                <Target className="h-6 w-6 text-brown-600 mr-3" />
                <h2 className="text-xl font-semibold">Project Details</h2>
              </div>

              <div>
                <label htmlFor="project_type" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Type <span className="text-red-500">*</span>
                </label>
                <select
                  id="project_type"
                  name="project_type"
                  value={formData.project_type}
                  onChange={handleChange}
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                >
                  <option value="">Select project type</option>
                  <option value="residential_interior">Residential Interior Design</option>
                  <option value="commercial_interior">Commercial Interior Design</option>
                  <option value="renovation">Renovation</option>
                  <option value="new_construction">New Construction</option>
                  <option value="consultation">Design Consultation</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Location <span className="text-red-500">*</span>
                </label>
                <input
                  id="location"
                  name="location"
                  type="text"
                  value={formData.location}
                  onChange={handleChange}
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="City, State or Full Address"
                />
              </div>

              <div>
                <label htmlFor="preferred_style" className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Style
                </label>
                <select
                  id="preferred_style"
                  name="preferred_style"
                  value={formData.preferred_style}
                  onChange={handleChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                >
                  <option value="">Select preferred style</option>
                  <option value="modern">Modern</option>
                  <option value="contemporary">Contemporary</option>
                  <option value="traditional">Traditional</option>
                  <option value="transitional">Transitional</option>
                  <option value="industrial">Industrial</option>
                  <option value="minimalist">Minimalist</option>
                  <option value="rustic">Rustic</option>
                  <option value="eclectic">Eclectic</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="urgency" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Urgency
                </label>
                <select
                  id="urgency"
                  name="urgency"
                  value={formData.urgency}
                  onChange={handleChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                >
                  <option value="low">Low - Flexible timeline</option>
                  <option value="medium">Medium - Standard timeline</option>
                  <option value="high">High - Need to start soon</option>
                  <option value="urgent">Urgent - Need to start immediately</option>
                </select>
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  onClick={prevStep}
                  variant="outline"
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!validateStep()}
                  className="flex items-center bg-brown-600 hover:bg-brown-700 text-white"
                >
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )}

          {/* Step 3: Budget & Timeline */}
          {currentStep === 3 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="flex items-center mb-6">
                <DollarSign className="h-6 w-6 text-brown-600 mr-3" />
                <h2 className="text-xl font-semibold">Budget & Timeline</h2>
              </div>

              <div>
                <label htmlFor="budget_range" className="block text-sm font-medium text-gray-700 mb-1">
                  Budget Range <span className="text-red-500">*</span>
                </label>
                <select
                  id="budget_range"
                  name="budget_range"
                  value={formData.budget_range}
                  onChange={handleChange}
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                >
                  <option value="">Select budget range</option>
                  <option value="under_5k">Under $5,000</option>
                  <option value="5k_10k">$5,000 - $10,000</option>
                  <option value="10k_25k">$10,000 - $25,000</option>
                  <option value="25k_plus">$25,000+</option>
                  <option value="flexible">Flexible - Depends on proposal</option>
                </select>
                <p className="text-sm text-gray-500 mt-1">
                  This helps us match you with designers who work within your budget range
                </p>
              </div>

              <div>
                <label htmlFor="timeline_preference" className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Timeline <span className="text-red-500">*</span>
                </label>
                <select
                  id="timeline_preference"
                  name="timeline_preference"
                  value={formData.timeline_preference}
                  onChange={handleChange}
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                >
                  <option value="">Select timeline</option>
                  <option value="1_month">1 Month</option>
                  <option value="2_3_months">2-3 Months</option>
                  <option value="3_6_months">3-6 Months</option>
                  <option value="6_plus_months">6+ Months</option>
                  <option value="flexible">Flexible</option>
                </select>
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  onClick={prevStep}
                  variant="outline"
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!validateStep()}
                  className="flex items-center bg-brown-600 hover:bg-brown-700 text-white"
                >
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )}

          {/* Step 4: Designer Selection */}
          {currentStep === 4 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="flex items-center mb-6">
                <Users className="h-6 w-6 text-brown-600 mr-3" />
                <h2 className="text-xl font-semibold">Designer Assignment</h2>
              </div>

              {preSelectedDesignerId ? (
                // Pre-selected designer from URL
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <CheckCircle className="h-5 w-5 text-blue-600 mr-2" />
                    <h3 className="font-medium text-blue-900">Sending to Specific Designer</h3>
                  </div>
                  {connectedDesigners.find(d => d.id === preSelectedDesignerId) && (
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {connectedDesigners.find(d => d.id === preSelectedDesignerId)?.avatar_url ? (
                          <img
                            src={connectedDesigners.find(d => d.id === preSelectedDesignerId)?.avatar_url!}
                            alt={connectedDesigners.find(d => d.id === preSelectedDesignerId)?.full_name}
                            className="h-10 w-10 rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <User className="h-5 w-5 text-gray-500" />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {connectedDesigners.find(d => d.id === preSelectedDesignerId)?.full_name}
                        </p>
                        <p className="text-sm text-gray-600">
                          {connectedDesigners.find(d => d.id === preSelectedDesignerId)?.specialization}
                        </p>
                      </div>
                    </div>
                  )}
                  <p className="text-sm text-blue-700 mt-2">
                    This brief will be sent directly to this designer.
                  </p>
                </div>
              ) : (
                // Designer selection options
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      How would you like to assign this brief?
                    </label>
                    <div className="space-y-3">
                      <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          name="assignmentType"
                          value="platform"
                          checked={assignmentType === 'platform'}
                          onChange={() => handleAssignmentTypeChange('platform')}
                          className="mr-3"
                        />
                        <div className="flex items-center">
                          <Building className="h-5 w-5 text-gray-500 mr-3" />
                          <div>
                            <p className="font-medium">Send to Platform</p>
                            <p className="text-sm text-gray-600">Admin will assign the best designer for your project</p>
                          </div>
                        </div>
                      </label>

                      {connectedDesigners.length > 0 && (
                        <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="radio"
                            name="assignmentType"
                            value="multiple"
                            checked={assignmentType === 'multiple'}
                            onChange={() => handleAssignmentTypeChange('multiple')}
                            className="mr-3"
                          />
                          <div className="flex items-center">
                            <Users className="h-5 w-5 text-gray-500 mr-3" />
                            <div>
                              <p className="font-medium">Send to Connected Designers</p>
                              <p className="text-sm text-gray-600">Choose from your connected designers</p>
                            </div>
                          </div>
                        </label>
                      )}
                    </div>
                  </div>

                  {assignmentType === 'multiple' && connectedDesigners.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Select Designers <span className="text-red-500">*</span>
                      </label>
                      <div className="space-y-3 max-h-64 overflow-y-auto">
                        {connectedDesigners.map((designer) => (
                          <label key={designer.id} className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input
                              type="checkbox"
                              checked={selectedDesigners.includes(designer.id)}
                              onChange={(e) => handleDesignerSelection(designer.id, e.target.checked)}
                              className="mr-3"
                            />
                            <div className="flex items-center space-x-3 flex-1">
                              <div className="flex-shrink-0">
                                {designer.avatar_url ? (
                                  <img
                                    src={designer.avatar_url}
                                    alt={designer.full_name}
                                    className="h-10 w-10 rounded-full object-cover"
                                  />
                                ) : (
                                  <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                    <User className="h-5 w-5 text-gray-500" />
                                  </div>
                                )}
                              </div>
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">{designer.full_name}</p>
                                <p className="text-sm text-gray-600">{designer.specialization}</p>
                                <div className="flex items-center mt-1">
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    designer.availability_status === 'available'
                                      ? 'bg-green-100 text-green-800'
                                      : designer.availability_status === 'busy'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-gray-100 text-gray-800'
                                  }`}>
                                    {designer.availability_status}
                                  </span>
                                  <span className="text-xs text-gray-500 ml-2">
                                    {designer.completed_projects} projects completed
                                  </span>
                                </div>
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                      {selectedDesigners.length > 0 && (
                        <p className="text-sm text-gray-600 mt-2">
                          {selectedDesigners.length} designer{selectedDesigners.length !== 1 ? 's' : ''} selected.
                          A separate brief will be created for each designer.
                        </p>
                      )}
                    </div>
                  )}

                  {connectedDesigners.length === 0 && (
                    <div className="text-center py-6 bg-gray-50 rounded-lg">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 mb-2">No connected designers found</p>
                      <p className="text-sm text-gray-500">Your brief will be sent to the platform for admin assignment</p>
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-between">
                <Button
                  type="button"
                  onClick={prevStep}
                  variant="outline"
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!validateStep()}
                  className="flex items-center bg-brown-600 hover:bg-brown-700 text-white"
                >
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )}

          {/* Step 5: Project Objectives & Review */}
          {currentStep === 5 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <div className="flex items-center mb-6">
                <Target className="h-6 w-6 text-brown-600 mr-3" />
                <h2 className="text-xl font-semibold">Project Objectives & Review</h2>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Project Objectives
                </label>
                {formData.objectives.map((objective, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <input
                      type="text"
                      value={objective}
                      onChange={(e) => handleObjectiveChange(index, e.target.value)}
                      className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      placeholder={`Objective ${index + 1}`}
                    />
                    {formData.objectives.length > 1 && (
                      <Button
                        type="button"
                        onClick={() => removeObjective(index)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  onClick={addObjective}
                  variant="outline"
                  size="sm"
                  className="mt-2"
                >
                  Add Objective
                </Button>
              </div>

              {/* Review Section */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Review Your Brief</h3>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium">Title:</span> {formData.title}
                  </div>
                  <div>
                    <span className="font-medium">Type:</span> {formData.project_type.replace('_', ' ')}
                  </div>
                  <div>
                    <span className="font-medium">Location:</span> {formData.location}
                  </div>
                  <div>
                    <span className="font-medium">Budget:</span> {formData.budget_range.replace('_', ' - $')}
                  </div>
                  <div>
                    <span className="font-medium">Timeline:</span> {formData.timeline_preference.replace('_', ' ')}
                  </div>
                  <div>
                    <span className="font-medium">Urgency:</span> {formData.urgency}
                  </div>
                  <div>
                    <span className="font-medium">Assignment:</span> {
                      assignmentType === 'platform'
                        ? 'Platform (Admin will assign)'
                        : preSelectedDesignerId
                        ? `Specific Designer (${connectedDesigners.find(d => d.id === preSelectedDesignerId)?.full_name})`
                        : `${selectedDesigners.length} Selected Designer${selectedDesigners.length !== 1 ? 's' : ''}`
                    }
                  </div>
                  {selectedDesigners.length > 0 && assignmentType === 'multiple' && (
                    <div className="ml-4">
                      {selectedDesigners.map(designerId => {
                        const designer = connectedDesigners.find(d => d.id === designerId);
                        return designer ? (
                          <div key={designerId} className="text-xs text-gray-600">
                            • {designer.full_name}
                          </div>
                        ) : null;
                      })}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  onClick={prevStep}
                  variant="outline"
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="flex items-center bg-brown-600 hover:bg-brown-700 text-white"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Brief
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </motion.div>
          )}
        </form>
      </div>
    </div>
  );
}
