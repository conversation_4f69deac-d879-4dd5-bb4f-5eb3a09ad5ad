"use client";

import { useState, useEffect, ReactNode, cloneElement, isValidElement } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface CollapsibleSidebarProps {
  children: ReactNode;
  defaultCollapsed?: boolean;
  className?: string;
  role?: string;
  onCollapseChange?: (isCollapsed: boolean) => void;
}

export function CollapsibleSidebar({
  children,
  defaultCollapsed = false,
  className = "",
  role = "admin",
  onCollapseChange
}: CollapsibleSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  // Load collapsed state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem(`sidebar-collapsed-${role}`);
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState));
    }
  }, [role]);

  // Save collapsed state to localStorage
  const toggleCollapsed = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    localStorage.setItem(`sidebar-collapsed-${role}`, JSON.stringify(newState));
    // Notify parent component of the change
    onCollapseChange?.(newState);
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <motion.aside
        initial={false}
        animate={{ 
          width: isCollapsed ? 64 : 320,
          transition: { duration: 0.3, ease: "easeInOut" }
        }}
        className={`hidden lg:flex bg-white border-r border-gray-200 h-screen flex-col shadow-sm fixed left-0 top-0 z-30 ${className}`}
      >
        {/* Toggle Button */}
        <button
          onClick={toggleCollapsed}
          className="absolute -right-3 top-6 w-6 h-6 bg-white border border-gray-200 rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-shadow z-40"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? (
            <ChevronRight className="h-3 w-3 text-gray-600" />
          ) : (
            <ChevronLeft className="h-3 w-3 text-gray-600" />
          )}
        </button>

        {/* Sidebar Content */}
        <div className="flex flex-col h-full overflow-hidden">
          {isValidElement(children)
            ? cloneElement(children, { isCollapsed } as any)
            : children
          }
        </div>
      </motion.aside>
    </>
  );
}

// Hook to get current collapsed state with real-time updates
export function useSidebarCollapsed(role: string = "admin") {
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    const savedState = localStorage.getItem(`sidebar-collapsed-${role}`);
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState));
    }

    // Listen for storage changes to sync across tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === `sidebar-collapsed-${role}` && e.newValue !== null) {
        setIsCollapsed(JSON.parse(e.newValue));
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [role]);

  return { isCollapsed, setIsCollapsed };
}
