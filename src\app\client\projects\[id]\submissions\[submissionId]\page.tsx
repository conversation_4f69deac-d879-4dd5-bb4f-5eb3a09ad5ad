"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Download,
  Eye,
  FileText,
  Image,
  Clock,
  User,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  CheckCircle,
  XCircle,
  RotateCcw,
  AlertTriangle,
  Loader2,
  AlertCircle,
  Calendar,
  Folder
} from "lucide-react";

interface Submission {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'needs_revision' | 'rejected';
  revision_requested: boolean;
  feedback?: string;
  created_at: string;
  updated_at: string;
  project_id: string;
  designer_id: string;
  designer: {
    full_name: string;
    email: string;
    avatar_url?: string;
  };
}

interface SubmissionFile {
  id: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  created_at: string;
}

interface SubmissionFeedback {
  id: string;
  content: string;
  is_approved: boolean;
  created_at: string;
  user: {
    full_name: string;
    role: string;
  };
}

interface Project {
  id: string;
  title: string;
  status: string;
  client_id: string;
  designer: {
    full_name: string;
    email: string;
  } | null;
}

export default function ProjectSubmissionDetailPage() {
  const { id: projectId, submissionId } = useParams();
  const router = useRouter();
  const { user } = useOptimizedAuth();
  
  const [project, setProject] = useState<Project | null>(null);
  const [submission, setSubmission] = useState<Submission | null>(null);
  const [files, setFiles] = useState<SubmissionFile[]>([]);
  const [feedback, setFeedback] = useState<SubmissionFeedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Feedback form
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [feedbackContent, setFeedbackContent] = useState('');
  const [submittingFeedback, setSubmittingFeedback] = useState(false);

  useEffect(() => {
    if (projectId && submissionId && user) {
      fetchSubmissionData();
    }
  }, [projectId, submissionId, user]);

  const fetchSubmissionData = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          status,
          client_id,
          designer:profiles!designer_id(full_name, email)
        `)
        .eq('id', projectId)
        .eq('client_id', user?.id)
        .single();

      if (projectError) throw projectError;
      if (!projectData) {
        setError('Project not found or you do not have access to it');
        return;
      }

      setProject(projectData);

      // Fetch submission details
      const { data: submissionData, error: submissionError } = await supabase
        .from('submissions')
        .select(`
          *,
          designer:profiles!designer_id(full_name, email, avatar_url)
        `)
        .eq('id', submissionId)
        .eq('project_id', projectId)
        .single();

      if (submissionError) throw submissionError;
      if (!submissionData) {
        setError('Submission not found');
        return;
      }

      setSubmission(submissionData);

      // Fetch submission files
      const { data: filesData, error: filesError } = await supabase
        .from('submission_files')
        .select('*')
        .eq('submission_id', submissionId)
        .order('created_at', { ascending: true });

      if (filesError) throw filesError;
      setFiles(filesData || []);

      // Fetch feedback
      const { data: feedbackData, error: feedbackError } = await supabase
        .from('submission_feedback')
        .select(`
          *,
          user:profiles!user_id(full_name, role)
        `)
        .eq('submission_id', submissionId)
        .order('created_at', { ascending: true });

      if (feedbackError) throw feedbackError;
      setFeedback(feedbackData || []);

    } catch (error) {
      console.error('Error fetching submission data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load submission data');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitFeedback = async (isApproved: boolean | null) => {
    if (!feedbackContent.trim() && isApproved === null) {
      return;
    }

    setSubmittingFeedback(true);
    try {
      const { error } = await supabase
        .from('submission_feedback')
        .insert({
          submission_id: submissionId,
          content: feedbackContent.trim() || (isApproved ? 'Design approved' : 'Design needs revisions'),
          is_approved: isApproved || false,
          user_id: user?.id
        });

      if (error) throw error;

      // Update submission status if this is an approval/rejection
      if (isApproved !== null) {
        const newStatus = isApproved ? 'approved' : 'needs_revision';
        const { error: updateError } = await supabase
          .from('submissions')
          .update({ 
            status: newStatus,
            revision_requested: !isApproved,
            feedback: feedbackContent.trim(),
            updated_at: new Date().toISOString()
          })
          .eq('id', submissionId);

        if (updateError) throw updateError;

        // Create notification for designer
        await supabase
          .from('notifications')
          .insert({
            user_id: submission?.designer_id,
            type: 'submission_feedback',
            title: `Submission ${isApproved ? 'Approved' : 'Needs Revision'}`,
            content: `Your submission "${submission?.title}" has been ${isApproved ? 'approved' : 'marked for revision'}. ${feedbackContent}`,
            related_id: submissionId,
            read: false
          });
      }

      // Refresh data
      await fetchSubmissionData();
      
      // Reset form
      setFeedbackContent('');
      setShowFeedbackForm(false);

    } catch (error) {
      console.error('Error submitting feedback:', error);
      setError('Failed to submit feedback');
    } finally {
      setSubmittingFeedback(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'needs_revision':
        return 'bg-orange-100 text-orange-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'needs_revision':
        return <RotateCcw className="h-5 w-5 text-orange-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className="h-5 w-5 text-blue-600" />;
    } else if (fileType === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-600" />;
    } else {
      return <Folder className="h-5 w-5 text-gray-600" />;
    }
  };

  const downloadFile = async (file: SubmissionFile) => {
    try {
      const { data, error } = await supabase.storage
        .from('project-files')
        .download(file.file_path);

      if (error) throw error;

      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.file_name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      setError('Failed to download file');
    }
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Loader2 className="animate-spin h-12 w-12 text-primary mx-auto mb-4" />
          <p className="text-gray-500">Loading submission details...</p>
        </div>
      </div>
    );
  }

  if (error || !project || !submission) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <div>
            <h3 className="font-medium">Error Loading Submission</h3>
            <p className="mt-1">{error || 'Submission not found'}</p>
          </div>
        </div>
        <div className="mt-6 space-x-3">
          <Link href={`/client/projects/${projectId}/submissions`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Submissions
            </Button>
          </Link>
          <Link href={`/client/projects/${projectId}`}>
            <Button variant="outline">
              Back to Project
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <nav className="flex items-center space-x-2 text-sm text-gray-500">
          <Link href={`/client/projects/${projectId}`} className="hover:text-primary">
            {project.title}
          </Link>
          <span>/</span>
          <Link href={`/client/projects/${projectId}/submissions`} className="hover:text-primary">
            Submissions
          </Link>
          <span>/</span>
          <span className="text-gray-900">{submission.title}</span>
        </nav>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Submission Header */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                    {submission.designer.avatar_url ? (
                      <img
                        src={submission.designer.avatar_url}
                        alt={submission.designer.full_name}
                        className="h-12 w-12 rounded-full object-cover"
                      />
                    ) : (
                      <User className="h-6 w-6 text-gray-400" />
                    )}
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold">{submission.title}</h1>
                    <p className="text-gray-500">
                      by {submission.designer.full_name} • {formatDate(submission.created_at)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(submission.status)}
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(submission.status)}`}>
                    {submission.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            <div className="p-6">
              {/* Description */}
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3">Description</h3>
                <p className="text-gray-700 leading-relaxed">{submission.description}</p>
              </div>

              {/* Revision Notice */}
              {submission.revision_requested && (
                <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex items-center mb-2">
                    <RotateCcw className="h-5 w-5 text-orange-600 mr-2" />
                    <span className="font-medium text-orange-800">Revision Requested</span>
                  </div>
                  {submission.feedback && (
                    <p className="text-orange-700">{submission.feedback}</p>
                  )}
                </div>
              )}

              {/* Files */}
              {files.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-3">Attached Files</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {files.map((file) => (
                      <div key={file.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            {getFileIcon(file.file_type)}
                            <span className="ml-2 text-sm font-medium truncate">{file.file_name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => downloadFile(file)}
                              className="text-blue-600 hover:text-blue-700"
                              title="Download"
                            >
                              <Download className="h-4 w-4" />
                            </button>
                            {file.file_type.startsWith('image/') && (
                              <button
                                onClick={() => {
                                  supabase.storage
                                    .from('project-files')
                                    .getPublicUrl(file.file_path)
                                    .then(({ data }) => {
                                      window.open(data.publicUrl, '_blank');
                                    });
                                }}
                                className="text-blue-600 hover:text-blue-700"
                                title="Preview"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        </div>
                        <p className="text-xs text-gray-500">{formatFileSize(file.file_size)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Feedback Section */}
              <div id="feedback">
                <h3 className="text-lg font-medium mb-3">Feedback & Reviews</h3>
                
                {feedback.length > 0 && (
                  <div className="space-y-4 mb-6">
                    {feedback.map((item) => (
                      <div key={item.id} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <span className="text-sm font-medium">{item.user.full_name}</span>
                            <span className="mx-2 text-gray-400">•</span>
                            <span className="text-xs text-gray-500">{formatDate(item.created_at)}</span>
                          </div>
                          {item.is_approved ? (
                            <span className="text-green-600 text-xs font-medium">APPROVED</span>
                          ) : (
                            <span className="text-orange-600 text-xs font-medium">FEEDBACK</span>
                          )}
                        </div>
                        <p className="text-sm text-gray-700">{item.content}</p>
                      </div>
                    ))}
                  </div>
                )}

                {/* Feedback Form */}
                {submission.status === 'pending' && (
                  <div>
                    {!showFeedbackForm ? (
                      <Button 
                        onClick={() => setShowFeedbackForm(true)}
                        className="flex items-center"
                      >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Provide Feedback
                      </Button>
                    ) : (
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="mb-4">
                          <label htmlFor="feedback" className="block text-sm font-medium text-gray-700 mb-2">
                            Your Feedback
                          </label>
                          <textarea
                            id="feedback"
                            rows={4}
                            value={feedbackContent}
                            onChange={(e) => setFeedbackContent(e.target.value)}
                            className="w-full p-3 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                            placeholder="Share your thoughts on this design submission..."
                          />
                        </div>
                        
                        <div className="flex flex-col sm:flex-row gap-3">
                          <Button
                            variant="outline"
                            onClick={() => handleSubmitFeedback(null)}
                            disabled={!feedbackContent.trim() || submittingFeedback}
                          >
                            <MessageSquare className="h-4 w-4 mr-2" />
                            Comment Only
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => handleSubmitFeedback(false)}
                            disabled={submittingFeedback}
                            className="bg-orange-50 text-orange-700 border-orange-300 hover:bg-orange-100"
                          >
                            <ThumbsDown className="h-4 w-4 mr-2" />
                            Request Revisions
                          </Button>
                          <Button
                            onClick={() => handleSubmitFeedback(true)}
                            disabled={submittingFeedback}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            {submittingFeedback ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <ThumbsUp className="h-4 w-4 mr-2" />
                            )}
                            Approve
                          </Button>
                        </div>
                        
                        <div className="mt-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setShowFeedbackForm(false);
                              setFeedbackContent('');
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          {/* Project Info */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold">Project Details</h3>
            </div>
            <div className="p-6">
              <h4 className="font-medium mb-2">{project.title}</h4>
              <p className="text-sm text-gray-500 mb-4">
                Designer: {project.designer?.full_name || 'Not assigned'}
              </p>
              <Link href={`/client/projects/${projectId}`}>
                <Button variant="outline" className="w-full">
                  View Full Project
                </Button>
              </Link>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold">Quick Actions</h3>
            </div>
            <div className="p-6 space-y-3">
              <Link href={`/client/projects/${projectId}/submissions`}>
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  All Submissions
                </Button>
              </Link>
              
              {project.designer && (
                <Link href={`/client/messages?designer=${project.designer.email}`}>
                  <Button variant="outline" className="w-full">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Message Designer
                  </Button>
                </Link>
              )}
              
              <Link href={`/client/projects/${projectId}`}>
                <Button variant="outline" className="w-full">
                  Back to Project
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
