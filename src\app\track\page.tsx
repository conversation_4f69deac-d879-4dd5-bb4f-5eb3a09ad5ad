"use client";

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Search, Clock, CheckCircle, AlertCircle, FileText, Calendar, ArrowRight } from 'lucide-react';
// getTrackingRequest is now handled via API route
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

type TrackingRequest = {
  id: string;
  tracking_number: string;
  request_type: 'sample_request' | 'vision_builder';
  status: string;
  name: string;
  email: string;
  project_type: string;
  description: string;
  service_category: string;
  sample_type: string;
  vision_prompt: string;
  selected_style: string;
  image_url: string;
  file_path: string;
  file_name: string;
  created_at: string;
  updated_at: string;
  completed_at: string;
  notes: string;
};

// Helper function to fetch tracking data via API
const getTrackingRequest = async (trackingNumber: string) => {
  const response = await fetch(`/api/tracking/${trackingNumber}`);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

export default function TrackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const trackingParam = searchParams.get('tracking');

  const [trackingNumber, setTrackingNumber] = useState(trackingParam || '');
  const [trackingRequest, setTrackingRequest] = useState<TrackingRequest | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searched, setSearched] = useState(false);

  const handleSearch = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!trackingNumber.trim()) {
      setError('Please enter a tracking number');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await getTrackingRequest(trackingNumber.trim());
      setTrackingRequest(data as TrackingRequest);
      setSearched(true);

      // Update URL with tracking number
      router.push(`/track?tracking=${trackingNumber.trim()}`);
    } catch (err) {
      console.error('Error fetching tracking request:', err);
      setError('No request found with this tracking number. Please check and try again.');
      setTrackingRequest(null);
    } finally {
      setLoading(false);
    }
  };

  // Auto-search if tracking number is provided in URL
  useEffect(() => {
    if (trackingParam) {
      handleSearch();
    }
  }, [trackingParam]);

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
        return <Clock className="h-5 w-5 text-blue-600" />;
      case 'processing':
        return <FileText className="h-5 w-5 text-yellow-600" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'cancelled':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-10">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Track Your Project</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Enter your tracking number to check the status of your sample request or vision builder submission.
          </p>
        </div>

        <div className="bg-white shadow-lg rounded-lg p-6 mb-10">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label htmlFor="tracking" className="block text-sm font-medium text-gray-700 mb-1">
                Tracking Number
              </label>
              <input
                type="text"
                id="tracking"
                placeholder="Enter your tracking number (e.g., TR-XXXX-XXXX-XXXX)"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
            </div>
            <div className="flex items-end">
              <Button
                type="submit"
                variant="default"
                className="w-full md:w-auto whitespace-nowrap"
                disabled={loading}
              >
                {loading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Searching...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <Search className="mr-2 h-4 w-4" />
                    Track Request
                  </span>
                )}
              </Button>
            </div>
          </form>
        </div>

        <AnimatePresence>
          {searched && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {trackingRequest ? (
                <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                  <div className="p-6 border-b">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div>
                        <h2 className="text-xl font-bold mb-1">
                          {trackingRequest.request_type === 'sample_request' ? 'Sample Request' : 'Vision Builder'} Details
                        </h2>
                        <p className="text-sm text-gray-600">Tracking Number: {trackingRequest.tracking_number}</p>
                      </div>
                      <div className={`px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(trackingRequest.status)}`}>
                        <span className="flex items-center">
                          {getStatusIcon(trackingRequest.status)}
                          <span className="ml-1">{formatStatus(trackingRequest.status)}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Request Type</h3>
                        <p className="font-medium">
                          {trackingRequest.request_type === 'sample_request' ? 'Sample Request' : 'Vision Builder'}
                        </p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Submitted On</h3>
                        <p className="font-medium">{formatDate(trackingRequest.created_at)}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Name</h3>
                        <p className="font-medium">{trackingRequest.name}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Email</h3>
                        <p className="font-medium">{trackingRequest.email}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Project Type</h3>
                        <p className="font-medium">{trackingRequest.project_type || 'Not specified'}</p>
                      </div>

                      {trackingRequest.request_type === 'sample_request' && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Sample Type</h3>
                          <p className="font-medium">{trackingRequest.sample_type || 'Not specified'}</p>
                        </div>
                      )}

                      {trackingRequest.request_type === 'vision_builder' && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Selected Style</h3>
                          <p className="font-medium">{trackingRequest.selected_style || 'Not specified'}</p>
                        </div>
                      )}

                      {trackingRequest.completed_at && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Completed On</h3>
                          <p className="font-medium">{formatDate(trackingRequest.completed_at)}</p>
                        </div>
                      )}
                    </div>

                    <div className="mt-6">
                      <h3 className="text-sm font-medium text-gray-500 mb-1">
                        {trackingRequest.request_type === 'sample_request' ? 'Description' : 'Vision Prompt'}
                      </h3>
                      <p className="text-gray-700 bg-gray-50 p-3 rounded">
                        {trackingRequest.request_type === 'sample_request'
                          ? trackingRequest.description
                          : trackingRequest.vision_prompt}
                      </p>
                    </div>

                    {trackingRequest.notes && (
                      <div className="mt-6">
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Notes from our team</h3>
                        <p className="text-gray-700 bg-gray-50 p-3 rounded">{trackingRequest.notes}</p>
                      </div>
                    )}

                    <div className="mt-8 pt-6 border-t">
                      <h3 className="font-medium mb-4">Request Timeline</h3>
                      <div className="relative pl-8 border-l-2 border-gray-200 space-y-6">
                        <div className="relative">
                          <div className="absolute -left-[25px] mt-1">
                            <div className="bg-blue-500 h-5 w-5 rounded-full flex items-center justify-center">
                              <Calendar className="h-3 w-3 text-white" />
                            </div>
                          </div>
                          <p className="text-sm font-medium">Request Submitted</p>
                          <p className="text-xs text-gray-500">{formatDate(trackingRequest.created_at)}</p>
                        </div>

                        {trackingRequest.status === 'processing' && (
                          <div className="relative">
                            <div className="absolute -left-[25px] mt-1">
                              <div className="bg-yellow-500 h-5 w-5 rounded-full flex items-center justify-center">
                                <FileText className="h-3 w-3 text-white" />
                              </div>
                            </div>
                            <p className="text-sm font-medium">Processing Started</p>
                            <p className="text-xs text-gray-500">{formatDate(trackingRequest.updated_at)}</p>
                          </div>
                        )}

                        {trackingRequest.status === 'completed' && (
                          <div className="relative">
                            <div className="absolute -left-[25px] mt-1">
                              <div className="bg-green-500 h-5 w-5 rounded-full flex items-center justify-center">
                                <CheckCircle className="h-3 w-3 text-white" />
                              </div>
                            </div>
                            <p className="text-sm font-medium">Request Completed</p>
                            <p className="text-xs text-gray-500">{formatDate(trackingRequest.completed_at)}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="mt-8 pt-6 border-t">
                      <h3 className="font-medium mb-4">Need more help?</h3>
                      <p className="text-gray-600 mb-4">
                        If you have any questions about your request or would like to provide additional information, please contact us.
                      </p>
                      <Link href="/contact">
                        <Button variant="outline" className="flex items-center">
                          Contact Support
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white shadow-lg rounded-lg p-6 text-center">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h2 className="text-xl font-bold mb-2">No Request Found</h2>
                  <p className="text-gray-600 mb-6">
                    We couldn't find any request with the tracking number you provided. Please check the number and try again.
                  </p>
                  <Button variant="outline" onClick={() => setSearched(false)}>
                    Try Again
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
