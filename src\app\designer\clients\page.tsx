"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useConnectedClients } from "@/hooks/useDashboardData";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useResponsive } from "@/components/mobile/ResponsiveLayout";
import { MobileCard, MobileListItem, MobileButton } from "@/components/mobile/MobileCard";
import {
  Search,
  Users,
  FolderOpen,
  Mail,
  Phone,
  MessageSquare,
  Calendar,
  User,
  ExternalLink,
  MoreVertical
} from "lucide-react";

type Client = {
  id: string;
  full_name: string;
  email: string;
  phone: string | null;
  company: string | null;
  avatar_url: string | null;
  project_count: number;
  active_projects: number;
  last_interaction: string | null;
};

export default function DesignerClients() {
  const { user } = useOptimizedAuth();
  const { isMobile, isTablet } = useResponsive();
  const [searchQuery, setSearchQuery] = useState("");

  // Use the unified connected clients hook
  const { data: clients = [], isLoading: loading, error } = useConnectedClients(
    user?.id || '',
    'designer'
  );

  // Helper function to format dates
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No recent interaction';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };


  // Filter clients based on search query
  const filteredClients = clients.filter((client: any) => {
    const matchesSearch =
      client.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (client.company && client.company.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesSearch;
  });

  if (loading) {
    return (
      <div className="space-y-4 lg:space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brown-600 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading connected clients...</p>
            <p className={`text-gray-400 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Checking connections, projects, and invitations
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className={`font-bold ${isMobile ? 'text-xl' : 'text-2xl'}`}>
          Clients ({filteredClients.length})
        </h1>
      </div>

      {error && (
        <MobileCard className="bg-red-50 border-red-200">
          <p className="text-red-600 text-sm">{error}</p>
        </MobileCard>
      )}

      {/* Search - Mobile Optimized */}
      <MobileCard padding="sm">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search clients..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={`input-mobile pl-10 w-full ${
                isMobile ? 'text-base' : 'text-sm'
              }`}
            />
          </div>
      </MobileCard>

      {/* No clients state */}
      {clients.length === 0 ? (
        <MobileCard className="text-center" padding="lg">
            <Users className={`text-gray-300 mx-auto mb-4 ${isMobile ? 'h-12 w-12' : 'h-16 w-16'}`} />
            <h2 className={`font-medium mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
              No connected clients found
            </h2>
            <p className={`text-gray-500 mb-4 ${isMobile ? 'text-sm' : 'text-base'}`}>
              You don't have any connected clients yet. Clients will appear here when:
            </p>
            <div className={`text-gray-400 bg-gray-50 p-4 rounded-lg ${isMobile ? 'text-xs' : 'text-sm'}`}>
              <ul className="list-disc list-inside space-y-1 text-left">
                <li>You have active projects with clients</li>
                <li>Clients accept your invitations</li>
                <li>Connections are established in the system</li>
              </ul>
            </div>
            {error && (
              <div className={`mt-4 p-3 bg-red-50 text-red-600 rounded-lg ${isMobile ? 'text-xs' : 'text-sm'}`}>
                <p className="font-medium">Debug Info:</p>
                <p>{error}</p>
              </div>
            )}
        </MobileCard>
      ) : filteredClients.length === 0 ? (
        <MobileCard className="text-center" padding="lg">
            <h2 className={`font-medium mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
              No matching clients found
            </h2>
            <p className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
              Try adjusting your search criteria.
            </p>
        </MobileCard>
      ) : (
        <div className={`grid gap-4 ${
            isMobile
              ? 'grid-cols-1'
              : isTablet
                ? 'grid-cols-2'
                : 'grid-cols-3'
          }`}>
            {filteredClients.map((client: any) => (
              <MobileCard key={client.id} hover className="overflow-hidden">
                {isMobile ? (
                  // Mobile Layout - List Style
                  <MobileListItem
                    leftElement={
                      <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        {client.avatar_url ? (
                          <img
                            src={client.avatar_url}
                            alt={client.full_name}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <User className="h-6 w-6 text-gray-400" />
                        )}
                      </div>
                    }
                    rightElement={
                      <div className="flex items-center space-x-2">
                        <Link href={`/designer/messages?client=${client.id}`}>
                          <MobileButton variant="outline" size="sm">
                            <MessageSquare className="h-4 w-4" />
                          </MobileButton>
                        </Link>
                        <Link href={`/designer/clients/${client.id}`}>
                          <MobileButton variant="primary" size="sm">
                            <ExternalLink className="h-4 w-4" />
                          </MobileButton>
                        </Link>
                      </div>
                    }
                  >
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-base truncate">{client.full_name}</h3>
                      <p className="text-sm text-gray-500 truncate">{client.email}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-400">
                          via {client.source}
                        </span>
                        <span className="text-xs text-gray-400">
                          {formatDate(client.connection_date)}
                        </span>
                      </div>
                    </div>
                  </MobileListItem>
                ) : (
                  // Desktop Layout - Card Style
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mr-4 overflow-hidden">
                        {client.avatar_url ? (
                          <img
                            src={client.avatar_url}
                            alt={client.full_name}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <User className="h-6 w-6 text-gray-400" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-medium text-lg">{client.full_name}</h3>
                        {client.company && (
                          <p className="text-gray-500 text-sm">{client.company}</p>
                        )}
                        <p className="text-xs text-gray-400">
                          Connected via {client.source}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-4 w-4 mr-2 text-gray-400" />
                        <a href={`mailto:${client.email}`} className="hover:text-brown-600 truncate">
                          {client.email}
                        </a>
                      </div>

                      {client.phone && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Phone className="h-4 w-4 mr-2 text-gray-400" />
                          <a href={`tel:${client.phone}`} className="hover:text-brown-600">
                            {client.phone}
                          </a>
                        </div>
                      )}

                      <div className="flex items-center text-sm text-gray-600">
                        <FolderOpen className="h-4 w-4 mr-2 text-gray-400" />
                        <span>
                          Connected {client.connection_status || 'Active'}
                        </span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                        <span>Connected: {formatDate(client.connection_date)}</span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Link href={`/designer/messages?client=${client.id}`} className="flex-1">
                        <MobileButton variant="outline" fullWidth>
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Message
                        </MobileButton>
                      </Link>
                      <Link href={`/designer/clients/${client.id}`} className="flex-1">
                        <MobileButton variant="primary" fullWidth>
                          View Details
                        </MobileButton>
                      </Link>
                    </div>
                  </div>
                )}
              </MobileCard>
            ))}
        </div>
      )}
    </div>
  );
}
