/**
 * Mobile Optimization Utilities
 * Comprehensive mobile experience enhancements
 */

// Touch target size constants (following iOS and Android guidelines)
export const TOUCH_TARGETS = {
  MIN_SIZE: 44, // Minimum touch target size in pixels
  PREFERRED_SIZE: 48, // Preferred touch target size
  SPACING: 8, // Minimum spacing between touch targets
} as const;

// Responsive breakpoints
export const BREAKPOINTS = {
  MOBILE: 640,
  TABLET: 768,
  DESKTOP: 1024,
  LARGE: 1280,
} as const;

// Typography scale for mobile
export const MOBILE_TYPOGRAPHY = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
} as const;

// Mobile spacing scale
export const MOBILE_SPACING = {
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  10: '2.5rem',   // 40px
  12: '3rem',     // 48px
} as const;

/**
 * Check if current device is mobile
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < BREAKPOINTS.TABLET;
}

/**
 * Check if current device is tablet
 */
export function isTabletDevice(): boolean {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= BREAKPOINTS.TABLET && window.innerWidth < BREAKPOINTS.DESKTOP;
}

/**
 * Check if current device supports touch
 */
export function isTouchDevice(): boolean {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * Get device type
 */
export function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
  if (typeof window === 'undefined') return 'desktop';
  
  const width = window.innerWidth;
  if (width < BREAKPOINTS.TABLET) return 'mobile';
  if (width < BREAKPOINTS.DESKTOP) return 'tablet';
  return 'desktop';
}

/**
 * Generate mobile-optimized CSS classes
 */
export function getMobileClasses(options: {
  touchTarget?: boolean;
  spacing?: keyof typeof MOBILE_SPACING;
  typography?: keyof typeof MOBILE_TYPOGRAPHY;
  fullWidth?: boolean;
  centerText?: boolean;
}): string {
  const classes: string[] = [];
  
  if (options.touchTarget) {
    classes.push('min-h-[44px]', 'min-w-[44px]', 'flex', 'items-center', 'justify-center');
  }
  
  if (options.spacing) {
    classes.push(`p-${options.spacing}`);
  }
  
  if (options.typography) {
    classes.push(`text-${options.typography}`);
  }
  
  if (options.fullWidth) {
    classes.push('w-full');
  }
  
  if (options.centerText) {
    classes.push('text-center');
  }
  
  return classes.join(' ');
}

/**
 * Optimize button for mobile
 */
export function optimizeButtonForMobile(element: HTMLElement): void {
  if (!isMobileDevice()) return;
  
  // Ensure minimum touch target size
  const rect = element.getBoundingClientRect();
  if (rect.height < TOUCH_TARGETS.MIN_SIZE) {
    element.style.minHeight = `${TOUCH_TARGETS.MIN_SIZE}px`;
  }
  if (rect.width < TOUCH_TARGETS.MIN_SIZE) {
    element.style.minWidth = `${TOUCH_TARGETS.MIN_SIZE}px`;
  }
  
  // Add touch-friendly padding
  element.style.padding = `${MOBILE_SPACING[3]} ${MOBILE_SPACING[4]}`;
  
  // Improve touch feedback
  element.style.cursor = 'pointer';
  element.style.userSelect = 'none';
  element.style.webkitTapHighlightColor = 'transparent';
}

/**
 * Prevent zoom on input focus (iOS Safari)
 */
export function preventZoomOnInputFocus(): void {
  if (typeof document === 'undefined') return;
  
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach((input) => {
    const element = input as HTMLElement;
    if (element.style.fontSize === '' || parseFloat(element.style.fontSize) < 16) {
      element.style.fontSize = '16px';
    }
  });
}

/**
 * Handle safe area insets for devices with notches
 */
export function applySafeAreaInsets(element: HTMLElement): void {
  if (typeof window === 'undefined') return;
  
  element.style.paddingTop = 'max(env(safe-area-inset-top), 1rem)';
  element.style.paddingBottom = 'max(env(safe-area-inset-bottom), 1rem)';
  element.style.paddingLeft = 'max(env(safe-area-inset-left), 1rem)';
  element.style.paddingRight = 'max(env(safe-area-inset-right), 1rem)';
}

/**
 * Optimize scroll performance
 */
export function optimizeScrollPerformance(element: HTMLElement): void {
  element.style.webkitOverflowScrolling = 'touch';
  element.style.overscrollBehavior = 'contain';
}

/**
 * Add haptic feedback (if supported)
 */
export function addHapticFeedback(type: 'light' | 'medium' | 'heavy' = 'light'): void {
  if (typeof navigator !== 'undefined' && 'vibrate' in navigator) {
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30],
    };
    navigator.vibrate(patterns[type]);
  }
}

/**
 * Optimize table for mobile
 */
export function optimizeTableForMobile(table: HTMLTableElement): void {
  if (!isMobileDevice()) return;
  
  // Add horizontal scroll
  const wrapper = document.createElement('div');
  wrapper.style.overflowX = 'auto';
  wrapper.style.webkitOverflowScrolling = 'touch';
  
  table.parentNode?.insertBefore(wrapper, table);
  wrapper.appendChild(table);
  
  // Ensure minimum column widths
  const cells = table.querySelectorAll('th, td');
  cells.forEach((cell) => {
    const element = cell as HTMLElement;
    element.style.minWidth = '120px';
    element.style.whiteSpace = 'nowrap';
  });
}

/**
 * Create mobile-optimized modal
 */
export function createMobileModal(content: HTMLElement): HTMLElement {
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 z-50 flex items-end justify-center sm:items-center';
  
  const backdrop = document.createElement('div');
  backdrop.className = 'fixed inset-0 bg-black bg-opacity-50';
  
  const panel = document.createElement('div');
  if (isMobileDevice()) {
    panel.className = 'w-full max-h-[90vh] bg-white rounded-t-lg overflow-hidden';
    panel.style.borderTopLeftRadius = '1rem';
    panel.style.borderTopRightRadius = '1rem';
  } else {
    panel.className = 'max-w-md w-full bg-white rounded-lg overflow-hidden';
  }
  
  panel.appendChild(content);
  modal.appendChild(backdrop);
  modal.appendChild(panel);
  
  return modal;
}

/**
 * Optimize form for mobile
 */
export function optimizeFormForMobile(form: HTMLFormElement): void {
  if (!isMobileDevice()) return;
  
  const inputs = form.querySelectorAll('input, select, textarea');
  inputs.forEach((input) => {
    const element = input as HTMLElement;
    
    // Prevent zoom on focus
    element.style.fontSize = '16px';
    
    // Optimize input types for mobile keyboards
    if (element.getAttribute('type') === 'email') {
      element.setAttribute('inputmode', 'email');
    } else if (element.getAttribute('type') === 'tel') {
      element.setAttribute('inputmode', 'tel');
    } else if (element.getAttribute('type') === 'number') {
      element.setAttribute('inputmode', 'numeric');
    }
    
    // Add mobile-friendly spacing
    element.style.padding = `${MOBILE_SPACING[3]} ${MOBILE_SPACING[4]}`;
    element.style.marginBottom = MOBILE_SPACING[4];
  });
  
  // Optimize buttons
  const buttons = form.querySelectorAll('button');
  buttons.forEach((button) => {
    optimizeButtonForMobile(button);
  });
}

/**
 * Add pull-to-refresh functionality
 */
export function addPullToRefresh(
  container: HTMLElement,
  onRefresh: () => Promise<void>
): () => void {
  if (!isTouchDevice()) return () => {};
  
  let startY = 0;
  let currentY = 0;
  let isRefreshing = false;
  
  const handleTouchStart = (e: TouchEvent) => {
    if (container.scrollTop === 0) {
      startY = e.touches[0].clientY;
    }
  };
  
  const handleTouchMove = (e: TouchEvent) => {
    if (container.scrollTop === 0 && !isRefreshing) {
      currentY = e.touches[0].clientY;
      const pullDistance = currentY - startY;
      
      if (pullDistance > 0) {
        e.preventDefault();
        container.style.transform = `translateY(${Math.min(pullDistance * 0.5, 100)}px)`;
      }
    }
  };
  
  const handleTouchEnd = async () => {
    const pullDistance = currentY - startY;
    
    if (pullDistance > 100 && !isRefreshing) {
      isRefreshing = true;
      container.style.transform = 'translateY(50px)';
      
      try {
        await onRefresh();
      } finally {
        isRefreshing = false;
        container.style.transform = 'translateY(0)';
        container.style.transition = 'transform 0.3s ease';
        setTimeout(() => {
          container.style.transition = '';
        }, 300);
      }
    } else {
      container.style.transform = 'translateY(0)';
    }
    
    startY = 0;
    currentY = 0;
  };
  
  container.addEventListener('touchstart', handleTouchStart, { passive: false });
  container.addEventListener('touchmove', handleTouchMove, { passive: false });
  container.addEventListener('touchend', handleTouchEnd);
  
  return () => {
    container.removeEventListener('touchstart', handleTouchStart);
    container.removeEventListener('touchmove', handleTouchMove);
    container.removeEventListener('touchend', handleTouchEnd);
  };
}

/**
 * Initialize mobile optimizations
 */
export function initializeMobileOptimizations(): void {
  if (typeof document === 'undefined') return;
  
  // Prevent zoom on input focus
  preventZoomOnInputFocus();
  
  // Optimize existing buttons
  const buttons = document.querySelectorAll('button');
  buttons.forEach((button) => {
    optimizeButtonForMobile(button as HTMLElement);
  });
  
  // Optimize existing forms
  const forms = document.querySelectorAll('form');
  forms.forEach((form) => {
    optimizeFormForMobile(form as HTMLFormElement);
  });
  
  // Optimize existing tables
  const tables = document.querySelectorAll('table');
  tables.forEach((table) => {
    optimizeTableForMobile(table as HTMLTableElement);
  });
  
  // Add viewport meta tag if missing
  if (!document.querySelector('meta[name="viewport"]')) {
    const viewport = document.createElement('meta');
    viewport.name = 'viewport';
    viewport.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
    document.head.appendChild(viewport);
  }
}
