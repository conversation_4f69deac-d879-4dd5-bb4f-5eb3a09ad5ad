"use client";

import { useState, useRef } from "react";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowRight,
  Sparkles,
  Save,
  Share2,
  Download,
  Loader2,
  Home,
  Building2,
  Factory,
  TreePine,
  Waves,
  Mountain,
  ChevronDown,
  ChevronUp,
  Mail,
  FileDown,
  Upload,
  Send,
  Check,
  Heart,
  Eye
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

// Example style presets for the AI generator
const stylePresets = [
  { id: "modern", name: "Modern Minimalist", icon: <Home className="h-6 w-6" /> },
  { id: "traditional", name: "Traditional", icon: <Building2 className="h-6 w-6" /> },
  { id: "industrial", name: "Industrial", icon: <Factory className="h-6 w-6" /> },
  { id: "scandinavian", name: "Scandinavian", icon: <TreePine className="h-6 w-6" /> },
  { id: "mediterranean", name: "Mediterranean", icon: <Waves className="h-6 w-6" /> },
  { id: "japanese", name: "Japanese", icon: <Mountain className="h-6 w-6" /> },
];

// Example space types
const spaceTypes = [
  { id: "residential", name: "Residential" },
  { id: "commercial", name: "Commercial" },
  { id: "office", name: "Office" },
  { id: "landscape", name: "Landscape" },
  { id: "interior", name: "Interior" },
  { id: "exterior", name: "Exterior" },
];

export default function VisionBuilderPage() {
  const [prompt, setPrompt] = useState("");
  const [selectedStyle, setSelectedStyle] = useState("");
  const [selectedSpace, setSelectedSpace] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [showSignupPrompt, setShowSignupPrompt] = useState(false);
  const [showMoreActions, setShowMoreActions] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [savedImages, setSavedImages] = useState<number[]>([]);

  const promptInputRef = useRef<HTMLTextAreaElement>(null);
  
  // Example function to generate images (would connect to Krea AI or similar API)
  const generateImages = async () => {
    if (!prompt) return;
    
    setIsGenerating(true);
    
    try {
      // This would be replaced with actual API call to Krea AI
      // For demo purposes, we'll simulate a delay and use placeholder images
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Example response with placeholder images
      const newImages = [
        "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
        "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2075&q=80",
        "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2053&q=80",
        "https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      ];
      
      setGeneratedImages(newImages);
      
      // After generating 3 sets of images, show signup prompt
      if (generatedImages.length > 8) {
        setShowSignupPrompt(true);
      }
    } catch (error) {
      console.error("Error generating images:", error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Build the complete prompt with style and space type
  const getFullPrompt = () => {
    let fullPrompt = prompt;
    
    if (selectedStyle) {
      const style = stylePresets.find(s => s.id === selectedStyle);
      if (style) {
        fullPrompt += ` in ${style.name} style`;
      }
    }
    
    if (selectedSpace) {
      const space = spaceTypes.find(s => s.id === selectedSpace);
      if (space) {
        fullPrompt += ` for a ${space.name} space`;
      }
    }
    
    return fullPrompt;
  };
  
  // Focus the prompt input when a style or space is selected
  const focusPromptInput = () => {
    if (promptInputRef.current) {
      promptInputRef.current.focus();
    }
  };

  // Handle image save/unsave
  const toggleSaveImage = (index: number) => {
    setSavedImages(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  // Handle quick actions
  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'download':
        // Implement download logic
        console.log('Downloading image...');
        break;
      case 'share':
        // Implement share logic
        console.log('Sharing image...');
        break;
      case 'save':
        toggleSaveImage(selectedImageIndex);
        break;
      default:
        break;
    }
  };

  return (
    <Layout>
      {/* Hero Section - Mobile Optimized */}
      <section className="relative min-h-[50vh] sm:min-h-[55vh] md:min-h-[60vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <div
            className="absolute inset-0 bg-black bg-opacity-50 z-10"
            aria-hidden="true"
          />
          <Image
            src="https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="AI-powered architectural visualization"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
        </div>
        <div className="container mx-auto px-4 z-20 pt-24 sm:pt-28 md:pt-32 lg:pt-20 pb-8">
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight">
            Build Your Vision
          </h1>
          <p className="text-lg sm:text-xl text-white max-w-2xl leading-relaxed">
            Use the power of AI to visualize your architectural ideas with simple text prompts.
          </p>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            {/* Prompt Builder */}
            <div className="bg-white shadow-xl p-8 mb-12">
              <h2 className="text-2xl font-bold mb-6">Describe Your Vision</h2>
              
              {/* Style Selection */}
              <div className="mb-8">
                <h3 className="text-lg font-medium mb-4">Select an Architectural Style</h3>

                {/* Desktop: Grid Layout */}
                <div className="hidden md:grid md:grid-cols-3 lg:grid-cols-6 gap-4">
                  {stylePresets.map((style) => (
                    <motion.div
                      key={style.id}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`cursor-pointer p-4 rounded-lg border-2 transition-all duration-300 ${
                        selectedStyle === style.id
                          ? 'border-primary bg-primary/5 shadow-lg'
                          : 'border-gray-200 hover:border-primary/50 hover:shadow-md'
                      }`}
                      onClick={() => {
                        setSelectedStyle(style.id);
                        focusPromptInput();
                      }}
                    >
                      <div className="flex flex-col items-center text-center space-y-2">
                        <div className={`p-3 rounded-full transition-colors ${
                          selectedStyle === style.id ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                        }`}>
                          {style.icon}
                        </div>
                        <p className={`text-sm font-medium transition-colors ${
                          selectedStyle === style.id ? 'text-primary' : 'text-gray-700'
                        }`}>
                          {style.name}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Mobile: Horizontal Scroll */}
                <div className="md:hidden">
                  <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 px-4 -mx-4">
                    {stylePresets.map((style) => (
                      <div key={style.id} className="flex-shrink-0 w-32 snap-center mr-4 last:mr-0 first:ml-4">
                        <motion.div
                          whileTap={{ scale: 0.95 }}
                          className={`cursor-pointer p-3 rounded-lg border-2 transition-all duration-300 h-full ${
                            selectedStyle === style.id
                              ? 'border-primary bg-primary/5'
                              : 'border-gray-200'
                          }`}
                          onClick={() => {
                            setSelectedStyle(style.id);
                            focusPromptInput();
                          }}
                        >
                          <div className="flex flex-col items-center text-center space-y-2">
                            <div className={`p-2 rounded-full transition-colors ${
                              selectedStyle === style.id ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                            }`}>
                              {style.icon}
                            </div>
                            <p className={`text-xs font-medium transition-colors leading-tight ${
                              selectedStyle === style.id ? 'text-primary' : 'text-gray-700'
                            }`}>
                              {style.name}
                            </p>
                          </div>
                        </motion.div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Space Type Selection */}
              <div className="mb-8">
                <h3 className="text-lg font-medium mb-4">What type of space?</h3>
                <div className="flex flex-wrap gap-2">
                  {spaceTypes.map((space) => (
                    <button
                      key={space.id}
                      className={`px-4 py-2 rounded-full text-sm transition-colors ${
                        selectedSpace === space.id 
                          ? 'bg-primary text-white' 
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
                      }`}
                      onClick={() => {
                        setSelectedSpace(space.id);
                        focusPromptInput();
                      }}
                    >
                      {space.name}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Text Prompt */}
              <div className="mb-6">
                <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
                  Describe your architectural vision in detail
                </label>
                <textarea
                  ref={promptInputRef}
                  id="prompt"
                  rows={4}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Example: A modern beach house with large windows, open floor plan, and sustainable materials..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                ></textarea>
              </div>
              
              {/* Preview of full prompt */}
              {(prompt || selectedStyle || selectedSpace) && (
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Full prompt:</span> {getFullPrompt()}
                  </p>
                </div>
              )}
              
              {/* Generate Button */}
              <div className="flex justify-center">
                <Button
                  variant="default"
                  size="lg"
                  className="group"
                  onClick={generateImages}
                  disabled={!prompt || isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      Generate with AI <Sparkles className="ml-2 h-5 w-5" />
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            {/* Generated Images - Improved Version */}
            {generatedImages.length > 0 && (
              <div className="mb-16">
                                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-2xl font-bold">Your Vision, Visualized</h2>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Eye className="h-4 w-4" />
                    <span>Image {selectedImageIndex + 1} of {generatedImages.length}</span>
                  </div>
                </div>

                {/* Main Image with Floating Actions */}
                <div className="relative bg-white shadow-xl rounded-lg overflow-hidden mb-8">
                  <div className="aspect-[4/3] relative group">
                    <img
                      src={generatedImages[selectedImageIndex]}
                      alt={`Generated design ${selectedImageIndex + 1}`}
                      className="object-cover w-full h-full"
                    />
                    
                    {/* Floating Action Bar */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="bg-black/80 backdrop-blur-sm rounded-full px-6 py-3 flex items-center space-x-4 shadow-lg">
                        <motion.button 
                          className="text-white hover:text-primary transition-colors p-2 rounded-full hover:bg-white/10"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => handleQuickAction('download')}
                          title="Download"
                        >
                          <Download className="h-5 w-5" />
                        </motion.button>
                        <motion.button 
                          className="text-white hover:text-primary transition-colors p-2 rounded-full hover:bg-white/10"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => handleQuickAction('share')}
                          title="Share"
                        >
                          <Share2 className="h-5 w-5" />
                        </motion.button>
                        <motion.button 
                          className={`transition-colors p-2 rounded-full hover:bg-white/10 ${
                            savedImages.includes(selectedImageIndex) 
                              ? 'text-red-400 hover:text-red-300' 
                              : 'text-white hover:text-primary'
                          }`}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => handleQuickAction('save')}
                          title={savedImages.includes(selectedImageIndex) ? "Unsave" : "Save"}
                        >
                          <Heart className={`h-5 w-5 ${savedImages.includes(selectedImageIndex) ? 'fill-current' : ''}`} />
                        </motion.button>
                      </div>
                    </div>

                    {/* Image Quality Badge */}
                    <div className="absolute top-4 right-4">
                      <div className="bg-black/60 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full">
                        HD Quality
                      </div>
                    </div>

                    {/* Save Indicator */}
                    {savedImages.includes(selectedImageIndex) && (
                      <motion.div 
                        className="absolute top-4 left-4"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      >
                        <div className="bg-red-500 text-white p-2 rounded-full shadow-lg">
                          <Heart className="h-4 w-4 fill-current" />
                        </div>
                      </motion.div>
                    )}
                  </div>

                  {/* Primary Actions - Always Visible */}
                  <div className="p-6 border-b border-gray-100">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <Button size="lg" className="w-full group">
                        <Send className="mr-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                        Submit to Architects
                      </Button>
                      <Button variant="outline" size="lg" className="w-full group">
                        <Download className="mr-2 h-5 w-5 group-hover:translate-y-1 transition-transform" />
                        Download HD Image
                      </Button>
                    </div>
                    
                    {/* Action Status */}
                    <div className="mt-4 flex items-center justify-center space-x-6 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span>Ready to submit</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <span>4K resolution available</span>
                      </div>
                    </div>
                  </div>

                  {/* Secondary Actions - Expandable */}
                  <div className="p-6">
                    <button
                      onClick={() => setShowMoreActions(!showMoreActions)}
                      className="flex items-center justify-between w-full text-left text-gray-600 hover:text-gray-900 transition-colors group"
                    >
                      <span className="font-medium flex items-center">
                        <span>More Options</span>
                        <span className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded-full">
                          {showMoreActions ? 'Hide' : '6 options'}
                        </span>
                      </span>
                      <motion.div
                        animate={{ rotate: showMoreActions ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronDown className="h-4 w-4 group-hover:text-primary transition-colors" />
                      </motion.div>
                    </button>
                    
                    <AnimatePresence>
                      {showMoreActions && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-3">
                            {[
                              { icon: FileDown, label: "Export PDF", action: "pdf" },
                              { icon: Mail, label: "Email Link", action: "email" },
                              { icon: Upload, label: "Save to Cloud", action: "cloud" },
                              { icon: Share2, label: "Social Share", action: "social" },
                              { icon: Save, label: "Add to Board", action: "board" },
                              { icon: Eye, label: "Full Screen", action: "fullscreen" }
                            ].map((option, index) => (
                              <motion.div
                                key={option.action}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.05 }}
                              >
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="justify-start w-full hover:bg-gray-50 group"
                                  onClick={() => console.log(`Action: ${option.action}`)}
                                >
                                  <option.icon className="mr-2 h-4 w-4 group-hover:text-primary transition-colors" />
                                  {option.label}
                                </Button>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>

                {/* Alternative Images - Improved Grid */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold">Alternative Options</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={generateImages}
                      disabled={isGenerating}
                      className="group"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="mr-2 h-4 w-4 group-hover:text-primary transition-colors" />
                          Generate More
                        </>
                      )}
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {generatedImages.map((image, index) => (
                      <motion.div
                        key={`alt-image-${index}`}
                        className={`cursor-pointer rounded-lg overflow-hidden transition-all relative group ${
                          selectedImageIndex === index
                            ? 'ring-2 ring-primary shadow-lg scale-105'
                            : 'hover:shadow-md hover:scale-102'
                        }`}
                        onClick={() => setSelectedImageIndex(index)}
                        whileHover={{ y: -2 }}
                        layout
                      >
                        <div className="aspect-[4/3] relative">
                          <img
                            src={image}
                            alt={`Alternative design ${index + 1}`}
                            className="object-cover w-full h-full"
                          />
                          
                          {/* Hover Overlay */}
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300" />
                          
                          {/* Selection Indicator */}
                          {selectedImageIndex === index && (
                            <motion.div 
                              className="absolute inset-0 bg-primary/10 flex items-center justify-center"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 0.2 }}
                            >
                              <div className="bg-primary text-white rounded-full p-2 shadow-lg">
                                <Check className="h-4 w-4" />
                              </div>
                            </motion.div>
                          )}

                          {/* Save Button on Hover */}
                          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleSaveImage(index);
                              }}
                              className={`p-2 rounded-full backdrop-blur-sm transition-colors ${
                                savedImages.includes(index)
                                  ? 'bg-red-500/80 text-white'
                                  : 'bg-black/60 text-white hover:bg-black/80'
                              }`}
                            >
                              <Heart className={`h-3 w-3 ${savedImages.includes(index) ? 'fill-current' : ''}`} />
                            </button>
                          </div>

                          {/* Image Number */}
                          <div className="absolute bottom-2 left-2">
                            <div className="bg-black/60 backdrop-blur-sm text-white text-xs px-2 py-1 rounded">
                              #{index + 1}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Generation Stats */}
                  <div className="mt-6 flex items-center justify-center space-x-8 text-sm text-gray-500">
                    <div className="flex items-center space-x-2">
                      <Sparkles className="h-4 w-4" />
                      <span>{generatedImages.length} images generated</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Heart className="h-4 w-4" />
                      <span>{savedImages.length} saved</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Eye className="h-4 w-4" />
                      <span>HD quality</span>
                    </div>
                  </div>
                </div>

                {/* Next Steps CTA */}
                <motion.div 
                  className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-xl p-8 text-center border border-primary/10"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <div className="max-w-2xl mx-auto">
                    <h3 className="text-2xl font-semibold mb-4 text-gray-900">Love what you see?</h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      Take the next step and work with our professional architects to bring your vision to life. 
                      Get detailed blueprints, 3D models, and construction-ready plans.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button size="lg" className="group">
                        Schedule Consultation
                        <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </Button>
                      <Button variant="outline" size="lg" className="group">
                        <Save className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                        Save & Continue Later
                      </Button>
                    </div>
                    
                    {/* Trust Indicators */}
                    <div className="mt-6 flex items-center justify-center space-x-6 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span>Free consultation</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <span>Licensed architects</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <span>30+ years experience</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            )}
            
            {/* Signup Prompt */}
            <AnimatePresence>
              {showSignupPrompt && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="bg-gradient-to-r from-primary/10 to-primary/5 p-8 rounded-xl border border-primary/20 text-center mb-16"
                >
                  <div className="max-w-2xl mx-auto">
                    <div className="mb-4">
                      <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
                        <Sparkles className="h-8 w-8 text-primary" />
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold mb-4">Ready to take your vision further?</h3>
                    <p className="text-gray-700 mb-6 leading-relaxed">
                      Create an account to save your designs, get expert feedback from our architects, 
                      and start turning your vision into reality with unlimited generations.
                    </p>
                    <div className="flex flex-col sm:flex-row justify-center gap-4">
                      <Link href="/auth/signup">
                        <Button variant="default" size="lg" className="w-full sm:w-auto group">
                          Create Free Account
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </Link>
                      <Button 
                        variant="outline" 
                        size="lg" 
                        onClick={() => setShowSignupPrompt(false)}
                        className="w-full sm:w-auto"
                      >
                        Continue Exploring
                      </Button>
                    </div>
                    
                    {/* Benefits List */}
                    <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center justify-center space-x-2 text-gray-600">
                        <Check className="h-4 w-4 text-green-500" />
                        <span>Unlimited generations</span>
                      </div>
                      <div className="flex items-center justify-center space-x-2 text-gray-600">
                        <Check className="h-4 w-4 text-green-500" />
                        <span>Save & organize designs</span>
                      </div>
                      <div className="flex items-center justify-center space-x-2 text-gray-600">
                        <Check className="h-4 w-4 text-green-500" />
                        <span>Expert consultations</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* How It Works */}
            <div className="mt-16">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">How It Works</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Transform your architectural ideas into stunning visualizations in just three simple steps
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  {
                    step: 1,
                    title: "Describe Your Vision",
                    description: "Use natural language to describe the architectural space you're imagining. Add details about style, materials, and atmosphere.",
                    icon: <Sparkles className="h-8 w-8" />,
                    color: "bg-blue-500"
                  },
                  {
                    step: 2,
                    title: "Generate Visualizations",
                    description: "Our AI transforms your text description into photorealistic architectural visualizations, giving you multiple design options.",
                    icon: <Eye className="h-8 w-8" />,
                    color: "bg-purple-500"
                  },
                  {
                    step: 3,
                    title: "Refine & Collaborate",
                    description: "Save your favorite designs, share them with our architects, and start the journey of bringing your vision to life.",
                    icon: <ArrowRight className="h-8 w-8" />,
                    color: "bg-green-500"
                  }
                ].map((item, index) => (
                  <motion.div
                    key={item.step}
                    className="bg-white p-8 shadow-lg rounded-xl border border-gray-100 hover:shadow-xl transition-shadow duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex items-center mb-6">
                      <div className={`w-12 h-12 ${item.color} rounded-full flex items-center justify-center text-white mr-4`}>
                        <span className="font-bold text-xl">{item.step}</span>
                      </div>
                      <div className={`w-12 h-12 ${item.color}/10 rounded-full flex items-center justify-center text-gray-700`}>
                        {item.icon}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-3">{item.title}</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {item.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </div>
            
            {/* FAQ Section */}
            <div className="mt-16">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Everything you need to know about our AI-powered vision builder
                </p>
              </div>
              
              <div className="space-y-6 max-w-3xl mx-auto">
                {[
                  {
                    question: "How accurate are the AI-generated visualizations?",
                    answer: "The AI generates conceptual visualizations that capture the essence of your description. While they're not construction-ready blueprints, they're excellent for exploring ideas and communicating your vision to our architects. The images are photorealistic and provide a strong foundation for architectural development."
                  },
                  {
                    question: "Can I use these images for my project?",
                    answer: "Yes! The images you generate are yours to use. They're perfect for concept boards, sharing ideas with stakeholders, or briefing our architectural team on your vision. You can download them in high resolution and use them commercially."
                  },
                  {
                    question: "What happens after I find a design I like?",
                    answer: "Once you've generated designs you love, you can save them to your account and schedule a consultation with our architects. We'll discuss how to develop your concept into detailed architectural plans, including blueprints, 3D models, and construction documents."
                  },
                  {
                    question: "How many images can I generate?",
                    answer: "Free users can generate up to 12 images per day. With a free account, you get unlimited generations, the ability to save designs, and access to premium features like higher resolution exports and priority support."
                  }
                ].map((faq, index) => (
                  <motion.div
                    key={index}
                    className="bg-white p-6 shadow-md rounded-lg border border-gray-100 hover:shadow-lg transition-shadow duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <h3 className="text-lg font-bold mb-3 text-gray-900">{faq.question}</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {faq.answer}
                    </p>
                  </motion.div>
                ))}
              </div>
            </div>
            
            {/* Final CTA Section */}
            <motion.div 
              className="mt-16 bg-gradient-to-r from-primary to-primary/80 p-8 rounded-xl text-center text-white"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="max-w-2xl mx-auto">
                <h2 className="text-3xl font-bold mb-4">Ready to Work with Our Architects?</h2>
                <p className="text-primary-100 mb-6 leading-relaxed">
                  Take the next step and connect with our team of experienced architects who can help bring your vision to life with professional plans and expert guidance.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/contact">
                    <Button variant="secondary" size="lg" className="w-full sm:w-auto group">
                      Schedule a Consultation
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                  <Link href="/projects">
                    <Button variant="outline" size="lg" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-primary">
                      View Our Work
                    </Button>
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
