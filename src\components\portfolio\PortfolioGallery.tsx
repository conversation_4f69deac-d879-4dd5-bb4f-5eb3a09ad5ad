'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { getPortfolioImages, deletePortfolioImage, updatePortfolioImage } from '@/lib/api/portfolio';
import { PortfolioImage } from '@/types/portfolio';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { StarIcon, PencilIcon, TrashIcon, XIcon, CheckIcon } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface PortfolioGalleryProps {
  projectId: string;
  isOwner?: boolean;
  onImagesChange?: () => void;
}

export function PortfolioGallery({
  projectId,
  isOwner = false,
  onImagesChange,
}: PortfolioGalleryProps) {
  const { token } = useAuth();
  const [images, setImages] = useState<PortfolioImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<PortfolioImage | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingImageId, setEditingImageId] = useState<string | null>(null);
  const [editCaption, setEditCaption] = useState('');
  const [editIsCover, setEditIsCover] = useState(false);

  useEffect(() => {
    if (!token) return;

    fetchImages();
  }, [token, projectId]);

  const fetchImages = async () => {
    if (!token) return;

    setLoading(true);
    try {
      const fetchedImages = await getPortfolioImages(token, projectId);
      setImages(fetchedImages);
    } catch (error) {
      console.error('Error fetching portfolio images:', error);
      toast({
        title: 'Error',
        description: 'Failed to load project images',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (image: PortfolioImage) => {
    setSelectedImage(image);
  };

  const handleClosePreview = () => {
    setSelectedImage(null);
  };

  const handleDeleteClick = (image: PortfolioImage) => {
    setSelectedImage(image);
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!token || !selectedImage) return;

    try {
      await deletePortfolioImage(token, selectedImage.id);

      setImages(prev => prev.filter(img => img.id !== selectedImage.id));
      toast({
        title: 'Image Deleted',
        description: 'The image has been removed from the project',
      });

      if (onImagesChange) {
        onImagesChange();
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete image',
        variant: 'destructive',
      });
    } finally {
      setShowDeleteDialog(false);
      setSelectedImage(null);
    }
  };

  const handleEditClick = (image: PortfolioImage) => {
    setEditingImageId(image.id);
    setEditCaption(image.caption || '');
    setEditIsCover(image.is_cover);
  };

  const handleSaveEdit = async () => {
    if (!token || !editingImageId) return;

    try {
      const updatedImage = await updatePortfolioImage(token, editingImageId, {
        caption: editCaption,
        is_cover: editIsCover,
      });

      setImages(prev => prev.map(img =>
        img.id === editingImageId ? updatedImage : img
      ));

      toast({
        title: 'Image Updated',
        description: 'The image details have been updated',
      });

      if (onImagesChange) {
        onImagesChange();
      }
    } catch (error) {
      console.error('Error updating image:', error);
      toast({
        title: 'Error',
        description: 'Failed to update image',
        variant: 'destructive',
      });
    } finally {
      setEditingImageId(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingImageId(null);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3, 4, 5, 6].map(i => (
          <Skeleton key={i} className="aspect-video w-full rounded-md" />
        ))}
      </div>
    );
  }

  if (images.length === 0) {
    return (
      <div className="text-center py-12 border rounded-md">
        <p className="text-muted-foreground">No images have been added to this project yet.</p>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {images.map(image => (
          <div key={image.id} className="relative group rounded-md overflow-hidden">
            <div
              className="aspect-video cursor-pointer"
              onClick={() => handleImageClick(image)}
            >
              <Image
                src={image.image_url}
                alt={image.caption || 'Project image'}
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
            </div>

            {image.is_cover && (
              <div className="absolute top-2 right-2">
                <div className="bg-amber-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                  <StarIcon className="h-3 w-3 mr-1" />
                  Cover
                </div>
              </div>
            )}

            {isOwner && editingImageId !== image.id && (
              <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                <Button
                  variant="secondary"
                  size="icon"
                  className="h-8 w-8 bg-white/80 hover:bg-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditClick(image);
                  }}
                >
                  <PencilIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-8 w-8 bg-white/80 hover:bg-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteClick(image);
                  }}
                >
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>
            )}

            {isOwner && editingImageId === image.id && (
              <div className="absolute inset-0 bg-background/95 p-4 flex flex-col">
                <div className="space-y-4 flex-1">
                  <div className="space-y-2">
                    <Label htmlFor={`caption-${image.id}`}>Caption</Label>
                    <Input
                      id={`caption-${image.id}`}
                      value={editCaption}
                      onChange={(e) => setEditCaption(e.target.value)}
                      placeholder="Add a caption"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`cover-${image.id}`}
                      checked={editIsCover}
                      onCheckedChange={setEditIsCover}
                    />
                    <Label htmlFor={`cover-${image.id}`}>Use as cover image</Label>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelEdit}
                  >
                    <XIcon className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleSaveEdit}
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Image Preview Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4" onClick={handleClosePreview}>
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            <div className="relative h-full" onClick={(e) => e.stopPropagation()}>
              <Image
                src={selectedImage.image_url}
                alt={selectedImage.caption || 'Project image'}
                fill
                className="object-contain"
              />

              {selectedImage.caption && (
                <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-4">
                  <p>{selectedImage.caption}</p>
                </div>
              )}

              <Button
                variant="secondary"
                size="icon"
                className="absolute top-2 right-2 h-8 w-8 rounded-full"
                onClick={handleClosePreview}
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this image from your project.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
