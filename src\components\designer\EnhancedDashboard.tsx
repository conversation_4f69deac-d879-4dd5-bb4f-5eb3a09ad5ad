"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  DollarSign,
  FolderKanban,
  Users,
  Mail,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  Eye
} from "lucide-react";
import { AdminTasks } from "./AdminTasks";
import { WorkWithClients } from "./WorkWithClients";
import { PortfolioSection } from "./PortfolioSection";
import { AdminMessages } from "./AdminMessages";
import { ProjectAgreementFlow } from "./ProjectAgreementFlow";
import { EnhancedCommunication } from "./EnhancedCommunication";

interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalEarnings: number;
  pendingEarnings: number;
  totalClients: number;
  invitationsSent: number;
  averageRating: number;
}

interface Project {
  id: string;
  title: string;
  status: string;
  client_name: string;
  progress: number;
  due_date: string;
}

interface Client {
  id: string;
  full_name: string;
  avatar_url: string | null;
  company: string | null;
  last_project_date: string;
}

export function EnhancedDashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalEarnings: 0,
    pendingEarnings: 0,
    totalClients: 0,
    invitationsSent: 0,
    averageRating: 4.8
  });
  const [recentProjects, setRecentProjects] = useState<Project[]>([]);
  const [recentClients, setRecentClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchStats(),
        fetchRecentProjects(),
        fetchRecentClients()
      ]);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Fetch projects
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('id, status, created_at')
        .eq('designer_id', user?.id);

      if (projectsError) throw projectsError;

      // Fetch earnings from transactions
      const { data: transactions, error: transactionsError } = await supabase
        .from('transactions')
        .select('amount, status, type')
        .eq('designer_id', user?.id)
        .eq('type', 'payout');

      if (transactionsError) throw transactionsError;

      // Fetch clients count
      const { data: connections, error: connectionsError } = await supabase
        .from('connections')
        .select('client_id')
        .eq('designer_id', user?.id)
        .eq('status', 'active');

      if (connectionsError) throw connectionsError;

      // Fetch invitations
      const { data: invitations, error: invitationsError } = await supabase
        .from('invitations')
        .select('id, status')
        .eq('created_by', user?.id)
        .eq('role', 'client');

      if (invitationsError) throw invitationsError;

      // Calculate stats
      const totalProjects = projects?.length || 0;
      const activeProjects = projects?.filter(p => ['in_progress', 'review'].includes(p.status)).length || 0;
      const completedProjects = projects?.filter(p => p.status === 'completed').length || 0;

      const totalEarnings = transactions?.filter(t => t.status === 'completed').reduce((sum, t) => sum + t.amount, 0) || 0;
      const pendingEarnings = transactions?.filter(t => t.status === 'pending').reduce((sum, t) => sum + t.amount, 0) || 0;

      const totalClients = connections?.length || 0;
      const invitationsSent = invitations?.length || 0;

      setStats({
        totalProjects,
        activeProjects,
        completedProjects,
        totalEarnings,
        pendingEarnings,
        totalClients,
        invitationsSent,
        averageRating: 4.8 // This would come from reviews in a real implementation
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchRecentProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          status,
          progress,
          due_date,
          profiles!client_id(full_name)
        `)
        .eq('designer_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      const projects = (data || []).map(project => ({
        id: project.id,
        title: project.title,
        status: project.status,
        client_name: project.profiles?.full_name || 'Unknown Client',
        progress: project.progress || 0,
        due_date: project.due_date
      }));

      setRecentProjects(projects);
    } catch (error) {
      console.error('Error fetching recent projects:', error);
    }
  };

  const fetchRecentClients = async () => {
    try {
      const { data, error } = await supabase
        .from('connections')
        .select(`
          client_id,
          created_at,
          profiles!client_id(
            id,
            full_name,
            avatar_url,
            company
          )
        `)
        .eq('designer_id', user?.id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(6);

      if (error) throw error;

      const clients = (data || []).map(connection => ({
        id: connection.profiles?.id || '',
        full_name: connection.profiles?.full_name || 'Unknown Client',
        avatar_url: connection.profiles?.avatar_url || null,
        company: connection.profiles?.company || null,
        last_project_date: connection.created_at
      }));

      setRecentClients(clients);
    } catch (error) {
      console.error('Error fetching recent clients:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'in_progress':
        return 'text-blue-600 bg-blue-50';
      case 'review':
        return 'text-yellow-600 bg-yellow-50';
      case 'draft':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'in_progress':
        return <Clock className="h-4 w-4" />;
      case 'review':
        return <Eye className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="bg-white p-6 rounded-lg shadow-sm border animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Earnings</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalEarnings.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Projects</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeProjects}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <FolderKanban className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Clients</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalClients}</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-full">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Invitations</p>
              <p className="text-2xl font-bold text-gray-900">{stats.invitationsSent}</p>
            </div>
            <div className="p-3 bg-orange-50 rounded-full">
              <Mail className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Admin Tasks and Work with Clients */}
        <div className="lg:col-span-2 space-y-6">
          {/* Admin Tasks */}
          <AdminTasks />

          {/* Work with Clients */}
          <WorkWithClients />

          {/* Project Agreement Flow */}
          <ProjectAgreementFlow />

          {/* Portfolio Section */}
          <PortfolioSection />
        </div>

        {/* Right Column - Projects Overview and Clients */}
        <div className="space-y-6">
          {/* Projects Overview */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="bg-white rounded-lg shadow-sm border"
          >
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">Projects Overview</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total Projects</span>
                  <span className="text-lg font-semibold">{stats.totalProjects}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Active</span>
                  <span className="text-lg font-semibold text-blue-600">{stats.activeProjects}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Completed</span>
                  <span className="text-lg font-semibold text-green-600">{stats.completedProjects}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Invitations</span>
                  <span className="text-lg font-semibold text-purple-600">{stats.invitationsSent}</span>
                </div>
                <div className="flex items-center justify-between pt-2 border-t">
                  <span className="text-sm text-gray-600">Total Earnings</span>
                  <span className="text-lg font-semibold text-green-600">${stats.totalEarnings.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* My Projects Table */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="bg-white rounded-lg shadow-sm border"
          >
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">My Projects</h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                {recentProjects.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-gray-500 text-sm">No projects yet</p>
                  </div>
                ) : (
                  recentProjects.map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 text-sm truncate">{project.title}</h4>
                        <p className="text-xs text-gray-600 truncate">{project.client_name}</p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                          {getStatusIcon(project.status)}
                          <span className="ml-1 capitalize">{project.status.replace('_', ' ')}</span>
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </motion.div>

          {/* Clients */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
            className="bg-white rounded-lg shadow-sm border"
          >
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">Clients</h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                {recentClients.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-gray-500 text-sm">No clients yet</p>
                  </div>
                ) : (
                  recentClients.slice(0, 4).map((client) => (
                    <div key={client.id} className="flex items-center space-x-3 p-2">
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        {client.avatar_url ? (
                          <img
                            src={client.avatar_url}
                            alt={client.full_name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <span className="text-xs font-medium text-gray-600">
                            {client.full_name.split(' ').map(n => n[0]).join('')}
                          </span>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{client.full_name}</p>
                        <p className="text-xs text-gray-500 truncate">{client.company || 'Individual'}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </motion.div>

          {/* Admin Messages */}
          <AdminMessages />

          {/* Enhanced Communication */}
          <EnhancedCommunication />
        </div>
      </div>
    </div>
  );
}
