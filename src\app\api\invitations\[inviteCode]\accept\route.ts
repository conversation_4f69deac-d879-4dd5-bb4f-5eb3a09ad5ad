import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/invitations/[inviteCode]/accept
 * Accepts an invitation
 *
 * Request body:
 * {
 *   userId: string; // ID of the user accepting the invitation
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { inviteCode: string } }
) {
  try {
    const inviteCode = params.inviteCode;
    const { userId } = await request.json();

    if (!inviteCode) {
      return NextResponse.json(
        { error: 'Invite code is required' },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check if the invitation exists and is pending
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select('*')
      .eq('invite_code', inviteCode)
      .eq('status', 'pending')
      .gt('expires_at', new Date().toISOString())
      .single();

    if (invitationError) {
      console.error('Error finding invitation:', invitationError);
      return NextResponse.json(
        { error: 'Invalid or expired invitation' },
        { status: 404 }
      );
    }

    // Update the invitation status
    const { data, error } = await supabase
      .from('invitations')
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString(),
        accepted_by: userId
      })
      .eq('id', invitation.id) // Use the invitation ID for more precise targeting
      .select()
      .single();

    if (error) {
      console.error('Error accepting invitation:', error);
      return NextResponse.json(
        { error: 'Failed to accept invitation' },
        { status: 500 }
      );
    }

    console.log('Invitation accepted successfully:', data);

    // Create a connection between the designer and client
    if (invitation.role === 'client') {
      // The inviter is a designer, and the accepter is a client
      const connectionData = {
        designer_id: invitation.created_by,
        client_id: userId,
        created_by: invitation.created_by,
        invitation_id: invitation.id,
        status: 'active'
      };

      console.log('Creating connection with data:', connectionData);

      const { data: connection, error: connectionError } = await supabase
        .from('connections')
        .insert(connectionData)
        .select()
        .single();

      if (connectionError) {
        console.error('Error creating connection:', connectionError);
        // Log more details but don't fail the request
        return NextResponse.json({
          invitation: data,
          warning: 'Invitation accepted but connection creation failed',
          error: connectionError.message
        }, { status: 200 });
      }

      console.log('Connection created successfully:', connection);

      // Create notification for the designer
      await supabase
        .from('notifications')
        .insert({
          user_id: invitation.created_by,
          type: 'connection',
          title: 'New Client Connection',
          content: 'A client has accepted your invitation and is now connected with you.',
          read: false
        });

    } else if (invitation.role === 'designer') {
      // The inviter is a client or admin, and the accepter is a designer
      const connectionData = {
        designer_id: userId,
        client_id: invitation.created_by,
        created_by: invitation.created_by,
        invitation_id: invitation.id,
        status: 'active'
      };

      console.log('Creating connection with data:', connectionData);

      const { data: connection, error: connectionError } = await supabase
        .from('connections')
        .insert(connectionData)
        .select()
        .single();

      if (connectionError) {
        console.error('Error creating connection:', connectionError);
        // Log more details but don't fail the request
        return NextResponse.json({
          invitation: data,
          warning: 'Invitation accepted but connection creation failed',
          error: connectionError.message
        }, { status: 200 });
      }

      console.log('Connection created successfully:', connection);

      // Create notification for the client
      await supabase
        .from('notifications')
        .insert({
          user_id: invitation.created_by,
          type: 'connection',
          title: 'New Designer Connection',
          content: 'A designer has accepted your invitation and is now connected with you.',
          read: false
        });
    }

    return NextResponse.json({ invitation: data, success: true }, { status: 200 });
  } catch (error) {
    console.error('Error accepting invitation:', error);
    return NextResponse.json(
      { error: 'Failed to accept invitation' },
      { status: 500 }
    );
  }
}
