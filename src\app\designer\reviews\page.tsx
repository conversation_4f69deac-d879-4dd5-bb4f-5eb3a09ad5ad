"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  Star,
  TrendingUp,
  Award,
  MessageSquare,
  Calendar,
  User,
  ThumbsUp,
  Filter,
  Download,
  BarChart3
} from "lucide-react";

interface Review {
  id: string;
  project_title: string;
  client_name: string;
  client_avatar: string | null;
  rating: number;
  communication_rating: number;
  quality_rating: number;
  timeliness_rating: number;
  review_text: string;
  would_recommend: boolean;
  created_at: string;
}

interface RatingStats {
  overall_rating: number;
  total_reviews: number;
  communication_avg: number;
  quality_avg: number;
  timeliness_avg: number;
  recommendation_rate: number;
  rating_distribution: { [key: number]: number };
}

export default function DesignerReviews() {
  const { user } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<RatingStats>({
    overall_rating: 0,
    total_reviews: 0,
    communication_avg: 0,
    quality_avg: 0,
    timeliness_avg: 0,
    recommendation_rate: 0,
    rating_distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  });
  const [loading, setLoading] = useState(true);
  const [filterRating, setFilterRating] = useState<number | null>(null);

  useEffect(() => {
    if (user) {
      fetchReviews();
    }
  }, [user]);

  const fetchReviews = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Mock data for now - will be replaced with real data from project_reviews table
      const mockReviews: Review[] = [
        {
          id: '1',
          project_title: 'Modern Kitchen Renovation',
          client_name: 'Sarah Johnson',
          client_avatar: null,
          rating: 5,
          communication_rating: 5,
          quality_rating: 5,
          timeliness_rating: 4,
          review_text: 'Absolutely amazing work! The designer exceeded all my expectations. The kitchen looks stunning and the attention to detail is incredible. Communication was excellent throughout the project.',
          would_recommend: true,
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          project_title: 'Office Space Design',
          client_name: 'Tech Corp',
          client_avatar: null,
          rating: 4,
          communication_rating: 4,
          quality_rating: 5,
          timeliness_rating: 4,
          review_text: 'Great work on our office redesign. The space is much more functional and looks professional. Minor delays but overall very satisfied.',
          would_recommend: true,
          created_at: '2024-01-10T14:30:00Z'
        },
        {
          id: '3',
          project_title: 'Living Room Makeover',
          client_name: 'Mike Chen',
          client_avatar: null,
          rating: 5,
          communication_rating: 5,
          quality_rating: 5,
          timeliness_rating: 5,
          review_text: 'Perfect execution from start to finish. The designer understood our vision completely and delivered exactly what we wanted. Highly recommend!',
          would_recommend: true,
          created_at: '2024-01-05T09:15:00Z'
        }
      ];

      setReviews(mockReviews);

      // Calculate stats
      const totalReviews = mockReviews.length;
      const overallRating = mockReviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
      const communicationAvg = mockReviews.reduce((sum, review) => sum + review.communication_rating, 0) / totalReviews;
      const qualityAvg = mockReviews.reduce((sum, review) => sum + review.quality_rating, 0) / totalReviews;
      const timelinessAvg = mockReviews.reduce((sum, review) => sum + review.timeliness_rating, 0) / totalReviews;
      const recommendationRate = (mockReviews.filter(review => review.would_recommend).length / totalReviews) * 100;

      const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      mockReviews.forEach(review => {
        ratingDistribution[review.rating]++;
      });

      setStats({
        overall_rating: overallRating,
        total_reviews: totalReviews,
        communication_avg: communicationAvg,
        quality_avg: qualityAvg,
        timeliness_avg: timelinessAvg,
        recommendation_rate: recommendationRate,
        rating_distribution: ratingDistribution
      });
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    };

    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 30) return `${diffInDays} days ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  const filteredReviews = filterRating 
    ? reviews.filter(review => review.rating === filterRating)
    : reviews;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reviews & Ratings</h1>
          <p className="text-gray-600">Track your performance and client feedback</p>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Rating</p>
              <div className="flex items-center mt-2">
                <span className="text-2xl font-bold text-gray-900">{stats.overall_rating.toFixed(1)}</span>
                <div className="ml-2">
                  {renderStars(Math.round(stats.overall_rating))}
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">{stats.total_reviews} reviews</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-full">
              <Star className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Communication</p>
              <div className="flex items-center mt-2">
                <span className="text-2xl font-bold text-gray-900">{stats.communication_avg.toFixed(1)}</span>
                <div className="ml-2">
                  {renderStars(Math.round(stats.communication_avg))}
                </div>
              </div>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <MessageSquare className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Quality</p>
              <div className="flex items-center mt-2">
                <span className="text-2xl font-bold text-gray-900">{stats.quality_avg.toFixed(1)}</span>
                <div className="ml-2">
                  {renderStars(Math.round(stats.quality_avg))}
                </div>
              </div>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <Award className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Recommendation Rate</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{stats.recommendation_rate.toFixed(0)}%</p>
              <p className="text-xs text-gray-500 mt-1">Would recommend</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-full">
              <ThumbsUp className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Rating Distribution */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Rating Distribution</h3>
        <div className="space-y-3">
          {[5, 4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center">
              <span className="text-sm font-medium w-8">{rating}</span>
              <Star className="h-4 w-4 text-yellow-400 fill-current mx-2" />
              <div className="flex-1 bg-gray-200 rounded-full h-2 mx-3">
                <div
                  className="bg-yellow-400 h-2 rounded-full"
                  style={{
                    width: `${stats.total_reviews > 0 ? (stats.rating_distribution[rating] / stats.total_reviews) * 100 : 0}%`
                  }}
                ></div>
              </div>
              <span className="text-sm text-gray-600 w-8">{stats.rating_distribution[rating]}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Recent Reviews</h3>
            <div className="flex items-center space-x-3">
              <select
                value={filterRating || ''}
                onChange={(e) => setFilterRating(e.target.value ? parseInt(e.target.value) : null)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              >
                <option value="">All Ratings</option>
                <option value="5">5 Stars</option>
                <option value="4">4 Stars</option>
                <option value="3">3 Stars</option>
                <option value="2">2 Stars</option>
                <option value="1">1 Star</option>
              </select>
            </div>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredReviews.length === 0 ? (
            <div className="p-8 text-center">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No reviews yet</h3>
              <p className="text-gray-500">Reviews from completed projects will appear here</p>
            </div>
          ) : (
            filteredReviews.map((review) => (
              <motion.div
                key={review.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="p-6"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {review.client_avatar ? (
                      <img
                        src={review.client_avatar}
                        alt={review.client_name}
                        className="h-10 w-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                        <User className="h-5 w-5 text-gray-500" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-gray-900">{review.client_name}</h4>
                        <p className="text-sm text-gray-500">{review.project_title}</p>
                      </div>
                      <div className="text-right">
                        {renderStars(review.rating)}
                        <p className="text-xs text-gray-500 mt-1">{getTimeAgo(review.created_at)}</p>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 mb-3">{review.review_text}</p>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center">
                        <span className="text-gray-500 mr-2">Communication:</span>
                        {renderStars(review.communication_rating, 'sm')}
                      </div>
                      <div className="flex items-center">
                        <span className="text-gray-500 mr-2">Quality:</span>
                        {renderStars(review.quality_rating, 'sm')}
                      </div>
                      <div className="flex items-center">
                        <span className="text-gray-500 mr-2">Timeliness:</span>
                        {renderStars(review.timeliness_rating, 'sm')}
                      </div>
                    </div>
                    
                    {review.would_recommend && (
                      <div className="flex items-center mt-3 text-green-600">
                        <ThumbsUp className="h-4 w-4 mr-2" />
                        <span className="text-sm">Would recommend</span>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
