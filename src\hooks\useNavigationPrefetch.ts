"use client";

import { useCallback, useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useOptimizedAuth } from './useOptimizedAuth';
import { dashboardKeys } from './useDashboardData';
import { supabase } from '@/lib/supabase';

// Navigation prefetching hook for instant page transitions
export function useNavigationPrefetch() {
  const queryClient = useQueryClient();
  const { user, profile } = useOptimizedAuth();
  const prefetchedRoutes = useRef<Set<string>>(new Set());

  // Prefetch data for a specific route
  const prefetchRoute = useCallback(async (route: string) => {
    if (!user || !profile || prefetchedRoutes.current.has(route)) return;

    const role = profile.role;
    const userId = user.id;

    try {
      switch (route) {
        case 'dashboard':
          // Already handled by main prefetch hook
          break;

        case 'projects':
          await queryClient.prefetchQuery({
            queryKey: dashboardKeys.projectsByUser(userId, role),
            queryFn: async () => {
              let query = supabase.from('projects').select(`
                *,
                client:profiles!projects_client_id_fkey(full_name, email),
                designer:profiles!projects_designer_id_fkey(full_name, email)
              `);

              if (role === 'client') query = query.eq('client_id', userId);
              else if (role === 'designer') query = query.eq('designer_id', userId);

              const { data, error } = await query.order('created_at', { ascending: false });
              if (error) throw error;
              return data;
            },
            staleTime: 10 * 60 * 1000,
          });
          break;

        case 'proposals':
          await queryClient.prefetchQuery({
            queryKey: dashboardKeys.proposalsByUser(userId, role),
            queryFn: async () => {
              let query = supabase.from('proposals').select(`
                *,
                project:projects(title, client_id),
                designer:profiles!proposals_designer_id_fkey(full_name, email)
              `);

              if (role === 'designer') query = query.eq('designer_id', userId);
              else if (role === 'client') query = query.eq('project.client_id', userId);

              const { data, error } = await query.order('created_at', { ascending: false });
              if (error) throw error;
              return data;
            },
            staleTime: 10 * 60 * 1000,
          });
          break;

        case 'briefs':
          if (role === 'client' || role === 'designer') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.briefsByUser(userId, role),
              queryFn: async () => {
                let query = supabase.from('project_briefs').select(`
                  id, title, description, budget_range, timeline_preference,
                  urgency, status, assigned_designer_id, created_at,
                  profiles!project_briefs_assigned_designer_id_fkey(full_name)
                `);

                if (role === 'client') query = query.eq('client_id', userId);
                else if (role === 'designer') query = query.eq('assigned_designer_id', userId);

                const { data, error } = await query.order('created_at', { ascending: false });
                if (error) throw error;
                return data;
              },
              staleTime: 10 * 60 * 1000,
            });
          }
          break;

        case 'designers':
          if (role === 'client' || role === 'admin') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.availableDesigners(),
              queryFn: async () => {
                const { data, error } = await supabase
                  .from('profiles')
                  .select('id, full_name, avatar_url, skills')
                  .eq('role', 'designer')
                  .eq('is_active', true)
                  .order('created_at', { ascending: false });

                if (error) throw error;
                return data;
              },
              staleTime: 15 * 60 * 1000,
            });
          }
          break;

        case 'users':
          if (role === 'admin') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.usersByRole('all'),
              queryFn: async () => {
                const { data, error } = await supabase
                  .from('profiles')
                  .select('id, full_name, email, role, created_at, last_sign_in_at, avatar_url, is_active')
                  .order('created_at', { ascending: false });

                if (error) throw error;
                return data;
              },
              staleTime: 10 * 60 * 1000,
            });
          }
          break;

        case 'messages':
          // Use new unified messaging system instead of old messages table
          await queryClient.prefetchQuery({
            queryKey: ['conversations', userId],
            queryFn: async () => {
              const response = await fetch('/api/conversations', {
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
                }
              });

              if (!response.ok) throw new Error('Failed to fetch conversations');
              return response.json();
            },
            staleTime: 2 * 60 * 1000, // Messages need fresher data
          });
          break;

        case 'portfolio':
          if (role === 'designer') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.portfolioByDesigner(userId),
              queryFn: async () => {
                const { data, error } = await supabase
                  .from('portfolio_projects')
                  .select(`
                    id, title, category, featured, completion_date,
                    portfolio_images(id, image_url, is_cover)
                  `)
                  .eq('designer_id', userId)
                  .order('featured', { ascending: false })
                  .limit(20);

                if (error) throw error;
                return data;
              },
              staleTime: 10 * 60 * 1000,
            });
          }
          break;

        case 'admin-messages':
          if (role === 'designer') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.adminMessages(userId),
              queryFn: async () => {
                const { data, error } = await supabase
                  .from('admin_messages')
                  .select(`
                    id, title, content, message_type, priority,
                    expires_at, created_at, read_at, action_required
                  `)
                  .or(`recipient_id.eq.${userId},recipient_id.is.null`)
                  .order('priority', { ascending: false })
                  .limit(20);

                if (error) throw error;
                return data;
              },
              staleTime: 5 * 60 * 1000,
            });
          }
          break;

        case 'availability':
          if (role === 'designer') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.availability(userId),
              queryFn: async () => {
                const { data, error } = await supabase
                  .from('designer_availability')
                  .select('*')
                  .eq('designer_id', userId)
                  .single();

                if (error && error.code !== 'PGRST116') throw error;
                return data;
              },
              staleTime: 5 * 60 * 1000,
            });
          }
          break;

        case 'reviews':
          if (role === 'designer') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.reviewsByDesigner(userId),
              queryFn: async () => {
                const { data, error } = await supabase
                  .from('project_reviews')
                  .select(`
                    id, rating, review_text, created_at,
                    project:projects(title),
                    client:profiles!client_id(full_name, avatar_url)
                  `)
                  .eq('designer_id', userId)
                  .order('created_at', { ascending: false })
                  .limit(20);

                if (error) throw error;
                return data;
              },
              staleTime: 10 * 60 * 1000,
            });
          }
          break;

        case 'payments':
          await queryClient.prefetchQuery({
            queryKey: dashboardKeys.paymentsByUser(userId),
            queryFn: async () => {
              let query = supabase
                .from('transactions')
                .select(`
                  id, amount, status, type, description, created_at,
                  project:projects(title)
                `)
                .order('created_at', { ascending: false })
                .limit(20);

              if (role === 'client') query = query.eq('client_id', userId);
              else if (role === 'designer') query = query.eq('designer_id', userId);

              const { data, error } = await query;
              if (error) throw error;
              return data;
            },
            staleTime: 5 * 60 * 1000,
          });
          break;

        case 'inspirations':
          if (role === 'client') {
            await queryClient.prefetchQuery({
              queryKey: dashboardKeys.inspirationsByUser(userId),
              queryFn: async () => {
                const { data, error } = await supabase
                  .from('inspiration_boards')
                  .select(`
                    id, title, description, is_public, created_at,
                    inspiration_items(id, image_url, title)
                  `)
                  .eq('client_id', userId)
                  .order('created_at', { ascending: false })
                  .limit(10);

                if (error) throw error;
                return data;
              },
              staleTime: 10 * 60 * 1000,
            });
          }
          break;

        case 'profile':
          await queryClient.prefetchQuery({
            queryKey: dashboardKeys.profile(userId),
            queryFn: async () => {
              const { data, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', userId)
                .single();

              if (error) throw error;
              return data;
            },
            staleTime: 30 * 60 * 1000, // Profile data is stable
          });
          break;
      }

      prefetchedRoutes.current.add(route);
    } catch (error) {
      console.warn(`Failed to prefetch route ${route}:`, error);
    }
  }, [user, profile, queryClient]);

  // Enhanced prefetch all navigation routes with complete coverage
  const prefetchAllRoutes = useCallback(async () => {
    if (!profile) return;

    const routes = {
      admin: [
        'dashboard', 'projects', 'proposals', 'users', 'designers',
        'applications', 'messages', 'finance', 'payments', 'analytics',
        'settings', 'profile'
      ],
      designer: [
        'dashboard', 'projects', 'proposals', 'briefs', 'clients',
        'portfolio', 'messages', 'admin-messages', 'availability',
        'reviews', 'settings', 'profile'
      ],
      client: [
        'dashboard', 'projects', 'proposals', 'briefs', 'designers',
        'messages', 'inspirations', 'disputes', 'payments', 'profile'
      ]
    };

    const roleRoutes = routes[profile.role] || [];

    // Prefetch critical routes immediately (dashboard, projects, messages)
    const criticalRoutes = ['dashboard', 'projects', 'messages'];
    const otherRoutes = roleRoutes.filter(route => !criticalRoutes.includes(route));

    // Prefetch critical routes first
    for (const route of criticalRoutes) {
      if (roleRoutes.includes(route)) {
        prefetchRoute(route);
      }
    }

    // Prefetch other routes with staggered timing
    for (let i = 0; i < otherRoutes.length; i++) {
      setTimeout(() => {
        prefetchRoute(otherRoutes[i]);
      }, (i + 1) * 150); // 150ms delay between each prefetch
    }
  }, [profile, prefetchRoute]);

  // Hover-based prefetching
  const prefetchOnHover = useCallback((route: string) => {
    // Debounce hover prefetching
    const timeoutId = setTimeout(() => {
      prefetchRoute(route);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [prefetchRoute]);

  // Intersection observer based prefetching
  const prefetchOnIntersection = useCallback((route: string, element: Element) => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            prefetchRoute(route);
            observer.unobserve(element);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [prefetchRoute]);

  // Initialize prefetching
  useEffect(() => {
    if (user && profile) {
      // Clear prefetched routes when user changes
      prefetchedRoutes.current.clear();
      
      // Start prefetching after a short delay
      const timeoutId = setTimeout(prefetchAllRoutes, 500);
      
      return () => clearTimeout(timeoutId);
    }
  }, [user, profile, prefetchAllRoutes]);

  return {
    prefetchRoute,
    prefetchOnHover,
    prefetchOnIntersection,
    prefetchAllRoutes,
  };
}

// Hook for smart route transitions
export function useSmartRouteTransition() {
  const queryClient = useQueryClient();

  const preloadRoute = useCallback(async (route: string) => {
    // Mark route as being loaded
    queryClient.setQueryData(['route-loading', route], true);

    try {
      // Simulate route preparation
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Mark route as ready
      queryClient.setQueryData(['route-ready', route], true);
    } catch (error) {
      console.warn(`Failed to preload route ${route}:`, error);
    } finally {
      queryClient.setQueryData(['route-loading', route], false);
    }
  }, [queryClient]);

  const isRouteReady = useCallback((route: string) => {
    return queryClient.getQueryData(['route-ready', route]) === true;
  }, [queryClient]);

  const isRouteLoading = useCallback((route: string) => {
    return queryClient.getQueryData(['route-loading', route]) === true;
  }, [queryClient]);

  return {
    preloadRoute,
    isRouteReady,
    isRouteLoading,
  };
}
