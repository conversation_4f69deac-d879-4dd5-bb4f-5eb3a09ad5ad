"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CreditCard, ArrowLeft } from 'lucide-react';
import { AddPaymentMethodWrapper } from './AddPaymentMethodWrapper';
import { PayPalAccountForm } from './PayPalAccountForm';

interface PaymentMethodSelectorProps {
  onSuccess: () => void;
  onCancel: () => void;
}

type PaymentMethodType = 'card' | 'paypal' | null;

export function PaymentMethodSelector({ onSuccess, onCancel }: PaymentMethodSelectorProps) {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethodType>(null);

  const handleBack = () => {
    setSelectedMethod(null);
  };

  if (selectedMethod === 'card') {
    return (
      <div>
        <div className="mb-4">
          <Button 
            variant="ghost" 
            className="flex items-center text-gray-600" 
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to payment methods
          </Button>
        </div>
        <AddPaymentMethodWrapper 
          onSuccess={onSuccess} 
          onCancel={handleBack} 
        />
      </div>
    );
  }

  if (selectedMethod === 'paypal') {
    return (
      <div>
        <div className="mb-4">
          <Button 
            variant="ghost" 
            className="flex items-center text-gray-600" 
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to payment methods
          </Button>
        </div>
        <PayPalAccountForm 
          onSuccess={onSuccess} 
          onCancel={handleBack} 
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Select Payment Method Type</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div 
          className="border rounded-lg p-6 hover:border-brown-600 hover:bg-brown-50 cursor-pointer transition-colors"
          onClick={() => setSelectedMethod('card')}
        >
          <div className="flex items-center mb-4">
            <CreditCard className="h-8 w-8 text-brown-600 mr-3" />
            <h4 className="text-lg font-medium">Credit Card</h4>
          </div>
          <p className="text-gray-600">
            Add a credit or debit card to your account for secure payments.
          </p>
        </div>
        
        <div 
          className="border rounded-lg p-6 hover:border-brown-600 hover:bg-brown-50 cursor-pointer transition-colors"
          onClick={() => setSelectedMethod('paypal')}
        >
          <div className="flex items-center mb-4">
            <div className="mr-3 text-[#003087] font-bold text-2xl">
              Pay<span className="text-[#009cde]">Pal</span>
            </div>
          </div>
          <p className="text-gray-600">
            Link your PayPal account for fast and secure checkout.
          </p>
        </div>
      </div>
      
      <div className="flex justify-end">
        <Button 
          variant="outline" 
          onClick={onCancel}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}
