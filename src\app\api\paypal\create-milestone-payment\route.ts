import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const PAYPAL_API_BASE = process.env.NODE_ENV === 'production' 
  ? 'https://api-m.paypal.com' 
  : 'https://api-m.sandbox.paypal.com';

// Token cache for PayPal access tokens
let cachedToken: string | null = null;
let tokenExpiry: number = 0;

// Optimized function to get PayPal access token with caching
async function getPayPalAccessToken() {
  // Return cached token if still valid
  if (cachedToken && Date.now() < tokenExpiry) {
    return cachedToken;
  }

  const clientId = process.env.PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('PayPal credentials are not configured');
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
  
  const response = await fetch(`${PAYPAL_API_BASE}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${auth}`
    },
    body: 'grant_type=client_credentials'
  });

  if (!response.ok) {
    const errorData = await response.json();
    console.error('PayPal auth error:', errorData);
    throw new Error('Failed to authenticate with PayPal');
  }

  const data = await response.json();
  
  // Cache the token (expires in 9 hours, we'll cache for 8 hours for safety)
  cachedToken = data.access_token;
  tokenExpiry = Date.now() + (8 * 60 * 60 * 1000); // 8 hours
  
  return cachedToken;
}

export async function POST(request: Request) {
  try {
    // Get request data
    const {
      milestoneId,
      projectId,
      clientId,
      amount,
      description
    } = await request.json();

    // Validate required fields
    if (!milestoneId || !projectId || !clientId || !amount || !description) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Initialize Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          persistSession: false
        }
      }
    );

    // Run database queries and PayPal auth in parallel for better performance
    const [
      { data: clientProfile, error: clientError },
      { data: projectData, error: projectError },
      { data: milestoneData, error: milestoneError },
      accessToken
    ] = await Promise.all([
      supabase
        .from('profiles')
        .select('full_name, email')
        .eq('id', clientId)
        .single(),
      supabase
        .from('projects')
        .select('title, designer_id')
        .eq('id', projectId)
        .single(),
      supabase
        .from('project_milestones')
        .select('title, amount, status')
        .eq('id', milestoneId)
        .single(),
      getPayPalAccessToken()
    ]);

    if (clientError) {
      console.error('Error fetching client profile:', clientError);
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      );
    }

    if (projectError) {
      console.error('Error fetching project:', projectError);
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    if (milestoneError) {
      console.error('Error fetching milestone:', milestoneError);
      return NextResponse.json(
        { error: 'Milestone not found' },
        { status: 404 }
      );
    }

    // Create PayPal order for milestone payment
    const orderResponse = await fetch(`${PAYPAL_API_BASE}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        intent: 'CAPTURE',
        purchase_units: [
          {
            reference_id: milestoneId,
            description: `${description} - ${milestoneData.title}`,
            custom_id: JSON.stringify({
              projectId,
              milestoneId,
              clientId,
              paymentType: 'milestone'
            }),
            amount: {
              currency_code: 'USD',
              value: (amount / 100).toFixed(2) // Convert cents to dollars
            }
          }
        ],
        application_context: {
          brand_name: 'Seniors Archi Firm',
          shipping_preference: 'NO_SHIPPING',
          user_action: 'PAY_NOW',
          return_url: `${process.env.FRONTEND_URL}/client/projects/${projectId}?payment=success&milestone=${milestoneId}`,
          cancel_url: `${process.env.FRONTEND_URL}/client/projects/${projectId}?payment=cancelled&milestone=${milestoneId}`
        }
      })
    });

    if (!orderResponse.ok) {
      const errorData = await orderResponse.json();
      console.error('PayPal order creation error:', errorData);
      return NextResponse.json(
        { error: 'Failed to create PayPal payment order' },
        { status: 500 }
      );
    }

    const orderData = await orderResponse.json();
    
    // Create a pending transaction record
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        amount: amount / 100, // Convert cents to dollars
        type: 'payment',
        status: 'pending',
        payment_method: 'paypal',
        paypal_order_id: orderData.id,
        project_id: projectId,
        client_id: clientId,
        milestone_id: milestoneId,
        description: `Milestone payment: ${milestoneData.title}`,
        metadata: {
          paypal_order_id: orderData.id,
          milestone_title: milestoneData.title,
          project_title: projectData.title
        }
      });

    if (transactionError) {
      console.error('Error creating transaction record:', transactionError);
      // Don't fail the request, just log the error
    }
    
    // Return the order data with approval URL
    const approvalUrl = orderData.links?.find((link: any) => link.rel === 'approve')?.href;
    
    return NextResponse.json({
      id: orderData.id,
      status: orderData.status,
      approvalUrl,
      amount: amount / 100,
      description: `${description} - ${milestoneData.title}`,
      milestone: {
        id: milestoneId,
        title: milestoneData.title,
        amount: milestoneData.amount
      },
      project: {
        id: projectId,
        title: projectData.title
      }
    });

  } catch (error: unknown) {
    console.error('Error creating milestone PayPal payment:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
