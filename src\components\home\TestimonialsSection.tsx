"use client";

import { useState, useRef, useEffect } from "react";
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight, Quote } from "lucide-react";


interface Testimonial {
  quote: string;
  author: string;
  position: string;
  image: string;
  company?: string;
  projectType?: string;
}

const testimonials: Testimonial[] = [
  {
    quote: "Senior's Archi-firm transformed our vision into a stunning reality that exceeded our expectations. Their attention to detail and innovative approach created a space that truly reflects our values.",
    author: "<PERSON>",
    position: "CEO",
    company: "Horizon Developments",
    projectType: "Commercial Office Building",
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
  },
  {
    quote: "Working with this team was a seamless experience from concept to completion. Their ability to balance aesthetic beauty with functional design has created a workspace that inspires our team every day.",
    author: "<PERSON>",
    position: "Founder",
    company: "Innovate Spaces",
    projectType: "Tech Campus Redesign",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
  },
  {
    quote: "The team's cultural sensitivity and deep understanding of our community's needs resulted in a public space that has become the heart of our neighborhood. Their collaborative approach made all the difference.",
    author: "Amara Okafor",
    position: "Director",
    company: "Community Arts Foundation",
    projectType: "Cultural Center",
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=776&q=80"
  }
];

const TestimonialsSection = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [direction, setDirection] = useState(0); // -1 for left, 1 for right, 0 for initial
  const sectionRef = useRef(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.5, 1, 1, 0.5]);
  const y = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [50, 0, 0, 50]);

  // Auto-rotate testimonials
  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setDirection(1);
      setActiveIndex((prev) => (prev + 1) % testimonials.length);
    }, 8000);

    return () => {
      if (intervalRef.current !== null) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Reset interval when manually changing testimonial
  const handleDotClick = (index: number): void => {
    setDirection(index > activeIndex ? 1 : -1);
    setActiveIndex(index);
    resetInterval();
  };

  const nextTestimonial = () => {
    setDirection(1);
    setActiveIndex((prev) => (prev + 1) % testimonials.length);
    resetInterval();
  };

  const prevTestimonial = () => {
    setDirection(-1);
    setActiveIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    resetInterval();
  };

  const resetInterval = () => {
    if (intervalRef.current !== null) {
      clearInterval(intervalRef.current);
    }
    intervalRef.current = setInterval(() => {
      setDirection(1);
      setActiveIndex((prev) => (prev + 1) % testimonials.length);
    }, 8000);
  };

  const activeTestimonial = testimonials[activeIndex];

  // Animation variants
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 500 : -500,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction > 0 ? -500 : 500,
      opacity: 0
    })
  };

  return (
    <motion.section
      ref={sectionRef}
      style={{ opacity, y }}
      className="py-20 bg-white relative hidden"
    >
      {/* Visual connector from previous section */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[1px] h-20 bg-gradient-to-b from-black/20 to-transparent" />

      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-4">Client Testimonials</h2>
          <motion.div
            className="h-1 w-20 bg-primary mx-auto mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          />
                   <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Hear what our clients have to say about their experience working with Senior's Archi-firm.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto relative">
          {/* Navigation arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-12 z-10 bg-white/80 hover:bg-white p-2 rounded-full shadow-md transition-colors duration-300"
            aria-label="Previous testimonial"
          >
            <ChevronLeft className="h-6 w-6 text-gray-700" />
          </button>

          {/* Testimonial content */}
          <div className="relative overflow-hidden h-[400px] md:h-[300px]">
            <AnimatePresence custom={direction} mode="wait">
              <motion.div
                key={`testimonial-${activeIndex}`}
                custom={direction}
                variants={slideVariants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="absolute inset-0 bg-white p-8 md:p-12 shadow-xl rounded-lg"
              >
                <div className="flex flex-col md:flex-row gap-8 items-center h-full">
                  <div className="md:w-1/3">
                    <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden mx-auto relative">
                      <img
                        src={activeTestimonial.image}
                        alt={activeTestimonial.author}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute -top-2 -right-2 bg-primary text-white p-2 rounded-full">
                        <Quote className="h-4 w-4" />
                      </div>
                    </div>
                    <div className="text-center mt-4">
                      <p className="font-bold text-gray-900">{activeTestimonial.author}</p>
                      <p className="text-primary text-sm">{activeTestimonial.position}</p>
                      {activeTestimonial.company && (
                        <p className="text-gray-500 text-sm">{activeTestimonial.company}</p>
                      )}
                      {activeTestimonial.projectType && (
                        <div className="mt-2">
                          <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                            {activeTestimonial.projectType}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="md:w-2/3 text-center md:text-left flex flex-col justify-center">
                    <svg className="w-10 h-10 text-primary/20 mb-4 mx-auto md:mx-0" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
                      <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                    </svg>
                    <p className="text-xl md:text-2xl text-gray-700 italic mb-6">
                      "{activeTestimonial.quote}"
                    </p>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          <button
            onClick={nextTestimonial}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-12 z-10 bg-white/80 hover:bg-white p-2 rounded-full shadow-md transition-colors duration-300"
            aria-label="Next testimonial"
          >
            <ChevronRight className="h-6 w-6 text-gray-700" />
          </button>

          {/* Background decorative elements */}
          <div className="absolute top-6 right-6 w-24 h-24 border-t-2 border-r-2 border-primary opacity-20 -z-10 rounded-tr-lg"></div>
          <div className="absolute bottom-6 left-6 w-24 h-24 border-b-2 border-l-2 border-primary opacity-20 -z-10 rounded-bl-lg"></div>

          {/* Testimonial navigation dots */}
          <div className="flex justify-center space-x-3 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => handleDotClick(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === activeIndex ? 'bg-primary scale-125' : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`View testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Progress bar */}
        <div className="max-w-4xl mx-auto mt-8">
          <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-primary"
              initial={{ width: `${(activeIndex / testimonials.length) * 100}%` }}
              animate={{
                width: `${((activeIndex + 1) / testimonials.length) * 100}%`
              }}
              transition={{ duration: 8, ease: "linear" }}
              key={activeIndex}
            />
          </div>
        </div>
      </div>

      {/* Visual connector to next section */}
      <motion.div
        className="w-[1px] h-20 bg-gradient-to-b from-transparent to-black/20 mx-auto mt-16"
        initial={{ scaleY: 0 }}
        whileInView={{ scaleY: 1 }}
        transition={{ duration: 1, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </motion.section>
  );
};

export default TestimonialsSection;
