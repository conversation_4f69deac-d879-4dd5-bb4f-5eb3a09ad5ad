"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { PerformanceOptimizer } from "@/components/optimization/PerformanceOptimizer";
import { OptimizedSidebar, DESIGNER_SIDEBAR_ITEMS } from "@/components/navigation/OptimizedSidebar";
import { useRenderTime } from "@/hooks/usePerformanceMonitoring";
import { useBatchDashboardData } from "@/hooks/useDashboardData";
import { AvailabilityToggle } from "@/components/designer/AvailabilityToggle";
import { AuthDebugger } from "@/components/debug/AuthDebugger";
import { UnifiedMobileNavigation } from "@/components/mobile/UnifiedMobileNavigation";
import { useMobileNavigation, useResponsiveNavigation } from "@/hooks/useMobileNavigation";
import { LogOut, User } from "lucide-react";
import { useRouter } from "next/navigation";
import WorkflowNotifications from "@/components/WorkflowNotifications";
import { LogoIcon } from "@/components/shared/LogoIcon";
import { CollapsibleSidebar, useSidebarCollapsed } from "@/components/shared/CollapsibleSidebar";

interface DesignerLayoutProps {
  children: React.ReactNode;
}

export default function DesignerLayout({ children }: DesignerLayoutProps) {
  const { user, profile, isLoading, signOut } = useOptimizedAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const router = useRouter();

  // Use unified mobile navigation - disable auto-close since UnifiedMobileNavigation handles it
  const mobileNav = useMobileNavigation({ autoCloseOnRouteChange: false });
  const { isMobile } = useResponsiveNavigation();
  const isCollapsed = useSidebarCollapsed("designer");

  // Performance monitoring for this layout
  useRenderTime('DesignerLayout');

  // Removed aggressive redirect logic to prevent redirect loops
  // Authentication checking is now handled by the layout render logic below

  // Batch load dashboard data for performance (only if user is designer)
  const dashboardQueries = useBatchDashboardData(
    user?.id || '',
    profile?.role || 'designer',
    { enabled: !!user && profile?.role === 'designer' }
  );

  // Extract data from optimized queries
  const stats = dashboardQueries[0]?.data || {};
  const projects = dashboardQueries[1]?.data || [];
  const proposals = dashboardQueries[2]?.data || [];

  // Get counts from optimized data
  const assignedProjects = stats.activeProjects || 0;
  const pendingBriefs = stats.pendingProposals || 0;
  const unreadMessages = 0; // Will be updated by real-time sync
  const unreadAdminMessages = 0; // Will be updated by real-time sync

  const handleSignOut = async () => {
    await signOut();
  };

  const toggleSidebar = () => {
    setSidebarOpen(prev => !prev);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  // Only show loading if session is actually loading (prevents loading screens on tab changes)
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If profile is null (failed to load), show a warning but allow access
  if (user && !profile) {
    console.warn('Designer dashboard: User authenticated but profile failed to load');
  }

  // Check authentication and role access
  if (!user) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Please sign in to access the designer dashboard.</p>
          <a href="/auth/login" className="text-brown-600 hover:text-brown-700 underline">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  // Only check role if profile loaded successfully AND we're not loading
  // This prevents cached role issues during authentication transitions
  if (!isLoading && profile && profile.role !== 'designer' && profile.role !== 'admin') {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <p className="text-gray-600 mb-4">You do not have designer privileges.</p>
          <p className="text-sm text-gray-500 mb-4">Your role: {profile.role}</p>
          <p className="text-xs text-gray-400 mb-4">
            If you just logged in, please <a href="/auth/login" className="text-brown-600 underline">try logging in again</a>
          </p>
          <a href="/" className="text-brown-600 hover:text-brown-700 underline">
            Go Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <PerformanceOptimizer
      enablePrefetching={true}
      enableRealtimeSync={true}
      enableDataPersistence={true}
      enablePerformanceMonitoring={true}
    >
      {/* Unified Mobile Navigation */}
      {isMobile && (
        <UnifiedMobileNavigation
          isOpen={mobileNav.isOpen}
          onToggle={mobileNav.toggle}
          onClose={mobileNav.close}
          variant="designer"
        />
      )}

      <div className="flex h-screen bg-gray-50">
        {/* Desktop sidebar overlay */}
        {sidebarOpen && !isMobile && (
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              closeSidebar();
            }}
          />
        )}

        {/* Optimized Sidebar */}
        <div className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="flex flex-col h-full">
            {/* Logo and brand */}
            <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
              <LogoIcon size="md" />
              <div className="flex items-center gap-2">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    closeSidebar();
                  }}
                  className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
                >
                  <span className="sr-only">Close menu</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* User profile summary */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center mb-3">
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.full_name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <p className="font-medium text-sm">{profile?.full_name || "Designer"}</p>
                  <p className="text-xs text-gray-500">
                    {profile?.specialization || "Interior Design"}
                  </p>
                </div>
              </div>

              {/* Availability Toggle */}
              <div className="mb-3">
                <AvailabilityToggle showLabel={false} className="justify-between" />
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              <OptimizedSidebar
                items={DESIGNER_SIDEBAR_ITEMS.map(item => ({
                  ...item,
                  badge: item.prefetchKey === 'projects' ? assignedProjects :
                         item.prefetchKey === 'briefs' ? pendingBriefs :
                         item.prefetchKey === 'messages' ? unreadMessages :
                         item.prefetchKey === 'admin-messages' ? unreadAdminMessages :
                         undefined
                }))}
                role="designer"
                className="space-y-1"
              />
            </div>

            {/* Sign out button */}
            <div className="p-4 border-t border-gray-200">
              <button
                onClick={handleSignOut}
                className="flex items-center w-full px-4 py-3 text-sm text-gray-700 rounded-md hover:bg-gray-100 transition-colors"
              >
                <LogOut className="h-5 w-5 mr-3" />
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 min-h-screen overflow-hidden flex flex-col lg:ml-0">
          {/* Content with mobile navigation spacing */}
          <div className={`flex-1 overflow-y-auto p-4 md:p-8 ${isMobile ? 'pt-20' : ''}`}>
            {children}
          </div>
        </main>
      </div>

      {/* Debug component for development */}
      <AuthDebugger />
    </PerformanceOptimizer>
  );
}
