import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';
import { nanoid } from 'nanoid';
import { Resend } from 'resend';

// Initialize Resend client on the server side (only if API key is available)
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

// Generate a unique tracking number
const generateTrackingNumber = (): string => {
  // Format: TR-XXXX-XXXX-XXXX (where X is alphanumeric)
  return `TR-${nanoid(4).toUpperCase()}-${nanoid(4).toUpperCase()}-${nanoid(4).toUpperCase()}`;
};

// Send sample request confirmation email
const sendSampleRequestConfirmationEmail = async (trackingData: any) => {
  try {
    // Skip email sending if no API key is available
    if (!process.env.RESEND_API_KEY || !resend) {
      console.warn('Skipping email sending - RESEND_API_KEY not configured');
      return;
    }

    const clientEmailResult = await resend.emails.send({
      from: 'Seniors Architecture Firm <<EMAIL>>',
      to: [trackingData.email],
      subject: 'Sample Request Received - Tracking #' + trackingData.tracking_number,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #8B4513; margin: 0; font-size: 28px;">Seniors Architecture Firm</h1>
              <div style="width: 50px; height: 3px; background-color: #8B4513; margin: 10px auto;"></div>
            </div>

            <h2 style="color: #8B4513; margin-bottom: 20px;">Sample Request Received!</h2>
            <p style="color: #333; line-height: 1.6; margin-bottom: 20px;">Dear ${trackingData.name},</p>

            <p style="color: #333; line-height: 1.6; margin-bottom: 20px;">
              Thank you for submitting your sample request! We're excited to provide you with a complimentary sample of our architectural services.
            </p>

            <div style="background-color: #f8f8f8; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #8B4513; margin-top: 0;">Request Details:</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr><td style="padding: 8px 0; color: #666;"><strong>Tracking Number:</strong></td><td style="padding: 8px 0; color: #333; font-weight: bold;">${trackingData.tracking_number}</td></tr>
                <tr><td style="padding: 8px 0; color: #666;"><strong>Project Type:</strong></td><td style="padding: 8px 0; color: #333;">${trackingData.project_type}</td></tr>
                <tr><td style="padding: 8px 0; color: #666;"><strong>Service Category:</strong></td><td style="padding: 8px 0; color: #333;">${trackingData.service_category || 'Not specified'}</td></tr>
                <tr><td style="padding: 8px 0; color: #666;"><strong>Submitted:</strong></td><td style="padding: 8px 0; color: #333;">${new Date().toLocaleDateString()}</td></tr>
              </table>
            </div>

            <div style="background-color: #e8f4f8; padding: 20px; border-radius: 8px; border-left: 4px solid #8B4513; margin: 20px 0;">
              <h3 style="color: #8B4513; margin-top: 0;">What Happens Next?</h3>
              <ul style="color: #333; line-height: 1.6; margin: 0; padding-left: 20px;">
                <li>Our team will review your project requirements</li>
                <li>We'll prepare a customized sample based on your specifications</li>
                <li>You'll receive email updates as your request progresses</li>
                <li>Typical processing time is 3-5 business days</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/track?tracking=${trackingData.tracking_number}"
                 style="background-color: #8B4513; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold;">
                Track Your Request
              </a>
            </div>

            <p style="color: #333; line-height: 1.6; margin: 20px 0;">
              You can track the status of your request at any time using your tracking number: <strong>${trackingData.tracking_number}</strong>
            </p>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #8B4513; font-weight: bold; margin: 0;">Best regards,</p>
              <p style="color: #8B4513; font-weight: bold; margin: 5px 0 0 0;">Seniors Architecture Firm Team</p>
              <p style="color: #666; font-size: 14px; margin: 10px 0 0 0;">
                Email: <EMAIL> | Website: seniorsarchifirm.com
              </p>
            </div>
          </div>
        </div>
      `
    });

    // Record the communication
    if (trackingData.id !== 'mock-id') {
      await supabaseServerClient
        .from('tracking_communications')
        .insert({
          tracking_request_id: trackingData.id,
          communication_type: 'email',
          subject: 'Sample Request Received - Tracking #' + trackingData.tracking_number,
          content: 'Sample request confirmation email sent',
          recipient_email: trackingData.email,
          status: clientEmailResult.error ? 'failed' : 'sent',
          metadata: clientEmailResult.data ? { resend_id: clientEmailResult.data.id } : null
        });
    }

    // Send admin notification (only if API key is available)
    if (process.env.RESEND_API_KEY && resend) {
      await resend.emails.send({
      from: 'Seniors Architecture Firm <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'New Sample Request - ' + trackingData.tracking_number,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #8B4513;">New Sample Request</h2>
          <p>A new sample request has been submitted and requires processing.</p>

          <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #8B4513;">Request Details:</h3>
            <p><strong>Tracking Number:</strong> ${trackingData.tracking_number}</p>
            <p><strong>Client Name:</strong> ${trackingData.name}</p>
            <p><strong>Email:</strong> ${trackingData.email}</p>
            <p><strong>Project Type:</strong> ${trackingData.project_type}</p>
            <p><strong>Service Category:</strong> ${trackingData.service_category || 'Not specified'}</p>
            <p><strong>Sample Type:</strong> ${trackingData.sample_type || 'Not specified'}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</p>
          </div>

          <div style="background-color: #e8f4f8; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #8B4513;">Project Description:</h4>
            <p style="margin-bottom: 0;">${trackingData.description}</p>
          </div>

          <p>
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/tracking"
               style="background-color: #8B4513; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Manage Request
            </a>
          </p>
        </div>
      `
      });
    }

  } catch (error) {
    console.error('Error sending sample request confirmation email:', error);
    throw error;
  }
};

export async function POST(request: NextRequest) {
  try {
    console.log('Sample Request API route called');
    console.log('Request method:', request.method);
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));

    let body;
    try {
      body = await request.json();
      console.log('Request body received:', JSON.stringify(body, null, 2));
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    const {
      name,
      email,
      projectType,
      description,
      sampleType,
      serviceCategory,
      filePath,
      fileName,
      fileType,
      fileSize,
      recaptchaToken
    } = body;

    console.log('Extracted fields:', {
      name: name,
      email: email,
      projectType: projectType,
      description: description,
      sampleType: sampleType,
      serviceCategory: serviceCategory,
      filePath: filePath,
      fileName: fileName,
      fileType: fileType,
      fileSize: fileSize,
      hasRecaptchaToken: !!recaptchaToken
    });

    // Validate required fields with more detailed checking
    const validationErrors = [];
    if (!name || typeof name !== 'string' || name.trim() === '') {
      validationErrors.push('name is required and must be a non-empty string');
    }
    if (!email || typeof email !== 'string' || email.trim() === '') {
      validationErrors.push('email is required and must be a non-empty string');
    }
    if (!projectType || typeof projectType !== 'string' || projectType.trim() === '') {
      validationErrors.push('projectType is required and must be a non-empty string');
    }
    if (!description || typeof description !== 'string' || description.trim() === '') {
      validationErrors.push('description is required and must be a non-empty string');
    }
    if (!sampleType || typeof sampleType !== 'string' || sampleType.trim() === '') {
      validationErrors.push('sampleType is required and must be a non-empty string');
    }
    if (!serviceCategory || typeof serviceCategory !== 'string' || serviceCategory.trim() === '') {
      validationErrors.push('serviceCategory is required and must be a non-empty string');
    }

    if (validationErrors.length > 0) {
      console.log('Validation failed:', validationErrors);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationErrors,
          receivedFields: {
            name: !!name,
            email: !!email,
            projectType: !!projectType,
            description: !!description,
            sampleType: !!sampleType,
            serviceCategory: !!serviceCategory
          }
        },
        { status: 400 }
      );
    }

    console.log('All required fields validated successfully');

    console.log('Starting createSampleRequestTracking with data:', body);
    const trackingNumber = generateTrackingNumber();
    console.log('Generated tracking number:', trackingNumber);

    // Verify reCAPTCHA if token provided (non-blocking)
    let recaptchaVerified = false;

    // Check if reCAPTCHA is disabled for development
    const skipRecaptcha = process.env.NODE_ENV === 'development' && process.env.SKIP_RECAPTCHA === 'true';

    if (skipRecaptcha) {
      console.log('Skipping reCAPTCHA verification (development mode)');
      recaptchaVerified = true;
    } else if (recaptchaToken) {
      try {
        console.log('Verifying reCAPTCHA token...');
        const { verifyRecaptcha } = await import('@/lib/recaptcha');
        const recaptchaResult = await verifyRecaptcha(recaptchaToken, 'sample_request');
        console.log('reCAPTCHA verification result:', recaptchaResult);

        if (recaptchaResult.success) {
          console.log('reCAPTCHA verification successful');
          recaptchaVerified = true;
        } else {
          console.warn('reCAPTCHA verification failed, but continuing with request');
          console.log('reCAPTCHA failure details:', recaptchaResult);
          // In development, continue anyway
          if (process.env.NODE_ENV === 'development') {
            console.log('Development mode: continuing despite reCAPTCHA failure');
            recaptchaVerified = true;
          }
        }
      } catch (error) {
        console.error('reCAPTCHA verification error:', error);
        console.warn('Skipping reCAPTCHA verification due to error, continuing with request');
        // In development, continue anyway
        if (process.env.NODE_ENV === 'development') {
          recaptchaVerified = true;
        }
      }
    } else {
      console.log('No reCAPTCHA token provided');
      // In development, allow requests without reCAPTCHA
      if (process.env.NODE_ENV === 'development') {
        console.log('Development mode: allowing request without reCAPTCHA');
        recaptchaVerified = true;
      }
    }

    try {
      console.log('Attempting to insert record into Supabase...');
      const { data: trackingData, error } = await supabaseServerClient
        .from('tracking_requests')
        .insert({
          tracking_number: trackingNumber,
          request_type: 'sample_request',
          status: 'submitted',
          internal_status: 'new', // Set internal status for admin workflow
          name: name,
          email: email,
          project_type: projectType,
          description: description,
          service_category: serviceCategory,
          sample_type: sampleType,
          file_path: filePath || null,
          file_name: fileName || null,
          file_type: fileType || null,
          file_size: fileSize || null,
          priority: 'normal', // Set default priority
          follow_up_required: false // Set default follow-up
        })
        .select()
        .single();

      console.log('Supabase response:', { trackingData, error });

      if (error) {
        console.error('Error creating tracking request:', error);
        // If the table doesn't exist, return a mock tracking record
        if (error.code === '42P01') { // PostgreSQL code for undefined_table
          console.warn('tracking_requests table does not exist, returning mock data');
          const mockData = {
            id: 'mock-id',
            tracking_number: trackingNumber,
            request_type: 'sample_request',
            status: 'submitted',
            internal_status: 'new',
            name: name,
            email: email,
            project_type: projectType,
            description: description,
            service_category: serviceCategory,
            sample_type: sampleType,
            file_path: filePath || null,
            file_name: fileName || null,
            file_type: fileType || null,
            file_size: fileSize || null,
            priority: 'normal',
            follow_up_required: false,
            created_at: new Date().toISOString()
          };

          // Try to send email even with mock data
          try {
            await sendSampleRequestConfirmationEmail(mockData);
          } catch (emailError) {
            console.error('Error sending sample request confirmation email:', emailError);
          }

          return NextResponse.json({ success: true, data: mockData });
        }
        throw error;
      }

      console.log('Successfully created tracking request in Supabase');

      // Send confirmation email
      try {
        console.log('Attempting to send confirmation email...');
        await sendSampleRequestConfirmationEmail(trackingData);
        console.log('Confirmation email sent successfully');
      } catch (emailError) {
        console.error('Error sending sample request confirmation email:', emailError);
        // Don't fail the entire request if email fails
      }

      return NextResponse.json({ success: true, data: trackingData });
    } catch (error) {
      console.error('Error in createSampleRequestTracking:', error);
      console.warn('Returning mock tracking data due to error');

      // Return a mock tracking record for any error
      const mockData = {
        id: 'mock-id',
        tracking_number: trackingNumber,
        request_type: 'sample_request',
        status: 'submitted',
        name: name,
        email: email,
        project_type: projectType,
        description: description,
        service_category: serviceCategory,
        sample_type: sampleType,
        file_path: filePath || null,
        file_name: fileName || null,
        file_type: fileType || null,
        file_size: fileSize || null,
        created_at: new Date().toISOString()
      };

      return NextResponse.json({ success: true, data: mockData });
    }
  } catch (error) {
    console.error('Error in sample request tracking API:', error);

    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Detailed error:', errorMessage);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}
