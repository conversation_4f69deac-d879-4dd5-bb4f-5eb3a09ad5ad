"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Star,
  FileText,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react";

interface QualityStandard {
  id: string;
  category: string;
  standard_name: string;
  description: string;
  criteria: string[];
  is_mandatory: boolean;
  weight: number;
  created_at: string;
}

export default function QualityStandardsPage() {
  const { user, profile } = useOptimizedAuth();
  const [standards, setStandards] = useState<QualityStandard[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchStandards();
    }
  }, [user, profile]);

  const fetchStandards = async () => {
    try {
      const { data, error } = await supabase
        .from('quality_standards')
        .select('*')
        .order('category', { ascending: true })
        .order('weight', { ascending: false });

      if (error) throw error;
      setStandards(data || []);
    } catch (error) {
      console.error('Error fetching standards:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'design':
        return <Star className="h-5 w-5 text-purple-500" />;
      case 'technical':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'content':
        return <Edit className="h-5 w-5 text-green-500" />;
      case 'ux':
        return <CheckCircle className="h-5 w-5 text-orange-500" />;
      case 'deliverable':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getCategoryBadge = (category: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (category) {
      case 'design':
        return `${baseClasses} bg-purple-100 text-purple-800 border border-purple-200`;
      case 'technical':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'content':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'ux':
        return `${baseClasses} bg-orange-100 text-orange-800 border border-orange-200`;
      case 'deliverable':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getWeightBadge = (weight: number) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    if (weight >= 5) {
      return `${baseClasses} bg-red-100 text-red-800`;
    } else if (weight >= 4) {
      return `${baseClasses} bg-orange-100 text-orange-800`;
    } else if (weight >= 3) {
      return `${baseClasses} bg-yellow-100 text-yellow-800`;
    } else {
      return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredStandards = standards.filter(standard => {
    const matchesSearch = standard.standard_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         standard.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' || standard.category === filter;
    return matchesSearch && matchesFilter;
  });

  const categories = [...new Set(standards.map(s => s.category))];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quality Standards</h1>
          <p className="text-gray-600 mt-2">Comprehensive quality checklist for all design categories</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={fetchStandards}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Button
            onClick={() => window.location.href = '/quality/standards/new'}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Standard
          </Button>
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {categories.map(category => (
          <div key={category} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 capitalize">{category}</p>
                <p className="text-2xl font-bold text-gray-900">
                  {standards.filter(s => s.category === category).length}
                </p>
              </div>
              {getCategoryIcon(category)}
            </div>
          </div>
        ))}
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category} className="capitalize">
                  {category}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search standards..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Standards List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Quality Standards Checklist</h2>
          <p className="text-gray-600 mt-1">Comprehensive standards used for quality reviews</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredStandards.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No standards found matching your criteria</p>
            </div>
          ) : (
            filteredStandards.map((standard) => (
              <div key={standard.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {getCategoryIcon(standard.category)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {standard.standard_name}
                      </h3>
                      <span className={getCategoryBadge(standard.category)}>
                        {standard.category.toUpperCase()}
                      </span>
                      {standard.is_mandatory && (
                        <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                          Mandatory
                        </span>
                      )}
                      <span className={getWeightBadge(standard.weight)}>
                        Weight: {standard.weight}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{standard.description}</p>
                    
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Criteria Checklist:</h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                        {standard.criteria.map((criterion, index) => (
                          <li key={index}>{criterion}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/quality/standards/${standard.id}/edit`}
                    >
                      <Edit className="h-4 w-4" />
                      Edit
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2 border-red-200 text-red-600 hover:bg-red-50"
                      onClick={() => {
                        if (confirm('Are you sure you want to delete this standard?')) {
                          // Handle delete
                        }
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Usage Guidelines */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <Info className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Quality Standards Guidelines</h3>
            <div className="text-blue-800 space-y-2">
              <p>• <strong>Mandatory standards</strong> must be met for approval</p>
              <p>• <strong>Weight</strong> indicates importance (1-5 scale)</p>
              <p>• <strong>Categories</strong> help organize standards by type</p>
              <p>• All criteria within a standard should be checked during review</p>
              <p>• Standards are automatically applied to all quality reviews</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
