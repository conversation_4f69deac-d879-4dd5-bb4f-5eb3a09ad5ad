-- Admin Messaging System Enhancements
-- Add flagging and review system to conversation messages

-- 1. Add admin-specific fields to conversation_messages table
ALTER TABLE conversation_messages 
ADD COLUMN IF NOT EXISTS is_flagged BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS flagged_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS flagged_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS flag_reason TEXT,
ADD COLUMN IF NOT EXISTS admin_reviewed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS admin_reviewed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS admin_reviewed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS admin_notes TEXT;

-- 2. <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_messages_flagged ON conversation_messages(is_flagged) WHERE is_flagged = TRUE;
CREATE INDEX IF NOT EXISTS idx_conversation_messages_admin_reviewed ON conversation_messages(admin_reviewed);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_flagged_at ON conversation_messages(flagged_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_admin_reviewed_at ON conversation_messages(admin_reviewed_at DESC);

-- 3. Create admin_message_actions table for audit trail
CREATE TABLE IF NOT EXISTS admin_message_actions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID REFERENCES conversation_messages(id) ON DELETE CASCADE,
    admin_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    action_type VARCHAR(50) NOT NULL, -- 'flag', 'unflag', 'review', 'unreview'
    reason TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create indexes for admin actions
CREATE INDEX IF NOT EXISTS idx_admin_message_actions_message_id ON admin_message_actions(message_id);
CREATE INDEX IF NOT EXISTS idx_admin_message_actions_admin_id ON admin_message_actions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_message_actions_created_at ON admin_message_actions(created_at DESC);

-- 5. Add RLS policies for admin actions
ALTER TABLE admin_message_actions ENABLE ROW LEVEL SECURITY;

-- Only admins can view and manage admin actions
CREATE POLICY "Admins can manage message actions" ON admin_message_actions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- 6. Create function to get conversation statistics
CREATE OR REPLACE FUNCTION get_conversation_stats(conversation_uuid UUID)
RETURNS TABLE (
    total_messages BIGINT,
    unread_count BIGINT,
    flagged_count BIGINT,
    last_message_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_messages,
        COUNT(*) FILTER (WHERE sender_id != auth.uid() AND created_at > COALESCE(
            (SELECT last_read_at FROM conversation_participants 
             WHERE conversation_id = conversation_uuid AND user_id = auth.uid()), 
            '1970-01-01'::timestamp with time zone
        )) as unread_count,
        COUNT(*) FILTER (WHERE is_flagged = TRUE) as flagged_count,
        MAX(created_at) as last_message_at
    FROM conversation_messages 
    WHERE conversation_id = conversation_uuid 
    AND deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create function to mark messages as reviewed
CREATE OR REPLACE FUNCTION mark_conversation_messages_reviewed(
    conversation_uuid UUID,
    admin_uuid UUID,
    admin_notes_text TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- Update messages to mark as reviewed
    UPDATE conversation_messages 
    SET 
        admin_reviewed = TRUE,
        admin_reviewed_by = admin_uuid,
        admin_reviewed_at = NOW(),
        admin_notes = COALESCE(admin_notes_text, admin_notes)
    WHERE conversation_id = conversation_uuid 
    AND admin_reviewed = FALSE
    AND deleted_at IS NULL;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Log the action
    INSERT INTO admin_message_actions (
        message_id, 
        admin_id, 
        action_type, 
        notes
    )
    SELECT 
        id,
        admin_uuid,
        'review',
        admin_notes_text
    FROM conversation_messages 
    WHERE conversation_id = conversation_uuid 
    AND admin_reviewed_by = admin_uuid
    AND admin_reviewed_at >= NOW() - INTERVAL '1 minute';
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to flag/unflag messages
CREATE OR REPLACE FUNCTION toggle_message_flag(
    message_uuid UUID,
    admin_uuid UUID,
    flag_reason_text TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_flag_status BOOLEAN;
    new_flag_status BOOLEAN;
BEGIN
    -- Get current flag status
    SELECT is_flagged INTO current_flag_status 
    FROM conversation_messages 
    WHERE id = message_uuid;
    
    -- Toggle flag status
    new_flag_status := NOT COALESCE(current_flag_status, FALSE);
    
    -- Update message
    UPDATE conversation_messages 
    SET 
        is_flagged = new_flag_status,
        flagged_by = CASE WHEN new_flag_status THEN admin_uuid ELSE NULL END,
        flagged_at = CASE WHEN new_flag_status THEN NOW() ELSE NULL END,
        flag_reason = CASE WHEN new_flag_status THEN flag_reason_text ELSE NULL END
    WHERE id = message_uuid;
    
    -- Log the action
    INSERT INTO admin_message_actions (
        message_id, 
        admin_id, 
        action_type, 
        reason
    ) VALUES (
        message_uuid,
        admin_uuid,
        CASE WHEN new_flag_status THEN 'flag' ELSE 'unflag' END,
        flag_reason_text
    );
    
    RETURN new_flag_status;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
