-- =====================================================
-- STEP 3: SETUP CHANGE REQUESTS FOR ENHANCED PROPOSALS
-- Run this AFTER step2_add_constraint.sql
-- =====================================================

-- Update change requests table to work with enhanced proposals
DO $$
BEGIN
  -- Check if proposal_change_requests table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'proposal_change_requests') THEN
    -- Add column for enhanced proposals if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                   WHERE table_name = 'proposal_change_requests' 
                   AND column_name = 'enhanced_proposal_id') THEN
      ALTER TABLE proposal_change_requests 
      ADD COLUMN enhanced_proposal_id UUID REFERENCES project_proposals_enhanced(id) ON DELETE CASCADE;
      
      RAISE NOTICE 'Added enhanced_proposal_id column to proposal_change_requests';
    ELSE
      RAISE NOTICE 'enhanced_proposal_id column already exists';
    END IF;
    
    -- Update existing change requests to link to enhanced proposals if possible
    -- This assumes you want to migrate existing change requests
    UPDATE proposal_change_requests 
    SET enhanced_proposal_id = (
      SELECT id FROM project_proposals_enhanced 
      WHERE project_proposals_enhanced.id = proposal_change_requests.proposal_id
      LIMIT 1
    )
    WHERE enhanced_proposal_id IS NULL 
      AND proposal_id IS NOT NULL;
    
    RAISE NOTICE 'Updated existing change requests to link to enhanced proposals';
    
  ELSE
    -- Create the table if it doesn't exist
    CREATE TABLE proposal_change_requests (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      proposal_id UUID REFERENCES project_proposals(id) ON DELETE CASCADE,
      enhanced_proposal_id UUID REFERENCES project_proposals_enhanced(id) ON DELETE CASCADE,
      requested_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'implemented')),
      section TEXT NOT NULL,
      description TEXT NOT NULL,
      response TEXT,
      responded_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
      responded_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Enable RLS
    ALTER TABLE proposal_change_requests ENABLE ROW LEVEL SECURITY;
    
    RAISE NOTICE 'Created proposal_change_requests table';
  END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_designer_brief 
ON project_proposals_enhanced(designer_id, brief_id);

CREATE INDEX IF NOT EXISTS idx_proposal_change_requests_enhanced_proposal 
ON proposal_change_requests(enhanced_proposal_id);

CREATE INDEX IF NOT EXISTS idx_proposal_change_requests_status 
ON proposal_change_requests(status);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Only create trigger if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_proposal_change_requests_updated_at') THEN
        CREATE TRIGGER update_proposal_change_requests_updated_at 
          BEFORE UPDATE ON proposal_change_requests 
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        
        RAISE NOTICE 'Created update trigger for proposal_change_requests';
    ELSE
        RAISE NOTICE 'Update trigger already exists';
    END IF;
END $$;

-- Success message
SELECT 'Change requests setup completed successfully!' as status;
