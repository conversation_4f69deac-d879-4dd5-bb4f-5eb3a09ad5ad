"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { PaymentForm } from "@/components/payment/PaymentForm";
import { PaymentMethodSelector } from "@/components/payment/PaymentMethodSelector";
import { QuickPaymentButton, MilestonePaymentCard } from "@/components/payment/QuickPaymentButton";
import {
  CreditCard,
  Download,
  ChevronDown,
  ChevronUp,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  FileText,
  DollarSign,
  Calendar,
  CheckSquare
} from "lucide-react";
import Link from "next/link";

type Invoice = {
  id: string;
  invoice_number: string;
  amount: number;
  status: string;
  due_date: string;
  issued_date: string;
  project_id: string;
  project_title: string;
  description: string;
};

type PaymentMethod = {
  id: string;
  payment_type: 'card' | 'paypal';
  card_brand?: string;
  last_four?: string;
  expiry_date?: string;
  paypal_email?: string;
  is_default: boolean;
};

type Milestone = {
  id: string;
  project_id: string;
  project_title: string;
  title: string;
  description: string;
  amount: number;
  percentage: number;
  due_date: string | null;
  status: string;
  order_index: number;
  completed_at: string | null;
  approved_at: string | null;
  paid_at: string | null;
};

export default function ClientPayments() {
  const { user } = useOptimizedAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [expandedInvoice, setExpandedInvoice] = useState<string | null>(null);
  const [expandedMilestone, setExpandedMilestone] = useState<string | null>(null);
  const [selectedMilestone, setSelectedMilestone] = useState<Milestone | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchInvoicesAndPayments();
    }
  }, [user]);

  const fetchInvoicesAndPayments = async () => {
    setLoading(true);
    try {
      // Fetch invoices
      console.log('Fetching invoices for client:', user?.id);
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('invoices')
        .select(`
          id,
          invoice_number,
          amount,
          status,
          due_date,
          issued_date,
          description,
          project_id,
          projects(title)
        `)
        .eq('client_id', user?.id)
        .order('issued_date', { ascending: false });

      if (invoicesError) {
        console.error('Error fetching invoices:', invoicesError);
        // Continue with empty invoices instead of throwing
        setInvoices([]);
      } else {
        console.log('Invoices fetched successfully:', invoicesData?.length || 0);
        setInvoices(
          (invoicesData || []).map(invoice => ({
            id: invoice.id,
            invoice_number: invoice.invoice_number,
            amount: invoice.amount,
            status: invoice.status,
            due_date: invoice.due_date,
            issued_date: invoice.issued_date,
            project_id: invoice.project_id,
            project_title: invoice.projects?.[0]?.title || 'Unknown Project',
            description: invoice.description
          }))
        );
      }

      // Fetch payment methods
      console.log('Fetching payment methods for user:', user?.id);
      const { data: paymentMethodsData, error: paymentMethodsError } = await supabase
        .from('payment_methods')
        .select('*')
        .eq('user_id', user?.id)
        .order('is_default', { ascending: false });

      if (paymentMethodsError) {
        console.error('Error fetching payment methods:', paymentMethodsError);
        // Continue with empty payment methods instead of throwing
        setPaymentMethods([]);
      } else {
        console.log('Payment methods fetched successfully:', paymentMethodsData?.length || 0);
        setPaymentMethods(paymentMethodsData || []);
      }

      // Fetch projects for this client
      console.log('Fetching projects for client:', user?.id);
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('id, title')
        .eq('client_id', user?.id);

      if (projectsError) {
        console.error('Error fetching projects:', projectsError);
        // If we can't get projects, we can't get milestones
        setMilestones([]);
      } else if (projectsData && projectsData.length > 0) {
        console.log('Projects fetched successfully:', projectsData.length);
        // Get project IDs
        const projectIds = projectsData.map(project => project.id);

        // Fetch milestones for these projects
        console.log('Fetching milestones for projects:', projectIds);
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('project_milestones')
          .select(`
            id,
            project_id,
            title,
            description,
            amount,
            percentage,
            due_date,
            status,
            order_index,
            completed_at,
            approved_at,
            paid_at
          `)
          .in('project_id', projectIds)
          .order('order_index', { ascending: true });

        if (milestonesError) {
          console.error('Error fetching milestones:', milestonesError);
          setMilestones([]);
        } else {
          console.log('Milestones fetched successfully:', milestonesData?.length || 0);
          // Add project titles to milestones
          const formattedMilestones = milestonesData.map(milestone => {
            const project = projectsData.find(p => p.id === milestone.project_id);
            return {
              ...milestone,
              project_title: project ? project.title : 'Unknown Project'
            };
          });

          setMilestones(formattedMilestones);
        }
      } else {
        console.log('No projects found for client');
        setMilestones([]);
      }
    } catch (error: Error | unknown) {
      // More detailed error logging
      console.error('Error fetching payment data:', error);
      console.error('Error details:', {
        name: error instanceof Error ? error.name : undefined,
        message: error instanceof Error ? error.message : undefined,
        code: (error as { code?: string })?.code,
        details: (error as { details?: string })?.details,
        hint: (error as { hint?: string })?.hint,
        stack: error instanceof Error ? error.stack : undefined
      });

      // Set a more informative error message
      if (error instanceof Error) {
        setError(`Error: ${error.message}`);
      } else if ((error as { code?: string })?.code) {
        setError(`Database error (${(error as { code: string }).code}): ${(error as { details?: string })?.details || 'Unknown error'}`);
      } else {
        setError('Failed to load payment information. Check console for details.');
      }
    } finally {
      setLoading(false);
    }
  };
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Paid
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </span>
        );
      case 'overdue':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertCircle className="h-3 w-3 mr-1" />
            Overdue
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  const toggleInvoiceExpand = (invoiceId: string) => {
    if (expandedInvoice === invoiceId) {
      setExpandedInvoice(null);
    } else {
      setExpandedInvoice(invoiceId);
    }
  };

  const toggleMilestoneExpand = (milestoneId: string) => {
    if (expandedMilestone === milestoneId) {
      setExpandedMilestone(null);
    } else {
      setExpandedMilestone(milestoneId);
    }
  };

  const handlePaymentClick = (milestone: Milestone) => {
    setSelectedMilestone(milestone);
    setShowPaymentForm(true);
  };

  const handlePaymentSuccess = async () => {
    setShowPaymentForm(false);
    await fetchInvoicesAndPayments();
  };

  const handlePaymentCancel = () => {
    setShowPaymentForm(false);
  };

  const handleAddPaymentMethod = () => {
    setShowAddPaymentMethod(true);
  };

  const handlePaymentMethodSuccess = () => {
    setShowAddPaymentMethod(false);
    fetchInvoicesAndPayments();
  };

  const handlePaymentMethodCancel = () => {
    setShowAddPaymentMethod(false);
  };

  const getMilestoneStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'active':
        return 'bg-brown-100 text-brown-800';
      case 'completed':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'paid':
        return 'bg-brown-50 text-brown-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-8">Payments</h1>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg">
          <p>{error}</p>
        </div>
      )}

      {/* Payment Methods Section */}
      {showAddPaymentMethod ? (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-lg font-semibold mb-6">Add Payment Method</h2>
          <div className="mb-4">
            <PaymentMethodSelector
              onSuccess={handlePaymentMethodSuccess}
              onCancel={handlePaymentMethodCancel}
            />
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold">Payment Methods</h2>
            <Button
              variant="outline"
              className="flex items-center"
              onClick={handleAddPaymentMethod}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Payment Method
            </Button>
          </div>

          {paymentMethods.length === 0 ? (
            <div className="text-center py-8 border rounded-lg">
              <CreditCard className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No payment methods added yet.</p>
              <Button onClick={handleAddPaymentMethod}>
                Add Payment Method
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {paymentMethods.map((method) => (
                <div key={method.id} className="border rounded-lg p-4 flex justify-between items-center">
                  <div className="flex items-center">
                    {method.payment_type === 'card' ? (
                      <>
                        <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center mr-4">
                          {method.card_brand === 'visa' && <span className="text-blue-600 font-bold">VISA</span>}
                          {method.card_brand === 'mastercard' && <span className="text-red-600 font-bold">MC</span>}
                          {method.card_brand === 'amex' && <span className="text-blue-800 font-bold">AMEX</span>}
                          {!['visa', 'mastercard', 'amex'].includes(method.card_brand || '') && <CreditCard className="h-5 w-5" />}
                        </div>
                        <div>
                          <p className="font-medium">•••• •••• •••• {method.last_four}</p>
                          <p className="text-sm text-gray-500">Expires {method.expiry_date}</p>
                        </div>
                      </>
                    ) : method.payment_type === 'paypal' ? (
                      <>
                        <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center mr-4">
                          <span className="text-[#003087] font-bold text-sm">Pay<span className="text-[#009cde]">Pal</span></span>
                        </div>
                        <div>
                          <p className="font-medium">PayPal Account</p>
                          <p className="text-sm text-gray-500">{method.paypal_email}</p>
                        </div>
                      </>
                    ) : (
                      <div>
                        <p className="font-medium">Unknown Payment Method</p>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center">
                    {method.is_default && (
                      <span className="mr-4 text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                        Default
                      </span>
                    )}
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Project Milestones Section */}
      {showPaymentForm && selectedMilestone ? (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-lg font-semibold mb-6">Make Payment</h2>
          <PaymentForm
            projectId={selectedMilestone.project_id}
            milestoneId={selectedMilestone.id}
            amount={selectedMilestone.amount * 100} // Convert to cents for Stripe
            description={`Payment for milestone: ${selectedMilestone.title} (${selectedMilestone.project_title})`}
            paymentType={selectedMilestone.order_index === 0 ? 'deposit' : 'milestone'}
            onSuccess={handlePaymentSuccess}
            onCancel={handlePaymentCancel}
          />
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-lg font-semibold mb-6">Project Milestones</h2>

          {milestones.length === 0 ? (
            <div className="text-center py-8 border rounded-lg">
              <CheckSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No project milestones yet.</p>
              <Link href="/client/projects">
                <Button>
                  View Your Projects
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {milestones
                .filter(milestone => milestone.status === 'approved' && !milestone.paid_at)
                .map((milestone) => (
                  <div key={milestone.id} className="border rounded-lg overflow-hidden">
                    <div
                      className="p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50"
                      onClick={() => toggleMilestoneExpand(milestone.id)}
                    >
                      <div>
                        <p className="font-medium">{milestone.title}</p>
                        <p className="text-sm text-gray-500">
                          {milestone.project_title}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getMilestoneStatusColor(milestone.status)}`}>
                          {milestone.status.charAt(0).toUpperCase() + milestone.status.slice(1)}
                        </span>
                        <p className="mx-4 font-bold">{formatCurrency(milestone.amount)}</p>
                        {expandedMilestone === milestone.id ? (
                          <ChevronUp className="h-5 w-5 text-gray-400" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>

                    {expandedMilestone === milestone.id && (
                      <div className="p-4 bg-gray-50 border-t">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-sm text-gray-500">Description</p>
                            <p>{milestone.description || 'No description provided'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Project</p>
                            <Link href={`/client/projects/${milestone.project_id}`} className="text-blue-600 hover:underline">
                              {milestone.project_title}
                            </Link>
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-600">
                            <p className="mb-1">This milestone has been completed and approved.</p>
                            <p>Click to pay with your saved payment method.</p>
                          </div>
                          <QuickPaymentButton
                            milestoneId={milestone.id}
                            projectId={milestone.project_id}
                            amount={milestone.amount}
                            title={milestone.title}
                            description={`Payment for milestone: ${milestone.title}`}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}

              {/* Completed Milestones */}
              <div className="mt-8">
                <h3 className="text-md font-medium mb-4">Payment History</h3>
                {milestones
                  .filter(milestone => milestone.paid_at)
                  .map((milestone) => (
                    <div key={milestone.id} className="border rounded-lg overflow-hidden mb-4">
                      <div
                        className="p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50"
                        onClick={() => toggleMilestoneExpand(milestone.id)}
                      >
                        <div>
                          <p className="font-medium">{milestone.title}</p>
                          <p className="text-sm text-gray-500">
                            {milestone.project_title} • Paid on {formatDate(milestone.paid_at || '')}
                          </p>
                        </div>
                        <div className="flex items-center">
                          <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getMilestoneStatusColor(milestone.status)}`}>
                            {milestone.status.charAt(0).toUpperCase() + milestone.status.slice(1)}
                          </span>
                          <p className="mx-4 font-bold">{formatCurrency(milestone.amount)}</p>
                          {expandedMilestone === milestone.id ? (
                            <ChevronUp className="h-5 w-5 text-gray-400" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                      </div>

                      {expandedMilestone === milestone.id && (
                        <div className="p-4 bg-gray-50 border-t">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-500">Description</p>
                              <p>{milestone.description || 'No description provided'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Project</p>
                              <Link href={`/client/projects/${milestone.project_id}`} className="text-blue-600 hover:underline">
                                {milestone.project_title}
                              </Link>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                {milestones.filter(milestone => milestone.paid_at).length === 0 && (
                  <p className="text-gray-500 text-center py-4">No payment history yet.</p>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Invoices Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-6">Invoices</h2>

        {invoices.length === 0 ? (
          <div className="text-center py-8 border rounded-lg">
            <p className="text-gray-500">No invoices yet.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {invoices.map((invoice) => (
              <div key={invoice.id} className="border rounded-lg overflow-hidden">
                <div
                  className="p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleInvoiceExpand(invoice.id)}
                >
                  <div>
                    <p className="font-medium">Invoice #{invoice.invoice_number}</p>
                    <p className="text-sm text-gray-500">
                      {invoice.project_title} • Issued on {formatDate(invoice.issued_date)}
                    </p>
                  </div>
                  <div className="flex items-center">
                    {getStatusBadge(invoice.status)}
                    <p className="mx-4 font-bold">{formatCurrency(invoice.amount)}</p>
                    {expandedInvoice === invoice.id ? (
                      <ChevronUp className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>

                {expandedInvoice === invoice.id && (
                  <div className="p-4 bg-gray-50 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-500">Description</p>
                        <p>{invoice.description}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Due Date</p>
                        <p>{formatDate(invoice.due_date)}</p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <Button variant="outline" className="flex items-center">
                        <Download className="h-4 w-4 mr-2" />
                        Download PDF
                      </Button>

                      {invoice.status !== 'paid' && (
                        <Button>
                          Pay Now
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}