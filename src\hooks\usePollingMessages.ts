"use client";

import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { messagingKeys, messagingCacheUtils } from '@/hooks/useMessagingKeys';

interface UsePollingMessagesProps {
  conversationId?: string;
  onNewMessage?: (message: any) => void;
  onMessageUpdate?: (message: any) => void;
  onConversationUpdate?: (conversation: any) => void;
  enabled?: boolean;
  pollInterval?: number;
}

export function usePollingMessages({
  conversationId,
  onNewMessage,
  onMessageUpdate,
  onConversationUpdate,
  enabled = true,
  pollInterval = 5000 // 5 seconds default
}: UsePollingMessagesProps) {
  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageCountRef = useRef<number>(0);
  const lastConversationUpdateRef = useRef<string>('');

  // Poll for new messages in a specific conversation
  const pollConversationMessages = useCallback(async () => {
    if (!conversationId || !enabled) return;

    try {
      // Get current message count from React Query cache
      const cachedMessages = queryClient.getQueryData(['conversation_messages', conversationId]);
      const currentCount = Array.isArray(cachedMessages) ? cachedMessages.length : 0;

      // Invalidate and refetch messages
      await queryClient.invalidateQueries({ 
        queryKey: ['conversation_messages', conversationId] 
      });

      // Get updated messages after refetch
      const updatedMessages = queryClient.getQueryData(['conversation_messages', conversationId]);
      const newCount = Array.isArray(updatedMessages) ? updatedMessages.length : 0;

      // Check if there are new messages
      if (newCount > lastMessageCountRef.current && lastMessageCountRef.current > 0) {
        const newMessages = Array.isArray(updatedMessages) 
          ? updatedMessages.slice(lastMessageCountRef.current)
          : [];
        
        // Trigger callback for each new message
        newMessages.forEach(message => {
          if (onNewMessage) {
            onNewMessage(message);
          }
        });

        console.log(`📨 Found ${newMessages.length} new messages in conversation ${conversationId}`);
      }

      lastMessageCountRef.current = newCount;

    } catch (error) {
      console.warn('Error polling conversation messages:', error);
    }
  }, [conversationId, enabled, queryClient, onNewMessage]);

  // Poll for conversation updates (for admin overview)
  const pollConversations = useCallback(async () => {
    if (!enabled) return;

    try {
      // Get current conversations from cache using standardized keys
      const cachedConversations = queryClient.getQueryData(messagingKeys.conversations(''));

      // Invalidate and refetch conversations using standardized keys
      await queryClient.invalidateQueries({ queryKey: messagingKeys.all });

      // Also invalidate general messages queries
      await queryClient.invalidateQueries({ queryKey: messagingKeys.all });

      // Trigger conversation update callback
      if (onConversationUpdate) {
        onConversationUpdate({ updated_at: new Date().toISOString() });
      }

      console.log('🔄 Polled conversations for updates');

    } catch (error) {
      console.warn('Error polling conversations:', error);
    }
  }, [enabled, queryClient, onConversationUpdate]);

  // Main polling function
  const poll = useCallback(async () => {
    if (!enabled) return;

    // Poll conversation messages if we have a specific conversation
    if (conversationId) {
      await pollConversationMessages();
    }

    // Always poll general conversations for admin overview
    await pollConversations();

  }, [enabled, conversationId, pollConversationMessages, pollConversations]);

  // Set up polling interval
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    console.log(`📊 Setting up message polling (interval: ${pollInterval / 1000}s)`);

    // Initial poll
    poll();

    // Set up interval
    intervalRef.current = setInterval(poll, pollInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, pollInterval, poll]);

  // Reset message count when conversation changes
  useEffect(() => {
    lastMessageCountRef.current = 0;
    lastConversationUpdateRef.current = '';
  }, [conversationId]);

  // Manual refresh function
  const refresh = useCallback(() => {
    if (enabled) {
      console.log('🔄 Manual message refresh triggered');
      poll();
    }
  }, [enabled, poll]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    lastMessageCountRef.current = 0;
    lastConversationUpdateRef.current = '';
  }, []);

  // Pause/resume polling
  const pausePolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const resumePolling = useCallback(() => {
    if (enabled && !intervalRef.current) {
      intervalRef.current = setInterval(poll, pollInterval);
    }
  }, [enabled, poll, pollInterval]);

  return { 
    cleanup, 
    refresh, 
    pausePolling, 
    resumePolling,
    isPolling: !!intervalRef.current
  };
}

// Hook for optimistic message updates - now using standardized keys
export function useOptimisticMessages(conversationId: string) {
  const queryClient = useQueryClient();

  const addOptimisticMessage = useCallback((message: any) => {
    return messagingCacheUtils.addOptimisticMessage(queryClient, conversationId, message);
  }, [queryClient, conversationId]);

  const updateOptimisticMessage = useCallback((tempId: string, realMessage: any) => {
    messagingCacheUtils.updateOptimisticMessage(queryClient, conversationId, tempId, realMessage);
  }, [queryClient, conversationId]);

  return { addOptimisticMessage, updateOptimisticMessage };
}

// Hook for message notifications
export function useMessageNotifications() {
  const showNotification = useCallback((message: any) => {
    // Only show notifications if the page is not visible
    if (document.hidden && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('New Message', {
        body: message.content || 'You have a new message',
        icon: '/favicon.ico',
        tag: `message_${message.id}`
      });
    }
  }, []);

  const requestPermission = useCallback(async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return Notification.permission === 'granted';
  }, []);

  return { showNotification, requestPermission };
}
