"use client";

import { useState, useEffect, useRef } from "react";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useRecaptcha } from "@/lib/recaptcha";
import { generateServiceSchema } from "@/lib/seo";
import StructuredData from "@/components/StructuredData";
import {
  ArrowRight,
  Clock,
  Upload,
  X,
  FileImage,
  File,
  Box,
  Palette,
  Building,
  Home,
  TreePine,
  GraduationCap,
  Briefcase,
  Lightbulb,
  Loader2,
} from "lucide-react";
import { fromUrlSafeServiceId } from "@/lib/service-utils";
import { createSampleRequestTracking } from "@/lib/tracking";
import SuccessScreen from "@/components/tracking/SuccessScreen";

// Service categories with icons and sample descriptions
const serviceCategories = [
  {
    id: "creative-design-branding",
    title: "Creative Design & Branding",
    icon: <Palette className="h-6 w-6" />,
    description: "Brand identity and visual communication",
    sampleType: "Brand concept visualization",
    inputExamples: ["Logo sketches", "Brand references", "Color preferences"],
    outputExample: "Professional brand identity package"
  },
  {
    id: "innovative-architectural-design", 
    title: "Innovative Architectural Design",
    icon: <Lightbulb className="h-6 w-6" />,
    description: "Cutting-edge architectural solutions",
    sampleType: "3D architectural visualization",
    inputExamples: ["Sketches", "Floor plans", "Reference photos"],
    outputExample: "Photorealistic 3D rendering"
  },
  {
    id: "interior-design",
    title: "Interior Design", 
    icon: <Home className="h-6 w-6" />,
    description: "Interior space design and visualization",
    sampleType: "Interior concept rendering",
    inputExamples: ["Room photos", "Style references", "Layout sketches"],
    outputExample: "Professional interior visualization"
  },
  {
    id: "urban-architectural-planning",
    title: "Urban & Architectural Planning",
    icon: <Building className="h-6 w-6" />,
    description: "Urban planning and development",
    sampleType: "Urban planning visualization",
    inputExamples: ["Site plans", "Zoning maps", "Development concepts"],
    outputExample: "3D masterplan rendering"
  },
  {
    id: "residential-commercial-projects",
    title: "Residential & Commercial Projects",
    icon: <Briefcase className="h-6 w-6" />,
    description: "Residential and commercial architecture",
    sampleType: "Project visualization",
    inputExamples: ["Building sketches", "Site photos", "Design brief"],
    outputExample: "Architectural rendering"
  },
  {
    id: "landscape-architecture-integration",
    title: "Landscape and Architecture Integration",
    icon: <TreePine className="h-6 w-6" />,
    description: "Landscape and architectural harmony",
    sampleType: "Landscape integration rendering",
    inputExamples: ["Site photos", "Landscape plans", "Building designs"],
    outputExample: "Integrated landscape visualization"
  },
  {
    id: "educational-community-spaces",
    title: "Educational & Community-Oriented Spaces",
    icon: <GraduationCap className="h-6 w-6" />,
    description: "Educational and community facilities",
    sampleType: "Community space visualization",
    inputExamples: ["Space requirements", "Concept sketches", "Site plans"],
    outputExample: "Educational facility rendering"
  }
];

// File format information
const acceptedFormats = [
  { type: "Images", formats: ["JPG", "PNG"], icon: <FileImage className="h-5 w-5" /> },
  { type: "CAD Files", formats: ["DWG"], icon: <File className="h-5 w-5" /> },
  { type: "3D Models", formats: ["SKP", "3DS", "OBJ"], icon: <Box className="h-5 w-5" /> },
  { type: "Documents", formats: ["PDF"], icon: <File className="h-5 w-5" /> }
];

// Before/After Slider Component
function BeforeAfterSlider() {
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = () => setIsDragging(true);
  const handleMouseUp = () => setIsDragging(false);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !sliderRef.current) return;
    
    const rect = sliderRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  };

  return (
    <div 
      ref={sliderRef}
      className="relative w-full h-96 overflow-hidden rounded-lg cursor-col-resize select-none"
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      {/* Before Image */}
      <div className="absolute inset-0">
        <Image
          src="https://images.unsplash.com/photo-1574790398664-0cb03682ed1c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
          alt="Before: Hand sketch"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, 50vw"
        />
        <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded text-sm font-medium">
          BEFORE: Your Sketch
        </div>
      </div>
      
      {/* After Image */}
      <div
        className="absolute inset-0 overflow-hidden"
        style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
      >
        <Image
          src="https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
          alt="After: 3D Rendering"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, 50vw"
        />
        <div className="absolute top-4 right-4 bg-primary/90 text-white px-3 py-1 rounded text-sm font-medium">
          AFTER: Our 3D Render
        </div>
      </div>
      
      {/* Slider Handle */}
      <div 
        className="absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-col-resize"
        style={{ left: `${sliderPosition}%` }}
        onMouseDown={handleMouseDown}
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
          <div className="w-1 h-4 bg-gray-400 rounded"></div>
          <div className="w-1 h-4 bg-gray-400 rounded ml-1"></div>
        </div>
      </div>
    </div>
  );
}

export default function SampleRequestPage() {
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [serviceFromUrl, setServiceFromUrl] = useState<string | null>(null);
  const [trackingNumber, setTrackingNumber] = useState<string>("");
  const [selectedService, setSelectedService] = useState<string>("");

  // Refs for scrolling
  const formSectionRef = useRef<HTMLDivElement>(null);

  // File upload state
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [fileError, setFileError] = useState<string | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    projectDescription: "",
  });

  // reCAPTCHA hook
  const { isLoaded, execute } = useRecaptcha();

  // Get service from URL parameter and pre-populate project type
  useEffect(() => {
    const serviceParam = searchParams.get("service");
    if (serviceParam) {
      const decodedService = fromUrlSafeServiceId(serviceParam);

      // Find matching service in our service categories
      let matchingService = serviceCategories.find(cat => cat.title === decodedService);

      // If not found by exact title match, try to find by partial matching
      if (!matchingService) {
        matchingService = serviceCategories.find(cat => {
          const catWords = cat.title.toLowerCase().split(/[\s&-]+/);
          const serviceWords = decodedService.toLowerCase().split(/[\s&-]+/);
          return catWords.some(word => serviceWords.includes(word)) ||
                 serviceWords.some(word => catWords.includes(word));
        });
      }

      // If still not found, try case-insensitive matching
      if (!matchingService) {
        matchingService = serviceCategories.find(cat =>
          cat.title.toLowerCase() === decodedService.toLowerCase()
        );
      }

      setServiceFromUrl(decodedService);

      if (matchingService) {
        setSelectedService(matchingService.title);

        // Auto-scroll to form section after a short delay
        setTimeout(() => {
          if (formSectionRef.current) {
            formSectionRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }, 500);
      }
    }
  }, [searchParams]);

  // Handle service selection with auto-scroll
  const handleServiceSelection = (serviceTitle: string) => {
    setSelectedService(serviceTitle);

    // Auto-scroll to form section after a short delay
    setTimeout(() => {
      if (formSectionRef.current) {
        formSectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }, 300);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    if (name === 'projectType') {
      setSelectedService(value);
    }
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setFileError(null);

    // Check file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      setFileError("File size exceeds 50MB limit");
      return;
    }

    // Check file type
    const acceptedTypes = [
      "image/jpeg", "image/png", "image/gif", "application/pdf",
      "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/zip", "application/x-zip-compressed", "model/gltf-binary", "model/gltf+json",
      "application/octet-stream"
    ];

    if (!acceptedTypes.includes(file.type) && 
        !file.name.endsWith(".skp") && !file.name.endsWith(".3ds") && 
        !file.name.endsWith(".obj") && !file.name.endsWith(".fbx") &&
        !file.name.endsWith(".dwg")) {
      setFileError("Unsupported file type. Please upload images, PDFs, CAD files, or 3D models.");
      return;
    }

    setIsUploading(true);

    // Create file preview for images
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setFilePreview(event.target?.result as string);
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } else {
      setIsUploading(false);
    }

    setUploadedFile(file);
  };

  const triggerFileInput = () => fileInputRef.current?.click();
  
  const removeFile = () => {
    setUploadedFile(null);
    setFilePreview(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.fullName.trim()) {
      alert('Please enter your full name.');
      return;
    }

    if (!formData.email.trim()) {
      alert('Please enter your email address.');
      return;
    }

    if (!selectedService) {
      alert('Please select a service type.');
      return;
    }

    if (!formData.projectDescription.trim()) {
      alert('Please provide a project description.');
      return;
    }

    if (!isLoaded) {
      alert('Security verification is loading. Please try again in a moment.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Execute reCAPTCHA
      const recaptchaToken = await execute('sample_request');

      const requestData = {
        name: formData.fullName,
        email: formData.email,
        projectType: selectedService,
        description: formData.projectDescription,
        sampleType: serviceCategories.find(cat => cat.title === selectedService)?.sampleType || "General Sample",
        serviceCategory: selectedService,
        fileName: uploadedFile?.name,
        fileType: uploadedFile?.type,
        fileSize: uploadedFile?.size,
        recaptchaToken,
      };

      console.log('Submitting sample request with data:', requestData);

      const trackingData = await createSampleRequestTracking(requestData);

      setTrackingNumber(trackingData.tracking_number);
      setIsSubmitted(true);
    } catch (error) {
      console.error("Error submitting sample request:", error);
      alert(`Error submitting request: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedServiceData = serviceCategories.find(cat => cat.title === selectedService);

  return (
    <Layout>
      {!isSubmitted ? (
        <>
          {/* Hero Section - Mobile Optimized */}
          <section className="relative min-h-[60vh] sm:min-h-[65vh] md:min-h-[70vh] flex items-center">
            <div className="absolute inset-0 z-0">
              <Image
                src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                alt="Architectural design process"
                fill
                className="object-cover"
                priority
                sizes="100vw"
              />
              <div className="absolute inset-0 bg-black/50" />
            </div>
            <div className="container mx-auto px-4 relative z-10 text-white pt-24 sm:pt-28 md:pt-32 lg:pt-20 pb-8">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
                Transform Your Ideas into Reality
              </h1>
              <p className="text-lg sm:text-xl md:text-2xl max-w-3xl leading-relaxed">
                See how we turn your sketches and concepts into professional 3D visualizations.
                Request a free sample in just 24-48 hours.
              </p>
            </div>
          </section>

          {/* Before/After Demonstration Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-4">
              <div className="max-w-6xl mx-auto">
                {/* Section Header */}
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">See the Transformation</h2>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Drag the slider to see how we transform your sketches into professional 3D visualizations
                  </p>
                </div>

                {/* Before/After Slider */}
                <div className="mb-12">
                  <BeforeAfterSlider />
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <Clock className="h-8 w-8 text-primary mx-auto mb-2" />
                    <h3 className="font-semibold mb-1">24-48 Hours</h3>
                    <p className="text-gray-600 text-sm">Fast turnaround time</p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-primary mb-2">FREE</div>
                    <h3 className="font-semibold mb-1">Sample Request</h3>
                    <p className="text-gray-600 text-sm">No cost, no commitment</p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-primary mb-2">7+</div>
                    <h3 className="font-semibold mb-1">Service Types</h3>
                    <p className="text-gray-600 text-sm">Comprehensive solutions</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* What to Upload Section */}
          <section className="py-20 bg-white">
            <div className="container mx-auto px-4">
              <div className="max-w-6xl mx-auto">
                <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">What You Can Upload</h2>

                {/* Desktop: Grid Layout */}
                <div className="hidden md:grid md:grid-cols-4 gap-6 mb-12">
                  {acceptedFormats.map((format, index) => (
                    <div key={index} className="text-center p-6 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex justify-center mb-4 text-primary">
                        {format.icon}
                      </div>
                      <h3 className="font-semibold mb-2">{format.type}</h3>
                      <p className="text-sm text-gray-600">
                        {format.formats.join(", ")}
                      </p>
                    </div>
                  ))}
                </div>

                {/* Mobile: Horizontal Scroll */}
                <div className="md:hidden mb-12">
                  <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 px-4 -mx-4">
                    {acceptedFormats.map((format, index) => (
                      <div key={index} className="flex-shrink-0 w-56 snap-center mr-4 last:mr-0 first:ml-4">
                        <div className="text-center p-6 border rounded-lg h-full">
                          <div className="flex justify-center mb-4 text-primary">
                            {format.icon}
                          </div>
                          <h3 className="font-semibold mb-2">{format.type}</h3>
                          <p className="text-sm text-gray-600">
                            {format.formats.join(", ")}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Mobile scroll indicator */}
                  <div className="flex justify-center mt-4 space-x-2">
                    {acceptedFormats.map((_, index) => (
                      <div key={index} className="w-2 h-2 rounded-full bg-gray-300 opacity-60"></div>
                    ))}
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="font-semibold text-blue-900 mb-4">Examples of what to upload:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
                    <div>
                      <strong>Sketches:</strong> Hand-drawn concepts, rough ideas, napkin sketches
                    </div>
                    <div>
                      <strong>Photos:</strong> Site photos, reference images, inspiration pictures
                    </div>
                    <div>
                      <strong>Plans:</strong> Floor plans, site plans, CAD drawings
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Service Selection & Form Section */}
          <section ref={formSectionRef} className="py-20 bg-gray-50">
            <div className="container mx-auto px-4">
              <div className="max-w-6xl mx-auto">
                <div className="bg-white rounded-lg shadow-lg p-6 md:p-8">
                  <h2 className="text-3xl md:text-4xl font-bold text-center mb-8">Request Your Free Sample</h2>

                  {/* Service Selection */}
                  <div className="mb-8">
                    <h3 className="text-xl font-semibold mb-6">Choose Your Service Type</h3>

                    {/* Desktop: Grid Layout */}
                    <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                      {serviceCategories.map((service) => (
                        <div
                          key={service.id}
                          className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                            selectedService === service.title
                              ? 'border-primary bg-primary/5 shadow-md'
                              : 'border-gray-200 hover:border-primary/50'
                          }`}
                          onClick={() => handleServiceSelection(service.title)}
                        >
                          <div className="flex items-center mb-2">
                            <div className="text-primary mr-3">{service.icon}</div>
                            <h4 className="font-medium text-sm">{service.title}</h4>
                          </div>
                          <p className="text-xs text-gray-600 mb-2">{service.description}</p>
                          <p className="text-xs text-primary font-medium">
                            Sample: {service.sampleType}
                          </p>
                        </div>
                      ))}
                    </div>

                    {/* Mobile: Horizontal Scroll (1.5 cards visible) */}
                    <div className="md:hidden mb-6">
                      <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 px-4 -mx-4">
                        {serviceCategories.map((service, index) => (
                          <div key={service.id} className="flex-shrink-0 w-72 snap-center mr-4 last:mr-0 first:ml-4">
                            <div
                              className={`p-4 border rounded-lg cursor-pointer transition-all h-full ${
                                selectedService === service.title
                                  ? 'border-primary bg-primary/5'
                                  : 'border-gray-200'
                              }`}
                              onClick={() => handleServiceSelection(service.title)}
                            >
                              <div className="flex items-center mb-2">
                                <div className="text-primary mr-3">{service.icon}</div>
                                <h4 className="font-medium text-sm leading-tight">{service.title}</h4>
                              </div>
                              <p className="text-xs text-gray-600 mb-2 leading-relaxed">{service.description}</p>
                              <p className="text-xs text-primary font-medium">
                                Sample: {service.sampleType}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Mobile scroll indicator */}
                      <div className="flex justify-center mt-4 space-x-2">
                        {serviceCategories.map((_, index) => (
                          <div key={index} className="w-2 h-2 rounded-full bg-gray-300 opacity-60"></div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Selected Service Details */}
                  {selectedServiceData && (
                    <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                      <h4 className="font-semibold text-blue-900 mb-3">
                        What you'll receive: {selectedServiceData.outputExample}
                      </h4>
                      <p className="text-sm text-blue-800 mb-2">
                        <strong>Upload examples:</strong> {selectedServiceData.inputExamples.join(", ")}
                      </p>
                      <p className="text-sm text-blue-800">
                        <strong>Delivery:</strong> 24-48 hours
                      </p>
                    </div>
                  )}

                  {/* Form */}
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Full Name *
                        </label>
                        <input
                          type="text"
                          name="fullName"
                          required
                          value={formData.fullName}
                          onChange={handleInputChange}
                          className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                          placeholder="Enter your full name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          name="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                          placeholder="Enter your email address"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Project Description *
                      </label>
                      <textarea
                        name="projectDescription"
                        rows={4}
                        required
                        value={formData.projectDescription}
                        onChange={handleInputChange}
                        placeholder="Describe your project, vision, and what you'd like to see in the sample..."
                        className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors resize-none"
                      />
                    </div>

                    {/* File Upload */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Upload Files (Optional)
                      </label>
                      <div
                        className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-primary hover:bg-gray-50 transition-all duration-200"
                        onClick={triggerFileInput}
                      >
                        {uploadedFile ? (
                          <div className="space-y-3">
                            {filePreview ? (
                              <img src={filePreview} alt="Preview" className="max-h-32 mx-auto rounded shadow-sm" />
                            ) : (
                              <File className="h-12 w-12 text-gray-400 mx-auto" />
                            )}
                            <p className="text-sm font-medium text-gray-900">{uploadedFile.name}</p>
                            <p className="text-xs text-gray-500">
                              {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeFile();
                              }}
                              className="mt-2"
                            >
                              <X className="h-4 w-4 mr-1" />
                              Remove
                            </Button>
                          </div>
                        ) : (
                          <div>
                            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                            <p className="text-sm text-gray-600 mb-1 font-medium">
                              Click to upload or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                              JPG, PNG, PDF, DWG, 3D files (Max 50MB)
                            </p>
                          </div>
                        )}
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        onChange={handleFileChange}
                        className="hidden"
                        accept=".jpg,.jpeg,.png,.pdf,.dwg,.skp,.3ds,.obj,.fbx"
                      />
                      {fileError && (
                        <p className="text-red-600 text-sm mt-2 font-medium">{fileError}</p>
                      )}
                    </div>

                    <div className="text-center pt-6">
                      <Button
                        type="submit"
                        disabled={isSubmitting || !selectedService}
                        size="lg"
                        className="px-8 py-4 font-semibold text-base"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Submitting...
                          </>
                        ) : (
                          <>
                            Request Free Sample
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </>
                        )}
                      </Button>

                      {!selectedService && (
                        <p className="text-sm text-gray-500 mt-3">
                          Please select a service type to continue
                        </p>
                      )}
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </section>
        </>
      ) : (
        <SuccessScreen
          trackingNumber={trackingNumber}
          requestType="sample_request"
        />
      )}
    </Layout>
  );
}
