'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { DisputesList } from '@/components/disputes/DisputesList';
import { Button } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react';

export default function ClientDisputesPage() {
  const { user, loading } = useOptimizedAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  if (loading || !user) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">My Disputes</h1>
        <Button onClick={() => router.push('/client/projects')}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Create New Dispute
        </Button>
      </div>

      <DisputesList userRole="client" />
    </div>
  );
}
