import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';
import { verifyRecaptcha } from '@/lib/recaptcha';
import crypto from 'crypto';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    const { email, source = 'website', recaptchaToken } = await request.json();

    // Validate email
    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { error: 'Valid email address is required' },
        { status: 400 }
      );
    }

    // Verify reCAPTCHA
    if (recaptchaToken) {
      const recaptchaResult = await verifyRecaptcha(recaptchaToken, 'newsletter_subscribe');
      if (!recaptchaResult.success) {
        return NextResponse.json(
          { error: 'Security verification failed. Please try again.' },
          { status: 400 }
        );
      }
    }

    // Generate confirmation token
    const confirmationToken = crypto.randomBytes(32).toString('hex');

    // Check if email already exists
    const { data: existingSubscriber, error: checkError } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('email', email.toLowerCase())
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Database error:', checkError);
      return NextResponse.json(
        { error: 'Database error occurred' },
        { status: 500 }
      );
    }

    let subscriber;

    if (existingSubscriber) {
      if (existingSubscriber.status === 'confirmed') {
        return NextResponse.json(
          { message: 'Email is already subscribed to our newsletter' },
          { status: 200 }
        );
      }

      // Update existing subscriber with new token
      const { data: updatedSubscriber, error: updateError } = await supabase
        .from('newsletter_subscribers')
        .update({
          confirmation_token: confirmationToken,
          status: 'pending',
          subscribed_at: new Date().toISOString(),
          source,
          updated_at: new Date().toISOString()
        })
        .eq('email', email.toLowerCase())
        .select()
        .single();

      if (updateError) {
        console.error('Update error:', updateError);
        return NextResponse.json(
          { error: 'Failed to update subscription' },
          { status: 500 }
        );
      }

      subscriber = updatedSubscriber;
    } else {
      // Create new subscriber
      const { data: newSubscriber, error: insertError } = await supabase
        .from('newsletter_subscribers')
        .insert({
          email: email.toLowerCase(),
          confirmation_token: confirmationToken,
          status: 'pending',
          source,
          metadata: {
            ip: request.ip || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown'
          }
        })
        .select()
        .single();

      if (insertError) {
        console.error('Insert error:', insertError);
        return NextResponse.json(
          { error: 'Failed to create subscription' },
          { status: 500 }
        );
      }

      subscriber = newSubscriber;
    }

    // Send confirmation email
    const confirmationUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/newsletter/confirm?token=${confirmationToken}`;

    try {
      await resend.emails.send({
        from: `${process.env.NEWSLETTER_FROM_NAME} <${process.env.NEWSLETTER_FROM_EMAIL}>`,
        to: email,
        subject: 'Confirm your newsletter subscription',
        html: `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Confirm Newsletter Subscription</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <img src="${process.env.NEXT_PUBLIC_SITE_URL}/seniors-logo.svg" alt="Senior's Archi-Firm" style="height: 60px;">
              </div>
              
              <h1 style="color: #B1956B; text-align: center;">Welcome to Our Newsletter!</h1>
              
              <p>Thank you for subscribing to Senior's Archi-Firm newsletter. To complete your subscription, please confirm your email address by clicking the button below:</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${confirmationUrl}" style="background-color: #B1956B; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Confirm Subscription</a>
              </div>
              
              <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #666;">${confirmationUrl}</p>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              
              <p style="font-size: 14px; color: #666;">
                You're receiving this email because you signed up for our newsletter at seniorsarchifirm.com. 
                If you didn't sign up, you can safely ignore this email.
              </p>
              
              <div style="text-align: center; margin-top: 30px; font-size: 14px; color: #666;">
                <p>Senior's Archi-Firm<br>
                Global HQ – Riyadh, Saudi Arabia<br>
                <a href="mailto:<EMAIL>" style="color: #B1956B;"><EMAIL></a></p>
              </div>
            </body>
          </html>
        `,
        text: `
Welcome to Senior's Archi-Firm Newsletter!

Thank you for subscribing to our newsletter. To complete your subscription, please confirm your email address by visiting this link:

${confirmationUrl}

If you didn't sign up for our newsletter, you can safely ignore this email.

Best regards,
Senior's Archi-Firm Team
<EMAIL>
        `
      });
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the request if email fails, but log it
    }

    return NextResponse.json({
      message: 'Subscription successful! Please check your email to confirm.',
      requiresConfirmation: true
    });

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
