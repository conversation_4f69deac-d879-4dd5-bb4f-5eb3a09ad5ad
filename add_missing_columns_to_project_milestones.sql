-- Add missing columns to project_milestones table
-- This script adds columns that are referenced in the code but might be missing in your database

-- First, let's check if the columns exist before adding them to avoid errors
DO $$
BEGIN
    -- Add completed_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'completed_at'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add approved_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'approved_at'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN approved_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add paid_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'paid_at'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN paid_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add completed_by column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'completed_by'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN completed_by UUID REFERENCES profiles(id);
    END IF;

    -- Add approved_by column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'approved_by'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN approved_by UUID REFERENCES profiles(id);
    END IF;

    -- Add percentage column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'percentage'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN percentage DECIMAL(5, 2) DEFAULT 0;
    END IF;

    -- Add order_index column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'order_index'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN order_index INTEGER DEFAULT 0;
    END IF;

    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    -- Add updated_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END
$$;

-- Create trigger for updated_at field if it doesn't exist
DO $$
BEGIN
    -- Check if the update_modified_column function exists
    IF NOT EXISTS (
        SELECT FROM pg_proc WHERE proname = 'update_modified_column'
    ) THEN
        -- Create the function
        CREATE FUNCTION update_modified_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    END IF;

    -- Create the trigger if it doesn't exist
    IF NOT EXISTS (
        SELECT FROM pg_trigger WHERE tgname = 'update_project_milestones_updated_at'
    ) THEN
        CREATE TRIGGER update_project_milestones_updated_at
        BEFORE UPDATE ON project_milestones
        FOR EACH ROW EXECUTE FUNCTION update_modified_column();
    END IF;
END
$$;

-- Verify the columns were added
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'project_milestones'
ORDER BY ordinal_position;
