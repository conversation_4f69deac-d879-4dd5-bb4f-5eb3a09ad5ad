import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: Request) {
  try {
    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
      apiVersion: '2025-04-30.basil',
    });

    // Get the request body to extract the user ID
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get the authorization header
    const authHeader = request.headers.get('Authorization');

    let supabase;

    // Try to use service role key if available
    if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
      // Create a Supabase client with service role key for admin access
      supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            persistSession: false
          }
        }
      );
    }
    // Fall back to using the client's auth token
    else if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];

      supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          auth: {
            persistSession: false,
            autoRefreshToken: false
          },
          global: {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        }
      );
    } else {
      console.error('No authentication method available');
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }

    // Get user profile
    let profile;
    let customerId = null;

    try {
      // First try to get profile with stripe_customer_id
      const { data, error } = await supabase
        .from('profiles')
        .select('stripe_customer_id, email, full_name')
        .eq('id', userId)
        .single();

      if (error) {
        // If there's an error about the column not existing, try without that column
        if (error.message && error.message.includes('column profiles.stripe_customer_id does not exist')) {
          console.log('stripe_customer_id column does not exist, fetching profile without it');
          const { data: basicProfile, error: basicError } = await supabase
            .from('profiles')
            .select('email, full_name')
            .eq('id', userId)
            .single();

          if (basicError) {
            console.error('Error fetching basic profile:', basicError);
            return NextResponse.json(
              { error: 'User profile not found' },
              { status: 404 }
            );
          }

          profile = basicProfile;
        } else {
          // Some other error occurred
          console.error('Error fetching profile:', error);
          return NextResponse.json(
            { error: 'Failed to fetch user profile' },
            { status: 500 }
          );
        }
      } else {
        profile = data;
        customerId = data.stripe_customer_id;
      }
    } catch (err) {
      console.error('Unexpected error fetching profile:', err);
      return NextResponse.json(
        { error: 'An unexpected error occurred' },
        { status: 500 }
      );
    }

    // If user doesn't have a Stripe customer ID, create one
    if (!customerId) {
      // Create a new customer in Stripe
      const customer = await stripe.customers.create({
        email: profile.email,
        name: profile.full_name,
        metadata: {
          userId: userId,
        },
      });

      customerId = customer.id;

      // Save the customer ID to the user's profile
      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', userId);
    }

    // Create a SetupIntent
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
      usage: 'off_session', // Allow the payment method to be used for future payments
    });

    // Return the client secret
    return NextResponse.json({ clientSecret: setupIntent.client_secret });
  } catch (error: unknown) {
    console.error('Error creating setup intent:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create setup intent' },
      { status: 500 }
    );

  }
}