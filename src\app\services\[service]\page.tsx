"use client";

import { useParams } from "next/navigation";
import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { motion } from "framer-motion";
import {
  ArrowRight,
  CheckCircle,
  Sparkles,
  Users,
  Clock,
  Award,
  ChevronRight,
  Star,
  Quote
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { fromUrlSafeServiceId, toUrlSafeServiceId } from "@/lib/service-utils";

// Service data with comprehensive information
const serviceData = {
  "Creative Design & Branding": {
    title: "Creative Design & Branding",
    subtitle: "Unique architectural identities that tell your story",
    description: "We create distinctive brand experiences through innovative design solutions that reflect your vision and values.",
    heroImage: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    features: [
      "Brand Identity Development",
      "Logo & Visual Identity Design", 
      "Brand Strategy & Positioning",
      "Marketing Material Design",
      "Digital Brand Guidelines",
      "Architectural Branding Solutions"
    ],
    benefits: [
      "Stand out in competitive markets",
      "Build trust and recognition",
      "Attract your ideal clients",
      "Increase perceived value"
    ],
    process: [
      { step: "Discovery", description: "Understanding your brand vision and market position" },
      { step: "Strategy", description: "Developing brand positioning and messaging framework" },
      { step: "Design", description: "Creating visual identity and brand elements" },
      { step: "Implementation", description: "Applying brand across all touchpoints" }
    ],
    packages: [
      {
        name: "Essential",
        price: "Starting at $2,500",
        features: ["Logo Design", "Basic Brand Guidelines", "Business Card Design"]
      },
      {
        name: "Professional", 
        price: "Starting at $5,000",
        features: ["Complete Visual Identity", "Brand Guidelines", "Marketing Materials", "Digital Assets"]
      },
      {
        name: "Enterprise",
        price: "Custom Quote",
        features: ["Full Brand Strategy", "Complete Identity System", "Implementation Support", "Ongoing Consultation"]
      }
    ],
    gallery: [
      "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1634942537034-2531766767d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    ],
    testimonial: {
      text: "The branding work transformed our entire business. We went from being just another architecture firm to having a distinctive identity that clients remember.",
      author: "Sarah Johnson",
      role: "CEO, Modern Spaces Architecture"
    }
  },
  "Innovative Architectural Design": {
    title: "Innovative Architectural Design", 
    subtitle: "Cutting-edge solutions that push boundaries",
    description: "We create architectural solutions that combine innovation with functionality, pushing the boundaries of what's possible.",
    heroImage: "https://images.unsplash.com/photo-1577493340887-b7bfff550145?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    features: [
      "Parametric Design Solutions",
      "Sustainable Architecture",
      "Smart Building Integration", 
      "Advanced Material Research",
      "3D Modeling & Visualization",
      "Structural Innovation"
    ],
    benefits: [
      "Future-proof designs",
      "Energy efficiency",
      "Unique architectural solutions",
      "Enhanced functionality"
    ],
    process: [
      { step: "Research", description: "Analyzing site conditions and innovative possibilities" },
      { step: "Concept", description: "Developing cutting-edge design concepts" },
      { step: "Development", description: "Refining designs with advanced technology" },
      { step: "Execution", description: "Implementing innovative construction methods" }
    ],
    packages: [
      {
        name: "Concept Design",
        price: "Starting at $15,000", 
        features: ["Initial Concept", "3D Visualizations", "Basic Documentation"]
      },
      {
        name: "Full Design",
        price: "Starting at $35,000",
        features: ["Complete Design Package", "Advanced Modeling", "Construction Documents", "Consultation"]
      },
      {
        name: "Innovation Lab",
        price: "Custom Quote",
        features: ["Experimental Design", "Research & Development", "Prototype Development", "Full Implementation"]
      }
    ],
    gallery: [
      "https://images.unsplash.com/photo-1577493340887-b7bfff550145?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    ],
    testimonial: {
      text: "Their innovative approach resulted in a building that's not just beautiful, but also 40% more energy efficient than traditional designs.",
      author: "Michael Chen",
      role: "Project Director, Green Tech Campus"
    }
  },
  "Interior Design": {
    title: "Interior Design",
    subtitle: "Immersive spaces that inspire and function beautifully", 
    description: "We craft interior environments that balance aesthetics with functionality, creating spaces that enhance daily life.",
    heroImage: "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    features: [
      "Space Planning & Layout",
      "Material & Finish Selection",
      "Custom Furniture Design",
      "Lighting Design",
      "Color Consultation", 
      "Project Management"
    ],
    benefits: [
      "Optimized space utilization",
      "Enhanced comfort and productivity",
      "Increased property value",
      "Personalized living experience"
    ],
    process: [
      { step: "Assessment", description: "Evaluating space potential and client needs" },
      { step: "Planning", description: "Creating optimal layouts and design concepts" },
      { step: "Selection", description: "Choosing materials, furniture, and finishes" },
      { step: "Installation", description: "Managing implementation and final styling" }
    ],
    packages: [
      {
        name: "Design Consultation",
        price: "Starting at $1,500",
        features: ["Space Assessment", "Design Recommendations", "Material Suggestions"]
      },
      {
        name: "Full Interior Design",
        price: "Starting at $8,000", 
        features: ["Complete Design Package", "3D Renderings", "Material Sourcing", "Project Coordination"]
      },
      {
        name: "Luxury Design",
        price: "Custom Quote",
        features: ["Premium Materials", "Custom Furniture", "Full-Service Implementation", "Ongoing Support"]
      }
    ],
    gallery: [
      "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1618219908412-a29a1bb7b86e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    ],
    testimonial: {
      text: "They transformed our office into a space that our team actually loves coming to. Productivity has increased significantly.",
      author: "Lisa Rodriguez",
      role: "HR Director, Creative Solutions Inc."
    }
  },
  "Urban & Architectural Planning": {
    title: "Urban & Architectural Planning",
    subtitle: "Sustainable community solutions for the future",
    description: "We develop comprehensive urban solutions and master plans that create sustainable, livable communities.",
    heroImage: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    features: [
      "Master Planning",
      "Zoning Analysis",
      "Urban Design",
      "Transportation Planning",
      "Environmental Impact Assessment",
      "Community Engagement"
    ],
    benefits: [
      "Sustainable development",
      "Enhanced quality of life",
      "Economic growth",
      "Environmental protection"
    ],
    process: [
      { step: "Analysis", description: "Comprehensive site and community analysis" },
      { step: "Planning", description: "Developing master plan and zoning strategies" },
      { step: "Design", description: "Creating detailed urban design solutions" },
      { step: "Implementation", description: "Phased development and ongoing consultation" }
    ],
    packages: [
      {
        name: "Site Analysis",
        price: "Starting at $10,000",
        features: ["Site Assessment", "Zoning Review", "Preliminary Recommendations"]
      },
      {
        name: "Master Planning",
        price: "Starting at $50,000",
        features: ["Complete Master Plan", "Phasing Strategy", "Design Guidelines", "Community Consultation"]
      },
      {
        name: "Full Development",
        price: "Custom Quote", 
        features: ["Comprehensive Planning", "Environmental Studies", "Implementation Support", "Ongoing Consultation"]
      }
    ],
    gallery: [
      "https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1524813686514-a57563d77965?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    ],
    testimonial: {
      text: "Their master planning approach created a development that's become a model for sustainable urban growth in our region.",
      author: "David Park",
      role: "City Planning Director"
    }
  },
  "Residential & Commercial Projects": {
    title: "Residential & Commercial Projects",
    subtitle: "Tailored architectural solutions for every need",
    description: "We deliver customized architectural solutions for both residential and commercial clients, creating spaces that work.",
    heroImage: "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    features: [
      "Custom Home Design",
      "Commercial Architecture",
      "Renovation & Additions",
      "Mixed-Use Development",
      "Retail Space Design",
      "Office Building Design"
    ],
    benefits: [
      "Customized solutions",
      "Increased property value",
      "Functional design",
      "Timely delivery"
    ],
    process: [
      { step: "Consultation", description: "Understanding project requirements and goals" },
      { step: "Design", description: "Creating tailored architectural solutions" },
      { step: "Documentation", description: "Detailed construction drawings and specifications" },
      { step: "Support", description: "Construction administration and project support" }
    ],
    packages: [
      {
        name: "Residential Design",
        price: "Starting at $12,000",
        features: ["Custom Home Design", "Construction Documents", "3D Visualizations"]
      },
      {
        name: "Commercial Design",
        price: "Starting at $25,000",
        features: ["Commercial Architecture", "Code Compliance", "Project Management", "Construction Support"]
      },
      {
        name: "Mixed-Use Development",
        price: "Custom Quote",
        features: ["Complex Planning", "Multi-Phase Design", "Regulatory Approval", "Full Project Support"]
      }
    ],
    gallery: [
      "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1577493340887-b7bfff550145?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    ],
    testimonial: {
      text: "From concept to completion, they delivered exactly what we envisioned. Our new headquarters has become a landmark in the area.",
      author: "Jennifer Walsh",
      role: "CEO, Walsh Enterprises"
    }
  },
  "Landscape and Architecture Integration": {
    title: "Landscape and Architecture Integration",
    subtitle: "Harmonizing built environments with nature",
    description: "We create seamless connections between architectural design and natural landscapes, enhancing both built and natural environments.",
    heroImage: "https://images.unsplash.com/photo-1600573472550-8090b5e0745e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    features: [
      "Landscape Architecture",
      "Site Planning",
      "Environmental Integration",
      "Outdoor Living Spaces",
      "Sustainable Landscaping",
      "Water Feature Design"
    ],
    benefits: [
      "Enhanced property value",
      "Environmental sustainability",
      "Improved quality of life",
      "Reduced maintenance costs"
    ],
    process: [
      { step: "Site Analysis", description: "Comprehensive environmental and topographical study" },
      { step: "Integration Design", description: "Creating harmony between architecture and landscape" },
      { step: "Implementation", description: "Coordinated construction of integrated elements" },
      { step: "Maintenance", description: "Ongoing landscape management and care guidance" }
    ],
    packages: [
      {
        name: "Landscape Design",
        price: "Starting at $8,000",
        features: ["Site Analysis", "Landscape Plan", "Plant Selection", "Installation Guidance"]
      },
      {
        name: "Integrated Design",
        price: "Starting at $20,000",
        features: ["Architecture Integration", "Comprehensive Planning", "Environmental Systems", "Project Coordination"]
      },
      {
        name: "Estate Planning",
        price: "Custom Quote",
        features: ["Master Site Planning", "Phased Implementation", "Ongoing Consultation", "Maintenance Programs"]
      }
    ],
    gallery: [
      "https://images.unsplash.com/photo-1600573472550-8090b5e0745e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    ],
    testimonial: {
      text: "The integration of our building with the natural landscape is so seamless, visitors often think it grew there naturally.",
      author: "Robert Kim",
      role: "Resort Owner, Mountain View Lodge"
    }
  },
  "Educational & Community-Oriented Spaces": {
    title: "Educational & Community-Oriented Spaces",
    subtitle: "Designing spaces that foster learning and community",
    description: "We design inclusive spaces that promote learning, community engagement, and social interaction for all ages and abilities.",
    heroImage: "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    features: [
      "Educational Facility Design",
      "Community Center Planning",
      "Accessible Design",
      "Flexible Learning Spaces",
      "Public Space Design",
      "Cultural Institution Design"
    ],
    benefits: [
      "Enhanced learning outcomes",
      "Community engagement",
      "Inclusive accessibility",
      "Flexible functionality"
    ],
    process: [
      { step: "Community Input", description: "Gathering stakeholder needs and community vision" },
      { step: "Functional Design", description: "Creating flexible, accessible learning environments" },
      { step: "Implementation", description: "Coordinated construction with minimal disruption" },
      { step: "Activation", description: "Supporting community adoption and ongoing use" }
    ],
    packages: [
      {
        name: "Educational Design",
        price: "Starting at $15,000",
        features: ["Classroom Design", "Learning Environment Planning", "Accessibility Compliance"]
      },
      {
        name: "Community Facility",
        price: "Starting at $30,000",
        features: ["Multi-Use Design", "Community Consultation", "Flexible Spaces", "Full Documentation"]
      },
      {
        name: "Campus Planning",
        price: "Custom Quote",
        features: ["Master Planning", "Phased Development", "Community Integration", "Long-term Strategy"]
      }
    ],
    gallery: [
      "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1577493340887-b7bfff550145?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    ],
    testimonial: {
      text: "The new community center has become the heart of our neighborhood. It's amazing how good design can bring people together.",
      author: "Maria Santos",
      role: "Community Board President"
    }
  }
};

export default function ServiceDetailPage() {
  const params = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [service, setService] = useState<any>(null);

  // Get service from URL parameter
  const serviceParam = typeof params.service === 'string' ? params.service : '';

  useEffect(() => {
    const serviceName = fromUrlSafeServiceId(serviceParam);

    // Try to find the service with better matching
    let foundService = serviceData[serviceName as keyof typeof serviceData];

    // If not found, try case-insensitive matching
    if (!foundService) {
      const availableKeys = Object.keys(serviceData);
      const matchingKey = availableKeys.find(key =>
        key.toLowerCase() === serviceName.toLowerCase()
      );
      if (matchingKey) {
        foundService = serviceData[matchingKey as keyof typeof serviceData];
      }
    }

    // If still not found, try partial matching
    if (!foundService) {
      const availableKeys = Object.keys(serviceData);
      const matchingKey = availableKeys.find(key => {
        const keyWords = key.toLowerCase().split(/[\s&-]+/);
        const serviceWords = serviceName.toLowerCase().split(/[\s&-]+/);
        return keyWords.some(word => serviceWords.includes(word)) ||
               serviceWords.some(word => keyWords.includes(word));
      });
      if (matchingKey) {
        foundService = serviceData[matchingKey as keyof typeof serviceData];
      }
    }

    setService(foundService);

    // Simulate loading for smooth transitions
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, [serviceParam]);

  if (isLoading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading service details...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!service) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Service Not Found</h1>
            <p className="text-gray-600 mb-8">The service you're looking for doesn't exist.</p>
            <Link href="/services">
              <button className="bg-brown-600 text-white px-6 py-2 rounded hover:bg-brown-700 transition-colors flex items-center justify-center">
                <ArrowRight className="mr-2 h-4 w-4" />
                Back to Services
              </button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Hero Section - Mobile Optimized */}
      <section className="relative min-h-[70vh] sm:min-h-[75vh] md:min-h-[80vh] lg:min-h-[85vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <Image
            src={service.heroImage}
            alt={service.title}
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        <div className="container mx-auto px-4 relative z-10 text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl pt-24 sm:pt-28 md:pt-32 lg:pt-20 pb-8"
          >
            {/* Service Title - Mobile Optimized Typography */}
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
              {service.title}
            </h1>

            {/* Subtitle - Responsive */}
            <p className="text-lg sm:text-xl md:text-2xl mb-6 sm:mb-8 text-gray-200 leading-relaxed">
              {service.subtitle}
            </p>

            {/* Description - Concise on Mobile */}
            <p className="text-base sm:text-lg mb-6 sm:mb-8 max-w-2xl leading-relaxed">
              {service.description}
            </p>

            {/* Hero CTAs - Mobile Optimized */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <Link href={`/vision-builder/${toUrlSafeServiceId(service.title)}`}>
                <button className="bg-primary text-white px-6 sm:px-8 py-3 rounded-lg hover:bg-primary/90 transition-all duration-200 flex items-center justify-center w-full sm:w-auto font-medium hover:scale-105">
                  <Sparkles className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  Build Your Vision
                </button>
              </Link>
              <Link href={`/sample-request?service=${toUrlSafeServiceId(service.title)}`}>
                <button className="bg-white/10 backdrop-blur-sm border border-white text-white px-6 sm:px-8 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-all duration-200 flex items-center justify-center w-full sm:w-auto font-medium">
                  Request Free Sample
                  <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features & Benefits Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
              {/* What We Offer */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl md:text-4xl font-bold mb-8">What We Offer</h2>
                <div className="space-y-4">
                  {service.features.map((feature, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center"
                    >
                      <CheckCircle className="h-6 w-6 text-primary mr-4 flex-shrink-0" />
                      <span className="text-lg text-gray-700">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Benefits */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl md:text-4xl font-bold mb-8">Why Choose This Service</h2>
                <div className="space-y-6">
                  {service.benefits.map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-start"
                    >
                      <Star className="h-6 w-6 text-primary mr-4 flex-shrink-0 mt-1" />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{benefit}</h3>
                        <p className="text-gray-600">Experience the difference that professional {service.title.toLowerCase()} can make for your project.</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Work</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Explore examples of our {service.title.toLowerCase()} projects and see the quality we deliver.
              </p>
            </motion.div>

            {/* Desktop: 3-column grid, Mobile: horizontal scroll */}
            <div className="hidden md:grid md:grid-cols-3 gap-8">
              {service.gallery.map((image, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative aspect-[4/3] overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                >
                  <Image
                    src={image}
                    alt={`${service.title} example ${index + 1}`}
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </motion.div>
              ))}
            </div>

            {/* Mobile: horizontal scroll */}
            <div className="md:hidden">
              <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 px-4 -mx-4">
                {service.gallery.map((image, index) => (
                  <div key={index} className="flex-shrink-0 w-80 snap-center mr-4 last:mr-0 first:ml-4">
                    <div className="relative aspect-[4/3] overflow-hidden rounded-lg shadow-lg">
                      <Image
                        src={image}
                        alt={`${service.title} example ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Process</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                We follow a proven methodology to ensure exceptional results for every project.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {service.process.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                      {index + 1}
                    </div>
                    {index < service.process.length - 1 && (
                      <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gray-200 -z-10" />
                    )}
                  </div>
                  <h3 className="text-xl font-bold mb-3">{step.step}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Packages Section - COMMENTED OUT UNTIL CLIENT PROVIDES PRICING */}
      {false && (
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-16"
              >
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Service Packages</h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Choose the package that best fits your project needs and budget.
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {service.packages.map((pkg, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className={`bg-white rounded-lg shadow-lg p-8 relative ${
                      index === 1 ? 'ring-2 ring-primary scale-105' : ''
                    }`}
                  >
                    {index === 1 && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span className="bg-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold mb-2">{pkg.name}</h3>
                      <div className="text-3xl font-bold text-primary mb-4">{pkg.price}</div>
                    </div>

                    <ul className="space-y-4 mb-8">
                      {pkg.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center">
                          <CheckCircle className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <div className="space-y-3">
                      <Link href={`/vision-builder/${toUrlSafeServiceId(service.title)}`}>
                        <button className={`w-full px-6 py-2 rounded transition-colors flex items-center justify-center ${
                          index === 1
                            ? 'bg-brown-600 text-white hover:bg-brown-700'
                            : 'border border-brown-600 text-brown-600 hover:bg-brown-50'
                        }`}>
                          <Sparkles className="mr-2 h-4 w-4" />
                          Build Your Vision
                        </button>
                      </Link>
                      <Link href={`/sample-request?service=${toUrlSafeServiceId(service.title)}`}>
                        <button className="w-full border border-brown-600 text-brown-600 px-6 py-2 rounded hover:bg-brown-50 transition-colors flex items-center justify-center">
                          Request Sample
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </button>
                      </Link>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Testimonial Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Quote className="h-12 w-12 text-primary mx-auto mb-8" />
              <blockquote className="text-2xl md:text-3xl font-light text-gray-900 mb-8 leading-relaxed">
                "{service.testimonial.text}"
              </blockquote>
              <div className="flex items-center justify-center">
                <div className="text-center">
                  <div className="font-semibold text-lg text-gray-900">{service.testimonial.author}</div>
                  <div className="text-gray-600">{service.testimonial.role}</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Start Your {service.title} Project?
              </h2>
              <p className="text-xl mb-8 text-gray-200">
                Let's bring your vision to life with our expert {service.title.toLowerCase()} services.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href={`/vision-builder/${toUrlSafeServiceId(service.title)}`}>
                  <button className="bg-white text-brown-600 px-8 py-3 rounded hover:bg-gray-100 transition-colors flex items-center justify-center w-full sm:w-auto">
                    <Sparkles className="mr-2 h-5 w-5" />
                    Build Your Vision
                  </button>
                </Link>
                <Link href={`/sample-request?service=${toUrlSafeServiceId(service.title)}`}>
                  <button className="bg-white/10 border border-white text-white px-8 py-3 rounded hover:bg-white hover:text-brown-600 transition-colors flex items-center justify-center w-full sm:w-auto">
                    Request Free Sample
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </button>
                </Link>
              </div>

              <div className="mt-8 pt-8 border-t border-white/20">
                <Link href="/services" className="inline-flex items-center text-white/80 hover:text-white transition-colors">
                  <ChevronRight className="mr-2 h-4 w-4 rotate-180" />
                  Back to All Services
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
