import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { consent_given, consent_types, session_id } = await request.json();

    if (!session_id) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Get client IP and user agent
    const ip = request.ip || 
               request.headers.get('x-forwarded-for')?.split(',')[0] || 
               request.headers.get('x-real-ip') || 
               'unknown';
    
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Check if consent already exists for this session
    const { data: existingConsent, error: checkError } = await supabase
      .from('cookie_consents')
      .select('*')
      .eq('session_id', session_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Database check error:', checkError);
      return NextResponse.json(
        { error: 'Database error occurred' },
        { status: 500 }
      );
    }

    if (existingConsent) {
      // Update existing consent
      const { error: updateError } = await supabase
        .from('cookie_consents')
        .update({
          consent_given,
          consent_types,
          updated_at: new Date().toISOString()
        })
        .eq('session_id', session_id);

      if (updateError) {
        console.error('Consent update error:', updateError);
        return NextResponse.json(
          { error: 'Failed to update consent' },
          { status: 500 }
        );
      }
    } else {
      // Create new consent record
      const { error: insertError } = await supabase
        .from('cookie_consents')
        .insert({
          session_id,
          ip_address: ip,
          user_agent: userAgent,
          consent_given,
          consent_types
        });

      if (insertError) {
        console.error('Consent insert error:', insertError);
        return NextResponse.json(
          { error: 'Failed to save consent' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      message: 'Cookie consent saved successfully'
    });

  } catch (error) {
    console.error('Cookie consent error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Get consent for this session
    const { data: consent, error } = await supabase
      .from('cookie_consents')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Database error occurred' },
        { status: 500 }
      );
    }

    if (!consent) {
      return NextResponse.json(
        { error: 'No consent found for this session' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      consent_given: consent.consent_given,
      consent_types: consent.consent_types,
      consent_date: consent.consent_date
    });

  } catch (error) {
    console.error('Cookie consent retrieval error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
