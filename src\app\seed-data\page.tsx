"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";

export default function SeedData() {
  const { user, profile } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  const checkTableExists = async (tableName: string) => {
    try {
      const { error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true })
        .limit(1);

      return !error;
    } catch {
      return false;
    }
  };

  const seedTestData = async () => {
    if (!user || !profile) {
      setMessage("Please log in first");
      return;
    }

    setLoading(true);
    setMessage("Checking database tables...");

    try {
      // Check if required tables exist
      const tablesExist = {
        profiles: await checkTableExists('profiles'),
        connections: await checkTableExists('connections'),
        project_briefs: await checkTableExists('project_briefs'),
        project_proposals_enhanced: await checkTableExists('project_proposals_enhanced'),
        projects: await checkTableExists('projects')
      };

      console.log('Table existence check:', tablesExist);

      const missingTables = Object.entries(tablesExist)
        .filter(([_, exists]) => !exists)
        .map(([table, _]) => table);

      if (missingTables.length > 0) {
        setMessage(`❌ Missing database tables: ${missingTables.join(', ')}. Please run the database migrations first.`);
        return;
      }

      setMessage("All tables found. Starting data seeding...");
      // 1. Create a test designer if user is client
      if (profile.role === 'client') {
        setMessage("Step 1: Checking for existing designer...");

        // Check if designer exists
        const { data: existingDesigner, error: checkError } = await supabase
          .from('profiles')
          .select('id')
          .eq('role', 'designer')
          .limit(1)
          .single();

        if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
          console.error('Error checking for designer:', checkError);
          throw new Error(`Failed to check for existing designer: ${checkError.message}`);
        }

        let designerId = existingDesigner?.id;

        if (!designerId) {
          setMessage("Step 1b: Creating test designer...");

          // Create a test designer
          const { data: newDesigner, error: designerError } = await supabase
            .from('profiles')
            .insert([{
              full_name: 'Test Designer',
              email: '<EMAIL>',
              role: 'designer',
              skills: ['Interior Design', 'Architecture']
            }])
            .select()
            .single();

          if (designerError) {
            console.error('Error creating designer:', designerError);
            throw new Error(`Failed to create designer: ${designerError.message}`);
          }
          designerId = newDesigner.id;
        }

        // 2. Create connection between client and designer
        setMessage("Step 2: Creating connection...");

        const { error: connectionError } = await supabase
          .from('connections')
          .upsert([{
            client_id: user.id,
            designer_id: designerId,
            status: 'active'
          }], { onConflict: 'designer_id,client_id' });

        if (connectionError) {
          console.error('Error creating connection:', connectionError);
          throw new Error(`Failed to create connection: ${connectionError.message}`);
        }

        // 3. Create test project briefs
        setMessage("Step 3: Creating project briefs...");

        const { error: briefError } = await supabase
          .from('project_briefs')
          .upsert([
            {
              client_id: user.id,
              title: 'Modern Living Room Redesign',
              description: 'Looking to redesign my living room with a modern aesthetic',
              budget_range: '10k_25k',
              timeline_preference: '2-3 months',
              urgency: 'medium',
              status: 'pending'
            },
            {
              client_id: user.id,
              title: 'Kitchen Renovation',
              description: 'Complete kitchen renovation with new appliances',
              budget_range: '25k_plus',
              timeline_preference: '3-4 months',
              urgency: 'low',
              status: 'assigned',
              assigned_designer_id: designerId
            }
          ], { onConflict: 'id' });

        if (briefError) {
          console.error('Error creating briefs:', briefError);
          throw new Error(`Failed to create briefs: ${briefError.message}`);
        }

        // 4. Get brief IDs for proposals
        const { data: briefs } = await supabase
          .from('project_briefs')
          .select('id')
          .eq('client_id', user.id);

        if (briefs && briefs.length > 0) {
          // 5. Create test proposals
          const { error: proposalError } = await supabase
            .from('project_proposals_enhanced')
            .upsert([
              {
                brief_id: briefs[0].id,
                designer_id: designerId,
                title: 'Modern Living Room Proposal',
                description: 'Comprehensive redesign with modern furniture and lighting',
                total_budget: 15000,
                timeline_weeks: 8,
                status: 'submitted',
                submitted_at: new Date().toISOString()
              }
            ], { onConflict: 'id' });

          if (proposalError) throw proposalError;
        }

        // 6. Create test projects
        const { error: projectError } = await supabase
          .from('projects')
          .upsert([
            {
              client_id: user.id,
              designer_id: designerId,
              title: 'Bedroom Makeover',
              description: 'Complete bedroom renovation',
              status: 'in_progress',
              budget: 8000,
              progress: 45
            },
            {
              client_id: user.id,
              designer_id: designerId,
              title: 'Bathroom Renovation',
              description: 'Modern bathroom design',
              status: 'completed',
              budget: 12000,
              progress: 100
            }
          ], { onConflict: 'id' });

        if (projectError) throw projectError;

        setMessage("✅ Test data seeded successfully! Check your dashboard.");
      } else {
        setMessage("This seeding is designed for client accounts.");
      }
    } catch (error: any) {
      console.error('Error seeding data:', error);
      console.error('Error details:', {
        message: error?.message,
        code: error?.code,
        details: error?.details,
        hint: error?.hint,
        full: error
      });
      setMessage(`❌ Error: ${error?.message || error?.code || 'Unknown error occurred'}`);
    } finally {
      setLoading(false);
    }
  };

  const testBasicOperations = async () => {
    if (!user || !profile) {
      setMessage("Please log in first");
      return;
    }

    setLoading(true);
    setMessage("Testing basic database operations...");

    try {
      // Test 1: Read from profiles table
      setMessage("Test 1: Reading profiles...");
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, full_name, role')
        .eq('id', user.id)
        .single();

      if (profileError) {
        throw new Error(`Profile read failed: ${profileError.message}`);
      }

      console.log('Profile data:', profileData);

      // Test 2: Try to read connections
      setMessage("Test 2: Reading connections...");
      const { data: connectionsData, error: connectionsError } = await supabase
        .from('connections')
        .select('*')
        .eq('client_id', user.id);

      if (connectionsError) {
        throw new Error(`Connections read failed: ${connectionsError.message}`);
      }

      console.log('Connections data:', connectionsData);

      // Test 3: Try to read project_briefs
      setMessage("Test 3: Reading project briefs...");
      const { data: briefsData, error: briefsError } = await supabase
        .from('project_briefs')
        .select('*')
        .eq('client_id', user.id);

      if (briefsError) {
        throw new Error(`Project briefs read failed: ${briefsError.message}`);
      }

      console.log('Briefs data:', briefsData);

      setMessage("✅ All basic operations successful! Database is working correctly.");
    } catch (error: any) {
      console.error('Basic operations test failed:', error);
      setMessage(`❌ Basic test failed: ${error?.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const clearTestData = async () => {
    if (!user || !profile) {
      setMessage("Please log in first");
      return;
    }

    setLoading(true);
    setMessage("Clearing test data...");

    try {
      // Delete in reverse order of dependencies
      await supabase.from('project_proposals_enhanced').delete().eq('designer_id', user.id);
      await supabase.from('projects').delete().eq('client_id', user.id);
      await supabase.from('project_briefs').delete().eq('client_id', user.id);
      await supabase.from('connections').delete().eq('client_id', user.id);

      setMessage("✅ Test data cleared successfully!");
    } catch (error: any) {
      console.error('Error clearing data:', error);
      setMessage(`❌ Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return <div>Please log in to seed test data</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Seed Test Data</h1>
      <p className="mb-4">User: {profile?.full_name} ({profile?.role})</p>
      
      <div className="space-x-4 mb-4">
        <Button
          onClick={testBasicOperations}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {loading ? 'Testing...' : 'Test Database'}
        </Button>

        <Button
          onClick={seedTestData}
          disabled={loading}
          className="bg-green-600 hover:bg-green-700"
        >
          {loading ? 'Seeding...' : 'Seed Test Data'}
        </Button>

        <Button
          onClick={clearTestData}
          disabled={loading}
          variant="outline"
          className="border-red-600 text-red-600 hover:bg-red-50"
        >
          {loading ? 'Clearing...' : 'Clear Test Data'}
        </Button>
      </div>

      {message && (
        <div className={`p-4 rounded ${
          message.includes('✅') ? 'bg-green-100 text-green-800' : 
          message.includes('❌') ? 'bg-red-100 text-red-800' : 
          'bg-blue-100 text-blue-800'
        }`}>
          {message}
        </div>
      )}
    </div>
  );
}
