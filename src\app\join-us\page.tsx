"use client";

import { useState } from "react";
import Layout from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  ArrowRight,
  Check,
  FileText,
  ThumbsUp,
  Monitor,
  FileCheck,
  Award,
  ChevronDown,
  ChevronUp,
  Upload,
  X,
  AlertCircle,
  CheckCircle,
  Loader2,
  Image,
  MapPin,
  Phone,
  Mail,
  Briefcase
} from "lucide-react";

const howItWorksSteps = [
  {
    icon: <FileText className="h-6 w-6 text-primary" />,
    title: "Receive Task/Project",
    description: "Get notified of new projects that match your skills and expertise. Each project comes with clear requirements and deadlines."
  },
  {
    icon: <ThumbsUp className="h-6 w-6 text-primary" />,
    title: "Accept or Decline the Project",
    description: "Review project details and decide if it fits your schedule and interests. You have full control over which projects you take on."
  },
  {
    icon: <Monitor className="h-6 w-6 text-primary" />,
    title: "Work Remotely on Tasks",
    description: "Complete your assigned tasks from anywhere in the world, on your own schedule while meeting project milestones."
  },
  {
    icon: <FileCheck className="h-6 w-6 text-primary" />,
    title: "Deliver Drafts and Revisions",
    description: "Submit your work through our platform and collaborate with the team on feedback and revisions until client approval."
  },
  {
    icon: <Award className="h-6 w-6 text-primary" />,
    title: "Build a Project Reputation",
    description: "Earn ratings and reviews for your work, building your reputation within our network to access more prestigious projects."
  }
];

const joinSteps = [
  {
    number: "01",
    title: "Apply Online",
    description: "Fill out our simple application form with your details, experience, and portfolio links."
  },
  {
    number: "02",
    title: "Quick Skills Test",
    description: "Complete a brief design challenge to demonstrate your approach and creative thinking."
  },
  {
    number: "03",
    title: "Get Projects Assigned",
    description: "Once approved, start receiving project opportunities matched to your skills and experience level."
  }
];

export default function JoinUsPage() {
  const [howItWorksExpanded, setHowItWorksExpanded] = useState(false);
  const [joinStepsExpanded, setJoinStepsExpanded] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    full_name: '',
    phone: '',
    location: '',
    specialization: '',
    experience: '',
    portfolio_url: '',
    bio: ''
  });
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [portfolioFiles, setPortfolioFiles] = useState<File[]>([]);
  const [certificateFiles, setCertificateFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Helper functions
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleResumeUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        setError('Resume file size must be less than 10MB');
        return;
      }
      if (file.type !== 'application/pdf') {
        setError('Resume must be a PDF file');
        return;
      }
      setResumeFile(file);
      setError(null);
    }
  };

  const handlePortfolioUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles: File[] = [];

    for (const file of files) {
      if (file.size > 50 * 1024 * 1024) {
        setError(`File ${file.name} is too large. Maximum size is 50MB.`);
        return;
      }

      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setError(`File ${file.name} is not a supported format. Please use PDF or image files.`);
        return;
      }

      validFiles.push(file);
    }

    setPortfolioFiles(prev => [...prev, ...validFiles]);
    setError(null);
  };

  const removePortfolioFile = (index: number) => {
    setPortfolioFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleCertificateUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles: File[] = [];

    for (const file of files) {
      if (file.size > 10 * 1024 * 1024) {
        setError(`Certificate file ${file.name} is too large. Maximum size is 10MB.`);
        return;
      }

      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        setError(`Certificate file ${file.name} is not a supported format. Please use PDF or image files.`);
        return;
      }

      validFiles.push(file);
    }

    setCertificateFiles(prev => [...prev, ...validFiles]);
    setError(null);
  };

  const removeCertificateFile = (index: number) => {
    setCertificateFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async (applicationId: string) => {
    const uploadResults = {
      resumeKey: '',
      portfolioKeys: [] as string[],
      certificateKeys: [] as string[]
    };

    try {
      // Upload resume if provided
      if (resumeFile) {
        const formData = new FormData();
        formData.append('file', resumeFile);
        formData.append('fileType', 'resume');
        formData.append('applicationId', applicationId);

        const response = await fetch('/api/designer-application-upload', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error('Failed to upload resume');
        }

        const result = await response.json();
        uploadResults.resumeKey = result.fileKey; // Store the actual R2 key
        console.log('Resume uploaded with key:', result.fileKey);
      }

      // Upload portfolio files
      for (let i = 0; i < portfolioFiles.length; i++) {
        const file = portfolioFiles[i];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('fileType', 'portfolio');
        formData.append('applicationId', applicationId);
        formData.append('fileIndex', i.toString());

        const response = await fetch('/api/designer-application-upload', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const result = await response.json();
        uploadResults.portfolioKeys.push(result.fileKey); // Store the actual R2 key
        console.log(`Portfolio file ${i} uploaded with key:`, result.fileKey);
      }

      // Upload certificate files
      for (let i = 0; i < certificateFiles.length; i++) {
        const file = certificateFiles[i];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('fileType', 'certificate');
        formData.append('applicationId', applicationId);
        formData.append('fileIndex', i.toString());

        const response = await fetch('/api/designer-application-upload', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Failed to upload certificate ${file.name}`);
        }

        const result = await response.json();
        uploadResults.certificateKeys.push(result.fileKey); // Store the actual R2 key
        console.log(`Certificate file ${i} uploaded with key:`, result.fileKey);
      }

      return uploadResults;
    } catch (error) {
      console.error('Error uploading files:', error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate required fields
      if (!formData.full_name || !formData.email || !formData.specialization || !formData.experience || !formData.bio) {
        setError('Please fill in all required fields');
        setLoading(false);
        return;
      }

      // Generate a temporary application ID for file uploads
      const tempApplicationId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      let resumeUrl = '';
      let portfolioFileUrls: string[] = [];
      let certificateUrls: string[] = [];

      // Upload files if any
      if (resumeFile || portfolioFiles.length > 0 || certificateFiles.length > 0) {
        setUploading(true);
        try {
          const uploadResults = await uploadFiles(tempApplicationId);

          // Use the actual R2 keys returned from upload
          resumeUrl = uploadResults.resumeKey;
          portfolioFileUrls = uploadResults.portfolioKeys;
          certificateUrls = uploadResults.certificateKeys;

          console.log('Files uploaded successfully:', {
            resumeKey: resumeUrl,
            portfolioKeys: portfolioFileUrls,
            certificateKeys: certificateUrls
          });
        } catch (uploadError) {
          setError('Failed to upload files. Please try again.');
          setLoading(false);
          setUploading(false);
          return;
        }
        setUploading(false);
      }

      // Submit application
      const applicationData = {
        ...formData,
        resume_url: resumeUrl,
        portfolio_files: portfolioFileUrls,
        certificates: certificateUrls
      };

      const response = await fetch('/api/designer-application', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(applicationData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit application');
      }

      const result = await response.json();

      setSuccess('Application submitted successfully! You will receive a confirmation email shortly. We will review your application and get back to you within 5-7 business days.');

      // Reset form
      setFormData({
        email: '',
        full_name: '',
        phone: '',
        location: '',
        specialization: '',
        experience: '',
        portfolio_url: '',
        bio: ''
      });
      setResumeFile(null);
      setPortfolioFiles([]);
      setCertificateFiles([]);

    } catch (error) {
      console.error('Error submitting application:', error);
      setError(error instanceof Error ? error.message : 'Failed to submit application. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative h-[40vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <div
            className="absolute inset-0 bg-black bg-opacity-50 z-10"
            aria-hidden="true"
          />
          <img
            src="https://images.unsplash.com/photo-1664575599736-c5197c684128?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Architectural team working"
            className="object-cover w-full h-full"
          />
        </div>
        <div className="container mx-auto px-4 z-20">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">Join Our Team</h1>
          <div className="bg-primary h-1 w-20" />
        </div>
      </section>

      {/* Introduction */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Design With Purpose</h2>
            <p className="text-lg text-gray-700 mb-8">
              Join our global network of talented architects and designers. Work on exciting projects with flexible, project-based collaboration while maintaining your independence.
            </p>
          </div>
        </div>
      </section>

      {/* How to Join Our Team - Collapsible */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <button
              onClick={() => setJoinStepsExpanded(!joinStepsExpanded)}
              className="w-full flex items-center justify-between p-6 bg-white shadow-md hover:shadow-lg transition-all duration-300 mb-4"
            >
              <h2 className="text-2xl md:text-3xl font-bold">How to Join Our Team</h2>
              {joinStepsExpanded ? (
                <ChevronUp className="h-6 w-6 text-primary" />
              ) : (
                <ChevronDown className="h-6 w-6 text-primary" />
              )}
            </button>

            {joinStepsExpanded && (
              <div className="bg-white p-6 shadow-md">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {joinSteps.map((step, index) => (
                    <div key={index} className="relative p-6 border border-gray-200 hover:border-primary transition-colors duration-300">
                      <div className="text-4xl font-bold text-primary/20 mb-3">
                        {step.number}
                      </div>
                      <h3 className="text-lg font-bold mb-3">{step.title}</h3>
                      <p className="text-gray-600 text-sm">{step.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* How It Works After You Join - Collapsible */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <button
              onClick={() => setHowItWorksExpanded(!howItWorksExpanded)}
              className="w-full flex items-center justify-between p-6 bg-white shadow-md hover:shadow-lg transition-all duration-300 mb-4"
            >
              <h2 className="text-2xl md:text-3xl font-bold">How It Works After You Join</h2>
              {howItWorksExpanded ? (
                <ChevronUp className="h-6 w-6 text-primary" />
              ) : (
                <ChevronDown className="h-6 w-6 text-primary" />
              )}
            </button>

            {howItWorksExpanded && (
              <div className="bg-white p-6 shadow-md space-y-4">
                {howItWorksSteps.map((step, index) => (
                  <div key={index} className="p-4 border border-gray-200 hover:border-primary transition-colors duration-300">
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                        {step.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-bold mb-2">{step.title}</h3>
                        <p className="text-gray-600 text-sm">{step.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary/10">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-6">Ready to Join Our Creative Team?</h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            Apply today and start collaborating with architects and designers from around the world.
          </p>
          <Link href="#apply">
            <Button variant="default" size="lg" className="group">
              Apply Now
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Application Form Section */}
      <section id="apply" className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center">Apply Now</h2>
            <p className="text-center text-gray-700 mb-12">
              Ready to join our global team of architects and designers? Fill out the comprehensive application form below.
            </p>

            {error && (
              <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
                <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
                <span>{error}</span>
              </div>
            )}

            {success && (
              <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
                <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
                <span>{success}</span>
              </div>
            )}

            <div className="bg-white p-8 shadow-xl">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Personal Information */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <Mail className="h-5 w-5 mr-2 text-primary" />
                    Personal Information
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="full_name"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleInputChange}
                        required
                        className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                      </label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="+****************"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                        Location
                      </label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="text"
                          id="location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="City, Country"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Professional Information */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <Briefcase className="h-5 w-5 mr-2 text-primary" />
                    Professional Information
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="specialization" className="block text-sm font-medium text-gray-700 mb-1">
                        Specialization <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="specialization"
                        name="specialization"
                        value={formData.specialization}
                        onChange={handleInputChange}
                        required
                        className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        <option value="">Select your specialization</option>
                        <option value="residential">Residential Design</option>
                        <option value="commercial">Commercial Architecture</option>
                        <option value="interior">Interior Design</option>
                        <option value="landscape">Landscape Architecture</option>
                        <option value="sustainable">Sustainable Design</option>
                        <option value="urban">Urban Planning</option>
                        <option value="industrial">Industrial Design</option>
                        <option value="renovation">Renovation & Restoration</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="experience" className="block text-sm font-medium text-gray-700 mb-1">
                        Years of Experience <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="experience"
                        name="experience"
                        value={formData.experience}
                        onChange={handleInputChange}
                        required
                        className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        <option value="">Select experience level</option>
                        <option value="0-2">0-2 years</option>
                        <option value="3-5">3-5 years</option>
                        <option value="6-10">6-10 years</option>
                        <option value="10+">10+ years</option>
                      </select>
                    </div>
                  </div>

                  <div className="mt-4">
                    <label htmlFor="portfolio_url" className="block text-sm font-medium text-gray-700 mb-1">
                      Portfolio URL
                    </label>
                    <input
                      type="url"
                      id="portfolio_url"
                      name="portfolio_url"
                      value={formData.portfolio_url}
                      onChange={handleInputChange}
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="https://your-portfolio-site.com"
                    />
                  </div>

                  <div className="mt-4">
                    <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                      Professional Bio <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="bio"
                      name="bio"
                      value={formData.bio}
                      onChange={handleInputChange}
                      rows={4}
                      required
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="Tell us about your background, experience, and what makes you a great fit for our team..."
                    />
                  </div>
                </div>

                {/* File Uploads */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                    <Upload className="h-5 w-5 mr-2 text-primary" />
                    Documents & Portfolio
                  </h3>

                  {/* Resume Upload */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Resume (PDF only, max 10MB)
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
                      <input
                        type="file"
                        accept=".pdf"
                        onChange={handleResumeUpload}
                        className="hidden"
                        id="resume-upload"
                      />
                      <label htmlFor="resume-upload" className="cursor-pointer">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-sm text-gray-600">
                          {resumeFile ? resumeFile.name : 'Click to upload your resume'}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">PDF format only</p>
                      </label>
                      {resumeFile && (
                        <button
                          type="button"
                          onClick={() => setResumeFile(null)}
                          className="mt-2 text-red-600 hover:text-red-700 text-sm"
                        >
                          Remove file
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Portfolio Files Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Portfolio Files (Images/PDFs, max 50MB each)
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
                      <input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png,.gif,.webp"
                        multiple
                        onChange={handlePortfolioUpload}
                        className="hidden"
                        id="portfolio-upload"
                      />
                      <label htmlFor="portfolio-upload" className="cursor-pointer">
                        <Image className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-sm text-gray-600">
                          Click to upload portfolio files
                        </p>
                        <p className="text-xs text-gray-500 mt-1">Images and PDFs accepted</p>
                      </label>
                    </div>

                    {portfolioFiles.length > 0 && (
                      <div className="mt-4 space-y-2">
                        {portfolioFiles.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm text-gray-700">{file.name}</span>
                            <button
                              type="button"
                              onClick={() => removePortfolioFile(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Certificates Upload */}
                  <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Certificates & Certifications (PDFs/Images, max 10MB each)
                    </label>
                    <p className="text-xs text-gray-500 mb-3">
                      Upload your professional certificates, licenses, or certifications (optional)
                    </p>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
                      <input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        multiple
                        onChange={handleCertificateUpload}
                        className="hidden"
                        id="certificate-upload"
                      />
                      <label htmlFor="certificate-upload" className="cursor-pointer">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-sm text-gray-600">
                          Click to upload certificates
                        </p>
                        <p className="text-xs text-gray-500 mt-1">PDFs and images accepted</p>
                      </label>
                    </div>

                    {certificateFiles.length > 0 && (
                      <div className="mt-4 space-y-2">
                        {certificateFiles.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm text-gray-700">{file.name}</span>
                            <button
                              type="button"
                              onClick={() => removeCertificateFile(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-center">
                  <Button
                    type="submit"
                    variant="default"
                    size="lg"
                    disabled={loading || uploading}
                    className="w-full md:w-auto"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Submitting Application...
                      </>
                    ) : uploading ? (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Uploading Files...
                      </>
                    ) : (
                      'Submit Application'
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}