"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";
import {
  MessageSquare,
  Send,
  Loader2,
  AlertCircle,
  User,
  Clock,
  Reply,
  Lock,
} from "lucide-react";
import { motion } from "framer-motion";
import { formatDistanceToNow } from "date-fns";

type Comment = {
  id: string;
  content: string;
  created_at: string;
  user_id: string;
  user_name: string;
  user_avatar: string | null;
  user_role: string;
  is_internal: boolean;
  parent_id: string | null;
  replies?: Comment[];
};

type ProposalCommentsProps = {
  proposalId: string;
  userRole: "client" | "designer" | "admin";
};

export default function ProposalComments({
  proposalId,
  userRole,
}: ProposalCommentsProps) {
  const { user } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [isInternal, setIsInternal] = useState(false);
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user && proposalId) {
      fetchComments();
    }
  }, [user, proposalId]);

  const fetchComments = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("proposal_comments")
        .select(`
          id,
          content,
          created_at,
          is_internal,
          parent_id,
          user_id,
          profiles:user_id (
            full_name,
            avatar_url,
            role
          )
        `)
        .eq("proposal_id", proposalId)
        .order("created_at", { ascending: true });

      if (error) throw error;

      // Transform the data to include user information
      const transformedComments = data.map((comment) => ({
        id: comment.id,
        content: comment.content,
        created_at: comment.created_at,
        user_id: comment.user_id,
        user_name: comment.profiles?.full_name || "Unknown User",
        user_avatar: comment.profiles?.avatar_url,
        user_role: comment.profiles?.role || "unknown",
        is_internal: comment.is_internal,
        parent_id: comment.parent_id,
      }));

      // Organize comments into threads
      const threadedComments = transformedComments.filter(
        (comment) => !comment.parent_id
      );
      
      // Add replies to parent comments
      threadedComments.forEach((parent) => {
        parent.replies = transformedComments.filter(
          (comment) => comment.parent_id === parent.id
        );
      });

      setComments(threadedComments);
    } catch (error) {
      console.error("Error fetching comments:", error);
      setError("Failed to load comments");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !newComment.trim()) return;

    setSubmitting(true);
    setError(null);

    try {
      const { error } = await supabase.from("proposal_comments").insert({
        proposal_id: proposalId,
        user_id: user.id,
        content: newComment.trim(),
        is_internal: isInternal,
        parent_id: replyTo,
      });

      if (error) throw error;

      // Clear form and refresh comments
      setNewComment("");
      setIsInternal(false);
      setReplyTo(null);
      await fetchComments();
    } catch (error) {
      console.error("Error submitting comment:", error);
      setError("Failed to submit comment. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return "Unknown date";
    }
  };

  const canSeeInternalComments = userRole === "designer" || userRole === "admin";

  return (
    <div className="bg-white border border-gray-200 rounded-lg">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-lg font-semibold flex items-center">
          <MessageSquare className="h-5 w-5 mr-2" />
          Discussion
        </h2>
      </div>

      <div className="p-4">
        {error && (
          <div className="bg-red-50 border border-red-200 p-3 rounded-md mb-4 flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p>No comments yet. Start the discussion!</p>
          </div>
        ) : (
          <div className="space-y-4 mb-6">
            {comments.map((comment) => (
              <div key={comment.id} className="space-y-3">
                <div
                  className={`p-3 rounded-lg ${
                    comment.is_internal
                      ? "bg-yellow-50 border border-yellow-200"
                      : "bg-gray-50 border border-gray-200"
                  }`}
                >
                  {comment.is_internal && canSeeInternalComments && (
                    <div className="flex items-center text-yellow-600 text-xs mb-2">
                      <Lock className="h-3 w-3 mr-1" />
                      <span>Internal note (only visible to designers and admins)</span>
                    </div>
                  )}
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3">
                      {comment.user_avatar ? (
                        <img
                          src={comment.user_avatar}
                          alt={comment.user_name}
                          className="h-8 w-8 rounded-full"
                        />
                      ) : (
                        <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-500" />
                        </div>
                      )}
                    </div>
                    <div className="flex-grow">
                      <div className="flex items-center">
                        <span className="font-medium text-sm">
                          {comment.user_name}
                        </span>
                        <span className="text-xs text-gray-500 ml-2">
                          {comment.user_role === "client"
                            ? "Client"
                            : comment.user_role === "designer"
                            ? "Designer"
                            : "Admin"}
                        </span>
                        <span className="text-xs text-gray-400 ml-2 flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(comment.created_at)}
                        </span>
                      </div>
                      <div className="mt-1 text-sm whitespace-pre-line">
                        {comment.content}
                      </div>
                      <div className="mt-2">
                        <button
                          onClick={() => setReplyTo(comment.id)}
                          className="text-xs text-gray-500 hover:text-gray-700 flex items-center"
                        >
                          <Reply className="h-3 w-3 mr-1" />
                          Reply
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Replies */}
                {comment.replies && comment.replies.length > 0 && (
                  <div className="pl-8 space-y-3">
                    {comment.replies.map((reply) => (
                      <div
                        key={reply.id}
                        className={`p-3 rounded-lg ${
                          reply.is_internal
                            ? "bg-yellow-50 border border-yellow-200"
                            : "bg-gray-50 border border-gray-200"
                        }`}
                      >
                        {reply.is_internal && canSeeInternalComments && (
                          <div className="flex items-center text-yellow-600 text-xs mb-2">
                            <Lock className="h-3 w-3 mr-1" />
                            <span>Internal note</span>
                          </div>
                        )}
                        
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mr-3">
                            {reply.user_avatar ? (
                              <img
                                src={reply.user_avatar}
                                alt={reply.user_name}
                                className="h-6 w-6 rounded-full"
                              />
                            ) : (
                              <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center">
                                <User className="h-3 w-3 text-gray-500" />
                              </div>
                            )}
                          </div>
                          <div className="flex-grow">
                            <div className="flex items-center">
                              <span className="font-medium text-xs">
                                {reply.user_name}
                              </span>
                              <span className="text-xs text-gray-400 ml-2 flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {formatDate(reply.created_at)}
                              </span>
                            </div>
                            <div className="mt-1 text-xs whitespace-pre-line">
                              {reply.content}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Reply form */}
                {replyTo === comment.id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    transition={{ duration: 0.2 }}
                    className="pl-8"
                  >
                    <form
                      onSubmit={handleSubmitComment}
                      className="flex flex-col space-y-2"
                    >
                      <textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Write a reply..."
                        className="w-full p-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                        rows={2}
                      />
                      <div className="flex justify-between items-center">
                        {(userRole === "designer" || userRole === "admin") && (
                          <label className="flex items-center text-xs">
                            <input
                              type="checkbox"
                              checked={isInternal}
                              onChange={(e) => setIsInternal(e.target.checked)}
                              className="mr-1 h-3 w-3"
                            />
                            Internal note
                          </label>
                        )}
                        <div className="flex space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setReplyTo(null)}
                            className="text-xs py-1 px-2 h-auto"
                          >
                            Cancel
                          </Button>
                          <Button
                            type="submit"
                            size="sm"
                            disabled={!newComment.trim() || submitting}
                            className="bg-brown-600 hover:bg-brown-700 text-white text-xs py-1 px-2 h-auto"
                          >
                            {submitting ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              "Reply"
                            )}
                          </Button>
                        </div>
                      </div>
                    </form>
                  </motion.div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* New comment form */}
        {!replyTo && (
          <form onSubmit={handleSubmitComment} className="mt-4">
            <div className="border border-gray-300 rounded-md overflow-hidden focus-within:ring-2 focus-within:ring-brown-500 focus-within:border-transparent">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="w-full p-3 border-none focus:outline-none resize-none"
                rows={3}
              />
              <div className="bg-gray-50 p-2 flex items-center justify-between">
                {(userRole === "designer" || userRole === "admin") && (
                  <label className="flex items-center text-sm text-gray-600">
                    <input
                      type="checkbox"
                      checked={isInternal}
                      onChange={(e) => setIsInternal(e.target.checked)}
                      className="mr-2 h-4 w-4"
                    />
                    Internal note (only visible to designers and admins)
                  </label>
                )}
                <Button
                  type="submit"
                  disabled={!newComment.trim() || submitting}
                  className="bg-brown-600 hover:bg-brown-700 text-white flex items-center"
                >
                  {submitting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4 mr-2" />
                  )}
                  Send
                </Button>
              </div>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
