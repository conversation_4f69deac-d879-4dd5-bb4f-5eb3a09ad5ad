"use client";

import React, { useState, useEffect, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Save,
  Share2,
  Download,
  Loader2,
  CheckCircle,
  RefreshCw,
  HelpCircle,
  ListFilter,
  MessageSquare,
  ChevronUp,
  ChevronDown,
  ChevronRight,
  Upload,
  Send,
  Mail,
  FileDown,
  Home,
  Building2,
  Factory,
  TreePine,
  Waves,
  Mountain,
  Heart,
  Star,
  Badge,
  Eye,
  Copy,
  ExternalLink,
  Settings,
  Info,
  Clock,
  Zap,
  Users,
  TrendingUp
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { fetchImagesForStyle, getRandomImageForStyle, getNextImageInSequence, fallbackImages } from "@/lib/r2-images";
import { getAiGeneratedImage, regenerateAiImage } from "@/lib/ai-images";
import { fromUrlSafeServiceId, mapServiceIdToKey } from "@/lib/service-utils";
import { Toaster, toast } from "react-hot-toast";
import { createVisionBuilderTracking } from "@/lib/tracking";
import SuccessScreen from "@/components/tracking/SuccessScreen";

// Design styles organized by service categories
const designStylesByService = {
  "creative-design-&-branding": [
    { id: "minimalist", name: "Minimalist", description: "Clean, simple, spacious. Focus on typography, negative space, and a limited color palette." },
    { id: "modern", name: "Modern", description: "Bold shapes, sans-serif fonts, innovative layouts. Often tech-driven and sleek." },
    { id: "classic-timeless", name: "Classic / Timeless", description: "Elegant typography, symmetry, and structured layouts. Black & white or deep, luxurious tones." },
    { id: "artistic-handcrafted", name: "Artistic / Handcrafted", description: "Hand-drawn elements, brush strokes, or imperfect lines. Personal, expressive, and unique." },
    { id: "geometric", name: "Geometric", description: "Strong lines, shapes, grids, and symmetry. Clear structure, precision, and mathematical beauty." },
    { id: "organic-nature-inspired", name: "Organic / Nature-Inspired", description: "Earth tones, natural textures, flowing shapes. Reflects sustainability and eco-consciousness." },
    { id: "vintage-retro", name: "Vintage / Retro", description: "Inspired by past decades (70s, 80s, etc.). Uses nostalgic fonts, faded colors, and old-school layouts." },
    { id: "industrial-urban", name: "Industrial / Urban", description: "Exposed textures, metallic tones, mechanical or architectural elements. Communicates strength and utility." },
    { id: "luxurious-premium", name: "Luxurious / Premium", description: "Metallic gold, silver, rich contrast, and polished typography. Aimed at high-end clients." },
    { id: "futuristic-digital", name: "Futuristic / Digital", description: "Neon colors, tech-inspired shapes, tech-inspired elements. Used for AI, innovation, or digital platforms." },
    { id: "playful-youthful", name: "Playful / Youthful", description: "Bold colors, funky fonts, illustrations. Energetic, vibrant, and full of movement." },
    { id: "monogram-symbolic", name: "Monogram / Symbolic", description: "Custom symbols or initials as the main brand identity. Clean, professional, and highly versatile." }
  ],
  "innovative-architectural-design": [
    { id: "parametric-design", name: "Parametric Design", description: "Complex, algorithm-driven forms with fluid geometries and intricate patterns." },
    { id: "sustainable-green-architecture", name: "Sustainable / Green Architecture", description: "Eco-friendly designs that minimize environmental impact and maximize energy efficiency." },
    { id: "high-tech-smart-architecture", name: "High-Tech / Smart Architecture", description: "Technology-integrated buildings with automated systems and cutting-edge materials." },
    { id: "minimalist-modernism", name: "Minimalist Modernism", description: "Clean lines, open spaces, and reduced ornamentation focusing on essential elements." },
    { id: "biophilic-design", name: "Biophilic Design", description: "Architecture that connects occupants with nature through natural light, vegetation, and organic forms." },
    { id: "brutalist-revival", name: "Brutalist Revival", description: "Bold, monolithic structures with exposed concrete and geometric forms." },
    { id: "modular-prefabricated-design", name: "Modular / Prefabricated Design", description: "Standardized, factory-built components assembled on-site for efficiency." },
    { id: "deconstructivism", name: "Deconstructivism", description: "Fragmented, non-linear designs that challenge conventional architectural forms." },
    { id: "adaptive-reuse-urban-renewal", name: "Adaptive Reuse / Urban Renewal", description: "Transforming existing structures for new purposes while preserving historical elements." },
    { id: "floating-mobile-architecture", name: "Floating / Mobile Architecture", description: "Structures designed to be movable or situated on water, adapting to changing environments." }
  ],
  "interior-design": [
    { id: "modern", name: "Modern", description: "Clean lines, simple color palettes, and functional furniture with an emphasis on space and light." },
    { id: "contemporary", name: "Contemporary", description: "Current design trends featuring curved lines, neutral colors, and statement pieces." },
    { id: "minimalist", name: "Minimalist", description: "Simplicity in form and function with limited color palettes and clutter-free spaces." },
    { id: "scandinavian", name: "Scandinavian", description: "Light, airy spaces with functional furniture, natural materials, and minimal decorations." },
    { id: "industrial", name: "Industrial", description: "Raw, unfinished elements like exposed brick, concrete, and metal with vintage accessories." },
    { id: "traditional", name: "Traditional", description: "Classic designs with rich color palettes, ornate details, and elegant furnishings." },
    { id: "transitional", name: "Transitional", description: "Blend of traditional and contemporary elements creating a balanced, timeless look." },
    { id: "bohemian", name: "Bohemian (Boho)", description: "Eclectic mix of patterns, textures, and colors with vintage and handcrafted items." },
    { id: "rustic", name: "Rustic", description: "Natural, weathered materials like wood and stone with comfortable, casual furnishings." },
    { id: "mid-century-modern", name: "Mid-Century Modern", description: "1950s-inspired design with clean lines, organic forms, and functional aesthetics." },
    { id: "coastal-mediterranean", name: "Coastal / Mediterranean", description: "Light, airy spaces with blue and white color schemes inspired by seaside living." },
    { id: "art-deco", name: "Art Deco", description: "Bold geometric patterns, rich colors, and luxurious materials with symmetrical designs." }
  ],
  "urban-&-architectural-planning": [
    { id: "modernist-urban-planning", name: "Modernist Urban Planning", description: "Functional zoning, efficient transportation networks, and rational organization of space." },
    { id: "new-urbanism", name: "New Urbanism", description: "Walkable neighborhoods, mixed-use development, and traditional community planning principles." },
    { id: "smart-cities", name: "Smart Cities", description: "Technology-integrated urban environments with data-driven infrastructure and services." },
    { id: "garden-cities", name: "Garden Cities", description: "Self-contained communities surrounded by greenbelts with balanced residential, industrial, and agricultural areas." },
    { id: "transit-oriented-development", name: "Transit-Oriented Development (TOD)", description: "Compact, walkable, mixed-use communities centered around high-quality transit systems." },
    { id: "sustainable-urbanism", name: "Sustainable Urbanism", description: "Eco-friendly urban planning focusing on environmental impact, resource efficiency, and quality of life." },
    { id: "mixed-use-master-planning", name: "Mixed-Use Master Planning", description: "Comprehensive planning for diverse functions (residential, commercial, recreational) in integrated developments." },
    { id: "historic-preservation", name: "Historic Preservation / Adaptive Reuse", description: "Conserving cultural heritage while adapting historic urban fabric for contemporary needs." },
    { id: "landscape-urbanism", name: "Landscape Urbanism", description: "Landscape-driven approach to urban design, integrating natural systems with built environments." },
    { id: "postmodern-urbanism", name: "Postmodern Urbanism", description: "Diverse, contextual urban design embracing historical references, local identity, and human scale." }
  ],
  "residential-&-commercial-projects": [
    { id: "modern", name: "Modern", description: "Clean lines, open floor plans, and minimal ornamentation with an emphasis on function." },
    { id: "contemporary", name: "Contemporary", description: "Current design trends featuring innovative materials, sustainable practices, and flexible spaces." },
    { id: "classic-neoclassical", name: "Classic / Neoclassical", description: "Timeless designs inspired by Greek and Roman architecture with symmetrical facades and columns." },
    { id: "colonial-traditional", name: "Colonial / Traditional", description: "Historical American styles with formal layouts, symmetrical facades, and classic detailing." },
    { id: "mediterranean", name: "Mediterranean", description: "Inspired by coastal European architecture with stucco walls, terracotta roofs, and arched elements." },
    { id: "minimalist", name: "Minimalist", description: "Simplified forms, limited material palette, and focus on space and light." },
    { id: "industrial", name: "Industrial", description: "Raw, utilitarian aesthetic with exposed structural elements, metals, and repurposed materials." },
    { id: "high-tech", name: "High-Tech", description: "Technology-forward designs with smart systems, innovative materials, and energy efficiency." },
    { id: "sustainable-eco-friendly", name: "Sustainable / Eco-Friendly", description: "Environmentally conscious design with renewable materials, energy efficiency, and minimal impact." },
    { id: "mixed-use-urban-design", name: "Mixed-Use Urban Design", description: "Integrated residential and commercial spaces promoting walkability and community interaction." },
    { id: "luxury-premium", name: "Luxury / Premium", description: "High-end finishes, custom details, and exclusive amenities for discerning clients." },
    { id: "cultural-vernacular", name: "Cultural / Vernacular", description: "Designs reflecting local traditions, materials, and building techniques adapted for modern use." }
  ],
  "landscape-and-architecture-integration": [
    { id: "biophilic-design", name: "Biophilic Design", description: "Integrating nature into architecture to enhance human wellbeing and environmental connection." },
    { id: "organic-architecture", name: "Organic Architecture", description: "Harmonizing human habitation with the natural environment through integrated, flowing designs." },
    { id: "eco-integrated-architecture", name: "Eco-Integrated Architecture", description: "Sustainable designs that minimize environmental impact while maximizing natural resource use." },
    { id: "topographic-architecture", name: "Topographic Architecture", description: "Buildings that follow and enhance the natural contours and features of the landscape." },
    { id: "vernacular-landscape-integration", name: "Vernacular Landscape Integration", description: "Designs reflecting local cultural traditions and environmental adaptations." },
    { id: "minimalist-landscape-architecture", name: "Minimalist Landscape Architecture", description: "Clean, simple outdoor spaces with carefully selected elements and materials." },
    { id: "desert-architecture-integration", name: "Desert Architecture Integration", description: "Designs adapted to arid environments, utilizing shade, thermal mass, and water conservation." },
    { id: "forest-or-jungle-embedded-design", name: "Forest or Jungle-Embedded Design", description: "Architecture that preserves and incorporates existing forest ecosystems." },
    { id: "coastal-marine-integration", name: "Coastal / Marine Integration", description: "Designs responding to waterfront conditions, views, and marine environments." },
    { id: "urban-landscape-fusion", name: "Urban Landscape Fusion", description: "Blending built environments with green spaces in dense urban contexts." }
  ],
  "educational-&-community-oriented-spaces": [
    { id: "open-flexible-learning-environments", name: "Open & Flexible Learning Environments", description: "Adaptable spaces that support diverse teaching methods and learning styles." },
    { id: "courtyard-centered-design", name: "Courtyard-Centered Design", description: "Buildings organized around central outdoor spaces promoting community interaction." },
    { id: "cultural-contextual-design", name: "Cultural & Contextual Design", description: "Spaces reflecting local cultural values, traditions, and environmental conditions." },
    { id: "sustainable-green-campuses", name: "Sustainable / Green Campuses", description: "Eco-friendly educational facilities with minimal environmental impact and educational value." },
    { id: "civic-inspired-monumental-architecture", name: "Civic-Inspired / Monumental Architecture", description: "Designs expressing community pride and civic importance through scale and presence." },
    { id: "playful-child-centric-design", name: "Playful / Child-Centric Design", description: "Engaging, colorful spaces designed specifically for children's needs and development." },
    { id: "tech-integrated-spaces", name: "Tech-Integrated Spaces", description: "Learning environments with embedded technology supporting digital education and collaboration." },
    { id: "community-hub-design", name: "Community Hub Design", description: "Multi-functional spaces serving diverse community needs and fostering social connection." },
    { id: "inclusive-universal-design", name: "Inclusive / Universal Design", description: "Spaces accessible to all users regardless of age, ability, or background." },
    { id: "modular-scalable-design", name: "Modular / Scalable Design", description: "Flexible systems allowing for growth, reconfiguration, and adaptation over time." }
  ]
};

// Example prompts to help users get started
const examplePrompts = {
  "creative-design-&-branding": [
    "A logo for a sustainable fashion brand with earthy tones and organic shapes",
    "A minimalist brand identity for a tech startup focused on AI innovation",
    "A vintage-inspired logo for a craft coffee roastery with handcrafted elements"
  ],
  "innovative-architectural-design": [
    "A sustainable office building with green terraces and natural ventilation",
    "A parametric facade design for a modern art museum with dynamic lighting",
    "A modular housing complex that adapts to different family sizes and needs"
  ],
  "interior-design": [
    "A Scandinavian-inspired living room with natural light and minimal furniture",
    "An industrial loft conversion with exposed brick and steel elements",
    "A biophilic office space with living walls and natural materials"
  ],
  "urban-&-architectural-planning": [
    "A walkable mixed-use neighborhood with integrated green spaces",
    "A transit-oriented development around a new light rail station",
    "A sustainable urban masterplan with water management systems"
  ],
  "residential-&-commercial-projects": [
    "A modern family home with indoor-outdoor living spaces and natural light",
    "A sustainable retail complex with green roofs and solar panels",
    "A luxury apartment building with community amenities and smart home features"
  ],
  "landscape-and-architecture-integration": [
    "A hillside home that follows the natural topography with minimal excavation",
    "A coastal residence that responds to ocean views and weather conditions",
    "An urban park that integrates with surrounding buildings and infrastructure"
  ],
  "educational-&-community-oriented-spaces": [
    "A flexible learning environment with modular furniture and collaborative spaces",
    "A community center with indoor and outdoor gathering areas for all ages",
    "A sustainable school campus with outdoor classrooms and learning gardens"
  ]
};
// Progress Steps component with improved mobile responsiveness
const ProgressSteps = ({ currentStep }: { currentStep: number }) => {
  // Don't show progress steps on landing screen (step 0) or on mobile
  if (currentStep === 0) return null;

  const steps = [
    { number: 1, label: "Start Your Vision", description: "Select style & describe" },
    { number: 2, label: "Generating", description: "AI creating your vision" },
    { number: 3, label: "View Your Vision", description: "Review & refine results" },
    { number: 4, label: "Submit Your Vision", description: "Complete your request" }
  ];

  return (
    <div className="mb-16 px-4 sm:px-0">
      {/* Desktop view: enhanced horizontal steps */}
      <div className="hidden sm:flex items-center justify-between w-full max-w-5xl mx-auto">
        {steps.map((step, index) => (
          <React.Fragment key={step.number}>
            <motion.div
              className={`flex flex-col items-center transition-all duration-300 ${
                currentStep >= step.number ? 'text-primary' : 'text-gray-400'
              }`}
              whileHover={{ scale: 1.05 }}
            >
              <motion.div
                className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 transition-all duration-300
                  ${currentStep >= step.number
                    ? 'bg-primary text-white shadow-lg'
                    : 'bg-gray-200 text-gray-500'
                  }`}
                whileHover={{ scale: 1.1 }}
              >
                {currentStep > step.number ? (
                  <CheckCircle className="h-6 w-6" />
                ) : (
                  <span className="text-lg font-bold">{step.number}</span>
                )}
              </motion.div>
              <div className="text-center">
                <div className="text-sm font-bold mb-1">{step.label}</div>
                <div className="text-xs text-gray-500 max-w-24">{step.description}</div>
              </div>
            </motion.div>

            {index < steps.length - 1 && (
              <motion.div
                className={`h-1 flex-grow mx-4 transition-all duration-500 ${
                  currentStep > step.number ? 'bg-primary' : 'bg-gray-200'
                }`}
                initial={{ scaleX: 0 }}
                animate={{ scaleX: currentStep > step.number ? 1 : 0 }}
                style={{ transformOrigin: 'left' }}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

// Responsive image card component with enhanced features
const ImageCard = ({
  image,
  isSelected,
  label,
  onClick,
  aspectRatio = "aspect-[4/3]",
  imageType = "style_example", // "ai_generated" or "style_example"
  isGenerating = false
}: {
  image: string;
  isSelected: boolean;
  label: string;
  onClick: () => void;
  aspectRatio?: string;
  imageType?: "ai_generated" | "style_example";
  isGenerating?: boolean;
}) => {
  const isAiGenerated = imageType === "ai_generated";

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`bg-white border rounded-lg overflow-hidden cursor-pointer transition-all duration-300
        ${isSelected
          ? 'ring-4 ring-primary border-primary shadow-xl transform scale-105'
          : 'hover:shadow-lg border-gray-200 hover:border-gray-300'
        }`}
      onClick={onClick}
      whileHover={{ y: -2 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className={`${aspectRatio} relative overflow-hidden`}>
        <Image
          src={image}
          alt={label}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          className={`object-cover transition-transform duration-300 ${
            isSelected ? 'scale-105' : 'hover:scale-102'
          }`}
          priority={isSelected}
          loading={isSelected ? "eager" : "lazy"}
        />

        {/* Image type badge */}
        <div className="absolute top-2 left-2">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className={`px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm
              ${isAiGenerated
                ? 'bg-green-500/90 text-white'
                : 'bg-blue-500/90 text-white'
              }`}
          >
            {isAiGenerated ? (
              <div className="flex items-center space-x-1">
                <Sparkles className="h-3 w-3" />
                <span>AI Generated</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1">
                <span>Style Example</span>
              </div>
            )}
          </motion.div>
        </div>

        {/* Selection indicator */}
        {isSelected && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute top-2 right-2 bg-primary text-white rounded-full p-1"
          >
            <CheckCircle className="h-4 w-4" />
          </motion.div>
        )}

        {/* Loading overlay for generating images */}
        {isGenerating && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-black/50 flex items-center justify-center"
          >
            <div className="text-white text-center">
              <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
              <div className="text-xs">Generating...</div>
            </div>
          </motion.div>
        )}
      </div>

      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <p className="text-sm text-gray-700 font-medium flex-1">{label}</p>
          {isAiGenerated && (
            <div className="ml-2 text-xs text-green-600 font-medium">
              Customizable
            </div>
          )}
        </div>

        <div className="flex justify-between items-center">
          <div className="text-xs text-gray-500">
            {isAiGenerated ? "AI-powered design" : "Pre-made example"}
          </div>
          <motion.button
            className={`p-1 rounded-full transition-colors ${
              isSelected
                ? 'text-primary bg-primary/10'
                : 'text-gray-400 hover:text-primary hover:bg-gray-100'
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            aria-label={isSelected ? "Selected" : "Select this design"}
          >
            <CheckCircle className="h-4 w-4" />
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

// Enhanced visual style selection component with icons (no images)
const StyleSelect = ({
  styles,
  selectedStyle,
  onSelect
}: {
  styles: { id: string; name: string; description: string }[];
  selectedStyle: string;
  onSelect: (styleId: string) => void;
}) => {
  // Icon mapping for different styles
  const getStyleIcon = (styleId: string) => {
    // Map common style keywords to icons
    if (styleId.includes('modern') || styleId.includes('minimalist') || styleId.includes('contemporary')) {
      return <Home className="h-6 w-6" />;
    } else if (styleId.includes('traditional') || styleId.includes('classic') || styleId.includes('colonial')) {
      return <Building2 className="h-6 w-6" />;
    } else if (styleId.includes('industrial') || styleId.includes('urban') || styleId.includes('tech')) {
      return <Factory className="h-6 w-6" />;
    } else if (styleId.includes('scandinavian') || styleId.includes('nature') || styleId.includes('organic') || styleId.includes('sustainable')) {
      return <TreePine className="h-6 w-6" />;
    } else if (styleId.includes('mediterranean') || styleId.includes('coastal') || styleId.includes('marine')) {
      return <Waves className="h-6 w-6" />;
    } else if (styleId.includes('japanese') || styleId.includes('zen') || styleId.includes('mountain') || styleId.includes('topographic')) {
      return <Mountain className="h-6 w-6" />;
    } else {
      return <Sparkles className="h-6 w-6" />;
    }
  };

  return (
    <div className="max-h-[60vh] overflow-y-auto">
      {/* Desktop: Grid Layout */}
      <div className="hidden md:grid md:grid-cols-3 lg:grid-cols-4 gap-4">
        {styles.map((style, index) => (
          <motion.div
            key={style.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.05 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`cursor-pointer p-4 rounded-lg border-2 transition-all duration-300 ${
              selectedStyle === style.id
                ? 'border-primary bg-primary/5 shadow-lg'
                : 'border-gray-200 hover:border-primary/50 hover:shadow-md'
            }`}
            onClick={() => onSelect(style.id)}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className={`p-3 rounded-full transition-colors ${
                selectedStyle === style.id ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
              }`}>
                {getStyleIcon(style.id)}
              </div>
              <div>
                <h3 className={`font-medium text-sm transition-colors ${
                  selectedStyle === style.id ? 'text-primary' : 'text-gray-700'
                }`}>
                  {style.name}
                </h3>
                <p className="text-xs text-gray-500 mt-1 leading-tight">
                  {style.description.split('.')[0]}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Mobile: Horizontal Scroll */}
      <div className="md:hidden">
        <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 px-4 -mx-4">
          {styles.map((style, index) => (
            <div key={style.id} className="flex-shrink-0 w-40 snap-center mr-4 last:mr-0 first:ml-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                whileTap={{ scale: 0.95 }}
                className={`cursor-pointer p-3 rounded-lg border-2 transition-all duration-300 h-full ${
                  selectedStyle === style.id
                    ? 'border-primary bg-primary/5'
                    : 'border-gray-200'
                }`}
                onClick={() => onSelect(style.id)}
              >
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className={`p-2 rounded-full transition-colors ${
                    selectedStyle === style.id ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {getStyleIcon(style.id)}
                  </div>
                  <div>
                    <h3 className={`font-medium text-xs transition-colors leading-tight ${
                      selectedStyle === style.id ? 'text-primary' : 'text-gray-700'
                    }`}>
                      {style.name}
                    </h3>
                  </div>
                </div>
              </motion.div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Style description component
const StyleDescription = ({
  style
}: {
  style: { id: string; name: string; description: string } | undefined;
}) => {
  if (!style) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="mt-4 p-4 border border-gray-200 bg-gray-50"
    >
      <h3 className="font-bold text-lg mb-2">{style.name}</h3>
      <p className="text-sm text-gray-600">{style.description}</p>
    </motion.div>
  );
};

// Individual FAQ Item Component
const FAQItem = ({ question, answer }: { question: string; answer: string }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full p-4 text-left bg-white hover:bg-gray-50 transition-colors flex items-center justify-between"
        whileHover={{ backgroundColor: "rgb(249, 250, 251)" }}
      >
        <span className="font-medium text-gray-900 pr-4">{question}</span>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
          className="flex-shrink-0"
        >
          <ChevronDown className="h-5 w-5 text-gray-500" />
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0 bg-gray-50 border-t border-gray-200">
              <p className="text-gray-600 leading-relaxed">{answer}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Define the form data type
interface RequestFormData {
  name: string;
  email: string;
  projectType: string;
  vision: string;
  image: string | null;
  uploadedImage?: File;
}

// Vision Submission Form component
const SampleRequestForm = ({
  serviceName,
  prompt,
  selectedImage,
  onGoBack,
  onSubmit
}: {
  serviceName: string;
  prompt: string;
  selectedImage: string | null;
  onGoBack: () => void;
  onSubmit: (formData: RequestFormData) => void;
}) => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [projectType, setProjectType] = useState(serviceName);
  const [vision, setVision] = useState(prompt);
  const [uploadedImage, setUploadedImage] = useState<string | null>(selectedImage);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // File input reference
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    // Store the file for later upload
    const reader = new FileReader();
    reader.onload = (event) => {
      setUploadedImage(event.target?.result as string);
      // Store the file in a ref for later upload
      fileRef.current = file;
      setIsUploading(false);
    };
    reader.readAsDataURL(file);
  };

  // Ref to store the uploaded file
  const fileRef = useRef<File | null>(null);

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) newErrors.name = "Name is required";
    if (!email.trim()) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = "Email is invalid";
    if (!projectType.trim()) newErrors.projectType = "Project type is required";
    if (!vision.trim()) newErrors.vision = "Vision description is required";
    if (!uploadedImage) newErrors.image = "An image is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);

      // Prepare form data
      const formData = {
        name,
        email,
        projectType,
        vision,
        image: uploadedImage,
        uploadedImage: fileRef.current || undefined
      };

      // Submit form data
      onSubmit(formData);

      // Reset submission state after a delay (simulating API call)
      setTimeout(() => {
        setIsSubmitting(false);
      }, 1500);
    }
  };

  return (
    <div className="bg-white shadow-xl rounded-lg p-4 md:p-8 mb-12">
      <div className="flex items-center mb-8">
        <button
          onClick={onGoBack}
          className="text-gray-600 hover:text-primary transition-colors flex items-center mr-4"
          aria-label="Go back to results"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          <span className="text-sm">Back to Results</span>
        </button>
        <h2 className="text-xl md:text-2xl font-bold">Submit Your Vision</h2>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Two-Column Layout for Better Balance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

          {/* Left Column: Form Fields */}
          <div className="space-y-6">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Your Information</h3>

              {/* Name and Email in a row on larger screens */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className={`w-full p-3 border rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
                      ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="Your full name"
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className={`w-full p-3 border rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
                      ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
                </div>
              </div>

              {/* Project Type */}
              <div>
                <label htmlFor="projectType" className="block text-sm font-medium text-gray-700 mb-2">
                  Project Type <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <select
                    id="projectType"
                    value={projectType}
                    onChange={(e) => setProjectType(e.target.value)}
                    className={`w-full p-3 border rounded-lg bg-white appearance-none transition-all focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
                      ${errors.projectType ? 'border-red-500' : 'border-gray-300'}`}
                  >
                    <option value={serviceName}>{serviceName}</option>
                    <option value="Residential">Residential</option>
                    <option value="Commercial">Commercial</option>
                    <option value="Mixed-Use">Mixed-Use</option>
                    <option value="Other">Other</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  </div>
                </div>
                {errors.projectType && <p className="mt-1 text-sm text-red-500">{errors.projectType}</p>}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Project Details</h3>

              {/* Vision Description */}
              <div>
                <label htmlFor="vision" className="block text-sm font-medium text-gray-700 mb-2">
                  Describe Your Vision <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="vision"
                  rows={6}
                  value={vision}
                  onChange={(e) => setVision(e.target.value)}
                  className={`w-full p-3 border rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none
                    ${errors.vision ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder="Provide additional details about your project..."
                ></textarea>
                {errors.vision && <p className="mt-1 text-sm text-red-500">{errors.vision}</p>}
              </div>
            </div>
          </div>

          {/* Right Column: Image Section */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Project Visualization</h3>

              {/* Image Preview */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4">
                {uploadedImage ? (
                  <div className="relative aspect-[4/3] rounded-lg overflow-hidden">
                    <Image
                      src={uploadedImage}
                      alt="Project visualization"
                      fill
                      sizes="(max-width: 768px) 100vw, 400px"
                      className="object-cover"
                    />
                    <div className="absolute top-2 right-2">
                      <div className="bg-green-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Ready
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="aspect-[4/3] bg-gray-50 rounded-lg flex flex-col items-center justify-center text-gray-500">
                    <Upload className="h-12 w-12 mb-2 text-gray-400" />
                    <p className="text-sm font-medium">No image selected</p>
                    <p className="text-xs">Upload an image or use your generated vision</p>
                  </div>
                )}
              </div>

              {/* Upload Controls */}
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-primary rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">
                      {uploadedImage === selectedImage
                        ? "Using your AI-generated vision"
                        : "Custom image uploaded"}
                    </span>
                  </div>
                  {uploadedImage && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                </div>

                <div className="grid grid-cols-1 gap-3">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleImageUpload}
                    accept="image/*"
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={triggerFileInput}
                    disabled={isUploading}
                    className="w-full justify-center"
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Upload Different Image
                      </>
                    )}
                  </Button>

                  {selectedImage && selectedImage !== uploadedImage && (
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={() => setUploadedImage(selectedImage)}
                      className="w-full justify-center text-primary"
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Use Generated Vision
                    </Button>
                  )}
                </div>
              </div>

              {errors.image && <p className="mt-2 text-sm text-red-500">{errors.image}</p>}
            </div>
          </div>
        </div>

        {/* Submit Button - Full Width */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex justify-center">
            <Button
              type="submit"
              variant="default"
              size="lg"
              disabled={isSubmitting}
              className="min-w-[250px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Submitting Your Vision...
                </>
              ) : (
                <>
                  Submit Your Vision
                  <ArrowRight className="ml-2 h-5 w-5" />
                </>
              )}
            </Button>
          </div>
        </div>
      </form>

    </div>
  );
};

// Refinement Controls Component (AI-only features)
const RefinementControls = ({
  refinements,
  onRefinementChange,
  onApplyRefinements,
  isRefining,
  disabled = false
}: {
  refinements: {
    colorPalette: string;
    materialFocus: string;
    lighting: string;
  };
  onRefinementChange: (key: string, value: string) => void;
  onApplyRefinements: () => void;
  isRefining: boolean;
  disabled?: boolean;
}) => {
  const colorOptions = [
    { id: 'default', name: 'Default', description: 'Original color scheme' },
    { id: 'warm', name: 'Warm Tones', description: 'Reds, oranges, yellows' },
    { id: 'cool', name: 'Cool Tones', description: 'Blues, greens, purples' },
    { id: 'monochrome', name: 'Monochrome', description: 'Black, white, grays' },
    { id: 'vibrant', name: 'Vibrant', description: 'Bold, saturated colors' },
    { id: 'earthy', name: 'Earthy', description: 'Browns, beiges, natural tones' }
  ];

  const materialOptions = [
    { id: 'balanced', name: 'Balanced Mix', description: 'Variety of materials' },
    { id: 'wood', name: 'Wood Focus', description: 'Emphasize wooden elements' },
    { id: 'concrete', name: 'Concrete', description: 'Modern concrete finishes' },
    { id: 'glass', name: 'Glass', description: 'Transparent, reflective surfaces' },
    { id: 'metal', name: 'Metal', description: 'Steel, aluminum accents' },
    { id: 'stone', name: 'Stone', description: 'Natural stone textures' },
    { id: 'fabric', name: 'Fabric', description: 'Soft textile elements' }
  ];

  const lightingOptions = [
    { id: 'natural', name: 'Natural Light', description: 'Daylight, windows' },
    { id: 'dramatic', name: 'Dramatic', description: 'Strong contrasts, shadows' },
    { id: 'soft', name: 'Soft', description: 'Gentle, diffused lighting' },
    { id: 'bright', name: 'Bright', description: 'Well-lit, energetic' },
    { id: 'moody', name: 'Moody', description: 'Atmospheric, intimate' },
    { id: 'golden', name: 'Golden Hour', description: 'Warm, sunset lighting' }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: "auto" }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.4 }}
      className={`border border-gray-200 rounded-lg p-6 bg-gradient-to-r from-green-50 to-blue-50 ${
        disabled ? 'opacity-50 pointer-events-none' : ''
      }`}
    >
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-bold text-gray-800 flex items-center">
            <Sparkles className="h-5 w-5 text-green-600 mr-2" />
            AI Refinement Controls
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            Fine-tune your AI-generated design with these controls
          </p>
        </div>
        {disabled && (
          <div className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
            AI images only
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Color Palette */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Color Palette
          </label>
          <div className="space-y-2">
            {colorOptions.map((option) => (
              <label
                key={option.id}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                  refinements.colorPalette === option.id
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="colorPalette"
                  value={option.id}
                  checked={refinements.colorPalette === option.id}
                  onChange={(e) => onRefinementChange('colorPalette', e.target.value)}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium">{option.name}</div>
                  <div className="text-xs text-gray-500">{option.description}</div>
                </div>
                {refinements.colorPalette === option.id && (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                )}
              </label>
            ))}
          </div>
        </div>

        {/* Material Focus */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Material Focus
          </label>
          <div className="space-y-2">
            {materialOptions.map((option) => (
              <label
                key={option.id}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                  refinements.materialFocus === option.id
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="materialFocus"
                  value={option.id}
                  checked={refinements.materialFocus === option.id}
                  onChange={(e) => onRefinementChange('materialFocus', e.target.value)}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium">{option.name}</div>
                  <div className="text-xs text-gray-500">{option.description}</div>
                </div>
                {refinements.materialFocus === option.id && (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                )}
              </label>
            ))}
          </div>
        </div>

        {/* Lighting */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Lighting Style
          </label>
          <div className="space-y-2">
            {lightingOptions.map((option) => (
              <label
                key={option.id}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                  refinements.lighting === option.id
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="lighting"
                  value={option.id}
                  checked={refinements.lighting === option.id}
                  onChange={(e) => onRefinementChange('lighting', e.target.value)}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium">{option.name}</div>
                  <div className="text-xs text-gray-500">{option.description}</div>
                </div>
                {refinements.lighting === option.id && (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                )}
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Apply Button */}
      <div className="flex justify-center">
        <Button
          onClick={onApplyRefinements}
          disabled={isRefining}
          className="min-w-[200px]"
          size="lg"
        >
          {isRefining ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Applying Refinements...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-5 w-5" />
              Apply Refinements
            </>
          )}
        </Button>
      </div>
    </motion.div>
  );
};

// Enhanced loading component for Step 2 (Generation phase)
const GenerationLoadingScreen = ({
  prompt,
  styleName,
  serviceName
}: {
  prompt: string;
  styleName: string;
  serviceName: string;
}) => {
  const [loadingMessage, setLoadingMessage] = useState("Analyzing your vision...");
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const messages = [
      "Analyzing your vision...",
      "Selecting the perfect style...",
      "Generating your design...",
      "Adding finishing touches...",
      "Almost ready..."
    ];

    let messageIndex = 0;
    let progressValue = 0;

    const interval = setInterval(() => {
      messageIndex = (messageIndex + 1) % messages.length;
      setLoadingMessage(messages[messageIndex]);

      progressValue = Math.min(progressValue + 20, 90);
      setProgress(progressValue);
    }, 800);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center px-4">
        {/* Main loading animation */}
        <motion.div
          className="mb-8"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative w-32 h-32 mx-auto mb-6">
            <motion.div
              className="absolute inset-0 border-4 border-primary/20 rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            />
            <motion.div
              className="absolute inset-2 border-4 border-primary border-t-transparent rounded-full"
              animate={{ rotate: -360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <Sparkles className="h-8 w-8 text-primary" />
            </div>
          </div>
        </motion.div>

        {/* Loading message */}
        <motion.div
          key={loadingMessage}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="mb-6"
        >
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
            We're generating your vision...
          </h2>
          <p className="text-lg text-primary font-medium mb-4">
            {loadingMessage}
          </p>
        </motion.div>

        {/* Progress bar */}
        <div className="mb-8">
          <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-primary to-primary/80"
              initial={{ width: "0%" }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <p className="text-sm text-gray-500 mt-2">{progress}% complete</p>
        </div>

        {/* Project details */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="bg-gray-50 rounded-lg p-6 text-left"
        >
          <h3 className="font-bold text-lg mb-4 text-center">Your Vision Details</h3>
          <div className="space-y-3">
            <div>
              <span className="text-sm font-medium text-gray-600">Service:</span>
              <p className="text-gray-800">{serviceName}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600">Style:</span>
              <p className="text-gray-800">{styleName}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600">Description:</span>
              <p className="text-gray-800 line-clamp-3">{prompt}</p>
            </div>
          </div>
        </motion.div>

        {/* Fun facts or tips while loading */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="mt-8 text-center"
        >
          <p className="text-sm text-gray-500 italic">
            💡 Did you know? Our AI considers over 1000 design principles to create your perfect vision
          </p>
        </motion.div>
      </div>
    </div>
  );
};

// Skeleton loader for images
const ImageSkeleton = ({ aspectRatio = "aspect-[4/3]" }: { aspectRatio?: string }) => {
  return (
    <div className="bg-white border rounded-lg overflow-hidden">
      <div className={`${aspectRatio} bg-gray-200 animate-pulse`}></div>
      <div className="p-4">
        <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
      </div>
    </div>
  );
};

// Function to get the appropriate hero image based on service ID
const getServiceHeroImage = (serviceId: string): string => {
  // Map service IDs to their corresponding image paths
  const serviceImageMap: Record<string, string> = {
    "creative-design-&-branding": "/services-images/branding.jpg",
    "innovative-architectural-design": "/services-images/innovative-architecture.jpg",
    "interior-design": "/services-images/interior-design.jpg",
    "urban-&-architectural-planning": "/services-images/urban-plan.jpg",
    "residential-&-commercial-projects": "/services-images/residential.jpg",
    "landscape-and-architecture-integration": "/services-images/landscape-and-architure.jpg",
    "educational-&-community-oriented-spaces": "/services-images/community-and-educational.jpg"
  };

  // Return the corresponding image path or a default image if not found
  return serviceImageMap[serviceId] ||
    "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80";
};

// Collapsible section component for mobile
const CollapsibleSection = ({
  title,
  children,
  defaultOpen = false,
  mobileOnly = true
}: {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  mobileOnly?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className={`${mobileOnly ? 'md:hidden' : ''} border border-gray-200 mb-6`}>
      <button
        className="w-full p-4 flex justify-between items-center bg-gray-50 text-left"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <h3 className="font-bold text-lg">{title}</h3>
        <div className="transform transition-transform duration-200">
          {isOpen ? (
            <ChevronUp className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronDown className="h-5 w-5 text-gray-500" />
          )}
        </div>
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-4">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default function ServiceVisionBuilder() {
  const params = useParams();
  const router = useRouter();

  // Get the raw service ID from the URL and decode it
  const rawServiceId = typeof params.service === 'string' ? params.service : '';
  const decodedServiceId = fromUrlSafeServiceId(rawServiceId);

  // Get all available service keys from the designStylesByService object
  const availableServiceKeys = Object.keys(designStylesByService);

  // Map the decoded service ID to the correct key in the designStylesByService object
  const serviceId = mapServiceIdToKey(decodedServiceId, availableServiceKeys);

  const [selectedStyle, setSelectedStyle] = useState("");
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [generationComplete, setGenerationComplete] = useState(false);
  const [availableR2Images, setAvailableR2Images] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [imageTypes, setImageTypes] = useState<("ai_generated" | "style_example")[]>([]); // Track image types
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0); // 0: Landing, 1: Style selection, 2: Generation, 3: Results, 4: Form, 5: Success
  const [isPromptFocused, setIsPromptFocused] = useState(false);
  const [showExamplePrompts, setShowExamplePrompts] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState<string>("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [generationMethod, setGenerationMethod] = useState<"ai" | "r2" | "fallback">("ai"); // Track how images were generated

  // Refinement controls state (only for AI-generated images)
  const [refinements, setRefinements] = useState({
    colorPalette: 'default', // default, warm, cool, monochrome, vibrant, earthy
    materialFocus: 'balanced', // balanced, wood, concrete, glass, metal, stone, fabric
    lighting: 'natural' // natural, dramatic, soft, bright, moody, golden
  });
  const [showRefinements, setShowRefinements] = useState(false);
  const [isRefining, setIsRefining] = useState(false);

  // New state for export options and alternative images
  const [showAlternatives, setShowAlternatives] = useState(false);
  const [activeExportTab, setActiveExportTab] = useState("submit");
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const promptInputRef = useRef<HTMLTextAreaElement>(null);

  // Format the service name for display
  const formatServiceName = (id: string) => {
    return id
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
      .replace(/&/g, '&');
  };

  const serviceName = formatServiceName(serviceId);

  // Get the available styles for this service
  const availableStyles = designStylesByService[serviceId as keyof typeof designStylesByService] || [];

  // Get example prompts for this service
  const serviceExamplePrompts = examplePrompts[serviceId as keyof typeof examplePrompts] || [];

  // Handle style selection
  const handleStyleSelect = (styleId: string) => {
    setSelectedStyle(styleId);
    if (promptInputRef.current) {
      promptInputRef.current.focus();
    }

    // Show a toast notification
    toast.success(`${availableStyles.find(s => s.id === styleId)?.name} style selected!`);
  };

  // Handle example prompt selection
  const handleExamplePromptSelect = (examplePrompt: string) => {
    setPrompt(examplePrompt);
    setShowExamplePrompts(false);
    if (promptInputRef.current) {
      promptInputRef.current.focus();
    }
  };

  // Fetch available R2 images for the selected style
  useEffect(() => {
    if (selectedStyle && currentStep === 1) {
      const fetchImages = async () => {
        try {
          const images = await fetchImagesForStyle(serviceId, selectedStyle);
          setAvailableR2Images(images);
        } catch (error) {
          console.error("Error fetching style images:", error);
          toast.error("Couldn't load example images for this style");
        }
      };

      fetchImages();
    }
  }, [selectedStyle, serviceId, currentStep]);

  // Generate images using AI or R2
  const generateImages = async () => {
    if (!selectedStyle || !prompt) return;

    setIsGenerating(true);
    setCurrentStep(2);

    try {
      // Get the style name for better prompting
      const styleName = availableStyles.find(s => s.id === selectedStyle)?.name || selectedStyle;

      toast.loading("Generating your vision with AI...", { id: "generate-toast" });

      // Try to generate an image using AI
      const imageUrl = await getAiGeneratedImage(prompt, serviceName, styleName);

      // Set the generated image
      const newImages = [imageUrl];
      const newImageTypes: ("ai_generated" | "style_example")[] = ["ai_generated"];

      // Add some R2 images as alternatives
      for (let i = 0; i < 3; i++) {
        try {
          const altImage = await getRandomImageForStyle(serviceId, selectedStyle);
          newImages.push(altImage);
          newImageTypes.push("style_example");
        } catch (error) {
          const randomIndex = Math.floor(Math.random() * fallbackImages.length);
          newImages.push(fallbackImages[randomIndex]);
          newImageTypes.push("style_example");
        }
      }

      setGeneratedImages(newImages);
      setImageTypes(newImageTypes);
      setSelectedImage(imageUrl);
      setGenerationMethod("ai");
      setGenerationComplete(true);
      setCurrentStep(3);

      toast.success("Your AI-powered vision has been generated!", { id: "generate-toast" });
    } catch (error) {
      console.error("Error generating images:", error);
      toast.error("AI generation failed. Using curated examples instead.", { id: "generate-toast" });

      // Use R2 images as fallback if AI generation fails
      try {
        const imageUrl = await getRandomImageForStyle(serviceId, selectedStyle);
        const newImages = [imageUrl];
        const newImageTypes: ("ai_generated" | "style_example")[] = ["style_example"];

        // Add more R2 images to fill the grid
        for (let i = 0; i < 3; i++) {
          try {
            const altImage = await getRandomImageForStyle(serviceId, selectedStyle);
            newImages.push(altImage);
            newImageTypes.push("style_example");
          } catch (r2Error) {
            const randomIndex = Math.floor(Math.random() * fallbackImages.length);
            newImages.push(fallbackImages[randomIndex]);
            newImageTypes.push("style_example");
          }
        }

        setGeneratedImages(newImages);
        setImageTypes(newImageTypes);
        setSelectedImage(imageUrl);
        setGenerationMethod("r2");
      } catch (r2Error) {
        // If both AI and R2 fail, use fallback images
        console.error("Error fetching R2 images:", r2Error);
        setGeneratedImages(fallbackImages);
        setImageTypes(Array(fallbackImages.length).fill("style_example"));
        setSelectedImage(fallbackImages[0]);
        setGenerationMethod("fallback");
        toast.error("Using backup examples. Please try again later for AI generation.");
      }

      setGenerationComplete(true);
      setCurrentStep(3);
    } finally {
      setIsGenerating(false);
    }
  };

  // Regenerate the image using AI or get the next one in sequence
  const regenerateImage = async () => {
    if (!selectedStyle || isRegenerating) return;

    setIsRegenerating(true);
    toast.loading("Regenerating your vision...", { id: "regenerate-toast" });

    try {
      // Get the style name for better prompting
      const styleName = availableStyles.find(s => s.id === selectedStyle)?.name || selectedStyle;

      // Try to regenerate using AI first
      try {
        // Generate a new AI image with the original prompt
        const newImageUrl = await regenerateAiImage(prompt, serviceName, styleName);

        // Update the selected image
        setSelectedImage(newImageUrl);

        // Update the first image in the generated images array
        const updatedImages = [...generatedImages];
        updatedImages[0] = newImageUrl;
        setGeneratedImages(updatedImages);

        toast.success("New vision generated!", { id: "regenerate-toast" });
      } catch (aiError) {
        console.error("Error regenerating AI image:", aiError);
        toast.error("AI regeneration failed. Using alternative image.", { id: "regenerate-toast" });

        // Fall back to R2 image sequence if AI generation fails
        const result = await getNextImageInSequence(
          serviceId,
          selectedStyle,
          availableR2Images.length > 0 ? availableR2Images : generatedImages,
          currentImageIndex
        );

        // Update the selected image and current index
        setSelectedImage(result.nextImage);
        setCurrentImageIndex(result.nextIndex);

        // Update the first image in the generated images array
        const updatedImages = [...generatedImages];
        updatedImages[0] = result.nextImage;
        setGeneratedImages(updatedImages);
      }
    } catch (error) {
      console.error("Error regenerating image:", error);
      toast.error("Failed to regenerate. Please try again.", { id: "regenerate-toast" });
    } finally {
      setIsRegenerating(false);
    }
  };

  // Handle image selection
  const handleImageSelect = (image: string) => {
    setSelectedImage(image);
    toast.success("Image selected!");
  };

  // Handle refinement changes
  const handleRefinementChange = (key: string, value: string) => {
    setRefinements(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Apply refinements to AI-generated image
  const applyRefinements = async () => {
    if (!selectedImage || !selectedImage.startsWith('data:') || !selectedStyle || !prompt) {
      toast.error("Refinements only work with AI-generated images");
      return;
    }

    setIsRefining(true);
    toast.loading("Applying refinements to your design...", { id: "refine-toast" });

    try {
      // Get the style name for better prompting
      const styleName = availableStyles.find(s => s.id === selectedStyle)?.name || selectedStyle;

      // Build enhanced prompt with refinements
      let enhancedPrompt = `Generate an image of a professional ${styleName} design for a ${serviceName} project. ${prompt}`;

      // Add color refinements
      if (refinements.colorPalette !== 'default') {
        const colorDescriptions = {
          warm: 'with warm color tones (reds, oranges, yellows)',
          cool: 'with cool color tones (blues, greens, purples)',
          monochrome: 'in monochrome (black, white, and gray tones)',
          vibrant: 'with vibrant, bold, saturated colors',
          earthy: 'with earthy, natural color tones (browns, beiges, natural colors)'
        };
        enhancedPrompt += ` ${colorDescriptions[refinements.colorPalette as keyof typeof colorDescriptions]}`;
      }

      // Add material refinements
      if (refinements.materialFocus !== 'balanced') {
        const materialDescriptions = {
          wood: 'emphasizing wooden materials and textures',
          concrete: 'featuring modern concrete finishes and surfaces',
          glass: 'with prominent glass elements and transparent surfaces',
          metal: 'highlighting metal accents and steel elements',
          stone: 'incorporating natural stone textures and materials',
          fabric: 'featuring soft textile elements and fabric textures'
        };
        enhancedPrompt += ` ${materialDescriptions[refinements.materialFocus as keyof typeof materialDescriptions]}`;
      }

      // Add lighting refinements
      if (refinements.lighting !== 'natural') {
        const lightingDescriptions = {
          dramatic: 'with dramatic lighting and strong shadows',
          soft: 'with soft, gentle, diffused lighting',
          bright: 'with bright, well-lit, energetic lighting',
          moody: 'with moody, atmospheric, intimate lighting',
          golden: 'with warm golden hour lighting and sunset tones'
        };
        enhancedPrompt += ` ${lightingDescriptions[refinements.lighting as keyof typeof lightingDescriptions]}`;
      }

      enhancedPrompt += '. Please provide both a description and a visual representation.';

      console.log('Applying refinements with enhanced prompt:', enhancedPrompt);

      // Generate refined image using AI
      const refinedImageUrl = await getAiGeneratedImage(enhancedPrompt, serviceName, styleName);

      // Update the selected image and the first image in the array
      setSelectedImage(refinedImageUrl);
      const updatedImages = [...generatedImages];
      updatedImages[0] = refinedImageUrl;
      setGeneratedImages(updatedImages);

      // Update image types to reflect the refined image is still AI-generated
      const updatedImageTypes = [...imageTypes];
      updatedImageTypes[0] = "ai_generated";
      setImageTypes(updatedImageTypes);

      toast.success("Refinements applied successfully!", { id: "refine-toast" });
    } catch (error) {
      console.error("Error applying refinements:", error);
      toast.error("Failed to apply refinements. Please try again.", { id: "refine-toast" });
    } finally {
      setIsRefining(false);
    }
  };
  // Handle image download
  const handleDownload = () => {
    if (!selectedImage) return;

    toast.loading("Preparing download...", { id: "download-toast" });

    try {
      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = selectedImage;
      link.download = `${serviceId}-${selectedStyle}-visualization.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Download started!", { id: "download-toast" });
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Download failed. Please try again.", { id: "download-toast" });
    }
  };

  // Continue to the next step in the submission process
  const handleContinue = () => {
    toast.success("Moving to the next step...");
    // Change to step 4 (Vision Submission Form)
    setCurrentStep(4);
  };

  // Handle going back from the vision submission form to the results
  const handleGoBackToResults = () => {
    setCurrentStep(3);
  };

  // Handle form submission
  const handleFormSubmit = async (formData: RequestFormData) => {
    try {
      console.log("Vision Builder form submission started");

      // Get the style name for better tracking
      const styleName = availableStyles.find(s => s.id === selectedStyle)?.name || selectedStyle;
      console.log("Selected style:", styleName);
      console.log("Form data:", formData);

      // Create tracking request first to get the tracking number
      console.log("Creating tracking request...");
      const trackingData = await createVisionBuilderTracking({
        name: formData.name,
        email: formData.email,
        projectType: formData.projectType,
        visionPrompt: formData.vision,
        selectedStyle: styleName,
        serviceCategory: serviceName,
        imageUrl: "", // We'll update this after uploading
      });

      console.log("Tracking request created:", trackingData);

      // Set tracking number from response
      setTrackingNumber(trackingData.tracking_number);

      // Upload the image to Cloudflare R2 if it's a data URL (AI-generated)
      let imageUrl = "";
      if (selectedImage && selectedImage.startsWith('data:')) {
        console.log("Uploading AI-generated image to R2...");
        // Upload AI-generated image
        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            imageDataUrl: selectedImage,
            requestId: trackingData.tracking_number,
          }),
        });

        if (!uploadResponse.ok) {
          const errorText = await uploadResponse.text();
          console.error("Upload response error:", errorText);
          throw new Error(`Failed to upload AI-generated image: ${errorText}`);
        }

        const uploadData = await uploadResponse.json();
        console.log("AI image uploaded successfully:", uploadData);
        imageUrl = uploadData.fileKey;
      }
      // If it's a user-uploaded image (File object)
      else if (formData.uploadedImage) {
        console.log("Uploading user image to R2...");
        // Create a FormData object to send the file
        const formDataObj = new FormData();
        formDataObj.append('file', formData.uploadedImage);
        formDataObj.append('type', 'vision_builder');
        formDataObj.append('requestId', trackingData.tracking_number);

        // Upload the file
        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formDataObj,
        });

        if (!uploadResponse.ok) {
          const errorText = await uploadResponse.text();
          console.error("Upload response error:", errorText);
          throw new Error(`Failed to upload image: ${errorText}`);
        }

        const uploadData = await uploadResponse.json();
        console.log("User image uploaded successfully:", uploadData);
        imageUrl = uploadData.fileKey;
      }
      // If it's a URL from R2 or external source, use it directly
      else if (selectedImage) {
        console.log("Using existing image URL:", selectedImage);
        imageUrl = selectedImage;
      }

      // Update the tracking record with the image URL if we have one
      if (imageUrl) {
        console.log("Updating tracking record with image URL...");
        const updateResponse = await fetch(`/api/tracking/${trackingData.tracking_number}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ imageUrl }),
        });

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.error("Update tracking response error:", errorText);
          throw new Error(`Failed to update tracking record: ${errorText}`);
        }

        const updateData = await updateResponse.json();
        console.log("Tracking record updated:", updateData);
      }

      console.log("Vision Builder submission completed successfully");

      // Show success message and update state
      toast.success("Your vision has been submitted successfully!");
      setIsSubmitted(true);
      setCurrentStep(5);
    } catch (error) {
      console.error("Error submitting vision request:", error);
      toast.error(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      alert(`Error submitting request: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };
  // Go back to the previous step
  const handleGoBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <Layout>
      {/* Toast notifications container */}
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            iconTheme: {
              primary: '#4CAF50',
              secondary: '#FFFFFF',
            },
          },
          error: {
            iconTheme: {
              primary: '#E53935',
              secondary: '#FFFFFF',
            },
          },
        }}
      />

      {/* Enhanced Hero Section - Mobile Optimized */}
      <section className="relative min-h-[70vh] sm:min-h-[75vh] md:min-h-[80vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <div
            className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/30 z-10"
            aria-hidden="true"
          />
          <Image
            src={getServiceHeroImage(serviceId)}
            alt={`${serviceName} visualization`}
            fill
            priority
            sizes="100vw"
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 md:px-8 z-20 pt-24 sm:pt-28 md:pt-32 lg:pt-20 pb-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl"
          >
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex items-center mb-6"
            >
              <Link href="/#services-section" className="text-white hover:text-primary transition-colors flex items-center mr-4">
                <ArrowLeft className="h-5 w-5 mr-2" />
                <span className="text-sm md:text-base">Back to Services</span>
              </Link>
              <span className="inline-block px-4 py-2 bg-primary/20 backdrop-blur-sm text-white text-sm font-medium rounded-full border border-white/20">
                AI-Powered Design Generation
              </span>
            </motion.div>

            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight">
              <span className="block">Visualize Your</span>
              <span className="block text-primary/90">{serviceName}</span>
            </h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-lg sm:text-xl md:text-2xl text-white/90 max-w-3xl mb-6 sm:mb-8 leading-relaxed"
            >
              Transform your ideas into stunning visual concepts with our advanced AI technology.
              Describe your vision and watch it come to life in seconds.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="hidden md:flex flex-wrap gap-4 text-white/80"
            >
              <div className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">AI-Generated Designs</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">Instant Results</span>
              </div>
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">Unlimited Variations</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-6 md:px-8">
          <div className="max-w-6xl mx-auto">
            {/* Progress Steps */}
            <ProgressSteps currentStep={currentStep} />

            {/* Step 0: Landing Screen */}
            <AnimatePresence mode="wait">
              {currentStep === 0 && (
                <motion.div
                  key="step0"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white shadow-xl rounded-lg p-8 md:p-16 mb-16 text-center"
                >
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="mb-8"
                  >
                    <div className="w-24 h-24 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center">
                      <Sparkles className="h-12 w-12 text-primary" />
                    </div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                      Start Your Vision
                    </h2>
                    <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8 leading-relaxed">
                      Take our quick style quiz to discover your design preferences and visualize
                      your ideas instantly with AI
                    </p>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mb-8"
                  >
                    <Button
                      variant="default"
                      size="lg"
                      className="group relative overflow-hidden px-8 py-4 text-lg"
                      onClick={() => setCurrentStep(1)}
                    >
                      <span className="flex items-center">
                        Get Started
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                      </span>
                    </Button>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                    className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
                  >
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-3 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-primary font-bold">1</span>
                      </div>
                      <h3 className="font-semibold mb-2">Choose Style</h3>
                      <p className="text-sm text-gray-600">Select from our curated design styles</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-3 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-primary font-bold">2</span>
                      </div>
                      <h3 className="font-semibold mb-2">Describe Vision</h3>
                      <p className="text-sm text-gray-600">Tell us about your project in detail</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-3 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-primary font-bold">3</span>
                      </div>
                      <h3 className="font-semibold mb-2">Get Results</h3>
                      <p className="text-sm text-gray-600">Receive AI-generated visualizations</p>
                    </div>
                  </motion.div>
                </motion.div>
              )}

              {/* Step 1: Style Selection */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white shadow-xl rounded-lg p-6 md:p-12 mb-16"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-3xl md:text-4xl font-bold mb-4">Select a Design Style</h2>
                    <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                      Choose the aesthetic direction that best matches your vision for this {serviceName.toLowerCase()} project.
                    </p>
                  </div>

                  <div className="mb-8">
                    <StyleSelect
                      styles={availableStyles}
                      selectedStyle={selectedStyle}
                      onSelect={handleStyleSelect}
                    />
                    {selectedStyle && (
                      <StyleDescription
                        style={availableStyles.find(s => s.id === selectedStyle)}
                      />
                    )}
                  </div>

                  {/* Text Prompt */}
                  <div className="mb-6 relative">
                    <div className="flex justify-between items-center mb-2">
                      <label htmlFor="prompt" className="block text-sm font-medium text-gray-700">
                        Describe your {serviceName.toLowerCase()} vision in detail
                      </label>
                      <button
                        type="button"
                        onClick={() => setShowExamplePrompts(!showExamplePrompts)}
                        className="text-primary text-sm flex items-center hover:underline focus:outline-none"
                        aria-label="Show example prompts"
                      >
                        <HelpCircle className="h-4 w-4 mr-1" />
                        Examples
                      </button>
                    </div>

                    <div className="relative">
                      <textarea
                        ref={promptInputRef}
                        id="prompt"
                        rows={4}
                        className={`w-full p-3 border rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
                          ${isPromptFocused ? 'border-primary' : 'border-gray-300'}
                          ${!selectedStyle ? 'bg-gray-50' : 'bg-white'}`}
                        placeholder={`Example: A ${selectedStyle ? availableStyles.find(s => s.id === selectedStyle)?.name.toLowerCase() : 'modern'} design for my ${serviceName.toLowerCase()} project with...`}
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        onFocus={() => setIsPromptFocused(true)}
                        onBlur={() => setIsPromptFocused(false)}
                        disabled={!selectedStyle}
                      ></textarea>

                      {/* Character count */}
                      <div className="absolute bottom-2 right-3 text-xs text-gray-500">
                        {prompt.length}/500
                      </div>
                    </div>

                    {/* Example prompts dropdown */}
                    <AnimatePresence>
                      {showExamplePrompts && serviceExamplePrompts.length > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 py-1"
                        >
                          <div className="p-2 text-sm font-medium text-gray-700 border-b">
                            Click an example to use it:
                          </div>
                          {serviceExamplePrompts.map((examplePrompt, index) => (
                            <button
                              key={index}
                              className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none transition-colors"
                              onClick={() => handleExamplePromptSelect(examplePrompt)}
                            >
                              {examplePrompt}
                            </button>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* Generate Button */}
                  <div className="flex justify-center">
                    <Button
                      variant="default"
                      size="lg"
                      className="group relative overflow-hidden"
                      onClick={generateImages}
                      disabled={!selectedStyle || !prompt || prompt.length < 10}
                    >
                      <span className="flex items-center">
                        Generate Visualization
                        <Sparkles className="ml-2 h-5 w-5" />
                      </span>
                      {(!selectedStyle || !prompt || prompt.length < 10) && (
                        <span className="absolute -bottom-6 left-0 right-0 text-xs text-center opacity-70 transition-opacity">
                          {!selectedStyle ? 'Select a style first' :
                           !prompt ? 'Add a description' :
                           'Description too short'}
                        </span>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Enhanced Generation in Progress */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white shadow-xl rounded-lg mb-12"
                >
                  <GenerationLoadingScreen
                    prompt={prompt}
                    styleName={availableStyles.find(s => s.id === selectedStyle)?.name || selectedStyle}
                    serviceName={serviceName}
                  />

                  {/* Cancel button */}
                  <div className="p-6 border-t border-gray-100 text-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleGoBack}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Cancel Generation
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Results */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="mb-16"
                >
                  <div className="bg-white shadow-xl rounded-lg p-6 md:p-12 mb-16">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
                      <div className="flex flex-col md:flex-row md:items-center gap-3">
                        <h2 className="text-2xl md:text-3xl font-bold">Your Vision, Visualized</h2>
                        <div className="flex gap-2">
                          {generationMethod === "ai" && (
                            <div className="bg-green-100 text-green-800 text-xs font-medium px-3 py-1 rounded-full flex items-center">
                              <Sparkles className="h-3 w-3 mr-1" />
                              AI Generated
                            </div>
                          )}
                          {generationMethod === "r2" && (
                            <div className="bg-blue-100 text-blue-800 text-xs font-medium px-3 py-1 rounded-full">
                              Curated Examples
                            </div>
                          )}
                          {generationMethod === "fallback" && (
                            <div className="bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full">
                              Backup Examples
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-5 w-5 mr-2" />
                        <span className="text-sm font-medium">Generation Complete</span>
                      </div>
                    </div>

                    {/* Generation method explanation */}
                    {generationMethod !== "ai" && (
                      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <p className="text-sm text-blue-800">
                          {generationMethod === "r2"
                            ? "AI generation is temporarily unavailable. We've provided curated design examples that match your selected style."
                            : "We're experiencing technical difficulties. These backup examples can still help visualize your project."
                          }
                        </p>
                      </div>
                    )}

                    {/* Main Generated Image with Enhanced Export System */}
                    {selectedImage && (
                      <div className="bg-white shadow-xl rounded-xl overflow-hidden mb-8">
                        {/* Image Container with Floating Actions */}
                        <div className="aspect-[16/9] relative group">
                          <Image
                            src={generatedImages[selectedImageIndex] || selectedImage}
                            alt="Your selected design"
                            fill
                            priority
                            sizes="(max-width: 768px) 100vw, 800px"
                            className="object-cover"
                          />

                          {/* Image Quality Badge */}
                          <div className="absolute top-4 left-4">
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2"
                            >
                              <Star className="h-4 w-4 text-yellow-400" />
                              High Quality
                            </motion.div>
                          </div>

                          {/* Image Type Indicator */}
                          <div className="absolute top-4 right-4">
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className={`px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm ${
                                selectedImage.startsWith('data:')
                                  ? 'bg-green-500/90 text-white'
                                  : 'bg-blue-500/90 text-white'
                              }`}
                            >
                              {selectedImage.startsWith('data:') ? (
                                <div className="flex items-center gap-1">
                                  <Zap className="h-3 w-3" />
                                  AI Generated
                                </div>
                              ) : (
                                <div className="flex items-center gap-1">
                                  <Badge className="h-3 w-3" />
                                  Style Example
                                </div>
                              )}
                            </motion.div>
                          </div>

                          {/* Image Number Badge */}
                          <div className="absolute bottom-4 left-4">
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium"
                            >
                              {selectedImageIndex + 1} of {generatedImages.length}
                            </motion.div>
                          </div>

                          {/* Always-Visible Floating Action Icons */}
                          {/* Desktop: Vertical Column on Right */}
                          <div className="hidden md:block absolute top-4 right-4">
                            <div className="flex flex-col gap-2">
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={handleDownload}
                                className="p-3 bg-white/90 backdrop-blur-sm text-gray-700 hover:text-primary hover:bg-white transition-all rounded-lg shadow-lg"
                                title="Download Image"
                              >
                                <Download className="h-5 w-5" />
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                className="p-3 bg-white/90 backdrop-blur-sm text-gray-700 hover:text-primary hover:bg-white transition-all rounded-lg shadow-lg"
                                title="Share Image"
                              >
                                <Share2 className="h-5 w-5" />
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={regenerateImage}
                                disabled={isRegenerating}
                                className="p-3 bg-white/90 backdrop-blur-sm text-gray-700 hover:text-primary hover:bg-white transition-all rounded-lg shadow-lg"
                                title="Regenerate"
                              >
                                {isRegenerating ? (
                                  <Loader2 className="h-5 w-5 animate-spin" />
                                ) : (
                                  <RefreshCw className="h-5 w-5" />
                                )}
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={handleContinue}
                                className="p-3 bg-primary text-white hover:bg-primary/90 transition-all rounded-lg shadow-lg"
                                title="Submit to Architects"
                              >
                                <Send className="h-5 w-5" />
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                className="p-3 bg-white/90 backdrop-blur-sm text-gray-700 hover:text-red-400 hover:bg-white transition-all rounded-lg shadow-lg"
                                title="Save to Favorites"
                              >
                                <Heart className="h-5 w-5" />
                              </motion.button>
                            </div>
                          </div>

                          {/* Mobile: Horizontal Row at Bottom */}
                          <div className="md:hidden absolute bottom-4 left-4 right-4">
                            <div className="flex items-center justify-center gap-3 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={handleDownload}
                                className="p-2 text-gray-700 hover:text-primary transition-colors rounded-md"
                                title="Download"
                              >
                                <Download className="h-5 w-5" />
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                className="p-2 text-gray-700 hover:text-primary transition-colors rounded-md"
                                title="Share"
                              >
                                <Share2 className="h-5 w-5" />
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={regenerateImage}
                                disabled={isRegenerating}
                                className="p-2 text-gray-700 hover:text-primary transition-colors rounded-md"
                                title="Regenerate"
                              >
                                {isRegenerating ? (
                                  <Loader2 className="h-5 w-5 animate-spin" />
                                ) : (
                                  <RefreshCw className="h-5 w-5" />
                                )}
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={handleContinue}
                                className="p-2 bg-primary text-white hover:bg-primary/90 transition-all rounded-md"
                                title="Submit"
                              >
                                <Send className="h-5 w-5" />
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                className="p-2 text-gray-700 hover:text-red-400 transition-colors rounded-md"
                                title="Favorite"
                              >
                                <Heart className="h-5 w-5" />
                              </motion.button>
                            </div>
                          </div>
                        </div>

                        {/* Prominent Next Step Actions */}
                        <div className="p-6">
                          <div className="flex flex-col sm:flex-row justify-center gap-4">
                            <Button
                              variant="outline"
                              size="lg"
                              className="group"
                              onClick={handleGoBack}
                            >
                              <ArrowLeft className="mr-2 h-5 w-5" />
                              Go Back
                            </Button>

                            <Button
                              variant="default"
                              size="lg"
                              className="group"
                              onClick={handleContinue}
                            >
                              Continue to Next Step
                              <ArrowRight className="ml-2 h-5 w-5" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Enhanced Refinement Controls - Only for AI-generated images */}
                    {selectedImage && selectedImage.startsWith('data:') && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                        className="mb-8"
                      >
                        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                          {/* Refinement Header */}
                          <div className="bg-gradient-to-r from-primary/5 to-primary/10 p-6 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                              <div>
                                <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                                  <Settings className="h-5 w-5 text-primary" />
                                  Refine Your Design
                                </h3>
                                <p className="text-sm text-gray-600 mt-1">
                                  Fine-tune your AI-generated design with advanced controls
                                </p>
                              </div>
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => setShowRefinements(!showRefinements)}
                                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                                  showRefinements
                                    ? 'bg-primary text-white'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:border-primary'
                                }`}
                              >
                                {showRefinements ? (
                                  <div className="flex items-center gap-2">
                                    <ChevronUp className="h-4 w-4" />
                                    Hide Controls
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    <ChevronDown className="h-4 w-4" />
                                    Show Controls
                                  </div>
                                )}
                              </motion.button>
                            </div>
                          </div>

                          {/* Collapsible Refinement Content */}
                          <AnimatePresence>
                            {showRefinements && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3, ease: "easeInOut" }}
                                className="overflow-hidden"
                              >
                                <div className="p-6">
                                  {/* Refinement Status */}
                                  {isRefining && (
                                    <motion.div
                                      initial={{ opacity: 0, y: -10 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4"
                                    >
                                      <div className="flex items-center gap-3">
                                        <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
                                        <div>
                                          <p className="font-medium text-blue-900">Refining Your Design</p>
                                          <p className="text-sm text-blue-700">Please wait while we apply your refinements...</p>
                                        </div>
                                      </div>
                                    </motion.div>
                                  )}

                                  {/* Refinement Controls Component */}
                                  <RefinementControls
                                    refinements={refinements}
                                    onRefinementChange={handleRefinementChange}
                                    onApplyRefinements={applyRefinements}
                                    isRefining={isRefining}
                                    disabled={!selectedImage || !selectedImage.startsWith('data:')}
                                  />

                                  {/* Refinement Tips */}
                                  <div className="mt-6 bg-gray-50 rounded-lg p-4">
                                    <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                                      <Info className="h-4 w-4 text-primary" />
                                      Refinement Tips
                                    </h4>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-gray-600">
                                      <div className="flex items-start gap-2">
                                        <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                        <span>Use specific color names for better results</span>
                                      </div>
                                      <div className="flex items-start gap-2">
                                        <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                        <span>Describe materials and textures clearly</span>
                                      </div>
                                      <div className="flex items-start gap-2">
                                        <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                        <span>Mention lighting preferences</span>
                                      </div>
                                      <div className="flex items-start gap-2">
                                        <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                                        <span>Include spatial relationships</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      </motion.div>
                    )}

                    {/* Alternative Images - Hidden by Default */}
                    {generatedImages.length > 1 && (
                      <div className="mb-8">
                        <div className="text-center mb-6">
                          <motion.button
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setShowAlternatives(!showAlternatives)}
                            className="inline-flex items-center gap-3 px-6 py-3 bg-gray-100 hover:bg-gray-200 rounded-lg transition-all font-medium text-gray-700"
                          >
                            <Eye className="h-5 w-5" />
                            <span>View Alternative Options</span>
                            <div className="bg-primary text-white px-2 py-1 rounded-full text-xs">
                              {generatedImages.length}
                            </div>
                            {showAlternatives ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </motion.button>
                        </div>

                        <AnimatePresence>
                          {showAlternatives && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3, ease: "easeInOut" }}
                              className="overflow-hidden"
                            >

                        {/* Desktop: Enhanced Grid Layout */}
                        <div className="hidden sm:grid sm:grid-cols-2 md:grid-cols-4 gap-6">
                          {generatedImages.map((image, index) => {
                            const imageType = imageTypes[index] || "style_example";
                            const isSelected = selectedImageIndex === index;
                            const isAiGenerated = image.startsWith('data:');

                            return (
                              <motion.div
                                key={`alt-image-${index}`}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                whileHover={{ y: -4 }}
                                className={`cursor-pointer rounded-xl overflow-hidden transition-all duration-300 group ${
                                  isSelected
                                    ? 'ring-4 ring-primary shadow-xl scale-105'
                                    : 'hover:shadow-lg border border-gray-200 hover:border-primary/50'
                                }`}
                                onClick={() => setSelectedImageIndex(index)}
                              >
                                <div className="aspect-[4/3] relative overflow-hidden">
                                  <Image
                                    src={image}
                                    alt={`Alternative design ${index + 1}`}
                                    fill
                                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                                    sizes="(max-width: 768px) 50vw, 25vw"
                                  />

                                  {/* Selection Indicator */}
                                  {isSelected && (
                                    <motion.div
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      className="absolute top-3 right-3 bg-primary text-white rounded-full p-1.5"
                                    >
                                      <CheckCircle className="h-4 w-4" />
                                    </motion.div>
                                  )}

                                  {/* Image Type Badge */}
                                  <div className="absolute top-3 left-3">
                                    <div className={`px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm ${
                                      isAiGenerated
                                        ? 'bg-green-500/90 text-white'
                                        : 'bg-blue-500/90 text-white'
                                    }`}>
                                      {isAiGenerated ? (
                                        <div className="flex items-center gap-1">
                                          <Zap className="h-3 w-3" />
                                          AI
                                        </div>
                                      ) : (
                                        <div className="flex items-center gap-1">
                                          <Badge className="h-3 w-3" />
                                          Example
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Quick Actions on Hover */}
                                  <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <div className="flex items-center gap-1 bg-black/70 backdrop-blur-sm rounded-lg p-1">
                                      <motion.button
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                        className="p-1.5 text-white hover:text-red-400 transition-colors rounded"
                                        title="Save to Favorites"
                                      >
                                        <Heart className="h-3 w-3" />
                                      </motion.button>
                                      <motion.button
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDownload();
                                        }}
                                        className="p-1.5 text-white hover:text-primary transition-colors rounded"
                                        title="Download"
                                      >
                                        <Download className="h-3 w-3" />
                                      </motion.button>
                                    </div>
                                  </div>

                                  {/* Overlay for non-selected images */}
                                  {!isSelected && (
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
                                  )}
                                </div>

                                <div className={`p-4 transition-colors ${
                                  isSelected ? 'bg-primary/5' : 'bg-white group-hover:bg-gray-50'
                                }`}>
                                  <div className="flex items-center justify-between mb-2">
                                    <p className={`text-sm font-medium ${
                                      isSelected ? 'text-primary' : 'text-gray-700'
                                    }`}>
                                      Option {index + 1}
                                    </p>
                                    {isSelected && (
                                      <div className="text-xs text-primary font-medium bg-primary/10 px-2 py-1 rounded-full">
                                        Selected
                                      </div>
                                    )}
                                  </div>

                                  <div className="flex items-center justify-between text-xs text-gray-500">
                                    <span>{isAiGenerated ? "AI Generated" : "Style Example"}</span>
                                    <div className="flex items-center gap-1">
                                      <Star className="h-3 w-3 text-yellow-400" />
                                      <span>4.8</span>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            );
                          })}
                        </div>

                        {/* Mobile: Enhanced Horizontal Scroll */}
                        <div className="sm:hidden">
                          <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 px-4 -mx-4 gap-4">
                            {generatedImages.map((image, index) => {
                              const isSelected = selectedImageIndex === index;
                              const isAiGenerated = image.startsWith('data:');

                              return (
                                <div key={`mobile-alt-${index}`} className="flex-shrink-0 w-56 snap-center">
                                  <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: index * 0.1 }}
                                    className={`cursor-pointer rounded-xl overflow-hidden transition-all duration-300 ${
                                      isSelected
                                        ? 'ring-4 ring-primary shadow-xl'
                                        : 'border border-gray-200 hover:shadow-lg'
                                    }`}
                                    onClick={() => setSelectedImageIndex(index)}
                                  >
                                    <div className="aspect-[4/3] relative">
                                      <Image
                                        src={image}
                                        alt={`Alternative design ${index + 1}`}
                                        fill
                                        className="object-cover"
                                        sizes="224px"
                                      />

                                      {/* Selection Indicator */}
                                      {isSelected && (
                                        <motion.div
                                          initial={{ scale: 0 }}
                                          animate={{ scale: 1 }}
                                          className="absolute top-2 right-2 bg-primary text-white rounded-full p-1"
                                        >
                                          <CheckCircle className="h-3 w-3" />
                                        </motion.div>
                                      )}

                                      {/* Image Type Badge */}
                                      <div className="absolute top-2 left-2">
                                        <div className={`px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm ${
                                          isAiGenerated
                                            ? 'bg-green-500/90 text-white'
                                            : 'bg-blue-500/90 text-white'
                                        }`}>
                                          {isAiGenerated ? 'AI' : 'Example'}
                                        </div>
                                      </div>
                                    </div>

                                    <div className={`p-3 transition-colors ${
                                      isSelected ? 'bg-primary/5' : 'bg-white'
                                    }`}>
                                      <div className="flex items-center justify-between">
                                        <p className={`text-sm font-medium ${
                                          isSelected ? 'text-primary' : 'text-gray-700'
                                        }`}>
                                          Option {index + 1}
                                        </p>
                                        <div className="flex items-center gap-1 text-xs text-gray-500">
                                          <Star className="h-3 w-3 text-yellow-400" />
                                          <span>4.8</span>
                                        </div>
                                      </div>
                                    </div>
                                  </motion.div>
                                </div>
                              );
                            })}
                          </div>
                        </div>

                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    )}


                  </div>

                  {/* Selected Style Info */}
                  {selectedStyle && (
                    <div className="bg-gray-50 p-6 rounded-lg mb-12">
                      <h3 className="text-xl font-bold mb-4">About {availableStyles.find(s => s.id === selectedStyle)?.name} Style</h3>
                      <p className="text-gray-600 mb-4">
                        {availableStyles.find(s => s.id === selectedStyle)?.description}
                      </p>
                      <p className="text-sm text-gray-500">
                        This style is perfect for {serviceName.toLowerCase()} projects that want to convey a sense of
                        {selectedStyle.includes('modern') ? ' contemporary sophistication' :
                         selectedStyle.includes('minimal') ? ' elegant simplicity' :
                         selectedStyle.includes('traditional') ? ' timeless elegance' :
                         ' unique character'}.
                      </p>
                    </div>
                  )}

                  {/* Your Prompt */}
                  <div className="bg-white p-6 rounded-lg shadow-md mb-12">
                    <h3 className="text-lg font-bold mb-2">Your Description</h3>
                    <div className="bg-gray-50 p-4 rounded border border-gray-200">
                      <p className="text-gray-700">{prompt}</p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Sample Request Form */}
              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="mb-16"
                >
                  <SampleRequestForm
                    serviceName={serviceName}
                    prompt={prompt}
                    selectedImage={selectedImage}
                    onGoBack={handleGoBackToResults}
                    onSubmit={handleFormSubmit}
                  />
                </motion.div>
              )}

              {/* Step 5: Success Screen */}
              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="mb-16"
                >
                  <SuccessScreen
                    trackingNumber={trackingNumber}
                    requestType="vision_builder"
                  />
                </motion.div>
              )}
            </AnimatePresence>



            {/* How It Works - Enhanced with better icons and improved content */}
            <div className="mt-20 relative">
              {/* Visual separator */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-1 bg-primary/30"></div>

              {/* Desktop version */}
              <div className="hidden md:block">
                <h2 className="text-2xl font-bold mb-8 text-center">How It Works</h2>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
                  <div className="bg-white p-6 md:p-8 shadow-md hover:shadow-lg transition-all duration-300 border-t-2 border-primary/20">
                    <div className="flex justify-center mb-6">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                        <div className="relative">
                          <span className="absolute -top-1 -right-1 w-5 h-5 bg-primary rounded-full text-white text-xs flex items-center justify-center">1</span>
                          <ListFilter className="h-8 w-8 text-primary" />
                        </div>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-center">Choose a Style</h3>
                    <p className="text-gray-600 text-center text-base">
                      Select your preferred {serviceName.toLowerCase()} style from our dropdown menu to define the aesthetic direction of your project.
                    </p>
                  </div>

                  <div className="bg-white p-6 md:p-8 shadow-md hover:shadow-lg transition-all duration-300 border-t-2 border-primary/20">
                    <div className="flex justify-center mb-6">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                        <div className="relative">
                          <span className="absolute -top-1 -right-1 w-5 h-5 bg-primary rounded-full text-white text-xs flex items-center justify-center">2</span>
                          <MessageSquare className="h-8 w-8 text-primary" />
                        </div>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-center">Describe Your Vision</h3>
                    <p className="text-gray-600 text-center text-base">
                      Tell us what you want in simple terms. Include key details about materials, colors, and specific features.
                    </p>
                  </div>

                  <div className="bg-white p-6 md:p-8 shadow-md hover:shadow-lg transition-all duration-300 border-t-2 border-primary/20">
                    <div className="flex justify-center mb-6">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                        <div className="relative">
                          <span className="absolute -top-1 -right-1 w-5 h-5 bg-primary rounded-full text-white text-xs flex items-center justify-center">3</span>
                          <Sparkles className="h-8 w-8 text-primary" />
                        </div>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-center">See It Come to Life</h3>
                    <p className="text-gray-600 text-center text-base">
                      Our AI instantly generates realistic visualizations of your concept that you can download, share, or use as a starting point.
                    </p>
                  </div>

                  <div className="bg-white p-6 md:p-8 shadow-md hover:shadow-lg transition-all duration-300 border-t-2 border-primary/20">
                    <div className="flex justify-center mb-6">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                        <div className="relative">
                          <span className="absolute -top-1 -right-1 w-5 h-5 bg-primary rounded-full text-white text-xs flex items-center justify-center">4</span>
                          <Share2 className="h-8 w-8 text-primary" />
                        </div>
                      </div>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-center">Submit Your Vision</h3>
                    <p className="text-gray-600 text-center text-base">
                      Share your details and selected design with our team to receive personalized feedback and explore how we can bring your vision to reality.
                    </p>
                  </div>
                </div>
              </div>

              {/* Mobile collapsible version */}
              <div className="md:hidden">
                <CollapsibleSection
                  title="How It Works"
                  defaultOpen={false}
                >
                  <div className="space-y-6">
                    <div className="flex items-start">
                      <div className="mr-4 mt-1">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <div className="relative">
                            <span className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full text-white text-xs flex items-center justify-center">1</span>
                            <ListFilter className="h-5 w-5 text-primary" />
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold mb-1">Choose a Style</h3>
                        <p className="text-gray-600 text-sm">
                          Select your preferred style from our dropdown menu to define your project's aesthetic direction.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="mr-4 mt-1">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <div className="relative">
                            <span className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full text-white text-xs flex items-center justify-center">2</span>
                            <MessageSquare className="h-5 w-5 text-primary" />
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold mb-1">Describe Your Vision</h3>
                        <p className="text-gray-600 text-sm">
                          Tell us what you want in simple terms. Include details about materials, colors, and features.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="mr-4 mt-1">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <div className="relative">
                            <span className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full text-white text-xs flex items-center justify-center">3</span>
                            <Sparkles className="h-5 w-5 text-primary" />
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold mb-1">See It Come to Life</h3>
                        <p className="text-gray-600 text-sm">
                          Our AI instantly generates visualizations that you can download, share, or use as a starting point.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="mr-4 mt-1">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <div className="relative">
                            <span className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full text-white text-xs flex items-center justify-center">4</span>
                            <Share2 className="h-5 w-5 text-primary" />
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold mb-1">Submit Your Vision</h3>
                        <p className="text-gray-600 text-sm">
                          Share your details and selected design with our team to receive personalized feedback on your vision.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Mobile-friendly quick tips */}
                  <div className="mt-6 bg-gray-50 p-4 border-l-4 border-primary">
                    <h4 className="font-bold text-sm mb-2">Quick Tips:</h4>
                    <ul className="text-xs text-gray-600 space-y-1">
                      <li>• Be specific in your description for better results</li>
                      <li>• You can regenerate images if you're not satisfied</li>
                      <li>• Download images to share with your team</li>
                    </ul>
                  </div>
                </CollapsibleSection>
              </div>
            </div>

            {/* Next Steps CTA - Enhanced with better visual separation */}
            {/* Hide this section throughout the entire Vision Builder flow */}
            {false && (
              <div className="mt-20 relative">
                {/* Visual separator */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-1 bg-primary/30"></div>

                {/* Desktop version */}
                <div className="hidden md:block">
                  <div className="bg-primary/10 p-8 border-l-4 border-primary text-center">
                    <h2 className="text-2xl font-bold mb-4">Ready to Make Your Vision a Reality?</h2>
                    <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
                      After visualizing your {serviceName.toLowerCase()} concept, connect with our team of experienced architects who will help bring your vision to life.
                    </p>
                    <Link href="/contact">
                      <Button variant="default" size="lg" className="min-w-[200px] group">
                        Schedule a Consultation
                        <motion.div
                          className="ml-2"
                          initial={{ x: 0 }}
                          whileHover={{ x: 5 }}
                          transition={{ type: "spring", stiffness: 400, damping: 10 }}
                        >
                          <ArrowRight className="h-4 w-4" />
                        </motion.div>
                      </Button>
                    </Link>
                  </div>
                </div>

                {/* Mobile version - always visible but more compact */}
                <div className="md:hidden">
                  <div className="bg-primary/10 p-5 border-l-4 border-primary text-center">
                    <h2 className="text-xl font-bold mb-3">Ready to Make Your Vision a Reality?</h2>
                    <p className="text-gray-700 text-sm mb-4">
                      Connect with our team of experienced architects who will help bring your vision to life.
                    </p>
                    <Link href="/contact">
                      <Button variant="default" className="w-full group">
                        Schedule a Consultation
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {/* FAQ Section - Individual Collapsible Items */}
            <div className="mt-20 relative">
              {/* Visual separator */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-1 bg-primary/30"></div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
              >
                <div className="bg-gray-50 p-6 border-b border-gray-200">
                  <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                    <HelpCircle className="h-6 w-6 text-primary" />
                    Frequently Asked Questions
                  </h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {/* FAQ Item 1 */}
                    <FAQItem
                      question="How accurate are these visualizations?"
                      answer="Our AI visualizations provide a conceptual preview of your ideas. They're excellent for direction and inspiration, while final designs by our architects will be more detailed and precisely tailored to your requirements."
                    />

                    {/* FAQ Item 2 */}
                    <FAQItem
                      question="Can I use these images in my presentations?"
                      answer="Yes! You can download and use these visualizations for personal reference or to share with stakeholders. They're perfect for communicating your vision to others."
                    />

                    {/* FAQ Item 3 */}
                    <FAQItem
                      question="What happens after I generate my vision?"
                      answer="Schedule a consultation with our team, and we'll discuss your project in detail. We'll refine the concept based on your feedback and develop a comprehensive plan to bring your vision to life."
                    />

                    {/* FAQ Item 4 */}
                    <FAQItem
                      question="How can I get the best results?"
                      answer="Be specific in your description. Include details about materials, colors, spatial requirements, and key features. The more detailed your prompt, the better the AI can visualize your concept."
                    />

                    {/* FAQ Item 5 */}
                    <FAQItem
                      question="Can I modify the generated designs?"
                      answer="Yes! Use our refinement controls to adjust colors, materials, lighting, and other aspects. You can also regenerate variations or submit your vision to our architects for professional customization."
                    />

                    {/* FAQ Item 6 */}
                    <FAQItem
                      question="Are the generated images high resolution?"
                      answer="Yes, all generated images are available in high resolution for download. You can also export them as PDFs with project details or share them directly via email."
                    />
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Floating help button - New addition */}
      <div className="fixed bottom-6 right-6 z-50">
        <Link href="/contact">
          <Button
            variant="default"
            size="icon"
            className="h-14 w-14 rounded-full shadow-lg"
            aria-label="Get help"
          >
            <HelpCircle className="h-6 w-6" />
          </Button>
        </Link>
      </div>
    </Layout>
  );
}

