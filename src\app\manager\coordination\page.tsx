"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Users,
  MessageSquare,
  Calendar,
  CheckCircle,
  Clock,
  AlertTriangle,
  Plus,
  Eye,
  Filter,
  Search,
  RefreshCw,
  Target,
  Star,
  TrendingUp,
  FileText
} from "lucide-react";

interface TeamCoordination {
  id: string;
  project_id: string;
  coordination_type: string;
  status: string;
  priority: string;
  description: string;
  participants: string[];
  scheduled_date: string | null;
  completed_date: string | null;
  notes: string | null;
  created_at: string;
  project: {
    title: string;
    status: string;
    client: {
      full_name: string;
    };
    designer: {
      full_name: string;
    };
  };
}

export default function TeamCoordinationPage() {
  const { user, profile } = useOptimizedAuth();
  const [coordinations, setCoordinations] = useState<TeamCoordination[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('active');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchCoordinations();
    }
  }, [user, profile, filter]);

  const fetchCoordinations = async () => {
    try {
      // In a real app, this would fetch actual coordination records
      // For now, we'll create mock data based on manager activities
      const mockCoordinations: TeamCoordination[] = [
        {
          id: '1',
          project_id: 'proj1',
          coordination_type: 'kickoff_meeting',
          status: 'scheduled',
          priority: 'high',
          description: 'Initial project kickoff with all stakeholders',
          participants: ['client', 'designer', 'quality_team'],
          scheduled_date: new Date(Date.now() + 86400000).toISOString(),
          completed_date: null,
          notes: 'Prepare project brief and timeline',
          created_at: new Date().toISOString(),
          project: {
            title: 'Brand Identity Design',
            status: 'in_progress',
            client: { full_name: 'John Smith' },
            designer: { full_name: 'Sarah Johnson' }
          }
        },
        {
          id: '2',
          project_id: 'proj2',
          coordination_type: 'progress_review',
          status: 'active',
          priority: 'normal',
          description: 'Weekly progress review and status update',
          participants: ['designer', 'quality_team'],
          scheduled_date: new Date(Date.now() + 172800000).toISOString(),
          completed_date: null,
          notes: 'Review milestone completion and quality standards',
          created_at: new Date().toISOString(),
          project: {
            title: 'Website Redesign',
            status: 'in_progress',
            client: { full_name: 'Emily Davis' },
            designer: { full_name: 'Mike Wilson' }
          }
        },
        {
          id: '3',
          project_id: 'proj3',
          coordination_type: 'client_feedback',
          status: 'completed',
          priority: 'normal',
          description: 'Collect and coordinate client feedback session',
          participants: ['client'],
          scheduled_date: new Date(Date.now() - 86400000).toISOString(),
          completed_date: new Date(Date.now() - 3600000).toISOString(),
          notes: 'Client satisfied with initial concepts',
          created_at: new Date().toISOString(),
          project: {
            title: 'Logo Design Project',
            status: 'review',
            client: { full_name: 'Lisa Brown' },
            designer: { full_name: 'Alex Chen' }
          }
        },
        {
          id: '4',
          project_id: 'proj4',
          coordination_type: 'quality_escalation',
          status: 'urgent',
          priority: 'urgent',
          description: 'Quality review escalation - requires immediate attention',
          participants: ['designer', 'quality_team', 'admin'],
          scheduled_date: new Date().toISOString(),
          completed_date: null,
          notes: 'Designer reached 3 revision limit, needs manager intervention',
          created_at: new Date().toISOString(),
          project: {
            title: 'Marketing Materials',
            status: 'revision',
            client: { full_name: 'Robert Taylor' },
            designer: { full_name: 'Jessica Lee' }
          }
        }
      ];

      setCoordinations(mockCoordinations);
    } catch (error) {
      console.error('Error fetching coordinations:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCoordination = async (type: string) => {
    // This would open a modal or navigate to a coordination creation form
    window.location.href = `/manager/coordination/new?type=${type}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Calendar className="h-4 w-4 text-blue-500" />;
      case 'active':
        return <Clock className="h-4 w-4 text-orange-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'urgent':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'scheduled':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'active':
        return `${baseClasses} bg-orange-100 text-orange-800 border border-orange-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getCoordinationTypeIcon = (type: string) => {
    switch (type) {
      case 'kickoff_meeting':
        return <Users className="h-4 w-4 text-blue-500" />;
      case 'progress_review':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'client_feedback':
        return <MessageSquare className="h-4 w-4 text-purple-500" />;
      case 'quality_escalation':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'team_sync':
        return <Users className="h-4 w-4 text-orange-500" />;
      default:
        return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  const filteredCoordinations = coordinations.filter(coord => {
    const matchesSearch = coord.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         coord.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' || coord.status === filter;
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Team Coordination</h1>
          <p className="text-gray-600 mt-2">Coordinate team activities and project workflows</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => createCoordination('team_sync')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            New Coordination
          </Button>
          <Button
            onClick={fetchCoordinations}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Coordination Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => createCoordination('kickoff_meeting')}
          >
            <Users className="h-5 w-5" />
            Schedule Kickoff
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => createCoordination('progress_review')}
          >
            <TrendingUp className="h-5 w-5" />
            Progress Review
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => createCoordination('client_feedback')}
          >
            <MessageSquare className="h-5 w-5" />
            Client Feedback
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => createCoordination('team_sync')}
          >
            <Target className="h-5 w-5" />
            Team Sync
          </Button>
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Coordinations</p>
              <p className="text-2xl font-bold text-blue-600">
                {coordinations.filter(c => c.status === 'active' || c.status === 'scheduled').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">
                {coordinations.filter(c => c.status === 'completed').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Urgent Items</p>
              <p className="text-2xl font-bold text-red-600">
                {coordinations.filter(c => c.status === 'urgent' || c.priority === 'urgent').length}
              </p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Week</p>
              <p className="text-2xl font-bold text-purple-600">
                {coordinations.filter(c => {
                  const coordDate = new Date(c.scheduled_date || c.created_at);
                  const now = new Date();
                  const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
                  const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000);
                  return coordDate >= weekStart && coordDate <= weekEnd;
                }).length}
              </p>
            </div>
            <Calendar className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="active">Active</option>
              <option value="scheduled">Scheduled</option>
              <option value="completed">Completed</option>
              <option value="urgent">Urgent</option>
              <option value="all">All Coordinations</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects or descriptions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Coordinations List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Coordination Activities</h2>
          <p className="text-gray-600 mt-1">Manage team coordination and project workflows</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredCoordinations.length === 0 ? (
            <div className="p-8 text-center">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No coordination activities found</p>
              <Button
                onClick={() => createCoordination('team_sync')}
                className="mt-4 flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create First Coordination
              </Button>
            </div>
          ) : (
            filteredCoordinations.map((coordination) => (
              <div key={coordination.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {getCoordinationTypeIcon(coordination.coordination_type)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {coordination.project?.title || 'Team Coordination'}
                      </h3>
                      <span className={getStatusBadge(coordination.status)}>
                        {coordination.status.toUpperCase()}
                      </span>
                      <span className={getPriorityBadge(coordination.priority)}>
                        {coordination.priority.toUpperCase()}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{coordination.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Client:</span> {coordination.project?.client?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Designer:</span> {coordination.project?.designer?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">
                          {coordination.scheduled_date ? 'Scheduled' : 'Created'}:
                        </span>
                        {new Date(coordination.scheduled_date || coordination.created_at).toLocaleDateString()}
                      </div>
                    </div>

                    {coordination.notes && (
                      <div className="bg-blue-50 rounded-lg p-3 mb-3">
                        <p className="text-sm text-blue-800">
                          <span className="font-medium">Notes:</span> {coordination.notes}
                        </p>
                      </div>
                    )}

                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>Type: {coordination.coordination_type.replace('_', ' ')}</span>
                      <span>•</span>
                      <span>Participants: {coordination.participants.length}</span>
                      {coordination.completed_date && (
                        <>
                          <span>•</span>
                          <span>Completed: {new Date(coordination.completed_date).toLocaleDateString()}</span>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/coordination/${coordination.id}`}
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>
                    
                    {coordination.status !== 'completed' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/manager/coordination/${coordination.id}/manage`}
                      >
                        <Target className="h-4 w-4" />
                        Manage
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Guidelines */}
      <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
        <div className="flex items-start gap-3">
          <Target className="h-6 w-6 text-purple-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-purple-900 mb-2">Coordination Best Practices</h3>
            <div className="text-purple-800 space-y-2">
              <p>• <strong>Regular Check-ins:</strong> Schedule weekly progress reviews with team members</p>
              <p>• <strong>Clear Communication:</strong> Ensure all stakeholders understand their roles and deadlines</p>
              <p>• <strong>Proactive Management:</strong> Address issues before they become blockers</p>
              <p>• <strong>Documentation:</strong> Keep detailed notes of all coordination activities</p>
              <p>• <strong>Follow-up:</strong> Ensure action items are completed and tracked</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
