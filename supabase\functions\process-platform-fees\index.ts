import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { transactionId, paymentIntentId } = await req.json()

    if (!transactionId && !paymentIntentId) {
      return new Response(
        JSON.stringify({ error: 'Transaction ID or Payment Intent ID required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get transaction details
    let transaction
    if (transactionId) {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .single()
      
      if (error || !data) {
        return new Response(
          JSON.stringify({ error: 'Transaction not found' }),
          { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
      transaction = data
    } else {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('transaction_id', paymentIntentId)
        .single()
      
      if (error || !data) {
        return new Response(
          JSON.stringify({ error: 'Transaction not found' }),
          { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
      transaction = data
    }

    // Get platform fee settings
    const { data: feeSettings } = await supabase
      .from('platform_fee_settings')
      .select('*')
      .single()

    const platformFeeRate = feeSettings?.platform_commission_rate || 15 // Default 15%
    const processingFeeRate = feeSettings?.payment_processing_fee || 2.9 // Default 2.9%

    // Calculate fees
    const grossAmount = transaction.amount
    const platformFee = (grossAmount * platformFeeRate) / 100
    const processingFee = (grossAmount * processingFeeRate) / 100
    const designerAmount = grossAmount - platformFee - processingFee

    // Create platform revenue transaction
    const { error: platformRevenueError } = await supabase
      .from('transactions')
      .insert({
        transaction_id: `PLT-${Date.now()}-${transaction.id}`,
        amount: platformFee,
        type: 'platform_revenue',
        status: 'completed',
        project_id: transaction.project_id,
        client_id: transaction.client_id,
        designer_id: transaction.designer_id,
        milestone_id: transaction.milestone_id,
        parent_transaction_id: transaction.id,
        platform_fee: platformFee,
        processing_fee: processingFee,
        notes: `Platform fee from transaction ${transaction.transaction_id}`,
        processed_at: new Date().toISOString()
      })

    if (platformRevenueError) {
      console.error('Error creating platform revenue transaction:', platformRevenueError)
      return new Response(
        JSON.stringify({ error: 'Failed to record platform revenue' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create processing fee transaction
    const { error: processingFeeError } = await supabase
      .from('transactions')
      .insert({
        transaction_id: `FEE-${Date.now()}-${transaction.id}`,
        amount: processingFee,
        type: 'processing_fee',
        status: 'completed',
        project_id: transaction.project_id,
        client_id: transaction.client_id,
        designer_id: transaction.designer_id,
        milestone_id: transaction.milestone_id,
        parent_transaction_id: transaction.id,
        platform_fee: platformFee,
        processing_fee: processingFee,
        notes: `Processing fee from transaction ${transaction.transaction_id}`,
        processed_at: new Date().toISOString()
      })

    if (processingFeeError) {
      console.error('Error creating processing fee transaction:', processingFeeError)
    }

    // Update original transaction with fee breakdown
    const { error: updateError } = await supabase
      .from('transactions')
      .update({
        platform_fee: platformFee,
        processing_fee: processingFee,
        designer_amount: designerAmount,
        fee_processed: true,
        fee_processed_at: new Date().toISOString()
      })
      .eq('id', transaction.id)

    if (updateError) {
      console.error('Error updating transaction with fees:', updateError)
      return new Response(
        JSON.stringify({ error: 'Failed to update transaction' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create designer payout queue entry
    const { error: payoutQueueError } = await supabase
      .from('designer_payout_queue')
      .insert({
        designer_id: transaction.designer_id,
        transaction_id: transaction.id,
        milestone_id: transaction.milestone_id,
        amount: designerAmount,
        status: 'pending',
        created_at: new Date().toISOString()
      })

    if (payoutQueueError) {
      console.error('Error adding to payout queue:', payoutQueueError)
    }

    return new Response(
      JSON.stringify({
        success: true,
        grossAmount,
        platformFee,
        processingFee,
        designerAmount,
        feeBreakdown: {
          platformFeeRate: `${platformFeeRate}%`,
          processingFeeRate: `${processingFeeRate}%`,
          designerReceives: `${((designerAmount / grossAmount) * 100).toFixed(1)}%`
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error processing platform fees:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
