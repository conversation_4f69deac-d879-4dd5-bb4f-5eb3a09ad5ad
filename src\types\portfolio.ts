export interface PortfolioProject {
  id: string;
  designer_id: string;
  title: string;
  description: string | null;
  category: string | null;
  client_name: string | null;
  completion_date: string | null;
  featured: boolean;
  created_at: string;
  updated_at: string;
  images?: PortfolioImage[];
  tags?: PortfolioTag[];
  cover_image?: string | null;
}

export interface PortfolioImage {
  id: string;
  project_id: string;
  image_url: string;
  caption: string | null;
  display_order: number;
  is_cover: boolean;
  created_at: string;
}

export interface PortfolioTag {
  id: string;
  name: string;
}

export interface CreatePortfolioProjectParams {
  title: string;
  description?: string;
  category?: string;
  client_name?: string;
  completion_date?: string;
  featured?: boolean;
}

export interface UpdatePortfolioProjectParams {
  title?: string;
  description?: string | null;
  category?: string | null;
  client_name?: string | null;
  completion_date?: string | null;
  featured?: boolean;
}

export interface CreatePortfolioImageParams {
  project_id: string;
  image_url: string;
  caption?: string;
  display_order?: number;
  is_cover?: boolean;
}

export interface UpdatePortfolioImageParams {
  caption?: string | null;
  display_order?: number;
  is_cover?: boolean;
}

export interface DesignerSkill {
  id: string;
  name: string;
  category?: string;
}

export const SKILL_CATEGORIES = [
  'Architecture',
  'Interior Design',
  'Landscape Design',
  'Urban Planning',
  'Visualization',
  'Sustainability',
  'Project Management',
  'Software',
  'Other'
] as const;

export type SkillCategory = typeof SKILL_CATEGORIES[number];

export const PREDEFINED_SKILLS: Record<SkillCategory, string[]> = {
  'Architecture': [
    'Residential Architecture',
    'Commercial Architecture',
    'Institutional Architecture',
    'Industrial Architecture',
    'Hospitality Architecture',
    'Healthcare Architecture',
    'Educational Architecture',
    'Cultural Architecture',
    'Religious Architecture',
    'Mixed-Use Architecture',
    'Adaptive Reuse',
    'Historic Preservation',
    'Parametric Design',
    'Sustainable Architecture',
    'Passive House Design'
  ],
  'Interior Design': [
    'Residential Interiors',
    'Commercial Interiors',
    'Hospitality Interiors',
    'Retail Interiors',
    'Office Interiors',
    'Healthcare Interiors',
    'Educational Interiors',
    'Exhibition Design',
    'Furniture Design',
    'Lighting Design',
    'Color Theory',
    'Material Selection',
    'Space Planning',
    'Universal Design',
    'Biophilic Design'
  ],
  'Landscape Design': [
    'Residential Landscapes',
    'Commercial Landscapes',
    'Urban Landscapes',
    'Parks and Recreation',
    'Garden Design',
    'Planting Design',
    'Hardscape Design',
    'Water Features',
    'Sustainable Landscaping',
    'Xeriscaping',
    'Green Roofs',
    'Vertical Gardens',
    'Ecological Restoration',
    'Stormwater Management',
    'Irrigation Systems'
  ],
  'Urban Planning': [
    'Master Planning',
    'Urban Design',
    'City Planning',
    'Regional Planning',
    'Transit-Oriented Development',
    'Sustainable Urban Planning',
    'Community Development',
    'Land Use Planning',
    'Zoning',
    'Public Space Design',
    'Walkability',
    'Smart Cities',
    'Urban Regeneration',
    'Placemaking',
    'Environmental Planning'
  ],
  'Visualization': [
    '3D Modeling',
    'Rendering',
    'Animation',
    'Virtual Reality',
    'Augmented Reality',
    'Photorealistic Rendering',
    'Conceptual Visualization',
    'Diagramming',
    'Presentation Graphics',
    'Photography',
    'Videography',
    'Sketching',
    'Hand Drawing',
    'Physical Modeling',
    'Immersive Visualization'
  ],
  'Sustainability': [
    'LEED Certification',
    'BREEAM Certification',
    'WELL Certification',
    'Energy Modeling',
    'Carbon Footprint Analysis',
    'Renewable Energy Integration',
    'Water Conservation',
    'Waste Management',
    'Sustainable Materials',
    'Life Cycle Assessment',
    'Net Zero Design',
    'Circular Economy',
    'Resilient Design',
    'Climate Adaptation',
    'Regenerative Design'
  ],
  'Project Management': [
    'Construction Management',
    'Budget Management',
    'Schedule Management',
    'Contract Administration',
    'Quality Control',
    'Risk Management',
    'Stakeholder Management',
    'Team Leadership',
    'Client Relations',
    'Contractor Coordination',
    'Permit Processing',
    'Code Compliance',
    'Value Engineering',
    'Procurement',
    'Post-Occupancy Evaluation'
  ],
  'Software': [
    'AutoCAD',
    'Revit',
    'SketchUp',
    'Rhino',
    'Grasshopper',
    'ArchiCAD',
    '3ds Max',
    'Blender',
    'V-Ray',
    'Lumion',
    'Enscape',
    'Twinmotion',
    'Adobe Photoshop',
    'Adobe Illustrator',
    'Adobe InDesign'
  ],
  'Other': [
    'Building Information Modeling (BIM)',
    'Computational Design',
    'Fabrication',
    'Construction Documentation',
    'Building Codes',
    'Accessibility Standards',
    'Cost Estimation',
    'Specifications Writing',
    'Feasibility Studies',
    'Site Analysis',
    'Programming',
    'Research',
    'Teaching',
    'Writing',
    'Public Speaking'
  ]
};
