import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import Stripe from 'https://esm.sh/stripe@12.0.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2022-11-15',
})

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the signature from the header
    const signature = req.headers.get('stripe-signature')
    if (!signature) {
      return new Response('Missing stripe-signature header', {
        status: 400,
        headers: corsHeaders,
      })
    }

    // Get the webhook secret from the environment
    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')
    if (!webhookSecret) {
      return new Response('Missing STRIPE_WEBHOOK_SECRET', {
        status: 500,
        headers: corsHeaders,
      })
    }

    // Get the request body
    const body = await req.text()

    // Verify the webhook signature
    let event
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      return new Response(`Webhook signature verification failed: ${err.message}`, {
        status: 400,
        headers: corsHeaders,
      })
    }

    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object, supabase)
        break
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object, supabase)
        break
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object, supabase)
        break
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})

async function handlePaymentIntentSucceeded(paymentIntent, supabase) {
  console.log('Payment intent succeeded:', paymentIntent.id)

  // Get the metadata from the payment intent
  const { projectId, milestoneId, clientId, designerId, paymentType } = paymentIntent.metadata

  try {
    // Update the transaction status
    const { error: transactionError } = await supabase
      .from('transactions')
      .update({
        status: 'completed',
        processed_at: new Date().toISOString(),
      })
      .eq('transaction_id', paymentIntent.id)

    if (transactionError) throw transactionError

    // Process platform fees and queue designer payout
    await processPaymentFees(paymentIntent, supabase)

    // Create escrow hold for the payment
    await createEscrowHold(paymentIntent, supabase)

    // If this is a milestone payment, update the milestone status to 'approved' (not 'paid' yet)
    if (milestoneId) {
      const { error: milestoneError } = await supabase
        .from('project_milestones')
        .update({
          status: 'approved', // Changed from 'paid' to 'approved'
          approved_at: new Date().toISOString(),
        })
        .eq('id', milestoneId)

      if (milestoneError) throw milestoneError
    }

    // Create a notification for the client
    const { error: clientNotificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: clientId,
        type: 'payment',
        title: 'Payment Successful',
        content: `Your payment of $${(paymentIntent.amount / 100).toFixed(2)} has been processed successfully.`,
        link: `/client/projects/${projectId}`,
        read: false,
      })

    if (clientNotificationError) throw clientNotificationError

    // Create a notification for the designer if applicable
    if (designerId) {
      const { error: designerNotificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: designerId,
          type: 'payment',
          title: 'Payment Received',
          content: `A payment of $${(paymentIntent.amount / 100).toFixed(2)} has been received for your project.`,
          link: `/designer/projects/${projectId}`,
          read: false,
        })

      if (designerNotificationError) throw designerNotificationError
    }

    // Create a notification for admins
    const { data: admins, error: adminsError } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'admin')

    if (adminsError) throw adminsError

    for (const admin of admins) {
      const { error: adminNotificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: admin.id,
          type: 'payment',
          title: 'New Payment Processed',
          content: `A payment of $${(paymentIntent.amount / 100).toFixed(2)} has been processed for project ${projectId}.`,
          link: `/admin/finance/transactions/${paymentIntent.id}`,
          read: false,
        })

      if (adminNotificationError) throw adminNotificationError
    }
  } catch (error) {
    console.error('Error handling payment intent succeeded:', error)
    throw error
  }
}

async function handlePaymentIntentFailed(paymentIntent, supabase) {
  console.log('Payment intent failed:', paymentIntent.id)

  // Get the metadata from the payment intent
  const { projectId, milestoneId, clientId } = paymentIntent.metadata

  try {
    // Update the transaction status
    const { error: transactionError } = await supabase
      .from('transactions')
      .update({
        status: 'failed',
        processed_at: new Date().toISOString(),
      })
      .eq('transaction_id', paymentIntent.id)

    if (transactionError) throw transactionError

    // Create a notification for the client
    const { error: clientNotificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: clientId,
        type: 'alert',
        title: 'Payment Failed',
        content: `Your payment of $${(paymentIntent.amount / 100).toFixed(2)} has failed. Please try again or contact support.`,
        link: `/client/projects/${projectId}`,
        read: false,
      })

    if (clientNotificationError) throw clientNotificationError

    // Create a notification for admins
    const { data: admins, error: adminsError } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'admin')

    if (adminsError) throw adminsError

    for (const admin of admins) {
      const { error: adminNotificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: admin.id,
          type: 'alert',
          title: 'Payment Failed',
          content: `A payment of $${(paymentIntent.amount / 100).toFixed(2)} has failed for project ${projectId}.`,
          link: `/admin/finance/transactions/${paymentIntent.id}`,
          read: false,
        })

      if (adminNotificationError) throw adminNotificationError
    }
  } catch (error) {
    console.error('Error handling payment intent failed:', error)
    throw error
  }
}

async function handleCheckoutSessionCompleted(session, supabase) {
  console.log('Checkout session completed:', session.id)

  // Get the metadata from the session
  const { projectId, milestoneId, clientId, designerId, paymentType } = session.metadata

  try {
    // Create a transaction record
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        transaction_id: session.payment_intent,
        amount: session.amount_total / 100, // Convert from cents
        status: 'completed',
        type: 'payment',
        project_id: projectId,
        milestone_id: milestoneId,
        client_id: clientId,
        designer_id: designerId,
        processed_at: new Date().toISOString(),
        notes: `${paymentType} payment via Stripe Checkout`,
      })

    if (transactionError) throw transactionError

    // If this is a milestone payment, update the milestone status
    if (milestoneId) {
      const { error: milestoneError } = await supabase
        .from('project_milestones')
        .update({
          status: 'paid',
          paid_at: new Date().toISOString(),
        })
        .eq('id', milestoneId)

      if (milestoneError) throw milestoneError
    }

    // Create notifications (similar to handlePaymentIntentSucceeded)
    // ...
  } catch (error) {
    console.error('Error handling checkout session completed:', error)
    throw error
  }
}

async function processPaymentFees(paymentIntent, supabase) {
  console.log('Processing platform fees for payment:', paymentIntent.id)

  try {
    // Get the transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .select('*')
      .eq('transaction_id', paymentIntent.id)
      .single()

    if (transactionError || !transaction) {
      console.error('Transaction not found for fee processing:', paymentIntent.id)
      return
    }

    // Get platform fee settings
    const { data: feeSettings } = await supabase
      .from('platform_fee_settings')
      .select('*')
      .single()

    const platformFeeRate = feeSettings?.platform_commission_rate || 15 // Default 15%
    const processingFeeRate = feeSettings?.payment_processing_fee || 2.9 // Default 2.9%

    // Calculate fees
    const grossAmount = transaction.amount
    const platformFee = (grossAmount * platformFeeRate) / 100
    const processingFee = (grossAmount * processingFeeRate) / 100
    const designerAmount = grossAmount - platformFee - processingFee

    // Update transaction with fee breakdown
    const { error: updateError } = await supabase
      .from('transactions')
      .update({
        platform_fee: platformFee,
        processing_fee: processingFee,
        designer_amount: designerAmount,
        fee_processed: true,
        fee_processed_at: new Date().toISOString()
      })
      .eq('id', transaction.id)

    if (updateError) {
      console.error('Error updating transaction with fees:', updateError)
      return
    }

    // Create platform revenue record
    const { error: revenueError } = await supabase
      .from('platform_revenue')
      .insert({
        transaction_id: transaction.id,
        revenue_type: 'platform_fee',
        amount: platformFee,
        project_id: transaction.project_id,
        designer_id: transaction.designer_id,
        client_id: transaction.client_id,
        notes: `Platform fee from payment ${paymentIntent.id}`
      })

    if (revenueError) {
      console.error('Error recording platform revenue:', revenueError)
    }

    // Queue designer payout if there's a designer
    if (transaction.designer_id && designerAmount > 0) {
      const { error: queueError } = await supabase
        .from('designer_payout_queue')
        .insert({
          designer_id: transaction.designer_id,
          transaction_id: transaction.id,
          milestone_id: transaction.milestone_id,
          amount: designerAmount
        })

      if (queueError) {
        console.error('Error queuing designer payout:', queueError)
      } else {
        console.log(`Queued payout of $${designerAmount} for designer ${transaction.designer_id}`)
      }
    }

    console.log(`Processed fees: Platform: $${platformFee}, Processing: $${processingFee}, Designer: $${designerAmount}`)

  } catch (error) {
    console.error('Error processing payment fees:', error)
  }
}

async function createEscrowHold(paymentIntent, supabase) {
  try {
    console.log('Creating escrow hold for payment:', paymentIntent.id)

    const { projectId, milestoneId, clientId, designerId } = paymentIntent.metadata

    if (!projectId || !clientId) {
      console.log('Missing required metadata for escrow hold')
      return
    }

    // Calculate fees (same logic as processPaymentFees)
    const grossAmount = paymentIntent.amount / 100 // Convert from cents
    const platformFeeRate = 0.15 // 15%
    const processingFeeRate = 0.029 // 2.9%

    const platformFee = grossAmount * platformFeeRate
    const processingFee = grossAmount * processingFeeRate
    const netAmount = grossAmount - platformFee - processingFee

    // Get or create escrow account
    let escrowAccount
    const { data: existingAccount } = await supabase
      .from('escrow_accounts')
      .select('*')
      .eq('project_id', projectId)
      .eq('client_id', clientId)
      .eq('designer_id', designerId || '')
      .single()

    if (existingAccount) {
      escrowAccount = existingAccount
    } else {
      // Create new escrow account
      const accountNumber = `ESC-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

      const { data: newAccount, error: accountError } = await supabase
        .from('escrow_accounts')
        .insert({
          project_id: projectId,
          client_id: clientId,
          designer_id: designerId || '',
          account_number: accountNumber,
          status: 'active'
        })
        .select()
        .single()

      if (accountError) throw accountError
      escrowAccount = newAccount
    }

    // Get transaction ID
    const { data: transaction } = await supabase
      .from('transactions')
      .select('id')
      .eq('transaction_id', paymentIntent.id)
      .single()

    if (!transaction) {
      console.error('Transaction not found for payment intent:', paymentIntent.id)
      return
    }

    // Create escrow hold
    const autoReleaseDate = new Date()
    autoReleaseDate.setDate(autoReleaseDate.getDate() + 30) // 30 days auto-release

    const { data: hold, error: holdError } = await supabase
      .from('escrow_holds')
      .insert({
        escrow_account_id: escrowAccount.id,
        transaction_id: transaction.id,
        milestone_id: milestoneId || null,
        project_id: projectId,
        gross_amount: grossAmount,
        platform_fee: platformFee,
        processing_fee: processingFee,
        net_amount: netAmount,
        hold_reason: 'milestone_completion',
        status: 'active',
        requires_manager_approval: true,
        requires_quality_approval: false,
        auto_release_date: autoReleaseDate.toISOString()
      })
      .select()
      .single()

    if (holdError) throw holdError

    // Update transaction with escrow status
    await supabase
      .from('transactions')
      .update({ escrow_status: 'held' })
      .eq('id', transaction.id)

    // Update milestone with escrow hold reference
    if (milestoneId) {
      await supabase
        .from('project_milestones')
        .update({ escrow_hold_id: hold.id })
        .eq('id', milestoneId)
    }

    // Update escrow account totals
    await supabase
      .from('escrow_accounts')
      .update({
        total_held: supabase.raw(`total_held + ${netAmount}`)
      })
      .eq('id', escrowAccount.id)

    // Log escrow activity
    await supabase
      .from('escrow_activities')
      .insert({
        escrow_account_id: escrowAccount.id,
        escrow_hold_id: hold.id,
        project_id: projectId,
        activity_type: 'hold_created',
        description: `Funds held in escrow: $${netAmount.toFixed(2)}`,
        performed_by: clientId,
        metadata: {
          payment_intent_id: paymentIntent.id,
          gross_amount: grossAmount,
          net_amount: netAmount,
          hold_reason: 'milestone_completion'
        }
      })

    console.log(`Escrow hold created: $${netAmount.toFixed(2)} for project ${projectId}`)

  } catch (error) {
    console.error('Error creating escrow hold:', error)
  }
}
