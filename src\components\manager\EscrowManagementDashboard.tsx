'use client';

import React, { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Eye,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  TrendingUp,
  Shield,
  FileText,
  Calendar
} from 'lucide-react';

interface EscrowHold {
  id: string;
  project_id: string;
  milestone_id?: string;
  gross_amount: number;
  net_amount: number;
  status: string;
  held_at: string;
  auto_release_date?: string;
  project: {
    title: string;
    status: string;
  };
  milestone?: {
    title: string;
    order_index: number;
  };
  escrow_account: {
    client_id: string;
    designer_id: string;
  };
}

interface EscrowRelease {
  id: string;
  project_id: string;
  release_amount: number;
  release_type: string;
  requested_at: string;
  manager_approval_status: string;
  quality_approval_status: string;
  status: string;
  manager_notes?: string;
  project: {
    title: string;
  };
  milestone?: {
    title: string;
  };
  requester: {
    full_name: string;
  };
}

interface EscrowStats {
  total_projects: number;
  total_held_amount: number;
  pending_approvals: number;
  processed_releases: number;
  disputed_holds: number;
}

interface EscrowManagementDashboardProps {
  managerId?: string;
  compact?: boolean;
}

export default function EscrowManagementDashboard({ 
  managerId, 
  compact = false 
}: EscrowManagementDashboardProps) {
  const { user } = useOptimizedAuth();
  const [holds, setHolds] = useState<EscrowHold[]>([]);
  const [releases, setReleases] = useState<EscrowRelease[]>([]);
  const [stats, setStats] = useState<EscrowStats>({
    total_projects: 0,
    total_held_amount: 0,
    pending_approvals: 0,
    processed_releases: 0,
    disputed_holds: 0
  });
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchEscrowData();
    }
  }, [user, managerId]);

  const fetchEscrowData = async () => {
    try {
      setLoading(true);
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      // Fetch escrow holds
      const holdsResponse = await fetch('/api/escrow/holds', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      let holdsData = { holds: [] };
      if (holdsResponse.ok) {
        holdsData = await holdsResponse.json();
        setHolds(holdsData.holds || []);
      } else {
        console.warn('Failed to fetch escrow holds:', holdsResponse.status);
        setHolds([]);
      }

      // Fetch escrow releases
      const releasesResponse = await fetch('/api/escrow/releases', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      let releasesData = { releases: [] };
      if (releasesResponse.ok) {
        releasesData = await releasesResponse.json();
        setReleases(releasesData.releases || []);
      } else {
        console.warn('Failed to fetch escrow releases:', releasesResponse.status);
        setReleases([]);
      }

      // Calculate stats
      const totalHeld = (holdsData?.holds || [])
        .filter((h: EscrowHold) => h.status === 'active')
        .reduce((sum: number, h: EscrowHold) => sum + h.net_amount, 0);

      const pendingApprovals = (releasesData?.releases || [])
        .filter((r: EscrowRelease) => r.manager_approval_status === 'pending').length;

      const processedReleases = (releasesData?.releases || [])
        .filter((r: EscrowRelease) => r.status === 'processed').length;

      const disputedHolds = (holdsData?.holds || [])
        .filter((h: EscrowHold) => h.status === 'disputed').length;

      setStats({
        total_projects: new Set((holdsData?.holds || []).map((h: EscrowHold) => h.project_id)).size,
        total_held_amount: totalHeld,
        pending_approvals: pendingApprovals,
        processed_releases: processedReleases,
        disputed_holds: disputedHolds
      });

    } catch (error) {
      console.error('Error fetching escrow data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveRelease = async (releaseId: string, notes?: string) => {
    try {
      setProcessing(releaseId);
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch('/api/escrow/releases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'approve_release',
          releaseId,
          approverRole: 'manager',
          notes
        })
      });

      if (response.ok) {
        await fetchEscrowData();
      } else {
        const errorData = await response.json();
        console.error('Error approving release:', errorData.error);
      }
    } catch (error) {
      console.error('Error approving release:', error);
    } finally {
      setProcessing(null);
    }
  };

  const handleRejectRelease = async (releaseId: string, notes?: string) => {
    try {
      setProcessing(releaseId);
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch('/api/escrow/releases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'reject_release',
          releaseId,
          notes
        })
      });

      if (response.ok) {
        await fetchEscrowData();
      } else {
        const errorData = await response.json();
        console.error('Error rejecting release:', errorData.error);
      }
    } catch (error) {
      console.error('Error rejecting release:', error);
    } finally {
      setProcessing(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'pending_release':
        return 'bg-yellow-100 text-yellow-800';
      case 'released':
        return 'bg-green-100 text-green-800';
      case 'disputed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-orange-100 text-orange-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'processed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-600">Loading escrow data...</span>
      </div>
    );
  }

  if (compact) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Shield className="h-5 w-5 text-green-500" />
            Escrow Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(stats.total_held_amount)}
              </div>
              <div className="text-sm text-gray-600">Total Held</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {stats.pending_approvals}
              </div>
              <div className="text-sm text-gray-600">Pending Approvals</div>
            </div>
          </div>
          
          {stats.pending_approvals > 0 && (
            <div className="p-3 bg-orange-50 rounded-md">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium text-orange-800">
                  {stats.pending_approvals} release{stats.pending_approvals > 1 ? 's' : ''} awaiting approval
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Shield className="h-6 w-6 text-green-500" />
          <h2 className="text-xl font-semibold text-gray-900">Escrow Management</h2>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchEscrowData}
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Projects</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_projects}</p>
              </div>
              <TrendingUp className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Held</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(stats.total_held_amount)}
                </p>
              </div>
              <DollarSign className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending Approvals</p>
                <p className="text-2xl font-bold text-orange-600">{stats.pending_approvals}</p>
              </div>
              <Clock className="h-6 w-6 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Processed</p>
                <p className="text-2xl font-bold text-blue-600">{stats.processed_releases}</p>
              </div>
              <CheckCircle className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Disputes</p>
                <p className="text-2xl font-bold text-red-600">{stats.disputed_holds}</p>
              </div>
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="pending" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pending">Pending Approvals ({stats.pending_approvals})</TabsTrigger>
          <TabsTrigger value="holds">Active Holds ({holds.filter(h => h.status === 'active').length})</TabsTrigger>
          <TabsTrigger value="processed">Processed ({stats.processed_releases})</TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          {releases.filter(r => r.manager_approval_status === 'pending').length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle className="h-12 w-12 text-green-300 mx-auto mb-4" />
                <h4 className="font-medium text-gray-900 mb-2">No Pending Approvals</h4>
                <p className="text-sm text-gray-600">All escrow releases are up to date</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {releases
                .filter(r => r.manager_approval_status === 'pending')
                .map((release) => (
                  <Card key={release.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold text-gray-900">
                              {release.project.title}
                            </h4>
                            <Badge className={getStatusColor(release.status)}>
                              {release.status.replace('_', ' ')}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-gray-400" />
                              <span className="font-medium">
                                {formatCurrency(release.release_amount)}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">
                                {formatDate(release.requested_at)}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">
                                {release.release_type.replace('_', ' ')}
                              </span>
                            </div>
                          </div>

                          {release.milestone && (
                            <div className="mt-2 text-sm text-gray-600">
                              Milestone: {release.milestone.title}
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRejectRelease(release.id, 'Rejected by manager')}
                            disabled={processing === release.id}
                            className="flex items-center gap-2"
                          >
                            <ThumbsDown className="h-4 w-4" />
                            Reject
                          </Button>
                          
                          <Button
                            size="sm"
                            onClick={() => handleApproveRelease(release.id, 'Approved by manager')}
                            disabled={processing === release.id}
                            className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                          >
                            {processing === release.id ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <ThumbsUp className="h-4 w-4" />
                            )}
                            Approve
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="holds" className="space-y-4">
          {holds.filter(h => h.status === 'active').length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Shield className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h4 className="font-medium text-gray-900 mb-2">No Active Holds</h4>
                <p className="text-sm text-gray-600">No funds are currently held in escrow</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {holds
                .filter(h => h.status === 'active')
                .map((hold) => (
                  <Card key={hold.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold text-gray-900">
                              {hold.project.title}
                            </h4>
                            <Badge className={getStatusColor(hold.status)}>
                              {hold.status.replace('_', ' ')}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-gray-400" />
                              <span className="font-medium">
                                {formatCurrency(hold.net_amount)}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">
                                Held: {formatDate(hold.held_at)}
                              </span>
                            </div>
                            
                            {hold.auto_release_date && (
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-400" />
                                <span className="text-gray-600">
                                  Auto-release: {formatDate(hold.auto_release_date)}
                                </span>
                              </div>
                            )}
                          </div>

                          {hold.milestone && (
                            <div className="mt-2 text-sm text-gray-600">
                              Milestone: {hold.milestone.title}
                            </div>
                          )}
                        </div>

                        <div className="ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`/manager/projects/${hold.project_id}`, '_blank')}
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Project
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="processed" className="space-y-4">
          {releases.filter(r => r.status === 'processed').length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h4 className="font-medium text-gray-900 mb-2">No Processed Releases</h4>
                <p className="text-sm text-gray-600">Processed releases will appear here</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {releases
                .filter(r => r.status === 'processed')
                .slice(0, 10)
                .map((release) => (
                  <Card key={release.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold text-gray-900">
                              {release.project.title}
                            </h4>
                            <Badge className={getStatusColor(release.status)}>
                              {release.status}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="font-medium">
                              {formatCurrency(release.release_amount)}
                            </span>
                            <span>•</span>
                            <span>{formatDate(release.requested_at)}</span>
                            <span>•</span>
                            <span>{release.release_type.replace('_', ' ')}</span>
                          </div>
                        </div>
                        
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
