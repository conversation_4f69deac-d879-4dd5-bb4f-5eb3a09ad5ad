"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  User,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Star,
  MapPin,
  Calendar,
  Briefcase,
  ExternalLink,
  MessageSquare,
  FileText,
  Download,
  Mail,
  Filter,
  Search,
  MoreHorizontal,
  Send,
  Users,
  AlertTriangle,
  Trash2,
  Edit
} from "lucide-react";

interface DesignerApplication {
  id: string;
  full_name: string;
  email: string;
  phone: string | null;
  location: string | null;
  specialization: string;
  experience: string;
  portfolio_url: string | null;
  bio: string;
  resume_url: string | null;
  portfolio_files: string[] | null;
  certificates: string[] | null;
  application_status: 'pending' | 'under_review' | 'interview_scheduled' | 'approved' | 'rejected' | 'withdrawn' | 'on_hold';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  admin_notes: string | null;
  tags: string[] | null;
  created_at: string;
  status_updated_at: string | null;
  status_updated_by: string | null;
}

interface CommunicationModalProps {
  application: DesignerApplication | null;
  isOpen: boolean;
  onClose: () => void;
  onSent: () => void;
}

const CommunicationModal = ({ application, isOpen, onClose, onSent }: CommunicationModalProps) => {
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [sending, setSending] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);

  useEffect(() => {
    if (isOpen) {
      fetchEmailTemplates();
    }
  }, [isOpen]);

  const fetchEmailTemplates = async () => {
    const { data } = await supabase
      .from('email_templates')
      .select('*')
      .eq('category', 'designer_application')
      .eq('is_active', true);
    
    setTemplates(data || []);
  };

  const handleTemplateSelect = (template: any) => {
    setSubject(template.subject);
    setContent(template.content);
  };

  const handleSend = async () => {
    if (!application || !subject.trim() || !content.trim()) return;

    setSending(true);
    try {
      const response = await fetch(`/api/admin/applications/${application.id}/communicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subject: subject.trim(),
          content: content.trim(),
          communication_type: 'email'
        })
      });

      if (response.ok) {
        onSent();
        onClose();
        setSubject('');
        setContent('');
      }
    } catch (error) {
      console.error('Error sending communication:', error);
    } finally {
      setSending(false);
    }
  };

  if (!isOpen || !application) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Send Message to {application.full_name}</h3>
          <Button variant="ghost" onClick={onClose}>
            <XCircle className="h-5 w-5" />
          </Button>
        </div>

        {/* Email Templates */}
        {templates.length > 0 && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Quick Templates</label>
            <div className="flex flex-wrap gap-2">
              {templates.map((template) => (
                <Button
                  key={template.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleTemplateSelect(template)}
                >
                  {template.name}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Subject */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Subject</label>
          <input
            type="text"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            className="w-full p-2 border rounded-md"
            placeholder="Email subject..."
          />
        </div>

        {/* Content */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Message</label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={8}
            className="w-full p-2 border rounded-md"
            placeholder="Your message..."
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSend} 
            disabled={sending || !subject.trim() || !content.trim()}
          >
            {sending ? 'Sending...' : 'Send Email'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export function EnhancedDesignerApplicationReview() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<DesignerApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedApplication, setSelectedApplication] = useState<DesignerApplication | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'under_review' | 'approved' | 'rejected'>('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [showCommunicationModal, setShowCommunicationModal] = useState(false);
  const [communicationApplication, setCommunicationApplication] = useState<DesignerApplication | null>(null);

  useEffect(() => {
    fetchApplications();
  }, [filter]);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('designer_applications')
        .select('*')
        .order('created_at', { ascending: false });

      if (filter !== 'all') {
        query = query.eq('application_status', filter);
      }

      const { data, error } = await query;

      if (error) throw error;
      setApplications(data || []);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApplicationDecision = async (applicationId: string, decision: 'approved' | 'rejected' | 'under_review' | 'on_hold') => {
    setProcessingId(applicationId);
    
    try {
      if (decision === 'approved') {
        // Use existing approval API
        const response = await fetch(`/api/designer-application/${applicationId}/approve`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ approved_by: user?.id })
        });

        if (!response.ok) throw new Error('Failed to approve application');
      } else if (decision === 'rejected') {
        // Use existing rejection API
        const response = await fetch(`/api/designer-application/${applicationId}/reject`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            rejected_by: user?.id,
            rejection_reason: 'Application did not meet current requirements'
          })
        });

        if (!response.ok) throw new Error('Failed to reject application');
      } else {
        // Update status for other decisions
        const { error } = await supabase
          .from('designer_applications')
          .update({
            application_status: decision,
            status_updated_by: user?.id,
            status_updated_at: new Date().toISOString()
          })
          .eq('id', applicationId);

        if (error) throw error;
      }

      // Refresh applications
      fetchApplications();

      // Close modal if open
      if (selectedApplication?.id === applicationId) {
        setSelectedApplication(null);
      }

    } catch (error) {
      console.error('Error updating application:', error);
      alert('Failed to update application status');
    } finally {
      setProcessingId(null);
    }
  };

  const handleCommunicate = (application: DesignerApplication) => {
    setCommunicationApplication(application);
    setShowCommunicationModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'under_review': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'on_hold': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredApplications = applications.filter(app =>
    app.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.specialization.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Designer Applications</h2>
          <p className="text-gray-600">Review and manage designer applications</p>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex gap-2">
          {['all', 'pending', 'under_review', 'approved', 'rejected'].map((status) => (
            <Button
              key={status}
              variant={filter === status ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(status as any)}
            >
              {status.replace('_', ' ').toUpperCase()}
            </Button>
          ))}
        </div>
        
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search applications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border rounded-md w-64"
            />
          </div>
        </div>
      </div>

      {/* Applications List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Applicant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Specialization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Applied
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredApplications.map((application) => (
                <tr key={application.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-400" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {application.full_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {application.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {application.specialization}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(application.application_status)}`}>
                      {application.application_status.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(application.priority || 'normal')}`}>
                      {application.priority || 'normal'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(application.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() => setSelectedApplication(application)}
                        variant="outline"
                        size="sm"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Review
                      </Button>
                      
                      <Button
                        onClick={() => handleCommunicate(application)}
                        variant="outline"
                        size="sm"
                      >
                        <Mail className="h-4 w-4 mr-1" />
                        Message
                      </Button>
                      
                      {application.application_status === 'pending' && (
                        <>
                          <Button
                            onClick={() => handleApplicationDecision(application.id, 'approved')}
                            disabled={processingId === application.id}
                            className="bg-green-600 hover:bg-green-700 text-white"
                            size="sm"
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Approve
                          </Button>
                          <Button
                            onClick={() => handleApplicationDecision(application.id, 'rejected')}
                            disabled={processingId === application.id}
                            variant="outline"
                            className="text-red-600 border-red-300 hover:bg-red-50"
                            size="sm"
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Reject
                          </Button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Communication Modal */}
      <CommunicationModal
        application={communicationApplication}
        isOpen={showCommunicationModal}
        onClose={() => {
          setShowCommunicationModal(false);
          setCommunicationApplication(null);
        }}
        onSent={() => {
          // Refresh applications or show success message
          console.log('Communication sent successfully');
        }}
      />
    </div>
  );
}
