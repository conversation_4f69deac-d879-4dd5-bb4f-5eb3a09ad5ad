"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  User,
  Mail,
  Calendar,
  MapPin,
  Star,
  ArrowLeft,
  UserPlus,
  Briefcase,
  Award,
  Clock
} from "lucide-react";

interface InviteData {
  id: string;
  inviteCode: string;
  role: 'client' | 'designer';
  createdBy: string;
  createdAt: string;
  expiresAt: string;
  inviter: {
    id: string;
    full_name: string;
    role: string;
    avatar_url?: string;
    email?: string;
    skills?: string[];
    portfolio_url?: string;
    created_at: string;
  };
}

export default function InvitePreviewPage() {
  const params = useParams();
  const router = useRouter();
  const inviteCode = params.inviteCode as string;
  
  const [inviteData, setInviteData] = useState<InviteData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (inviteCode) {
      validateInvite();
    }
  }, [inviteCode]);

  const validateInvite = async () => {
    try {
      const response = await fetch('/api/invitations/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ inviteCode })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Invalid invitation code');
      }

      if (data.valid && data.invitation) {
        setInviteData({
          id: data.invitation.id,
          inviteCode: data.invitation.inviteCode,
          role: data.invitation.role,
          createdBy: data.invitation.createdBy,
          createdAt: data.invitation.createdAt,
          expiresAt: data.invitation.expiresAt,
          inviter: data.invitation.inviter
        });
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = () => {
    router.push(`/auth/signup?invite=${inviteCode}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (error || !inviteData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md mx-auto text-center p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h1 className="text-xl font-bold text-red-800 mb-2">Invalid Invitation</h1>
            <p className="text-red-600 mb-4">{error || 'This invitation link is invalid or has expired.'}</p>
            <Link href="/" className="text-brown-600 hover:text-brown-700 font-medium">
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const { inviter } = inviteData;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center text-gray-600 hover:text-gray-800">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Home
            </Link>
            <h1 className="text-xl font-bold text-gray-900">Profile Preview</h1>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-lg shadow-lg overflow-hidden"
        >
          {/* Profile Header */}
          <div className="bg-gradient-to-r from-brown-600 to-brown-700 px-8 py-12 text-white">
            <div className="flex items-center space-x-6">
              <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                {inviter.avatar_url ? (
                  <img
                    src={inviter.avatar_url}
                    alt={inviter.full_name}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <User className="h-12 w-12 text-white" />
                )}
              </div>
              <div>
                <h2 className="text-3xl font-bold mb-2">{inviter.full_name}</h2>
                <div className="flex items-center space-x-4 text-brown-100">
                  <span className="flex items-center">
                    <Briefcase className="h-4 w-4 mr-1" />
                    {inviter.role === 'designer' ? 'Designer' : 'Client'}
                  </span>
                  <span className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Member since {formatDate(inviter.created_at)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Content */}
          <div className="p-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Left Column */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Invitation Details</h3>
                  <div className="space-y-3">
                    <div className="flex items-center text-gray-600">
                      <UserPlus className="h-5 w-5 mr-3 text-brown-600" />
                      <span>You're invited to join as a <strong>{inviteData.role}</strong></span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Clock className="h-5 w-5 mr-3 text-brown-600" />
                      <span>Expires on {formatDate(inviteData.expiresAt)}</span>
                    </div>
                  </div>
                </div>

                {inviter.role === 'designer' && inviter.skills && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Skills & Expertise</h3>
                    <div className="flex flex-wrap gap-2">
                      {inviter.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-brown-100 text-brown-800 rounded-full text-sm"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                {inviter.portfolio_url && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Portfolio</h3>
                    <a
                      href={inviter.portfolio_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-brown-600 hover:text-brown-700"
                    >
                      <Award className="h-5 w-5 mr-2" />
                      View Portfolio
                    </a>
                  </div>
                )}

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Get Started</h3>
                  <p className="text-gray-600 mb-4">
                    {inviter.role === 'designer' 
                      ? `${inviter.full_name} has invited you to collaborate on design projects. Join now to start working together!`
                      : `${inviter.full_name} has invited you to connect and explore potential design opportunities.`
                    }
                  </p>
                  <button
                    onClick={handleSignUp}
                    className="w-full bg-brown-600 text-white py-3 px-6 rounded-lg hover:bg-brown-700 transition-colors font-medium"
                  >
                    Accept Invitation & Sign Up
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mt-8 bg-white rounded-lg shadow p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">About Senior's Archi-Firm</h3>
          <p className="text-gray-600">
            Join our platform to connect with talented designers and clients. Whether you're looking to 
            bring your architectural vision to life or showcase your design expertise, our platform 
            provides the tools and community to make it happen.
          </p>
        </motion.div>
      </div>
    </div>
  );
}
