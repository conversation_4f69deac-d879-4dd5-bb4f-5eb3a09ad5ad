"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, CheckCircle, AlertCircle, Mail } from 'lucide-react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';

interface PayPalAccountFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function PayPalAccountForm({ onSuccess, onCancel }: PayPalAccountFormProps) {
  const { user } = useOptimizedAuth();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [makeDefault, setMakeDefault] = useState(false);

  // Simplified PayPal account saving (no billing agreement needed)
  const savePayPalAccount = async () => {
    if (!user || !email) return;

    setLoading(true);
    setError(null);

    try {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address');
      }

      // Check if PayPal email already exists for this user
      const { data: existingPayPal, error: checkError } = await supabase
        .from('payment_methods')
        .select('id')
        .eq('user_id', user.id)
        .eq('payment_type', 'paypal')
        .eq('paypal_email', email)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingPayPal) {
        throw new Error('This PayPal account is already added to your payment methods');
      }

      // If making this default, update other payment methods
      if (makeDefault) {
        await supabase
          .from('payment_methods')
          .update({ is_default: false })
          .eq('user_id', user.id);
      }

      // Save PayPal email as payment method
      const { error: insertError } = await supabase
        .from('payment_methods')
        .insert({
          user_id: user.id,
          payment_type: 'paypal',
          paypal_email: email,
          is_default: makeDefault,
          // Set dummy values for required card fields (will be null for PayPal)
          stripe_payment_method_id: `paypal_${Date.now()}`,
          card_brand: 'paypal',
          last_four: '****',
          expiry_date: 'N/A'
        });

      if (insertError) throw insertError;

      setSuccess(true);
      setTimeout(() => {
        onSuccess();
      }, 2000);

    } catch (err) {
      console.error('Error saving PayPal account:', err);
      setError(err instanceof Error ? err.message : 'Failed to save PayPal account');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    savePayPalAccount();
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {success ? (
        <div className="text-center py-8">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            PayPal Account Added
          </h3>
          <p className="text-gray-500 mb-4">
            Your PayPal account has been successfully saved as a payment method.
          </p>
          <p className="text-sm text-gray-400">
            You can now use this PayPal account for quick payments when milestones are approved.
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="text-center mb-6">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
              <Mail className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Add PayPal Account
            </h3>
            <p className="text-sm text-gray-500">
              Save your PayPal email for quick payments when milestones are approved.
            </p>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              PayPal Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brown-500 focus:border-brown-500"
              placeholder="<EMAIL>"
              required
              disabled={loading}
            />
            <p className="mt-1 text-xs text-gray-500">
              This should be the email address associated with your PayPal account.
            </p>
          </div>

          <div className="flex items-center">
            <input
              id="make-default"
              name="make-default"
              type="checkbox"
              checked={makeDefault}
              onChange={(e) => setMakeDefault(e.target.checked)}
              className="h-4 w-4 text-brown-600 border-gray-300 rounded"
              disabled={loading}
            />
            <label
              htmlFor="make-default"
              className="ml-2 block text-sm text-gray-700"
            >
              Make this my default payment method
            </label>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md">
            <p className="text-sm">
              <strong>How it works:</strong> When milestones are approved, you'll receive a payment notification with a quick PayPal payment link. No automatic charges - you stay in control.
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading || !email}
              className="flex items-center bg-brown-600 hover:bg-brown-700 text-white"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save PayPal Account'
              )}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}
