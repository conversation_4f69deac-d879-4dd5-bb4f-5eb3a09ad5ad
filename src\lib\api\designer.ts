/**
 * Get the current availability status for the authenticated designer
 */
export async function getAvailability(token: string): Promise<{ availability: boolean }> {
  const response = await fetch('/api/designer/availability', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bear<PERSON> ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch availability status');
  }
  
  return response.json();
}

/**
 * Update the availability status for the authenticated designer
 */
export async function updateAvailability(token: string, availability: boolean): Promise<{ availability: boolean }> {
  const response = await fetch('/api/designer/availability', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ availability })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update availability status');
  }
  
  return response.json();
}

/**
 * Get the skills for the authenticated designer or a specified designer
 */
export async function getSkills(token: string, designerId?: string): Promise<{ skills: string[], predefined_skills: Record<string, string[]> }> {
  let url = '/api/designer/skills';
  
  if (designerId) {
    url += `?designer_id=${designerId}`;
  }
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch designer skills');
  }
  
  return response.json();
}

/**
 * Update the skills for the authenticated designer
 */
export async function updateSkills(token: string, skills: string[]): Promise<{ skills: string[] }> {
  const response = await fetch('/api/designer/skills', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ skills })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update skills');
  }
  
  return response.json();
}
