"use client";

import { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { PaymentForm } from '@/components/payment/PaymentForm';
import { motion, AnimatePresence } from "framer-motion";
import {
  CheckCircle,
  Clock,
  AlertCircle,
  CreditCard,
  ChevronDown,
  ChevronUp,
  FileText,
  DollarSign,
  Calendar,
  Upload,
  Download,
  Loader2
} from 'lucide-react';
import Link from 'next/link';

interface Milestone {
  id: string;
  title: string;
  description: string;
  amount: number;
  percentage: number;
  due_date: string | null;
  status: string;
  order_index: number;
  completed_at: string | null;
  completed_by: string | null;
  approved_at: string | null;
  approved_by: string | null;
  paid_at: string | null;
  deliverables?: Deliverable[];
  is_expanded?: boolean;
}

interface Deliverable {
  id: string;
  milestone_id: string;
  title: string;
  description: string | null;
  file_url: string | null;
  file_name: string | null;
  uploaded_at: string | null;
  uploaded_by: string | null;
  status: string;
}

interface MilestonesListProps {
  projectId: string;
  designerId?: string;
  userRole: 'client' | 'designer' | 'admin';
  onMilestoneUpdate?: () => void;
}

export default function MilestonesList({
  projectId,
  designerId,
  userRole,
  onMilestoneUpdate
}: MilestonesListProps) {
  const { user } = useOptimizedAuth();
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMilestone, setSelectedMilestone] = useState<Milestone | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [uploadingDeliverable, setUploadingDeliverable] = useState(false);
  const [deliverableFile, setDeliverableFile] = useState<File | null>(null);
  const [deliverableTitle, setDeliverableTitle] = useState('');
  const [deliverableDescription, setDeliverableDescription] = useState('');
  const [uploadMilestoneId, setUploadMilestoneId] = useState<string | null>(null);

  useEffect(() => {
    if (user && projectId) {
      fetchMilestones();
    }
  }, [user, projectId]);

  const fetchMilestones = async () => {
    setLoading(true);
    try {
      // Fetch milestones for the project
      const { data: milestonesData, error: milestonesError } = await supabase
        .from('project_milestones')
        .select(`
          id,
          title,
          description,
          amount,
          percentage,
          due_date,
          status,
          order_index,
          completed_at,
          completed_by,
          approved_at,
          approved_by,
          paid_at
        `)
        .eq('project_id', projectId)
        .order('order_index', { ascending: true });

      if (milestonesError) throw milestonesError;

      // Try to fetch deliverables for each milestone if the table exists
      const milestoneIds = milestonesData.map(m => m.id);

      let deliverablesData: any[] = [];

      try {
        const { data, error } = await supabase
          .from('deliverables')
          .select('*')
          .in('milestone_id', milestoneIds);

        if (!error) {
          deliverablesData = data || [];
        }
      } catch (error) {
        console.log('Deliverables table may not exist yet:', error);
        // Continue without deliverables data
      }

      // Combine milestones with their deliverables (if any)
      const formattedMilestones = milestonesData.map(milestone => {
        const milestoneDeliverables = deliverablesData.filter(d => d.milestone_id === milestone.id);
        return {
          ...milestone,
          deliverables: milestoneDeliverables || [],
          is_expanded: false
        };
      });

      setMilestones(formattedMilestones);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching milestones:', error);
        setError(error.message || 'Failed to load milestones');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const toggleMilestoneExpanded = (milestoneId: string) => {
    setMilestones(milestones.map(milestone =>
      milestone.id === milestoneId
        ? { ...milestone, is_expanded: !milestone.is_expanded }
        : milestone
    ));
  };

  const handlePaymentClick = (milestone: Milestone) => {
    setSelectedMilestone(milestone);
    setShowPaymentForm(true);
  };

  const handlePaymentSuccess = async () => {
    setShowPaymentForm(false);
    await fetchMilestones();
    if (onMilestoneUpdate) {
      onMilestoneUpdate();
    }
  };

  const handlePaymentCancel = () => {
    setShowPaymentForm(false);
  };

  const handleUploadClick = (milestoneId: string) => {
    setUploadMilestoneId(milestoneId);
    setDeliverableFile(null);
    setDeliverableTitle('');
    setDeliverableDescription('');
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setDeliverableFile(e.target.files[0]);
    }
  };

  const handleUploadDeliverable = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!deliverableFile || !uploadMilestoneId || !user) return;

    setUploadingDeliverable(true);

    try {
      // Upload file to storage
      const fileExt = deliverableFile.name.split('.').pop();
      const fileName = `${uploadMilestoneId}_${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `deliverables/${projectId}/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('project-files')
        .upload(filePath, deliverableFile);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('project-files')
        .getPublicUrl(filePath);

      // Create deliverable record
      const { error: deliverableError } = await supabase
        .from('deliverables')
        .insert({
          milestone_id: uploadMilestoneId,
          title: deliverableTitle,
          description: deliverableDescription,
          file_url: publicUrl,
          file_name: deliverableFile.name,
          uploaded_at: new Date().toISOString(),
          uploaded_by: user.id,
          status: 'submitted'
        });

      if (deliverableError) throw deliverableError;

      // If this is the first deliverable, mark milestone as completed
      const milestone = milestones.find(m => m.id === uploadMilestoneId);
      if (milestone && milestone.status === 'active' && !milestone.completed_at) {
        const { error: milestoneError } = await supabase
          .from('project_milestones')
          .update({
            status: 'completed',
            completed_at: new Date().toISOString(),
            completed_by: user.id
          })
          .eq('id', uploadMilestoneId);

        if (milestoneError) throw milestoneError;
      }

      // Refresh milestones
      await fetchMilestones();

      // Reset form
      setUploadMilestoneId(null);
      setDeliverableFile(null);
      setDeliverableTitle('');
      setDeliverableDescription('');

      if (onMilestoneUpdate) {
        onMilestoneUpdate();
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error uploading deliverable:', error);
        setError(error.message || 'Failed to upload deliverable');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setUploadingDeliverable(false);
    }
  };

  const handleApproveMilestone = async (milestoneId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('project_milestones')
        .update({
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: user.id
        })
        .eq('id', milestoneId);

      if (error) throw error;

      // Refresh milestones
      await fetchMilestones();

      if (onMilestoneUpdate) {
        onMilestoneUpdate();
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error approving milestone:', error);
        setError(error.message || 'Failed to approve milestone');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-50 border-amber-200 text-amber-800';
      case 'active':
        return 'bg-brown-50 border-brown-200 text-brown-800';
      case 'completed':
        return 'bg-amber-50 border-amber-200 text-amber-800';
      case 'approved':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'paid':
        return 'bg-brown-50 border-brown-200 text-brown-800';
      case 'revision':
        return 'bg-amber-50 border-amber-200 text-amber-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
      case 'draft':
        return <Clock className="h-5 w-5 text-amber-600" />;
      case 'active':
        return <Clock className="h-5 w-5 text-blue-600" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-amber-600" />;
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'paid':
        return <DollarSign className="h-5 w-5 text-brown-600" />;
      case 'revision':
        return <AlertCircle className="h-5 w-5 text-amber-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  if (loading && milestones.length === 0) {
    return (
      <div className="p-6 flex justify-center items-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <div className="h-8 w-8 border-t-2 border-b-2 border-brown-600"></div>
        </motion.div>
      </div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="p-6 bg-red-50 border border-red-200 flex items-start"
      >
        <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
        <span className="text-red-700">{error}</span>
      </motion.div>
    );
  }

  if (milestones.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="p-6 text-center bg-gray-50 border border-gray-200"
      >
        <FileText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
        <h3 className="text-lg font-medium mb-2">No Milestones</h3>
        <p className="text-gray-500 mb-6">
          This project doesn't have any milestones yet.
        </p>
        {userRole === 'admin' && (
          <Link href={`/admin/projects/${projectId}/milestones/create`}>
            <Button className="bg-brown-600 hover:bg-brown-700 text-white border-0">
              Create Milestone
            </Button>
          </Link>
        )}
      </motion.div>
    );
  }

  return (
    <div>
      {showPaymentForm && selectedMilestone ? (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className="mb-6"
        >
          <PaymentForm
            projectId={projectId}
            milestoneId={selectedMilestone.id}
            amount={selectedMilestone.amount * 100} // Convert to cents for Stripe
            description={`Payment for milestone: ${selectedMilestone.title}`}
            designerId={designerId}
            paymentType={selectedMilestone.order_index === 0 ? 'deposit' : 'milestone'}
            onSuccess={handlePaymentSuccess}
            onCancel={handlePaymentCancel}
          />
        </motion.div>
      ) : (
        <div className="space-y-4">
          {milestones.map((milestone) => (
            <motion.div
              key={milestone.id}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 overflow-hidden"
              >
              <div
                className="p-4 border-b flex justify-between items-center cursor-pointer"
                onClick={() => toggleMilestoneExpanded(milestone.id)}
              >
                <div className="flex items-center">
                  {getStatusIcon(milestone.status)}
                  <div className="ml-3">
                    <h3 className="font-medium">{milestone.title}</h3>
                    <div className="flex items-center mt-1">
                      <span className={`px-2 py-0.5 text-xs font-medium border ${
                        milestone.status === 'completed' ? 'bg-green-50 border-green-200 text-green-800' :
                        milestone.status === 'approved' ? 'bg-green-50 border-green-200 text-green-800' :
                        milestone.status === 'active' ? 'bg-blue-50 border-blue-200 text-blue-800' :
                        milestone.status === 'pending' ? 'bg-amber-50 border-amber-200 text-amber-800' :
                        milestone.status === 'paid' ? 'bg-blue-50 border-blue-200 text-blue-800' :
                        'bg-gray-50 border-gray-200 text-gray-800'
                      }`}>
                        {milestone.status.charAt(0).toUpperCase() + milestone.status.slice(1)}
                      </span>
                      {milestone.due_date && (
                        <span className="ml-2 text-xs text-gray-500 flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Due: {formatDate(milestone.due_date)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="text-right mr-4">
                    <p className="font-bold">{formatCurrency(milestone.amount)}</p>
                    <p className="text-xs text-gray-500">{milestone.percentage}% of total</p>
                  </div>
                  {milestone.is_expanded ? (
                    <ChevronUp className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  )}
                </div>
              </div>

              <AnimatePresence>
                {milestone.is_expanded && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                <div className="p-4">
                  {milestone.description && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Description</h4>
                      <p className="text-gray-800">{milestone.description}</p>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Status</h4>
                      <p className="font-medium">{milestone.status.charAt(0).toUpperCase() + milestone.status.slice(1)}</p>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Amount</h4>
                      <p className="font-medium">{formatCurrency(milestone.amount)}</p>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Due Date</h4>
                      <p className="font-medium">{formatDate(milestone.due_date)}</p>
                    </div>
                  </div>

                  {/* Deliverables Section */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Deliverables</h4>

                    {milestone.deliverables && milestone.deliverables.length > 0 ? (
                      <div className="space-y-2">
                        {milestone.deliverables.map((deliverable) => (
                          <div key={deliverable.id} className="border rounded-md p-3 flex justify-between items-center">
                            <div>
                              <p className="font-medium">{deliverable.title}</p>
                              {deliverable.description && (
                                <p className="text-sm text-gray-500">{deliverable.description}</p>
                              )}
                              <p className="text-xs text-gray-500 mt-1">
                                Uploaded on {formatDate(deliverable.uploaded_at)}
                              </p>
                            </div>
                            {deliverable.file_url && (
                              <a
                                href={deliverable.file_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
                              >
                                <Download className="h-4 w-4 mr-1" />
                                Download
                              </a>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">No deliverables uploaded yet.</p>
                    )}

                    {/* Upload Deliverable Form */}
                    {userRole === 'designer' &&
                     (milestone.status === 'active' || milestone.status === 'completed') &&
                     !milestone.approved_at &&
                     uploadMilestoneId === milestone.id && (
                      <div className="mt-4 border rounded-md p-4">
                        <h4 className="font-medium mb-3">Upload Deliverable</h4>
                        <form onSubmit={handleUploadDeliverable}>
                          <div className="mb-3">
                            <label htmlFor="deliverableTitle" className="block text-sm font-medium text-gray-700 mb-1">
                              Title
                            </label>
                            <input
                              type="text"
                              id="deliverableTitle"
                              value={deliverableTitle}
                              onChange={(e) => setDeliverableTitle(e.target.value)}
                              className="w-full px-3 py-2 border rounded-md"
                              required
                            />
                          </div>

                          <div className="mb-3">
                            <label htmlFor="deliverableDescription" className="block text-sm font-medium text-gray-700 mb-1">
                              Description (optional)
                            </label>
                            <textarea
                              id="deliverableDescription"
                              value={deliverableDescription}
                              onChange={(e) => setDeliverableDescription(e.target.value)}
                              className="w-full px-3 py-2 border rounded-md"
                              rows={3}
                            ></textarea>
                          </div>

                          <div className="mb-4">
                            <label htmlFor="deliverableFile" className="block text-sm font-medium text-gray-700 mb-1">
                              File
                            </label>
                            <input
                              type="file"
                              id="deliverableFile"
                              onChange={handleFileChange}
                              className="w-full"
                              required
                            />
                          </div>

                          <div className="flex justify-end space-x-2">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setUploadMilestoneId(null)}
                            >
                              Cancel
                            </Button>
                            <Button
                              type="submit"
                              disabled={!deliverableFile || uploadingDeliverable}
                              className="flex items-center"
                            >
                              {uploadingDeliverable ? (
                                <>
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                  Uploading...
                                </>
                              ) : (
                                <>
                                  <Upload className="h-4 w-4 mr-2" />
                                  Upload Deliverable
                                </>
                              )}
                            </Button>
                          </div>
                        </form>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end space-x-3">
                    {userRole === 'designer' &&
                     milestone.status === 'active' &&
                     !uploadMilestoneId && (
                      <Button
                        onClick={() => handleUploadClick(milestone.id)}
                        className="flex items-center bg-blue-600 hover:bg-blue-700 text-white border-0"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Deliverable
                      </Button>
                    )}

                    {userRole === 'client' &&
                     milestone.status === 'completed' &&
                     !milestone.approved_at && (
                      <Button
                        onClick={() => handleApproveMilestone(milestone.id)}
                        className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve Deliverables
                      </Button>
                    )}

                    {userRole === 'admin' &&
                     milestone.status === 'completed' &&
                     !milestone.approved_at && (
                      <Button
                        onClick={() => handleApproveMilestone(milestone.id)}
                        className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve Milestone
                      </Button>
                    )}

                    {userRole === 'client' &&
                     milestone.status === 'approved' &&
                     !milestone.paid_at && (
                      <Button
                        onClick={() => handlePaymentClick(milestone)}
                        className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
                      >
                        <CreditCard className="h-4 w-4 mr-2" />
                        Make Payment
                      </Button>
                    )}

                    {userRole === 'admin' && (
                      <Link href={`/admin/projects/${projectId}/milestones/${milestone.id}/edit`}>
                        <Button variant="outline" className="flex items-center border-brown-600 text-brown-600 hover:bg-brown-50">
                          Edit Milestone
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
                </motion.div>
              )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
