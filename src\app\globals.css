@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 35 31% 56%; /* Changed from 336 89% 51% to 35 31% 56% (#B1956B) */
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 35 31% 56%; /* Changed to match primary */
    --radius: 0.5rem;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 35 31% 56%; /* Changed to match light theme */
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 35 31% 56%; /* Changed to match primary */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-montserrat;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Custom thin scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background-color 0.3s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Brown themed scrollbar */
  .scrollbar-thumb-brown-300::-webkit-scrollbar-thumb {
    background: #d2b48c;
  }

  .scrollbar-thumb-brown-400::-webkit-scrollbar-thumb:hover {
    background: #b8860b;
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: #f3f4f6;
  }

  .hover\:scrollbar-thumb-brown-400:hover::-webkit-scrollbar-thumb {
    background: #b8860b;
  }
}

/* Mobile-First Responsive Design System */

/* Typography Scale - Mobile First */
:root {
  /* Mobile Typography (320px - 640px) */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */

  /* Mobile Spacing */
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */

  /* Mobile Touch Targets */
  --touch-target-min: 44px;
  --touch-target-preferred: 48px;
  --touch-spacing: 8px;

  /* Mobile Borders & Radius */
  --border-radius-sm: 0.375rem;  /* 6px */
  --border-radius: 0.5rem;       /* 8px */
  --border-radius-lg: 0.75rem;   /* 12px */

  /* Animation Timing */
  --transition-fast: 150ms;
  --transition-normal: 200ms;
  --transition-slow: 300ms;
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
}

/* Tablet Adjustments (641px - 1024px) */
@media (min-width: 641px) {
  :root {
    --text-xs: 0.75rem;    /* 12px */
    --text-sm: 0.875rem;   /* 14px */
    --text-base: 1rem;     /* 16px */
    --text-lg: 1.25rem;    /* 20px */
    --text-xl: 1.5rem;     /* 24px */
    --text-2xl: 1.75rem;   /* 28px */
    --text-3xl: 2rem;      /* 32px */

    --space-4: 1.25rem;    /* 20px */
    --space-6: 2rem;       /* 32px */
    --space-8: 3rem;       /* 48px */
  }
}

/* Desktop Adjustments (1025px+) */
@media (min-width: 1025px) {
  :root {
    --text-lg: 1.375rem;   /* 22px */
    --text-xl: 1.625rem;   /* 26px */
    --text-2xl: 2rem;      /* 32px */
    --text-3xl: 2.5rem;    /* 40px */

    --space-6: 2.5rem;     /* 40px */
    --space-8: 4rem;       /* 64px */
  }
}

/* Touch-Optimized Interactive Elements */
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

.touch-target-preferred {
  min-height: var(--touch-target-preferred);
  min-width: var(--touch-target-preferred);
}

/* Mobile-Optimized Buttons */
.btn-mobile {
  min-height: var(--touch-target-preferred);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--border-radius);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all var(--transition-normal) var(--ease-out);
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.btn-mobile:active {
  transform: scale(0.98);
}

/* Mobile-Optimized Cards */
.card-mobile {
  background: white;
  border-radius: var(--border-radius-lg);
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal) var(--ease-out);
}

.card-mobile:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Mobile-Optimized Form Elements */
.input-mobile {
  min-height: var(--touch-target-preferred);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--border-radius);
  border: 1px solid #d1d5db;
  font-size: var(--text-base);
  transition: all var(--transition-normal) var(--ease-out);
}

.input-mobile:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Mobile-Specific Utilities */
@media (max-width: 640px) {
  .hide-mobile { display: none !important; }
  .mobile-full-width { width: 100% !important; }
  .mobile-text-center { text-align: center !important; }
  .mobile-p-4 { padding: var(--space-4) !important; }
  .mobile-mb-4 { margin-bottom: var(--space-4) !important; }
  .mobile-space-y-4 > * + * { margin-top: var(--space-4) !important; }
}

/* Desktop-Specific Utilities */
@media (min-width: 1024px) {
  .hide-desktop { display: none !important; }
}

/* Enhanced Brown Color Palette for Admin Sidebar */
:root {
  --brown-25: #fefdfb;
  --brown-50: #fdf8f6;
  --brown-100: #f2e8e5;
  --brown-200: #eaddd7;
  --brown-300: #e0cfc7;
  --brown-400: #d2bab0;
  --brown-500: #b29286;
  --brown-600: #a18072;
  --brown-700: #8b6f47;
  --brown-800: #6f5f3f;
  --brown-900: #5d4e37;
}

/* Enhanced Admin Sidebar Styles */
.admin-sidebar-group {
  background: linear-gradient(135deg, var(--brown-25) 0%, #ffffff 100%);
  border: 1px solid var(--brown-100);
  box-shadow: 0 1px 3px 0 rgba(161, 128, 114, 0.1), 0 1px 2px 0 rgba(161, 128, 114, 0.06);
}

.admin-sidebar-group:hover {
  box-shadow: 0 4px 6px -1px rgba(161, 128, 114, 0.1), 0 2px 4px -1px rgba(161, 128, 114, 0.06);
}

.admin-menu-item-active {
  background: linear-gradient(135deg, var(--brown-600) 0%, var(--brown-700) 100%);
  box-shadow: 0 4px 14px 0 rgba(161, 128, 114, 0.39);
}

.admin-submenu-container {
  background: linear-gradient(135deg, var(--brown-25) 0%, #ffffff 100%);
  border-left: 2px solid var(--brown-200);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .admin-sidebar-group,
  .admin-menu-item-active,
  .admin-submenu-container {
    transition: none !important;
  }
}

/* Enhanced Focus Styles for Admin Sidebar */
.admin-menu-item:focus-visible {
  outline: 2px solid var(--brown-500);
  outline-offset: 2px;
}

/* Touch-friendly improvements for mobile */
@media (max-width: 1024px) {
  .admin-menu-item {
    min-height: 44px; /* Minimum touch target size */
    padding: 12px 16px;
  }

  .admin-submenu-item {
    min-height: 40px;
    padding: 10px 12px;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .card-mobile {
    border-width: 2px;
  }

  .btn-mobile {
    border-width: 2px;
  }
}

