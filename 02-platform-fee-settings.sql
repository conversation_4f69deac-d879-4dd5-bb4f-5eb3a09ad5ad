-- =====================================================
-- SCRIPT 2: CREATE UNIFIED PLATFORM FEE SETTINGS TABLE
-- =====================================================

-- Unified Platform Fee Settings
CREATE TABLE IF NOT EXISTS platform_fee_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  platform_commission_rate DECIMAL(5,2) NOT NULL DEFAULT 15.0,
  payment_processing_fee DECIMAL(5,2) NOT NULL DEFAULT 2.9,
  designer_payout_rate DECIMAL(5,2) NOT NULL DEFAULT 82.1,
  minimum_project_value DECIMAL(10,2) NOT NULL DEFAULT 100.0,
  maximum_commission_cap DECIMAL(10,2),
  minimum_payout_amount DECIMAL(10,2) NOT NULL DEFAULT 50.0,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_percentages CHECK (
    ABS(platform_commission_rate + payment_processing_fee + designer_payout_rate - 100.0) < 0.1
  ),
  CONSTRAINT positive_rates CHECK (
    platform_commission_rate >= 0 AND 
    payment_processing_fee >= 0 AND 
    designer_payout_rate >= 0
  )
);

-- Insert default settings with correct math (15 + 2.9 + 82.1 = 100)
INSERT INTO platform_fee_settings (
  platform_commission_rate, 
  payment_processing_fee, 
  designer_payout_rate
) VALUES (15.0, 2.9, 82.1)
ON CONFLICT DO NOTHING;

-- Verify completion
SELECT 'Script 2 completed: Platform fee settings created' as status;
