"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { PerformanceOptimizer } from "@/components/optimization/PerformanceOptimizer";
import { EnhancedAdminSidebar } from "@/components/admin/EnhancedAdminSidebar";
import { CollapsibleSidebar, useSidebarCollapsed } from "@/components/shared/CollapsibleSidebar";
import { AdminBreadcrumb } from "@/components/admin/AdminBreadcrumb";
import { useRenderTime } from "@/hooks/usePerformanceMonitoring";
import { UnifiedMobileNavigation } from "@/components/mobile/UnifiedMobileNavigation";
import { useMobileNavigation, useResponsiveNavigation } from "@/hooks/useMobileNavigation";

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const { user, profile, isLoading } = useOptimizedAuth();

  // Use unified mobile navigation - disable auto-close since UnifiedMobileNavigation handles it
  const mobileNav = useMobileNavigation({ autoCloseOnRouteChange: false });
  const { isMobile } = useResponsiveNavigation();

  // Track sidebar collapsed state
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Load initial collapsed state
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-collapsed-admin');
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState));
    }
  }, []);

  // Performance monitoring for this layout
  useRenderTime('AdminLayout');

  // Only show loading if session is actually loading (prevents loading screens on tab changes)
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Check authentication and role access
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">Please sign in to access the admin portal.</p>
          <a href="/auth/login" className="text-brown-600 hover:text-brown-700 underline">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  // Only check role if profile loaded successfully
  if (profile && profile.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">You do not have admin privileges.</p>
          <p className="text-sm text-gray-500 mb-4">Your role: {profile.role}</p>
          <a href="/" className="text-brown-600 hover:text-brown-700 underline">
            Go Home
          </a>
        </div>
      </div>
    );
  }

  // If profile is null (failed to load), show a warning but allow access
  if (user && !profile) {
    console.warn('Admin dashboard: User authenticated but profile failed to load');
  }

  return (
    <PerformanceOptimizer
      enablePrefetching={true}
      enableRealtimeSync={true}
      enableDataPersistence={true}
      enablePerformanceMonitoring={true}
    >
      {/* Unified Mobile Navigation */}
      {isMobile && (
        <UnifiedMobileNavigation
          isOpen={mobileNav.isOpen}
          onToggle={mobileNav.toggle}
          onClose={mobileNav.close}
          variant="admin"
        />
      )}

      <div className="min-h-screen bg-gray-50">
        {/* Enhanced Admin Sidebar with Collapsible Functionality - Hidden on Mobile */}
        {!isMobile && (
          <CollapsibleSidebar
            role="admin"
            className="bg-white"
            onCollapseChange={setIsCollapsed}
          >
            <EnhancedAdminSidebar />
          </CollapsibleSidebar>
        )}

        {/* Main Content Area - Responsive to sidebar state */}
        <div className={`min-h-screen ${isMobile ? '' : isCollapsed ? 'lg:pl-16' : 'lg:pl-80'}`}>
          {!isMobile && <AdminBreadcrumb />}
          <main className={`${isMobile ? 'pt-20 p-4' : 'p-6'}`}>
            {children}
          </main>
        </div>
      </div>
    </PerformanceOptimizer>
  );
}
