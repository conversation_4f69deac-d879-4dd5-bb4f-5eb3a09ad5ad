"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { 
  ArrowLeft, 
  Save, 
  AlertCircle, 
  CheckCircle, 
  DollarSign,
  Calendar
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  client_name: string;
  budget: number | null;
};

export default function NewMilestone() {
  const { user } = useAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: '',
    percentage: '',
    due_date: '',
    status: 'pending'
  });

  useEffect(() => {
    if (user && projectId) {
      fetchProjectData();
    }
  }, [user, projectId]);

  const fetchProjectData = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          budget,
          client_id,
          profiles!client_id(full_name)
        `)
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;
      
      setProject({
        id: projectData.id,
        title: projectData.title,
        client_name: projectData.profiles?.full_name || 'Unknown Client',
        budget: projectData.budget
      });
    } catch (error: Error | unknown) {
      console.error('Error fetching project data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'amount' && project?.budget) {
      // Update percentage when amount changes
      const amount = parseFloat(value) || 0;
      const percentage = project.budget ? (amount / project.budget) * 100 : 0;
      
      setFormData({
        ...formData,
        amount: value,
        percentage: percentage.toFixed(2)
      });
    } else if (name === 'percentage' && project?.budget) {
      // Update amount when percentage changes
      const percentage = parseFloat(value) || 0;
      const amount = project.budget ? (percentage / 100) * project.budget : 0;
      
      setFormData({
        ...formData,
        percentage: value,
        amount: amount.toFixed(2)
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!project) return;
    
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Validate form
      if (!formData.title) {
        throw new Error('Milestone title is required');
      }
      
      if (!formData.amount && !formData.percentage) {
        throw new Error('Either amount or percentage must be specified');
      }
      
      // Check if the project_milestones table exists
      const { error: checkError } = await supabase
        .from('project_milestones')
        .select('id')
        .limit(1);
      
      // If the table doesn't exist, create it
      if (checkError && checkError.message.includes('does not exist')) {
        // In a real implementation, you would create the table via a migration
        // For this example, we'll just show a message
        throw new Error('The project_milestones table does not exist. Please create it first.');
      }
      
      // Get the highest order value
      const { data: existingMilestones, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('order')
        .eq('project_id', projectId)
        .order('order', { ascending: false })
        .limit(1);
      
      if (milestonesError && !milestonesError.message.includes('does not exist')) {
        throw milestonesError;
      }
      
      const nextOrder = existingMilestones && existingMilestones.length > 0 
        ? existingMilestones[0].order + 1 
        : 1;
      
      // Insert the new milestone
      const { error: insertError } = await supabase
        .from('project_milestones')
        .insert({
          project_id: projectId,
          title: formData.title,
          description: formData.description || null,
          amount: parseFloat(formData.amount) || 0,
          percentage: parseFloat(formData.percentage) || 0,
          due_date: formData.due_date || null,
          status: formData.status,
          order: nextOrder,
          completed_at: null,
          paid_at: null
        });
      
      if (insertError) throw insertError;
      
      setSuccess('Milestone created successfully');
      
      // Redirect after a short delay
      setTimeout(() => {
        router.push(`/admin/projects/${projectId}/milestones`);
      }, 1500);
    } catch (error: Error | unknown) {
      console.error('Error creating milestone:', error);
      setError(error instanceof Error ? error.message : 'Failed to create milestone');
    } finally {
      setSaving(false);
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'Not set';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading project data...</p>
        </div>
      </div>
    );
  }

  if (error && !project) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            Project not found
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center">
        <Link href={`/admin/projects/${projectId}/milestones`} className="mr-4">
          <Button variant="ghost" className="p-0 h-auto">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Add New Milestone</h1>
          <p className="text-gray-500">{project.title} - {project.client_name}</p>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Project Details</h2>
        </div>
        <div className="p-6">
          <div className="flex items-start">
            <DollarSign className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
            <div>
              <p className="text-sm text-gray-500">Total Budget</p>
              <p className="font-medium">{formatCurrency(project.budget)}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Milestone Details</h2>
        </div>
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Milestone Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border rounded-md"
                placeholder="e.g. Initial Deposit"
              />
            </div>
            
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                className="w-full px-4 py-2 border rounded-md"
                placeholder="Describe what this milestone covers"
              ></textarea>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                  Amount
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">$</span>
                  <input
                    type="number"
                    id="amount"
                    name="amount"
                    value={formData.amount}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-4 py-2 border rounded-md pl-8"
                    placeholder="0.00"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="percentage" className="block text-sm font-medium text-gray-700 mb-1">
                  Percentage of Total Budget
                </label>
                <div className="relative">
                  <input
                    type="number"
                    id="percentage"
                    name="percentage"
                    value={formData.percentage}
                    onChange={handleChange}
                    min="0"
                    max="100"
                    step="0.1"
                    className="w-full px-4 py-2 border rounded-md pr-8"
                    placeholder="0.0"
                  />
                  <span className="absolute right-3 top-2 text-gray-500">%</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="due_date" className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                  <input
                    type="date"
                    id="due_date"
                    name="due_date"
                    value={formData.due_date}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md pl-10"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border rounded-md"
                >
                  <option value="pending">Pending</option>
                  <option value="active">Active</option>
                  <option value="completed">Completed</option>
                  <option value="paid">Paid</option>
                </select>
              </div>
            </div>
          </div>
          
          <div className="mt-8 flex justify-end">
            <Link href={`/admin/projects/${projectId}/milestones`}>
              <Button type="button" variant="outline" className="mr-4">
                Cancel
              </Button>
            </Link>
            <Button 
              type="submit" 
              disabled={saving}
              className="flex items-center"
            >
              {saving ? (
                <span className="flex items-center">
                  <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                  Creating...
                </span>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Milestone
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
