-- Check conversation_participants table data
SELECT 
  COUNT(*) as total_participants,
  COUNT(DISTINCT conversation_id) as conversations_with_participants,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_participants,
  COUNT(CASE WHEN role = 'member' THEN 1 END) as member_participants
FROM conversation_participants;

-- Sample participants
SELECT 
  conversation_id,
  user_id,
  role,
  joined_at,
  is_muted
FROM conversation_participants 
ORDER BY joined_at DESC 
LIMIT 10;
