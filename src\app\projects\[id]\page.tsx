import Image from "next/image";
import Layout from "@/components/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, MapPin, Calendar, Ruler, User } from "lucide-react";
import Link from "next/link";

// This would typically come from your CMS or API
const projectData = {
  id: "1",
  title: "The Glass Pavilion",
  location: "Cape Town, South Africa",
  year: "2022",
  area: "450 sq.m",
  client: "Private Residence",
  architect: "<PERSON>",
  description: "A minimalist glass and steel structure that seamlessly integrates with its stunning coastal surroundings, providing panoramic ocean views while maintaining privacy through strategic landscaping.",
  challenge: "The main challenge was to create a structure that would withstand coastal weather conditions while maintaining the ethereal quality of a glass pavilion.",
  solution: "We employed specialized marine-grade materials and innovative structural solutions to ensure durability without compromising the design's aesthetic integrity.",
  images: [
    {
      url: "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c",
      alt: "Glass Pavilion Exterior",
      caption: "Front view showcasing the seamless integration with landscape"
    },
    {
      url: "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3",
      alt: "Interior Living Space",
      caption: "Open-plan living area with ocean views"
    },
    {
      url: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c",
      alt: "Architectural Detail",
      caption: "Custom steel and glass junction detail"
    }
  ],
  materials: [
    "Low-E Glass",
    "Marine-grade Stainless Steel",
    "Exposed Concrete",
    "Teak Wood"
  ],
  specifications: [
    "Double-glazed facade",
    "Passive cooling system",
    "Automated shading",
    "Rainwater harvesting"
  ]
};

export default function ProjectDetail() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative h-[90vh] w-full">
        <Image
          src={projectData.images[0].url}
          alt={projectData.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-8 md:p-16">
          <div className="container mx-auto">
            <Link href="/projects">
              <Button variant="ghost" className="text-white mb-6">
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Projects
              </Button>
            </Link>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
              {projectData.title}
            </h1>
            <div className="flex flex-wrap gap-4 text-white/80">
              <div className="flex items-center">
                <MapPin className="mr-2 h-4 w-4" />
                {projectData.location}
              </div>
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                {projectData.year}
              </div>
              <div className="flex items-center">
                <Ruler className="mr-2 h-4 w-4" />
                {projectData.area}
              </div>
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4" />
                {projectData.client}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Project Overview */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
            <div>
              <h2 className="text-3xl font-bold mb-6">Overview</h2>
              <p className="text-lg text-gray-600 mb-8">
                {projectData.description}
              </p>
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Challenge</h3>
                  <p className="text-gray-600">{projectData.challenge}</p>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Solution</h3>
                  <p className="text-gray-600">{projectData.solution}</p>
                </div>
              </div>
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-6">Specifications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Materials</h3>
                  <ul className="space-y-2 text-gray-600">
                    {projectData.materials.map((material, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-primary rounded-full mr-3" />
                        {material}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Features</h3>
                  <ul className="space-y-2 text-gray-600">
                    {projectData.specifications.map((spec, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-primary rounded-full mr-3" />
                        {spec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Project Gallery */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Project Gallery</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projectData.images.map((image, index) => (
              <div key={index} className="space-y-4">
                <div className="relative aspect-[4/3] overflow-hidden">
                  <Image
                    src={image.url}
                    alt={image.alt}
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <p className="text-sm text-gray-600">{image.caption}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </Layout>
  );
}