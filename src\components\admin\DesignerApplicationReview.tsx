"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  User,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Star,
  MapPin,
  Calendar,
  Briefcase,
  ExternalLink,
  MessageSquare,
  FileText
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface DesignerApplication {
  id: string;
  full_name: string;
  email: string;
  phone: string | null;
  avatar_url: string | null;
  specialization: string | null;
  skills: string[] | null;
  portfolio_url: string | null;
  application_status: 'pending' | 'approved' | 'rejected';
  application_date: string;
  approved_at: string | null;
  approved_by: string | null;
  created_at: string;
  last_sign_in_at: string | null;
  experience_years?: number;
  location?: string;
  bio?: string;
}

export function DesignerApplicationReview() {
  const { user } = useOptimizedAuth();
  const [applications, setApplications] = useState<DesignerApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedApplication, setSelectedApplication] = useState<DesignerApplication | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');

  useEffect(() => {
    fetchApplications();
  }, [filter]);

  const fetchApplications = async () => {
    try {
      let query = supabase
        .from('profiles')
        .select('*')
        .eq('role', 'designer')
        .order('application_date', { ascending: false });

      if (filter !== 'all') {
        query = query.eq('application_status', filter);
      }

      const { data, error } = await query;

      if (error) throw error;
      setApplications(data || []);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApplicationDecision = async (applicationId: string, decision: 'approved' | 'rejected') => {
    setProcessingId(applicationId);
    
    try {
      const updateData: any = {
        application_status: decision,
        updated_at: new Date().toISOString()
      };

      if (decision === 'approved') {
        updateData.approved_at = new Date().toISOString();
        updateData.approved_by = user?.id;
        updateData.is_active = true;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', applicationId);

      if (error) throw error;

      // Update local state
      setApplications(prev => 
        prev.map(app => 
          app.id === applicationId 
            ? { ...app, ...updateData }
            : app
        )
      );

      // Close modal if open
      if (selectedApplication?.id === applicationId) {
        setSelectedApplication(null);
      }

      console.log(`Application ${decision} successfully`);
    } catch (error) {
      console.error(`Error ${decision} application:`, error);
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Designer Applications</h3>
          <div className="flex items-center space-x-2">
            {/* Filter Buttons */}
            {['all', 'pending', 'approved', 'rejected'].map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption as any)}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${
                  filter === filterOption
                    ? 'bg-brown-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      <div className="p-6">
        {applications.length === 0 ? (
          <div className="text-center py-8">
            <User className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Applications</h4>
            <p className="text-gray-600">No designer applications found for the selected filter.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {applications.map((application) => (
              <motion.div
                key={application.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                      {application.avatar_url ? (
                        <img
                          src={application.avatar_url}
                          alt={application.full_name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <User className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{application.full_name}</h4>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(application.application_status)}`}>
                          {getStatusIcon(application.application_status)}
                          <span className="ml-1 capitalize">{application.application_status}</span>
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{application.email}</p>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        {application.specialization && (
                          <div className="flex items-center">
                            <Briefcase className="h-4 w-4 mr-1" />
                            <span>{application.specialization}</span>
                          </div>
                        )}
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>Applied {formatDate(application.application_date || application.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      onClick={() => setSelectedApplication(application)}
                      variant="outline"
                      size="sm"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Review
                    </Button>
                    
                    {application.application_status === 'pending' && (
                      <>
                        <Button
                          onClick={() => handleApplicationDecision(application.id, 'approved')}
                          disabled={processingId === application.id}
                          className="bg-green-600 hover:bg-green-700 text-white"
                          size="sm"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button
                          onClick={() => handleApplicationDecision(application.id, 'rejected')}
                          disabled={processingId === application.id}
                          variant="outline"
                          className="text-red-600 border-red-300 hover:bg-red-50"
                          size="sm"
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Application Detail Modal */}
      {selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Designer Application Review</h3>
                <button
                  onClick={() => setSelectedApplication(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircle className="h-6 w-6" />
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-6">
              {/* Designer Info */}
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {selectedApplication.avatar_url ? (
                    <img
                      src={selectedApplication.avatar_url}
                      alt={selectedApplication.full_name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <div>
                  <h4 className="text-xl font-semibold">{selectedApplication.full_name}</h4>
                  <p className="text-gray-600">{selectedApplication.email}</p>
                  {selectedApplication.phone && (
                    <p className="text-gray-600">{selectedApplication.phone}</p>
                  )}
                </div>
              </div>

              {/* Skills and Specialization */}
              <div>
                <h5 className="font-medium mb-2">Specialization & Skills</h5>
                <div className="space-y-2">
                  {selectedApplication.specialization && (
                    <p className="text-sm"><strong>Specialization:</strong> {selectedApplication.specialization}</p>
                  )}
                  {selectedApplication.skills && selectedApplication.skills.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-1">Skills:</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedApplication.skills.map((skill, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-brown-100 text-brown-800 text-xs rounded-full"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Portfolio */}
              {selectedApplication.portfolio_url && (
                <div>
                  <h5 className="font-medium mb-2">Portfolio</h5>
                  <a
                    href={selectedApplication.portfolio_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-brown-600 hover:text-brown-700"
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View Portfolio
                  </a>
                </div>
              )}

              {/* Application Status */}
              <div>
                <h5 className="font-medium mb-2">Application Status</h5>
                <div className="space-y-2 text-sm">
                  <p><strong>Status:</strong> 
                    <span className={`ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(selectedApplication.application_status)}`}>
                      {getStatusIcon(selectedApplication.application_status)}
                      <span className="ml-1 capitalize">{selectedApplication.application_status}</span>
                    </span>
                  </p>
                  <p><strong>Applied:</strong> {formatDate(selectedApplication.application_date || selectedApplication.created_at)}</p>
                  {selectedApplication.approved_at && (
                    <p><strong>Approved:</strong> {formatDate(selectedApplication.approved_at)}</p>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              {selectedApplication.application_status === 'pending' && (
                <div className="flex items-center space-x-3 pt-4 border-t">
                  <Button
                    onClick={() => handleApplicationDecision(selectedApplication.id, 'approved')}
                    disabled={processingId === selectedApplication.id}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve Application
                  </Button>
                  <Button
                    onClick={() => handleApplicationDecision(selectedApplication.id, 'rejected')}
                    disabled={processingId === selectedApplication.id}
                    variant="outline"
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Application
                  </Button>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </motion.div>
  );
}
