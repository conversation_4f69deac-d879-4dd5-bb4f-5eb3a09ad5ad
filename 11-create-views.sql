-- =====================================================
-- SCRIPT 11: CREATE VIEWS FOR MANAGER OVERSIGHT
-- =====================================================

-- Manager Projects Overview (All Projects) - Simplified without proposals
CREATE OR REPLACE VIEW manager_projects_overview AS
SELECT
  p.*,
  c.full_name as client_name,
  c.email as client_email,
  d.full_name as designer_name,
  d.email as designer_email,
  d.specialization as designer_specialization,
  COUNT(pm.id) as milestone_count,
  COUNT(CASE WHEN pm.status = 'completed' THEN 1 END) as completed_milestones
FROM projects p
LEFT JOIN profiles c ON p.client_id = c.id
LEFT JOIN profiles d ON p.designer_id = d.id
LEFT JOIN project_milestones pm ON p.id = pm.project_id
GROUP BY p.id, c.full_name, c.email, d.full_name, d.email, d.specialization;

-- Manager Proposals Overview (All Proposals) - Using correct table
CREATE OR REPLACE VIEW manager_proposals_overview AS
SELECT 
  pp.*,
  pb.title as brief_title,
  pb.status as brief_status,
  c.full_name as client_name,
  c.email as client_email,
  d.full_name as designer_name,
  d.email as designer_email,
  d.specialization as designer_specialization
FROM project_proposals_enhanced pp
LEFT JOIN project_briefs pb ON pp.brief_id = pb.id
LEFT JOIN profiles c ON pb.client_id = c.id
LEFT JOIN profiles d ON pp.designer_id = d.id;

-- Verify completion
SELECT 'Script 11 completed: Manager oversight views created' as status;
