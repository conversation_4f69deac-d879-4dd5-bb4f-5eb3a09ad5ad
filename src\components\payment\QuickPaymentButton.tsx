"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, CreditCard, ExternalLink } from 'lucide-react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

interface QuickPaymentButtonProps {
  milestoneId: string;
  projectId: string;
  amount: number; // in dollars
  title: string;
  description?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
}

export function QuickPaymentButton({
  milestoneId,
  projectId,
  amount,
  title,
  description,
  className = '',
  variant = 'default',
  size = 'md'
}: QuickPaymentButtonProps) {
  const { user } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleQuickPayment = async () => {
    if (!user) {
      setError('Please log in to make a payment');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create PayPal payment order
      const response = await fetch('/api/paypal/create-milestone-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          milestoneId,
          projectId,
          clientId: user.id,
          amount: Math.round(amount * 100), // Convert to cents
          description: description || `Payment for milestone: ${title}`
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment');
      }

      const paymentData = await response.json();
      
      if (paymentData.approvalUrl) {
        // Redirect to PayPal for payment
        window.location.href = paymentData.approvalUrl;
      } else {
        throw new Error('No payment URL received');
      }

    } catch (err) {
      console.error('Error creating quick payment:', err);
      setError(err instanceof Error ? err.message : 'Failed to create payment');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className={className}>
      <Button
        onClick={handleQuickPayment}
        disabled={loading}
        variant={variant}
        size={size}
        className="flex items-center"
      >
        {loading ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Creating Payment...
          </>
        ) : (
          <>
            <CreditCard className="h-4 w-4 mr-2" />
            Pay {formatCurrency(amount)}
            <ExternalLink className="h-3 w-3 ml-1" />
          </>
        )}
      </Button>
      
      {error && (
        <p className="text-sm text-red-600 mt-2">{error}</p>
      )}
    </div>
  );
}

interface PaymentNotificationProps {
  milestoneId: string;
  projectId: string;
  amount: number;
  title: string;
  projectTitle: string;
  designerName: string;
  paymentMethod?: string;
}

export function PaymentNotification({
  milestoneId,
  projectId,
  amount,
  title,
  projectTitle,
  designerName,
  paymentMethod = 'PayPal'
}: PaymentNotificationProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <CreditCard className="h-6 w-6 text-blue-600" />
        </div>
        <div className="ml-4 flex-1">
          <h3 className="text-lg font-medium text-blue-900 mb-2">
            Payment Required
          </h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p>
              <strong>Milestone:</strong> {title}
            </p>
            <p>
              <strong>Project:</strong> {projectTitle}
            </p>
            <p>
              <strong>Designer:</strong> {designerName}
            </p>
            <p>
              <strong>Amount:</strong> {formatCurrency(amount)}
            </p>
          </div>
          <div className="mt-4">
            <QuickPaymentButton
              milestoneId={milestoneId}
              projectId={projectId}
              amount={amount}
              title={title}
              description={`Payment for milestone: ${title} in project ${projectTitle}`}
              className="inline-block"
              variant="default"
              size="sm"
            />
          </div>
          <p className="text-xs text-blue-600 mt-3">
            You'll be redirected to {paymentMethod} to complete the payment securely.
          </p>
        </div>
      </div>
    </div>
  );
}

interface MilestonePaymentCardProps {
  milestone: {
    id: string;
    title: string;
    amount: number;
    status: string;
    project_id: string;
    project_title: string;
    designer_name: string;
    approved_at?: string;
  };
  showPayButton?: boolean;
}

export function MilestonePaymentCard({ 
  milestone, 
  showPayButton = true 
}: MilestonePaymentCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="border rounded-lg p-4 bg-white">
      <div className="flex justify-between items-start mb-3">
        <div>
          <h4 className="font-medium text-gray-900">{milestone.title}</h4>
          <p className="text-sm text-gray-500">{milestone.project_title}</p>
          <p className="text-xs text-gray-400">Designer: {milestone.designer_name}</p>
        </div>
        <div className="text-right">
          <p className="font-bold text-lg">{formatCurrency(milestone.amount)}</p>
          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
            Ready for Payment
          </span>
        </div>
      </div>
      
      {milestone.approved_at && (
        <p className="text-xs text-gray-500 mb-3">
          Approved on {formatDate(milestone.approved_at)}
        </p>
      )}
      
      {showPayButton && (
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-600">
            Click to pay with your saved payment method
          </p>
          <QuickPaymentButton
            milestoneId={milestone.id}
            projectId={milestone.project_id}
            amount={milestone.amount}
            title={milestone.title}
            size="sm"
          />
        </div>
      )}
    </div>
  );
}
