import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { NegotiationManager } from '@/lib/negotiation-manager';

/**
 * API endpoint for advanced negotiation management
 * Handles creation, approval, and management of project negotiations
 */

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile and role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const managerId = searchParams.get('managerId');

    // Build query based on user role and parameters
    let query = supabase
      .from('negotiations')
      .select(`
        *,
        project:projects!inner(title, status),
        initiator:profiles!negotiations_initiated_by_fkey(full_name, email),
        manager:profiles!negotiations_manager_id_fkey(full_name, email)
      `);

    // Apply filters based on role
    switch (profile.role) {
      case 'admin':
        // Admins can see all negotiations
        break;
      case 'manager':
        // Managers can see negotiations for their assigned projects
        query = query.eq('manager_id', user.id);
        break;
      case 'client':
        // Clients can see negotiations for their projects
        const { data: clientProjects } = await supabase
          .from('projects')
          .select('id')
          .eq('client_id', user.id);
        
        if (clientProjects && clientProjects.length > 0) {
          query = query.in('project_id', clientProjects.map(p => p.id));
        } else {
          return NextResponse.json({ success: true, negotiations: [] });
        }
        break;
      case 'designer':
        // Designers can see negotiations for their projects
        const { data: designerProjects } = await supabase
          .from('projects')
          .select('id')
          .eq('designer_id', user.id);
        
        if (designerProjects && designerProjects.length > 0) {
          query = query.in('project_id', designerProjects.map(p => p.id));
        } else {
          return NextResponse.json({ success: true, negotiations: [] });
        }
        break;
      default:
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Apply additional filters
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (managerId && profile.role === 'admin') {
      query = query.eq('manager_id', managerId);
    }

    const { data: negotiations, error } = await query
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching negotiations:', error);
      return NextResponse.json({ error: 'Failed to fetch negotiations' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      negotiations: negotiations || []
    });

  } catch (error) {
    console.error('Error in negotiations GET API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'create':
        const {
          projectId,
          negotiationType,
          title,
          description,
          currentTerms,
          proposedTerms,
          priority,
          deadline,
          managerId
        } = body;

        // Validate required fields
        if (!projectId || !negotiationType || !title || !description) {
          return NextResponse.json(
            { error: 'Project ID, negotiation type, title, and description are required' },
            { status: 400 }
          );
        }

        // Create negotiation
        const createResult = await NegotiationManager.createNegotiation({
          projectId,
          initiatedBy: user.id,
          negotiationType,
          title,
          description,
          currentTerms: currentTerms || {},
          proposedTerms: proposedTerms || {},
          priority,
          deadline,
          managerId
        });

        if (!createResult.success) {
          return NextResponse.json(
            { error: createResult.error || 'Failed to create negotiation' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          negotiation: createResult.negotiation,
          message: 'Negotiation created successfully'
        }, { status: 201 });

      case 'approve':
        const { negotiationId, approverRole, notes } = body;

        if (!negotiationId || !approverRole) {
          return NextResponse.json(
            { error: 'Negotiation ID and approver role are required' },
            { status: 400 }
          );
        }

        // Approve negotiation
        const approveResult = await NegotiationManager.approveNegotiation({
          negotiationId,
          approverId: user.id,
          approverRole,
          notes
        });

        if (!approveResult.success) {
          return NextResponse.json(
            { error: approveResult.error || 'Failed to approve negotiation' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          negotiation: approveResult.negotiation,
          message: 'Negotiation approved successfully'
        });

      case 'reject':
        const { negotiationId: rejectId, reason } = body;

        if (!rejectId || !reason) {
          return NextResponse.json(
            { error: 'Negotiation ID and reason are required' },
            { status: 400 }
          );
        }

        // Reject negotiation
        const rejectResult = await NegotiationManager.rejectNegotiation({
          negotiationId: rejectId,
          rejectedBy: user.id,
          reason
        });

        if (!rejectResult.success) {
          return NextResponse.json(
            { error: rejectResult.error || 'Failed to reject negotiation' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'Negotiation rejected successfully'
        });

      case 'add_message':
        const { negotiationId: messageNegotiationId, messageType, content, attachments, termsChanges } = body;

        if (!messageNegotiationId || !messageType || !content) {
          return NextResponse.json(
            { error: 'Negotiation ID, message type, and content are required' },
            { status: 400 }
          );
        }

        // Add message to negotiation
        const messageResult = await NegotiationManager.addNegotiationMessage({
          negotiationId: messageNegotiationId,
          senderId: user.id,
          messageType,
          content,
          attachments,
          termsChanges
        });

        if (!messageResult.success) {
          return NextResponse.json(
            { error: messageResult.error || 'Failed to add message' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          message: messageResult.message,
          message_text: 'Message added successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in negotiations POST API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
