"use client";

import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useDashboardStats, useProjects, useProposals, useProjectBriefs, useConnectedDesigners } from "@/hooks/useDashboardData";
import { supabase } from "@/lib/supabase";
import { useState, useEffect } from "react";

export default function DebugData() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [rawData, setRawData] = useState<any>({});
  
  // Use hooks
  const { data: stats, isLoading: statsLoading, error: statsError } = useDashboardStats(user?.id || '', 'client');
  const { data: projects, isLoading: projectsLoading, error: projectsError } = useProjects(user?.id || '', 'client');
  const { data: proposals, isLoading: proposalsLoading, error: proposalsError } = useProposals(user?.id || '', 'client');
  const { data: projectBriefs, isLoading: briefsLoading, error: briefsError } = useProjectBriefs(user?.id || '', 'client');
  const { data: connectedDesigners, isLoading: designersLoading, error: designersError } = useConnectedDesigners(user?.id || '', 'client');

  useEffect(() => {
    if (user) {
      fetchRawData();
    }
  }, [user]);

  const fetchRawData = async () => {
    if (!user) return;

    try {
      // Check connections table
      const { data: connections, error: connectionsError } = await supabase
        .from('connections')
        .select('*')
        .eq('client_id', user.id);

      // Check project_briefs table
      const { data: briefs, error: briefsError } = await supabase
        .from('project_briefs')
        .select('*')
        .eq('client_id', user.id);

      // Check proposals table
      const { data: proposalsData, error: proposalsError } = await supabase
        .from('proposals')
        .select('*');

      // Check projects table
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .eq('client_id', user.id);

      setRawData({
        connections: { data: connections, error: connectionsError },
        briefs: { data: briefs, error: briefsError },
        proposals: { data: proposalsData, error: proposalsError },
        projects: { data: projectsData, error: projectsError },
        userId: user.id,
        userRole: profile?.role
      });
    } catch (error) {
      console.error('Error fetching raw data:', error);
    }
  };

  if (authLoading) {
    return <div>Loading auth...</div>;
  }

  if (!user) {
    return <div>No user</div>;
  }

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">Data Debug Page</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Hook Results */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Hook Results</h2>
          
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-semibold">Stats Hook</h3>
            <p>Loading: {statsLoading.toString()}</p>
            <p>Error: {statsError?.message || 'None'}</p>
            <pre className="text-xs">{JSON.stringify(stats, null, 2)}</pre>
          </div>

          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-semibold">Projects Hook</h3>
            <p>Loading: {projectsLoading.toString()}</p>
            <p>Error: {projectsError?.message || 'None'}</p>
            <p>Count: {projects?.length || 0}</p>
          </div>

          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-semibold">Proposals Hook</h3>
            <p>Loading: {proposalsLoading.toString()}</p>
            <p>Error: {proposalsError?.message || 'None'}</p>
            <p>Count: {proposals?.length || 0}</p>
          </div>

          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-semibold">Project Briefs Hook</h3>
            <p>Loading: {briefsLoading.toString()}</p>
            <p>Error: {briefsError?.message || 'None'}</p>
            <p>Count: {projectBriefs?.length || 0}</p>
          </div>

          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-semibold">Connected Designers Hook</h3>
            <p>Loading: {designersLoading.toString()}</p>
            <p>Error: {designersError?.message || 'None'}</p>
            <p>Count: {connectedDesigners?.length || 0}</p>
          </div>
        </div>

        {/* Raw Database Data */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Raw Database Data</h2>
          
          <div className="bg-blue-100 p-4 rounded">
            <h3 className="font-semibold">User Info</h3>
            <p>User ID: {rawData.userId}</p>
            <p>Role: {rawData.userRole}</p>
          </div>

          <div className="bg-blue-100 p-4 rounded">
            <h3 className="font-semibold">Connections Table</h3>
            <p>Error: {rawData.connections?.error?.message || 'None'}</p>
            <p>Count: {rawData.connections?.data?.length || 0}</p>
            <pre className="text-xs">{JSON.stringify(rawData.connections?.data, null, 2)}</pre>
          </div>

          <div className="bg-blue-100 p-4 rounded">
            <h3 className="font-semibold">Project Briefs Table</h3>
            <p>Error: {rawData.briefs?.error?.message || 'None'}</p>
            <p>Count: {rawData.briefs?.data?.length || 0}</p>
            <pre className="text-xs">{JSON.stringify(rawData.briefs?.data, null, 2)}</pre>
          </div>

          <div className="bg-blue-100 p-4 rounded">
            <h3 className="font-semibold">Proposals Table</h3>
            <p>Error: {rawData.proposals?.error?.message || 'None'}</p>
            <p>Count: {rawData.proposals?.data?.length || 0}</p>
            <pre className="text-xs">{JSON.stringify(rawData.proposals?.data?.slice(0, 3), null, 2)}</pre>
          </div>

          <div className="bg-blue-100 p-4 rounded">
            <h3 className="font-semibold">Projects Table</h3>
            <p>Error: {rawData.projects?.error?.message || 'None'}</p>
            <p>Count: {rawData.projects?.data?.length || 0}</p>
            <pre className="text-xs">{JSON.stringify(rawData.projects?.data, null, 2)}</pre>
          </div>
        </div>
      </div>
    </div>
  );
}
