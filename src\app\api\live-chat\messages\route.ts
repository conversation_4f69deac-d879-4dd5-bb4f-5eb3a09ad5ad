import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');
    const limit = parseInt(searchParams.get('limit') || '100');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabaseServerClient
      .from('live_chat_messages')
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching chat messages:', error);
      return NextResponse.json(
        { error: 'Failed to fetch chat messages' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
      count: data?.length || 0
    });

  } catch (error) {
    console.error('Error in chat messages GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const {
      session_id,
      sender_type,
      sender_id,
      content,
      message_type = 'text'
    } = await request.json();

    if (!session_id || !sender_type || !content) {
      return NextResponse.json(
        { error: 'Session ID, sender type, and content are required' },
        { status: 400 }
      );
    }

    // Verify session exists
    const { data: session, error: sessionError } = await supabaseServerClient
      .from('live_chat_sessions')
      .select('id, status')
      .eq('id', session_id)
      .single();

    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Create message
    const messageData = {
      session_id,
      sender_type,
      sender_id: sender_id || null,
      content: content.trim(),
      message_type
    };

    const { data: message, error } = await supabaseServerClient
      .from('live_chat_messages')
      .insert([messageData])
      .select()
      .single();

    if (error) {
      console.error('Error creating chat message:', error);
      return NextResponse.json(
        { error: 'Failed to create chat message' },
        { status: 500 }
      );
    }

    // Update session status if needed
    if (sender_type === 'admin' && session.status === 'waiting') {
      await supabaseServerClient
        .from('live_chat_sessions')
        .update({
          status: 'active',
          admin_joined_at: new Date().toISOString()
        })
        .eq('id', session_id);
    }

    // If visitor sends message and admin is offline, send notification
    if (sender_type === 'visitor') {
      const { data: adminStatus } = await supabaseServerClient
        .from('admin_chat_status')
        .select('is_online, last_activity')
        .single();

      const isAdminOnline = adminStatus?.is_online && 
        new Date(adminStatus.last_activity) > new Date(Date.now() - 5 * 60 * 1000);

      if (!isAdminOnline) {
        // Get session details for notification
        const { data: sessionDetails } = await supabaseServerClient
          .from('live_chat_sessions')
          .select(`
            *,
            profiles:user_id (
              full_name,
              email
            )
          `)
          .eq('id', session_id)
          .single();

        if (sessionDetails) {
          const visitorInfo = {
            name: sessionDetails.profiles?.full_name || sessionDetails.visitor_name || 'Anonymous',
            email: sessionDetails.profiles?.email || sessionDetails.visitor_email || 'No email'
          };

          // Send notification (this would typically be done via a queue in production)
          try {
            await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/live-chat/notify-admin`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                sessionId: session_id,
                type: 'offline_message',
                message: content,
                visitorInfo
              })
            });
          } catch (notificationError) {
            console.error('Error sending notification:', notificationError);
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: message
    });

  } catch (error) {
    console.error('Error in chat messages POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { message_ids, is_read = true } = await request.json();

    if (!message_ids || !Array.isArray(message_ids)) {
      return NextResponse.json(
        { error: 'Message IDs array is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabaseServerClient
      .from('live_chat_messages')
      .update({
        is_read,
        read_at: is_read ? new Date().toISOString() : null
      })
      .in('id', message_ids)
      .select();

    if (error) {
      console.error('Error updating message read status:', error);
      return NextResponse.json(
        { error: 'Failed to update message read status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data,
      updated_count: data?.length || 0
    });

  } catch (error) {
    console.error('Error in chat messages PUT API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
