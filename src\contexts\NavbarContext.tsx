"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface NavbarContextType {
  navbarVisible: boolean;
  setNavbarVisible: (visible: boolean) => void;
}

const NavbarContext = createContext<NavbarContextType>({
  navbarVisible: true,
  setNavbarVisible: () => {},
});

export const useNavbarContext = () => useContext(NavbarContext);

export const NavbarProvider = ({ children }: { children: ReactNode }) => {
  const [navbarVisible, setNavbarVisible] = useState(true);

  return (
    <NavbarContext.Provider value={{ navbarVisible, setNavbarVisible }}>
      {children}
    </NavbarContext.Provider>
  );
};