# Automatic Payout Scheduler Setup

This document explains how to set up automatic payout processing using cron jobs or scheduled functions.

## Option 1: Supabase Edge Functions with pg_cron

### 1. Enable pg_cron Extension

```sql
-- Enable pg_cron extension in your Supabase database
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Grant usage to your service role
GRANT USAGE ON SCHEMA cron TO service_role;
```

### 2. Create Scheduled Job

```sql
-- Schedule automatic payouts to run every Monday at 9:00 AM UTC
SELECT cron.schedule(
    'automatic-payouts',
    '0 9 * * 1',  -- Every Monday at 9:00 AM
    $$
    SELECT net.http_post(
        url := 'https://your-project.supabase.co/functions/v1/payout-scheduler',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);
```

### 3. Alternative: Daily Check

```sql
-- Schedule daily checks at 10:00 AM UTC
SELECT cron.schedule(
    'daily-payout-check',
    '0 10 * * *',  -- Every day at 10:00 AM
    $$
    SELECT net.http_post(
        url := 'https://your-project.supabase.co/functions/v1/payout-scheduler',
        headers := '{"Content-Type": "application/json", "Authorization": "Bearer YOUR_SERVICE_ROLE_KEY"}'::jsonb,
        body := '{}'::jsonb
    );
    $$
);
```

## Option 2: External Cron Service (Recommended for Production)

### 1. Using GitHub Actions

Create `.github/workflows/payout-scheduler.yml`:

```yaml
name: Automatic Payout Scheduler

on:
  schedule:
    # Run every Monday at 9:00 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch: # Allow manual triggering

jobs:
  process-payouts:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Payout Processing
        run: |
          curl -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" \
            "${{ secrets.SUPABASE_URL }}/functions/v1/payout-scheduler"
```

### 2. Using Vercel Cron Jobs

Create `api/cron/payouts.js`:

```javascript
export default async function handler(req, res) {
  // Verify the request is from Vercel Cron
  if (req.headers.authorization !== `Bearer ${process.env.CRON_SECRET}`) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/payout-scheduler`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      }
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || 'Payout processing failed');
    }

    res.status(200).json({ 
      success: true, 
      message: 'Payouts processed successfully',
      data 
    });
  } catch (error) {
    console.error('Payout cron error:', error);
    res.status(500).json({ 
      error: 'Failed to process payouts',
      message: error.message 
    });
  }
}
```

Add to `vercel.json`:

```json
{
  "crons": [
    {
      "path": "/api/cron/payouts",
      "schedule": "0 9 * * 1"
    }
  ]
}
```

### 3. Using AWS Lambda + EventBridge

Create `lambda/payout-scheduler.js`:

```javascript
const https = require('https');

exports.handler = async (event) => {
    const postData = JSON.stringify({});
    
    const options = {
        hostname: 'your-project.supabase.co',
        port: 443,
        path: '/functions/v1/payout-scheduler',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: 200,
                    body: JSON.stringify({
                        message: 'Payout processing triggered',
                        response: JSON.parse(data)
                    })
                });
            });
        });

        req.on('error', (error) => {
            reject({
                statusCode: 500,
                body: JSON.stringify({
                    error: 'Failed to trigger payout processing',
                    message: error.message
                })
            });
        });

        req.write(postData);
        req.end();
    });
};
```

## Option 3: Node.js Cron Job

### 1. Install Dependencies

```bash
npm install node-cron @supabase/supabase-js
```

### 2. Create Scheduler Script

Create `scripts/payout-scheduler.js`:

```javascript
const cron = require('node-cron');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Schedule automatic payouts every Monday at 9:00 AM
cron.schedule('0 9 * * 1', async () => {
  console.log('Starting automatic payout processing...');
  
  try {
    const { data, error } = await supabase.functions.invoke('payout-scheduler');
    
    if (error) {
      console.error('Payout processing failed:', error);
      return;
    }
    
    console.log('Payout processing completed:', data);
  } catch (error) {
    console.error('Error in payout scheduler:', error);
  }
}, {
  scheduled: true,
  timezone: "UTC"
});

console.log('Payout scheduler started. Waiting for scheduled runs...');
```

### 3. Run as Service

Create `ecosystem.config.js` for PM2:

```javascript
module.exports = {
  apps: [{
    name: 'payout-scheduler',
    script: './scripts/payout-scheduler.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      SUPABASE_URL: 'your-supabase-url',
      SUPABASE_SERVICE_ROLE_KEY: 'your-service-role-key'
    }
  }]
};
```

Start with PM2:
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Environment Variables Required

```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
STRIPE_SECRET_KEY=sk_live_...
FRONTEND_URL=https://your-domain.com
```

## Monitoring and Alerts

### 1. Add Logging to Payout Function

```typescript
// In your payout-scheduler function
console.log(`Payout scheduler started at ${new Date().toISOString()}`);
console.log(`Found ${readyDesigners.length} designers ready for payout`);
console.log(`Processed ${totalProcessed} successful payouts, ${failedPayouts} failed`);
```

### 2. Set Up Alerts

```sql
-- Create a function to send alerts for failed payouts
CREATE OR REPLACE FUNCTION send_payout_alert(
  alert_type TEXT,
  message TEXT
)
RETURNS VOID AS $$
BEGIN
  -- Insert notification for admins
  INSERT INTO notifications (
    user_id,
    type,
    title,
    content,
    read
  )
  SELECT 
    id,
    'admin_alert',
    'Payout System Alert',
    message,
    false
  FROM profiles 
  WHERE role = 'admin';
END;
$$ LANGUAGE plpgsql;
```

## Testing the Scheduler

### 1. Manual Test

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
  "https://your-project.supabase.co/functions/v1/payout-scheduler"
```

### 2. Test with Specific Designer

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
  -d '{"designerId": "designer-uuid", "force": true}' \
  "https://your-project.supabase.co/functions/v1/process-automatic-payouts"
```

## Troubleshooting

### Common Issues:

1. **Cron job not running**: Check timezone settings and cron syntax
2. **Authentication errors**: Verify service role key permissions
3. **Stripe errors**: Check Stripe Connect account status
4. **Database errors**: Verify RLS policies and permissions

### Debug Commands:

```sql
-- Check scheduled jobs (pg_cron)
SELECT * FROM cron.job;

-- Check job run history
SELECT * FROM cron.job_run_details ORDER BY start_time DESC LIMIT 10;

-- Check recent payout transactions
SELECT * FROM transactions WHERE type = 'payout' ORDER BY created_at DESC LIMIT 10;
```

## Security Considerations

1. **Use service role key** for cron jobs, not anon key
2. **Restrict function access** to authenticated requests only
3. **Validate webhook signatures** if using external services
4. **Monitor for unusual activity** in payout processing
5. **Set up rate limiting** to prevent abuse

## Recommended Schedule

- **Daily**: Check for eligible payouts (10:00 AM UTC)
- **Weekly**: Process weekly payouts (Monday 9:00 AM UTC)
- **Monthly**: Process monthly payouts (1st of month, 9:00 AM UTC)

Choose the schedule that matches your platform settings and business requirements.
