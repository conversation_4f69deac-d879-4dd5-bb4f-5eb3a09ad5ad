"use client";

import { motion } from "framer-motion";
import <PERSON> from "next/link";
import { Button } from "../ui/button";

const values = [
  {
    title: "Creativity",
    description: "We embrace originality, crafting unique and inspiring architectural designs."
  },
  {
    title: "Precision",
    description: "Our focus on detail ensures exceptional quality in every project."
  },
  {
    title: "Integrity",
    description: "We uphold the highest ethical standards in all our work."
  },
  {
    title: "Innovation",
    description: "We strive to push the boundaries of architecture with forward-thinking solutions."
  },
  {
    title: "Client-Centricity",
    description: "Our clients' needs and aspirations are at the heart of everything we do."
  }
];

const AboutUsSection = () => {
  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
          {/* Left Column - About Us Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">About Us</h2>
            <div className="h-1 w-20 bg-primary mb-8"></div>
            <p className="text-lg text-gray-600 mb-8">
              Welcome to Senior's Archi-Firm, where we bring creative visions to life with precision, integrity, and innovation.
            </p>
            <p className="text-gray-600 mb-8">
              We create spaces that are more than just buildings—each design bridges creativity and strategy, art and purpose. Our work speaks to human needs, shaping environments that inspire and endure.
            </p>
            <Link href="/about">
              <Button variant="outline" size="lg">
                Learn More About Us
              </Button>
            </Link>
          </motion.div>
          
          {/* Right Column - Our Values */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold mb-8">Our Values</h3>
            <div className="space-y-6">
              {values.map((value, index) => (
                <motion.div
                  key={index}
                  className="flex items-start"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 + 0.3 }}
                  viewport={{ once: true }}
                >
                  <div className="mr-4 mt-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                  <div>
                    <h4 className="text-xl font-medium mb-2">{value.title}</h4>
                    <p className="text-gray-600">{value.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
        
        {/* Quote */}
        <motion.div
          className="mt-20 text-center max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <blockquote className="text-2xl font-light italic text-gray-700">
            "Building spaces that inspire, connect, and endure."
          </blockquote>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutUsSection;
