-- Add missing columns to profiles table for designer applications
-- This migration adds all the columns needed for the designer approval process

-- Add bio column (currently missing and causing the error)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;

-- Add location column (for designer location info)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location TEXT;

-- Add specialization column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS specialization TEXT;

-- Add experience column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS experience TEXT;

-- Add years_experience column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS years_experience INTEGER;

-- Add portfolio_url column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS portfolio_url TEXT;

-- Add resume_url column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS resume_url TEXT;

-- Add portfolio_files column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS portfolio_files TEXT[];

-- Add applied_at column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS applied_at TIMESTAMP WITH TIME ZONE;

-- Add approved_by column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES profiles(id);

-- Add rejected_by column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS rejected_by UUID REFERENCES profiles(id);

-- Add temp_password column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS temp_password BOOLEAN DEFAULT FALSE;

-- Add is_active column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Add availability column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS availability BOOLEAN DEFAULT TRUE;

-- Add last_sign_in_at column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS last_sign_in_at TIMESTAMP WITH TIME ZONE;

-- Add stripe_customer_id column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

-- Add invite_code column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS invite_code TEXT UNIQUE;

-- Add invited_by column (if not already exists)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS invited_by UUID REFERENCES profiles(id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_specialization ON profiles(specialization);
CREATE INDEX IF NOT EXISTS idx_profiles_location ON profiles(location);
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_availability ON profiles(availability);

-- Add comments for documentation
COMMENT ON COLUMN profiles.bio IS 'Designer bio/description from application';
COMMENT ON COLUMN profiles.location IS 'Designer location from application';
COMMENT ON COLUMN profiles.specialization IS 'Designer specialization area';
COMMENT ON COLUMN profiles.experience IS 'Designer experience description';
COMMENT ON COLUMN profiles.years_experience IS 'Years of experience as integer';
COMMENT ON COLUMN profiles.portfolio_url IS 'External portfolio URL';
COMMENT ON COLUMN profiles.resume_url IS 'Resume file URL in R2 storage';
COMMENT ON COLUMN profiles.portfolio_files IS 'Array of portfolio file URLs in R2 storage';
COMMENT ON COLUMN profiles.applied_at IS 'When the designer application was submitted';
COMMENT ON COLUMN profiles.approved_by IS 'Admin who approved the designer application';
COMMENT ON COLUMN profiles.rejected_by IS 'Admin who rejected the designer application';
COMMENT ON COLUMN profiles.temp_password IS 'Flag indicating if user has temporary password';
COMMENT ON COLUMN profiles.is_active IS 'Whether the user account is active';
COMMENT ON COLUMN profiles.availability IS 'Whether the designer is available for new projects';
COMMENT ON COLUMN profiles.last_sign_in_at IS 'Last time the user signed in';
COMMENT ON COLUMN profiles.stripe_customer_id IS 'Stripe customer ID for payments';
COMMENT ON COLUMN profiles.invite_code IS 'Unique invite code for user registration';
COMMENT ON COLUMN profiles.invited_by IS 'User who sent the invitation';
