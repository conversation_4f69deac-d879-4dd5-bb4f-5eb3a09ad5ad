import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const type = url.searchParams.get('type');
    const role = url.searchParams.get('role');
    const search = url.searchParams.get('search');

    let query = supabase
      .from('admin_messages')
      .select(`
        id,
        recipient_id,
        recipient_role,
        title,
        content,
        message_type,
        priority,
        read_at,
        action_required,
        action_url,
        expires_at,
        created_by,
        created_at,
        created_by_profile:profiles!created_by(full_name),
        recipient_profile:profiles!recipient_id(full_name)
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (type && type !== 'all') {
      query = query.eq('message_type', type);
    }

    if (role && role !== 'all') {
      query = query.eq('recipient_role', role);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching admin messages:', error);
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      messages: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in admin messages API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      recipient_type,
      recipient_role,
      recipient_id,
      title,
      content,
      message_type,
      priority,
      action_required,
      action_url,
      expires_at
    } = body;

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      );
    }

    if (recipient_type === 'specific' && !recipient_id) {
      return NextResponse.json(
        { error: 'Recipient ID is required for specific messages' },
        { status: 400 }
      );
    }

    // Prepare message data
    const messageData = {
      title: title.trim(),
      content: content.trim(),
      message_type: message_type || 'info',
      priority: priority || 'normal',
      action_required: action_required || false,
      action_url: action_url?.trim() || null,
      expires_at: expires_at ? new Date(expires_at).toISOString() : null,
      created_by: user.id,
      recipient_id: recipient_type === 'specific' ? recipient_id : null,
      recipient_role: recipient_type === 'role' ? recipient_role : 
                     recipient_type === 'all' ? 'all' : null,
    };

    if (recipient_type === 'all' || recipient_type === 'role') {
      // Create a single message record for broadcast messages
      const { data, error } = await supabase
        .from('admin_messages')
        .insert([messageData])
        .select(`
          id,
          recipient_id,
          recipient_role,
          title,
          content,
          message_type,
          priority,
          read_at,
          action_required,
          action_url,
          expires_at,
          created_by,
          created_at
        `)
        .single();

      if (error) {
        console.error('Error creating admin message:', error);
        return NextResponse.json(
          { error: 'Failed to create message' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: data,
        recipients_count: recipient_type === 'all' ? 'all users' : `all ${recipient_role}s`
      });

    } else {
      // Create message for specific recipient
      const { data, error } = await supabase
        .from('admin_messages')
        .insert([messageData])
        .select(`
          id,
          recipient_id,
          recipient_role,
          title,
          content,
          message_type,
          priority,
          read_at,
          action_required,
          action_url,
          expires_at,
          created_by,
          created_at
        `)
        .single();

      if (error) {
        console.error('Error creating admin message:', error);
        return NextResponse.json(
          { error: 'Failed to create message' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: data,
        recipients_count: 1
      });
    }

  } catch (error) {
    console.error('Error in admin messages POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
