"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useProposals } from "@/hooks/useDashboardData";
import { motion } from "framer-motion";
import { useAcceptProposal, useRejectProposal } from "@/hooks/useDashboardData";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Target,
  Clock,
  DollarSign,
  User,
  Eye,
  FileText,
  CheckCircle,
  XCircle,
  Calendar,
  Star,
  AlertCircle,
  Filter,
  Search,
  ChevronRight,
  Briefcase,
  MessageSquare,
} from "lucide-react";

interface Proposal {
  id: string;
  brief_id: string;
  brief_title: string;
  designer_id: string;
  designer_name: string;
  designer_avatar: string | null;
  designer_rating: number;
  title: string;
  description: string;
  total_budget: number;
  timeline_weeks: number;
  status:
    | "draft"
    | "submitted"
    | "under_review"
    | "accepted"
    | "rejected"
    | "withdrawn";
  submitted_at: string;
  expires_at: string | null;
  milestones: {
    title: string;
    description: string;
    amount: number;
    due_date: string;
  }[];
  terms_and_conditions: string;
}
export default function ClientProposals() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Use optimized data fetching with correct parameters
  const { data: proposals = [], isLoading, error } = useProposals(user?.id || '', 'client');

  // Use optimized hooks with correct parameters
  const acceptProposal = useAcceptProposal(user?.id || '');
  const rejectProposal = useRejectProposal();
  const [showChangeRequestModal, setShowChangeRequestModal] = useState(false);
  const [selectedProposalId, setSelectedProposalId] = useState<string | null>(
    null
  );
  const [changeRequestForm, setChangeRequestForm] = useState({
    section: "",
    description: "",
  });

  // Transform proposals data for display
  const formattedProposals: Proposal[] = proposals.map((proposal) => ({
    id: proposal.id,
    brief_id: proposal.brief_id,
    brief_title: proposal.brief?.title || "Unknown Brief",
    designer_id: proposal.designer_id,
    designer_name: proposal.designer?.full_name || "Unknown Designer",
    designer_avatar: proposal.designer?.avatar_url || null,
    designer_rating: 4.8, // TODO: Calculate from project_reviews table
    title: proposal.title || "",
    description: proposal.description || "",
    total_budget: Number(proposal.total_budget) || 0,
    timeline_weeks: Number(proposal.timeline_weeks) || 0,
    status: proposal.status,
    submitted_at: proposal.submitted_at,
    expires_at: proposal.expires_at,
    milestones: (proposal.milestones || []).map(
      (milestone: any) => ({
        title: milestone.title || "",
        description: milestone.description || "",
        amount: Number(milestone.amount) || 0,
        due_date: milestone.due_date || "",
      })
    ),
    terms_and_conditions: proposal.terms_and_conditions || "",
  }));
  const handleProposalAction = async (
    proposalId: string,
    action: "accept" | "reject",
    briefId?: string
  ) => {
    if (!confirm(`Are you sure you want to ${action} this proposal?`)) {
      return;
    }

    try {
      if (action === 'accept') {
        await acceptProposal.mutateAsync({ proposalId, briefId });
      } else {
        await rejectProposal.mutateAsync({ proposalId, userId: user?.id || '' });
      }

      // Note: Local state updates are handled by React Query optimistic updates
    } catch (error) {
      console.error(`Error ${action}ing proposal:`, error);
    }
  };

  const openChangeRequestModal = (proposalId: string) => {
    setSelectedProposalId(proposalId);
    setShowChangeRequestModal(true);
    setChangeRequestForm({ section: "", description: "" });
  };

  const handleChangeRequest = async () => {
    if (
      !selectedProposalId ||
      !changeRequestForm.section ||
      !changeRequestForm.description ||
      !user
    ) {
      alert("Please fill in all fields");
      return;
    }

    try {
      const { error } = await supabase.from("proposal_change_requests").insert({
        proposal_id: selectedProposalId,
        requested_by: user.id,
        section: changeRequestForm.section,
        description: changeRequestForm.description,
        status: "pending",
      });

      if (error) throw error;

      alert("Change request submitted successfully!");
      setShowChangeRequestModal(false);
      setSelectedProposalId(null);
      setChangeRequestForm({ section: "", description: "" });
    } catch (error) {
      console.error("Error submitting change request:", error);
      alert("Failed to submit change request");
    }
  };
  // Enhanced status display for client perspective
  const getClientStatusDisplay = (status: string, expiresAt: string | null = null) => {
    // Check if proposal is expired
    const isExpired = expiresAt && new Date(expiresAt) < new Date();

    if (isExpired && (status === 'submitted' || status === 'under_review')) {
      return {
        text: "EXPIRED",
        color: "text-red-600 bg-red-50 border-red-200",
        icon: "expired"
      };
    }

    switch (status) {
      case "submitted":
        return {
          text: "NEW",
          color: "text-blue-600 bg-blue-50 border-blue-200",
          icon: "new"
        };
      case "under_review":
        return {
          text: "REVIEWING",
          color: "text-yellow-600 bg-yellow-50 border-yellow-200",
          icon: "review"
        };
      case "accepted":
        return {
          text: "ACCEPTED",
          color: "text-green-600 bg-green-50 border-green-200",
          icon: "accepted"
        };
      case "rejected":
        return {
          text: "REJECTED",
          color: "text-red-600 bg-red-50 border-red-200",
          icon: "rejected"
        };
      case "withdrawn":
        return {
          text: "WITHDRAWN",
          color: "text-gray-600 bg-gray-50 border-gray-200",
          icon: "withdrawn"
        };
      case "draft":
        return {
          text: "DRAFT",
          color: "text-gray-600 bg-gray-50 border-gray-200",
          icon: "draft"
        };
      default:
        return {
          text: status.replace("_", " ").toUpperCase(),
          color: "text-gray-600 bg-gray-50 border-gray-200",
          icon: "default"
        };
    }
  };

  const getStatusIcon = (iconType: string) => {
    switch (iconType) {
      case "accepted":
        return <CheckCircle className="h-4 w-4" />;
      case "new":
        return <FileText className="h-4 w-4" />;
      case "review":
        return <Clock className="h-4 w-4" />;
      case "rejected":
        return <XCircle className="h-4 w-4" />;
      case "withdrawn":
        return <AlertCircle className="h-4 w-4" />;
      case "expired":
        return <AlertCircle className="h-4 w-4" />;
      case "draft":
        return <Target className="h-4 w-4" />;
      default:
        return <Target className="h-4 w-4" />;
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${
              star <= rating ? "text-yellow-400 fill-current" : "text-gray-300"
            }`}
          />
        ))}
        <span className="text-xs text-gray-500 ml-1">({rating})</span>
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getProposalTimingInfo = (expiresAt: string | null) => {
    if (!expiresAt) return { isExpired: false, isExpiringSoon: false, daysLeft: null };

    const expirationDate = new Date(expiresAt);
    const now = new Date();
    const timeDiff = expirationDate.getTime() - now.getTime();
    const daysDiff = timeDiff / (1000 * 3600 * 24);

    return {
      isExpired: daysDiff <= 0,
      isExpiringSoon: daysDiff <= 2 && daysDiff > 0,
      daysLeft: Math.ceil(daysDiff)
    };
  };

  const filteredProposals = formattedProposals.filter((proposal) => {
    const matchesSearch =
      proposal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proposal.brief_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proposal.designer_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || proposal.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Show loading only for initial load or auth loading
  const loading = authLoading || (isLoading && formattedProposals.length === 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Proposals</h1>
          <p className="text-gray-600">
            Review and manage proposals from designers
          </p>
        </div>
        <Link href="/client/briefs">
          <Button
            variant="outline"
            className="border-brown-600 text-brown-600 hover:bg-brown-50"
          >
            <Briefcase className="h-4 w-4 mr-2" />
            View My Briefs
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search proposals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Statuses</option>
            <option value="submitted">Submitted</option>
            <option value="under_review">Under Review</option>
            <option value="accepted">Accepted</option>
            <option value="rejected">Rejected</option>
            <option value="withdrawn">Withdrawn</option>
          </select>
        </div>
      </div>

      {/* Proposals List */}
      {filteredProposals.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {formattedProposals.length === 0
              ? "No proposals yet"
              : "No proposals match your filters"}
          </h3>
          <p className="text-gray-500 mb-4">
            {formattedProposals.length === 0
              ? "Proposals from designers will appear here once you submit project briefs"
              : "Try adjusting your search terms or filters"}
          </p>
          {formattedProposals.length === 0 && (
            <Link href="/client/briefs/new">
              <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                <Briefcase className="h-4 w-4 mr-2" />
                Submit a Brief
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredProposals.map((proposal) => (
            <motion.div
              key={proposal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              {/* Enhanced Timing Warnings */}
              {(() => {
                const timingInfo = getProposalTimingInfo(proposal.expires_at);
                const statusDisplay = getClientStatusDisplay(proposal.status, proposal.expires_at);

                if (timingInfo.isExpired) {
                  return (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center">
                      <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                      <span className="text-sm text-red-800">
                        This proposal expired on {formatDate(proposal.expires_at)}
                      </span>
                    </div>
                  );
                } else if (timingInfo.isExpiringSoon) {
                  return (
                    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center">
                      <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="text-sm text-yellow-800">
                        This proposal expires in {timingInfo.daysLeft} day{timingInfo.daysLeft !== 1 ? 's' : ''} on {formatDate(proposal.expires_at)}
                      </span>
                    </div>
                  );
                }
                return null;
              })()}

              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {proposal.title}
                    </h3>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full border flex items-center ${getClientStatusDisplay(proposal.status, proposal.expires_at).color}`}
                    >
                      {getStatusIcon(getClientStatusDisplay(proposal.status, proposal.expires_at).icon)}
                      <span className="ml-1">
                        {getClientStatusDisplay(proposal.status, proposal.expires_at).text}
                      </span>
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    For: {proposal.brief_title}
                  </p>
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {proposal.description}
                  </p>
                </div>
              </div>

              {/* Designer Info */}
              <div className="flex items-center space-x-3 mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {proposal.designer_avatar ? (
                    <img
                      src={proposal.designer_avatar}
                      alt={proposal.designer_name}
                      className="h-10 w-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="h-5 w-5 text-gray-500" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">
                    {proposal.designer_name}
                  </p>
                  {renderStars(proposal.designer_rating)}
                </div>
                <Link
                  href={`/client/messages?otherUserId=${proposal.designer_id}&type=direct`}
                >
                  <Button variant="outline" size="sm">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Message
                  </Button>
                </Link>
              </div>

              {/* Proposal Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center text-sm text-gray-500">
                  <DollarSign className="h-4 w-4 mr-2" />$
                  {(proposal.total_budget || 0).toLocaleString()}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="h-4 w-4 mr-2" />
                  {proposal.timeline_weeks} weeks
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  Submitted {formatDate(proposal.submitted_at)}
                </div>
              </div>

              {/* Milestones Preview */}
              {proposal.milestones && proposal.milestones.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">
                    Project Milestones:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {proposal.milestones.slice(0, 4).map((milestone, index) => (
                      <div
                        key={index}
                        className="text-xs text-gray-600 flex justify-between"
                      >
                        <span>{milestone.title}</span>
                        <span>${(milestone.amount || 0).toLocaleString()}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {proposal.expires_at && (
                    <span className="text-xs text-gray-500">
                      Expires: {formatDate(proposal.expires_at)}
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Link href={`/client/proposals/${proposal.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </Link>
                  {(() => {
                    const timingInfo = getProposalTimingInfo(proposal.expires_at);
                    const canTakeAction = (proposal.status === "submitted" || proposal.status === "under_review") && !timingInfo.isExpired;

                    if (canTakeAction) {
                      return (
                        <>
                          <Button
                            onClick={() =>
                              handleProposalAction(proposal.id, "accept", proposal.brief_id)
                            }
                            disabled={acceptProposal.isPending || rejectProposal.isPending}
                            size="sm"
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            {acceptProposal.isPending ? "Accepting..." : "Accept"}
                          </Button>
                          <Button
                            onClick={() =>
                              handleProposalAction(proposal.id, "reject")
                            }
                            disabled={acceptProposal.isPending || rejectProposal.isPending}
                            size="sm"
                            variant="outline"
                            className="border-red-600 text-red-600 hover:bg-red-50"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            {rejectProposal.isPending ? "Rejecting..." : "Reject"}
                          </Button>
                        </>
                      );
                    } else if (timingInfo.isExpired) {
                      return (
                        <span className="text-sm text-red-600 font-medium">
                          ⏰ Proposal Expired
                        </span>
                      );
                    }
                    return null;
                  })()}
                  {proposal.status === "rejected" && (
                    <Button
                      onClick={() => openChangeRequestModal(proposal.id)}
                      size="sm"
                      variant="outline"
                      className="border-orange-600 text-orange-600 hover:bg-orange-50"
                    >
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Request Changes
                    </Button>
                  )}
                  {proposal.status === "accepted" && (
                    <span className="text-sm text-green-600 font-medium">
                      ✓ Proposal Accepted
                    </span>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Change Request Modal */}
      {showChangeRequestModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Request Changes
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Section to Change
                </label>
                <select
                  value={changeRequestForm.section}
                  onChange={(e) =>
                    setChangeRequestForm((prev) => ({
                      ...prev,
                      section: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                >
                  <option value="">Select section...</option>
                  <option value="budget">Budget</option>
                  <option value="timeline">Timeline</option>
                  <option value="scope">Project Scope</option>
                  <option value="milestones">Milestones</option>
                  <option value="terms">Terms & Conditions</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Change Description
                </label>
                <textarea
                  value={changeRequestForm.description}
                  onChange={(e) =>
                    setChangeRequestForm((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Describe the changes you'd like to see..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                />
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6">
              <Button
                onClick={() => setShowChangeRequestModal(false)}
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                onClick={handleChangeRequest}
                className="bg-brown-600 hover:bg-brown-700 text-white"
              >
                Submit Request
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
