import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * API route for bulk operations on designer applications
 */
export async function POST(request: NextRequest) {
  try {
    const { 
      action, 
      application_ids, 
      status, 
      notes,
      email_template_id,
      custom_message 
    } = await request.json();

    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    if (!application_ids || !Array.isArray(application_ids) || application_ids.length === 0) {
      return NextResponse.json({ error: 'Application IDs are required' }, { status: 400 });
    }

    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Get applications
    const { data: applications, error: fetchError } = await supabase
      .from('designer_applications')
      .select('*')
      .in('id', application_ids);

    if (fetchError) {
      return NextResponse.json({ error: 'Failed to fetch applications' }, { status: 500 });
    }

    // Process each application
    for (const application of applications) {
      try {
        switch (action) {
          case 'update_status':
            await updateApplicationStatus(application, status, notes, user.id);
            break;
          
          case 'send_email':
            await sendBulkEmail(application, email_template_id, custom_message, user.id);
            break;
          
          case 'delete':
            await deleteApplication(application.id);
            break;
          
          default:
            throw new Error(`Unknown action: ${action}`);
        }
        
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`${application.full_name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: true,
      results,
      message: `Processed ${results.success} applications successfully, ${results.failed} failed`
    });

  } catch (error) {
    console.error('Error in bulk operation:', error);
    return NextResponse.json(
      { error: 'Failed to process bulk operation' },
      { status: 500 }
    );
  }
}

async function updateApplicationStatus(application: any, status: string, notes: string, adminId: string) {
  const { error } = await supabase
    .from('designer_applications')
    .update({
      application_status: status,
      admin_notes: notes,
      status_updated_by: adminId,
      status_updated_at: new Date().toISOString()
    })
    .eq('id', application.id);

  if (error) throw error;

  // Send status update email
  let emailSubject = '';
  let emailContent = '';

  switch (status) {
    case 'under_review':
      emailSubject = 'Your Designer Application is Under Review';
      emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #8B4513;">Application Update</h2>
          <p>Dear ${application.full_name},</p>
          <p>Thank you for your interest in joining Seniors Architecture Firm. We wanted to update you on the status of your designer application.</p>
          <p><strong>Current Status:</strong> Under Review</p>
          <p>Our team is currently reviewing your application and portfolio. We will contact you within the next 5-7 business days with next steps.</p>
          ${notes ? `<p><strong>Additional Notes:</strong> ${notes}</p>` : ''}
          <p>If you have any questions, please don't hesitate to reach out.</p>
          <p>Best regards,<br>Seniors Architecture Firm Team</p>
        </div>
      `;
      break;
    
    case 'on_hold':
      emailSubject = 'Your Designer Application Status Update';
      emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #8B4513;">Application Update</h2>
          <p>Dear ${application.full_name},</p>
          <p>We wanted to provide you with an update on your designer application with Seniors Architecture Firm.</p>
          <p><strong>Current Status:</strong> On Hold</p>
          <p>Your application is currently on hold. We will contact you when we are ready to proceed with the next steps.</p>
          ${notes ? `<p><strong>Additional Information:</strong> ${notes}</p>` : ''}
          <p>Thank you for your patience and continued interest.</p>
          <p>Best regards,<br>Seniors Architecture Firm Team</p>
        </div>
      `;
      break;
  }

  if (emailSubject && emailContent) {
    await resend.emails.send({
      from: 'Seniors Architecture Firm <<EMAIL>>',
      to: [application.email],
      subject: emailSubject,
      html: emailContent
    });

    // Record communication
    await supabase
      .from('application_communications')
      .insert({
        application_id: application.id,
        communication_type: 'email',
        subject: emailSubject,
        content: emailContent,
        sent_by: adminId,
        recipient_email: application.email,
        status: 'sent'
      });
  }
}

async function sendBulkEmail(application: any, templateId: string, customMessage: string, adminId: string) {
  let emailSubject = '';
  let emailContent = customMessage;

  if (templateId) {
    const { data: template } = await supabase
      .from('email_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (template) {
      emailSubject = template.subject;
      emailContent = template.content;

      // Replace template variables
      const variables = {
        full_name: application.full_name,
        email: application.email,
        specialization: application.specialization,
        application_date: new Date(application.created_at).toLocaleDateString()
      };

      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        emailSubject = emailSubject.replace(regex, value || '');
        emailContent = emailContent.replace(regex, value || '');
      });
    }
  }

  await resend.emails.send({
    from: 'Seniors Architecture Firm <<EMAIL>>',
    to: [application.email],
    subject: emailSubject,
    html: emailContent
  });

  // Record communication
  await supabase
    .from('application_communications')
    .insert({
      application_id: application.id,
      communication_type: 'email',
      subject: emailSubject,
      content: emailContent,
      sent_by: adminId,
      recipient_email: application.email,
      status: 'sent',
      template_used: templateId
    });
}

async function deleteApplication(applicationId: string) {
  const { error } = await supabase
    .from('designer_applications')
    .delete()
    .eq('id', applicationId);

  if (error) throw error;
}
