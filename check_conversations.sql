-- Check conversations table data
SELECT 
  COUNT(*) as total_conversations,
  COUNT(CASE WHEN type = 'direct' THEN 1 END) as direct_conversations,
  COUNT(CASE WHEN type = 'project' THEN 1 END) as project_conversations,
  COUNT(CASE WHEN project_id IS NOT NULL THEN 1 END) as conversations_with_project
FROM conversations;

-- Sample conversations
SELECT 
  id,
  type,
  title,
  project_id,
  created_by,
  created_at,
  is_active
FROM conversations 
ORDER BY created_at DESC 
LIMIT 5;
