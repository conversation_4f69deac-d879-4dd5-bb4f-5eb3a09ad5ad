'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Wifi, 
  WifiOff, 
  Bell, 
  Activity,
  Clock
} from 'lucide-react';

interface RealTimeUpdatesProps {
  userId: string;
  userRole: string;
  onUpdate: () => void;
}

interface RealtimeEvent {
  id: string;
  type: string;
  message: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high';
}

export default function RealTimeUpdates({ userId, userRole, onUpdate }: RealTimeUpdatesProps) {
  const [connected, setConnected] = useState(false);
  const [events, setEvents] = useState<RealtimeEvent[]>([]);
  const [lastActivity, setLastActivity] = useState<Date | null>(null);

  useEffect(() => {
    // Simulate real-time connection
    setConnected(true);
    setLastActivity(new Date());

    // Simulate periodic updates
    const interval = setInterval(() => {
      // Simulate random events based on user role
      const eventTypes = {
        manager: ['project_update', 'approval_request', 'team_notification'],
        client: ['project_progress', 'message_received', 'milestone_completed'],
        designer: ['review_assigned', 'feedback_received', 'project_assigned'],
        admin: ['system_alert', 'user_activity', 'performance_update'],
        quality_team: ['review_assigned', 'sla_warning', 'quality_alert']
      };

      const types = eventTypes[userRole as keyof typeof eventTypes] || ['general_update'];
      const randomType = types[Math.floor(Math.random() * types.length)];
      
      // Only add events occasionally (20% chance)
      if (Math.random() < 0.2) {
        const newEvent: RealtimeEvent = {
          id: Date.now().toString(),
          type: randomType,
          message: getEventMessage(randomType, userRole),
          timestamp: new Date(),
          priority: Math.random() < 0.1 ? 'high' : Math.random() < 0.3 ? 'medium' : 'low'
        };

        setEvents(prev => [newEvent, ...prev.slice(0, 4)]); // Keep only 5 most recent
        setLastActivity(new Date());
        
        // Trigger dashboard update for important events
        if (newEvent.priority === 'high' || Math.random() < 0.3) {
          onUpdate();
        }
      }
    }, 10000); // Check every 10 seconds

    // Cleanup
    return () => {
      clearInterval(interval);
      setConnected(false);
    };
  }, [userId, userRole, onUpdate]);

  const getEventMessage = (type: string, role: string): string => {
    const messages = {
      project_update: 'Project milestone completed by team member',
      approval_request: 'New escrow release requires your approval',
      team_notification: 'Team member posted an update',
      project_progress: 'Your project has reached a new milestone',
      message_received: 'New message from your designer',
      milestone_completed: 'Project milestone marked as complete',
      review_assigned: 'New quality review assigned to you',
      feedback_received: 'Client provided feedback on your submission',
      project_assigned: 'New project has been assigned to you',
      system_alert: 'System performance metrics updated',
      user_activity: 'New user registration detected',
      performance_update: 'Platform performance metrics refreshed',
      sla_warning: 'Review approaching SLA deadline',
      quality_alert: 'Quality threshold alert triggered',
      general_update: 'System update available'
    };

    return messages[type as keyof typeof messages] || 'New activity detected';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      default:
        return 'bg-blue-500';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {connected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm font-medium">
                Real-time Updates {connected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            {lastActivity && (
              <div className="flex items-center space-x-1 text-xs text-gray-500">
                <Clock className="h-3 w-3" />
                <span>Last: {lastActivity.toLocaleTimeString()}</span>
              </div>
            )}
          </div>

          {/* Recent Events */}
          {events.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center space-x-2">
                <Bell className="h-4 w-4" />
                <span>Recent Activity</span>
              </h4>
              
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {events.map((event) => (
                  <div key={event.id} className="flex items-start space-x-3 p-2 bg-gray-50 rounded text-sm">
                    <div className={`w-2 h-2 rounded-full mt-2 ${getPriorityColor(event.priority)}`}></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-gray-900 truncate">{event.message}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant={getPriorityBadgeVariant(event.priority)} className="text-xs">
                          {event.priority}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {event.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Events State */}
          {events.length === 0 && connected && (
            <div className="text-center py-4">
              <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Monitoring for real-time updates...</p>
            </div>
          )}

          {/* Disconnected State */}
          {!connected && (
            <div className="text-center py-4">
              <WifiOff className="h-8 w-8 text-red-400 mx-auto mb-2" />
              <p className="text-sm text-red-600">Real-time updates disconnected</p>
              <p className="text-xs text-gray-500">Attempting to reconnect...</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
