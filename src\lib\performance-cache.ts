'use client';

import { supabase } from '@/lib/supabase';

/**
 * Performance Cache Manager
 * Handles API response caching, data optimization, and performance monitoring
 */

interface CacheEntry {
  key: string;
  data: any;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  version: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalEntries: number;
  memoryUsage: number;
}

export class PerformanceCache {
  private static instance: PerformanceCache;
  private cache: Map<string, CacheEntry> = new Map();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalEntries: 0,
    memoryUsage: 0
  };

  private constructor() {
    // Start cleanup interval
    this.startCleanupInterval();
  }

  static getInstance(): PerformanceCache {
    if (!PerformanceCache.instance) {
      PerformanceCache.instance = new PerformanceCache();
    }
    return PerformanceCache.instance;
  }

  /**
   * Get cached data or fetch from source
   */
  async get<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = 5 * 60 * 1000 // 5 minutes default
  ): Promise<T> {
    const entry = this.cache.get(key);
    const now = Date.now();

    // Check if cache entry exists and is still valid
    if (entry && (now - entry.timestamp) < entry.ttl) {
      this.stats.hits++;
      this.updateHitRate();
      return entry.data as T;
    }

    // Cache miss - fetch fresh data
    this.stats.misses++;
    this.updateHitRate();

    try {
      const data = await fetchFunction();
      
      // Store in cache
      this.set(key, data, ttl);
      
      return data;
    } catch (error) {
      // If fetch fails and we have stale data, return it
      if (entry) {
        console.warn(`Cache: Using stale data for key ${key} due to fetch error:`, error);
        return entry.data as T;
      }
      throw error;
    }
  }

  /**
   * Set cache entry
   */
  set(key: string, data: any, ttl: number = 5 * 60 * 1000): void {
    const entry: CacheEntry = {
      key,
      data,
      timestamp: Date.now(),
      ttl,
      version: 1
    };

    this.cache.set(key, entry);
    this.updateStats();
  }

  /**
   * Invalidate cache entry
   */
  invalidate(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateStats();
    }
    return deleted;
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): number {
    let deletedCount = 0;
    const regex = new RegExp(pattern);

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    if (deletedCount > 0) {
      this.updateStats();
    }

    return deletedCount;
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
    this.updateStats();
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Preload cache with common data
   */
  async preloadCommonData(userId: string, userRole: string): Promise<void> {
    const preloadTasks: Promise<any>[] = [];

    try {
      // Preload user profile
      preloadTasks.push(
        this.get(`profile:${userId}`, async () => {
          const { data } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();
          return data;
        }, 10 * 60 * 1000) // 10 minutes
      );

      // Role-specific preloading
      switch (userRole) {
        case 'manager':
          // Preload manager dashboard data
          preloadTasks.push(
            this.get(`manager:dashboard:${userId}`, async () => {
              const { data } = await supabase
                .from('manager_dashboard_cache')
                .select('*')
                .eq('manager_id', userId)
                .single();
              return data;
            }, 5 * 60 * 1000)
          );

          // Preload manager projects
          preloadTasks.push(
            this.get(`manager:projects:${userId}`, async () => {
              const { data } = await supabase
                .from('mv_project_overview')
                .select('*')
                .eq('manager_name', userId)
                .limit(20);
              return data;
            }, 3 * 60 * 1000)
          );
          break;

        case 'client':
          // Preload client projects
          preloadTasks.push(
            this.get(`client:projects:${userId}`, async () => {
              const { data } = await supabase
                .from('projects')
                .select('*, designer:profiles!designer_id(full_name, avatar_url)')
                .eq('client_id', userId)
                .order('created_at', { ascending: false })
                .limit(10);
              return data;
            }, 3 * 60 * 1000)
          );
          break;

        case 'designer':
          // Preload designer projects
          preloadTasks.push(
            this.get(`designer:projects:${userId}`, async () => {
              const { data } = await supabase
                .from('projects')
                .select('*, client:profiles!client_id(full_name, avatar_url)')
                .eq('designer_id', userId)
                .order('created_at', { ascending: false })
                .limit(10);
              return data;
            }, 3 * 60 * 1000)
          );
          break;
      }

      // Execute all preload tasks
      await Promise.allSettled(preloadTasks);
      
      console.log(`Cache: Preloaded data for ${userRole} user ${userId}`);
    } catch (error) {
      console.error('Cache: Preload failed:', error);
    }
  }

  /**
   * Get optimized project data with caching
   */
  async getOptimizedProjects(params: {
    userId: string;
    userRole: string;
    limit?: number;
    status?: string;
  }): Promise<any[]> {
    const { userId, userRole, limit = 20, status } = params;
    const cacheKey = `projects:${userRole}:${userId}:${limit}:${status || 'all'}`;

    return this.get(cacheKey, async () => {
      let query = supabase.from('mv_project_overview').select('*');

      // Apply role-based filtering
      switch (userRole) {
        case 'manager':
          query = query.eq('manager_name', userId);
          break;
        case 'client':
          query = query.eq('client_name', userId);
          break;
        case 'designer':
          query = query.eq('designer_name', userId);
          break;
        case 'admin':
          // Admin can see all projects
          break;
        default:
          throw new Error('Invalid user role');
      }

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    }, 2 * 60 * 1000); // 2 minutes cache
  }

  /**
   * Get optimized escrow data with caching
   */
  async getOptimizedEscrowData(params: {
    userId: string;
    userRole: string;
    type: 'holds' | 'releases';
  }): Promise<any[]> {
    const { userId, userRole, type } = params;
    const cacheKey = `escrow:${type}:${userRole}:${userId}`;

    return this.get(cacheKey, async () => {
      const tableName = type === 'holds' ? 'escrow_holds' : 'escrow_releases';
      let query = supabase.from(tableName).select('*');

      // Apply role-based filtering
      switch (userRole) {
        case 'manager':
          if (type === 'releases') {
            query = query.eq('manager_id', userId);
          } else {
            // For holds, get projects managed by this manager
            const { data: projects } = await supabase
              .from('projects')
              .select('id')
              .eq('assigned_manager_id', userId);
            
            if (projects && projects.length > 0) {
              const projectIds = projects.map(p => p.id);
              query = query.in('project_id', projectIds);
            } else {
              return [];
            }
          }
          break;
        case 'admin':
          // Admin can see all
          break;
        default:
          throw new Error('Insufficient permissions');
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      return data || [];
    }, 1 * 60 * 1000); // 1 minute cache for financial data
  }

  /**
   * Batch invalidate related caches
   */
  invalidateRelatedCaches(entityType: string, entityId: string): void {
    const patterns = {
      project: [
        `projects:.*:${entityId}`,
        `manager:projects:.*`,
        `client:projects:.*`,
        `designer:projects:.*`,
        `escrow:.*:.*`
      ],
      user: [
        `profile:${entityId}`,
        `.*:${entityId}:.*`,
        `manager:dashboard:${entityId}`
      ],
      escrow: [
        `escrow:.*:.*`,
        `manager:dashboard:.*`
      ]
    };

    const patternsToInvalidate = patterns[entityType as keyof typeof patterns] || [];
    
    let totalInvalidated = 0;
    patternsToInvalidate.forEach(pattern => {
      totalInvalidated += this.invalidatePattern(pattern);
    });

    console.log(`Cache: Invalidated ${totalInvalidated} entries for ${entityType}:${entityId}`);
  }

  /**
   * Private methods
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  private updateStats(): void {
    this.stats.totalEntries = this.cache.size;
    this.stats.memoryUsage = this.estimateMemoryUsage();
    this.updateHitRate();
  }

  private estimateMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += JSON.stringify(entry).length * 2; // Rough estimate
    }
    return totalSize;
  }

  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  private cleanup(): void {
    const now = Date.now();
    let deletedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) > entry.ttl) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    if (deletedCount > 0) {
      this.updateStats();
      console.log(`Cache: Cleaned up ${deletedCount} expired entries`);
    }
  }
}

// Export singleton instance
export const performanceCache = PerformanceCache.getInstance();

// Utility functions for common cache operations
export const cacheKeys = {
  profile: (userId: string) => `profile:${userId}`,
  projects: (userRole: string, userId: string, filters?: string) => 
    `projects:${userRole}:${userId}:${filters || 'default'}`,
  escrow: (type: string, userRole: string, userId: string) => 
    `escrow:${type}:${userRole}:${userId}`,
  dashboard: (userRole: string, userId: string) => 
    `dashboard:${userRole}:${userId}`,
  activities: (userRole: string, userId: string, limit?: number) => 
    `activities:${userRole}:${userId}:${limit || 10}`,
  negotiations: (userRole: string, userId: string, status?: string) => 
    `negotiations:${userRole}:${userId}:${status || 'all'}`
};

// Cache invalidation helpers
export const invalidateUserCache = (userId: string) => {
  performanceCache.invalidateRelatedCaches('user', userId);
};

export const invalidateProjectCache = (projectId: string) => {
  performanceCache.invalidateRelatedCaches('project', projectId);
};

export const invalidateEscrowCache = () => {
  performanceCache.invalidateRelatedCaches('escrow', 'all');
};
