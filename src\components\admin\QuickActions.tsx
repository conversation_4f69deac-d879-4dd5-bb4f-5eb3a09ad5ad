"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Plus,
  UserPlus,
  FolderPlus,
  MessageSquare,
  Bell,
  Search,
  Download,
  Upload,
  Settings,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface QuickAction {
  name: string;
  description: string;
  href: string;
  icon: any;
  color: string;
  badge?: number;
  urgent?: boolean;
}

export function QuickActions() {
  const { user } = useOptimizedAuth();
  const [stats, setStats] = useState({
    pendingApplications: 0,
    unreadMessages: 0,
    activeDisputes: 0,
    pendingPayouts: 0
  });

  useEffect(() => {
    if (user) {
      fetchQuickStats();
    }
  }, [user]);

  const fetchQuickStats = async () => {
    try {
      // Get pending applications
      const { count: pendingApplications } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'designer')
        .eq('application_status', 'pending');

      // Get unread messages (mock for now)
      const unreadMessages = 3; // This would come from a messages table

      // Get active disputes (mock for now)
      const activeDisputes = 1; // This would come from a disputes table

      // Get pending payouts (mock for now)
      const pendingPayouts = 5; // This would come from transactions table

      setStats({
        pendingApplications: pendingApplications || 0,
        unreadMessages,
        activeDisputes,
        pendingPayouts
      });
    } catch (error) {
      console.error('Error fetching quick stats:', error);
    }
  };

  const quickActions: QuickAction[] = [
    {
      name: "Review Applications",
      description: "Review pending designer applications",
      href: "/admin/dashboard?tab=applications",
      icon: UserPlus,
      color: "bg-blue-500 hover:bg-blue-600",
      badge: stats.pendingApplications,
      urgent: stats.pendingApplications > 0
    },
    {
      name: "Assign Projects",
      description: "Assign projects to designers",
      href: "/admin/dashboard?tab=assignments",
      icon: FolderPlus,
      color: "bg-green-500 hover:bg-green-600"
    },
    {
      name: "Messages",
      description: "View and respond to messages",
      href: "/admin/messages",
      icon: MessageSquare,
      color: "bg-purple-500 hover:bg-purple-600",
      badge: stats.unreadMessages,
      urgent: stats.unreadMessages > 0
    },
    {
      name: "Financial Reports",
      description: "View financial analytics",
      href: "/admin/dashboard?tab=reports",
      icon: BarChart3,
      color: "bg-brown-500 hover:bg-brown-600"
    },
    {
      name: "Disputes",
      description: "Handle active disputes",
      href: "/admin/disputes",
      icon: AlertTriangle,
      color: "bg-red-500 hover:bg-red-600",
      badge: stats.activeDisputes,
      urgent: stats.activeDisputes > 0
    },
    {
      name: "Payouts",
      description: "Process pending payouts",
      href: "/admin/finance/payouts",
      icon: DollarSign,
      color: "bg-yellow-500 hover:bg-yellow-600",
      badge: stats.pendingPayouts,
      urgent: stats.pendingPayouts > 0
    },
    {
      name: "Platform Settings",
      description: "Configure platform settings",
      href: "/admin/settings",
      icon: Settings,
      color: "bg-gray-500 hover:bg-gray-600"
    },
    {
      name: "Export Data",
      description: "Export platform data",
      href: "/admin/export",
      icon: Download,
      color: "bg-indigo-500 hover:bg-indigo-600"
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm border p-6"
    >
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          <p className="text-sm text-gray-600">Frequently used admin functions</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={fetchQuickStats}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <Search className="h-5 w-5" />
        </motion.button>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {quickActions.map((action, index) => {
          const Icon = action.icon;

          return (
            <motion.div
              key={action.name}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: index * 0.05 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href={action.href}
                className={`relative block p-4 rounded-xl text-white transition-all duration-200 ${action.color} ${
                  action.urgent ? 'ring-2 ring-red-300 ring-opacity-50' : ''
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <Icon className="h-6 w-6" />
                  {action.badge && action.badge > 0 && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className={`text-xs font-bold px-2 py-1 rounded-full ${
                        action.urgent
                          ? 'bg-red-500 text-white'
                          : 'bg-white bg-opacity-20 text-white'
                      }`}
                    >
                      {action.badge}
                    </motion.span>
                  )}
                  {action.urgent && !action.badge && (
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="w-3 h-3 bg-red-400 rounded-full"
                    />
                  )}
                </div>

                <h4 className="font-semibold text-sm mb-1">{action.name}</h4>
                <p className="text-xs opacity-90 leading-relaxed">{action.description}</p>
              </Link>
            </motion.div>
          );
        })}
      </div>

      {/* Urgent Actions Alert */}
      {(stats.pendingApplications > 0 || stats.unreadMessages > 0 || stats.activeDisputes > 0) && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg"
        >
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-3" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-800">Attention Required</h4>
              <p className="text-sm text-red-700 mt-1">
                You have {stats.pendingApplications + stats.unreadMessages + stats.activeDisputes} items requiring immediate attention.
              </p>
            </div>
            <div className="flex space-x-2">
              {stats.pendingApplications > 0 && (
                <Link href="/admin/dashboard?tab=applications">
                  <Button size="sm" className="bg-red-600 hover:bg-red-700 text-white">
                    Review Applications
                  </Button>
                </Link>
              )}
              {stats.activeDisputes > 0 && (
                <Link href="/admin/disputes">
                  <Button size="sm" variant="outline" className="border-red-300 text-red-700 hover:bg-red-50">
                    Handle Disputes
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}
