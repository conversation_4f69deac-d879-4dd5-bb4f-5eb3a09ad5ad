"use client";

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CheckCircle, Copy, ArrowRight, Home } from 'lucide-react';
import { motion } from 'framer-motion';

interface SuccessScreenProps {
  trackingNumber: string;
  requestType: 'sample_request' | 'vision_builder';
}

export default function SuccessScreen({ trackingNumber, requestType }: SuccessScreenProps) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(trackingNumber);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const requestTypeLabel = requestType === 'sample_request' ? 'Sample Request' : 'Vision';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white shadow-xl rounded-lg overflow-hidden max-w-2xl mx-auto"
    >
      <div className="p-6 sm:p-10">
        <div className="flex flex-col items-center text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <h2 className="text-2xl sm:text-3xl font-bold mb-2">Request Submitted Successfully!</h2>
          <p className="text-gray-600 max-w-md">
            Your {requestTypeLabel} has been received and is being processed by our team.
          </p>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <p className="text-sm text-gray-600 mb-2">Your Tracking Number:</p>
          <div className="flex items-center justify-between bg-white border border-gray-200 rounded-md p-3">
            <span className="font-mono text-lg font-semibold">{trackingNumber}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={copyToClipboard}
              className="text-gray-500 hover:text-primary"
            >
              {copied ? (
                <span className="text-green-600 flex items-center">
                  <CheckCircle className="h-4 w-4 mr-1" /> Copied
                </span>
              ) : (
                <span className="flex items-center">
                  <Copy className="h-4 w-4 mr-1" /> Copy
                </span>
              )}
            </Button>
          </div>
        </div>

        <div className="bg-primary/10 p-4 rounded-lg mb-6">
          <h3 className="font-medium mb-2">What happens next?</h3>
          <ul className="text-sm space-y-2">
            <li className="flex items-start">
              <span className="inline-block bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">1</span>
              <span>Our team will review your request and begin working on it.</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">2</span>
              <span>You can check the status of your request anytime using your tracking number.</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2 mt-0.5">3</span>
              <span>We'll also send updates to the email address you provided.</span>
            </li>
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href={`/track?tracking=${trackingNumber}`}>
            <Button variant="default" className="w-full sm:w-auto">
              Track Your Request
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link href="/">
            <Button variant="outline" className="w-full sm:w-auto">
              Return to Home
              <Home className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
