import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { ClientSatisfactionManager } from '@/lib/client-satisfaction-manager';

/**
 * API endpoint for client satisfaction management
 * Handles surveys, responses, metrics, and insights
 */

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile and role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'list';
    const projectId = searchParams.get('projectId');
    const timeframe = searchParams.get('timeframe') || 'month';
    const managerId = searchParams.get('managerId');

    switch (action) {
      case 'metrics':
        // Get satisfaction metrics
        const metricsParams: any = { timeframe };
        
        if (profile.role === 'manager') {
          metricsParams.managerId = user.id;
        } else if (managerId && profile.role === 'admin') {
          metricsParams.managerId = managerId;
        }
        
        if (projectId) {
          metricsParams.projectId = projectId;
        }

        const metricsResult = await ClientSatisfactionManager.getSatisfactionMetrics(metricsParams);
        
        if (!metricsResult.success) {
          return NextResponse.json(
            { error: metricsResult.error || 'Failed to get metrics' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          metrics: metricsResult.metrics
        });

      case 'insights':
        // Generate satisfaction insights
        const insightsParams: any = { timeframe };
        
        if (profile.role === 'manager') {
          insightsParams.managerId = user.id;
        } else if (managerId && profile.role === 'admin') {
          insightsParams.managerId = managerId;
        }

        const insightsResult = await ClientSatisfactionManager.generateSatisfactionInsights(insightsParams);
        
        if (!insightsResult.success) {
          return NextResponse.json(
            { error: insightsResult.error || 'Failed to generate insights' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          insights: insightsResult.insights
        });

      case 'list':
      default:
        // Get satisfaction responses
        let query = supabase
          .from('client_satisfaction')
          .select(`
            *,
            project:projects!inner(title, status),
            client:profiles!client_satisfaction_client_id_fkey(full_name, email),
            manager:profiles!client_satisfaction_manager_id_fkey(full_name, email)
          `);

        // Apply filters based on role
        switch (profile.role) {
          case 'admin':
            // Admins can see all satisfaction data
            break;
          case 'manager':
            // Managers can see satisfaction for their projects
            query = query.eq('manager_id', user.id);
            break;
          case 'client':
            // Clients can see their own satisfaction responses
            query = query.eq('client_id', user.id);
            break;
          default:
            return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
        }

        // Apply additional filters
        if (projectId) {
          query = query.eq('project_id', projectId);
        }
        if (managerId && profile.role === 'admin') {
          query = query.eq('manager_id', managerId);
        }

        const { data: satisfactionData, error } = await query
          .order('completed_at', { ascending: false })
          .limit(50);

        if (error) {
          console.error('Error fetching satisfaction data:', error);
          return NextResponse.json({ error: 'Failed to fetch satisfaction data' }, { status: 500 });
        }

        return NextResponse.json({
          success: true,
          satisfaction: satisfactionData || []
        });
    }

  } catch (error) {
    console.error('Error in client satisfaction GET API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'create_survey':
        const {
          projectId,
          clientId,
          managerId,
          surveyType,
          milestoneId,
          customQuestions
        } = body;

        // Validate required fields
        if (!projectId || !clientId || !surveyType) {
          return NextResponse.json(
            { error: 'Project ID, client ID, and survey type are required' },
            { status: 400 }
          );
        }

        // Create satisfaction survey
        const surveyResult = await ClientSatisfactionManager.createSatisfactionSurvey({
          projectId,
          clientId,
          managerId,
          surveyType,
          milestoneId,
          customQuestions
        });

        if (!surveyResult.success) {
          return NextResponse.json(
            { error: surveyResult.error || 'Failed to create survey' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          surveyId: surveyResult.surveyId,
          message: 'Survey created and sent successfully'
        }, { status: 201 });

      case 'submit_response':
        const {
          surveyId,
          ratings,
          feedback,
          customResponses
        } = body;

        // Validate required fields
        if (!surveyId || !ratings) {
          return NextResponse.json(
            { error: 'Survey ID and ratings are required' },
            { status: 400 }
          );
        }

        // Validate ratings
        const requiredRatings = ['overall_rating', 'communication_rating', 'quality_rating', 'timeline_rating', 'value_rating'];
        for (const rating of requiredRatings) {
          if (!ratings[rating] || ratings[rating] < 1 || ratings[rating] > 10) {
            return NextResponse.json(
              { error: `${rating} must be between 1 and 10` },
              { status: 400 }
            );
          }
        }

        // Submit satisfaction response
        const responseResult = await ClientSatisfactionManager.submitSatisfactionResponse({
          surveyId,
          ratings,
          feedback: feedback || {},
          customResponses
        });

        if (!responseResult.success) {
          return NextResponse.json(
            { error: responseResult.error || 'Failed to submit response' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          satisfaction: responseResult.satisfaction,
          message: 'Response submitted successfully'
        });

      case 'send_survey':
        const {
          projectId: sendProjectId,
          surveyType: sendSurveyType,
          milestoneId: sendMilestoneId
        } = body;

        if (!sendProjectId || !sendSurveyType) {
          return NextResponse.json(
            { error: 'Project ID and survey type are required' },
            { status: 400 }
          );
        }

        // Get project details
        const { data: project } = await supabase
          .from('projects')
          .select('client_id, assigned_manager_id')
          .eq('id', sendProjectId)
          .single();

        if (!project) {
          return NextResponse.json({ error: 'Project not found' }, { status: 404 });
        }

        // Create and send survey
        const sendResult = await ClientSatisfactionManager.createSatisfactionSurvey({
          projectId: sendProjectId,
          clientId: project.client_id,
          managerId: project.assigned_manager_id,
          surveyType: sendSurveyType,
          milestoneId: sendMilestoneId
        });

        if (!sendResult.success) {
          return NextResponse.json(
            { error: sendResult.error || 'Failed to send survey' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          surveyId: sendResult.surveyId,
          message: 'Survey sent to client successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in client satisfaction POST API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
