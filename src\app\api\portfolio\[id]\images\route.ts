import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/portfolio/[id]/images
 * Gets all images for a specific portfolio project
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the images for the project
    const { data: images, error: imagesError } = await supabase
      .from('portfolio_images')
      .select('*')
      .eq('project_id', projectId)
      .order('display_order', { ascending: true });
    
    if (imagesError) {
      console.error('Error fetching portfolio images:', imagesError);
      return NextResponse.json(
        { error: 'Failed to fetch portfolio images' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(images, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/portfolio/[id]/images:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/portfolio/[id]/images
 * Adds a new image to a portfolio project
 * 
 * Request body:
 * {
 *   image_url: string;
 *   caption?: string;
 *   display_order?: number;
 *   is_cover?: boolean;
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the project to check ownership
    const { data: project, error: projectError } = await supabase
      .from('portfolio_projects')
      .select('designer_id')
      .eq('id', projectId)
      .single();
    
    if (projectError) {
      return NextResponse.json(
        { error: 'Portfolio project not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to add images to this project
    const isAdmin = profile.role === 'admin';
    const isOwner = project.designer_id === user.id;
    
    if (!isAdmin && !isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to add images to this project' },
        { status: 403 }
      );
    }
    
    const { image_url, caption, display_order, is_cover } = await request.json();
    
    // Validate required fields
    if (!image_url) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }
    
    // If this is a cover image, update any existing cover images
    if (is_cover) {
      await supabase
        .from('portfolio_images')
        .update({ is_cover: false })
        .eq('project_id', projectId)
        .eq('is_cover', true);
    }
    
    // Get the highest display order if not provided
    let finalDisplayOrder = display_order;
    if (finalDisplayOrder === undefined) {
      const { data: maxOrderData } = await supabase
        .from('portfolio_images')
        .select('display_order')
        .eq('project_id', projectId)
        .order('display_order', { ascending: false })
        .limit(1);
      
      finalDisplayOrder = maxOrderData && maxOrderData.length > 0
        ? (maxOrderData[0].display_order || 0) + 1
        : 0;
    }
    
    // Add the image
    const { data, error } = await supabase
      .from('portfolio_images')
      .insert({
        project_id: projectId,
        image_url,
        caption: caption || null,
        display_order: finalDisplayOrder,
        is_cover: is_cover || false
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error adding portfolio image:', error);
      return NextResponse.json(
        { error: 'Failed to add portfolio image' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/portfolio/[id]/images:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
