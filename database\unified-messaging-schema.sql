-- Unified Messaging System Schema
-- This replaces the fragmented messaging tables with a clean, modern approach

-- 1. Conversations Table (Main conversation threads)
CREATE TABLE IF NOT EXISTS conversations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    type VARCHAR(50) NOT NULL DEFAULT 'direct', -- 'direct', 'project', 'group'
    title VARCHAR(255), -- For project conversations or group chats
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE, -- For project-based conversations
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- 2. Conversation Participants (Who's in each conversation)
CREATE TABLE IF NOT EXISTS conversation_participants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member', -- 'admin', 'member'
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_muted BOOLEAN DEFAULT FALSE,
    UNIQUE(conversation_id, user_id)
);

-- 3. Messages Table (All messages in conversations)
CREATE TABLE IF NOT EXISTS conversation_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text', -- 'text', 'image', 'file', 'system'
    reply_to_id UUID REFERENCES conversation_messages(id) ON DELETE SET NULL, -- For replies
    edited_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Message Attachments (Files, images, etc.)
CREATE TABLE IF NOT EXISTS message_attachments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID REFERENCES conversation_messages(id) ON DELETE CASCADE,
    file_url TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_type VARCHAR(100),
    file_size BIGINT,
    thumbnail_url TEXT, -- For images/videos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Message Read Status (Who has read what)
CREATE TABLE IF NOT EXISTS message_read_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID REFERENCES conversation_messages(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(message_id, user_id)
);

-- 6. Message Reactions (Like WhatsApp reactions)
CREATE TABLE IF NOT EXISTS message_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID REFERENCES conversation_messages(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    emoji VARCHAR(10) NOT NULL, -- '👍', '❤️', '😂', etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(message_id, user_id, emoji)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversations_project_id ON conversations(project_id);
CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_user_id ON conversation_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_participants_conversation_id ON conversation_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_conversation_id ON conversation_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_sender_id ON conversation_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_message_read_status_user_id ON message_read_status(user_id);
CREATE INDEX IF NOT EXISTS idx_message_read_status_message_id ON message_read_status(message_id);

-- Row Level Security (RLS) Policies
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_read_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_reactions ENABLE ROW LEVEL SECURITY;

-- Conversations: Users can only see conversations they're part of
CREATE POLICY "Users can view their conversations" ON conversations
    FOR SELECT USING (
        id IN (
            SELECT conversation_id 
            FROM conversation_participants 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their conversations" ON conversations
    FOR UPDATE USING (
        id IN (
            SELECT conversation_id 
            FROM conversation_participants 
            WHERE user_id = auth.uid()
        )
    );

-- Conversation Participants: Users can see participants in their conversations
CREATE POLICY "Users can view conversation participants" ON conversation_participants
    FOR SELECT USING (
        conversation_id IN (
            SELECT conversation_id 
            FROM conversation_participants 
            WHERE user_id = auth.uid()
        )
    );

-- Messages: Users can only see messages in conversations they're part of
CREATE POLICY "Users can view conversation messages" ON conversation_messages
    FOR SELECT USING (
        conversation_id IN (
            SELECT conversation_id 
            FROM conversation_participants 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages in their conversations" ON conversation_messages
    FOR INSERT WITH CHECK (
        conversation_id IN (
            SELECT conversation_id 
            FROM conversation_participants 
            WHERE user_id = auth.uid()
        ) AND sender_id = auth.uid()
    );

-- Message Attachments: Users can see attachments in messages they can see
CREATE POLICY "Users can view message attachments" ON message_attachments
    FOR SELECT USING (
        message_id IN (
            SELECT id FROM conversation_messages
            WHERE conversation_id IN (
                SELECT conversation_id 
                FROM conversation_participants 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Read Status: Users can manage their own read status
CREATE POLICY "Users can manage their read status" ON message_read_status
    FOR ALL USING (user_id = auth.uid());

-- Reactions: Users can see and manage reactions in their conversations
CREATE POLICY "Users can view message reactions" ON message_reactions
    FOR SELECT USING (
        message_id IN (
            SELECT id FROM conversation_messages
            WHERE conversation_id IN (
                SELECT conversation_id 
                FROM conversation_participants 
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can manage their reactions" ON message_reactions
    FOR ALL USING (user_id = auth.uid());

-- Functions for automatic conversation creation
CREATE OR REPLACE FUNCTION create_project_conversation(
    p_project_id UUID,
    p_client_id UUID,
    p_designer_id UUID
) RETURNS UUID AS $$
DECLARE
    conversation_id UUID;
BEGIN
    -- Create conversation
    INSERT INTO conversations (type, project_id, created_by)
    VALUES ('project', p_project_id, p_client_id)
    RETURNING id INTO conversation_id;
    
    -- Add participants
    INSERT INTO conversation_participants (conversation_id, user_id, role)
    VALUES 
        (conversation_id, p_client_id, 'admin'),
        (conversation_id, p_designer_id, 'member');
    
    RETURN conversation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update conversation timestamp when new message is added
CREATE OR REPLACE FUNCTION update_conversation_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE conversations 
    SET 
        updated_at = NOW(),
        last_message_at = NOW()
    WHERE id = NEW.conversation_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update conversation timestamp
CREATE TRIGGER update_conversation_on_message
    AFTER INSERT ON conversation_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_timestamp();

-- Function to automatically create conversation when project is assigned
CREATE OR REPLACE FUNCTION auto_create_project_conversation()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create conversation if both client and designer are assigned
    IF NEW.client_id IS NOT NULL AND NEW.designer_id IS NOT NULL THEN
        -- Check if conversation already exists
        IF NOT EXISTS (
            SELECT 1 FROM conversations 
            WHERE project_id = NEW.id AND type = 'project'
        ) THEN
            PERFORM create_project_conversation(NEW.id, NEW.client_id, NEW.designer_id);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to create conversation when project gets both client and designer
CREATE TRIGGER auto_create_conversation_on_project_assignment
    AFTER INSERT OR UPDATE ON projects
    FOR EACH ROW
    EXECUTE FUNCTION auto_create_project_conversation();
