// This script can be run to generate initial invoices from existing milestones
// Run with: node scripts/generate-initial-invoices.js

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Generate invoice number
const generateInvoiceNumber = (prefix = 'INV') => {
  const randomDigits = Math.floor(10000 + Math.random() * 90000); // 5-digit random number
  const timestamp = Date.now().toString().slice(-4); // Last 4 digits of timestamp
  return `${prefix}-${timestamp}${randomDigits}`;
};

// Main function to generate invoices
async function generateInitialInvoices() {
  try {
    console.log('Fetching approved milestones...');
    
    // Get all approved milestones that have been paid
    const { data: milestones, error: milestonesError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        project_id,
        title,
        description,
        amount,
        status,
        paid_at,
        projects(
          id,
          title,
          client_id
        )
      `)
      .eq('status', 'paid')
      .not('paid_at', 'is', null);
    
    if (milestonesError) {
      throw milestonesError;
    }
    
    console.log(`Found ${milestones.length} paid milestones`);
    
    // Create invoices for each milestone
    const invoices = [];
    
    for (const milestone of milestones) {
      const projectId = milestone.project_id;
      const clientId = milestone.projects?.client_id;
      
      if (!clientId) {
        console.warn(`Skipping milestone ${milestone.id} - no client ID found`);
        continue;
      }
      
      const invoiceData = {
        invoice_number: generateInvoiceNumber(),
        amount: milestone.amount,
        status: 'paid',
        due_date: new Date(milestone.paid_at).toISOString(),
        issued_date: new Date(new Date(milestone.paid_at).getTime() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days before paid_at
        description: `Payment for milestone: ${milestone.title} (${milestone.projects?.title})`,
        project_id: projectId,
        client_id: clientId
      };
      
      invoices.push(invoiceData);
    }
    
    // Get all approved milestones that have not been paid yet
    const { data: pendingMilestones, error: pendingError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        project_id,
        title,
        description,
        amount,
        status,
        approved_at,
        projects(
          id,
          title,
          client_id
        )
      `)
      .eq('status', 'approved')
      .is('paid_at', null);
    
    if (pendingError) {
      throw pendingError;
    }
    
    console.log(`Found ${pendingMilestones.length} pending approved milestones`);
    
    // Create pending invoices
    for (const milestone of pendingMilestones) {
      const projectId = milestone.project_id;
      const clientId = milestone.projects?.client_id;
      
      if (!clientId) {
        console.warn(`Skipping milestone ${milestone.id} - no client ID found`);
        continue;
      }
      
      const now = new Date();
      const dueDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
      
      const invoiceData = {
        invoice_number: generateInvoiceNumber(),
        amount: milestone.amount,
        status: 'pending',
        due_date: dueDate.toISOString(),
        issued_date: now.toISOString(),
        description: `Payment for milestone: ${milestone.title} (${milestone.projects?.title})`,
        project_id: projectId,
        client_id: clientId
      };
      
      invoices.push(invoiceData);
    }
    
    // Insert all invoices
    if (invoices.length > 0) {
      console.log(`Inserting ${invoices.length} invoices...`);
      
      const { data: insertedInvoices, error: insertError } = await supabase
        .from('invoices')
        .insert(invoices)
        .select();
      
      if (insertError) {
        throw insertError;
      }
      
      console.log(`Successfully inserted ${insertedInvoices.length} invoices`);
    } else {
      console.log('No invoices to insert');
    }
    
    console.log('Done!');
  } catch (error) {
    console.error('Error generating invoices:', error);
  }
}

// Run the script
generateInitialInvoices()
  .then(() => process.exit(0))
  .catch(err => {
    console.error('Script failed:', err);
    process.exit(1);
  });
