'use client';

import { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { getSkills, updateSkills } from '@/lib/api/designer';
import { SKILL_CATEGORIES, PREDEFINED_SKILLS, SkillCategory } from '@/types/portfolio';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusIcon, XIcon, CheckIcon, SearchIcon } from 'lucide-react';

interface SkillsSelectorProps {
  onSkillsChange?: (skills: string[]) => void;
  readOnly?: boolean;
  designerId?: string;
}

export function SkillsSelector({
  onSkillsChange,
  readOnly = false,
  designerId
}: SkillsSelectorProps) {
  const { token, profile } = useAuth();
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeCategory, setActiveCategory] = useState<SkillCategory>('Architecture');
  const [searchQuery, setSearchQuery] = useState('');
  const [customSkill, setCustomSkill] = useState('');
  const [allPredefinedSkills, setAllPredefinedSkills] = useState<string[]>([]);

  useEffect(() => {
    // Flatten all predefined skills into a single array for search
    const flattenedSkills = Object.values(PREDEFINED_SKILLS).flat();
    setAllPredefinedSkills(flattenedSkills);
  }, []);

  useEffect(() => {
    if (!token) return;

    const fetchSkills = async () => {
      setLoading(true);
      try {
        const data = await getSkills(token, designerId);
        setSelectedSkills(data.skills || []);

        if (onSkillsChange) {
          onSkillsChange(data.skills || []);
        }
      } catch (error) {
        console.error('Error fetching skills:', error);
        toast({
          title: 'Error',
          description: 'Failed to load skills',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSkills();
  }, [token, designerId, onSkillsChange]);

  const handleSaveSkills = async () => {
    if (!token || readOnly) return;

    setSaving(true);
    try {
      const data = await updateSkills(token, selectedSkills);
      setSelectedSkills(data.skills || []);

      if (onSkillsChange) {
        onSkillsChange(data.skills || []);
      }

      toast({
        title: 'Skills Updated',
        description: 'Your skills have been updated successfully',
      });
    } catch (error) {
      console.error('Error updating skills:', error);
      toast({
        title: 'Error',
        description: 'Failed to update skills',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const toggleSkill = (skill: string) => {
    if (readOnly) return;

    setSelectedSkills(prev => {
      const isSelected = prev.includes(skill);

      if (isSelected) {
        return prev.filter(s => s !== skill);
      } else {
        if (prev.length >= 20) {
          toast({
            title: 'Maximum Skills Reached',
            description: 'You can select a maximum of 20 skills',
            variant: 'destructive',
          });
          return prev;
        }
        return [...prev, skill];
      }
    });
  };

  const addCustomSkill = () => {
    if (!customSkill.trim() || readOnly) return;

    const formattedSkill = customSkill.trim();

    if (selectedSkills.includes(formattedSkill)) {
      toast({
        title: 'Skill Already Added',
        description: 'This skill is already in your list',
        variant: 'destructive',
      });
      return;
    }

    if (selectedSkills.length >= 20) {
      toast({
        title: 'Maximum Skills Reached',
        description: 'You can select a maximum of 20 skills',
        variant: 'destructive',
      });
      return;
    }

    setSelectedSkills(prev => [...prev, formattedSkill]);
    setCustomSkill('');
  };

  const filteredSkills = searchQuery
    ? allPredefinedSkills.filter(skill =>
        skill.toLowerCase().includes(searchQuery.toLowerCase()))
    : PREDEFINED_SKILLS[activeCategory];

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="flex flex-wrap gap-2">
          {[1, 2, 3, 4, 5].map(i => (
            <Skeleton key={i} className="h-8 w-20" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label>Selected Skills ({selectedSkills.length}/20)</Label>
        <div className="flex flex-wrap gap-2 min-h-[50px] p-2 border rounded-md">
          {selectedSkills.length === 0 ? (
            <p className="text-sm text-muted-foreground">No skills selected</p>
          ) : (
            selectedSkills.map(skill => (
              <Badge key={skill} variant="secondary" className="flex items-center gap-1">
                {skill}
                {!readOnly && (
                  <button
                    type="button"
                    onClick={() => toggleSkill(skill)}
                    className="ml-1 h-4 w-4 rounded-full hover:bg-destructive/20"
                    aria-label={`Remove ${skill}`}
                  >
                    <XIcon className="h-3 w-3" />
                  </button>
                )}
              </Badge>
            ))
          )}
        </div>
      </div>

      {!readOnly && (
        <>
          <div className="space-y-2">
            <Label>Add Custom Skill</Label>
            <div className="flex gap-2">
              <Input
                value={customSkill}
                onChange={(e) => setCustomSkill(e.target.value)}
                placeholder="Enter a custom skill"
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addCustomSkill();
                  }
                }}
              />
              <Button
                type="button"
                onClick={addCustomSkill}
                size="sm"
                disabled={!customSkill.trim()}
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Select from Predefined Skills</Label>
            <div className="flex items-center space-x-2 mb-4">
              <SearchIcon className="h-4 w-4 text-muted-foreground" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search skills..."
                className="flex-1"
              />
            </div>

            {!searchQuery && (
              <Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as SkillCategory)}>
                <TabsList className="grid grid-cols-3 md:flex md:flex-wrap">
                  {SKILL_CATEGORIES.map(category => (
                    <TabsTrigger key={category} value={category} className="text-xs md:text-sm">
                      {category}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            )}

            <div className="flex flex-wrap gap-2 mt-4 max-h-[300px] overflow-y-auto p-2 border rounded-md">
              {filteredSkills.length === 0 ? (
                <p className="text-sm text-muted-foreground">No skills found</p>
              ) : (
                filteredSkills.map(skill => {
                  const isSelected = selectedSkills.includes(skill);
                  return (
                    <Badge
                      key={skill}
                      variant={isSelected ? "default" : "outline"}
                      className="cursor-pointer flex items-center gap-1"
                      onClick={() => toggleSkill(skill)}
                    >
                      {skill}
                      {isSelected && <CheckIcon className="h-3 w-3 ml-1" />}
                    </Badge>
                  );
                })
              )}
            </div>
          </div>

          <Button
            type="button"
            onClick={handleSaveSkills}
            disabled={saving}
            className="w-full"
          >
            {saving ? 'Saving...' : 'Save Skills'}
          </Button>
        </>
      )}
    </div>
  );
}
