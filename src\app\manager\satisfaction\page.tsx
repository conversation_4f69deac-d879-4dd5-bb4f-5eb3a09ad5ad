"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Star,
  Users,
  MessageSquare,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  AlertTriangle,
  Eye,
  Plus,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  BarChart3,
  ThumbsUp,
  ThumbsDown,
  Send
} from "lucide-react";

interface ClientFeedback {
  id: string;
  project_id: string;
  client_id: string;
  designer_id: string;
  manager_id: string;
  overall_rating: number;
  communication_rating: number;
  quality_rating: number;
  timeline_rating: number;
  value_rating: number;
  feedback_text: string;
  would_recommend: boolean;
  collected_at: string;
  created_at: string;
  project: {
    title: string;
    status: string;
    budget: number;
  };
  client: {
    full_name: string;
    email: string;
  };
  designer: {
    full_name: string;
  };
}

interface SatisfactionSurvey {
  id: string;
  project_id: string;
  client_id: string;
  survey_type: string;
  status: string;
  sent_at: string;
  completed_at: string | null;
  reminder_count: number;
  project: {
    title: string;
  };
  client: {
    full_name: string;
    email: string;
  };
}

export default function ManagerSatisfactionPage() {
  const { user, profile } = useOptimizedAuth();
  const [feedback, setFeedback] = useState<ClientFeedback[]>([]);
  const [surveys, setSurveys] = useState<SatisfactionSurvey[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'feedback' | 'surveys'>('overview');
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchSatisfactionData();
    }
  }, [user, profile, filter]);

  const fetchSatisfactionData = async () => {
    try {
      // Fetch client satisfaction data for manager's projects
      const { data: feedbackData, error: feedbackError } = await supabase
        .from('client_satisfaction')
        .select(`
          *,
          project:projects(title, status),
          client:profiles!client_satisfaction_client_id_fkey(full_name, email),
          designer:profiles!client_satisfaction_designer_id_fkey(full_name)
        `)
        .eq('manager_id', user?.id)
        .order('created_at', { ascending: false });

      if (feedbackError) throw feedbackError;
      setFeedback(feedbackData || []);

      // For now, set surveys to empty array since satisfaction_surveys table doesn't exist
      // You can create this table later if needed for survey management
      setSurveys([]);

    } catch (error) {
      console.error('Error fetching satisfaction data:', error);
    } finally {
      setLoading(false);
    }
  };

  const sendSatisfactionSurvey = async (projectId: string, clientId: string) => {
    try {
      const { data, error } = await supabase
        .from('satisfaction_surveys')
        .insert({
          project_id: projectId,
          client_id: clientId,
          manager_id: user?.id,
          survey_type: 'post_project',
          status: 'sent',
          sent_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Log manager activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: projectId,
        activity_type: 'satisfaction_survey',
        description: 'Sent client satisfaction survey',
        outcome: 'survey_sent'
      });

      fetchSatisfactionData();
      alert('Satisfaction survey sent successfully!');
    } catch (error) {
      console.error('Error sending survey:', error);
      alert('Error sending survey. Please try again.');
    }
  };

  const sendSurveyReminder = async (surveyId: string) => {
    try {
      const survey = surveys.find(s => s.id === surveyId);
      if (!survey) return;

      const { error } = await supabase
        .from('satisfaction_surveys')
        .update({
          reminder_count: survey.reminder_count + 1,
          last_reminder_at: new Date().toISOString()
        })
        .eq('id', surveyId);

      if (error) throw error;

      fetchSatisfactionData();
      alert('Reminder sent successfully!');
    } catch (error) {
      console.error('Error sending reminder:', error);
      alert('Error sending reminder. Please try again.');
    }
  };

  const getOverallSatisfactionStats = () => {
    if (feedback.length === 0) return { average: 0, total: 0, distribution: {} };

    const total = feedback.length;
    const average = feedback.reduce((sum, f) => sum + f.overall_satisfaction, 0) / total;

    const distribution = feedback.reduce((acc, f) => {
      const rating = Math.floor(f.overall_satisfaction);
      acc[rating] = (acc[rating] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    return { average, total, distribution };
  };

  const getRecommendationRate = () => {
    if (feedback.length === 0) return 0;
    const recommendations = feedback.filter(f => f.would_recommend).length;
    return Math.round((recommendations / feedback.length) * 100);
  };

  const getRatingBadge = (rating: number) => {
    if (rating >= 4.5) return "bg-green-100 text-green-800 border border-green-200";
    if (rating >= 3.5) return "bg-blue-100 text-blue-800 border border-blue-200";
    if (rating >= 2.5) return "bg-yellow-100 text-yellow-800 border border-yellow-200";
    return "bg-red-100 text-red-800 border border-red-200";
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'sent':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'expired':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const filteredFeedback = feedback.filter(f => {
    const matchesSearch = f.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         f.client?.full_name?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' ||
                         (filter === 'high' && f.overall_satisfaction >= 4) ||
                         (filter === 'medium' && f.overall_satisfaction >= 3 && f.overall_satisfaction < 4) ||
                         (filter === 'low' && f.overall_satisfaction < 3);
    return matchesSearch && matchesFilter;
  });

  const stats = getOverallSatisfactionStats();
  const recommendationRate = getRecommendationRate();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Client Satisfaction</h1>
          <p className="text-gray-600 mt-2">Monitor and improve client satisfaction across projects</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => window.location.href = '/manager/satisfaction/survey/new'}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Send Survey
          </Button>
          <Button
            onClick={fetchSatisfactionData}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Rating</p>
              <p className="text-2xl font-bold text-blue-600">{stats.average.toFixed(1)}/5</p>
              <p className="text-xs text-gray-500 mt-1">
                Based on {stats.total} reviews
              </p>
            </div>
            <Star className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Recommendation Rate</p>
              <p className="text-2xl font-bold text-green-600">{recommendationRate}%</p>
              <p className="text-xs text-gray-500 mt-1">
                Would recommend us
              </p>
            </div>
            <ThumbsUp className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Surveys</p>
              <p className="text-2xl font-bold text-amber-600">
                {surveys.filter(s => s.status === 'sent').length}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Awaiting response
              </p>
            </div>
            <MessageSquare className="h-8 w-8 text-amber-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Response Rate</p>
              <p className="text-2xl font-bold text-purple-600">
                {surveys.length > 0
                  ? Math.round((surveys.filter(s => s.status === 'completed').length / surveys.length) * 100)
                  : 0}%
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Survey completion
              </p>
            </div>
            <BarChart3 className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'feedback', label: 'Client Feedback', icon: MessageSquare },
              { id: 'surveys', label: 'Surveys', icon: Send }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-brown-500 text-brown-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Rating Distribution */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Rating Distribution</h3>
                  <div className="space-y-3">
                    {[5, 4, 3, 2, 1].map((rating) => (
                      <div key={rating} className="flex items-center gap-3">
                        <span className="text-sm font-medium text-gray-700 w-8">{rating}★</span>
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{
                              width: `${stats.total > 0 ? ((stats.distribution[rating] || 0) / stats.total) * 100 : 0}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600 w-8">
                          {stats.distribution[rating] || 0}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Recent Trends */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Satisfaction Trends</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                      <div className="flex items-center gap-3">
                        <TrendingUp className="h-5 w-5 text-green-500" />
                        <span className="font-medium text-gray-900">Communication</span>
                      </div>
                      <span className="text-green-600 font-semibold">
                        {feedback.length > 0
                          ? (feedback.reduce((sum, f) => sum + f.communication_rating, 0) / feedback.length).toFixed(1)
                          : '0.0'}/5
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="h-5 w-5 text-blue-500" />
                        <span className="font-medium text-gray-900">Quality</span>
                      </div>
                      <span className="text-blue-600 font-semibold">
                        {feedback.length > 0
                          ? (feedback.reduce((sum, f) => sum + f.quality_rating, 0) / feedback.length).toFixed(1)
                          : '0.0'}/5
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                      <div className="flex items-center gap-3">
                        <Calendar className="h-5 w-5 text-purple-500" />
                        <span className="font-medium text-gray-900">Timeline</span>
                      </div>
                      <span className="text-purple-600 font-semibold">
                        {feedback.length > 0
                          ? (feedback.reduce((sum, f) => sum + f.timeline_rating, 0) / feedback.length).toFixed(1)
                          : '0.0'}/5
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'feedback' && (
            <div className="space-y-6">
              {/* Filters and Search */}
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <Filter className="h-5 w-5 text-gray-400" />
                  <select
                    value={filter}
                    onChange={(e) => setFilter(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  >
                    <option value="all">All Ratings</option>
                    <option value="high">High (4+ stars)</option>
                    <option value="medium">Medium (3-4 stars)</option>
                    <option value="low">Low (&lt; 3 stars)</option>
                  </select>
                </div>

                <div className="flex items-center gap-2 flex-1">
                  <Search className="h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search projects or clients..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
              </div>

              {/* Feedback List */}
              <div className="space-y-4">
                {filteredFeedback.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No client feedback found</p>
                  </div>
                ) : (
                  filteredFeedback.map((feedbackItem) => (
                    <div key={feedbackItem.id} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {feedbackItem.project?.title}
                            </h3>
                            <span className={`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ${getRatingBadge(feedbackItem.overall_satisfaction)}`}>
                              {feedbackItem.overall_satisfaction.toFixed(1)} ★
                            </span>
                            {feedbackItem.would_recommend && (
                              <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-green-100 text-green-800">
                                <ThumbsUp className="h-3 w-3 mr-1" />
                                Recommends
                              </span>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                            <div>
                              <span className="font-medium">Client:</span> {feedbackItem.client?.full_name}
                            </div>
                            <div>
                              <span className="font-medium">Designer:</span> {feedbackItem.designer?.full_name}
                            </div>
                            <div>
                              <span className="font-medium">Budget:</span> ${feedbackItem.project?.budget?.toLocaleString()}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <p className="text-xs text-gray-600">Communication</p>
                              <p className="text-lg font-semibold text-blue-600">{feedbackItem.communication_rating.toFixed(1)}</p>
                            </div>
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <p className="text-xs text-gray-600">Quality</p>
                              <p className="text-lg font-semibold text-green-600">{feedbackItem.quality_rating.toFixed(1)}</p>
                            </div>
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <p className="text-xs text-gray-600">Timeline</p>
                              <p className="text-lg font-semibold text-purple-600">{feedbackItem.timeline_rating.toFixed(1)}</p>
                            </div>
                          </div>

                          {feedbackItem.feedback_text && (
                            <div className="bg-blue-50 rounded-lg p-4">
                              <p className="text-sm text-blue-800">
                                <span className="font-medium">Client Feedback:</span> "{feedbackItem.feedback_text}"
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="flex flex-col gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={() => window.location.href = `/manager/satisfaction/${feedbackItem.id}`}
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </Button>
                          <p className="text-xs text-gray-500 text-center">
                            {new Date(feedbackItem.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {activeTab === 'surveys' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Satisfaction Surveys</h3>
                <Button
                  onClick={() => window.location.href = '/manager/satisfaction/survey/new'}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Send New Survey
                </Button>
              </div>

              <div className="space-y-4">
                {surveys.length === 0 ? (
                  <div className="text-center py-8">
                    <Send className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No surveys sent yet</p>
                    <Button
                      onClick={() => window.location.href = '/manager/satisfaction/survey/new'}
                      className="mt-4 flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Send First Survey
                    </Button>
                  </div>
                ) : (
                  surveys.map((survey) => (
                    <div key={survey.id} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {survey.project?.title}
                            </h3>
                            <span className={getStatusBadge(survey.status)}>
                              {survey.status.toUpperCase()}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                            <div>
                              <span className="font-medium">Client:</span> {survey.client?.full_name}
                            </div>
                            <div>
                              <span className="font-medium">Email:</span> {survey.client?.email}
                            </div>
                            <div>
                              <span className="font-medium">Type:</span> {survey.survey_type.replace('_', ' ')}
                            </div>
                          </div>

                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              <span>Sent: {new Date(survey.sent_at).toLocaleDateString()}</span>
                            </div>
                            {survey.completed_at && (
                              <div className="flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span>Completed: {new Date(survey.completed_at).toLocaleDateString()}</span>
                              </div>
                            )}
                            {survey.reminder_count > 0 && (
                              <span className="text-amber-600">
                                {survey.reminder_count} reminder{survey.reminder_count > 1 ? 's' : ''} sent
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-3">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={() => window.location.href = `/manager/satisfaction/survey/${survey.id}`}
                          >
                            <Eye className="h-4 w-4" />
                            View Survey
                          </Button>

                          {survey.status === 'sent' && (
                            <Button
                              size="sm"
                              className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700"
                              onClick={() => sendSurveyReminder(survey.id)}
                            >
                              <Send className="h-4 w-4" />
                              Send Reminder
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Guidelines */}
      <div className="bg-green-50 rounded-xl p-6 border border-green-200">
        <div className="flex items-start gap-3">
          <Star className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-green-900 mb-2">Client Satisfaction Best Practices</h3>
            <div className="text-green-800 space-y-2">
              <p>• <strong>Regular Check-ins:</strong> Send satisfaction surveys at key project milestones</p>
              <p>• <strong>Quick Response:</strong> Address any concerns or low ratings immediately</p>
              <p>• <strong>Follow-up:</strong> Send reminders for pending surveys within 3-5 days</p>
              <p>• <strong>Continuous Improvement:</strong> Use feedback to improve processes and team performance</p>
              <p>• <strong>Recognition:</strong> Share positive feedback with the team to boost morale</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
