"use client";

import { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { usePerformanceMonitoring } from '@/hooks/usePerformanceMonitoring';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Clock, 
  Database, 
  Zap, 
  AlertTriangle, 
  CheckCircle,
  X,
  BarChart3,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

// Performance debug component for development
export function PerformanceDebug() {
  const [isVisible, setIsVisible] = useState(false);
  const [metrics, setMetrics] = useState<any>({});

  // Safely get query client
  let queryClient;
  try {
    queryClient = useQueryClient();
  } catch (error) {
    // QueryClient not available, skip monitoring
    if (process.env.NODE_ENV === 'development') {
      console.warn('QueryClient not available for performance monitoring');
    }
    return null;
  }

  const { getPerformanceSummary, monitorQueryCache } = usePerformanceMonitoring();

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    const updateMetrics = () => {
      const summary = getPerformanceSummary();
      const cacheStats = monitorQueryCache();
      setMetrics({ ...summary, ...cacheStats });
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 2000);

    // Show/hide with keyboard shortcut
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);

    return () => {
      clearInterval(interval);
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [getPerformanceSummary, monitorQueryCache]);

  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return null;
  }

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (value <= thresholds.warning) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50"
      >
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-sm">Performance Monitor</h3>
          </div>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        <div className="space-y-3 text-xs">
          {/* Page Load Time */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-3 w-3" />
              <span>Page Load</span>
            </div>
            <div className="flex items-center space-x-1">
              {getStatusIcon(metrics.pageLoadTime || 0, { good: 1000, warning: 2000 })}
              <span className={getStatusColor(metrics.pageLoadTime || 0, { good: 1000, warning: 2000 })}>
                {metrics.pageLoadTime ? `${Math.round(metrics.pageLoadTime)}ms` : 'N/A'}
              </span>
            </div>
          </div>

          {/* Query Performance */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Database className="h-3 w-3" />
              <span>Query Time</span>
            </div>
            <div className="flex items-center space-x-1">
              {getStatusIcon(metrics.queryTime || 0, { good: 200, warning: 500 })}
              <span className={getStatusColor(metrics.queryTime || 0, { good: 200, warning: 500 })}>
                {metrics.queryTime ? `${Math.round(metrics.queryTime)}ms` : 'N/A'}
              </span>
            </div>
          </div>

          {/* Cache Hit Rate */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Zap className="h-3 w-3" />
              <span>Cache Hit Rate</span>
            </div>
            <div className="flex items-center space-x-1">
              {metrics.cacheHitRate >= 80 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-yellow-600" />
              )}
              <span className={metrics.cacheHitRate >= 80 ? 'text-green-600' : 'text-yellow-600'}>
                {metrics.cacheHitRate ? `${Math.round(metrics.cacheHitRate)}%` : 'N/A'}
              </span>
            </div>
          </div>

          {/* Query Cache Stats */}
          <div className="border-t pt-2 mt-2">
            <div className="flex items-center space-x-2 mb-1">
              <BarChart3 className="h-3 w-3" />
              <span className="font-medium">Query Cache</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">Total:</span>
                <span className="ml-1 font-medium">{metrics.totalQueries || 0}</span>
              </div>
              <div>
                <span className="text-gray-500">Cached:</span>
                <span className="ml-1 font-medium text-green-600">{metrics.cachedQueries || 0}</span>
              </div>
              <div>
                <span className="text-gray-500">Loading:</span>
                <span className="ml-1 font-medium text-blue-600">{metrics.loadingQueries || 0}</span>
              </div>
              <div>
                <span className="text-gray-500">Errors:</span>
                <span className="ml-1 font-medium text-red-600">{metrics.errorQueries || 0}</span>
              </div>
            </div>
          </div>

          {/* Performance Tips */}
          <div className="border-t pt-2 mt-2">
            <div className="text-xs text-gray-500">
              <p className="mb-1">Tips:</p>
              <ul className="space-y-1 text-xs">
                {metrics.pageLoadTime > 2000 && (
                  <li className="text-red-600">• Page load is slow (&gt;2s)</li>
                )}
                {metrics.cacheHitRate < 70 && (
                  <li className="text-yellow-600">• Low cache hit rate (&lt;70%)</li>
                )}
                {metrics.errorQueries > 0 && (
                  <li className="text-red-600">• {metrics.errorQueries} query errors</li>
                )}
                {metrics.pageLoadTime <= 1000 && metrics.cacheHitRate >= 80 && metrics.errorQueries === 0 && (
                  <li className="text-green-600">• Performance is optimal!</li>
                )}
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-3 pt-2 border-t text-xs text-gray-400">
          Press Ctrl+Shift+P to toggle
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// Performance summary for production
export function PerformanceSummary() {
  const [summary, setSummary] = useState<any>({});

  // Safely handle performance monitoring
  let performanceMonitoring;
  try {
    performanceMonitoring = usePerformanceMonitoring();
  } catch (error) {
    // Performance monitoring not available
    return null;
  }

  const { getPerformanceSummary } = performanceMonitoring;

  useEffect(() => {
    const updateSummary = () => {
      setSummary(getPerformanceSummary());
    };

    updateSummary();
    const interval = setInterval(updateSummary, 10000); // Every 10 seconds

    return () => clearInterval(interval);
  }, [getPerformanceSummary]);

  // Only show performance issues in production
  if (process.env.NODE_ENV === 'development') return null;

  const hasIssues = summary.pageLoadTime > 3000 || summary.errorRate > 5;

  if (!hasIssues) return null;

  return (
    <div className="fixed top-4 right-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3 max-w-sm z-40">
      <div className="flex items-center space-x-2">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <span className="text-sm font-medium text-yellow-800">Performance Notice</span>
      </div>
      <div className="mt-1 text-xs text-yellow-700">
        {summary.pageLoadTime > 3000 && <p>Page loading slower than expected</p>}
        {summary.errorRate > 5 && <p>Some features may be experiencing issues</p>}
      </div>
    </div>
  );
}
