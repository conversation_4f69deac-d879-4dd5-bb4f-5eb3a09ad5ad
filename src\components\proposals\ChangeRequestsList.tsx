"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";
import {
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  Clock,
  FileText,
  DollarSign,
  Edit,
  MessageSquare,
  User,
  RefreshCw,
} from "lucide-react";
import { motion } from "framer-motion";
import { formatDistanceToNow } from "date-fns";

type ChangeRequest = {
  id: string;
  section: string;
  description: string;
  status: string;
  response: string | null;
  created_at: string;
  updated_at: string;
  requested_by: string;
  requester_name: string;
  requester_avatar: string | null;
  responded_at: string | null;
  responded_by: string | null;
  responder_name: string | null;
};

type ChangeRequestsListProps = {
  proposalId: string;
  userRole: "client" | "designer" | "admin";
  onRequestChanges?: () => void;
};

export default function ChangeRequestsList({
  proposalId,
  userRole,
  onRequestChanges,
}: ChangeRequestsListProps) {
  const { user } = useAuth();
  const [changeRequests, setChangeRequests] = useState<ChangeRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [respondingTo, setRespondingTo] = useState<string | null>(null);
  const [response, setResponse] = useState("");
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (user && proposalId) {
      fetchChangeRequests();
    }
  }, [user, proposalId]);

  const fetchChangeRequests = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("proposal_change_requests")
        .select(`
          id,
          section,
          description,
          status,
          response,
          created_at,
          updated_at,
          requested_by,
          requester:profiles!proposal_change_requests_requested_by_fkey(full_name, avatar_url),
          responded_at,
          responded_by,
          responder:profiles!proposal_change_requests_responded_by_fkey(full_name)
        `)
        .or(`proposal_id.eq.${proposalId},enhanced_proposal_id.eq.${proposalId}`)
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Transform the data
      const transformedRequests = data.map((request) => ({
        id: request.id,
        section: request.section,
        description: request.description,
        status: request.status,
        response: request.response,
        created_at: request.created_at,
        updated_at: request.updated_at,
        requested_by: request.requested_by,
        requester_name: request.requester?.full_name || "Unknown User",
        requester_avatar: request.requester?.avatar_url,
        responded_at: request.responded_at,
        responded_by: request.responded_by,
        responder_name: request.responder?.full_name || null,
      }));

      setChangeRequests(transformedRequests);
    } catch (error) {
      console.error("Error fetching change requests:", error);
      setError("Failed to load change requests");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitResponse = async (requestId: string, accept: boolean) => {
    if (!user || !response.trim()) return;

    setSubmitting(true);
    setError(null);

    try {
      const status = accept ? "accepted" : "rejected";
      
      // Update the change request
      const { error } = await supabase
        .from("proposal_change_requests")
        .update({
          status,
          response: response.trim(),
          responded_by: user.id,
          responded_at: new Date().toISOString(),
        })
        .eq("id", requestId);

      if (error) throw error;

      // If accepted, update the proposal status
      if (accept) {
        // Try to update enhanced proposal first
        const { error: enhancedProposalError } = await supabase
          .from("project_proposals_enhanced")
          .update({
            status: "under_review",
          })
          .eq("id", proposalId);

        // If that fails, try regular proposal
        if (enhancedProposalError) {
          const { error: proposalError } = await supabase
            .from("project_proposals")
            .update({
              status: "revision_in_progress",
            })
            .eq("id", proposalId);

          if (proposalError) throw proposalError;
        }
      }

      // Get the requester ID to send notification
      const request = changeRequests.find((r) => r.id === requestId);
      if (request) {
        // Create notification for requester
        const { error: notificationError } = await supabase
          .from("notifications")
          .insert({
            user_id: request.requested_by,
            type: "proposal",
            title: `Change Request ${accept ? "Accepted" : "Rejected"}`,
            content: `Your change request has been ${
              accept ? "accepted" : "rejected"
            }. ${response.trim()}`,
            related_id: proposalId,
            read: false,
          });

        if (notificationError) throw notificationError;
      }

      // Reset form and refresh data
      setResponse("");
      setRespondingTo(null);
      await fetchChangeRequests();
    } catch (error) {
      console.error("Error responding to change request:", error);
      setError("Failed to submit response. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const getSectionIcon = (section: string) => {
    switch (section) {
      case "scope":
        return <FileText className="h-5 w-5" />;
      case "timeline":
        return <Clock className="h-5 w-5" />;
      case "budget":
        return <DollarSign className="h-5 w-5" />;
      case "milestones":
        return <Edit className="h-5 w-5" />;
      default:
        return <MessageSquare className="h-5 w-5" />;
    }
  };

  const getSectionName = (section: string): string => {
    const sections: Record<string, string> = {
      scope: "Scope of Work",
      timeline: "Project Timeline",
      budget: "Budget",
      milestones: "Payment Milestones",
      other: "Other Details",
    };
    return sections[section] || section;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </span>
        );
      case "accepted":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Accepted
          </span>
        );
      case "rejected":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </span>
        );
      case "implemented":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Implemented
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return "Unknown date";
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-lg font-semibold flex items-center">
          <RefreshCw className="h-5 w-5 mr-2" />
          Change Requests
        </h2>
        {userRole === "client" && onRequestChanges && (
          <Button
            onClick={onRequestChanges}
            className="bg-brown-600 hover:bg-brown-700 text-white"
          >
            Request Changes
          </Button>
        )}
      </div>

      <div className="p-4">
        {error && (
          <div className="bg-red-50 border border-red-200 p-3 rounded-md mb-4 flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        ) : changeRequests.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <RefreshCw className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p>No change requests yet.</p>
            {userRole === "client" && onRequestChanges && (
              <Button
                onClick={onRequestChanges}
                variant="outline"
                className="mt-4"
              >
                Request Changes
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {changeRequests.map((request) => (
              <div
                key={request.id}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <div className="bg-gray-50 p-3 flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="mr-3 p-2 bg-gray-100 rounded-full">
                      {getSectionIcon(request.section)}
                    </div>
                    <div>
                      <h3 className="font-medium">
                        Changes to {getSectionName(request.section)}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500 mt-1">
                        <div className="flex items-center mr-3">
                          {request.requester_avatar ? (
                            <img
                              src={request.requester_avatar}
                              alt={request.requester_name}
                              className="h-4 w-4 rounded-full mr-1"
                            />
                          ) : (
                            <User className="h-4 w-4 mr-1" />
                          )}
                          <span>{request.requester_name}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>{formatDate(request.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>{getStatusBadge(request.status)}</div>
                </div>
                <div className="p-4">
                  <div className="text-gray-700 whitespace-pre-line">
                    {request.description}
                  </div>

                  {request.response && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <h4 className="text-sm font-medium mb-2">Response:</h4>
                      <div className="text-gray-700 whitespace-pre-line">
                        {request.response}
                      </div>
                      {request.responded_at && (
                        <div className="text-xs text-gray-500 mt-2">
                          Responded by {request.responder_name}{" "}
                          {formatDate(request.responded_at)}
                        </div>
                      )}
                    </div>
                  )}

                  {userRole === "designer" &&
                    request.status === "pending" &&
                    respondingTo !== request.id && (
                      <div className="mt-4 pt-4 border-t border-gray-200 flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setRespondingTo(request.id)}
                          className="text-sm"
                        >
                          Respond
                        </Button>
                      </div>
                    )}

                  {respondingTo === request.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      transition={{ duration: 0.2 }}
                      className="mt-4 pt-4 border-t border-gray-200"
                    >
                      <h4 className="text-sm font-medium mb-2">Your Response:</h4>
                      <textarea
                        value={response}
                        onChange={(e) => setResponse(e.target.value)}
                        placeholder="Enter your response..."
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                        rows={3}
                      />
                      <div className="flex justify-end space-x-2 mt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setRespondingTo(null)}
                          disabled={submitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSubmitResponse(request.id, false)}
                          disabled={!response.trim() || submitting}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          {submitting ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 mr-1" />
                              Reject
                            </>
                          )}
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleSubmitResponse(request.id, true)}
                          disabled={!response.trim() || submitting}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          {submitting ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <>
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Accept
                            </>
                          )}
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
