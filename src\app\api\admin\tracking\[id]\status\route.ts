import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

/**
 * API route for updating tracking request status
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Status update API called for ID:', params.id);
    
    const body = await request.json();
    const { status, priority, adminNotes } = body;

    console.log('Status update request:', { status, priority, adminNotes: !!adminNotes });

    // Validate required fields
    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Validate status values
    const validStatuses = ['new', 'assigned', 'in_progress', 'completed', 'cancelled', 'archived'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate priority values if provided
    const validPriorities = ['low', 'normal', 'high', 'urgent'];
    if (priority && !validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: `Invalid priority. Must be one of: ${validPriorities.join(', ')}` },
        { status: 400 }
      );
    }

    try {
      // Prepare update data
      const updateData: any = {
        internal_status: status,
        updated_at: new Date().toISOString()
      };

      if (priority) {
        updateData.priority = priority;
      }

      if (adminNotes !== undefined) {
        updateData.admin_notes = adminNotes;
      }

      // Set completion date if status is completed
      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString();
      }

      console.log('Updating tracking request with data:', updateData);

      const { data, error } = await supabaseServerClient
        .from('tracking_requests')
        .update(updateData)
        .eq('id', params.id)
        .select()
        .single();

      if (error) {
        console.error('Database update error:', error);
        return NextResponse.json(
          { error: 'Failed to update tracking request', details: error.message },
          { status: 500 }
        );
      }

      if (!data) {
        return NextResponse.json(
          { error: 'Tracking request not found' },
          { status: 404 }
        );
      }

      console.log('Status update successful:', data.id);

      return NextResponse.json({
        success: true,
        data: data,
        message: 'Status updated successfully'
      });

    } catch (dbError) {
      console.error('Database error during status update:', dbError);
      
      // If database is not available, return mock success for development
      if (process.env.NODE_ENV === 'development') {
        console.warn('Database not available, returning mock success');
        return NextResponse.json({
          success: true,
          data: {
            id: params.id,
            internal_status: status,
            priority: priority || 'normal',
            admin_notes: adminNotes || '',
            updated_at: new Date().toISOString(),
            completed_at: status === 'completed' ? new Date().toISOString() : null
          },
          message: 'Status updated successfully (mock)',
          mock: true
        });
      }

      return NextResponse.json(
        { error: 'Database operation failed', details: dbError instanceof Error ? dbError.message : 'Unknown error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in status update API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * GET method to retrieve current status
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Getting status for tracking request:', params.id);

    const { data, error } = await supabaseServerClient
      .from('tracking_requests')
      .select('id, status, internal_status, priority, admin_notes, updated_at, completed_at')
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Database query error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch tracking request', details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Tracking request not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data
    });

  } catch (error) {
    console.error('Error in status GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
