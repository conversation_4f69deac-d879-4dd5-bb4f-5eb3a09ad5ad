import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * PATCH /api/milestones/[id]
 * Updates a milestone's status
 * 
 * Request body:
 * {
 *   status: string; // The new status for the milestone
 *   userId: string; // ID of the user updating the milestone
 *   feedback?: string; // Optional feedback for the milestone
 *   deliverableUrl?: string; // Optional URL for the milestone deliverable
 * }
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const milestoneId = params.id;
    const { status, userId, feedback, deliverableUrl } = await request.json();
    
    if (!milestoneId) {
      return NextResponse.json(
        { error: 'Milestone ID is required' },
        { status: 400 }
      );
    }
    
    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Get the current milestone
    const { data: milestone, error: milestoneError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        status,
        project_id,
        projects (
          client_id,
          designer_id
        )
      `)
      .eq('id', milestoneId)
      .single();
    
    if (milestoneError) {
      return NextResponse.json(
        { error: 'Failed to fetch milestone' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to update this milestone
    const isAdmin = profile.role === 'admin';
    const isDesigner = profile.role === 'designer' && userId === milestone.projects.designer_id;
    const isClient = profile.role === 'client' && userId === milestone.projects.client_id;
    
    if (!isAdmin && !isDesigner && !isClient) {
      return NextResponse.json(
        { error: 'You do not have permission to update this milestone' },
        { status: 403 }
      );
    }
    
    // Prepare the update data
    const updateData: any = {
      status
    };
    
    // Add additional fields based on the status
    if (status === 'completed') {
      updateData.completed_at = new Date().toISOString();
      updateData.completed_by = userId;
      
      if (deliverableUrl) {
        updateData.deliverable_url = deliverableUrl;
      }
    } else if (status === 'approved') {
      updateData.approved_at = new Date().toISOString();
      updateData.approved_by = userId;
    } else if (status === 'revision') {
      // Increment the revision count
      updateData.revision_count = supabase.rpc('increment_revision_count', { milestone_id: milestoneId });
      
      if (feedback) {
        updateData.feedback = feedback;
      }
    }
    
    // Update the milestone
    const { data, error } = await supabase
      .from('project_milestones')
      .update(updateData)
      .eq('id', milestoneId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating milestone:', error);
      return NextResponse.json(
        { error: 'Failed to update milestone' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ milestone: data }, { status: 200 });
  } catch (error) {
    console.error('Error updating milestone:', error);
    return NextResponse.json(
      { error: 'Failed to update milestone' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/milestones/[id]
 * Gets a milestone by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const milestoneId = params.id;
    
    if (!milestoneId) {
      return NextResponse.json(
        { error: 'Milestone ID is required' },
        { status: 400 }
      );
    }
    
    const { data, error } = await supabase
      .from('project_milestones')
      .select(`
        *,
        projects (
          id,
          title,
          client_id,
          designer_id,
          status
        )
      `)
      .eq('id', milestoneId)
      .single();
    
    if (error) {
      console.error('Error fetching milestone:', error);
      return NextResponse.json(
        { error: 'Failed to fetch milestone' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ milestone: data }, { status: 200 });
  } catch (error) {
    console.error('Error fetching milestone:', error);
    return NextResponse.json(
      { error: 'Failed to fetch milestone' },
      { status: 500 }
    );
  }
}
