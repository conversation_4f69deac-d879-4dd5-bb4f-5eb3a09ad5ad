"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";

export default function DesignerDashboard() {
  const router = useRouter();
  const { user, loading } = useOptimizedAuth();

  useEffect(() => {
    if (!loading) {
      // Redirect to the new dashboard route
      router.replace("/designer/dashboard");
    }
  }, [loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return null;
}
