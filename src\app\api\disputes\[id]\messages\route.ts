import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/disputes/[id]/messages
 * Creates a new message in a dispute
 * 
 * Request body:
 * {
 *   content: string;
 *   attachmentUrl?: string;
 *   attachmentName?: string;
 *   attachmentType?: string;
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const disputeId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the dispute to verify the user is involved
    const { data: dispute, error: disputeError } = await supabase
      .from('disputes')
      .select('client_id, designer_id, status')
      .eq('id', disputeId)
      .single();
    
    if (disputeError) {
      return NextResponse.json(
        { error: 'Dispute not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to add messages to this dispute
    const isAdmin = profile.role === 'admin';
    const isInvolved = dispute.client_id === user.id || dispute.designer_id === user.id;
    
    if (!isAdmin && !isInvolved) {
      return NextResponse.json(
        { error: 'You do not have permission to add messages to this dispute' },
        { status: 403 }
      );
    }
    
    // Check if the dispute is closed
    if (dispute.status === 'closed' && !isAdmin) {
      return NextResponse.json(
        { error: 'This dispute is closed and cannot receive new messages' },
        { status: 400 }
      );
    }
    
    const { content, attachmentUrl, attachmentName, attachmentType } = await request.json();
    
    // Validate required fields
    if (!content || content.trim() === '') {
      return NextResponse.json(
        { error: 'Message content is required' },
        { status: 400 }
      );
    }
    
    // Create the message
    const { data: message, error: messageError } = await supabase
      .from('dispute_messages')
      .insert({
        dispute_id: disputeId,
        sender_id: user.id,
        content: content.trim(),
        attachment_url: attachmentUrl || null,
        attachment_name: attachmentName || null,
        attachment_type: attachmentType || null,
        is_read: false
      })
      .select(`
        id,
        dispute_id,
        sender_id,
        content,
        is_read,
        attachment_url,
        attachment_name,
        attachment_type,
        created_at,
        profiles:sender_id (
          full_name,
          avatar_url,
          role
        )
      `)
      .single();
    
    if (messageError) {
      console.error('Error creating dispute message:', messageError);
      return NextResponse.json(
        { error: 'Failed to create message' },
        { status: 500 }
      );
    }
    
    // Create notifications for other parties
    const notifyUserIds: string[] = [];
    
    // Always notify admins if the sender is not an admin
    if (!isAdmin) {
      const { data: admins } = await supabase
        .from('profiles')
        .select('id')
        .eq('role', 'admin');
      
      if (admins && admins.length > 0) {
        notifyUserIds.push(...admins.map(admin => admin.id));
      }
    }
    
    // Notify the other party (client or designer)
    if (user.id === dispute.client_id) {
      notifyUserIds.push(dispute.designer_id);
    } else if (user.id === dispute.designer_id) {
      notifyUserIds.push(dispute.client_id);
    } else if (isAdmin) {
      // If admin is sending, notify both client and designer
      notifyUserIds.push(dispute.client_id, dispute.designer_id);
    }
    
    // Remove duplicates and the sender
    const uniqueNotifyUserIds = [...new Set(notifyUserIds)].filter(id => id !== user.id);
    
    if (uniqueNotifyUserIds.length > 0) {
      const notifications = uniqueNotifyUserIds.map(userId => ({
        user_id: userId,
        type: 'dispute',
        title: 'New Dispute Message',
        content: `New message in dispute: ${content.length > 50 ? content.substring(0, 50) + '...' : content}`,
        related_id: disputeId,
        read: false
      }));
      
      await supabase
        .from('notifications')
        .insert(notifications);
    }
    
    return NextResponse.json(message, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/disputes/[id]/messages:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/disputes/[id]/messages
 * Gets all messages for a dispute
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const disputeId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Get the dispute to verify the user is involved
    const { data: dispute, error: disputeError } = await supabase
      .from('disputes')
      .select('client_id, designer_id')
      .eq('id', disputeId)
      .single();
    
    if (disputeError) {
      return NextResponse.json(
        { error: 'Dispute not found' },
        { status: 404 }
      );
    }
    
    // Check if the user has permission to view messages for this dispute
    const isAdmin = profile.role === 'admin';
    const isInvolved = dispute.client_id === user.id || dispute.designer_id === user.id;
    
    if (!isAdmin && !isInvolved) {
      return NextResponse.json(
        { error: 'You do not have permission to view messages for this dispute' },
        { status: 403 }
      );
    }
    
    // Get the messages
    const { data: messages, error: messagesError } = await supabase
      .from('dispute_messages')
      .select(`
        id,
        dispute_id,
        sender_id,
        content,
        is_read,
        attachment_url,
        attachment_name,
        attachment_type,
        created_at,
        profiles:sender_id (
          full_name,
          avatar_url,
          role
        )
      `)
      .eq('dispute_id', disputeId)
      .order('created_at', { ascending: true });
    
    if (messagesError) {
      console.error('Error fetching dispute messages:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      );
    }
    
    // Mark messages as read if the user is not the sender
    const unreadMessageIds = messages
      .filter(message => message.sender_id !== user.id && !message.is_read)
      .map(message => message.id);
    
    if (unreadMessageIds.length > 0) {
      await supabase
        .from('dispute_messages')
        .update({ is_read: true })
        .in('id', unreadMessageIds);
    }
    
    return NextResponse.json(messages, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/disputes/[id]/messages:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
