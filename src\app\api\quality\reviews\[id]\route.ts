import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/quality/reviews/[id]
 * Get a specific quality review with full details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get review with all related data
    const { data: review, error } = await supabase
      .from('quality_reviews_new')
      .select(`
        *,
        projects:project_id (
          title,
          description,
          status,
          client_id,
          profiles:client_id (
            full_name,
            avatar_url,
            email
          )
        ),
        designer:designer_id (
          full_name,
          avatar_url,
          email,
          skills
        ),
        reviewer:reviewer_id (
          full_name,
          avatar_url
        ),
        project_milestones:milestone_id (
          title,
          description,
          amount,
          status
        ),
        project_submissions:submission_id (
          files,
          description,
          submitted_at,
          status
        ),
        quality_feedback (
          id,
          standard_id,
          passed,
          score,
          comments,
          suggestions,
          quality_standards:standard_id (
            standard_name,
            description,
            category,
            criteria,
            weight
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching quality review:', error);
      return NextResponse.json({ error: 'Review not found' }, { status: 404 });
    }

    // Check permissions
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    const canAccess = profile?.role === 'admin' || 
                     review.reviewer_id === user.id || 
                     review.designer_id === user.id ||
                     profile?.role === 'manager';

    if (!canAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json(review);

  } catch (error) {
    console.error('Error in GET /api/quality/reviews/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT /api/quality/reviews/[id]
 * Update a quality review (submit review, approve, reject, etc.)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const {
      status,
      overall_score,
      feedback,
      revision_notes,
      standards_feedback,
      time_spent_minutes
    } = await request.json();

    // Check if user can update this review
    const { data: review } = await supabase
      .from('quality_reviews_new')
      .select('reviewer_id, designer_id, project_id, status as current_status')
      .eq('id', id)
      .single();

    if (!review) {
      return NextResponse.json({ error: 'Review not found' }, { status: 404 });
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    const canUpdate = profile?.role === 'admin' || 
                     (review.reviewer_id === user.id && profile?.role === 'quality_team');

    if (!canUpdate) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Update review
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (status) {
      updateData.status = status;
      if (status !== 'pending') {
        updateData.reviewed_at = new Date().toISOString();
      }
    }

    if (overall_score) updateData.overall_score = overall_score;
    if (feedback) updateData.feedback = feedback;
    if (revision_notes) updateData.revision_notes = revision_notes;
    if (time_spent_minutes) updateData.time_spent_minutes = time_spent_minutes;

    // Handle revision count
    if (status === 'needs_revision') {
      updateData.revision_count = (review.current_status === 'needs_revision' ? 
        await getRevisionCount(id) + 1 : 1);
    }

    const { data: updatedReview, error: updateError } = await supabase
      .from('quality_reviews_new')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating quality review:', updateError);
      return NextResponse.json({ error: 'Failed to update review' }, { status: 500 });
    }

    // Handle standards feedback if provided
    if (standards_feedback && Array.isArray(standards_feedback)) {
      // Delete existing feedback
      await supabase
        .from('quality_feedback')
        .delete()
        .eq('review_id', id);

      // Insert new feedback
      const feedbackData = standards_feedback.map(feedback => ({
        review_id: id,
        standard_id: feedback.standard_id,
        passed: feedback.passed,
        score: feedback.score,
        comments: feedback.comments,
        suggestions: feedback.suggestions
      }));

      await supabase
        .from('quality_feedback')
        .insert(feedbackData);
    }

    // Create notifications based on status
    await createReviewNotifications(updatedReview, status);

    return NextResponse.json(updatedReview);

  } catch (error) {
    console.error('Error in PUT /api/quality/reviews/[id]:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to get current revision count
async function getRevisionCount(reviewId: string): Promise<number> {
  const { data } = await supabase
    .from('quality_reviews_new')
    .select('revision_count')
    .eq('id', reviewId)
    .single();

  return data?.revision_count || 0;
}

// Helper function to create notifications
async function createReviewNotifications(review: any, status: string) {
  const notifications = [];

  if (status === 'approved') {
    // Notify designer
    notifications.push({
      recipient_id: review.designer_id,
      notification_type: 'quality_approved',
      title: 'Quality Review Approved',
      message: 'Your submission has passed quality review and is approved.',
      priority: 'normal',
      metadata: { review_id: review.id, project_id: review.project_id }
    });

    // Notify manager if assigned
    if (review.project?.assigned_manager_id) {
      notifications.push({
        recipient_id: review.project.assigned_manager_id,
        notification_type: 'quality_approved',
        title: 'Quality Review Completed',
        message: 'A submission in your managed project has been approved.',
        priority: 'normal',
        metadata: { review_id: review.id, project_id: review.project_id }
      });
    }
  } else if (status === 'needs_revision') {
    // Notify designer
    notifications.push({
      recipient_id: review.designer_id,
      notification_type: 'quality_revision_required',
      title: 'Revision Required',
      message: 'Your submission requires revisions based on quality review feedback.',
      priority: 'high',
      metadata: { review_id: review.id, project_id: review.project_id }
    });
  } else if (status === 'rejected') {
    // Notify designer and escalate to manager
    notifications.push({
      recipient_id: review.designer_id,
      notification_type: 'quality_rejected',
      title: 'Submission Rejected',
      message: 'Your submission has been rejected. Please review feedback and resubmit.',
      priority: 'urgent',
      metadata: { review_id: review.id, project_id: review.project_id }
    });
  }

  if (notifications.length > 0) {
    await supabase
      .from('workflow_notifications')
      .insert(notifications);
  }
}
