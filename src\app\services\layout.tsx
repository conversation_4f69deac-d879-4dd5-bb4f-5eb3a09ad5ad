import { Metadata } from 'next';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

export const metadata: Metadata = generateSEOMetadata({
  title: "Architectural Services",
  description: "Comprehensive architectural services including innovative design, interior planning, urban development, and sustainable solutions. Explore our full range of professional architectural services.",
  path: "/services",
  keywords: [
    "architectural services",
    "building design services",
    "interior design",
    "urban planning",
    "landscape architecture",
    "sustainable design",
    "residential architecture",
    "commercial architecture",
    "architectural consultation",
    "design visualization"
  ]
});

export default function ServicesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
