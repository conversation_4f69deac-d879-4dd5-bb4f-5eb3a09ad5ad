'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Zap, 
  Database, 
  Clock, 
  TrendingUp, 
  Activity,
  Server,
  BarChart3,
  RefreshCw
} from 'lucide-react';

interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalEntries: number;
  memoryUsage: number;
}

interface PerformanceMetricsProps {
  cacheStats: CacheStats;
  lastRefresh: Date | null;
}

interface SystemPerformance {
  response_time_ms: number;
  cache_hit_rate: number;
  active_connections: number;
  memory_usage_mb: number;
  cpu_usage_percent: number;
  database_queries_per_second: number;
  error_rate_percent: number;
  uptime_hours: number;
}

export default function PerformanceMetrics({ cacheStats, lastRefresh }: PerformanceMetricsProps) {
  const [systemPerf, setSystemPerf] = useState<SystemPerformance | null>(null);
  const [loading, setLoading] = useState(false);
  const [expanded, setExpanded] = useState(false);

  // Fetch system performance data
  const fetchSystemPerformance = async () => {
    try {
      setLoading(true);
      
      // Simulate system performance data (in real app, this would come from monitoring service)
      const mockData: SystemPerformance = {
        response_time_ms: Math.random() * 200 + 50, // 50-250ms
        cache_hit_rate: cacheStats.hitRate,
        active_connections: Math.floor(Math.random() * 100) + 20,
        memory_usage_mb: Math.random() * 512 + 256, // 256-768MB
        cpu_usage_percent: Math.random() * 30 + 10, // 10-40%
        database_queries_per_second: Math.random() * 50 + 10,
        error_rate_percent: Math.random() * 2, // 0-2%
        uptime_hours: Math.random() * 720 + 24 // 24-744 hours
      };
      
      setSystemPerf(mockData);
    } catch (error) {
      console.error('Error fetching system performance:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemPerformance();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemPerformance, 30000);
    return () => clearInterval(interval);
  }, []);

  // Get performance status color
  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get cache health status
  const getCacheHealthStatus = () => {
    if (cacheStats.hitRate >= 80) return { status: 'Excellent', color: 'bg-green-500' };
    if (cacheStats.hitRate >= 60) return { status: 'Good', color: 'bg-blue-500' };
    if (cacheStats.hitRate >= 40) return { status: 'Fair', color: 'bg-yellow-500' };
    return { status: 'Poor', color: 'bg-red-500' };
  };

  const cacheHealth = getCacheHealthStatus();

  if (!expanded) {
    return (
      <Card className="border-l-4 border-l-green-500">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-green-500" />
                <span className="font-medium">Performance Optimized</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Database className="h-4 w-4 text-blue-500" />
                <span className="text-sm">Cache: {Math.round(cacheStats.hitRate)}% hit rate</span>
              </div>
              
              {systemPerf && (
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-purple-500" />
                  <span className="text-sm">Response: {Math.round(systemPerf.response_time_ms)}ms</span>
                </div>
              )}
              
              {lastRefresh && (
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    Updated: {lastRefresh.toLocaleTimeString()}
                  </span>
                </div>
              )}
            </div>
            
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setExpanded(true)}
            >
              View Details
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Performance Metrics</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchSystemPerformance}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setExpanded(false)}
            >
              Collapse
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Cache Performance */}
        <div>
          <h3 className="font-semibold mb-3 flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>Cache Performance</span>
            <Badge className={cacheHealth.color}>{cacheHealth.status}</Badge>
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{cacheStats.hits}</p>
              <p className="text-sm text-gray-600">Cache Hits</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{cacheStats.misses}</p>
              <p className="text-sm text-gray-600">Cache Misses</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{Math.round(cacheStats.hitRate)}%</p>
              <p className="text-sm text-gray-600">Hit Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{cacheStats.totalEntries}</p>
              <p className="text-sm text-gray-600">Total Entries</p>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-1">
              <span>Cache Hit Rate</span>
              <span>{Math.round(cacheStats.hitRate)}%</span>
            </div>
            <Progress value={cacheStats.hitRate} className="h-2" />
          </div>
          
          <div className="mt-2">
            <div className="flex justify-between text-sm mb-1">
              <span>Memory Usage</span>
              <span>{Math.round(cacheStats.memoryUsage / 1024)} KB</span>
            </div>
            <Progress value={Math.min((cacheStats.memoryUsage / (1024 * 1024)) * 100, 100)} className="h-2" />
          </div>
        </div>

        {/* System Performance */}
        {systemPerf && (
          <div>
            <h3 className="font-semibold mb-3 flex items-center space-x-2">
              <Server className="h-4 w-4" />
              <span>System Performance</span>
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className={`text-2xl font-bold ${getStatusColor(systemPerf.response_time_ms, { good: 100, warning: 200 })}`}>
                  {Math.round(systemPerf.response_time_ms)}ms
                </p>
                <p className="text-sm text-gray-600">Response Time</p>
              </div>
              
              <div className="text-center">
                <p className={`text-2xl font-bold ${getStatusColor(systemPerf.cpu_usage_percent, { good: 20, warning: 50 })}`}>
                  {Math.round(systemPerf.cpu_usage_percent)}%
                </p>
                <p className="text-sm text-gray-600">CPU Usage</p>
              </div>
              
              <div className="text-center">
                <p className={`text-2xl font-bold ${getStatusColor(systemPerf.memory_usage_mb, { good: 300, warning: 500 })}`}>
                  {Math.round(systemPerf.memory_usage_mb)}MB
                </p>
                <p className="text-sm text-gray-600">Memory Usage</p>
              </div>
              
              <div className="text-center">
                <p className={`text-2xl font-bold ${getStatusColor(systemPerf.error_rate_percent, { good: 0.5, warning: 1 })}`}>
                  {systemPerf.error_rate_percent.toFixed(2)}%
                </p>
                <p className="text-sm text-gray-600">Error Rate</p>
              </div>
            </div>
            
            <div className="mt-4 space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>CPU Usage</span>
                  <span>{Math.round(systemPerf.cpu_usage_percent)}%</span>
                </div>
                <Progress value={systemPerf.cpu_usage_percent} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Memory Usage</span>
                  <span>{Math.round(systemPerf.memory_usage_mb)}MB / 1GB</span>
                </div>
                <Progress value={(systemPerf.memory_usage_mb / 1024) * 100} className="h-2" />
              </div>
            </div>
          </div>
        )}

        {/* Performance Insights */}
        <div>
          <h3 className="font-semibold mb-3 flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Performance Insights</span>
          </h3>
          
          <div className="space-y-2">
            {cacheStats.hitRate >= 80 && (
              <div className="flex items-center space-x-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Excellent cache performance - data loading is optimized</span>
              </div>
            )}
            
            {systemPerf && systemPerf.response_time_ms < 100 && (
              <div className="flex items-center space-x-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Fast response times - under 100ms average</span>
              </div>
            )}
            
            {systemPerf && systemPerf.error_rate_percent < 0.5 && (
              <div className="flex items-center space-x-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Low error rate - system stability is excellent</span>
              </div>
            )}
            
            {cacheStats.hitRate < 60 && (
              <div className="flex items-center space-x-2 text-yellow-600">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-sm">Cache hit rate could be improved - consider longer TTL</span>
              </div>
            )}
            
            {systemPerf && systemPerf.response_time_ms > 200 && (
              <div className="flex items-center space-x-2 text-red-600">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm">High response times detected - check database performance</span>
              </div>
            )}
          </div>
        </div>

        {/* Last Updated */}
        {lastRefresh && (
          <div className="text-center text-sm text-gray-500 pt-4 border-t">
            Last updated: {lastRefresh.toLocaleString()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
