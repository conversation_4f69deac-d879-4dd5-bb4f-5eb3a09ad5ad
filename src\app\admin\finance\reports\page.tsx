"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { 
  ArrowLeft, 
  Download, 
  Calendar, 
  BarChart2, 
  <PERSON><PERSON>hart, 
  TrendingUp,
  Filter,
  RefreshC<PERSON>,
  Printer,
  AlertCircle
} from "lucide-react";

type ReportData = {
  totalRevenue: number;
  totalPayouts: number;
  netProfit: number;
  pendingPayments: number;
  completedProjects: number;
  activeProjects: number;
  monthlyRevenue: { month: string; amount: number }[];
  projectTypeDistribution: { type: string; count: number }[];
  topDesigners: { name: string; revenue: number; projects: number }[];
  topClients: { name: string; spent: number; projects: number }[];
};

export default function FinancialReports() {
  const { user } = useOptimizedAuth();
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<'month' | 'quarter' | 'year' | 'all'>('month');
  const [startDate, setStartDate] = useState<string>(
    new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );

  useEffect(() => {
    if (user) {
      fetchReportData();
    }
  }, [user, dateRange, startDate, endDate]);

  const fetchReportData = async () => {
    setLoading(true);
    try {
      // Calculate date range based on selection
      let rangeStart = startDate;
      let rangeEnd = endDate;

      if (dateRange === 'month') {
        const date = new Date();
        date.setMonth(date.getMonth() - 1);
        rangeStart = date.toISOString().split('T')[0];
        rangeEnd = new Date().toISOString().split('T')[0];
      } else if (dateRange === 'quarter') {
        const date = new Date();
        date.setMonth(date.getMonth() - 3);
        rangeStart = date.toISOString().split('T')[0];
        rangeEnd = new Date().toISOString().split('T')[0];
      } else if (dateRange === 'year') {
        const date = new Date();
        date.setFullYear(date.getFullYear() - 1);
        rangeStart = date.toISOString().split('T')[0];
        rangeEnd = new Date().toISOString().split('T')[0];
      }

      // Fetch transactions for the period
      const { data: transactions, error: transactionsError } = await supabase
        .from('transactions')
        .select(`
          id,
          amount,
          status,
          type,
          created_at,
          client_id,
          designer_id,
          project_id
        `)
        .gte('created_at', rangeStart)
        .lte('created_at', rangeEnd);

      if (transactionsError) throw transactionsError;

      // Fetch projects for the period
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id,
          status,
          type,
          client_id,
          designer_id,
          created_at
        `)
        .gte('created_at', rangeStart)
        .lte('created_at', rangeEnd);

      if (projectsError) throw projectsError;

      // Calculate total revenue (completed payments from clients)
      const totalRevenue = transactions
        .filter(t => t.type === 'payment' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      // Calculate total payouts to designers
      const totalPayouts = transactions
        .filter(t => t.type === 'payout' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      // Calculate net profit
      const netProfit = totalRevenue - totalPayouts;

      // Calculate pending payments
      const pendingPayments = transactions
        .filter(t => t.status === 'pending')
        .reduce((sum, t) => sum + t.amount, 0);

      // Count completed and active projects
      const completedProjects = projects.filter(p => p.status === 'completed').length;
      const activeProjects = projects.filter(p => ['active', 'in_progress'].includes(p.status)).length;

      // Calculate monthly revenue
      const monthlyRevenue = calculateMonthlyRevenue(transactions);

      // Calculate project type distribution
      const projectTypeDistribution = calculateProjectTypeDistribution(projects);

      // Get top designers and clients
      const [topDesigners, topClients] = await Promise.all([
        getTopDesigners(transactions, projects),
        getTopClients(transactions, projects)
      ]);

      setReportData({
        totalRevenue,
        totalPayouts,
        netProfit,
        pendingPayments,
        completedProjects,
        activeProjects,
        monthlyRevenue,
        projectTypeDistribution,
        topDesigners,
        topClients
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching report data:', error);
        setError(error.message || 'Failed to load report data');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const calculateMonthlyRevenue = (transactions: any[]) => {
    const months: Record<string, number> = {};
    
    transactions
      .filter(t => t.type === 'payment' && t.status === 'completed')
      .forEach(t => {
        const date = new Date(t.created_at);
        const monthYear = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
        
        if (!months[monthYear]) {
          months[monthYear] = 0;
        }
        
        months[monthYear] += t.amount;
      });
    
    return Object.entries(months)
      .map(([month, amount]) => ({ month, amount }))
      .sort((a, b) => {
        const [aMonth, aYear] = a.month.split(' ');
        const [bMonth, bYear] = b.month.split(' ');
        
        if (aYear !== bYear) {
          return parseInt(aYear) - parseInt(bYear);
        }
        
        const monthOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return monthOrder.indexOf(aMonth) - monthOrder.indexOf(bMonth);
      });
  };

  const calculateProjectTypeDistribution = (projects: any[]) => {
    const types: Record<string, number> = {};
    
    projects.forEach(p => {
      const type = p.type || 'Unspecified';
      
      if (!types[type]) {
        types[type] = 0;
      }
      
      types[type] += 1;
    });
    
    return Object.entries(types)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);
  };

  const getTopDesigners = async (transactions: any[], projects: any[]) => {
    // Get unique designer IDs
    const designerIds = [...new Set(projects.map(p => p.designer_id).filter(Boolean))];
    
    if (designerIds.length === 0) {
      return [];
    }
    
    // Fetch designer profiles
    const { data: designers, error } = await supabase
      .from('profiles')
      .select('id, full_name')
      .in('id', designerIds);
    
    if (error) throw error;
    
    // Create a map of designer IDs to names
    const designerMap: Record<string, string> = {};
    designers.forEach(d => {
      designerMap[d.id] = d.full_name;
    });
    
    // Calculate revenue and project count for each designer
    const designerStats: Record<string, { revenue: number; projects: number }> = {};
    
    // Count projects per designer
    projects.forEach(p => {
      if (p.designer_id && designerMap[p.designer_id]) {
        if (!designerStats[p.designer_id]) {
          designerStats[p.designer_id] = { revenue: 0, projects: 0 };
        }
        
        designerStats[p.designer_id].projects += 1;
      }
    });
    
    // Calculate revenue per designer
    transactions
      .filter(t => t.type === 'payout' && t.status === 'completed')
      .forEach(t => {
        if (t.designer_id && designerMap[t.designer_id]) {
          if (!designerStats[t.designer_id]) {
            designerStats[t.designer_id] = { revenue: 0, projects: 0 };
          }
          
          designerStats[t.designer_id].revenue += t.amount;
        }
      });
    
    // Convert to array and sort by revenue
    return Object.entries(designerStats)
      .map(([id, stats]) => ({
        name: designerMap[id],
        revenue: stats.revenue,
        projects: stats.projects
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
  };

  const getTopClients = async (transactions: any[], projects: any[]) => {
    // Get unique client IDs
    const clientIds = [...new Set(projects.map(p => p.client_id).filter(Boolean))];
    
    if (clientIds.length === 0) {
      return [];
    }
    
    // Fetch client profiles
    const { data: clients, error } = await supabase
      .from('profiles')
      .select('id, full_name')
      .in('id', clientIds);
    
    if (error) throw error;
    
    // Create a map of client IDs to names
    const clientMap: Record<string, string> = {};
    clients.forEach(c => {
      clientMap[c.id] = c.full_name;
    });
    
    // Calculate spending and project count for each client
    const clientStats: Record<string, { spent: number; projects: number }> = {};
    
    // Count projects per client
    projects.forEach(p => {
      if (p.client_id && clientMap[p.client_id]) {
        if (!clientStats[p.client_id]) {
          clientStats[p.client_id] = { spent: 0, projects: 0 };
        }
        
        clientStats[p.client_id].projects += 1;
      }
    });
    
    // Calculate spending per client
    transactions
      .filter(t => t.type === 'payment' && t.status === 'completed')
      .forEach(t => {
        if (t.client_id && clientMap[t.client_id]) {
          if (!clientStats[t.client_id]) {
            clientStats[t.client_id] = { spent: 0, projects: 0 };
          }
          
          clientStats[t.client_id].spent += t.amount;
        }
      });
    
    // Convert to array and sort by spending
    return Object.entries(clientStats)
      .map(([id, stats]) => ({
        name: clientMap[id],
        spent: stats.spent,
        projects: stats.projects
      }))
      .sort((a, b) => b.spent - a.spent)
      .slice(0, 5);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const exportReportData = () => {
    if (!reportData) return;
    
    const reportTitle = `Financial Report (${dateRange === 'custom' ? `${startDate} to ${endDate}` : dateRange})`;
    
    const reportText = `
${reportTitle}
Generated on: ${new Date().toLocaleDateString()}

SUMMARY
-------
Total Revenue: ${formatCurrency(reportData.totalRevenue)}
Total Payouts: ${formatCurrency(reportData.totalPayouts)}
Net Profit: ${formatCurrency(reportData.netProfit)}
Pending Payments: ${formatCurrency(reportData.pendingPayments)}
Completed Projects: ${reportData.completedProjects}
Active Projects: ${reportData.activeProjects}

MONTHLY REVENUE
--------------
${reportData.monthlyRevenue.map(m => `${m.month}: ${formatCurrency(m.amount)}`).join('\n')}

PROJECT TYPE DISTRIBUTION
------------------------
${reportData.projectTypeDistribution.map(p => `${p.type}: ${p.count} projects`).join('\n')}

TOP DESIGNERS
------------
${reportData.topDesigners.map(d => `${d.name}: ${formatCurrency(d.revenue)} (${d.projects} projects)`).join('\n')}

TOP CLIENTS
----------
${reportData.topClients.map(c => `${c.name}: ${formatCurrency(c.spent)} (${c.projects} projects)`).join('\n')}
`;
    
    const blob = new Blob([reportText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', `financial_report_${dateRange}_${new Date().toISOString().split('T')[0]}.txt`);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const printReport = () => {
    window.print();
  };

  if (loading && !reportData) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Generating financial reports...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <div className="flex items-center">
            <Link href="/admin/finance" className="mr-4">
              <Button variant="ghost" className="p-0 h-auto">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Financial Reports</h1>
          </div>
          <p className="text-gray-500 mt-2">Analyze financial performance and generate reports</p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <Button variant="outline" onClick={printReport} className="flex items-center">
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" onClick={exportReportData} className="flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Report Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex flex-col md:flex-row md:items-end gap-4">
          <div>
            <label htmlFor="dateRange" className="block text-sm font-medium text-gray-700 mb-1">
              Date Range
            </label>
            <select
              id="dateRange"
              className="w-full px-4 py-2 border rounded-md"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as any)}
            >
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="year">Last Year</option>
              <option value="all">All Time</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>
          
          {dateRange === 'custom' && (
            <>
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  id="startDate"
                  className="w-full px-4 py-2 border rounded-md"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  id="endDate"
                  className="w-full px-4 py-2 border rounded-md"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </>
          )}
          
          <Button onClick={fetchReportData} className="flex items-center">
            <RefreshCw className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {reportData && (
        <div className="space-y-8 print:space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 print:grid-cols-3">
            <div className="bg-white rounded-lg shadow-md p-6 print:shadow-none print:border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-gray-500 font-medium">Total Revenue</h3>
                <TrendingUp className="h-6 w-6 text-green-500" />
              </div>
              <p className="text-3xl font-bold">{formatCurrency(reportData.totalRevenue)}</p>
              <div className="mt-2 text-sm text-gray-500">
                <span>Payouts: {formatCurrency(reportData.totalPayouts)}</span>
              </div>
              <div className="mt-1 text-sm font-medium text-green-600">
                Net Profit: {formatCurrency(reportData.netProfit)}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 print:shadow-none print:border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-gray-500 font-medium">Projects</h3>
                <BarChart2 className="h-6 w-6 text-blue-500" />
              </div>
              <p className="text-3xl font-bold">{reportData.completedProjects + reportData.activeProjects}</p>
              <div className="mt-2 text-sm text-gray-500">
                <span>Completed: {reportData.completedProjects}</span>
                <span className="mx-2">•</span>
                <span>Active: {reportData.activeProjects}</span>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 print:shadow-none print:border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-gray-500 font-medium">Pending Payments</h3>
                <Calendar className="h-6 w-6 text-yellow-500" />
              </div>
              <p className="text-3xl font-bold">{formatCurrency(reportData.pendingPayments)}</p>
              <div className="mt-2 text-sm text-gray-500">
                Awaiting processing or approval
              </div>
            </div>
          </div>

          {/* Monthly Revenue Chart */}
          <div className="bg-white rounded-lg shadow-md p-6 print:shadow-none print:border">
            <h2 className="text-lg font-semibold mb-4">Monthly Revenue</h2>
            <div className="h-64">
              {/* In a real implementation, you would use a chart library like Chart.js or Recharts */}
              <div className="h-full flex items-end">
                {reportData.monthlyRevenue.map((month, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div 
                      className="w-full bg-primary bg-opacity-80 rounded-t"
                      style={{ 
                        height: `${(month.amount / Math.max(...reportData.monthlyRevenue.map(m => m.amount))) * 100}%`,
                        minHeight: '4px'
                      }}
                    ></div>
                    <div className="mt-2 text-xs text-gray-500 rotate-45 origin-top-left">
                      {month.month}
                    </div>
                    <div className="mt-1 text-xs font-medium">
                      {formatCurrency(month.amount)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 print:grid-cols-2">
            {/* Project Type Distribution */}
            <div className="bg-white rounded-lg shadow-md p-6 print:shadow-none print:border">
              <h2 className="text-lg font-semibold mb-4">Project Types</h2>
              <div className="space-y-4">
                {reportData.projectTypeDistribution.map((type, index) => (
                  <div key={index}>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">{type.type}</span>
                      <span className="text-sm text-gray-500">{type.count} projects</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary rounded-full h-2" 
                        style={{ 
                          width: `${(type.count / Math.max(...reportData.projectTypeDistribution.map(t => t.count))) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Designers */}
            <div className="bg-white rounded-lg shadow-md p-6 print:shadow-none print:border">
              <h2 className="text-lg font-semibold mb-4">Top Designers</h2>
              <div className="space-y-4">
                {reportData.topDesigners.map((designer, index) => (
                  <div key={index} className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                      <span className="text-gray-500 text-sm">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{designer.name}</p>
                      <div className="flex text-sm text-gray-500">
                        <span>{formatCurrency(designer.revenue)}</span>
                        <span className="mx-2">•</span>
                        <span>{designer.projects} projects</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Clients */}
            <div className="bg-white rounded-lg shadow-md p-6 print:shadow-none print:border">
              <h2 className="text-lg font-semibold mb-4">Top Clients</h2>
              <div className="space-y-4">
                {reportData.topClients.map((client, index) => (
                  <div key={index} className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                      <span className="text-gray-500 text-sm">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{client.name}</p>
                      <div className="flex text-sm text-gray-500">
                        <span>{formatCurrency(client.spent)}</span>
                        <span className="mx-2">•</span>
                        <span>{client.projects} projects</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
