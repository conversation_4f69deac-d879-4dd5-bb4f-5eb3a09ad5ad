"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Settings,
  Users,
  Calendar,
  Target,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertTriangle,
  Edit,
  Send,
  Plus,
  FileText,
  BarChart3,
  RefreshCw,
  Flag,
  Play,
  Pause,
  Square
} from "lucide-react";

interface CoordinationSession {
  id: string;
  project_id: string;
  coordination_type: string;
  title: string;
  description: string;
  priority: string;
  scheduled_date: string | null;
  estimated_duration: number;
  participants: string[];
  objectives: string[];
  deliverables: string[];
  notes: string;
  status: string;
  created_at: string;
  updated_at: string;
  project: {
    title: string;
    client: {
      full_name: string;
      email: string;
    };
    designer: {
      full_name: string;
      email: string;
    };
  };
  tasks: CoordinationTask[];
  messages: CoordinationMessage[];
}

interface CoordinationTask {
  id: string;
  title: string;
  description: string;
  assigned_to: string;
  due_date: string;
  priority: string;
  status: string;
  completion_percentage: number;
  created_at: string;
  assignee: {
    full_name: string;
    role: string;
  };
}

interface CoordinationMessage {
  id: string;
  sender_id: string;
  message: string;
  message_type: string;
  created_at: string;
  sender: {
    full_name: string;
    role: string;
  };
}

export default function CoordinationDetailPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const coordinationId = params.id as string;
  
  const [coordination, setCoordination] = useState<CoordinationSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'tasks' | 'messages' | 'progress'>('overview');
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'manager' && coordinationId) {
      fetchCoordinationData();
    }
  }, [user, profile, coordinationId]);

  const fetchCoordinationData = async () => {
    try {
      const { data, error } = await supabase
        .from('coordination_sessions')
        .select(`
          *,
          project:projects(
            title,
            client:profiles!projects_client_id_fkey(full_name, email),
            designer:profiles!projects_designer_id_fkey(full_name, email)
          ),
          tasks:coordination_tasks(
            *,
            assignee:profiles!coordination_tasks_assigned_to_fkey(full_name, role)
          ),
          messages:coordination_messages(
            *,
            sender:profiles!coordination_messages_sender_id_fkey(full_name, role)
          )
        `)
        .eq('id', coordinationId)
        .eq('manager_id', user?.id)
        .single();

      if (error) throw error;
      setCoordination(data);
    } catch (error) {
      console.error('Error fetching coordination data:', error);
      router.push('/manager/coordination');
    } finally {
      setLoading(false);
    }
  };

  const updateCoordinationStatus = async (newStatus: string) => {
    if (!coordination) return;

    try {
      const { error } = await supabase
        .from('coordination_sessions')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', coordinationId);

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: coordination.project_id,
        activity_type: 'coordination_status_update',
        description: `Updated coordination status to ${newStatus}`,
        outcome: newStatus
      });

      fetchCoordinationData();
    } catch (error) {
      console.error('Error updating coordination status:', error);
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !coordination) return;

    setSendingMessage(true);
    try {
      const { error } = await supabase
        .from('coordination_messages')
        .insert({
          coordination_id: coordinationId,
          sender_id: user?.id,
          message: newMessage.trim(),
          message_type: 'text'
        });

      if (error) throw error;

      // Send notifications to participants
      const notifications = coordination.participants
        .filter(participantId => participantId !== user?.id)
        .map(participantId => ({
          user_id: participantId,
          type: 'coordination_message',
          title: 'New Coordination Message',
          message: `New message in coordination session: ${coordination.title}`,
          data: { coordination_id: coordinationId }
        }));

      if (notifications.length > 0) {
        await supabase.from('notifications').insert(notifications);
      }

      setNewMessage('');
      fetchCoordinationData();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSendingMessage(false);
    }
  };

  const updateTaskStatus = async (taskId: string, newStatus: string, completionPercentage?: number) => {
    try {
      const updates: any = { 
        status: newStatus,
        updated_at: new Date().toISOString()
      };
      
      if (completionPercentage !== undefined) {
        updates.completion_percentage = completionPercentage;
      }

      const { error } = await supabase
        .from('coordination_tasks')
        .update(updates)
        .eq('id', taskId);

      if (error) throw error;

      fetchCoordinationData();
    } catch (error) {
      console.error('Error updating task status:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Calendar className="h-5 w-5 text-blue-500" />;
      case 'in_progress':
        return <Play className="h-5 w-5 text-green-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'cancelled':
        return <Square className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'scheduled':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'in_progress':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getTaskProgress = () => {
    if (!coordination?.tasks || coordination.tasks.length === 0) return 0;
    const totalProgress = coordination.tasks.reduce((sum, task) => sum + task.completion_percentage, 0);
    return Math.round(totalProgress / coordination.tasks.length);
  };

  const getCompletedTasks = () => {
    if (!coordination?.tasks) return 0;
    return coordination.tasks.filter(task => task.status === 'completed').length;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!coordination) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Coordination Session Not Found</h2>
          <p className="text-gray-600 mb-4">The coordination session could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/coordination')}>
            Back to Coordination
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <Settings className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{coordination.title}</h1>
            <p className="text-gray-600 mt-1">{coordination.project.title}</p>
          </div>
        </div>
      </div>

      {/* Status and Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            {getStatusIcon(coordination.status)}
            <div>
              <div className="flex items-center gap-3 mb-2">
                <span className={getStatusBadge(coordination.status)}>
                  {coordination.status.replace('_', ' ').toUpperCase()}
                </span>
                <span className={getPriorityBadge(coordination.priority)}>
                  {coordination.priority.toUpperCase()} PRIORITY
                </span>
              </div>
              <p className="text-sm text-gray-600">
                Type: {coordination.coordination_type.replace('_', ' ').toUpperCase()} •
                Created {new Date(coordination.created_at).toLocaleDateString()}
                {coordination.scheduled_date && (
                  <> • Scheduled for {new Date(coordination.scheduled_date).toLocaleDateString()}</>
                )}
              </p>
            </div>
          </div>

          <div className="flex gap-3">
            {coordination.status === 'scheduled' && (
              <Button
                onClick={() => updateCoordinationStatus('in_progress')}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Start Session
              </Button>
            )}

            {coordination.status === 'in_progress' && (
              <>
                <Button
                  variant="outline"
                  onClick={() => updateCoordinationStatus('scheduled')}
                  className="flex items-center gap-2"
                >
                  <Pause className="h-4 w-4" />
                  Pause
                </Button>
                <Button
                  onClick={() => updateCoordinationStatus('completed')}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  Complete
                </Button>
              </>
            )}

            <Button
              variant="outline"
              onClick={() => router.push(`/manager/coordination/${coordinationId}/edit`)}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Task Progress</p>
              <p className="text-2xl font-bold text-blue-600">{getTaskProgress()}%</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Tasks</p>
              <p className="text-2xl font-bold text-purple-600">{coordination.tasks?.length || 0}</p>
            </div>
            <Target className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{getCompletedTasks()}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Participants</p>
              <p className="text-2xl font-bold text-amber-600">{coordination.participants?.length || 0}</p>
            </div>
            <Users className="h-8 w-8 text-amber-500" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: FileText },
              { id: 'tasks', label: 'Tasks', icon: Target },
              { id: 'messages', label: 'Messages', icon: MessageSquare },
              { id: 'progress', label: 'Progress', icon: BarChart3 }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-brown-500 text-brown-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Session Description</h3>
                <p className="text-gray-700">{coordination.description || 'No description provided.'}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Session Objectives</h4>
                  {coordination.objectives && coordination.objectives.length > 0 ? (
                    <ul className="space-y-2">
                      {coordination.objectives.map((objective, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <Target className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{objective}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 text-sm">No objectives defined</p>
                  )}
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Expected Deliverables</h4>
                  {coordination.deliverables && coordination.deliverables.length > 0 ? (
                    <ul className="space-y-2">
                      {coordination.deliverables.map((deliverable, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{deliverable}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 text-sm">No deliverables defined</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'tasks' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Coordination Tasks</h3>
                <Button
                  onClick={() => router.push(`/manager/coordination/${coordinationId}/tasks/new`)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Task
                </Button>
              </div>

              {coordination.tasks && coordination.tasks.length > 0 ? (
                <div className="space-y-4">
                  {coordination.tasks.map((task) => (
                    <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{task.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={getStatusBadge(task.status)}>
                            {task.status.replace('_', ' ').toUpperCase()}
                          </span>
                          <span className={getPriorityBadge(task.priority)}>
                            {task.priority.toUpperCase()}
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          <span>{task.assignee.full_name} ({task.assignee.role})</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <BarChart3 className="h-4 w-4" />
                          <span>Progress: {task.completion_percentage}%</span>
                        </div>
                      </div>

                      <div className="mb-4">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${task.completion_percentage}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newProgress = prompt('Enter completion percentage (0-100):', task.completion_percentage.toString());
                            if (newProgress !== null) {
                              const progress = Math.max(0, Math.min(100, Number(newProgress)));
                              const newStatus = progress === 100 ? 'completed' : progress > 0 ? 'in_progress' : 'pending';
                              updateTaskStatus(task.id, newStatus, progress);
                            }
                          }}
                          className="flex items-center gap-2"
                        >
                          <Edit className="h-4 w-4" />
                          Update Progress
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newStatus = task.status === 'completed' ? 'in_progress' : 'completed';
                            const newProgress = newStatus === 'completed' ? 100 : task.completion_percentage;
                            updateTaskStatus(task.id, newStatus, newProgress);
                          }}
                          className={`flex items-center gap-2 ${
                            task.status === 'completed'
                              ? 'text-amber-600 border-amber-200 hover:bg-amber-50'
                              : 'text-green-600 border-green-200 hover:bg-green-50'
                          }`}
                        >
                          <CheckCircle className="h-4 w-4" />
                          {task.status === 'completed' ? 'Mark Incomplete' : 'Mark Complete'}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No tasks assigned yet</p>
                  <Button
                    onClick={() => router.push(`/manager/coordination/${coordinationId}/tasks/new`)}
                    className="mt-4 flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add First Task
                  </Button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Send Message</h3>
                <form onSubmit={sendMessage} className="space-y-4">
                  <textarea
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type your message to coordination participants..."
                    rows={4}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    required
                  />
                  <Button
                    type="submit"
                    disabled={!newMessage.trim() || sendingMessage}
                    className="flex items-center gap-2"
                  >
                    {sendingMessage ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                    Send Message
                  </Button>
                </form>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Coordination Messages</h3>
                {coordination.messages && coordination.messages.length > 0 ? (
                  <div className="space-y-4">
                    {coordination.messages.map((message) => (
                      <div key={message.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h4 className="font-medium text-gray-900">{message.sender.full_name}</h4>
                            <p className="text-sm text-gray-600 capitalize">({message.sender.role})</p>
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(message.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{message.message}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No messages yet</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'progress' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Task Completion</h4>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Overall Progress</span>
                      <span className="font-bold text-blue-600">{getTaskProgress()}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${getTaskProgress()}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Completed: {getCompletedTasks()}</span>
                      <span>Total: {coordination.tasks?.length || 0}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Session Status</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Current Status:</span>
                      <span className={getStatusBadge(coordination.status)}>
                        {coordination.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Priority:</span>
                      <span className={getPriorityBadge(coordination.priority)}>
                        {coordination.priority.toUpperCase()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span className="text-gray-900">{coordination.estimated_duration} minutes</span>
                    </div>
                  </div>
                </div>
              </div>

              {coordination.tasks && coordination.tasks.length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Task Progress Details</h4>
                  <div className="space-y-3">
                    {coordination.tasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900">{task.title}</h5>
                          <p className="text-sm text-gray-600">{task.assignee.full_name}</p>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${task.completion_percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900 w-12 text-right">
                            {task.completion_percentage}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
