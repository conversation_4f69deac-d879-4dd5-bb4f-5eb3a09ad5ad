"use client";

import { useEffect, useRef, useState } from 'react';
import { Loader2 } from 'lucide-react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { paypalSDK, getOptimizedPayPalConfig } from '@/lib/paypal-sdk';
import { usePayPal } from '@/components/providers/PayPalProvider';

declare global {
  interface Window {
    paypal?: {
      Buttons: (config: any) => {
        render: (container: HTMLElement) => void;
      };
    };
  }
}

interface PayPalButtonProps {
  amount: number;
  description: string;
  projectId: string;
  milestoneId?: string;
  paymentType: 'deposit' | 'milestone' | 'final';
  onSuccess: (details: any) => void;
  onError: (error: Error) => void;
  onCancel: () => void;
}

export function PayPalButton({
  amount,
  description,
  projectId,
  milestoneId,
  paymentType,
  onSuccess,
  onError,
  onCancel
}: PayPalButtonProps) {
  const { user } = useOptimizedAuth();
  const { isLoaded: paypalLoaded, isLoading: paypalLoading, error: paypalError } = usePayPal();
  const paypalRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [buttonRendered, setButtonRendered] = useState(false);

  // Handle PayPal errors from the provider
  useEffect(() => {
    if (paypalError) {
      onError(new Error(paypalError));
    }
  }, [paypalError, onError]);

  // Initialize PayPal button once the script is loaded
  useEffect(() => {
    if (paypalLoaded && paypalRef.current && user && !buttonRendered) {
      setLoading(true);

      try {
        // Clear any existing content in the PayPal button container
        if (paypalRef.current.children.length > 0) {
          paypalRef.current.innerHTML = '';
        }

        window.paypal?.Buttons({
          createOrder: async () => {
            try {
              // Create order on the server
              const response = await fetch('/api/paypal/create-order', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  amount,
                  description,
                  projectId,
                  milestoneId,
                  clientId: user.id,
                  paymentType
                }),
              });

              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to create PayPal order');
              }

              const orderData = await response.json();
              return orderData.id;
            } catch (err) {
              console.error('Error creating PayPal order:', err);
              onError(err instanceof Error ? err : new Error('Failed to create PayPal order'));
              throw err;
            }
          },
          onApprove: async (data: any, actions: any) => {
            try {
              // Capture the funds from the transaction
              const response = await fetch('/api/paypal/capture-order', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  orderId: data.orderID,
                  projectId,
                  milestoneId,
                  clientId: user.id,
                  paymentType
                }),
              });

              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to capture PayPal payment');
              }

              const details = await response.json();

              // Save PayPal as a payment method
              await savePayPalAsPaymentMethod(details);

              // Call onSuccess callback
              onSuccess(details);
              return details;
            } catch (err) {
              console.error('Error capturing PayPal payment:', err);
              onError(err instanceof Error ? err : new Error('Failed to capture PayPal payment'));
              throw err;
            }
          },
          onCancel: () => {
            onCancel();
          },
          onError: (err: Error) => {
            console.error('PayPal error:', err);
            onError(err);
          },
          style: {
            layout: 'horizontal',
            color: 'gold',
            shape: 'rect',
            label: 'paypal',
            height: 40,
            tagline: false
          },
          fundingSource: window.paypal?.FUNDING?.PAYPAL
        }).render(paypalRef.current);
        setButtonRendered(true);
      } catch (error) {
        console.error('Error rendering PayPal buttons:', error);
        onError(error instanceof Error ? error : new Error('Failed to render PayPal buttons'));
      } finally {
        setLoading(false);
      }
    }
  }, [paypalLoaded, amount, description, projectId, milestoneId, paymentType, user, onSuccess, onError, onCancel, buttonRendered]);

  // Save PayPal as a payment method in the database
  const savePayPalAsPaymentMethod = async (details: any) => {
    if (!user) return;

    try {
      // Check if PayPal is already saved as a payment method
      const { data: existingMethods, error: fetchError } = await supabase
        .from('payment_methods')
        .select('id')
        .eq('user_id', user.id)
        .eq('payment_type', 'paypal')
        .eq('paypal_email', details.payer.email_address);

      if (fetchError) {
        console.error('Error checking existing PayPal methods:', fetchError);
        return;
      }

      // If this PayPal account is already saved, don't add it again
      if (existingMethods && existingMethods.length > 0) {
        return;
      }

      // Save the PayPal account as a payment method
      const { error } = await supabase
        .from('payment_methods')
        .insert({
          user_id: user.id,
          payment_type: 'paypal',
          paypal_email: details.payer.email_address,
          paypal_payer_id: details.payer.payer_id,
          is_default: false,
          last_used_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error saving PayPal payment method:', error);
      }
    } catch (err) {
      console.error('Error saving PayPal as payment method:', err);
    }
  };

  return (
    <div className="w-full">
      {(loading || paypalLoading) && (
        <div className="flex justify-center items-center py-4">
          <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
          <span>
            {paypalLoading ? 'Initializing PayPal...' : 'Loading PayPal...'}
          </span>
        </div>
      )}
      <div ref={paypalRef} className={(loading || paypalLoading) ? 'hidden' : ''}></div>
    </div>
  );
}
