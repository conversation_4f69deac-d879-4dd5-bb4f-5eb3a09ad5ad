"use client";

import { useRef, useState } from "react";
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion";
import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";

const reasons = [
  {
    title: "Global Standards, Local Sensitivity",
    description: "We apply international best practices while respecting cultural and environmental contexts.",
    details: "Our team has worked across diverse global contexts, allowing us to bring world-class standards to every project while ensuring designs are culturally appropriate and environmentally responsive to their specific location.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
  {
    title: "Architecture Meets Storytelling",
    description: "Every project tells a unique story, expressing the values and vision of our clients.",
    details: "We believe architecture should communicate meaning. Through thoughtful design choices, spatial narratives, and material selections, we craft environments that tell compelling stories about their purpose, users, and context.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
      </svg>
    )
  },
  {
    title: "Technology-Driven Design",
    description: "Advanced digital tools and innovative processes ensure precision and creativity in every phase.",
    details: "From parametric modeling to virtual reality visualization and building information modeling (BIM), we leverage cutting-edge technologies to enhance design exploration, improve communication, and ensure exceptional execution.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    )
  },
  {
    title: "Quality Craftsmanship",
    description: "We partner with the best builders to ensure exceptional execution of our designs.",
    details: "The realization of architectural vision requires skilled execution. We maintain relationships with exceptional craftspeople and builders who share our commitment to quality, allowing us to deliver projects with impeccable attention to detail.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 13l4 4L19 7" />
      </svg>
    )
  },
  {
    title: "Collaborative, Transparent Process",
    description: "Clients remain informed and involved throughout the entire journey from concept to completion.",
    details: "We believe in demystifying the architectural process. Through regular communication, clear documentation, and collaborative decision-making, we ensure clients are engaged partners throughout the design and construction journey.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    )
  },
];

const WhyChooseUsSection = () => {
  const sectionRef = useRef(null);
  const [activeReason, setActiveReason] = useState<number | null>(null);
  
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.5, 1, 1, 0.5]);
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.98, 1, 1, 0.98]);

  // Staggered animation for reasons
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  return (
    <motion.section 
      ref={sectionRef}
      style={{ opacity, scale }}
      className="py-20 bg-black text-white relative"
    >
      {/* Visual connector from previous section */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[1px] h-20 bg-gradient-to-b from-transparent to-white/30" />
      
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-repeat" style={{ backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' fill=\'none\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Crect width=\'1\' height=\'1\' fill=\'%23FFFFFF\'/%3E%3C/svg%3E")' }} />
        </div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col md:flex-row gap-12">
          <motion.div 
            className="md:w-1/3"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Why Choose Us</h2>
            <motion.div 
              className="bg-primary h-1 w-20 mb-8"
              initial={{ width: 0 }}
              whileInView={{ width: 80 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            />
            <motion.p 
              className="text-lg opacity-80 mb-8"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 0.8 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              At Senior's Archi-firm, we blend artistic insight with technical excellence to create spaces that elevate human experience while honoring context and culture.
            </motion.p>
            <motion.p 
              className="text-lg opacity-80"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 0.8 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
            >
              Our approach is defined by a commitment to quality, innovation, and transformative design that shapes the built environment with intelligence and lasting impact.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              viewport={{ once: true }}
              className="mt-8"
            >
              <Link href="/about">
                <Button variant="outline" className="text-white border-white hover:bg-white hover:text-black group">
                  <span>Learn More</span>
                  <motion.div
                    className="ml-2"
                    initial={{ x: 0 }}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <ArrowRight className="h-4 w-4" />
                  </motion.div>
                </Button>
              </Link>
            </motion.div>
          </motion.div>

          <motion.div 
            className="md:w-2/3"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {reasons.map((reason, index) => (
                <motion.div 
                  key={`reason-${index}`} 
                  className={`border-l-4 border-primary pl-6 p-4 transition-colors duration-300 cursor-pointer ${
                    activeReason === index ? 'bg-white/10' : 'hover:bg-white/5'
                  }`}
                  variants={itemVariants}
                  onClick={() => setActiveReason(activeReason === index ? null : index)}
                >
                  <div className="flex items-center mb-3">
                    <div className="text-primary mr-3">
                      {reason.icon}
                    </div>
                    <h3 className="text-xl font-bold">{reason.title}</h3>
                  </div>
                  <p className="text-gray-300 mb-2">{reason.description}</p>
                  
                  <AnimatePresence>
                    {activeReason === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="mt-4 pt-4 border-t border-white/10">
                          <p className="text-gray-400 text-sm">{reason.details}</p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
        
        {/* Visual connector to next section */}
        <motion.div 
          className="w-[1px] h-20 bg-gradient-to-b from-transparent to-white/30 mx-auto mt-16"
          initial={{ scaleY: 0 }}
          whileInView={{ scaleY: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          viewport={{ once: true }}
        />
      </div>
      
      {/* Visual design elements */}
      <motion.div 
        className="absolute top-20 right-10 w-40 h-40 border border-primary/20 rounded-full opacity-20"
        initial={{ scale: 0, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 0.2 }}
        transition={{ duration: 1.5 }}
        viewport={{ once: true }}
      />
      
      <motion.div 
        className="absolute bottom-20 left-10 w-24 h-24 border border-white/20 rounded-full opacity-10"
        initial={{ scale: 0, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 0.1 }}
        transition={{ duration: 1.5, delay: 0.3 }}
        viewport={{ once: true }}
      />
    </motion.section>
  );
};

export default WhyChooseUsSection;
