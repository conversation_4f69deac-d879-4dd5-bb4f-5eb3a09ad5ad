import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/quality/reviews
 * Get quality reviews for the authenticated quality team member
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is quality team member or admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['quality_team', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Build query
    let query = supabase
      .from('quality_reviews_new')
      .select(`
        id,
        project_id,
        submission_id,
        milestone_id,
        reviewer_id,
        designer_id,
        review_type,
        status,
        overall_score,
        feedback,
        revision_notes,
        revision_count,
        sla_deadline,
        reviewed_at,
        created_at,
        updated_at,
        projects:project_id (
          title,
          status,
          client_id,
          profiles:client_id (
            full_name,
            avatar_url
          )
        ),
        designer:designer_id (
          full_name,
          avatar_url,
          email
        ),
        project_milestones:milestone_id (
          title,
          description,
          amount
        ),
        project_submissions:submission_id (
          files,
          description,
          submitted_at
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by status if specified
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    // Filter by reviewer if not admin
    if (profile.role === 'quality_team') {
      query = query.eq('reviewer_id', user.id);
    }

    const { data: reviews, error } = await query;

    if (error) {
      console.error('Error fetching quality reviews:', error);
      return NextResponse.json({ error: 'Failed to fetch reviews' }, { status: 500 });
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('quality_reviews_new')
      .select('id', { count: 'exact', head: true });

    if (status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    if (profile.role === 'quality_team') {
      countQuery = countQuery.eq('reviewer_id', user.id);
    }

    const { count } = await countQuery;

    // Calculate SLA status for each review
    const reviewsWithSLA = reviews?.map(review => ({
      ...review,
      sla_status: getSLAStatus(review.sla_deadline, review.status)
    }));

    return NextResponse.json({
      reviews: reviewsWithSLA,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in GET /api/quality/reviews:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/quality/reviews
 * Create a new quality review (usually triggered by submission)
 */
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const {
      project_id,
      submission_id,
      milestone_id,
      designer_id,
      review_type = 'submission'
    } = await request.json();

    if (!project_id || !designer_id) {
      return NextResponse.json(
        { error: 'Project ID and Designer ID are required' },
        { status: 400 }
      );
    }

    // Create quality review
    const { data: review, error } = await supabase
      .from('quality_reviews_new')
      .insert({
        project_id,
        submission_id,
        milestone_id,
        designer_id,
        review_type,
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating quality review:', error);
      return NextResponse.json({ error: 'Failed to create review' }, { status: 500 });
    }

    return NextResponse.json(review, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/quality/reviews:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to determine SLA status
function getSLAStatus(slaDeadline: string | null, status: string): string {
  if (!slaDeadline || !['pending', 'in_review'].includes(status)) {
    return 'completed';
  }

  const deadline = new Date(slaDeadline);
  const now = new Date();
  const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);

  if (hoursUntilDeadline < 0) {
    return 'overdue';
  } else if (hoursUntilDeadline < 4) {
    return 'due_soon';
  } else {
    return 'on_time';
  }
}
