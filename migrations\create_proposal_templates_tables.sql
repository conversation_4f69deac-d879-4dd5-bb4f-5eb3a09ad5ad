-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create proposal_templates table
CREATE TABLE IF NOT EXISTS proposal_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  scope_template TEXT,
  timeline_template TEXT,
  project_type TEXT, -- Matches project types in the application
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  is_default BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create proposal_template_milestones table
CREATE TABLE IF NOT EXISTS proposal_template_milestones (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  template_id UUID NOT NULL REFERENCES proposal_templates(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  percentage DECIMAL(5, 2) NOT NULL,
  estimated_days INTEGER,
  deliverables TEXT,
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create proposal_template_sections table for customizable sections
CREATE TABLE IF NOT EXISTS proposal_template_sections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  template_id UUID NOT NULL REFERENCES proposal_templates(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  section_type TEXT NOT NULL, -- 'introduction', 'terms', 'conditions', 'process', etc.
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for proposal_templates table
ALTER TABLE proposal_templates ENABLE ROW LEVEL SECURITY;

-- Admins can do everything with templates
CREATE POLICY "Admins can manage all proposal templates"
  ON proposal_templates
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Designers can view all active templates
CREATE POLICY "Designers can view active proposal templates"
  ON proposal_templates
  FOR SELECT
  USING (
    (is_active = TRUE) AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

-- Designers can view their own templates
CREATE POLICY "Designers can view their own proposal templates"
  ON proposal_templates
  FOR SELECT
  USING (
    created_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

-- Designers can create their own templates
CREATE POLICY "Designers can create their own proposal templates"
  ON proposal_templates
  FOR INSERT
  WITH CHECK (
    created_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

-- Designers can update their own templates
CREATE POLICY "Designers can update their own proposal templates"
  ON proposal_templates
  FOR UPDATE
  USING (
    created_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

-- Add RLS policies for proposal_template_milestones table
ALTER TABLE proposal_template_milestones ENABLE ROW LEVEL SECURITY;

-- Admins can do everything with template milestones
CREATE POLICY "Admins can manage all proposal template milestones"
  ON proposal_template_milestones
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Designers can view milestones for active templates
CREATE POLICY "Designers can view milestones for active templates"
  ON proposal_template_milestones
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM proposal_templates
      WHERE proposal_templates.id = proposal_template_milestones.template_id
      AND (proposal_templates.is_active = TRUE OR proposal_templates.created_by = auth.uid())
    ) AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

-- Designers can create milestones for their own templates
CREATE POLICY "Designers can create milestones for their own templates"
  ON proposal_template_milestones
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM proposal_templates
      WHERE proposal_templates.id = proposal_template_milestones.template_id
      AND proposal_templates.created_by = auth.uid()
    ) AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

-- Add RLS policies for proposal_template_sections table
ALTER TABLE proposal_template_sections ENABLE ROW LEVEL SECURITY;

-- Similar policies for template sections
CREATE POLICY "Admins can manage all proposal template sections"
  ON proposal_template_sections
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Designers can view sections for active templates"
  ON proposal_template_sections
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM proposal_templates
      WHERE proposal_templates.id = proposal_template_sections.template_id
      AND (proposal_templates.is_active = TRUE OR proposal_templates.created_by = auth.uid())
    ) AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

CREATE POLICY "Designers can create sections for their own templates"
  ON proposal_template_sections
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM proposal_templates
      WHERE proposal_templates.id = proposal_template_sections.template_id
      AND proposal_templates.created_by = auth.uid()
    ) AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );
