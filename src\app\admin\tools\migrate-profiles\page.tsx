"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Database,
  RefreshCw,
  Info
} from "lucide-react";

export default function MigrateProfilesPage() {
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [schemaInfo, setSchemaInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Check current schema on page load
  useEffect(() => {
    checkSchema();
  }, []);

  const checkSchema = async () => {
    setChecking(true);
    try {
      const response = await fetch('/api/run-migration/profiles', {
        method: 'GET',
      });

      const data = await response.json();
      setSchemaInfo(data);
    } catch (error) {
      console.error('Schema check error:', error);
    } finally {
      setChecking(false);
    }
  };

  const runMigration = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch('/api/run-migration/profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Migration failed');
      }

      setResults(data);
      // Refresh schema info after migration
      await checkSchema();
    } catch (error) {
      console.error('Migration error:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Migrate Profiles Table</h1>
          <p className="text-gray-600">
            This tool adds missing columns to the profiles table needed for designer applications.
          </p>
        </div>

        {/* Current Schema Status */}
        {schemaInfo && (
          <div className={`rounded-lg p-6 mb-8 ${
            schemaInfo.needsMigration 
              ? 'bg-yellow-50 border border-yellow-200' 
              : 'bg-green-50 border border-green-200'
          }`}>
            <div className="flex items-start">
              {schemaInfo.needsMigration ? (
                <AlertTriangle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5" />
              ) : (
                <CheckCircle className="h-6 w-6 text-green-600 mr-3 mt-0.5" />
              )}
              <div>
                <h3 className={`text-lg font-semibold mb-2 ${
                  schemaInfo.needsMigration ? 'text-yellow-800' : 'text-green-800'
                }`}>
                  {schemaInfo.needsMigration ? 'Migration Required' : 'Schema Up to Date'}
                </h3>
                
                {schemaInfo.needsMigration ? (
                  <div>
                    <p className="text-yellow-700 mb-2">
                      Missing {schemaInfo.missingColumns.length} required columns:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {schemaInfo.missingColumns.map((col: string) => (
                        <span key={col} className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm">
                          {col}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  <p className="text-green-700">
                    All required columns are present in the profiles table.
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Migration Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Database className="h-6 w-6 text-blue-600 mr-3" />
              <div>
                <h3 className="text-lg font-semibold">Run Profiles Migration</h3>
                <p className="text-gray-600 text-sm">
                  Add missing columns needed for designer applications
                </p>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={checkSchema}
                disabled={checking}
                variant="outline"
                className="flex items-center"
              >
                {checking ? (
                  <Loader2 className="animate-spin h-4 w-4 mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Check Schema
              </Button>
              
              <Button
                onClick={runMigration}
                disabled={loading || !schemaInfo?.needsMigration}
                className="flex items-center"
              >
                {loading ? (
                  <Loader2 className="animate-spin h-4 w-4 mr-2" />
                ) : (
                  <Database className="h-4 w-4 mr-2" />
                )}
                {loading ? 'Running Migration...' : 'Run Migration'}
              </Button>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div className="flex items-start">
              <XCircle className="h-6 w-6 text-red-600 mr-3 mt-0.5" />
              <div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  Migration Failed
                </h3>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Results Display */}
        {results && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div className="flex items-start mb-4">
              <CheckCircle className="h-6 w-6 text-green-600 mr-3 mt-0.5" />
              <div>
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  Migration Completed
                </h3>
                <p className="text-green-700">{results.message}</p>
              </div>
            </div>

            {/* Detailed Results */}
            {results.results && (
              <div className="mt-4">
                <h4 className="text-md font-semibold text-gray-800 mb-3">
                  Migration Details
                </h4>
                <div className="max-h-64 overflow-y-auto">
                  <div className="space-y-2">
                    {results.results.map((result: any, index: number) => (
                      <div 
                        key={index} 
                        className={`p-3 rounded border text-sm ${
                          result.status === 'success' 
                            ? 'bg-green-50 border-green-200' 
                            : result.status === 'warning'
                            ? 'bg-yellow-50 border-yellow-200'
                            : 'bg-red-50 border-red-200'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-mono text-xs">{result.statement}</span>
                          <span className={`px-2 py-1 rounded text-xs ${
                            result.status === 'success' 
                              ? 'bg-green-100 text-green-800' 
                              : result.status === 'warning'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {result.status.toUpperCase()}
                          </span>
                        </div>
                        
                        {result.error && (
                          <div className="text-gray-600 text-xs">
                            {result.error}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <Info className="h-6 w-6 text-blue-600 mr-3 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-blue-800 mb-2">
                What This Migration Does
              </h3>
              <ul className="text-blue-700 space-y-1 text-sm">
                <li>• Adds the missing <code>bio</code> column (fixing the current error)</li>
                <li>• Adds <code>location</code> column for designer location info</li>
                <li>• Ensures all designer-related columns exist in profiles table</li>
                <li>• Creates indexes for better performance</li>
                <li>• Safe to run multiple times (uses IF NOT EXISTS)</li>
                <li>• Does not modify existing data</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
