"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Bell,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Users,
  MessageSquare,
  Calendar,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Clock,
  Send,
  Archive
} from "lucide-react";

interface AdminMessage {
  id: string;
  recipient_id: string | null;
  recipient_role: 'designer' | 'client' | 'all';
  title: string;
  content: string;
  message_type: 'info' | 'warning' | 'success' | 'urgent' | 'announcement';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  read_at: string | null;
  action_required: boolean;
  action_url: string | null;
  expires_at: string | null;
  created_by: string;
  created_at: string;
  created_by_name?: string;
  recipient_name?: string;
  read_count?: number;
  total_recipients?: number;
}

export default function AdminMessagesPage() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<AdminMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchMessages();
    }
  }, [user]);

  const fetchMessages = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('admin_messages')
        .select(`
          id,
          recipient_id,
          recipient_role,
          title,
          content,
          message_type,
          priority,
          read_at,
          action_required,
          action_url,
          expires_at,
          created_by,
          created_at,
          created_by_profile:profiles!created_by(full_name),
          recipient_profile:profiles!recipient_id(full_name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform data and add read statistics
      const messagesWithStats = await Promise.all(
        (data || []).map(async (message) => {
          let readCount = 0;
          let totalRecipients = 0;

          if (message.recipient_role === 'all') {
            // Count all users
            const { count: totalCount } = await supabase
              .from('profiles')
              .select('*', { count: 'exact', head: true })
              .neq('role', 'admin');
            totalRecipients = totalCount || 0;

            // Count read messages (this would need a separate read_status table in real implementation)
            readCount = 0; // Placeholder
          } else if (message.recipient_role) {
            // Count users by role
            const { count: roleCount } = await supabase
              .from('profiles')
              .select('*', { count: 'exact', head: true })
              .eq('role', message.recipient_role);
            totalRecipients = roleCount || 0;
          } else {
            // Single recipient
            totalRecipients = 1;
            readCount = message.read_at ? 1 : 0;
          }

          return {
            ...message,
            created_by_name: Array.isArray(message.created_by_profile) 
              ? message.created_by_profile[0]?.full_name 
              : message.created_by_profile?.full_name || 'Unknown',
            recipient_name: Array.isArray(message.recipient_profile)
              ? message.recipient_profile[0]?.full_name
              : message.recipient_profile?.full_name || null,
            read_count: readCount,
            total_recipients: totalRecipients
          };
        })
      );

      setMessages(messagesWithStats);
    } catch (error) {
      console.error('Error fetching admin messages:', error);
      setError('Failed to fetch messages');
    } finally {
      setLoading(false);
    }
  };

  const deleteMessage = async (messageId: string) => {
    if (!confirm('Are you sure you want to delete this message?')) return;

    try {
      const { error } = await supabase
        .from('admin_messages')
        .delete()
        .eq('id', messageId);

      if (error) throw error;

      setSuccess('Message deleted successfully');
      fetchMessages();
    } catch (error) {
      console.error('Error deleting message:', error);
      setError('Failed to delete message');
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'urgent': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'announcement': return <Bell className="h-4 w-4 text-blue-500" />;
      default: return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'urgent': return 'bg-red-50 border-red-200 text-red-800';
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'success': return 'bg-green-50 border-green-200 text-green-800';
      case 'announcement': return 'bg-blue-50 border-blue-200 text-blue-800';
      default: return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || message.message_type === typeFilter;
    const matchesRole = roleFilter === 'all' || message.recipient_role === roleFilter;

    return matchesSearch && matchesType && matchesRole;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Messages</h1>
          <p className="text-gray-600">Manage system messages and announcements</p>
        </div>
        <Link href="/admin/admin-messages/create">
          <Button className="bg-brown-600 hover:bg-brown-700 text-white">
            <Plus className="h-4 w-4 mr-2" />
            Create Message
          </Button>
        </Link>
      </div>

      {/* Alerts */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search messages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Types</option>
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="success">Success</option>
            <option value="urgent">Urgent</option>
            <option value="announcement">Announcement</option>
          </select>

          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Recipients</option>
            <option value="designer">Designers</option>
            <option value="client">Clients</option>
            <option value="all">Everyone</option>
          </select>
        </div>
      </div>

      {/* Messages List */}
      {filteredMessages.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {messages.length === 0 ? 'No messages yet' : 'No messages match your filters'}
          </h3>
          <p className="text-gray-500 mb-4">
            {messages.length === 0
              ? 'Create your first admin message to communicate with users'
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {messages.length === 0 && (
            <Link href="/admin/admin-messages/create">
              <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Create First Message
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredMessages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {getMessageTypeIcon(message.message_type)}
                    <h3 className="text-lg font-semibold text-gray-900">{message.title}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(message.priority)}`}>
                      {message.priority.toUpperCase()}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getMessageTypeColor(message.message_type)}`}>
                      {message.message_type.toUpperCase()}
                    </span>
                  </div>

                  <p className="text-gray-600 mb-4 line-clamp-2">{message.content}</p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2" />
                      <span>
                        {message.recipient_role === 'all' ? 'Everyone' : 
                         message.recipient_role === 'designer' ? 'All Designers' :
                         message.recipient_role === 'client' ? 'All Clients' :
                         message.recipient_name || 'Specific User'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{new Date(message.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-2" />
                      <span>{message.read_count || 0} / {message.total_recipients || 0} read</span>
                    </div>
                  </div>

                  {message.expires_at && (
                    <div className="mt-2 flex items-center text-sm text-orange-600">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>Expires: {new Date(message.expires_at).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2 flex-shrink-0">
                  <Link href={`/admin/admin-messages/${message.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  </Link>
                  <Link href={`/admin/admin-messages/${message.id}/edit`}>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteMessage(message.id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
