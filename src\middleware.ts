import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  // Middleware now only handles basic cookie management
  // All authentication and redirects are handled client-side

  const response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Only handle Supabase cookie management - no authentication logic
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll().map((cookie) => ({
            name: cookie.name,
            value: cookie.value,
          }));
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            response.cookies.set({
              name,
              value,
              ...options,
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax',
              maxAge: 60 * 60 * 24 * 7, // 7 days
            });
          });
        },
      },
    }
  );

  // Just refresh the session to update cookies - no redirects
  try {
    await supabase.auth.getSession();
  } catch (error) {
    // Ignore errors - let client-side handle authentication
  }

  return response;
}

// Enable middleware only for auth callback handling
export const config = {
  matcher: ['/auth/callback'],
};
