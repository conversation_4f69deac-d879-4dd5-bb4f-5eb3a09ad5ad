import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      persistSession: false
    }
  }
);

// Regular client for non-admin operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * API route for approving designer applications
 * Creates user account and sends welcome email
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { approved_by } = await request.json();

    if (!approved_by) {
      return NextResponse.json(
        { error: 'approved_by is required' },
        { status: 400 }
      );
    }

    console.log(`Starting approval process for application ${id}`);

    // Get the application with row-level locking to prevent race conditions
    const { data: application, error: fetchError } = await supabase
      .from('designer_applications')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !application) {
      console.error('Application not found:', fetchError);
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    if (application.application_status !== 'pending') {
      console.log(`Application ${id} already processed with status: ${application.application_status}`);
      return NextResponse.json(
        { error: 'Application has already been processed' },
        { status: 400 }
      );
    }

    // Use a unique processing token to prevent race conditions
    const processingToken = `PROCESSING_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

    // Try to acquire lock by setting notes field (only if still pending)
    const { error: lockError, count } = await supabaseAdmin
      .from('designer_applications')
      .update({
        notes: processingToken
      })
      .eq('id', id)
      .eq('application_status', 'pending')
      .is('notes', null); // Only update if notes is currently null (not being processed)

    if (lockError) {
      console.error('Failed to lock application for processing:', lockError);
      return NextResponse.json(
        { error: 'Failed to lock application for processing' },
        { status: 500 }
      );
    }

    // Check if we successfully acquired the lock
    if (count === 0) {
      console.log('Application is already being processed or not in pending status');
      return NextResponse.json(
        { error: 'Application is being processed by another request or is no longer pending' },
        { status: 409 }
      );
    }

    console.log(`Application ${id} locked for processing with token: ${processingToken}`);

    // Clear the lock when done (success or failure)
    const clearLock = async () => {
      try {
        await supabaseAdmin
          .from('designer_applications')
          .update({ notes: null })
          .eq('id', id)
          .eq('notes', processingToken); // Only clear if it's still our token
      } catch (error) {
        console.error('Error clearing processing lock:', error);
      }
    };

    // Generate temporary password
    const tempPassword = 'TempPass123!';

    try {
      // Verify service role key is available
      if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
        console.error('SUPABASE_SERVICE_ROLE_KEY is not set');
        return NextResponse.json(
          { error: 'Server configuration error - missing service role key' },
          { status: 500 }
        );
      }

      // Check if user already exists in auth system first
      let existingAuthUser = null;
      try {
        // Note: Supabase doesn't have getUserByEmail in admin API, so we'll handle this in the creation step
        console.log(`Checking for existing auth user for ${application.email}...`);
      } catch (authCheckError) {
        console.log(`No existing auth user found for ${application.email}`);
      }

      // Check if user already exists in profiles table
      const { data: existingProfile, error: checkError } = await supabaseAdmin
        .from('profiles')
        .select('id, email, role')
        .eq('email', application.email)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('Error checking existing profiles:', checkError);
        await clearLock();
        return NextResponse.json(
          { error: 'Failed to verify user account status' },
          { status: 500 }
        );
      }

      // Handle different scenarios
      if (existingProfile && existingAuthUser) {
        console.error('User already exists in both auth and profiles with email:', application.email);
        await clearLock();
        return NextResponse.json(
          { error: 'A user account already exists with this email address' },
          { status: 400 }
        );
      }

      if (existingProfile && !existingAuthUser) {
        console.log('Profile exists but no auth user - this is an inconsistent state, cleaning up...');
        // Delete the orphaned profile and continue with normal creation
        await supabaseAdmin
          .from('profiles')
          .delete()
          .eq('id', existingProfile.id);
        console.log('Deleted orphaned profile, will create fresh user');
      }

      // Create auth user (simplified approach since getUserByEmail isn't available)
      console.log('Creating new auth user...');
      const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email: application.email,
        password: tempPassword,
        email_confirm: true,
        user_metadata: {
          full_name: application.full_name,
          temp_password: true
        }
      });

      if (authError) {
        console.error('Error creating auth user:', authError);
        console.error('Auth error details:', {
          message: authError.message,
          status: authError.status,
          code: 'AuthError'
        });

        let errorMessage = 'Failed to create user account';
        if (authError.message.includes('password')) {
          errorMessage = 'Password does not meet security requirements';
        } else if (authError.message.includes('email')) {
          errorMessage = 'Invalid email address format';
        } else if (authError.message.includes('already registered') || authError.message.includes('already exists')) {
          errorMessage = 'A user account already exists with this email address';
        }

        await clearLock();
        return NextResponse.json(
          { error: errorMessage },
          { status: 500 }
        );
      }

      if (!authUser || !authUser.user) {
        console.error('Auth user creation/update returned no user data');
        await clearLock();
        return NextResponse.json(
          { error: 'Failed to create user account - no user data returned' },
          { status: 500 }
        );
      }

      console.log(`Auth user ready: ${authUser.user.id} for email: ${authUser.user.email}`);

      // The Supabase auth trigger automatically creates a basic profile
      // We need to update it with the designer-specific information
      console.log('Updating profile with designer information...');

      // Wait a moment for the trigger to complete (in case of timing issues)
      await new Promise(resolve => setTimeout(resolve, 100));

      // Update the profile that was created by the auth trigger
      const { error: profileError } = await supabaseAdmin
        .from('profiles')
        .update({
          full_name: application.full_name,
          phone: application.phone,
          location: application.location,
          role: 'designer', // Override the default 'client' role from trigger
          specialization: application.specialization,
          experience: application.experience,
          portfolio_url: application.portfolio_url,
          bio: application.bio,
          resume_url: application.resume_url,
          portfolio_files: application.portfolio_files,
          applied_at: application.applied_at,
          approved_by: approved_by,
          temp_password: true,
          application_status: 'approved' // Keep this in sync for consistency
        })
        .eq('id', authUser.user.id);

      if (profileError) {
        console.error('Error updating profile:', profileError);

        // Clean up auth user if profile update fails
        try {
          await supabaseAdmin.auth.admin.deleteUser(authUser.user.id);
          console.log('Cleaned up auth user after profile update failure');
        } catch (cleanupError) {
          console.error('Error cleaning up auth user:', cleanupError);
        }

        await clearLock();
        return NextResponse.json(
          { error: 'Failed to update user profile with designer information' },
          { status: 500 }
        );
      }

      console.log('Profile updated successfully with designer information');

      // Update application status
      const { error: updateError } = await supabase
        .from('designer_applications')
        .update({
          application_status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: approved_by,
          created_user_id: authUser.user.id
        })
        .eq('id', id);

      if (updateError) {
        console.error('Error updating application:', updateError);
        // Don't fail the whole process if this fails
      }

      // Send welcome email
      try {
        await resend.emails.send({
          from: 'Seniors Architecture Firm <<EMAIL>>',
          to: [application.email],
          subject: 'Welcome to Seniors Architecture Firm - Your Application Approved!',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #8B4513;">Congratulations! Your Application Has Been Approved</h2>

              <p>Dear ${application.full_name},</p>

              <p>We're excited to inform you that your application to join Seniors Architecture Firm as a designer has been <strong>approved</strong>!</p>

              <div style="background-color: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #8B4513;">
                <h3 style="margin-top: 0; color: #8B4513;">Your Account Details:</h3>
                <p><strong>Email:</strong> ${application.email}</p>
                <p><strong>Temporary Password:</strong> <code style="background-color: #e8e8e8; padding: 2px 6px; border-radius: 4px;">${tempPassword}</code></p>
                <p><strong>Login URL:</strong> <a href="${process.env.NEXT_PUBLIC_APP_URL}/login">${process.env.NEXT_PUBLIC_APP_URL}/login</a></p>
              </div>

              <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ffeaa7;">
                <h4 style="margin-top: 0; color: #856404;">⚠️ Important Security Notice</h4>
                <p style="margin-bottom: 0; color: #856404;">
                  <strong>You must change your password on first login.</strong> For security reasons, you'll be prompted to create a new password when you first access your account.
                </p>
              </div>

              <h3 style="color: #8B4513;">Next Steps:</h3>
              <ol>
                <li>Click the login link above</li>
                <li>Enter your email and temporary password</li>
                <li>Create a new secure password</li>
                <li>Complete your designer profile</li>
                <li>Start browsing available projects</li>
              </ol>

              <p>Welcome to our team! We look forward to seeing the amazing work you'll create with us.</p>

              <p>If you have any questions or need assistance, please don't hesitate to contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

              <p>Best regards,<br>
              The Seniors Architecture Firm Team</p>

              <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
              <p style="font-size: 12px; color: #666;">
                This email contains sensitive login information. Please keep it secure and delete it after you've successfully logged in and changed your password.
              </p>
            </div>
          `
        });
        console.log('Welcome email sent successfully');
      } catch (emailError) {
        console.error('Error sending welcome email:', emailError);
        // Don't fail the approval if email fails
      }

      // Clear the processing lock on success
      await clearLock();

      return NextResponse.json({
        success: true,
        message: 'Application approved and user account created successfully',
        userId: authUser.user.id
      }, { status: 200 });

    } catch (error) {
      console.error('Error in approval process:', error);
      await clearLock();

      return NextResponse.json(
        { error: 'Failed to approve application' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in approve application API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
