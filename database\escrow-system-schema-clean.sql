-- =====================================================
-- ESCROW SYSTEM DATABASE SCHEMA - CLEAN VERSION
-- Creates all tables without foreign key constraints first
-- =====================================================

-- 1. EXTEND EXISTING TABLES (SAFELY)
-- =====================================================

-- Add escrow status to transactions table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'escrow_status') THEN
            ALTER TABLE transactions ADD COLUMN escrow_status VARCHAR(50) DEFAULT 'none' 
                CHECK (escrow_status IN ('none', 'held', 'pending_release', 'released', 'disputed'));
            RAISE NOTICE 'Added escrow_status column to transactions table';
        ELSE
            RAISE NOTICE 'escrow_status column already exists in transactions table';
        END IF;
    ELSE
        RAISE NOTICE 'transactions table does not exist - skipping escrow_status column addition';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Error adding escrow_status to transactions: %', SQLERRM;
END $$;

-- Add escrow_hold_id to project_milestones table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_milestones') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'project_milestones' AND column_name = 'escrow_hold_id') THEN
            ALTER TABLE project_milestones ADD COLUMN escrow_hold_id UUID;
            RAISE NOTICE 'Added escrow_hold_id column to project_milestones table';
        ELSE
            RAISE NOTICE 'escrow_hold_id column already exists in project_milestones table';
        END IF;
    ELSE
        RAISE NOTICE 'project_milestones table does not exist - skipping escrow_hold_id column addition';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Error adding escrow_hold_id to project_milestones: %', SQLERRM;
END $$;

-- 2. CREATE PAYOUTS TABLE IF NOT EXISTS
-- =====================================================

CREATE TABLE IF NOT EXISTS payouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID,
    designer_id UUID,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    payout_method VARCHAR(50) DEFAULT 'stripe_connect',
    external_payout_id TEXT,
    failure_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    escrow_release_id UUID
);

-- 3. CREATE ESCROW TABLES (NO FOREIGN KEYS)
-- =====================================================

-- Escrow Accounts
CREATE TABLE IF NOT EXISTS escrow_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    client_id UUID NOT NULL,
    designer_id UUID NOT NULL,
    manager_id UUID,
    account_number TEXT UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'closed', 'suspended')),
    total_held DECIMAL(10,2) DEFAULT 0.00,
    total_released DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    UNIQUE(project_id, client_id, designer_id)
);

-- Escrow Holds
CREATE TABLE IF NOT EXISTS escrow_holds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_account_id UUID NOT NULL,
    transaction_id UUID NOT NULL,
    milestone_id UUID,
    project_id UUID NOT NULL,
    gross_amount DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    processing_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    hold_reason VARCHAR(100) NOT NULL DEFAULT 'milestone_completion',
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'pending_release', 'released', 'disputed', 'cancelled')),
    requires_manager_approval BOOLEAN DEFAULT TRUE,
    requires_quality_approval BOOLEAN DEFAULT FALSE,
    manager_approved_at TIMESTAMP WITH TIME ZONE,
    manager_approved_by UUID,
    quality_approved_at TIMESTAMP WITH TIME ZONE,
    quality_approved_by UUID,
    held_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    release_requested_at TIMESTAMP WITH TIME ZONE,
    released_at TIMESTAMP WITH TIME ZONE,
    auto_release_date TIMESTAMP WITH TIME ZONE,
    hold_notes TEXT,
    release_notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Releases
CREATE TABLE IF NOT EXISTS escrow_releases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_hold_id UUID NOT NULL,
    escrow_account_id UUID NOT NULL,
    project_id UUID NOT NULL,
    milestone_id UUID,
    release_amount DECIMAL(10,2) NOT NULL,
    release_type VARCHAR(50) NOT NULL DEFAULT 'milestone_completion' 
        CHECK (release_type IN ('milestone_completion', 'project_completion', 'partial_release', 'dispute_resolution', 'cancellation')),
    requested_by UUID NOT NULL,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    manager_id UUID,
    manager_approval_status VARCHAR(50) DEFAULT 'pending' 
        CHECK (manager_approval_status IN ('pending', 'approved', 'rejected', 'not_required')),
    manager_approved_at TIMESTAMP WITH TIME ZONE,
    manager_notes TEXT,
    quality_approval_status VARCHAR(50) DEFAULT 'not_required' 
        CHECK (quality_approval_status IN ('pending', 'approved', 'rejected', 'not_required')),
    quality_approved_at TIMESTAMP WITH TIME ZONE,
    quality_approved_by UUID,
    quality_notes TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'processed', 'failed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID,
    payout_id UUID,
    payout_transaction_id TEXT,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Disputes
CREATE TABLE IF NOT EXISTS escrow_disputes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_hold_id UUID NOT NULL,
    project_id UUID NOT NULL,
    dispute_type VARCHAR(50) NOT NULL CHECK (dispute_type IN ('quality_issue', 'scope_change', 'timeline_delay', 'payment_dispute', 'other')),
    initiated_by UUID NOT NULL,
    initiated_against UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    evidence_files JSONB DEFAULT '[]',
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'mediation', 'resolved', 'closed')),
    assigned_mediator UUID,
    resolution_type VARCHAR(50) CHECK (resolution_type IN ('full_release', 'partial_release', 'full_refund', 'partial_refund', 'rework_required')),
    resolution_amount DECIMAL(10,2),
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Activities
CREATE TABLE IF NOT EXISTS escrow_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_account_id UUID,
    escrow_hold_id UUID,
    escrow_release_id UUID,
    project_id UUID NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    performed_by UUID NOT NULL,
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    previous_state JSONB,
    new_state JSONB,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}'
);

-- 4. CREATE INDEXES (WITH SAFETY CHECKS)
-- =====================================================

-- Create indexes safely with error handling
DO $$
BEGIN
    -- Escrow Accounts indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'escrow_accounts') THEN
        CREATE INDEX IF NOT EXISTS idx_escrow_accounts_project_id ON escrow_accounts(project_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_accounts_client_id ON escrow_accounts(client_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_accounts_designer_id ON escrow_accounts(designer_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_accounts_manager_id ON escrow_accounts(manager_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_accounts_status ON escrow_accounts(status);
        RAISE NOTICE 'Created indexes for escrow_accounts table';
    END IF;

    -- Escrow Holds indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'escrow_holds') THEN
        CREATE INDEX IF NOT EXISTS idx_escrow_holds_account_id ON escrow_holds(escrow_account_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_holds_transaction_id ON escrow_holds(transaction_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_holds_milestone_id ON escrow_holds(milestone_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_holds_project_id ON escrow_holds(project_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_holds_status ON escrow_holds(status);
        CREATE INDEX IF NOT EXISTS idx_escrow_holds_manager_approval ON escrow_holds(manager_approved_at, status);
        CREATE INDEX IF NOT EXISTS idx_escrow_holds_auto_release ON escrow_holds(auto_release_date, status);
        RAISE NOTICE 'Created indexes for escrow_holds table';
    END IF;

    -- Escrow Releases indexes (check if columns exist)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'escrow_releases') THEN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'escrow_releases' AND column_name = 'escrow_hold_id') THEN
            CREATE INDEX IF NOT EXISTS idx_escrow_releases_hold_id ON escrow_releases(escrow_hold_id);
        END IF;
        CREATE INDEX IF NOT EXISTS idx_escrow_releases_project_id ON escrow_releases(project_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_releases_manager_id ON escrow_releases(manager_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_releases_status ON escrow_releases(status);
        CREATE INDEX IF NOT EXISTS idx_escrow_releases_approval_status ON escrow_releases(manager_approval_status, quality_approval_status);
        RAISE NOTICE 'Created indexes for escrow_releases table';
    END IF;

    -- Escrow Disputes indexes (check if columns exist)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'escrow_disputes') THEN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'escrow_disputes' AND column_name = 'escrow_hold_id') THEN
            CREATE INDEX IF NOT EXISTS idx_escrow_disputes_hold_id ON escrow_disputes(escrow_hold_id);
        END IF;
        CREATE INDEX IF NOT EXISTS idx_escrow_disputes_project_id ON escrow_disputes(project_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_disputes_status ON escrow_disputes(status);
        CREATE INDEX IF NOT EXISTS idx_escrow_disputes_mediator ON escrow_disputes(assigned_mediator);
        RAISE NOTICE 'Created indexes for escrow_disputes table';
    END IF;

    -- Escrow Activities indexes
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'escrow_activities') THEN
        CREATE INDEX IF NOT EXISTS idx_escrow_activities_account_id ON escrow_activities(escrow_account_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_activities_project_id ON escrow_activities(project_id);
        CREATE INDEX IF NOT EXISTS idx_escrow_activities_performed_by ON escrow_activities(performed_by);
        CREATE INDEX IF NOT EXISTS idx_escrow_activities_performed_at ON escrow_activities(performed_at);
        CREATE INDEX IF NOT EXISTS idx_escrow_activities_type ON escrow_activities(activity_type);
        RAISE NOTICE 'Created indexes for escrow_activities table';
    END IF;

EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Error creating indexes: %', SQLERRM;
END $$;

-- 5. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE escrow_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_holds ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_activities ENABLE ROW LEVEL SECURITY;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Escrow system schema created successfully!';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Run escrow-foreign-keys.sql to add foreign key constraints';
    RAISE NOTICE '2. Run escrow-rls-policies.sql to add security policies';
    RAISE NOTICE '3. Run quality-workflow-functions.sql to add database functions';
END $$;
