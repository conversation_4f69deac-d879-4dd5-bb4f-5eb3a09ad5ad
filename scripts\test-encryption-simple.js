#!/usr/bin/env node

/**
 * Simple encryption test in pure JavaScript
 * Tests the core encryption logic without TypeScript
 */

const crypto = require('crypto');

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 12; // For GCM, recommended is 12 bytes
const TAG_LENGTH = 16; // GCM authentication tag length

function getEncryptionKey() {
  const primaryKey = process.env.ENCRYPTION_KEY;
  if (!primaryKey) {
    throw new Error('No encryption key found in environment variables');
  }
  
  // Ensure key is exactly 32 bytes (256 bits)
  const key = crypto.createHash('sha256').update(primaryKey).digest();
  return key;
}

function encryptSensitiveData(plaintext) {
  if (!plaintext || plaintext.trim() === '') {
    return '';
  }
  
  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    cipher.setAAD(Buffer.from('payment-data')); // Additional authenticated data
    
    let encrypted = cipher.update(plaintext, 'utf8');
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const tag = cipher.getAuthTag();
    
    // Combine iv, tag, and encrypted data
    const combined = Buffer.concat([iv, tag, encrypted]);
    return combined.toString('base64');
    
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt sensitive data');
  }
}

function decryptSensitiveData(encryptedData) {
  if (!encryptedData || encryptedData.trim() === '') {
    return '';
  }
  
  try {
    const key = getEncryptionKey();
    const combined = Buffer.from(encryptedData, 'base64');
    
    // Extract components
    const iv = combined.subarray(0, IV_LENGTH);
    const tag = combined.subarray(IV_LENGTH, IV_LENGTH + TAG_LENGTH);
    const encrypted = combined.subarray(IV_LENGTH + TAG_LENGTH);
    
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(tag);
    decipher.setAAD(Buffer.from('payment-data')); // Same AAD used in encryption
    
    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
    
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt sensitive data');
  }
}

function runTests() {
  console.log('🧪 Testing Encryption Functions');
  console.log('=' .repeat(40));
  console.log();

  let passed = 0;
  let failed = 0;

  function test(name, testFn) {
    try {
      testFn();
      console.log(`✅ ${name}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      failed++;
    }
  }

  // Test 1: Basic encryption/decryption
  test('Basic encryption/decryption', () => {
    const original = '**********123456';
    const encrypted = encryptSensitiveData(original);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (encrypted === original) {
      throw new Error('Data was not encrypted');
    }
    if (decrypted !== original) {
      throw new Error('Decrypted data does not match original');
    }
  });

  // Test 2: Empty string handling
  test('Empty string handling', () => {
    const encrypted = encryptSensitiveData('');
    const decrypted = decryptSensitiveData('');
    
    if (encrypted !== '' || decrypted !== '') {
      throw new Error('Empty strings should remain empty');
    }
  });

  // Test 3: Account number encryption
  test('Account number encryption', () => {
    const accountNumber = '**********';
    const encrypted = encryptSensitiveData(accountNumber);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (encrypted === accountNumber) {
      throw new Error('Account number was not encrypted');
    }
    if (decrypted !== accountNumber) {
      throw new Error('Account number decryption failed');
    }
  });

  // Test 4: IBAN encryption
  test('IBAN encryption', () => {
    const iban = '**********************';
    const encrypted = encryptSensitiveData(iban);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (decrypted !== iban) {
      throw new Error('IBAN encryption/decryption failed');
    }
  });

  // Test 5: Special characters
  test('Special characters handling', () => {
    const specialData = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    const encrypted = encryptSensitiveData(specialData);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (decrypted !== specialData) {
      throw new Error('Special characters encryption/decryption failed');
    }
  });

  console.log();
  console.log('=' .repeat(40));
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Encryption is working correctly.');
    return true;
  } else {
    console.log('⚠️  Some tests failed. Please check your encryption setup.');
    return false;
  }
}

if (require.main === module) {
  if (!process.env.ENCRYPTION_KEY) {
    console.error('❌ ENCRYPTION_KEY environment variable is required');
    console.log('Usage: ENCRYPTION_KEY=your-key node scripts/test-encryption-simple.js');
    process.exit(1);
  }
  
  const success = runTests();
  process.exit(success ? 0 : 1);
}

module.exports = { 
  encryptSensitiveData, 
  decryptSensitiveData, 
  runTests 
};
