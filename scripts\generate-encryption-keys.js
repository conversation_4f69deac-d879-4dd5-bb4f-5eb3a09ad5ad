#!/usr/bin/env node

/**
 * Encryption Key Generator for Senior's Archi-firm
 * 
 * This script generates secure encryption keys for the payment method encryption system.
 * Run this script once during initial setup and whenever you need to rotate keys.
 * 
 * Usage:
 *   node scripts/generate-encryption-keys.js
 */

const crypto = require('crypto');

function generateSecureKey() {
  return crypto.randomBytes(32).toString('hex');
}

function validateKey(key) {
  if (!key || key.length !== 64) {
    return false;
  }
  
  // Check for weak patterns
  const weakPatterns = [
    /^(.)\1+$/, // All same character
    /^(012|123|234|345|456|567|678|789|890)+/, // Sequential numbers
    /^(abc|def|ghi|jkl|mno|pqr|stu|vwx|yz)+/i, // Sequential letters
  ];
  
  return !weakPatterns.some(pattern => pattern.test(key));
}

function main() {
  console.log('🔐 Encryption Key Generator for <PERSON>\'s Archi-firm');
  console.log('=' .repeat(60));
  console.log();
  
  // Generate primary key
  let primaryKey;
  do {
    primaryKey = generateSecureKey();
  } while (!validateKey(primaryKey));
  
  // Generate backup key
  let backupKey;
  do {
    backupKey = generateSecureKey();
  } while (!validateKey(backupKey) || backupKey === primaryKey);
  
  console.log('✅ Generated secure encryption keys:');
  console.log();
  console.log('Primary Key (ENCRYPTION_KEY):');
  console.log(primaryKey);
  console.log();
  console.log('Backup Key (ENCRYPTION_KEY_BACKUP):');
  console.log(backupKey);
  console.log();
  console.log('📋 Add these to your .env.local file:');
  console.log('=' .repeat(60));
  console.log(`ENCRYPTION_KEY=${primaryKey}`);
  console.log(`ENCRYPTION_KEY_BACKUP=${backupKey}`);
  console.log();
  console.log('⚠️  IMPORTANT SECURITY NOTES:');
  console.log('- Store these keys securely and never commit them to version control');
  console.log('- Keep the backup key safe for key rotation purposes');
  console.log('- These keys are used to encrypt sensitive payment information');
  console.log('- If you lose these keys, encrypted data cannot be recovered');
  console.log();
  console.log('🔄 For key rotation (every 3 months):');
  console.log('1. Generate new keys using this script');
  console.log('2. Set the new primary key as ENCRYPTION_KEY');
  console.log('3. Keep the old primary key as ENCRYPTION_KEY_BACKUP temporarily');
  console.log('4. Update all encrypted data with the new key');
  console.log('5. Remove the old backup key');
  console.log();
}

if (require.main === module) {
  main();
}

module.exports = {
  generateSecureKey,
  validateKey
};
