"use client";

import WorkflowNotifications from "@/components/WorkflowNotifications";

interface DashboardNotificationsProps {
  className?: string;
  variant?: "header" | "inline" | "floating";
  role?: string;
}

export function DashboardNotifications({ 
  className = "", 
  variant = "header",
  role = "admin"
}: DashboardNotificationsProps) {
  const variantClasses = {
    header: "flex items-center",
    inline: "inline-flex items-center",
    floating: "fixed top-4 right-4 z-50"
  };

  return (
    <div className={`${variantClasses[variant]} ${className}`}>
      <WorkflowNotifications />
    </div>
  );
}
