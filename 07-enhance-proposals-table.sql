-- =====================================================
-- SCRIPT 7: ENHANCE PROJECT_PROPOSALS_ENHANCED TABLE
-- =====================================================

-- Add manager_status column
ALTER TABLE project_proposals_enhanced 
ADD COLUMN IF NOT EXISTS manager_status TEXT;

-- Add manager_status constraint separately
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'project_proposals_enhanced_manager_status_check'
  ) THEN
    ALTER TABLE project_proposals_enhanced 
    ADD CONSTRAINT project_proposals_enhanced_manager_status_check 
    CHECK (manager_status IN ('pending', 'approved', 'rejected', 'escalated'));
  END IF;
END $$;

-- Add manager_notes column
ALTER TABLE project_proposals_enhanced 
ADD COLUMN IF NOT EXISTS manager_notes TEXT;

-- Add manager_reviewed_at column
ALTER TABLE project_proposals_enhanced 
ADD COLUMN IF NOT EXISTS manager_reviewed_at TIMESTAMP WITH TIME ZONE;

-- Add manager_reviewed_by column (without foreign key first)
ALTER TABLE project_proposals_enhanced 
ADD COLUMN IF NOT EXISTS manager_reviewed_by UUID;

-- Add foreign key constraint separately
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'project_proposals_enhanced_manager_reviewed_by_fkey'
  ) THEN
    ALTER TABLE project_proposals_enhanced 
    ADD CONSTRAINT project_proposals_enhanced_manager_reviewed_by_fkey 
    FOREIGN KEY (manager_reviewed_by) REFERENCES profiles(id);
  END IF;
END $$;

-- Add priority column
ALTER TABLE project_proposals_enhanced 
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'normal';

-- Add priority constraint separately
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'project_proposals_enhanced_priority_check'
  ) THEN
    ALTER TABLE project_proposals_enhanced 
    ADD CONSTRAINT project_proposals_enhanced_priority_check 
    CHECK (priority IN ('low', 'normal', 'high', 'urgent'));
  END IF;
END $$;

-- Verify completion
SELECT 'Script 7 completed: project_proposals_enhanced table enhanced' as status;
