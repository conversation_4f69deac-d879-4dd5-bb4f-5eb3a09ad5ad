'use client';

import React, { useState, useEffect, useMemo, Suspense, lazy } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { performanceCache, cacheKeys } from '@/lib/performance-cache';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Activity, 
  TrendingUp, 
  Clock, 
  AlertCircle,
  RefreshCw,
  Zap,
  BarChart3,
  Users
} from 'lucide-react';

// Lazy load heavy components
const AdvancedAnalytics = lazy(() => import('./AdvancedAnalytics'));
const PerformanceMetrics = lazy(() => import('./PerformanceMetrics'));
const RealTimeUpdates = lazy(() => import('./RealTimeUpdates'));

interface DashboardData {
  stats: any;
  recent_projects?: any[];
  recent_activities?: any[];
  pending_approvals?: any[];
  projects?: any[];
  recent_messages?: any[];
  pending_reviews?: any[];
  manager_performance?: any[];
  system_activities?: any[];
  assigned_reviews?: any[];
  team_workload?: any[];
  performance_score?: number;
  cache_age_minutes?: number;
  cache_info?: {
    generated_at: string;
    user_role: string;
    force_refresh: boolean;
  };
}

interface OptimizedDashboardProps {
  enableRealTime?: boolean;
  enableAnalytics?: boolean;
  refreshInterval?: number;
}

export default function OptimizedDashboard({ 
  enableRealTime = true, 
  enableAnalytics = true,
  refreshInterval = 5 * 60 * 1000 // 5 minutes
}: OptimizedDashboardProps) {
  const { user, profile } = useOptimizedAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Memoized cache key
  const cacheKey = useMemo(() => {
    if (!user || !profile) return null;
    return cacheKeys.dashboard(profile.role, user.id);
  }, [user, profile]);

  // Fetch dashboard data with caching
  const fetchDashboardData = async (forceRefresh = false) => {
    if (!user || !profile || !cacheKey) return;

    try {
      setError(null);
      if (forceRefresh) setRefreshing(true);

      // Use performance cache for optimized data fetching
      const data = await performanceCache.get(
        cacheKey,
        async () => {
          const response = await fetch(`/api/optimized/dashboard?refresh=${forceRefresh}`, {
            headers: {
              'Authorization': `Bearer ${await user.getIdToken()}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`Dashboard API error: ${response.status}`);
          }

          const result = await response.json();
          return result.data;
        },
        forceRefresh ? 0 : 2 * 60 * 1000 // 2 minutes cache, 0 for force refresh
      );

      setDashboardData(data);
      setLastRefresh(new Date());

      // Preload related data in background
      if (enableAnalytics) {
        performanceCache.preloadCommonData(user.id, profile.role);
      }

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load dashboard');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchDashboardData();
  }, [user, profile]);

  // Auto-refresh interval
  useEffect(() => {
    if (!enableRealTime || !refreshInterval) return;

    const interval = setInterval(() => {
      fetchDashboardData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [enableRealTime, refreshInterval]);

  // Manual refresh
  const handleRefresh = () => {
    fetchDashboardData(true);
  };

  // Render loading skeleton
  const renderSkeleton = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-8 w-16" />
            </CardContent>
          </Card>
        ))}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // Render error state
  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="font-semibold text-gray-900 mb-2">Dashboard Error</h3>
            <p className="text-sm text-gray-600 mb-4">{error}</p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render loading state
  if (loading || !dashboardData) {
    return renderSkeleton();
  }

  // Get role-specific stats
  const getStatsCards = () => {
    const { stats } = dashboardData;
    
    switch (profile?.role) {
      case 'manager':
        return [
          {
            title: 'Total Projects',
            value: stats.total_projects || 0,
            icon: BarChart3,
            color: 'text-blue-600'
          },
          {
            title: 'Active Projects',
            value: stats.active_projects || 0,
            icon: Activity,
            color: 'text-green-600'
          },
          {
            title: 'Pending Approvals',
            value: stats.pending_escrow_approvals || 0,
            icon: Clock,
            color: 'text-orange-600'
          },
          {
            title: 'Performance Score',
            value: `${Math.round(dashboardData.performance_score || 0)}%`,
            icon: TrendingUp,
            color: 'text-purple-600'
          }
        ];
      
      case 'client':
        return [
          {
            title: 'My Projects',
            value: stats.total_projects || 0,
            icon: BarChart3,
            color: 'text-blue-600'
          },
          {
            title: 'Active Projects',
            value: stats.active_projects || 0,
            icon: Activity,
            color: 'text-green-600'
          },
          {
            title: 'Completed',
            value: stats.completed_projects || 0,
            icon: TrendingUp,
            color: 'text-purple-600'
          },
          {
            title: 'Progress',
            value: `${Math.round(stats.average_progress || 0)}%`,
            icon: Clock,
            color: 'text-orange-600'
          }
        ];
      
      case 'designer':
        return [
          {
            title: 'My Projects',
            value: stats.total_projects || 0,
            icon: BarChart3,
            color: 'text-blue-600'
          },
          {
            title: 'Active Projects',
            value: stats.active_projects || 0,
            icon: Activity,
            color: 'text-green-600'
          },
          {
            title: 'Pending Reviews',
            value: stats.pending_reviews || 0,
            icon: Clock,
            color: 'text-orange-600'
          },
          {
            title: 'Quality Score',
            value: `${Math.round(stats.average_quality_score || 0)}/10`,
            icon: TrendingUp,
            color: 'text-purple-600'
          }
        ];
      
      case 'admin':
        return [
          {
            title: 'Total Managers',
            value: stats.total_managers || 0,
            icon: Users,
            color: 'text-blue-600'
          },
          {
            title: 'Total Projects',
            value: stats.total_projects || 0,
            icon: BarChart3,
            color: 'text-green-600'
          },
          {
            title: 'Avg Satisfaction',
            value: `${Math.round(stats.average_satisfaction || 0)}/10`,
            icon: TrendingUp,
            color: 'text-purple-600'
          },
          {
            title: 'Total Revenue',
            value: `$${(stats.total_revenue || 0).toLocaleString()}`,
            icon: Activity,
            color: 'text-orange-600'
          }
        ];
      
      case 'quality_team':
        return [
          {
            title: 'Assigned Reviews',
            value: stats.assigned_reviews || 0,
            icon: BarChart3,
            color: 'text-blue-600'
          },
          {
            title: 'Overdue Reviews',
            value: stats.overdue_reviews || 0,
            icon: AlertCircle,
            color: 'text-red-600'
          },
          {
            title: 'Reviews Today',
            value: stats.reviews_today || 0,
            icon: Clock,
            color: 'text-green-600'
          },
          {
            title: 'Avg Completion',
            value: `${Math.round(stats.average_completion_time || 0)}h`,
            icon: TrendingUp,
            color: 'text-purple-600'
          }
        ];
      
      default:
        return [];
    }
  };

  const statsCards = getStatsCards();

  return (
    <div className="space-y-6">
      {/* Header with refresh controls */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {profile?.role === 'manager' ? 'Manager' : 
             profile?.role === 'client' ? 'Client' :
             profile?.role === 'designer' ? 'Designer' :
             profile?.role === 'admin' ? 'Admin' :
             profile?.role === 'quality_team' ? 'Quality Team' : ''} Dashboard
          </h1>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Zap className="h-4 w-4 text-green-500" />
            <span>Optimized Performance</span>
            {dashboardData.cache_age_minutes !== undefined && (
              <>
                <span>•</span>
                <span>Cache: {dashboardData.cache_age_minutes}m old</span>
              </>
            )}
            {lastRefresh && (
              <>
                <span>•</span>
                <span>Updated: {lastRefresh.toLocaleTimeString()}</span>
              </>
            )}
          </div>
        </div>
        <Button 
          onClick={handleRefresh} 
          variant="outline" 
          size="sm"
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsCards.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Role-specific content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Projects/Activities */}
        <Card>
          <CardHeader>
            <CardTitle>
              {profile?.role === 'manager' ? 'Recent Projects' :
               profile?.role === 'client' ? 'My Projects' :
               profile?.role === 'designer' ? 'My Projects' :
               profile?.role === 'admin' ? 'System Activities' :
               profile?.role === 'quality_team' ? 'Assigned Reviews' : 'Recent Items'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(dashboardData.recent_projects || 
                dashboardData.projects || 
                dashboardData.system_activities || 
                dashboardData.assigned_reviews || 
                []).slice(0, 5).map((item: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {item.title || item.description || item.log_type || 'Unknown'}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {item.status && <Badge variant="outline">{item.status}</Badge>}
                      {item.progress_percentage && (
                        <span className="ml-2">{Math.round(item.progress_percentage)}% complete</span>
                      )}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {new Date(item.created_at || item.updated_at || Date.now()).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Pending Actions/Messages */}
        <Card>
          <CardHeader>
            <CardTitle>
              {profile?.role === 'manager' ? 'Pending Approvals' :
               profile?.role === 'client' ? 'Recent Messages' :
               profile?.role === 'designer' ? 'Pending Reviews' :
               profile?.role === 'admin' ? 'Manager Performance' :
               profile?.role === 'quality_team' ? 'Team Workload' : 'Pending Actions'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(dashboardData.pending_approvals || 
                dashboardData.recent_messages || 
                dashboardData.pending_reviews || 
                dashboardData.manager_performance || 
                dashboardData.team_workload || 
                []).slice(0, 5).map((item: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {item.project?.title || item.full_name || item.manager_name || item.content || 'Unknown'}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {item.release_amount && `$${item.release_amount}`}
                      {item.avg_client_satisfaction && `Satisfaction: ${item.avg_client_satisfaction}/10`}
                      {item.current_workload && `Workload: ${item.current_workload}/${item.max_workload}`}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {item.requested_at && new Date(item.requested_at).toLocaleDateString()}
                      {item.created_at && new Date(item.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Analytics (Lazy Loaded) */}
      {enableAnalytics && (
        <Suspense fallback={<Skeleton className="h-64 w-full" />}>
          <AdvancedAnalytics 
            userRole={profile?.role || ''} 
            userId={user?.id || ''} 
            dashboardData={dashboardData}
          />
        </Suspense>
      )}

      {/* Performance Metrics (Lazy Loaded) */}
      <Suspense fallback={<Skeleton className="h-32 w-full" />}>
        <PerformanceMetrics 
          cacheStats={performanceCache.getStats()}
          lastRefresh={lastRefresh}
        />
      </Suspense>

      {/* Real-time Updates (Lazy Loaded) */}
      {enableRealTime && (
        <Suspense fallback={null}>
          <RealTimeUpdates 
            userId={user?.id || ''} 
            userRole={profile?.role || ''}
            onUpdate={() => fetchDashboardData()}
          />
        </Suspense>
      )}
    </div>
  );
}
