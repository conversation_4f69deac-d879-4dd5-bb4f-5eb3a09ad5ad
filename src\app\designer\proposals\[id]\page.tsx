"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useParams, useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import ChangeRequestsList from "@/components/proposals/ChangeRequestsList";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  FileText,
  DollarSign,
  Clock,
  Calendar,
  User,
  Edit,
  Send,
  Trash2,
  AlertCircle,
  CheckCircle,
  XCircle,
  MessageSquare,
  Eye,
  Target,
} from "lucide-react";

interface Milestone {
  title: string;
  description: string;
  duration_weeks: number;
  cost: number;
}

interface ProposalDetails {
  id: string;
  title: string;
  description: string;
  total_budget: number;
  timeline_weeks: number;
  milestones: Milestone[];
  terms_and_conditions: string;
  status:
    | "draft"
    | "submitted"
    | "under_review"
    | "accepted"
    | "rejected"
    | "withdrawn";
  submitted_at: string | null;
  reviewed_at: string | null;
  expires_at: string | null;
  created_at: string;
  updated_at: string;
  brief_id: string;
  brief_title: string;
  brief_description: string;
  client_id: string;
  client_name: string;
  client_avatar: string | null;
}

export default function ProposalDetails() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const [proposal, setProposal] = useState<ProposalDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [withdrawing, setWithdrawing] = useState(false);

  useEffect(() => {
    if (user && params.id) {
      fetchProposalDetails();
    }
  }, [user, params.id]);

  const fetchProposalDetails = async () => {
    if (!user || !params.id) return;

    try {
      const { data, error } = await supabase
        .from("project_proposals_enhanced")
        .select(
          `
          *,
          project_briefs!project_proposals_enhanced_brief_id_fkey(
            id,
            title,
            description,
            client_id,
            profiles!project_briefs_client_id_fkey(full_name, avatar_url)
          )
        `
        )
        .eq("id", params.id)
        .eq("designer_id", user.id)
        .single();

      if (error) throw error;

      const brief = data.project_briefs;
      const client = Array.isArray(brief?.profiles)
        ? brief.profiles[0]
        : brief?.profiles;

      setProposal({
        ...data,
        brief_id: brief?.id || "",
        brief_title: brief?.title || "Unknown Brief",
        brief_description: brief?.description || "",
        client_id: brief?.client_id || "",
        client_name: client?.full_name || "Unknown Client",
        client_avatar: client?.avatar_url || null,
        milestones: data.milestones || [],
      });
    } catch (error) {
      console.error("Error fetching proposal details:", error);
      setError("Failed to load proposal details");
    } finally {
      setLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (
      !proposal ||
      !confirm(
        "Are you sure you want to withdraw this proposal? This action cannot be undone."
      )
    ) {
      return;
    }

    setWithdrawing(true);
    try {
      const { error } = await supabase
        .from("project_proposals_enhanced")
        .update({ status: "withdrawn" })
        .eq("id", proposal.id);

      if (error) throw error;

      // Update local state
      setProposal((prev) => (prev ? { ...prev, status: "withdrawn" } : null));
    } catch (error) {
      console.error("Error withdrawing proposal:", error);
      alert("Failed to withdraw proposal. Please try again.");
    } finally {
      setWithdrawing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "accepted":
        return "text-green-600 bg-green-50 border-green-200";
      case "submitted":
      case "under_review":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "draft":
        return "text-gray-600 bg-gray-50 border-gray-200";
      case "rejected":
      case "withdrawn":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "accepted":
        return <CheckCircle className="h-5 w-5" />;
      case "rejected":
      case "withdrawn":
        return <XCircle className="h-5 w-5" />;
      case "submitted":
      case "under_review":
        return <Clock className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const isExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  const canEdit = proposal?.status === "draft";
  const canWithdraw =
    proposal?.status === "submitted" || proposal?.status === "under_review";

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <p className="text-red-700">{error || "Proposal not found"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/designer/proposals">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Proposals
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {proposal.title}
            </h1>
            <p className="text-gray-600">Proposal Details</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            href={`/designer/messages?otherUserId=${proposal.client_id}&type=direct`}
          >
            <Button variant="outline">
              <MessageSquare className="h-4 w-4 mr-2" />
              Message Client
            </Button>
          </Link>
          {canEdit && (
            <Link href={`/designer/proposals/${proposal.id}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit Proposal
              </Button>
            </Link>
          )}
          {canWithdraw && (
            <Button
              onClick={handleWithdraw}
              disabled={withdrawing}
              variant="outline"
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {withdrawing ? "Withdrawing..." : "Withdraw"}
            </Button>
          )}
        </div>
      </div>

      {/* Status and Info */}
      <div className="flex items-center space-x-4">
        <span
          className={`px-3 py-1 text-sm font-medium rounded-full border flex items-center ${getStatusColor(proposal.status)}`}
        >
          {getStatusIcon(proposal.status)}
          <span className="ml-2">
            {proposal.status.replace("_", " ").toUpperCase()}
          </span>
        </span>
        {isExpired(proposal.expires_at) && proposal.status === "submitted" && (
          <span className="px-3 py-1 text-sm font-medium rounded-full bg-red-100 text-red-700 border border-red-200">
            EXPIRED
          </span>
        )}
        <span className="text-sm text-gray-500">
          For: {proposal.brief_title}
        </span>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Proposal Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Proposal Description
            </h3>
            <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
              {proposal.description}
            </p>
          </motion.div>

          {/* Milestones */}
          {proposal.milestones && proposal.milestones.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Project Milestones
              </h3>
              <div className="space-y-4">
                {proposal.milestones.map((milestone, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">
                        {milestone.title}
                      </h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{milestone.duration_weeks} weeks</span>
                        <span className="font-medium">
                          ${milestone.cost.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm">
                      {milestone.description}
                    </p>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total from milestones:</span>
                  <span className="font-medium">
                    $
                    {proposal.milestones
                      .reduce((sum, m) => sum + m.cost, 0)
                      .toLocaleString()}
                  </span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Terms and Conditions */}
          {proposal.terms_and_conditions && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Terms and Conditions
              </h3>
              <div className="text-gray-700 text-sm leading-relaxed whitespace-pre-wrap">
                {proposal.terms_and_conditions}
              </div>
            </motion.div>
          )}
          {/* Change Requests */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <ChangeRequestsList proposalId={proposal.id} userRole="designer" />
          </motion.div>
        </div>

        {/* Right Column - Summary & Client Info */}
        <div className="space-y-6">
          {/* Client Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Client Information
            </h3>
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex-shrink-0">
                {proposal.client_avatar ? (
                  <img
                    src={proposal.client_avatar}
                    alt={proposal.client_name}
                    className="h-12 w-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="h-6 w-6 text-gray-500" />
                  </div>
                )}
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  {proposal.client_name}
                </p>
              </div>
            </div>
            <Link
              href={`/designer/messages?otherUserId=${proposal.client_id}&type=direct`}
            >
              <Button size="sm" variant="outline" className="w-full">
                <MessageSquare className="h-4 w-4 mr-2" />
                Message Client
              </Button>
            </Link>
          </motion.div>

          {/* Proposal Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Proposal Summary
            </h3>
            <div className="space-y-4">
              <div className="flex items-center text-sm">
                <DollarSign className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Total Budget:</span>
                <span className="ml-2 font-medium">
                  ${proposal.total_budget.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center text-sm">
                <Clock className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Timeline:</span>
                <span className="ml-2 font-medium">
                  {proposal.timeline_weeks} weeks
                </span>
              </div>
              <div className="flex items-center text-sm">
                <Target className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Milestones:</span>
                <span className="ml-2 font-medium">
                  {proposal.milestones?.length || 0}
                </span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Created:</span>
                <span className="ml-2 font-medium">
                  {formatDate(proposal.created_at)}
                </span>
              </div>
              {proposal.submitted_at && (
                <div className="flex items-center text-sm">
                  <Send className="h-4 w-4 text-gray-400 mr-3" />
                  <span className="text-gray-600">Submitted:</span>
                  <span className="ml-2 font-medium">
                    {formatDate(proposal.submitted_at)}
                  </span>
                </div>
              )}
              {proposal.expires_at && (
                <div className="flex items-center text-sm">
                  <AlertCircle className="h-4 w-4 text-gray-400 mr-3" />
                  <span className="text-gray-600">Expires:</span>
                  <span className="ml-2 font-medium">
                    {formatDate(proposal.expires_at)}
                  </span>
                </div>
              )}
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Quick Actions
            </h3>
            <div className="space-y-3">
              <Link href={`/designer/briefs/${proposal.brief_id}`}>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                >
                  <Eye className="h-4 w-4 mr-3" />
                  View Original Brief
                </Button>
              </Link>
              <Link
                href={`/designer/messages?otherUserId=${proposal.client_id}&type=direct`}
              >
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                >
                  <MessageSquare className="h-4 w-4 mr-3" />
                  Contact Client
                </Button>
              </Link>
              <Link href="/designer/proposals">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                >
                  <FileText className="h-4 w-4 mr-3" />
                  View All Proposals
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
