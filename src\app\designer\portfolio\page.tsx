'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { getPortfolioProjects, deletePortfolioProject } from '@/lib/api/portfolio';
import { PortfolioProject } from '@/types/portfolio';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { PortfolioProjectCard, PortfolioProjectCardSkeleton } from '@/components/portfolio/PortfolioProjectCard';
import { PortfolioProjectForm } from '@/components/portfolio/PortfolioProjectForm';
import { PlusIcon, FolderIcon, StarIcon } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

export default function DesignerPortfolioPage() {
  const { user, profile } = useOptimizedAuth();
  const router = useRouter();
  const [projects, setProjects] = useState<PortfolioProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<PortfolioProject | null>(null);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    if (profile && profile.role !== 'designer') {
      router.push('/');
      return;
    }

    fetchProjects();
  }, [user, profile, router]);

  const fetchProjects = async () => {
    if (!token) return;

    setLoading(true);
    try {
      const fetchedProjects = await getPortfolioProjects(token);
      setProjects(fetchedProjects);
    } catch (error) {
      console.error('Error fetching portfolio projects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load portfolio projects',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSuccess = (project: PortfolioProject) => {
    setProjects(prev => [project, ...prev]);
    setIsCreateDialogOpen(false);
  };

  const handleEditProject = (project: PortfolioProject) => {
    setEditingProject(project);
  };

  const handleEditSuccess = (updatedProject: PortfolioProject) => {
    setProjects(prev =>
      prev.map(p => p.id === updatedProject.id ? updatedProject : p)
    );
    setEditingProject(null);
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!token) return;

    try {
      await deletePortfolioProject(token, projectId);
      setProjects(prev => prev.filter(p => p.id !== projectId));
      toast({
        title: 'Project Deleted',
        description: 'The portfolio project has been deleted',
      });
    } catch (error) {
      console.error('Error deleting project:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete project',
        variant: 'destructive',
      });
    }
  };

  const filteredProjects = activeTab === 'featured'
    ? projects.filter(p => p.featured)
    : projects;

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">My Portfolio</h1>
          <p className="text-muted-foreground mt-1">
            Showcase your best work to attract potential clients
          </p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Project
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create New Portfolio Project</DialogTitle>
              <DialogDescription>
                Add details about your project. You can add images after creating the project.
              </DialogDescription>
            </DialogHeader>
            <PortfolioProjectForm
              onSuccess={handleCreateSuccess}
              onCancel={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList>
          <TabsTrigger value="all" className="flex items-center">
            <FolderIcon className="h-4 w-4 mr-2" />
            All Projects
          </TabsTrigger>
          <TabsTrigger value="featured" className="flex items-center">
            <StarIcon className="h-4 w-4 mr-2" />
            Featured
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <PortfolioProjectCardSkeleton key={i} />
          ))}
        </div>
      ) : filteredProjects.length === 0 ? (
        <div className="text-center py-12 border rounded-md">
          <h3 className="text-lg font-medium mb-2">No projects found</h3>
          <p className="text-muted-foreground mb-6">
            {activeTab === 'featured'
              ? "You don't have any featured projects yet."
              : "You haven't added any portfolio projects yet."}
          </p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Your First Project
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map(project => (
            <PortfolioProjectCard
              key={project.id}
              project={project}
              isOwner={true}
              onEdit={handleEditProject}
              onDelete={handleDeleteProject}
            />
          ))}
        </div>
      )}

      {/* Edit Project Dialog */}
      {editingProject && (
        <Dialog open={!!editingProject} onOpenChange={(open) => !open && setEditingProject(null)}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Edit Portfolio Project</DialogTitle>
              <DialogDescription>
                Update the details of your project.
              </DialogDescription>
            </DialogHeader>
            <PortfolioProjectForm
              project={editingProject}
              onSuccess={handleEditSuccess}
              onCancel={() => setEditingProject(null)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
