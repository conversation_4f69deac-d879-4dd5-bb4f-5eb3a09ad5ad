"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/Button";
import Link from "next/link";
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Calendar,
  ArrowUp,
  ArrowDown,
  Save
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  client_name: string;
  budget: number | null;
};

type Milestone = {
  id: string;
  title: string;
  description: string | null;
  amount: number;
  percentage: number;
  due_date: string | null;
  status: string;
  order: number;
  completed_at: string | null;
  paid_at: string | null;
};

export default function ProjectMilestones() {
  const { user } = useAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [project, setProject] = useState<Project | null>(null);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedMilestones, setEditedMilestones] = useState<Milestone[]>([]);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user && projectId) {
      fetchProjectData();
    }
  }, [user, projectId]);

  const fetchProjectData = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          budget,
          client_id,
          profiles!client_id(full_name)
        `)
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      setProject({
        id: projectData.id,
        title: projectData.title,
        client_name: projectData.profiles?.full_name || 'Unknown Client',
        budget: projectData.budget
      });

      // Try to fetch milestones if they exist
      try {
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('project_milestones')
          .select('*')
          .eq('project_id', projectId)
          .order('order', { ascending: true });

        if (milestonesError) throw milestonesError;

        if (milestonesData && milestonesData.length > 0) {
          setMilestones(milestonesData);
          setEditedMilestones(milestonesData);
        } else {
          // If no milestones exist, create default ones
          const defaultMilestones = createDefaultMilestones(projectData.budget);
          setMilestones(defaultMilestones);
          setEditedMilestones(defaultMilestones);
        }
      } catch (error) {
        console.log('Milestones table may not exist yet:', error);
        // Create default milestones
        const defaultMilestones = createDefaultMilestones(projectData.budget);
        setMilestones(defaultMilestones);
        setEditedMilestones(defaultMilestones);
      }
    } catch (error: Error | unknown) {
      console.error('Error fetching project data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const createDefaultMilestones = (budget: number | null): Milestone[] => {
    const totalBudget = budget || 0;
    return [
      {
        id: 'new-1',
        title: 'Initial Deposit',
        description: 'Payment to start the project',
        amount: totalBudget * 0.3,
        percentage: 30,
        due_date: null,
        status: 'pending',
        order: 1,
        completed_at: null,
        paid_at: null
      },
      {
        id: 'new-2',
        title: 'Concept Approval',
        description: 'Payment upon approval of initial concepts',
        amount: totalBudget * 0.3,
        percentage: 30,
        due_date: null,
        status: 'pending',
        order: 2,
        completed_at: null,
        paid_at: null
      },
      {
        id: 'new-3',
        title: 'Final Delivery',
        description: 'Final payment upon project completion',
        amount: totalBudget * 0.4,
        percentage: 40,
        due_date: null,
        status: 'pending',
        order: 3,
        completed_at: null,
        paid_at: null
      }
    ];
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Discard changes
      setEditedMilestones([...milestones]);
    }
    setIsEditing(!isEditing);
  };

  const handleMilestoneChange = (index: number, field: string, value: any) => {
    const updatedMilestones = [...editedMilestones];

    // Handle special case for amount and percentage
    if (field === 'amount' && project?.budget) {
      updatedMilestones[index].amount = parseFloat(value);
      updatedMilestones[index].percentage = (parseFloat(value) / project.budget) * 100;
    } else if (field === 'percentage' && project?.budget) {
      updatedMilestones[index].percentage = parseFloat(value);
      updatedMilestones[index].amount = (parseFloat(value) / 100) * project.budget;
    } else {
      // @ts-ignore
      updatedMilestones[index][field] = value;
    }

    setEditedMilestones(updatedMilestones);
  };

  const addMilestone = () => {
    const newOrder = editedMilestones.length > 0
      ? Math.max(...editedMilestones.map(m => m.order)) + 1
      : 1;

    const newMilestone: Milestone = {
      id: `new-${Date.now()}`,
      title: 'New Milestone',
      description: '',
      amount: 0,
      percentage: 0,
      due_date: null,
      status: 'pending',
      order: newOrder,
      completed_at: null,
      paid_at: null
    };

    setEditedMilestones([...editedMilestones, newMilestone]);
  };

  const removeMilestone = (index: number) => {
    const updatedMilestones = [...editedMilestones];
    updatedMilestones.splice(index, 1);

    // Update order
    updatedMilestones.forEach((milestone, idx) => {
      milestone.order = idx + 1;
    });

    setEditedMilestones(updatedMilestones);
  };

  const moveMilestoneUp = (index: number) => {
    if (index === 0) return;

    const updatedMilestones = [...editedMilestones];
    const temp = updatedMilestones[index];
    updatedMilestones[index] = updatedMilestones[index - 1];
    updatedMilestones[index - 1] = temp;

    // Update order
    updatedMilestones.forEach((milestone, idx) => {
      milestone.order = idx + 1;
    });

    setEditedMilestones(updatedMilestones);
  };

  const moveMilestoneDown = (index: number) => {
    if (index === editedMilestones.length - 1) return;

    const updatedMilestones = [...editedMilestones];
    const temp = updatedMilestones[index];
    updatedMilestones[index] = updatedMilestones[index + 1];
    updatedMilestones[index + 1] = temp;

    // Update order
    updatedMilestones.forEach((milestone, idx) => {
      milestone.order = idx + 1;
    });

    setEditedMilestones(updatedMilestones);
  };

  const saveMilestones = async () => {
    if (!project) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if the project_milestones table exists
      const { error: checkError } = await supabase
        .from('project_milestones')
        .select('id')
        .limit(1);

      // If the table doesn't exist, create it
      if (checkError && checkError.message.includes('does not exist')) {
        // In a real implementation, you would create the table via a migration
        // For this example, we'll just show a message
        throw new Error('The project_milestones table does not exist. Please create it first.');
      }

      // Delete existing milestones for this project
      const { error: deleteError } = await supabase
        .from('project_milestones')
        .delete()
        .eq('project_id', projectId);

      if (deleteError) throw deleteError;

      // Insert new milestones
      const milestonesToInsert = editedMilestones.map(milestone => ({
        project_id: projectId,
        title: milestone.title,
        description: milestone.description,
        amount: milestone.amount,
        percentage: milestone.percentage,
        due_date: milestone.due_date,
        status: milestone.status,
        order: milestone.order,
        completed_at: milestone.completed_at,
        paid_at: milestone.paid_at
      }));

      const { error: insertError } = await supabase
        .from('project_milestones')
        .insert(milestonesToInsert);

      if (insertError) throw insertError;

      // Refresh milestones
      const { data: refreshedMilestones, error: refreshError } = await supabase
        .from('project_milestones')
        .select('*')
        .eq('project_id', projectId)
        .order('order', { ascending: true });

      if (refreshError) throw refreshError;

      setMilestones(refreshedMilestones || []);
      setEditedMilestones(refreshedMilestones || []);
      setIsEditing(false);
      setSuccess('Milestones saved successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error: Error | unknown) {
      console.error('Error saving milestones:', error);
      setError(error instanceof Error ? error.message : 'Failed to save milestones');
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'Not set';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatus = (status: string) => {
    return status.replace('_', ' ').split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading project milestones...</p>
        </div>
      </div>
    );
  }

  if (error && !project) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            Project not found
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Link href={`/admin/projects/${projectId}`} className="mr-4">
            <Button variant="ghost" className="p-0 h-auto">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Payment Milestones</h1>
            <p className="text-gray-500">{project.title} - {project.client_name}</p>
          </div>
        </div>
        <div>
          {isEditing ? (
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleEditToggle}>
                Cancel
              </Button>
              <Button
                onClick={saveMilestones}
                disabled={saving}
                className="flex items-center"
              >
                {saving ? (
                  <span className="flex items-center">
                    <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                    Saving...
                  </span>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Milestones
                  </>
                )}
              </Button>
            </div>
          ) : (
            <Button onClick={handleEditToggle} className="flex items-center">
              <Edit className="h-4 w-4 mr-2" />
              Edit Milestones
            </Button>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Project Details</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start">
              <DollarSign className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Total Budget</p>
                <p className="font-medium">{formatCurrency(project.budget)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Clock className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Milestone Count</p>
                <p className="font-medium">{editedMilestones.length}</p>
              </div>
            </div>

            <div className="flex items-start">
              <CheckCircle className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Completed Milestones</p>
                <p className="font-medium">
                  {editedMilestones.filter(m => m.status === 'completed' || m.status === 'paid').length}
                  of {editedMilestones.length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">Payment Milestones</h2>
          {isEditing && (
            <Button size="sm" onClick={addMilestone} className="flex items-center">
              <Plus className="h-4 w-4 mr-1" />
              Add Milestone
            </Button>
          )}
        </div>
        <div className="p-6">
          {editedMilestones.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No payment milestones defined yet.</p>
              {isEditing && (
                <Button onClick={addMilestone} className="mt-4 flex items-center mx-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Milestone
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              {editedMilestones.map((milestone, index) => (
                <div key={milestone.id} className="border rounded-lg p-6">
                  {isEditing ? (
                    <div className="space-y-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 mr-4">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Title
                          </label>
                          <input
                            type="text"
                            value={milestone.title}
                            onChange={(e) => handleMilestoneChange(index, 'title', e.target.value)}
                            className="w-full px-4 py-2 border rounded-md"
                          />
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => moveMilestoneUp(index)}
                            disabled={index === 0}
                            className={`p-2 rounded-md ${
                              index === 0 ? 'text-gray-300' : 'text-gray-500 hover:bg-gray-100'
                            }`}
                          >
                            <ArrowUp className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => moveMilestoneDown(index)}
                            disabled={index === editedMilestones.length - 1}
                            className={`p-2 rounded-md ${
                              index === editedMilestones.length - 1 ? 'text-gray-300' : 'text-gray-500 hover:bg-gray-100'
                            }`}
                          >
                            <ArrowDown className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => removeMilestone(index)}
                            className="p-2 rounded-md text-red-500 hover:bg-red-50"
                          >
                            <Trash className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <textarea
                          value={milestone.description || ''}
                          onChange={(e) => handleMilestoneChange(index, 'description', e.target.value)}
                          rows={2}
                          className="w-full px-4 py-2 border rounded-md"
                        ></textarea>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Amount
                          </label>
                          <div className="relative">
                            <span className="absolute left-3 top-2 text-gray-500">$</span>
                            <input
                              type="number"
                              value={milestone.amount}
                              onChange={(e) => handleMilestoneChange(index, 'amount', e.target.value)}
                              min="0"
                              step="0.01"
                              className="w-full px-4 py-2 border rounded-md pl-8"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Percentage
                          </label>
                          <div className="relative">
                            <input
                              type="number"
                              value={milestone.percentage}
                              onChange={(e) => handleMilestoneChange(index, 'percentage', e.target.value)}
                              min="0"
                              max="100"
                              step="0.1"
                              className="w-full px-4 py-2 border rounded-md pr-8"
                            />
                            <span className="absolute right-3 top-2 text-gray-500">%</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Due Date
                          </label>
                          <input
                            type="date"
                            value={milestone.due_date ? new Date(milestone.due_date).toISOString().split('T')[0] : ''}
                            onChange={(e) => handleMilestoneChange(index, 'due_date', e.target.value)}
                            className="w-full px-4 py-2 border rounded-md"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Status
                        </label>
                        <select
                          value={milestone.status}
                          onChange={(e) => handleMilestoneChange(index, 'status', e.target.value)}
                          className="w-full px-4 py-2 border rounded-md"
                        >
                          <option value="pending">Pending</option>
                          <option value="active">Active</option>
                          <option value="completed">Completed</option>
                          <option value="paid">Paid</option>
                        </select>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-lg font-medium">{milestone.title}</h3>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(milestone.status)}`}>
                          {formatStatus(milestone.status)}
                        </span>
                      </div>

                      {milestone.description && (
                        <p className="text-gray-700 mb-4">{milestone.description}</p>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="flex items-start">
                          <DollarSign className="h-5 w-5 text-gray-400 mt-0.5 mr-2" />
                          <div>
                            <p className="text-sm text-gray-500">Amount</p>
                            <p className="font-medium">{formatCurrency(milestone.amount)}</p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="text-gray-400 mt-0.5 mr-2 w-5 h-5 flex items-center justify-center">%</div>
                          <div>
                            <p className="text-sm text-gray-500">Percentage</p>
                            <p className="font-medium">{milestone.percentage.toFixed(1)}%</p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <Calendar className="h-5 w-5 text-gray-400 mt-0.5 mr-2" />
                          <div>
                            <p className="text-sm text-gray-500">Due Date</p>
                            <p className="font-medium">{formatDate(milestone.due_date)}</p>
                          </div>
                        </div>
                      </div>

                      {(milestone.completed_at || milestone.paid_at) && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t">
                          {milestone.completed_at && (
                            <div className="flex items-start">
                              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-2" />
                              <div>
                                <p className="text-sm text-gray-500">Completed On</p>
                                <p className="font-medium">{formatDate(milestone.completed_at)}</p>
                              </div>
                            </div>
                          )}

                          {milestone.paid_at && (
                            <div className="flex items-start">
                              <DollarSign className="h-5 w-5 text-green-500 mt-0.5 mr-2" />
                              <div>
                                <p className="text-sm text-gray-500">Paid On</p>
                                <p className="font-medium">{formatDate(milestone.paid_at)}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
