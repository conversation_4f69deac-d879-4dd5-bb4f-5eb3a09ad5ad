"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";

export function AuthDebugger() {
  const { user, profile, loading } = useOptimizedAuth();
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [showDebug, setShowDebug] = useState(false);

  useEffect(() => {
    const getSessionInfo = async () => {
      // Only get session info if user exists (avoid security warning)
      if (user) {
        const { data: { session } } = await supabase.auth.getSession();
        setSessionInfo(session);
      } else {
        setSessionInfo(null);
      }
    };

    getSessionInfo();
  }, [user]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setShowDebug(!showDebug)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm shadow-lg hover:bg-blue-700"
      >
        🐛 Debug Auth
      </button>
      
      {showDebug && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-300 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <h3 className="font-bold text-sm mb-3">Authentication Debug Info</h3>
          
          <div className="space-y-3 text-xs">
            <div>
              <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
            </div>
            
            <div>
              <strong>User:</strong> {user ? 'Authenticated' : 'Not authenticated'}
              {user && (
                <div className="ml-2 text-gray-600">
                  <div>ID: {user.id}</div>
                  <div>Email: {user.email}</div>
                </div>
              )}
            </div>
            
            <div>
              <strong>Profile:</strong> {profile ? 'Loaded' : 'Not loaded'}
              {profile && (
                <div className="ml-2 text-gray-600">
                  <div>Name: {profile.full_name}</div>
                  <div>Role: {profile.role}</div>
                </div>
              )}
            </div>
            
            <div>
              <strong>Session:</strong> {sessionInfo ? 'Active' : 'None'}
              {sessionInfo && (
                <div className="ml-2 text-gray-600">
                  <div>Expires: {new Date(sessionInfo.expires_at * 1000).toLocaleString()}</div>
                  <div>Token: {sessionInfo.access_token ? 'Present' : 'Missing'}</div>
                </div>
              )}
            </div>
            
            <button
              onClick={() => {
                console.log('Auth Debug Info:', {
                  user,
                  profile,
                  loading,
                  sessionInfo
                });
              }}
              className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-200"
            >
              Log to Console
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
