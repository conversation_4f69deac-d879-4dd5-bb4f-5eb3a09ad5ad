import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/disputes/[id]/attachments
 * Uploads an attachment to a dispute
 * 
 * Request body:
 * {
 *   fileUrl: string;
 *   fileName: string;
 *   fileType: string;
 *   fileSize: number;
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const disputeId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the dispute to verify the user is involved
    const { data: dispute, error: disputeError } = await supabase
      .from('disputes')
      .select('client_id, designer_id, status')
      .eq('id', disputeId)
      .single();
    
    if (disputeError) {
      return NextResponse.json(
        { error: 'Dispute not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to add attachments to this dispute
    const isAdmin = profile.role === 'admin';
    const isInvolved = dispute.client_id === user.id || dispute.designer_id === user.id;
    
    if (!isAdmin && !isInvolved) {
      return NextResponse.json(
        { error: 'You do not have permission to add attachments to this dispute' },
        { status: 403 }
      );
    }
    
    // Check if the dispute is closed
    if (dispute.status === 'closed' && !isAdmin) {
      return NextResponse.json(
        { error: 'This dispute is closed and cannot receive new attachments' },
        { status: 400 }
      );
    }
    
    const { fileUrl, fileName, fileType, fileSize } = await request.json();
    
    // Validate required fields
    if (!fileUrl || !fileName) {
      return NextResponse.json(
        { error: 'File URL and file name are required' },
        { status: 400 }
      );
    }
    
    // Create the attachment
    const { data: attachment, error: attachmentError } = await supabase
      .from('dispute_attachments')
      .insert({
        dispute_id: disputeId,
        file_url: fileUrl,
        file_name: fileName,
        file_type: fileType || null,
        file_size: fileSize || null,
        uploaded_by: user.id
      })
      .select(`
        id,
        dispute_id,
        file_url,
        file_name,
        file_type,
        file_size,
        uploaded_by,
        created_at,
        profiles:uploaded_by (
          full_name,
          role
        )
      `)
      .single();
    
    if (attachmentError) {
      console.error('Error creating dispute attachment:', attachmentError);
      return NextResponse.json(
        { error: 'Failed to create attachment' },
        { status: 500 }
      );
    }
    
    // Create notifications for other parties
    const notifyUserIds: string[] = [];
    
    // Always notify admins if the uploader is not an admin
    if (!isAdmin) {
      const { data: admins } = await supabase
        .from('profiles')
        .select('id')
        .eq('role', 'admin');
      
      if (admins && admins.length > 0) {
        notifyUserIds.push(...admins.map(admin => admin.id));
      }
    }
    
    // Notify the other party (client or designer)
    if (user.id === dispute.client_id) {
      notifyUserIds.push(dispute.designer_id);
    } else if (user.id === dispute.designer_id) {
      notifyUserIds.push(dispute.client_id);
    } else if (isAdmin) {
      // If admin is uploading, notify both client and designer
      notifyUserIds.push(dispute.client_id, dispute.designer_id);
    }
    
    // Remove duplicates and the uploader
    const uniqueNotifyUserIds = [...new Set(notifyUserIds)].filter(id => id !== user.id);
    
    if (uniqueNotifyUserIds.length > 0) {
      const notifications = uniqueNotifyUserIds.map(userId => ({
        user_id: userId,
        type: 'dispute',
        title: 'New Dispute Attachment',
        content: `New file uploaded to dispute: ${fileName}`,
        related_id: disputeId,
        read: false
      }));
      
      await supabase
        .from('notifications')
        .insert(notifications);
    }
    
    return NextResponse.json(attachment, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/disputes/[id]/attachments:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/disputes/[id]/attachments
 * Gets all attachments for a dispute
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const disputeId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Get the dispute to verify the user is involved
    const { data: dispute, error: disputeError } = await supabase
      .from('disputes')
      .select('client_id, designer_id')
      .eq('id', disputeId)
      .single();
    
    if (disputeError) {
      return NextResponse.json(
        { error: 'Dispute not found' },
        { status: 404 }
      );
    }
    
    // Check if the user has permission to view attachments for this dispute
    const isAdmin = profile.role === 'admin';
    const isInvolved = dispute.client_id === user.id || dispute.designer_id === user.id;
    
    if (!isAdmin && !isInvolved) {
      return NextResponse.json(
        { error: 'You do not have permission to view attachments for this dispute' },
        { status: 403 }
      );
    }
    
    // Get the attachments
    const { data: attachments, error: attachmentsError } = await supabase
      .from('dispute_attachments')
      .select(`
        id,
        dispute_id,
        file_url,
        file_name,
        file_type,
        file_size,
        uploaded_by,
        created_at,
        profiles:uploaded_by (
          full_name,
          role
        )
      `)
      .eq('dispute_id', disputeId)
      .order('created_at', { ascending: true });
    
    if (attachmentsError) {
      console.error('Error fetching dispute attachments:', attachmentsError);
      return NextResponse.json(
        { error: 'Failed to fetch attachments' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(attachments, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/disputes/[id]/attachments:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
