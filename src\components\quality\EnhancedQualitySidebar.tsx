"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { LogoIcon } from "@/components/shared/LogoIcon";
import {
  Star,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Users,
  User,
  ChevronDown,
  HelpCircle
} from "lucide-react";

const navigationItems = [
  {
    name: "Dashboard",
    href: "/quality/dashboard",
    icon: BarChart3,
    description: "Overview and metrics"
  },
  {
    name: "Pending Reviews",
    href: "/quality/reviews",
    icon: Clock,
    description: "Items awaiting review"
  },
  {
    name: "Quality Standards",
    href: "/quality/standards",
    icon: Star,
    description: "Review criteria and guidelines"
  },
  {
    name: "Review History",
    href: "/quality/history",
    icon: FileText,
    description: "Completed reviews"
  },
  {
    name: "Analytics",
    href: "/quality/analytics",
    icon: BarChart3,
    description: "Performance insights"
  },
  {
    name: "Team Performance",
    href: "/quality/performance",
    icon: TrendingUp,
    description: "Team metrics"
  },
  {
    name: "Settings",
    href: "/quality/settings",
    icon: Settings,
    description: "Quality preferences"
  }
];

const motionVariants = {
  menuItem: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.3 }
  }
};

interface EnhancedQualitySidebarProps {
  isCollapsed?: boolean;
}

export function EnhancedQualitySidebar({ isCollapsed = false }: EnhancedQualitySidebarProps = {}) {
  const { user, profile, signOut } = useOptimizedAuth();
  const pathname = usePathname();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  // Add active state to navigation items
  const enhancedNavigationItems = navigationItems.map(item => ({
    ...item,
    active: pathname === item.href || pathname.startsWith(item.href + '/')
  }));

  return (
    <>
      {/* Sidebar Content - No positioning, handled by CollapsibleSidebar */}
      <div className="flex flex-col h-full bg-white">
        {/* Header */}
        <div className={`flex-shrink-0 border-b border-gray-100 transition-all duration-300 ${
          isCollapsed ? 'p-2' : 'p-6'
        }`}>
          <div className={`${isCollapsed ? 'mb-2' : 'mb-4'}`}>
            <Link href="/quality/dashboard" className="flex items-center group">
              <LogoIcon size={isCollapsed ? "sm" : "md"} className={isCollapsed ? "" : "mr-4"} />
              {!isCollapsed && (
                <div>
                  <h2 className="font-bold text-xl text-gray-900 group-hover:text-brown-700 transition-colors">
                    Quality Team
                  </h2>
                  <p className="text-sm text-brown-600 font-medium">Review & Standards</p>
                </div>
              )}
            </Link>
          </div>
        </div>

        {/* Navigation */}
        <nav className={`flex-1 overflow-y-auto transition-all duration-300 ${
          isCollapsed ? 'p-2' : 'p-4'
        }`}>
          {isCollapsed ? (
            /* Collapsed Navigation - Icons Only */
            <div className="space-y-1">
              {enhancedNavigationItems.slice(0, 6).map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300 group relative ${
                    item.active
                      ? "bg-brown-600 text-white shadow-lg shadow-brown-600/25"
                      : "text-gray-700 hover:bg-brown-50 hover:text-brown-700"
                  }`}
                  title={item.name}
                  aria-label={item.name}
                >
                  <item.icon className={`h-5 w-5 ${
                    item.active ? "text-white" : "text-gray-500 group-hover:text-brown-600"
                  }`} />
                </Link>
              ))}
            </div>
          ) : (
            /* Expanded Navigation */
            <div className="space-y-2">
              {enhancedNavigationItems.map((item) => (
                <motion.div
                  key={item.name}
                  variants={motionVariants.menuItem}
                  initial="initial"
                  animate="animate"
                >
                  <Link
                    href={item.href}
                    className={`flex items-center px-4 py-3 rounded-xl transition-all duration-300 group relative ${
                      item.active
                        ? "bg-brown-600 text-white shadow-lg shadow-brown-600/25"
                        : "text-gray-700 hover:bg-brown-50 hover:text-brown-700"
                    }`}
                    role="menuitem"
                    aria-label={item.description || item.name}
                    tabIndex={0}
                  >
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <item.icon className={`h-5 w-5 mr-3 ${
                        item.active ? "text-white" : "text-gray-500 group-hover:text-brown-600"
                      }`} />
                    </motion.div>

                    <div className="flex-1 min-w-0">
                      <span className="font-medium block leading-tight">{item.name}</span>
                      {item.description && !item.active && (
                        <span className="text-xs text-gray-500 block leading-tight mt-0.5 break-words">
                          {item.description}
                        </span>
                      )}
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
          )}
        </nav>

        {/* Profile Section */}
        <div className={`flex-shrink-0 border-t border-gray-100 bg-gray-50 transition-all duration-300 ${
          isCollapsed ? 'p-2' : 'p-4'
        }`}>
          <div className="relative">
            {/* Profile Info and Dropdown Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              className={`flex items-center rounded-xl hover:bg-white transition-all duration-200 shadow-sm ${
                isCollapsed
                  ? 'p-1 justify-center w-10 h-10 mx-auto'
                  : 'px-3 py-3 w-full'
              }`}
            >
              <div className={`bg-brown-600 rounded-full flex items-center justify-center shadow-md transition-all duration-300 ${
                isCollapsed ? 'w-8 h-8' : 'w-10 h-10'
              }`}>
                {profile?.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.full_name}
                    className={`rounded-full object-cover transition-all duration-300 ${
                      isCollapsed ? 'w-8 h-8' : 'w-10 h-10'
                    }`}
                  />
                ) : (
                  <span className={`text-white font-bold transition-all duration-300 ${
                    isCollapsed ? 'text-sm' : 'text-base'
                  }`}>
                    {profile?.full_name?.charAt(0) || "Q"}
                  </span>
                )}
              </div>
              {!isCollapsed && (
                <>
                  <div className="flex-1 text-left min-w-0 ml-3">
                    <p className="text-sm font-semibold text-gray-900 leading-tight break-words">
                      {profile?.full_name || "Quality Team"}
                    </p>
                    <p className="text-xs text-gray-500 leading-tight break-words">
                      Quality Reviewer
                    </p>
                  </div>
                  <motion.div
                    animate={{ rotate: isProfileMenuOpen ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  </motion.div>
                </>
              )}
            </motion.button>

            <AnimatePresence>
              {isProfileMenuOpen && !isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute bottom-full mb-2 w-full bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden"
                >
                  <ul>
                    <li>
                      <Link
                        href="/quality/profile"
                        className="flex items-center px-4 py-3 hover:bg-gray-50 transition-colors"
                        onClick={() => setIsProfileMenuOpen(false)}
                      >
                        <Settings className="h-4 w-4 mr-3 text-gray-500" />
                        <span className="text-sm font-medium">Profile Settings</span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/quality/help"
                        className="flex items-center px-4 py-3 hover:bg-gray-50 transition-colors"
                        onClick={() => setIsProfileMenuOpen(false)}
                      >
                        <HelpCircle className="h-4 w-4 mr-3 text-gray-500" />
                        <span className="text-sm font-medium">Help & Support</span>
                      </Link>
                    </li>
                    <li className="border-t border-gray-100">
                      <button
                        onClick={handleSignOut}
                        disabled={isSigningOut}
                        className="w-full flex items-center px-4 py-3 text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSigningOut ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3" />
                        ) : (
                          <LogOut className="h-4 w-4 mr-3" />
                        )}
                        <span className="text-sm font-medium">
                          {isSigningOut ? 'Signing Out...' : 'Sign Out'}
                        </span>
                      </button>
                    </li>
                  </ul>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </>
  );
}
