"use client";

import { motion } from "framer-motion";

interface LogoIconProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  animated?: boolean;
}

export function LogoIcon({ className = "", size = "md", animated = true }: LogoIconProps) {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12", 
    lg: "w-16 h-16"
  };

  const LogoComponent = (
    <div className={`${sizeClasses[size]} bg-brown-600 rounded-xl flex items-center justify-center shadow-lg ${className}`}>
      <img
        src="/seniors-icon.svg"
        alt="Seniors Architecture Firm"
        className="w-full h-full object-contain p-2"
      />
    </div>
  );

  if (animated) {
    return (
      <motion.div
        whileHover={{ scale: 1.05 }}
        transition={{ duration: 0.2 }}
      >
        {LogoComponent}
      </motion.div>
    );
  }

  return LogoComponent;
}
