"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Star,
  Eye,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  User,
  Download,
  BarChart3
} from "lucide-react";

interface QualityReviewHistory {
  id: string;
  project_id: string;
  designer_id: string;
  review_type: string;
  status: string;
  overall_score: number | null;
  feedback: string | null;
  revision_count: number;
  reviewed_at: string;
  created_at: string;
  project: {
    title: string;
    client: {
      full_name: string;
    };
  };
  designer: {
    full_name: string;
    email: string;
  };
}

export default function QualityHistoryPage() {
  const { user, profile } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReviewHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<string>('30');

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchReviewHistory();
    }
  }, [user, profile, filter, dateRange]);

  const fetchReviewHistory = async () => {
    try {
      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        console.error('No access token available');
        return;
      }

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(dateRange));

      // Use API endpoint with proper authentication
      const params = new URLSearchParams({
        status: filter,
        limit: '100',
        page: '1'
      });

      const response = await fetch(`/api/quality/reviews?${params}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      // Filter by date range and completed reviews only
      const filteredReviews = (result.reviews || []).filter((review: QualityReviewHistory) => {
        const reviewDate = new Date(review.reviewed_at || review.created_at);
        const isInDateRange = reviewDate >= startDate && reviewDate <= endDate;
        const isCompleted = ['approved', 'rejected', 'needs_revision'].includes(review.status);
        return isInDateRange && isCompleted;
      });

      setReviews(filteredReviews);
    } catch (error) {
      console.error('Error fetching review history:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'needs_revision':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case 'needs_revision':
        return `${baseClasses} bg-orange-100 text-orange-800 border border-orange-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const exportHistory = () => {
    const csvContent = [
      ['Date', 'Project', 'Designer', 'Client', 'Status', 'Score', 'Revisions', 'Type'].join(','),
      ...reviews.map(review => [
        new Date(review.reviewed_at || review.created_at).toLocaleDateString(),
        `"${review.project?.title || 'Untitled'}"`,
        `"${review.designer?.full_name || 'Unknown'}"`,
        `"${review.project?.client?.full_name || 'Unknown'}"`,
        review.status,
        review.overall_score || '',
        review.revision_count,
        review.review_type
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `quality-review-history-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const filteredReviews = reviews.filter(review =>
    review.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    review.designer?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    review.project?.client?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate stats
  const totalReviews = filteredReviews.length;
  const approvedCount = filteredReviews.filter(r => r.status === 'approved').length;
  const rejectedCount = filteredReviews.filter(r => r.status === 'rejected').length;
  const revisionCount = filteredReviews.filter(r => r.status === 'needs_revision').length;
  const averageScore = filteredReviews.length > 0 
    ? filteredReviews.reduce((sum, r) => sum + (r.overall_score || 0), 0) / filteredReviews.filter(r => r.overall_score).length
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Review History</h1>
          <p className="text-gray-600 mt-2">Track and analyze completed quality reviews</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={exportHistory}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export CSV
          </Button>
          <Button
            onClick={fetchReviewHistory}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Reviews</p>
              <p className="text-2xl font-bold text-blue-600">{totalReviews}</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">{approvedCount}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-red-600">{rejectedCount}</p>
            </div>
            <XCircle className="h-8 w-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Revisions</p>
              <p className="text-2xl font-bold text-orange-600">{revisionCount}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Score</p>
              <p className="text-2xl font-bold text-purple-600">{averageScore.toFixed(1)}</p>
            </div>
            <Star className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="all">All Reviews</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="needs_revision">Needs Revision</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-gray-400" />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 3 months</option>
              <option value="365">Last year</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects, designers, or clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Reviews History List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Review History</h2>
          <p className="text-gray-600 mt-1">Complete history of your quality reviews</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredReviews.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No review history found matching your criteria</p>
            </div>
          ) : (
            filteredReviews.map((review) => (
              <div key={review.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(review.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {review.project?.title || 'Untitled Project'}
                      </h3>
                      <span className={getStatusBadge(review.status)}>
                        {review.status.replace('_', ' ').toUpperCase()}
                      </span>
                      {review.overall_score && (
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm font-medium">{review.overall_score}/5</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium">Designer:</span> {review.designer?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium">Client:</span> {review.project?.client?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">Reviewed:</span> {new Date(review.reviewed_at || review.created_at).toLocaleDateString()}
                      </div>
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="font-medium">Revisions:</span> {review.revision_count}
                      </div>
                    </div>

                    {review.feedback && (
                      <div className="bg-gray-50 rounded-lg p-3 mt-3">
                        <p className="text-sm text-gray-700 font-medium mb-1">Feedback:</p>
                        <p className="text-sm text-gray-600">{review.feedback}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
