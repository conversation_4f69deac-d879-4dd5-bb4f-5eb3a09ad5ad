"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { paypalSDK, getOptimizedPayPalConfig } from '@/lib/paypal-sdk';

interface PayPalContextType {
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  reload: () => Promise<void>;
}

const PayPalContext = createContext<PayPalContextType | undefined>(undefined);

interface PayPalProviderProps {
  children: React.ReactNode;
}

export function PayPalProvider({ children }: PayPalProviderProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadPayPalSDK = async () => {
    if (paypalSDK.isLoaded()) {
      setIsLoaded(true);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const config = getOptimizedPayPalConfig();
      await paypalSDK.loadSDK(config);
      setIsLoaded(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load PayPal SDK');
      console.error('PayPal SDK loading error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const reload = async () => {
    setIsLoaded(false);
    await loadPayPalSDK();
  };

  // Preload PayPal SDK when provider mounts
  useEffect(() => {
    // Only load if PayPal Client ID is configured
    const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
    if (clientId && clientId !== 'YOUR_PAYPAL_CLIENT_ID_HERE') {
      loadPayPalSDK();
    }
  }, []);

  const value: PayPalContextType = {
    isLoaded,
    isLoading,
    error,
    reload
  };

  return (
    <PayPalContext.Provider value={value}>
      {children}
    </PayPalContext.Provider>
  );
}

export function usePayPal() {
  const context = useContext(PayPalContext);
  if (context === undefined) {
    throw new Error('usePayPal must be used within a PayPalProvider');
  }
  return context;
}
