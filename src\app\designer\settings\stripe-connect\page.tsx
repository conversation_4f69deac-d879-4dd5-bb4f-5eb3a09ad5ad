"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  CreditCard,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  RefreshCw,
  DollarSign,
  Shield,
  Clock,
  ArrowRight,
  Info,
  Building2,
  Globe,
  User
} from "lucide-react";

interface StripeAccount {
  id: string;
  stripe_account_id: string;
  account_status: 'pending' | 'active' | 'restricted' | 'inactive';
  country: string;
  business_type: string;
  onboarding_completed: boolean;
  payouts_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export default function StripeConnectPage() {
  const { user } = useOptimizedAuth();
  const [stripeAccount, setStripeAccount] = useState<StripeAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchStripeAccount();
    }
  }, [user]);

  const fetchStripeAccount = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('designer_stripe_accounts')
        .select('*')
        .eq('designer_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      setStripeAccount(data);
    } catch (error: any) {
      console.error('Error fetching Stripe account:', error);
      setError(error.message || 'Failed to fetch Stripe account information');
    } finally {
      setLoading(false);
    }
  };

  const createStripeAccount = async () => {
    if (!user) return;

    setCreating(true);
    setError(null);

    try {
      const { data, error } = await supabase.functions.invoke('create-connect-account', {
        body: {
          designerId: user.id,
          email: user.email,
          country: 'US', // Default to US, could be made configurable
          businessType: 'individual'
        }
      });

      if (error) throw error;

      if (data.existing) {
        setSuccess('Stripe account found. Redirecting to complete setup...');
        setStripeAccount({
          id: data.accountId,
          stripe_account_id: data.accountId,
          account_status: data.status,
          country: 'US',
          business_type: 'individual',
          onboarding_completed: false,
          payouts_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      } else {
        setSuccess('Stripe account created successfully. Redirecting to onboarding...');
        // Redirect to Stripe onboarding
        if (data.onboardingUrl) {
          window.location.href = data.onboardingUrl;
        }
      }

      await fetchStripeAccount();

    } catch (error: any) {
      console.error('Error creating Stripe account:', error);
      setError(error.message || 'Failed to create Stripe account');
    } finally {
      setCreating(false);
    }
  };

  const refreshAccountStatus = async () => {
    if (!stripeAccount) return;

    setLoading(true);
    try {
      // This would typically call a function to refresh account status from Stripe
      // For now, we'll just refetch from our database
      await fetchStripeAccount();
      setSuccess('Account status refreshed');
    } catch (error: any) {
      console.error('Error refreshing account status:', error);
      setError(error.message || 'Failed to refresh account status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50 border-green-200';
      case 'pending': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'restricted': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending': return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'restricted': return <AlertCircle className="h-5 w-5 text-red-600" />;
      default: return <Info className="h-5 w-5 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Setup</h1>
        <p className="text-gray-600">
          Connect your Stripe account to receive payments for completed projects
        </p>
      </div>

      {/* Alerts */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {!stripeAccount ? (
        /* No Stripe Account - Setup Required */
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="p-8 text-center">
            <div className="mx-auto w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-6">
              <CreditCard className="h-8 w-8 text-blue-600" />
            </div>
            
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Set Up Your Payment Account
            </h2>
            
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              To receive payments for your design work, you need to connect a Stripe account. 
              This is secure and takes just a few minutes.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-green-50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">Secure</h3>
                <p className="text-sm text-gray-600">
                  Bank-level security with Stripe's trusted platform
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">Fast Setup</h3>
                <p className="text-sm text-gray-600">
                  Complete setup in under 5 minutes
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-50 rounded-full flex items-center justify-center mx-auto mb-3">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-medium text-gray-900 mb-2">Quick Payouts</h3>
                <p className="text-sm text-gray-600">
                  Receive payments within 2-3 business days
                </p>
              </div>
            </div>

            <Button
              onClick={createStripeAccount}
              disabled={creating}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
            >
              {creating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Setting up...
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Connect Stripe Account
                </>
              )}
            </Button>

            <p className="text-xs text-gray-500 mt-4">
              By connecting your account, you agree to Stripe's terms of service
            </p>
          </div>
        </div>
      ) : (
        /* Existing Stripe Account */
        <div className="space-y-6">
          {/* Account Status Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Account Status</h2>
              <Button
                onClick={refreshAccountStatus}
                variant="outline"
                size="sm"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center space-x-3 mb-4">
                  {getStatusIcon(stripeAccount.account_status)}
                  <div>
                    <p className="font-medium text-gray-900">Account Status</p>
                    <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full border ${getStatusColor(stripeAccount.account_status)}`}>
                      {stripeAccount.account_status.charAt(0).toUpperCase() + stripeAccount.account_status.slice(1)}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Onboarding Complete</span>
                    {stripeAccount.onboarding_completed ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <Clock className="h-4 w-4 text-yellow-600" />
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Payouts Enabled</span>
                    {stripeAccount.payouts_enabled ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <Clock className="h-4 w-4 text-yellow-600" />
                    )}
                  </div>
                </div>
              </div>

              <div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">Country: {stripeAccount.country}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {stripeAccount.business_type === 'individual' ? (
                      <User className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Building2 className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="text-sm text-gray-600">
                      Type: {stripeAccount.business_type.charAt(0).toUpperCase() + stripeAccount.business_type.slice(1)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      Created: {new Date(stripeAccount.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {stripeAccount.account_status === 'pending' && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                  <div>
                    <h3 className="font-medium text-yellow-800 mb-1">Setup Required</h3>
                    <p className="text-sm text-yellow-700 mb-3">
                      Your Stripe account setup is incomplete. Complete the onboarding process to start receiving payments.
                    </p>
                    <Button
                      onClick={() => window.open('https://connect.stripe.com/setup/s/' + stripeAccount.stripe_account_id, '_blank')}
                      size="sm"
                      className="bg-yellow-600 hover:bg-yellow-700 text-white"
                    >
                      Complete Setup
                      <ExternalLink className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {stripeAccount.account_status === 'active' && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                  <div>
                    <h3 className="font-medium text-green-800 mb-1">Ready to Receive Payments</h3>
                    <p className="text-sm text-green-700">
                      Your account is fully set up and ready to receive payments. Payouts will be processed automatically according to the platform schedule.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </motion.div>

          {/* Payout Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-lg border border-gray-200 p-6"
          >
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payout Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-3">How Payouts Work</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <ArrowRight className="h-4 w-4 mt-0.5 mr-2 text-gray-400" />
                    Payments are processed automatically when milestones are completed
                  </li>
                  <li className="flex items-start">
                    <ArrowRight className="h-4 w-4 mt-0.5 mr-2 text-gray-400" />
                    Platform fee (15%) is deducted before payout
                  </li>
                  <li className="flex items-start">
                    <ArrowRight className="h-4 w-4 mt-0.5 mr-2 text-gray-400" />
                    Payouts are sent weekly on Mondays
                  </li>
                  <li className="flex items-start">
                    <ArrowRight className="h-4 w-4 mt-0.5 mr-2 text-gray-400" />
                    Funds arrive in 2-3 business days
                  </li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Fee Structure</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Platform Fee:</span>
                    <span className="font-medium">15%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment Processing:</span>
                    <span className="font-medium">2.9%</span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span className="text-gray-900 font-medium">You Receive:</span>
                    <span className="font-bold text-green-600">~82%</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
