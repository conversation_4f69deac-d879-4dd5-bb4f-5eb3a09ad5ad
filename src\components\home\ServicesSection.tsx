"use client";

import React, { useRef, useState, useEffect } from "react";
import { motion, AnimatePresence, useMotionValue, useTransform } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "../ui/button";
import { ArrowRight, ChevronRight, ChevronLeft } from "lucide-react";
import { toUrlSafeServiceId } from "@/lib/service-utils";
import Image from "next/image";

// Updated services with multiple images
const services = [
  {
    title: "Creative Design & Branding",
    images: [
      "/services-images/branding.jpg",
      "/services-images/branding-detail-1.jpg",
      "/services-images/branding-detail-2.jpg",
    ],
    description: "Unique architectural identities",
    span: ""
  },
  {
    title: "Innovative Architectural Design",
    images: [
      "/services-images/innovative-architecture.jpg",
      "/services-images/innovative-architecture-detail-1.jpg",
      "/services-images/innovative-architecture-detail-2.jpg",
    ],
    description: "Cutting-edge structural solutions",
    span: ""
  },
  {
    title: "Interior Design",
    images: [
      "/services-images/interior-design.jpg",
      "/services-images/interior-design-detail-1.jpg",
      "/services-images/interior-design-detail-2.jpg",
    ],
    description: "Immersive interior spaces",
    span: ""
  },
  {
    title: "Urban & Architectural Planning",
    images: [
      "/services-images/urban-plan.jpg",
      "/services-images/urban-plan-detail-1.jpg",
      "/services-images/urban-plan-detail-2.jpg",
    ],
    description: "Sustainable community solutions",
    span: ""
  },
  {
    title: "Residential & Commercial Projects",
    images: [
      "/services-images/residential.jpg",
      "/services-images/residential-detail-1.jpg",
      "/services-images/residential-detail-2.jpg",
    ],
    description: "Tailored architectural solutions",
    span: ""
  },
  {
    title: "Landscape and Architecture Integration",
    images: [
      "/services-images/landscape-and-architure.jpg",
      "/services-images/landscape-and-architure-detail-1.jpg",
      "/services-images/landscape-and-architure-detail-2.jpg",
    ],
    description: "Connecting built & natural environments",
    span: ""
  },
  {
    title: "Educational & Community-Oriented Spaces",
    images: [
      "/services-images/community-and-educational.jpg",
      "/services-images/community-and-educational-detail-1.jpg",
      "/services-images/community-and-educational-detail-2.jpg",
    ],
    description: "Designing for learning and community engagement",
    span: "lg:col-span-2"
  },
];

interface Service {
  title: string;
  images: string[];
  description: string;
  span: string;
}

const ServiceCard = ({ service, index }: { service: Service; index: number }) => {
  // State for image carousel
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);

  // State for mobile interactions
  const [interactionState, setInteractionState] = useState<'initial' | 'expanded' | 'ready-to-navigate'>('initial');
  const [isExpanded, setIsExpanded] = useState(false);

  // Refs for touch/swipe handling
  const cardRef = useRef<HTMLDivElement>(null);
  const touchStartX = useRef<number | null>(null);
  const touchEndX = useRef<number | null>(null);

  // Motion values for 3D tilt effect
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  // Transform mouse position into rotation values
  const rotateX = useTransform(y, [-300, 300], [10, -10]); // Reversed for natural tilt
  const rotateY = useTransform(x, [-300, 300], [-10, 10]);

  // Handle mouse move for 3D tilt effect (desktop only)
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // Calculate distance from center (normalized)
    x.set(e.clientX - centerX);
    y.set(e.clientY - centerY);
  };

  // Reset tilt when mouse leaves
  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  // Auto-cycle images on hover for desktop
  const startImageCycle = () => {
    setIsHovering(true);
    // Reset to first image when hover starts
    setCurrentImageIndex(0);
  };

  const stopImageCycle = () => {
    setIsHovering(false);
    // Reset to first image when hover ends
    setCurrentImageIndex(0);
  };

  // Handle tap for mobile devices - progressive disclosure
  const handleTap = (e: React.MouseEvent) => {
    if (interactionState === 'initial') {
      // First tap: expand card and show details
      e.preventDefault();
      setInteractionState('expanded');
      setIsExpanded(true);
      return;
    }

    if (interactionState === 'expanded') {
      // Second tap: prepare for navigation on next tap
      e.preventDefault();
      setInteractionState('ready-to-navigate');
      return;
    }

    // Third tap or 'ready-to-navigate' state: allow navigation (default link behavior)
  };

  // Reset interaction state after timeout
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (interactionState !== 'initial') {
      timeout = setTimeout(() => {
        setInteractionState('initial');
        setIsExpanded(false);
      }, 5000); // Reset after 5 seconds of inactivity
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [interactionState]);

  // Handle touch events for swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;

    const diffX = touchStartX.current - touchEndX.current;
    const threshold = 50; // Minimum swipe distance

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0) {
        // Swiped left - next image
        setCurrentImageIndex((prev) => (prev + 1) % service.images.length);
      } else {
        // Swiped right - previous image
        setCurrentImageIndex((prev) => (prev === 0 ? service.images.length - 1 : prev - 1));
      }
    }

    // Reset touch coordinates
    touchStartX.current = null;
    touchEndX.current = null;
  };

  // Effect to cycle through images when hovering
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isHovering) {
      interval = setInterval(() => {
        setCurrentImageIndex((prev) => (prev + 1) % service.images.length);
      }, 2000); // Change image every 2 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isHovering, service.images.length]);

  // Manual image navigation
  const goToNextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev + 1) % service.images.length);
  };

  const goToPrevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev === 0 ? service.images.length - 1 : prev - 1));
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}
      style={{
        // Apply 3D tilt effect only on desktop
        rotateX: rotateX,
        rotateY: rotateY,
        transformPerspective: 1000,
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={startImageCycle}
      onMouseLeave={() => {
        stopImageCycle();
        handleMouseLeave();
      }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onClick={handleTap}
      className={`shadow-md hover:shadow-xl relative overflow-hidden group h-[400px] ${service.span} cursor-pointer transition-all duration-500 ${isExpanded ? 'scale-[1.02]' : ''}`}
    >
      {/* Image Carousel */}
      <div className="absolute inset-0 w-full h-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentImageIndex}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="absolute inset-0"
          >
            <Image
              src={service.images[currentImageIndex]}
              alt={service.title}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="object-cover transition-all duration-700 group-hover:scale-105 group-hover:brightness-110"
              priority={index < 3} // Only prioritize loading for first few cards
            />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Image navigation arrows - visible on hover or when expanded */}
      <div className={`absolute inset-x-0 top-1/2 -translate-y-1/2 flex justify-between px-4 z-30 opacity-0 ${isHovering || isExpanded ? 'opacity-100' : ''} transition-opacity duration-300`}>
        <button
          onClick={goToPrevImage}
          className="bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
          aria-label="Previous image"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>
        <button
          onClick={goToNextImage}
          className="bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
          aria-label="Next image"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>

      {/* Image indicators */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-20">
        {service.images.map((_, i) => (
          <div
            key={i}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              i === currentImageIndex ? 'bg-white scale-125' : 'bg-white/50'
            }`}
          />
        ))}
      </div>

      {/* Mobile interaction state indicator */}
      <div className={`absolute top-4 right-4 md:hidden transition-all duration-300 z-20 ${
        interactionState === 'initial' ? 'bg-black/50 rounded-full p-1' :
        interactionState === 'expanded' ? 'bg-primary/80 rounded-full p-1' :
        'bg-green-500/80 rounded-full p-1'
      }`}>
        {interactionState === 'initial' && <ChevronRight className="h-4 w-4 text-white" />}
        {interactionState === 'expanded' && <span className="text-white text-xs px-1">Tap again</span>}
        {interactionState === 'ready-to-navigate' && <ArrowRight className="h-4 w-4 text-white" />}
      </div>

      {/* Swipe indicator for mobile - briefly shown on first interaction */}
      {interactionState === 'expanded' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: [0, 1, 0] }}
          transition={{ duration: 2, times: [0, 0.2, 1] }}
          className="absolute inset-x-0 top-1/2 -translate-y-1/2 flex justify-between items-center px-8 pointer-events-none"
        >
          <div className="bg-black/30 rounded-full p-2">
            <ChevronLeft className="h-6 w-6 text-white" />
          </div>
          <div className="bg-black/30 rounded-full p-2">
            <ChevronRight className="h-6 w-6 text-white" />
          </div>
        </motion.div>
      )}

      {/* Gradient Overlay for text readability */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30 opacity-80 group-hover:opacity-90 transition-opacity duration-300"></div>

      {/* Decorative top line */}
      <div className="absolute top-0 left-0 w-full h-1 bg-primary transform origin-left transition-transform duration-300 scale-x-0 group-hover:scale-x-100 z-10"></div>

      {/* Content Container */}
      <div className="absolute inset-0 flex flex-col justify-end p-8 text-white z-10">
        {/* Title with modern typography */}
        <h3 className="text-2xl font-bold mb-3 tracking-tight transform transition-transform duration-300 group-hover:translate-y-[-8px]">
          {service.title}
        </h3>

        {/* Description - only visible on hover/interaction */}
        <div className={`overflow-hidden transition-all duration-500 ${isHovering || isExpanded ? 'h-auto opacity-100' : 'h-0 opacity-0'}`}>
          <p className="text-gray-200 mb-6 font-light transform transition-all duration-500 delay-100">
            {service.description}
          </p>
        </div>
        {/* Dual CTAs - only visible on hover/interaction */}
        <div className={`flex flex-col space-y-3 transform transition-all duration-300 ${isHovering || isExpanded ? 'translate-y-[-8px] opacity-100' : 'opacity-0'}`}>
          {/* Primary CTA - Build Your Vision */}
          <Link
            href={`/vision-builder/${toUrlSafeServiceId(service.title)}`}
            className="inline-block"
            onClick={(e) => {
              // Only navigate if in ready-to-navigate state on mobile
              if (interactionState !== 'ready-to-navigate' && window.innerWidth < 768) {
                e.preventDefault();
              }
            }}
          >
            <div className="group/link relative overflow-hidden">
              <span className="inline-block py-2 text-white font-medium transition-all duration-300 border-b border-transparent group-hover/link:border-white">
                Build Your Vision <ArrowRight className="h-4 w-4 ml-1 inline-block transform transition-transform duration-300 group-hover/link:translate-x-1" />
              </span>
            </div>
          </Link>

          {/* Secondary CTA - View Service Details */}
          <Link
            href={`/services/${toUrlSafeServiceId(service.title)}`}
            className="inline-block"
            onClick={(e) => {
              // Only navigate if in ready-to-navigate state on mobile
              if (interactionState !== 'ready-to-navigate' && window.innerWidth < 768) {
                e.preventDefault();
              }
            }}
          >
            <div className="group/link relative overflow-hidden">
              <span className="inline-block py-2 text-white font-medium transition-all duration-300 border-b border-transparent group-hover/link:border-white">
                View Details <ArrowRight className="h-4 w-4 ml-1 inline-block transform transition-transform duration-300 group-hover/link:translate-x-1" />
              </span>
            </div>
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

const ServicesSection = () => {
  const sectionRef = useRef(null);

  return (
    <section
      id="services-section"
      ref={sectionRef}
      className="py-24 bg-gray-50 relative"
    >
      {/* Visual connector from previous section */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[1px] h-20 bg-gradient-to-b from-primary/50 to-transparent" />

      <div className="container mx-auto px-4">
        {/* Section header with modern typography */}
        <div className="max-w-3xl mx-auto">
          <motion.div
            className="mb-20"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="h-[1px] w-12 bg-primary mr-4"></div>
              <span className="text-primary uppercase tracking-widest text-sm font-medium">What we offer</span>
            </div>

            <motion.h2
              className="text-4xl md:text-6xl font-bold tracking-tight leading-tight"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Our Services
            </motion.h2>

            <motion.p
              className="text-gray-600 mt-6 text-lg"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              We provide comprehensive architectural solutions tailored to your unique vision and requirements.
            </motion.p>
          </motion.div>
        </div>

        {/* Services grid with improved spacing - only last item spans 2 columns on large screens */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              service={service}
              index={index}
            />
          ))}
        </div>

        {/* CTA Button with modern styling */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          viewport={{ once: true }}
        >
          <Link href="/services">
            <div className="inline-block group relative overflow-hidden">
              <div className="absolute inset-0 w-full h-full bg-primary transform -translate-y-full transition-transform duration-300 group-hover:translate-y-0"></div>
              <Button
                variant="default"
                size="lg"
                className="relative z-10 px-10 py-6 text-white bg-primary group-hover:bg-transparent transition-colors duration-300 inline-flex items-center"
              >
                <span className="mr-2">View All Services</span>
                <ArrowRight className="h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          </Link>
        </motion.div>

        {/* Visual connector to next section */}
        <motion.div
          className="w-[1px] h-20 bg-gradient-to-b from-transparent to-black/20 mx-auto mt-20"
          initial={{ scaleY: 0 }}
          whileInView={{ scaleY: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          viewport={{ once: true }}
        />
      </div>
    </section>
  );
};

export default ServicesSection;
