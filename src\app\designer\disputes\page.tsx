'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { DisputesList } from '@/components/disputes/DisputesList';

export default function DesignerDisputesPage() {
  const { user, loading } = useOptimizedAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  if (loading || !user) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Project Disputes</h1>
      </div>

      <DisputesList userRole="designer" />
    </div>
  );
}
