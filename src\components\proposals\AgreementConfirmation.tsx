"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";
import {
  AlertCircle,
  CheckCircle,
  Loader2,
  FileText,
  Lock,
  Info,
} from "lucide-react";
import { motion } from "framer-motion";

type AgreementConfirmationProps = {
  proposalId: string;
  projectId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function AgreementConfirmation({
  proposalId,
  projectId,
  onSuccess,
  onCancel,
}: AgreementConfirmationProps) {
  const { user } = useAuth();
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleConfirm = async () => {
    if (!user) return;

    if (!termsAccepted) {
      setError("You must accept the terms and conditions to proceed");
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      // 1. Update proposal status
      const { error: proposalError } = await supabase
        .from("project_proposals")
        .update({
          status: "client_approved",
          client_approved_at: new Date().toISOString(),
          client_approved_by: user.id,
        })
        .eq("id", proposalId);

      if (proposalError) throw proposalError;

      // 2. Fetch proposal details and milestones
      const { data: proposalData, error: fetchError } = await supabase
        .from("project_proposals")
        .select(`
          designer_id,
          total_budget
        `)
        .eq("id", proposalId)
        .single();

      if (fetchError) throw fetchError;

      const { data: milestones, error: milestonesError } = await supabase
        .from("proposal_milestones")
        .select("*")
        .eq("proposal_id", proposalId)
        .order("order_index", { ascending: true });

      if (milestonesError) throw milestonesError;

      // 3. Create project milestones
      for (const milestone of milestones) {
        const { error: milestoneError } = await supabase
          .from("project_milestones")
          .insert({
            project_id: projectId,
            title: milestone.title,
            description: milestone.description,
            amount: milestone.amount,
            percentage: milestone.percentage,
            status: "pending",
            order_index: milestone.order_index,
            estimated_days: milestone.estimated_days,
          });

        if (milestoneError) throw milestoneError;
      }

      // 4. Update project status to active
      const { error: projectError } = await supabase
        .from("projects")
        .update({
          status: "active",
          designer_id: proposalData.designer_id,
          budget: proposalData.total_budget,
        })
        .eq("id", projectId);

      if (projectError) throw projectError;

      // 5. Create notification for designer
      const { error: notificationError } = await supabase
        .from("notifications")
        .insert({
          user_id: proposalData.designer_id,
          type: "proposal",
          title: "Proposal Approved",
          content: `Your proposal has been approved. You can now start working on the project.`,
          related_id: proposalId,
          read: false,
        });

      if (notificationError) throw notificationError;

      // 6. Create agreement record
      const { error: agreementError } = await supabase
        .from("project_agreements")
        .insert({
          project_id: projectId,
          proposal_id: proposalId,
          client_id: user.id,
          designer_id: proposalData.designer_id,
          agreed_at: new Date().toISOString(),
          agreement_type: "proposal_approval",
          status: "active",
        });

      if (agreementError) throw agreementError;

      setSuccess(true);

      // Call onSuccess callback after a delay
      setTimeout(() => {
        if (onSuccess) onSuccess();
      }, 2000);
    } catch (error) {
      console.error("Error confirming agreement:", error);
      setError("Failed to confirm agreement. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white p-6 rounded-lg border border-gray-200"
      >
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Agreement Confirmed</h3>
          <p className="text-gray-600 mb-4">
            You have successfully approved the proposal and confirmed the project agreement.
            The designer has been notified and can now begin work on your project.
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200">
      <h2 className="text-lg font-semibold mb-4">Confirm Project Agreement</h2>

      {error && (
        <div className="bg-red-50 border border-red-200 p-3 rounded-md mb-4 flex items-start">
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      <div className="mb-6">
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-md mb-4 flex items-start">
          <Info className="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <p className="text-blue-700 text-sm mb-2">
              By approving this proposal, you are entering into a binding agreement with the designer.
            </p>
            <p className="text-blue-700 text-sm">
              This agreement includes the scope of work, timeline, and payment terms as outlined in the proposal.
            </p>
          </div>
        </div>

        <div className="border border-gray-200 p-4 rounded-md mb-4">
          <h3 className="font-medium mb-2 flex items-center">
            <FileText className="h-4 w-4 mr-2" />
            Terms and Conditions
          </h3>
          <div className="text-sm text-gray-700 space-y-2">
            <p>
              1. The client agrees to the scope of work, timeline, and payment terms as outlined in the proposal.
            </p>
            <p>
              2. The designer agrees to complete the work according to the specifications and timeline in the proposal.
            </p>
            <p>
              3. Payment will be made according to the milestone schedule. Funds will be held in escrow until milestone completion.
            </p>
            <p>
              4. Any changes to the scope of work must be agreed upon by both parties and may affect the timeline and cost.
            </p>
            <p>
              5. The client will provide feedback within 5 business days of receiving deliverables.
            </p>
            <p>
              6. The designer retains intellectual property rights until final payment is made.
            </p>
            <p>
              7. Disputes will be resolved through the platform's dispute resolution process.
            </p>
          </div>
        </div>

        <div className="flex items-start mb-4">
          <input
            type="checkbox"
            id="terms"
            checked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            className="mt-1 mr-2"
          />
          <label htmlFor="terms" className="text-sm text-gray-700">
            I have read and agree to the terms and conditions of this proposal. I understand that by clicking "Confirm Agreement", I am entering into a binding contract with the designer.
          </label>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-2">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={submitting}
          >
            Cancel
          </Button>
        )}
        <Button
          type="button"
          onClick={handleConfirm}
          disabled={!termsAccepted || submitting}
          className="bg-brown-600 hover:bg-brown-700 text-white flex items-center"
        >
          {submitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Lock className="h-4 w-4 mr-2" />
              Confirm Agreement
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
