"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Mail, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useRecaptcha } from '@/lib/recaptcha';

interface NewsletterSignupProps {
  className?: string;
  variant?: 'footer' | 'inline' | 'modal';
  showTitle?: boolean;
}

const NewsletterSignup = ({
  className = '',
  variant = 'footer',
  showTitle = true
}: NewsletterSignupProps) => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  const { isLoaded, execute } = useRecaptcha();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !email.includes('@')) {
      setStatus('error');
      setMessage('Please enter a valid email address');
      return;
    }

    if (!isLoaded) {
      setStatus('error');
      setMessage('Security verification is loading. Please try again in a moment.');
      return;
    }

    setStatus('loading');
    setMessage('');

    try {
      // Execute reCAPTCHA
      const recaptchaToken = await execute('newsletter_subscribe');

      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.toLowerCase().trim(),
          source: variant === 'footer' ? 'footer' : 'website',
          recaptchaToken
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        setMessage(data.message || 'Successfully subscribed! Please check your email to confirm.');
        setEmail('');
      } else {
        setStatus('error');
        setMessage(data.error || 'Failed to subscribe. Please try again.');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Network error. Please check your connection and try again.');
    }
  };

  const resetStatus = () => {
    setStatus('idle');
    setMessage('');
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'footer':
        return {
          container: 'text-white',
          title: 'text-white text-xl font-bold mb-4',
          description: 'text-gray-300 text-sm mb-6 leading-relaxed',
          input: 'bg-white/10 border-white/20 text-white placeholder-gray-300 focus:border-primary focus:ring-primary/20 backdrop-blur-sm',
          button: 'bg-primary hover:bg-primary/90 text-white font-medium px-6 py-3'
        };
      case 'inline':
        return {
          container: 'text-gray-900',
          title: 'text-gray-900 text-xl font-bold mb-4',
          description: 'text-gray-600 text-sm mb-6',
          input: 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-primary focus:ring-primary/20',
          button: 'bg-primary hover:bg-primary/90 text-white'
        };
      case 'modal':
        return {
          container: 'text-gray-900',
          title: 'text-gray-900 text-2xl font-bold mb-4 text-center',
          description: 'text-gray-600 mb-6 text-center',
          input: 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-primary focus:ring-primary/20',
          button: 'bg-primary hover:bg-primary/90 text-white w-full'
        };
      default:
        return {
          container: '',
          title: '',
          description: '',
          input: '',
          button: ''
        };
    }
  };

  const styles = getVariantStyles();

  if (status === 'success') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`${styles.container} ${className}`}
      >
        <div className="flex items-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
          <div>
            <p className="text-green-800 font-medium text-sm">Subscription Successful!</p>
            <p className="text-green-700 text-xs mt-1">{message}</p>
          </div>
        </div>
        <button
          onClick={resetStatus}
          className="text-xs text-gray-500 hover:text-gray-700 mt-2 underline"
        >
          Subscribe another email
        </button>
      </motion.div>
    );
  }

  return (
    <div className={`${styles.container} ${className}`}>
      {showTitle && (
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-3">
            <div className={`p-2 rounded-lg ${variant === 'footer' ? 'bg-primary/20' : 'bg-primary/10'}`}>
              <Mail className={`h-5 w-5 ${variant === 'footer' ? 'text-primary' : 'text-primary'}`} />
            </div>
            <h3 className={styles.title}>Stay Updated</h3>
          </div>
          <p className={styles.description}>
            Get the latest architectural insights, project showcases, and industry trends delivered to your inbox.
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex-1">
            <label htmlFor="newsletter-email" className="sr-only">
              Email address
            </label>
            <div className="relative">
              <Mail className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 ${variant === 'footer' ? 'text-gray-300' : 'text-gray-400'}`} />
              <input
                id="newsletter-email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className={`w-full pl-11 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition-all duration-200 ${styles.input}`}
                disabled={status === 'loading'}
                required
              />
            </div>
          </div>
          <Button
            type="submit"
            disabled={status === 'loading' || !email}
            className={`${styles.button} ${variant === 'modal' ? 'mt-2' : ''} rounded-lg transition-all duration-200 hover:scale-105`}
          >
            {status === 'loading' ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Subscribing...
              </>
            ) : (
              'Subscribe'
            )}
          </Button>
        </div>

        {status === 'error' && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center gap-2 text-red-600 text-sm"
          >
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span>{message}</span>
          </motion.div>
        )}
      </form>

      {variant === 'footer' && (
        <div className="mt-4 pt-4 border-t border-white/10">
          <p className="text-xs text-gray-400 leading-relaxed">
            By subscribing, you agree to our{' '}
            <a href="/privacy-policy" className="text-primary hover:text-primary/80 underline transition-colors">
              Privacy Policy
            </a>
            . Unsubscribe at any time.
          </p>
        </div>
      )}
    </div>
  );
};

export default NewsletterSignup;
