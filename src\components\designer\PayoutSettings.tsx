"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { AddPayoutMethodForm } from "./AddPayoutMethodForm";
import {
  CreditCard,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Calendar,
  Settings,
  Building2,
  Globe,
  Loader2,
  Eye,
  EyeOff
} from "lucide-react";

interface PayoutMethod {
  id: string;
  method_type: 'bank_account' | 'paypal' | 'stripe_connect' | 'wise' | 'international_wire';
  is_default: boolean;
  is_verified: boolean;
  verification_status: 'pending' | 'verified' | 'failed' | 'requires_action';
  account_holder_name: string;
  bank_name?: string;
  account_number_encrypted?: string;
  routing_number?: string;
  swift_code?: string;
  iban?: string;
  bank_country?: string;
  bank_currency?: string;
  paypal_email?: string;
  stripe_account_id?: string;
  wise_profile_id?: string;
  minimum_payout_amount: number;
  payout_frequency: 'daily' | 'weekly' | 'bi_weekly' | 'monthly';
  auto_payout_enabled: boolean;
  created_at: string;
  last_used_at?: string;
}

interface PayoutHistory {
  id: string;
  amount: number;
  status: string;
  type: string;
  processed_at: string;
  notes?: string;
  payout_fee: number;
  payout_currency: string;
  external_payout_id?: string;
}

export function PayoutSettings() {
  const { user } = useAuth();
  const [payoutMethods, setPayoutMethods] = useState<PayoutMethod[]>([]);
  const [payoutHistory, setPayoutHistory] = useState<PayoutHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PayoutMethod | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'methods' | 'history' | 'preferences'>('methods');
  const [showAccountNumbers, setShowAccountNumbers] = useState<{[key: string]: boolean}>({});

  useEffect(() => {
    if (user) {
      fetchPayoutMethods();
      fetchPayoutHistory();
    }
  }, [user]);

  const fetchPayoutMethods = async () => {
    try {
      const { data, error } = await supabase
        .from('designer_payout_methods')
        .select('*')
        .eq('designer_id', user?.id)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPayoutMethods(data || []);
    } catch (error) {
      console.error('Error fetching payout methods:', error);
      setError('Failed to load payout methods');
    }
  };

  const fetchPayoutHistory = async () => {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('designer_id', user?.id)
        .eq('type', 'payout')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      setPayoutHistory(data || []);
    } catch (error) {
      console.error('Error fetching payout history:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMethodIcon = (methodType: string) => {
    switch (methodType) {
      case 'bank_account':
        return <Building2 className="h-5 w-5" />;
      case 'paypal':
        return <CreditCard className="h-5 w-5" />;
      case 'stripe_connect':
        return <CreditCard className="h-5 w-5" />;
      case 'wise':
        return <Globe className="h-5 w-5" />;
      case 'international_wire':
        return <Globe className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  const getMethodDisplayName = (methodType: string) => {
    switch (methodType) {
      case 'bank_account':
        return 'Bank Account';
      case 'paypal':
        return 'PayPal';
      case 'stripe_connect':
        return 'Stripe Connect';
      case 'wise':
        return 'Wise (TransferWise)';
      case 'international_wire':
        return 'International Wire';
      default:
        return methodType;
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const toggleAccountNumberVisibility = (methodId: string) => {
    setShowAccountNumbers(prev => ({
      ...prev,
      [methodId]: !prev[methodId]
    }));
  };

  const [decryptedAccountNumbers, setDecryptedAccountNumbers] = useState<Record<string, string>>({});

  const getDisplayAccountNumber = (methodId: string, encryptedAccountNumber?: string, showFull: boolean = false) => {
    if (!encryptedAccountNumber) return '';

    // Check if we already have the decrypted version
    const decryptedNumber = decryptedAccountNumbers[methodId];
    if (decryptedNumber) {
      if (showFull) {
        return decryptedNumber;
      } else {
        return maskAccountNumber(decryptedNumber);
      }
    }

    // If we need to show full number but don't have it decrypted, decrypt it
    if (showFull && !decryptedNumber) {
      decryptAccountNumber(methodId, encryptedAccountNumber);
      return 'Decrypting...';
    }

    // For masked display, we can show a generic mask without decrypting
    return maskAccountNumber(encryptedAccountNumber);
  };

  const maskAccountNumber = (accountNumber?: string) => {
    if (!accountNumber) return '';
    if (accountNumber.length <= 4) return '*'.repeat(accountNumber.length);
    return `****${accountNumber.slice(-4)}`;
  };

  const decryptAccountNumber = async (methodId: string, encryptedAccountNumber: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch('/api/encrypt-payment-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'decrypt',
          data: { account_number: encryptedAccountNumber }
        })
      });

      if (response.ok) {
        const { data } = await response.json();
        setDecryptedAccountNumbers(prev => ({
          ...prev,
          [methodId]: data.account_number || ''
        }));
      }
    } catch (error) {
      console.error('Error decrypting account number:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Payment & Payout Settings</h3>
          <p className="text-sm text-gray-500">
            Manage your payout methods and preferences for receiving payments
          </p>
        </div>
        <Button
          onClick={() => setShowAddForm(true)}
          className="bg-brown-600 hover:bg-brown-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Payout Method
        </Button>
      </div>

      {/* Error/Success Messages */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start"
          >
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
          </motion.div>
        )}
        {success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start"
          >
            <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{success}</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'methods', label: 'Payout Methods', icon: CreditCard },
            { id: 'history', label: 'Payout History', icon: Calendar },
            { id: 'preferences', label: 'Preferences', icon: Settings },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Add/Edit Form Modal */}
      <AnimatePresence>
        {(showAddForm || editingMethod) && (
          <AddPayoutMethodForm
            onClose={() => {
              setShowAddForm(false);
              setEditingMethod(null);
              setError(null);
            }}
            onSuccess={() => {
              fetchPayoutMethods();
              setSuccess(editingMethod ? 'Payout method updated successfully!' : 'Payout method added successfully!');
              setTimeout(() => setSuccess(null), 5000);
            }}
            editingMethod={editingMethod}
          />
        )}
      </AnimatePresence>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'methods' && (
          <div className="space-y-4">
            {payoutMethods.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No payout methods</h3>
                <p className="text-gray-500 mb-4">
                  Add a payout method to start receiving payments
                </p>
                <Button
                  onClick={() => setShowAddForm(true)}
                  className="bg-brown-600 hover:bg-brown-700 text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Payout Method
                </Button>
              </div>
            ) : (
              payoutMethods.map((method) => (
                <motion.div
                  key={method.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white border rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {getMethodIcon(method.method_type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900">
                            {getMethodDisplayName(method.method_type)}
                          </h4>
                          {method.is_default && (
                            <span className="px-2 py-1 text-xs bg-brown-100 text-brown-800 rounded-full">
                              Default
                            </span>
                          )}
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            method.verification_status === 'verified'
                              ? 'bg-green-100 text-green-800'
                              : method.verification_status === 'failed'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {method.verification_status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {method.account_holder_name}
                        </p>
                        {method.bank_name && (
                          <p className="text-sm text-gray-500">
                            {method.bank_name}
                            {method.account_number_encrypted && (
                              <span className="ml-2">
                                {getDisplayAccountNumber(
                                  method.id,
                                  method.account_number_encrypted,
                                  showAccountNumbers[method.id]
                                )}
                                <button
                                  onClick={() => toggleAccountNumberVisibility(method.id)}
                                  className="ml-2 text-brown-600 hover:text-brown-700"
                                >
                                  {showAccountNumbers[method.id] ? (
                                    <EyeOff className="h-3 w-3" />
                                  ) : (
                                    <Eye className="h-3 w-3" />
                                  )}
                                </button>
                              </span>
                            )}
                          </p>
                        )}
                        {method.paypal_email && (
                          <p className="text-sm text-gray-500">{method.paypal_email}</p>
                        )}
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <span>Min: {formatCurrency(method.minimum_payout_amount)}</span>
                          <span>Frequency: {method.payout_frequency}</span>
                          <span>Auto: {method.auto_payout_enabled ? 'Yes' : 'No'}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingMethod(method)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-4">
            {payoutHistory.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No payout history</h3>
                <p className="text-gray-500">
                  Your payout history will appear here once you start receiving payments
                </p>
              </div>
            ) : (
              <div className="bg-white border rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h4 className="font-medium text-gray-900">Recent Payouts</h4>
                </div>
                <div className="divide-y divide-gray-200">
                  {payoutHistory.map((payout) => (
                    <div key={payout.id} className="px-6 py-4 flex items-center justify-between">
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">
                            {formatCurrency(payout.amount, payout.payout_currency)}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            payout.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : payout.status === 'failed'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {payout.status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                          {formatDate(payout.processed_at)}
                          {payout.notes && ` • ${payout.notes}`}
                        </p>
                        {payout.payout_fee > 0 && (
                          <p className="text-xs text-gray-400">
                            Fee: {formatCurrency(payout.payout_fee, payout.payout_currency)}
                          </p>
                        )}
                      </div>
                      {payout.external_payout_id && (
                        <div className="text-right">
                          <p className="text-xs text-gray-500">
                            ID: {payout.external_payout_id}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'preferences' && (
          <div className="bg-white border rounded-lg p-6">
            <h4 className="font-medium text-gray-900 mb-4">Global Payout Preferences</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Minimum Payout Amount
                </label>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5 text-gray-400" />
                  <input
                    type="number"
                    min="50"
                    step="10"
                    defaultValue="100"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Minimum amount before automatic payouts are triggered
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Payout Frequency
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500">
                  <option value="weekly">Weekly</option>
                  <option value="bi_weekly">Bi-weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="auto_payout"
                  defaultChecked
                  className="h-4 w-4 text-brown-600 focus:ring-brown-500 border-gray-300 rounded"
                />
                <label htmlFor="auto_payout" className="ml-2 block text-sm text-gray-900">
                  Enable automatic payouts
                </label>
              </div>

              <div className="pt-4">
                <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                  Save Preferences
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
