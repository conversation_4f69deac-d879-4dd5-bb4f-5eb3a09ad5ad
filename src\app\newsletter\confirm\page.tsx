"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Layout from '@/components/Layout';
import { motion } from 'framer-motion';
import { CheckCircle, AlertCircle, Loader2, Mail, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function NewsletterConfirmPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const searchParams = useSearchParams();

  useEffect(() => {
    const token = searchParams.get('token');
    
    if (!token) {
      setStatus('error');
      setMessage('Invalid confirmation link. Please check your email for the correct link.');
      return;
    }

    confirmSubscription(token);
  }, [searchParams]);

  const confirmSubscription = async (token: string) => {
    try {
      const response = await fetch('/api/newsletter/confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        setMessage(data.message);
        setEmail(data.email);
      } else {
        setStatus('error');
        setMessage(data.error || 'Failed to confirm subscription');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Network error. Please try again later.');
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-16">
        <div className="container mx-auto px-4 max-w-2xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white rounded-lg shadow-lg p-8 md:p-12 text-center"
          >
            {status === 'loading' && (
              <>
                <Loader2 className="h-16 w-16 text-primary mx-auto mb-6 animate-spin" />
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                  Confirming Your Subscription
                </h1>
                <p className="text-gray-600">
                  Please wait while we confirm your newsletter subscription...
                </p>
              </>
            )}

            {status === 'success' && (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                >
                  <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-6" />
                </motion.div>
                
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                  Subscription Confirmed!
                </h1>
                
                <p className="text-gray-600 mb-6">
                  {message}
                </p>

                {email && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center justify-center gap-2 text-green-800">
                      <Mail className="h-5 w-5" />
                      <span className="font-medium">{email}</span>
                    </div>
                    <p className="text-green-700 text-sm mt-2">
                      You'll receive our latest architectural insights and project showcases.
                    </p>
                  </div>
                )}

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">What's Next?</h3>
                  <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">📧 Weekly Insights</h4>
                      <p>Get the latest architectural trends and design inspiration.</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">🏗️ Project Showcases</h4>
                      <p>Exclusive behind-the-scenes looks at our latest projects.</p>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                  <Link href="/">
                    <Button className="flex items-center gap-2">
                      <Home className="h-4 w-4" />
                      Back to Home
                    </Button>
                  </Link>
                  <Link href="/services">
                    <Button variant="outline">
                      Explore Our Services
                    </Button>
                  </Link>
                </div>
              </>
            )}

            {status === 'error' && (
              <>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                >
                  <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-6" />
                </motion.div>
                
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                  Confirmation Failed
                </h1>
                
                <p className="text-gray-600 mb-6">
                  {message}
                </p>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                  <h4 className="font-medium text-red-900 mb-2">Need Help?</h4>
                  <p className="text-red-700 text-sm">
                    If you continue to have issues, please contact us at{' '}
                    <a href="mailto:<EMAIL>" className="underline">
                      <EMAIL>
                    </a>
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/">
                    <Button className="flex items-center gap-2">
                      <Home className="h-4 w-4" />
                      Back to Home
                    </Button>
                  </Link>
                  <Link href="/contact">
                    <Button variant="outline">
                      Contact Support
                    </Button>
                  </Link>
                </div>
              </>
            )}
          </motion.div>
        </div>
      </div>
    </Layout>
  );
}
