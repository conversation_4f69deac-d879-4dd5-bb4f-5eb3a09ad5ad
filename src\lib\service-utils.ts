/**
 * Utility functions for handling service IDs consistently across the application
 */

/**
 * Converts a service title to a URL-safe service ID
 * @param serviceTitle The original service title (e.g., "Creative Design & Branding")
 * @returns A URL-safe service ID (e.g., "creative-design-%26-branding")
 */
export function toUrlSafeServiceId(serviceTitle: string): string {
  // Convert to lowercase, replace spaces with hyphens, and encode special characters
  return encodeURIComponent(serviceTitle.toLowerCase().replace(/\s+/g, '-'));
}

/**
 * Converts a URL-safe service ID back to a normalized service ID for object lookups
 * @param urlSafeServiceId The URL-safe service ID (e.g., "creative-design-%26-branding")
 * @returns A normalized service ID for object lookups (e.g., "creative design & branding")
 */
export function fromUrlSafeServiceId(urlSafeServiceId: string): string {
  // Decode the URL-safe service ID and convert hyphens back to spaces
  const decoded = decodeURIComponent(urlSafeServiceId);

  // Try to convert back to a more natural format (replace hyphens with spaces)
  // This helps with matching to service names in our objects
  return decoded.replace(/-/g, ' ');
}

/**
 * Maps a service ID (which might be URL-encoded) to the correct key in a service object
 * @param serviceId The service ID from the URL
 * @param availableKeys Array of available keys in the service object
 * @returns The matching key or the original serviceId if no match is found
 */
export function mapServiceIdToKey(serviceId: string, availableKeys: string[]): string {
  // First try direct match (already decoded)
  if (availableKeys.includes(serviceId)) {
    return serviceId;
  }

  // Try decoded version
  const decodedServiceId = fromUrlSafeServiceId(serviceId);
  if (availableKeys.includes(decodedServiceId)) {
    return decodedServiceId;
  }

  // Try case-insensitive match
  const lowerCaseServiceId = decodedServiceId.toLowerCase();
  const matchingKey = availableKeys.find(key => key.toLowerCase() === lowerCaseServiceId);
  if (matchingKey) {
    return matchingKey;
  }

  // Try partial match (for hyphenated URL parameters)
  const partialMatch = availableKeys.find(key => {
    // Convert spaces to hyphens for comparison
    const keyAsParam = key.toLowerCase().replace(/\s+/g, '-');
    const serviceIdAsParam = serviceId.toLowerCase().replace(/\s+/g, '-');
    return keyAsParam === serviceIdAsParam ||
           serviceIdAsParam.includes(keyAsParam) ||
           keyAsParam.includes(serviceIdAsParam);
  });

  if (partialMatch) {
    return partialMatch;
  }

  // If all else fails, return the decoded service ID
  return decodedServiceId;
}
