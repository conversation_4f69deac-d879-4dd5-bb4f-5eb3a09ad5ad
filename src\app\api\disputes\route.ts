import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/disputes
 * Gets all disputes for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    let query = supabase
      .from('disputes')
      .select(`
        id,
        project_id,
        client_id,
        designer_id,
        created_by,
        status,
        title,
        description,
        resolution_notes,
        resolved_by,
        resolved_at,
        created_at,
        updated_at,
        projects:project_id (
          title
        ),
        client:client_id (
          full_name,
          avatar_url
        ),
        designer:designer_id (
          full_name,
          avatar_url
        ),
        creator:created_by (
          full_name,
          role
        ),
        resolver:resolved_by (
          full_name,
          role
        )
      `);
    
    // If admin, get all disputes
    if (profile.role !== 'admin') {
      // If not admin, only get disputes where the user is the client or designer
      query = query.or(`client_id.eq.${user.id},designer_id.eq.${user.id}`);
    }
    
    // Add any filters from query parameters
    const status = request.nextUrl.searchParams.get('status');
    if (status) {
      query = query.eq('status', status);
    }
    
    const projectId = request.nextUrl.searchParams.get('project_id');
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    
    // Order by created_at descending (newest first)
    query = query.order('created_at', { ascending: false });
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching disputes:', error);
      return NextResponse.json(
        { error: 'Failed to fetch disputes' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/disputes:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/disputes
 * Creates a new dispute
 * 
 * Request body:
 * {
 *   projectId: string;
 *   title: string;
 *   description: string;
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { projectId, title, description } = await request.json();
    
    // Validate required fields
    if (!projectId || !title || !description) {
      return NextResponse.json(
        { error: 'Project ID, title, and description are required' },
        { status: 400 }
      );
    }
    
    // Get the project to verify the user is the client or designer
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('client_id, designer_id')
      .eq('id', projectId)
      .single();
    
    if (projectError) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }
    
    // Check if the user is the client or designer of the project
    if (project.client_id !== user.id && project.designer_id !== user.id) {
      return NextResponse.json(
        { error: 'You do not have permission to create a dispute for this project' },
        { status: 403 }
      );
    }
    
    // Create the dispute
    const { data, error } = await supabase
      .from('disputes')
      .insert({
        project_id: projectId,
        client_id: project.client_id,
        designer_id: project.designer_id,
        created_by: user.id,
        title,
        description,
        status: 'open'
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating dispute:', error);
      return NextResponse.json(
        { error: 'Failed to create dispute' },
        { status: 500 }
      );
    }
    
    // Create notifications for the other party and admins
    const otherPartyId = user.id === project.client_id ? project.designer_id : project.client_id;
    
    // Notification for the other party
    await supabase
      .from('notifications')
      .insert({
        user_id: otherPartyId,
        type: 'dispute',
        title: 'New Dispute Created',
        content: `A dispute has been created for a project you're involved in: ${title}`,
        related_id: data.id,
        read: false
      });
    
    // Notification for admins
    const { data: admins } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'admin');
    
    if (admins && admins.length > 0) {
      const adminNotifications = admins.map(admin => ({
        user_id: admin.id,
        type: 'dispute',
        title: 'New Dispute Created',
        content: `A new dispute has been created: ${title}`,
        related_id: data.id,
        read: false
      }));
      
      await supabase
        .from('notifications')
        .insert(adminNotifications);
    }
    
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/disputes:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
