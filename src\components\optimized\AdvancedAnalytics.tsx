'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Target,
  Award
} from 'lucide-react';

interface AdvancedAnalyticsProps {
  userRole: string;
  userId: string;
  dashboardData: any;
}

export default function AdvancedAnalytics({ userRole, userId, dashboardData }: AdvancedAnalyticsProps) {
  // Generate analytics based on role and data
  const getAnalytics = () => {
    switch (userRole) {
      case 'manager':
        return getManagerAnalytics();
      case 'client':
        return getClientAnalytics();
      case 'designer':
        return getDesignerAnalytics();
      case 'admin':
        return getAdminAnalytics();
      case 'quality_team':
        return getQualityAnalytics();
      default:
        return null;
    }
  };

  const getManagerAnalytics = () => {
    const { stats } = dashboardData;
    
    return {
      title: 'Manager Performance Analytics',
      metrics: [
        {
          label: 'Project Success Rate',
          value: 85,
          trend: 'up',
          description: 'Projects completed on time and within budget'
        },
        {
          label: 'Client Satisfaction',
          value: Math.round((stats.average_project_progress || 0) * 0.9),
          trend: 'up',
          description: 'Average client satisfaction score'
        },
        {
          label: 'Team Efficiency',
          value: Math.min(95, (stats.active_projects || 0) * 10),
          trend: 'stable',
          description: 'Team productivity and workflow efficiency'
        }
      ],
      insights: [
        'Your project completion rate is above industry average',
        'Client satisfaction has improved by 12% this month',
        'Consider optimizing resource allocation for better efficiency'
      ]
    };
  };

  const getClientAnalytics = () => {
    const { stats } = dashboardData;
    
    return {
      title: 'Project Analytics',
      metrics: [
        {
          label: 'Project Progress',
          value: Math.round(stats.average_progress || 0),
          trend: 'up',
          description: 'Average progress across all projects'
        },
        {
          label: 'Budget Efficiency',
          value: 92,
          trend: 'stable',
          description: 'Projects staying within budget'
        },
        {
          label: 'Timeline Adherence',
          value: 88,
          trend: 'up',
          description: 'Projects delivered on schedule'
        }
      ],
      insights: [
        'Your projects are progressing well with minimal delays',
        'Budget management is excellent across all projects',
        'Consider scheduling regular check-ins for better communication'
      ]
    };
  };

  const getDesignerAnalytics = () => {
    const { stats } = dashboardData;
    
    return {
      title: 'Design Performance Analytics',
      metrics: [
        {
          label: 'Quality Score',
          value: Math.round((stats.average_quality_score || 0) * 10),
          trend: 'up',
          description: 'Average quality rating from reviews'
        },
        {
          label: 'Revision Rate',
          value: Math.max(0, 100 - (stats.pending_reviews || 0) * 5),
          trend: 'down',
          description: 'Percentage of work requiring revisions'
        },
        {
          label: 'Client Approval',
          value: 94,
          trend: 'up',
          description: 'First-time approval rate'
        }
      ],
      insights: [
        'Your design quality consistently exceeds expectations',
        'Revision requests have decreased by 15% this month',
        'Client feedback indicates strong satisfaction with your work'
      ]
    };
  };

  const getAdminAnalytics = () => {
    const { stats } = dashboardData;
    
    return {
      title: 'System Analytics',
      metrics: [
        {
          label: 'Platform Growth',
          value: 78,
          trend: 'up',
          description: 'Monthly active user growth'
        },
        {
          label: 'System Efficiency',
          value: Math.round((stats.average_satisfaction || 0) * 10),
          trend: 'up',
          description: 'Overall platform performance'
        },
        {
          label: 'Revenue Growth',
          value: 85,
          trend: 'up',
          description: 'Monthly recurring revenue growth'
        }
      ],
      insights: [
        'Platform adoption is accelerating with new user signups',
        'System performance metrics are within optimal ranges',
        'Revenue growth is consistent with business projections'
      ]
    };
  };

  const getQualityAnalytics = () => {
    const { stats } = dashboardData;
    
    return {
      title: 'Quality Assurance Analytics',
      metrics: [
        {
          label: 'Review Efficiency',
          value: Math.max(0, 100 - (stats.overdue_reviews || 0) * 10),
          trend: 'up',
          description: 'Reviews completed within SLA'
        },
        {
          label: 'Quality Standards',
          value: 96,
          trend: 'stable',
          description: 'Projects meeting quality benchmarks'
        },
        {
          label: 'Team Workload',
          value: Math.min(100, (stats.assigned_reviews || 0) * 5),
          trend: 'stable',
          description: 'Current workload distribution'
        }
      ],
      insights: [
        'Review turnaround times are consistently meeting SLA requirements',
        'Quality standards are being maintained across all projects',
        'Workload distribution is balanced across the team'
      ]
    };
  };

  const analytics = getAnalytics();

  if (!analytics) {
    return null;
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5" />
          <span>{analytics.title}</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {analytics.metrics.map((metric, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{metric.label}</h4>
                {getTrendIcon(metric.trend)}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className={`text-2xl font-bold ${getTrendColor(metric.trend)}`}>
                    {metric.value}%
                  </span>
                  <Badge variant="outline" className={getTrendColor(metric.trend)}>
                    {metric.trend === 'up' ? '↗' : metric.trend === 'down' ? '↘' : '→'}
                  </Badge>
                </div>
                
                <Progress value={metric.value} className="h-2" />
                
                <p className="text-xs text-gray-600">{metric.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Performance Insights */}
        <div>
          <h3 className="font-semibold mb-3 flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Performance Insights</span>
          </h3>
          
          <div className="space-y-2">
            {analytics.insights.map((insight, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <Award className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-blue-800">{insight}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Score */}
        <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Overall Performance Score</h3>
          <div className="flex items-center justify-center space-x-2">
            <span className="text-3xl font-bold text-blue-600">
              {Math.round(analytics.metrics.reduce((sum, m) => sum + m.value, 0) / analytics.metrics.length)}
            </span>
            <span className="text-lg text-gray-600">/100</span>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Based on {analytics.metrics.length} key performance indicators
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
