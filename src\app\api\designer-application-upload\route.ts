import { NextRequest, NextResponse } from 'next/server';
import { uploadDesignerResume, uploadDesignerPortfolioFile, uploadDesignerCertificate } from '@/lib/r2-upload';

/**
 * API route for uploading designer application files to Cloudflare R2
 * Supports multipart/form-data for file uploads
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Designer application upload API called');
    
    const contentType = request.headers.get('content-type') || '';
    console.log('Content type:', contentType);

    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { error: 'Content type must be multipart/form-data' },
        { status: 400 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const fileType = formData.get('fileType') as string; // 'resume', 'portfolio', or 'certificate'
    const applicationId = formData.get('applicationId') as string;
    const fileIndex = formData.get('fileIndex') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!fileType || !['resume', 'portfolio', 'certificate'].includes(fileType)) {
      return NextResponse.json(
        { error: 'Invalid file type. Must be "resume", "portfolio", or "certificate"' },
        { status: 400 }
      );
    }

    if (!applicationId) {
      return NextResponse.json(
        { error: 'Application ID is required' },
        { status: 400 }
      );
    }

    // Validate file size
    const maxSize = fileType === 'portfolio' ? 50 * 1024 * 1024 : 10 * 1024 * 1024; // 50MB for portfolio, 10MB for resume/certificate
    if (file.size > maxSize) {
      const maxSizeMB = fileType === 'portfolio' ? '50MB' : '10MB';
      return NextResponse.json(
        { error: `File size exceeds ${maxSizeMB} limit` },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = {
      resume: ['application/pdf'],
      portfolio: [
        'application/pdf',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
      ],
      certificate: [
        'application/pdf',
        'image/jpeg',
        'image/jpg',
        'image/png'
      ]
    };

    if (!allowedTypes[fileType as keyof typeof allowedTypes].includes(file.type)) {
      return NextResponse.json(
        { error: `Invalid file type for ${fileType}. ${fileType === 'resume' ? 'Only PDF files are allowed.' : 'Only PDF and image files are allowed.'}` },
        { status: 400 }
      );
    }

    let fileKey: string;

    try {
      console.log(`Attempting to upload ${fileType} file to R2...`);
      
      if (fileType === 'resume') {
        fileKey = await uploadDesignerResume(file, file.name, applicationId);
      } else if (fileType === 'portfolio') {
        const index = fileIndex ? parseInt(fileIndex) : 0;
        fileKey = await uploadDesignerPortfolioFile(file, file.name, applicationId, index);
      } else if (fileType === 'certificate') {
        const index = fileIndex ? parseInt(fileIndex) : 0;
        fileKey = await uploadDesignerCertificate(file, file.name, applicationId, index);
      }
      
      console.log(`${fileType} file uploaded successfully to R2:`, fileKey);
    } catch (uploadError) {
      console.error(`Error uploading ${fileType} file to R2:`, uploadError);
      return NextResponse.json(
        { error: `Failed to upload ${fileType} file` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      fileKey,
      fileType,
      fileName: file.name,
      fileSize: file.size
    }, { status: 200 });

  } catch (error) {
    console.error('Error in designer application upload API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
