"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Star,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Calendar,
  Users,
  Target,
  Award
} from "lucide-react";

interface QualityAnalytics {
  totalReviews: number;
  averageScore: number;
  approvalRate: number;
  averageReviewTime: number;
  revisionRate: number;
  categoryBreakdown: Record<string, number>;
  monthlyTrends: Array<{
    month: string;
    reviews: number;
    averageScore: number;
    approvalRate: number;
  }>;
  designerPerformance: Array<{
    designer_name: string;
    total_reviews: number;
    average_score: number;
    approval_rate: number;
  }>;
}

export default function QualityAnalyticsPage() {
  const { user, profile } = useOptimizedAuth();
  const [analytics, setAnalytics] = useState<QualityAnalytics>({
    totalReviews: 0,
    averageScore: 0,
    approvalRate: 0,
    averageReviewTime: 0,
    revisionRate: 0,
    categoryBreakdown: {},
    monthlyTrends: [],
    designerPerformance: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<string>('30');

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchAnalytics();
    }
  }, [user, profile, timeRange]);

  const fetchAnalytics = async () => {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(timeRange));

      // Fetch quality reviews data
      const { data: reviews, error } = await supabase
        .from('quality_reviews')
        .select(`
          *,
          project:projects(title),
          designer:profiles!quality_reviews_designer_id_fkey(full_name)
        `)
        .eq('reviewer_id', user?.id)
        .gte('created_at', startDate.toISOString());

      if (error) throw error;

      // Calculate analytics
      const totalReviews = reviews?.length || 0;
      const completedReviews = reviews?.filter(r => r.overall_score) || [];
      const averageScore = completedReviews.length > 0 
        ? completedReviews.reduce((sum, r) => sum + (r.overall_score || 0), 0) / completedReviews.length
        : 0;

      const approvedReviews = reviews?.filter(r => r.status === 'approved') || [];
      const approvalRate = totalReviews > 0 ? (approvedReviews.length / totalReviews) * 100 : 0;

      const reviewsWithRevisions = reviews?.filter(r => r.revision_count > 0) || [];
      const revisionRate = totalReviews > 0 ? (reviewsWithRevisions.length / totalReviews) * 100 : 0;

      // Calculate average review time (mock data for now)
      const averageReviewTime = 18; // hours

      // Category breakdown (mock data)
      const categoryBreakdown = {
        'Design': 45,
        'Technical': 25,
        'Content': 15,
        'UX': 10,
        'Deliverable': 5
      };

      // Monthly trends (mock data)
      const monthlyTrends = [
        { month: 'Jan', reviews: 12, averageScore: 4.2, approvalRate: 85 },
        { month: 'Feb', reviews: 15, averageScore: 4.3, approvalRate: 87 },
        { month: 'Mar', reviews: 18, averageScore: 4.1, approvalRate: 82 },
        { month: 'Apr', reviews: 22, averageScore: 4.4, approvalRate: 89 },
        { month: 'May', reviews: 20, averageScore: 4.5, approvalRate: 91 },
        { month: 'Jun', reviews: 25, averageScore: 4.3, approvalRate: 88 }
      ];

      // Designer performance (mock data)
      const designerPerformance = [
        { designer_name: 'John Smith', total_reviews: 15, average_score: 4.5, approval_rate: 93 },
        { designer_name: 'Sarah Johnson', total_reviews: 12, average_score: 4.2, approval_rate: 87 },
        { designer_name: 'Mike Davis', total_reviews: 18, average_score: 4.1, approval_rate: 82 },
        { designer_name: 'Emily Brown', total_reviews: 10, average_score: 4.6, approval_rate: 95 }
      ];

      setAnalytics({
        totalReviews,
        averageScore,
        approvalRate,
        averageReviewTime,
        revisionRate,
        categoryBreakdown,
        monthlyTrends,
        designerPerformance
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quality Analytics</h1>
          <p className="text-gray-600 mt-2">Performance insights and quality metrics</p>
        </div>
        <div className="flex gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
            <option value="365">Last year</option>
          </select>
          <Button
            onClick={fetchAnalytics}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Reviews</p>
              <p className="text-2xl font-bold text-blue-600">{analytics.totalReviews}</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-2xl font-bold text-green-600">{analytics.averageScore.toFixed(1)}/5</p>
            </div>
            <Star className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approval Rate</p>
              <p className="text-2xl font-bold text-emerald-600">{analytics.approvalRate.toFixed(1)}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-emerald-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Review Time</p>
              <p className="text-2xl font-bold text-purple-600">{analytics.averageReviewTime}h</p>
            </div>
            <Clock className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Revision Rate</p>
              <p className="text-2xl font-bold text-orange-600">{analytics.revisionRate.toFixed(1)}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Category Breakdown */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Categories</h3>
          <div className="space-y-4">
            {Object.entries(analytics.categoryBreakdown).map(([category, percentage]) => (
              <div key={category} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{category}</span>
                <div className="flex items-center gap-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-brown-600 h-2 rounded-full" 
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-10">{percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Monthly Trends */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Trends</h3>
          <div className="space-y-4">
            {analytics.monthlyTrends.map((month) => (
              <div key={month.month} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-900">{month.month}</span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <span className="text-blue-600">{month.reviews} reviews</span>
                  <span className="text-green-600">{month.averageScore.toFixed(1)} avg</span>
                  <span className="text-purple-600">{month.approvalRate}% approved</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Designer Performance */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Designer Performance</h2>
          <p className="text-gray-600 mt-1">Quality metrics by designer</p>
        </div>

        <div className="divide-y divide-gray-200">
          {analytics.designerPerformance.map((designer, index) => (
            <div key={index} className="p-6 hover:bg-gray-50 transition-colors duration-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-brown-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {designer.designer_name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{designer.designer_name}</h3>
                    <p className="text-sm text-gray-600">{designer.total_reviews} reviews completed</p>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Average Score</p>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="font-semibold text-gray-900">{designer.average_score.toFixed(1)}</span>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Approval Rate</p>
                    <div className="flex items-center gap-1">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="font-semibold text-gray-900">{designer.approval_rate}%</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <p className="text-sm text-gray-600">Performance</p>
                    <div className="flex items-center gap-1">
                      {designer.approval_rate >= 90 ? (
                        <Award className="h-4 w-4 text-green-500" />
                      ) : designer.approval_rate >= 80 ? (
                        <TrendingUp className="h-4 w-4 text-blue-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-orange-500" />
                      )}
                      <span className="font-semibold text-gray-900">
                        {designer.approval_rate >= 90 ? 'Excellent' : 
                         designer.approval_rate >= 80 ? 'Good' : 'Needs Improvement'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Insights */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <Target className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Quality Insights</h3>
            <div className="text-blue-800 space-y-2">
              <p>• <strong>Performance:</strong> Your approval rate is {analytics.approvalRate.toFixed(1)}% - {analytics.approvalRate >= 85 ? 'excellent work!' : 'room for improvement'}</p>
              <p>• <strong>Efficiency:</strong> Average review time of {analytics.averageReviewTime} hours is {analytics.averageReviewTime <= 20 ? 'within target' : 'above target'}</p>
              <p>• <strong>Quality:</strong> Average score of {analytics.averageScore.toFixed(1)}/5 indicates {analytics.averageScore >= 4.0 ? 'high quality standards' : 'opportunity for stricter standards'}</p>
              <p>• <strong>Revisions:</strong> {analytics.revisionRate.toFixed(1)}% revision rate suggests {analytics.revisionRate <= 30 ? 'good initial quality' : 'need for clearer guidelines'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
