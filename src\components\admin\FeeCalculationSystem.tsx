"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  Calculator,
  DollarSign,
  Percent,
  TrendingUp,
  Settings,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface FeeSettings {
  id: string;
  platform_commission_rate: number;
  payment_processing_fee: number;
  designer_payout_rate: number;
  minimum_project_value: number;
  maximum_commission_cap: number | null;
  updated_at: string;
  updated_by: string;
}

interface ProjectCalculation {
  project_value: number;
  platform_commission: number;
  payment_processing_fee: number;
  designer_payout: number;
  net_platform_revenue: number;
}

export function FeeCalculationSystem() {
  const { user } = useOptimizedAuth();
  const [feeSettings, setFeeSettings] = useState<FeeSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [calculationExample, setCalculationExample] = useState<ProjectCalculation | null>(null);
  const [exampleProjectValue, setExampleProjectValue] = useState(1000);

  // Form state - FIXED MATH: 15% + 2.9% + 82.1% = 100%
  const [formData, setFormData] = useState({
    platform_commission_rate: 15,
    payment_processing_fee: 2.9,
    designer_payout_rate: 82.1, // FIXED: Was 85, now 82.1 (100 - 15 - 2.9)
    minimum_project_value: 100,
    maximum_commission_cap: null as number | null
  });

  useEffect(() => {
    fetchFeeSettings();
  }, []);

  useEffect(() => {
    calculateExample();
  }, [formData, exampleProjectValue]);

  const fetchFeeSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('fee_settings')
        .select('*')
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116' && !error.message?.includes('does not exist')) {
        throw error;
      }

      if (data) {
        setFeeSettings(data);
        setFormData({
          platform_commission_rate: data.platform_commission_rate,
          payment_processing_fee: data.payment_processing_fee,
          designer_payout_rate: data.designer_payout_rate,
          minimum_project_value: data.minimum_project_value,
          maximum_commission_cap: data.maximum_commission_cap
        });
      } else {
        // Create default settings if none exist or table doesn't exist
        if (error?.message?.includes('does not exist')) {
          console.warn('Fee settings table not found, using default values');
          setLoading(false);
          return;
        }
        await createDefaultSettings();
      }
    } catch (error: any) {
      console.error('Error fetching fee settings:', error);
      if (error.message?.includes('does not exist')) {
        console.warn('Fee settings table not found, using default values');
      }
    } finally {
      setLoading(false);
    }
  };

  const createDefaultSettings = async () => {
    try {
      const defaultSettings = {
        platform_commission_rate: 15,
        payment_processing_fee: 2.9,
        designer_payout_rate: 82.1, // FIXED: Correct calculation
        minimum_project_value: 100,
        maximum_commission_cap: null,
        updated_by: user?.id
      };

      const { data, error } = await supabase
        .from('fee_settings')
        .insert(defaultSettings)
        .select()
        .single();

      if (error) throw error;
      setFeeSettings(data);
    } catch (error) {
      console.error('Error creating default settings:', error);
    }
  };

  const calculateExample = () => {
    const projectValue = exampleProjectValue;
    const commissionRate = formData.platform_commission_rate / 100;
    const processingFeeRate = formData.payment_processing_fee / 100;

    let platformCommission = projectValue * commissionRate;

    // Apply commission cap if set
    if (formData.maximum_commission_cap && platformCommission > formData.maximum_commission_cap) {
      platformCommission = formData.maximum_commission_cap;
    }

    const paymentProcessingFee = projectValue * processingFeeRate;
    const designerPayout = projectValue - platformCommission;
    const netPlatformRevenue = platformCommission - paymentProcessingFee;

    setCalculationExample({
      project_value: projectValue,
      platform_commission: platformCommission,
      payment_processing_fee: paymentProcessingFee,
      designer_payout: designerPayout,
      net_platform_revenue: netPlatformRevenue
    });
  };

  const handleSaveSettings = async () => {
    setSaving(true);

    try {
      const updateData = {
        ...formData,
        updated_at: new Date().toISOString(),
        updated_by: user?.id
      };

      if (feeSettings) {
        // Update existing settings
        const { data, error } = await supabase
          .from('fee_settings')
          .update(updateData)
          .eq('id', feeSettings.id)
          .select()
          .single();

        if (error) throw error;
        setFeeSettings(data);
      } else {
        // Create new settings
        const { data, error } = await supabase
          .from('fee_settings')
          .insert(updateData)
          .select()
          .single();

        if (error) throw error;
        setFeeSettings(data);
      }

      console.log('Fee settings saved successfully');
    } catch (error) {
      console.error('Error saving fee settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: number | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-calculate designer payout rate when commission rate changes
    if (field === 'platform_commission_rate' && value !== null) {
      setFormData(prev => ({
        ...prev,
        designer_payout_rate: 100 - value
      }));
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  const formatPercentage = (rate: number) => {
    return `${rate.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Calculator className="h-6 w-6 text-brown-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Fee Calculation System</h3>
              <p className="text-sm text-gray-600">Configure platform commission rates and fee structures</p>
            </div>
          </div>
          <Button
            onClick={handleSaveSettings}
            disabled={saving}
            className="bg-brown-600 hover:bg-brown-700 text-white"
          >
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Settings
          </Button>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Fee Settings Form */}
          <div className="space-y-6">
            <h4 className="text-lg font-medium text-gray-900">Fee Configuration</h4>

            <div className="space-y-4">
              {/* Platform Commission Rate */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Platform Commission Rate (%)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    min="0"
                    max="50"
                    step="0.1"
                    value={formData.platform_commission_rate}
                    onChange={(e) => handleInputChange('platform_commission_rate', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                  <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Percentage of project value taken as platform commission</p>
              </div>

              {/* Payment Processing Fee */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Processing Fee (%)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                    value={formData.payment_processing_fee}
                    onChange={(e) => handleInputChange('payment_processing_fee', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                  <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Payment gateway fees (Stripe, PayPal, etc.)</p>
              </div>

              {/* Designer Payout Rate */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Designer Payout Rate (%)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={formData.designer_payout_rate}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
                  />
                  <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Automatically calculated (100% - Commission Rate)</p>
              </div>

              {/* Minimum Project Value */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Project Value ($)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    min="0"
                    step="1"
                    value={formData.minimum_project_value}
                    onChange={(e) => handleInputChange('minimum_project_value', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                  <DollarSign className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Minimum value required for projects on the platform</p>
              </div>

              {/* Maximum Commission Cap */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Commission Cap ($)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    min="0"
                    step="1"
                    value={formData.maximum_commission_cap || ''}
                    onChange={(e) => handleInputChange('maximum_commission_cap', e.target.value ? parseFloat(e.target.value) : null)}
                    placeholder="No cap"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                  <DollarSign className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                </div>
                <p className="text-xs text-gray-500 mt-1">Optional maximum commission amount for high-value projects</p>
              </div>
            </div>
          </div>

          {/* Calculation Example */}
          <div className="space-y-6">
            <h4 className="text-lg font-medium text-gray-900">Fee Calculation Example</h4>

            {/* Example Project Value Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Example Project Value ($)
              </label>
              <div className="relative">
                <input
                  type="number"
                  min="0"
                  step="100"
                  value={exampleProjectValue}
                  onChange={(e) => setExampleProjectValue(parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                />
                <DollarSign className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              </div>
            </div>

            {/* Calculation Breakdown */}
            {calculationExample && (
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h5 className="font-medium text-gray-900">Breakdown</h5>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Project Value:</span>
                    <span className="font-medium">{formatCurrency(calculationExample.project_value)}</span>
                  </div>

                  <div className="flex justify-between text-red-600">
                    <span>Platform Commission ({formatPercentage(formData.platform_commission_rate)}):</span>
                    <span className="font-medium">-{formatCurrency(calculationExample.platform_commission)}</span>
                  </div>

                  <div className="flex justify-between text-red-600">
                    <span>Payment Processing ({formatPercentage(formData.payment_processing_fee)}):</span>
                    <span className="font-medium">-{formatCurrency(calculationExample.payment_processing_fee)}</span>
                  </div>

                  <hr className="border-gray-300" />

                  <div className="flex justify-between text-green-600">
                    <span className="font-medium">Designer Payout:</span>
                    <span className="font-bold">{formatCurrency(calculationExample.designer_payout)}</span>
                  </div>

                  <div className="flex justify-between text-brown-600">
                    <span className="font-medium">Net Platform Revenue:</span>
                    <span className="font-bold">{formatCurrency(calculationExample.net_platform_revenue)}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Fee Structure Summary */}
            <div className="bg-brown-50 rounded-lg p-4">
              <h5 className="font-medium text-brown-900 mb-3 flex items-center">
                <Info className="h-4 w-4 mr-2" />
                Current Fee Structure
              </h5>

              <div className="space-y-2 text-sm text-brown-800">
                <div className="flex justify-between">
                  <span>Platform Commission:</span>
                  <span className="font-medium">{formatPercentage(formData.platform_commission_rate)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Designer Keeps:</span>
                  <span className="font-medium">{formatPercentage(formData.designer_payout_rate)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Processing Fee:</span>
                  <span className="font-medium">{formatPercentage(formData.payment_processing_fee)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Minimum Project:</span>
                  <span className="font-medium">{formatCurrency(formData.minimum_project_value)}</span>
                </div>
                {formData.maximum_commission_cap && (
                  <div className="flex justify-between">
                    <span>Commission Cap:</span>
                    <span className="font-medium">{formatCurrency(formData.maximum_commission_cap)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Validation Warnings */}
            {formData.platform_commission_rate > 25 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 flex items-start">
                <AlertCircle className="h-4 w-4 text-yellow-600 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">High Commission Rate</p>
                  <p>Commission rates above 25% may discourage designers from joining the platform.</p>
                </div>
              </div>
            )}

            {formData.minimum_project_value < 50 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 flex items-start">
                <AlertCircle className="h-4 w-4 text-yellow-600 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">Low Minimum Value</p>
                  <p>Very low minimum project values may not be profitable after processing fees.</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
