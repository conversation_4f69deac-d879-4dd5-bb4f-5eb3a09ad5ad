import { NextRequest, NextResponse } from 'next/server';
import { S3Client, ListObjectsV2Command, GetObjectCommand } from '@aws-sdk/client-s3';
import { fromUrlSafeServiceId } from '@/lib/service-utils';

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

const BUCKET_NAME = 'design-style-images';

// Helper function to format service names for R2 path
function formatServiceForR2Path(name: string): string {
  return name
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
    .replace(/&/g, 'and');
}

// Helper function to format style names for R2 path
function formatStyleForR2Path(name: string): string {
  // For style names, we want to keep them as single words with first letter capitalized
  // First, handle any slashes in the name
  const processedName = name.replace(/\//g, '-');

  // Then process as before
  return processedName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to check if a style name is compound (contains slash or hyphen)
function isCompoundStyleName(name: string): boolean {
  return name.includes('/') || name.includes('-');
}

// Helper function to split a compound style name into individual terms
function splitCompoundStyleName(name: string): string[] {
  // First normalize slashes to hyphens
  const normalizedName = name.replace(/\//g, '-');

  // Split by hyphen and trim each part
  return normalizedName.split('-').map(part => part.trim()).filter(part => part.length > 0);
}

// Helper function to get the first part of a compound style name
// This is useful for styles like "Artistic/Handcrafted" where we might want to search for just "Artistic"
function getFirstPartOfStyle(name: string): string {
  // If it's not a compound name, return the original
  if (!isCompoundStyleName(name)) {
    return name;
  }

  // Split and return the first part
  const parts = splitCompoundStyleName(name);
  return parts.length > 0 ? parts[0] : name;
}

// Helper function to get the second part of a compound style name
// This is useful for styles like "Artistic/Handcrafted" where we might want to search for just "Handcrafted"
function getSecondPartOfStyle(name: string): string | null {
  // If it's not a compound name, return null
  if (!isCompoundStyleName(name)) {
    return null;
  }

  // Split and return the second part if it exists
  const parts = splitCompoundStyleName(name);
  return parts.length > 1 ? parts[1] : null;
}

// Helper function to get all individual terms from a compound style name
function getAllStyleTerms(name: string): string[] {
  // If it's not a compound name, return just the original
  if (!isCompoundStyleName(name)) {
    return [name];
  }

  // Split and return all parts
  return splitCompoundStyleName(name);
}

// Helper function to get a list of images for a specific service and style
async function getImagesForStyle(service: string, style: string): Promise<string[]> {
  try {
    const formattedService = formatServiceForR2Path(service);
    const formattedStyle = formatStyleForR2Path(style);

    // Get all individual terms from the style name
    const styleTerms = getAllStyleTerms(style);
    const formattedTerms = styleTerms.map(term => formatStyleForR2Path(term));

    // For compound style names, get the first and second parts to try as fallbacks
    const firstPartOfStyle = getFirstPartOfStyle(style);
    const formattedFirstPart = formatStyleForR2Path(firstPartOfStyle);

    const secondPartOfStyle = getSecondPartOfStyle(style);
    const formattedSecondPart = secondPartOfStyle ? formatStyleForR2Path(secondPartOfStyle) : null;

    // Log the style processing for debugging
    console.log(`Processing style: "${style}"`);
    console.log(`Formatted style: "${formattedStyle}"`);
    console.log(`Style terms: ${JSON.stringify(styleTerms)}`);
    console.log(`Formatted terms: ${JSON.stringify(formattedTerms)}`);
    console.log(`First part: "${firstPartOfStyle}", Formatted: "${formattedFirstPart}"`);
    if (secondPartOfStyle) {
      console.log(`Second part: "${secondPartOfStyle}", Formatted: "${formattedSecondPart}"`);
    }

    // Build prefix patterns based on the style terms
    let prefixPatterns = [
      // Pattern 1: Service Folder/Style-Service-Number.png (with full style name)
      `${formattedService}/${formattedStyle}-${formattedService}`,

      // Pattern 2: Service Folder/Style-Number.png (with full style name)
      `${formattedService}/${formattedStyle}`,
    ];

    // Add patterns for the first term
    prefixPatterns.push(
      // Pattern 3: Service Folder/FirstTerm-Service-Number.png
      `${formattedService}/${formattedFirstPart}-${formattedService}`,

      // Pattern 4: Service Folder/FirstTerm-Number.png
      `${formattedService}/${formattedFirstPart}`
    );

    // Add patterns for the second term if it exists
    if (formattedSecondPart) {
      prefixPatterns.push(
        // Pattern 5: Service Folder/SecondTerm-Service-Number.png
        `${formattedService}/${formattedSecondPart}-${formattedService}`,

        // Pattern 6: Service Folder/SecondTerm-Number.png
        `${formattedService}/${formattedSecondPart}`
      );
    }

    // Add patterns for each individual term (if there are more than 2)
    if (formattedTerms.length > 2) {
      for (let i = 2; i < formattedTerms.length; i++) {
        const term = formattedTerms[i];
        prefixPatterns.push(
          `${formattedService}/${term}-${formattedService}`,
          `${formattedService}/${term}`
        );
      }
    }

    // Finally, add the service folder pattern to list everything
    prefixPatterns.push(`${formattedService}/`);

    // Log the prefix patterns
    console.log(`Generated ${prefixPatterns.length} prefix patterns:`);
    prefixPatterns.forEach((pattern, index) => {
      console.log(`  Pattern ${index + 1}: ${pattern}`);
    });

    let allImageKeys: string[] = [];

    // Try each prefix pattern
    for (const prefix of prefixPatterns) {
      console.log(`Searching for images with prefix: ${prefix}`);

      const command = new ListObjectsV2Command({
        Bucket: BUCKET_NAME,
        Prefix: prefix,
      });

      console.log(`Sending S3 command to bucket: ${BUCKET_NAME}`);
      const response = await s3Client.send(command);

      if (response.Contents && response.Contents.length > 0) {
        console.log(`Found ${response.Contents.length} images for prefix: ${prefix}`);

        // Extract the keys (file paths) from the response
        const imageKeys = response.Contents.map(item => item.Key as string);

        // Filter to only include keys that contain any of the style terms
        // This is important for the service folder pattern where we list everything
        const filteredKeys = imageKeys.filter(key => {
          const lowerKey = key.toLowerCase();
          const matchDetails: string[] = [];

          // Try to match the full formatted style
          if (lowerKey.includes(formattedStyle.toLowerCase())) {
            matchDetails.push(`Full style: "${formattedStyle}"`);
            return true;
          }

          // Try to match each individual term
          for (let i = 0; i < formattedTerms.length; i++) {
            const term = formattedTerms[i].toLowerCase();
            if (lowerKey.includes(term)) {
              matchDetails.push(`Term ${i + 1}: "${formattedTerms[i]}"`);
              return true;
            }

            // Try without spaces
            const noSpaceTerm = term.replace(/\s+/g, '');
            if (noSpaceTerm !== term && lowerKey.includes(noSpaceTerm)) {
              matchDetails.push(`No-space term ${i + 1}: "${noSpaceTerm}"`);
              return true;
            }
          }

          // For styles with spaces (like "Classic / Timeless"), try variations
          const noSpaceStyle = formattedStyle.replace(/\s+/g, '').toLowerCase();
          if (noSpaceStyle !== formattedStyle.toLowerCase() && lowerKey.includes(noSpaceStyle)) {
            matchDetails.push(`No-space style: "${noSpaceStyle}"`);
            return true;
          }

          // If it's a compound style, try to match just the first or second part
          if (isCompoundStyleName(style)) {
            // First part
            if (lowerKey.includes(formattedFirstPart.toLowerCase())) {
              matchDetails.push(`First part: "${formattedFirstPart}"`);
              return true;
            }

            // First part without spaces
            const noSpaceFirstPart = formattedFirstPart.replace(/\s+/g, '').toLowerCase();
            if (noSpaceFirstPart !== formattedFirstPart.toLowerCase() && lowerKey.includes(noSpaceFirstPart)) {
              matchDetails.push(`No-space first part: "${noSpaceFirstPart}"`);
              return true;
            }

            // Second part if it exists
            if (formattedSecondPart && lowerKey.includes(formattedSecondPart.toLowerCase())) {
              matchDetails.push(`Second part: "${formattedSecondPart}"`);
              return true;
            }

            // Second part without spaces
            if (formattedSecondPart) {
              const noSpaceSecondPart = formattedSecondPart.replace(/\s+/g, '').toLowerCase();
              if (noSpaceSecondPart !== formattedSecondPart.toLowerCase() && lowerKey.includes(noSpaceSecondPart)) {
                matchDetails.push(`No-space second part: "${noSpaceSecondPart}"`);
                return true;
              }
            }
          }

          return false;
        });

        if (filteredKeys.length > 0) {
          console.log(`Found ${filteredKeys.length} images matching style: ${formattedStyle}`);
          console.log(`Sample image keys: ${filteredKeys.slice(0, 3).join(', ')}`);

          // Log the matching criteria for the first few images to help with debugging
          filteredKeys.slice(0, 3).forEach((key, index) => {
            const lowerKey = key.toLowerCase();
            const matchCriteria = [];

            // Check full style match
            if (lowerKey.includes(formattedStyle.toLowerCase())) {
              matchCriteria.push(`Full style: "${formattedStyle}"`);
            }

            // Check individual term matches
            for (let i = 0; i < formattedTerms.length; i++) {
              const term = formattedTerms[i].toLowerCase();
              if (lowerKey.includes(term)) {
                matchCriteria.push(`Term ${i + 1}: "${formattedTerms[i]}"`);
              }

              // Check no-space term
              const noSpaceTerm = term.replace(/\s+/g, '');
              if (noSpaceTerm !== term && lowerKey.includes(noSpaceTerm)) {
                matchCriteria.push(`No-space term ${i + 1}: "${noSpaceTerm}"`);
              }
            }

            // Check no-space style
            const noSpaceStyle = formattedStyle.replace(/\s+/g, '').toLowerCase();
            if (noSpaceStyle !== formattedStyle.toLowerCase() && lowerKey.includes(noSpaceStyle)) {
              matchCriteria.push(`No-space style: "${noSpaceStyle}"`);
            }

            // Check first part
            if (lowerKey.includes(formattedFirstPart.toLowerCase())) {
              matchCriteria.push(`First part: "${formattedFirstPart}"`);
            }

            // Check no-space first part
            const noSpaceFirstPart = formattedFirstPart.replace(/\s+/g, '').toLowerCase();
            if (noSpaceFirstPart !== formattedFirstPart.toLowerCase() && lowerKey.includes(noSpaceFirstPart)) {
              matchCriteria.push(`No-space first part: "${noSpaceFirstPart}"`);
            }

            // Check second part
            if (formattedSecondPart && lowerKey.includes(formattedSecondPart.toLowerCase())) {
              matchCriteria.push(`Second part: "${formattedSecondPart}"`);
            }

            // Check no-space second part
            if (formattedSecondPart) {
              const noSpaceSecondPart = formattedSecondPart.replace(/\s+/g, '').toLowerCase();
              if (noSpaceSecondPart !== formattedSecondPart.toLowerCase() && lowerKey.includes(noSpaceSecondPart)) {
                matchCriteria.push(`No-space second part: "${noSpaceSecondPart}"`);
              }
            }

            console.log(`Image ${index + 1} (${key}) matched by: ${matchCriteria.join(', ')}`);
          });

          allImageKeys = [...allImageKeys, ...filteredKeys];

          // If we found images with this prefix, we can stop trying other patterns
          break;
        }
      } else {
        console.warn(`No images found for prefix: ${prefix}`);
      }
    }

    if (allImageKeys.length === 0) {
      console.warn(`No images found for any prefix patterns for service: ${formattedService}, style: ${formattedStyle}`);
      return [];
    }

    // Log the first few keys to help with debugging
    console.log(`Final sample image keys: ${allImageKeys.slice(0, 3).join(', ')}`);

    // Limit to at most 3 images per style to avoid overwhelming the UI
    const limitedKeys = allImageKeys.slice(0, 3);

    // Generate signed URLs for each image
    const imageUrls = limitedKeys.map(key => {
      // For simplicity, we're returning just the key path
      // In a real implementation, you might want to generate signed URLs
      return key;
    });

    return imageUrls;
  } catch (error) {
    console.error('Error fetching images from R2:', error);
    return [];
  }
}

// API route handler for listing images
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  let service = searchParams.get('service');
  const style = searchParams.get('style');

  if (!service || !style) {
    return NextResponse.json(
      { error: 'Service and style parameters are required' },
      { status: 400 }
    );
  }

  // Decode the service ID if it's URL-encoded
  service = fromUrlSafeServiceId(service);

  try {
    console.log(`Processing request for service: "${service}", style: "${style}"`);
    const images = await getImagesForStyle(service, style);

    // Get style terms for debug info
    const styleTerms = getAllStyleTerms(style);
    const formattedTerms = styleTerms.map(term => formatStyleForR2Path(term));
    const firstPartOfStyle = getFirstPartOfStyle(style);
    const secondPartOfStyle = getSecondPartOfStyle(style);

    if (images.length === 0) {
      return NextResponse.json(
        {
          message: 'No images found for the specified service and style',
          fallback: true,
          images: [],
          debug: {
            service,
            style,
            isCompound: isCompoundStyleName(style),
            styleTerms,
            formattedService: formatServiceForR2Path(service),
            formattedStyle: formatStyleForR2Path(style),
            formattedTerms,
            firstPartOfStyle,
            formattedFirstPart: formatStyleForR2Path(firstPartOfStyle),
            secondPartOfStyle,
            formattedSecondPart: secondPartOfStyle ? formatStyleForR2Path(secondPartOfStyle) : null,
            // Include sample prefix patterns for debugging
            samplePrefixPatterns: [
              `${formatServiceForR2Path(service)}/${formatStyleForR2Path(style)}-${formatServiceForR2Path(service)}`,
              `${formatServiceForR2Path(service)}/${formatStyleForR2Path(style)}`,
              `${formatServiceForR2Path(service)}/${formatStyleForR2Path(firstPartOfStyle)}-${formatServiceForR2Path(service)}`,
              `${formatServiceForR2Path(service)}/${formatStyleForR2Path(firstPartOfStyle)}`,
              secondPartOfStyle ? `${formatServiceForR2Path(service)}/${formatStyleForR2Path(secondPartOfStyle)}-${formatServiceForR2Path(service)}` : null,
              secondPartOfStyle ? `${formatServiceForR2Path(service)}/${formatStyleForR2Path(secondPartOfStyle)}` : null,
              `${formatServiceForR2Path(service)}/`
            ].filter(Boolean) // Remove null values
          }
        },
        { status: 200 }
      );
    }

    return NextResponse.json({
      images,
      count: images.length,
      debug: {
        service,
        style,
        isCompound: isCompoundStyleName(style),
        styleTerms,
        formattedService: formatServiceForR2Path(service),
        formattedStyle: formatStyleForR2Path(style),
        formattedTerms,
        firstPartOfStyle,
        formattedFirstPart: formatStyleForR2Path(firstPartOfStyle),
        secondPartOfStyle,
        formattedSecondPart: secondPartOfStyle ? formatStyleForR2Path(secondPartOfStyle) : null
      }
    }, { status: 200 });
  } catch (error) {
    console.error('Error in R2 images API:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch images from storage',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// API route handler for getting a specific image
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { key } = body;

    if (!key) {
      return NextResponse.json(
        { error: 'Image key is required' },
        { status: 400 }
      );
    }

    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    const response = await s3Client.send(command);

    if (!response.Body) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    // Convert the readable stream to a buffer
    const chunks = [];
    for await (const chunk of response.Body as any) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);

    // Return the image as a base64 string
    const base64Image = buffer.toString('base64');
    const contentType = response.ContentType || 'image/png';
    const dataUrl = `data:${contentType};base64,${base64Image}`;

    return NextResponse.json({ imageData: dataUrl }, { status: 200 });
  } catch (error) {
    console.error('Error fetching image from R2:', error);
    return NextResponse.json(
      { error: 'Failed to fetch image from storage' },
      { status: 500 }
    );
  }
}
