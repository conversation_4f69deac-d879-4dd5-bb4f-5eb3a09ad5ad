"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useParams } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import { useAcceptProposal, useRejectProposal } from "@/hooks/useDashboardData";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  FileText,
  DollarSign,
  Clock,
  Calendar,
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  Eye,
  Star,
  Users
} from "lucide-react";

interface Proposal {
  id: string;
  title: string;
  description: string;
  total_budget: number;
  timeline_weeks: number;
  status: string;
  submitted_at: string;
  expires_at: string | null;
  designer_id: string;
  designer_name: string;
  designer_avatar: string | null;
  designer_rating: number;
  milestones: {
    title: string;
    description: string;
    amount: number;
    due_date: string;
  }[];
}
interface BriefInfo {
  id: string;
  title: string;
  description: string;
  budget_range: string;
  status: string;
}

export default function BriefProposals() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [briefInfo, setBriefInfo] = useState<BriefInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<'budget' | 'timeline' | 'submitted'>('submitted');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Use optimized hooks with correct parameters
  const acceptProposal = useAcceptProposal(user?.id || '');
  const rejectProposal = useRejectProposal();

  useEffect(() => {
    if (user && params.briefId) {
      fetchBriefAndProposals();
    }
  }, [user, params.briefId]);

  const fetchBriefAndProposals = async () => {
    if (!user || !params.briefId) return;

    setLoading(true);
    try {
      // Fetch brief info
      const { data: briefData, error: briefError } = await supabase
        .from('project_briefs')
        .select('id, title, description, budget_range, status')
        .eq('id', params.briefId)
        .eq('client_id', user.id)
        .single();

      if (briefError) throw briefError;
      setBriefInfo(briefData);

      // Fetch proposals for this brief
      const { data: proposalsData, error: proposalsError } = await supabase
        .from('project_proposals_enhanced')
        .select(`
          id,
          title,
          description,
          total_budget,
          timeline_weeks,
          status,
          submitted_at,
          expires_at,
          designer_id,
          milestones,
          profiles!project_proposals_enhanced_designer_id_fkey(
            full_name,
            avatar_url
          )
        `)
        .eq('brief_id', params.briefId)
        .order('submitted_at', { ascending: false });

      if (proposalsError) throw proposalsError;

      const formattedProposals: Proposal[] = (proposalsData || []).map(proposal => {
        const designer = Array.isArray(proposal.profiles) ? proposal.profiles[0] : proposal.profiles;
        return {
          id: proposal.id,
          title: proposal.title,
          description: proposal.description,
          total_budget: proposal.total_budget || 0,
          timeline_weeks: proposal.timeline_weeks || 0,
          status: proposal.status,
          submitted_at: proposal.submitted_at,
          expires_at: proposal.expires_at,
          designer_id: proposal.designer_id,
          designer_name: designer?.full_name || 'Unknown Designer',
          designer_avatar: designer?.avatar_url || null,
          designer_rating: 4.8,
          milestones: proposal.milestones || []
        };
      });

      setProposals(formattedProposals);
    } catch (error) {
      console.error('Error fetching brief and proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProposalAction = async (proposalId: string, action: 'accept' | 'reject') => {
    if (!confirm(`Are you sure you want to ${action} this proposal?`)) {
      return;
    }

    try {
      if (action === 'accept') {
        await acceptProposal.mutateAsync({
          proposalId,
          briefId: params.briefId as string
        });
      } else {
        await rejectProposal.mutateAsync({ proposalId, userId: user?.id || '' });
      }

      // Update local state for immediate feedback
      setProposals(prev => prev.map(p =>
        p.id === proposalId
          ? { ...p, status: action === 'accept' ? 'accepted' : 'rejected' }
          : p
      ));
    } catch (error) {
      console.error(`Error ${action}ing proposal:`, error);
    }
  };

  const sortedProposals = [...proposals].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case 'budget':
        aValue = a.total_budget;
        bValue = b.total_budget;
        break;
      case 'timeline':
        aValue = a.timeline_weeks;
        bValue = b.timeline_weeks;
        break;
      case 'submitted':
        aValue = new Date(a.submitted_at).getTime();
        bValue = new Date(b.submitted_at).getTime();
        break;
      default:
        return 0;
    }

    if (sortOrder === 'asc') {
      return aValue - bValue;
    } else {
      return bValue - aValue;
    }
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Enhanced status display for client perspective
  const getClientStatusDisplay = (status: string, expiresAt: string | null = null) => {
    const isExpired = expiresAt && new Date(expiresAt) < new Date();

    if (isExpired && (status === 'submitted' || status === 'under_review')) {
      return { text: "EXPIRED", color: "text-red-600 bg-red-50 border-red-200", icon: "expired" };
    }

    switch (status) {
      case "submitted": return { text: "NEW", color: "text-blue-600 bg-blue-50 border-blue-200", icon: "new" };
      case "under_review": return { text: "REVIEWING", color: "text-yellow-600 bg-yellow-50 border-yellow-200", icon: "review" };
      case "accepted": return { text: "ACCEPTED", color: "text-green-600 bg-green-50 border-green-200", icon: "accepted" };
      case "rejected": return { text: "REJECTED", color: "text-red-600 bg-red-50 border-red-200", icon: "rejected" };
      case "withdrawn": return { text: "WITHDRAWN", color: "text-gray-600 bg-gray-50 border-gray-200", icon: "withdrawn" };
      default: return { text: status.replace("_", " ").toUpperCase(), color: "text-gray-600 bg-gray-50 border-gray-200", icon: "default" };
    }
  };

  const getStatusIcon = (iconType: string) => {
    switch (iconType) {
      case "accepted": return <CheckCircle className="h-4 w-4" />;
      case "new": return <FileText className="h-4 w-4" />;
      case "review": return <Clock className="h-4 w-4" />;
      case "rejected": return <XCircle className="h-4 w-4" />;
      case "withdrawn": return <AlertCircle className="h-4 w-4" />;
      case "expired": return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-xs text-gray-500 ml-1">({rating})</span>
      </div>
    );
  };

  const avgBudget = proposals.length > 0 
    ? proposals.reduce((sum, p) => sum + p.total_budget, 0) / proposals.length 
    : 0;
  
  const avgTimeline = proposals.length > 0 
    ? proposals.reduce((sum, p) => sum + p.timeline_weeks, 0) / proposals.length 
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (!briefInfo) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <p className="text-red-700">Brief not found or access denied</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/client/briefs">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Briefs
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Proposals for "{briefInfo.title}"</h1>
            <p className="text-gray-600">{proposals.length} proposal{proposals.length !== 1 ? 's' : ''} received</p>
          </div>
        </div>
        <Link href="/client/proposals">
          <Button variant="outline">
            <Eye className="h-4 w-4 mr-2" />
            View All Proposals
          </Button>
        </Link>
      </div>

      {/* Brief Info */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Brief Details</h3>
        <p className="text-gray-600 mb-4">{briefInfo.description}</p>
        <div className="flex items-center space-x-6 text-sm text-gray-500">
          <span>Budget Range: {briefInfo.budget_range.replace('_', '-').toUpperCase()}</span>
          <span>Status: {briefInfo.status.toUpperCase()}</span>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Proposals</p>
              <p className="text-2xl font-bold text-gray-900">{proposals.length}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Budget</p>
              <p className="text-2xl font-bold text-gray-900">${Math.round(avgBudget).toLocaleString()}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Timeline</p>
              <p className="text-2xl font-bold text-gray-900">{Math.round(avgTimeline)} weeks</p>
            </div>
            <Clock className="h-8 w-8 text-orange-600" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">
                {proposals.filter(p => p.status === 'submitted').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
      </div>

      {/* Sorting Controls */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'budget' | 'timeline' | 'submitted')}
            className="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-brown-500"
          >
            <option value="submitted">Date Submitted</option>
            <option value="budget">Budget</option>
            <option value="timeline">Timeline</option>
          </select>
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
            className="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-brown-500"
          >
            <option value="desc">High to Low</option>
            <option value="asc">Low to High</option>
          </select>
        </div>
      </div>

      {/* Proposals List */}
      {proposals.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No proposals yet</h3>
          <p className="text-gray-500">Proposals from designers will appear here when they respond to this brief</p>
        </div>
      ) : (
        <div className="space-y-4">
          {sortedProposals.map((proposal, index) => (
            <motion.div
              key={proposal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{proposal.title}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border flex items-center ${getClientStatusDisplay(proposal.status, proposal.expires_at).color}`}>
                      {getStatusIcon(getClientStatusDisplay(proposal.status, proposal.expires_at).icon)}
                      <span className="ml-1">{getClientStatusDisplay(proposal.status, proposal.expires_at).text}</span>
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4 line-clamp-2">{proposal.description}</p>
                </div>
              </div>

              {/* Designer Info */}
              <div className="flex items-center space-x-3 mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {proposal.designer_avatar ? (
                    <img
                      src={proposal.designer_avatar}
                      alt={proposal.designer_name}
                      className="h-10 w-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="h-5 w-5 text-gray-500" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{proposal.designer_name}</p>
                  {renderStars(proposal.designer_rating)}
                </div>
                <Link href={`/client/messages?otherUserId=${proposal.designer_id}&type=direct`}>
                  <Button variant="outline" size="sm">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Message
                  </Button>
                </Link>
              </div>

              {/* Proposal Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center text-sm text-gray-500">
                  <DollarSign className="h-4 w-4 mr-2" />
                  ${proposal.total_budget.toLocaleString()}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="h-4 w-4 mr-2" />
                  {proposal.timeline_weeks} weeks
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  {formatDate(proposal.submitted_at)}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {proposal.milestones && proposal.milestones.length > 0 && (
                    <span className="text-xs text-gray-500">
                      {proposal.milestones.length} milestone{proposal.milestones.length !== 1 ? 's' : ''}
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                                    <Link href={`/client/proposals/${proposal.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </Link>
                  {(() => {
                    const isExpired = proposal.expires_at && new Date(proposal.expires_at) < new Date();
                    const canTakeAction = (proposal.status === 'submitted' || proposal.status === 'under_review') && !isExpired;

                    if (canTakeAction) {
                      return (
                        <>
                          <Button
                            onClick={() => handleProposalAction(proposal.id, 'accept')}
                            disabled={acceptProposal.isPending || rejectProposal.isPending}
                            size="sm"
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            {acceptProposal.isPending ? 'Accepting...' : 'Accept'}
                          </Button>
                          <Button
                            onClick={() => handleProposalAction(proposal.id, 'reject')}
                            disabled={acceptProposal.isPending || rejectProposal.isPending}
                            size="sm"
                            variant="outline"
                            className="border-red-600 text-red-600 hover:bg-red-50"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            {rejectProposal.isPending ? 'Rejecting...' : 'Reject'}
                          </Button>
                        </>
                      );
                    } else if (isExpired) {
                      return (
                        <span className="text-sm text-red-600 font-medium">
                          ⏰ Proposal Expired
                        </span>
                      );
                    }
                    return null;
                  })()}
                  {proposal.status === 'accepted' && (
                    <span className="text-sm text-green-600 font-medium">
                      ✓ Accepted
                    </span>
                  )}
                  {proposal.status === 'rejected' && (
                    <span className="text-sm text-red-600 font-medium">
                      ✗ Rejected
                    </span>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}