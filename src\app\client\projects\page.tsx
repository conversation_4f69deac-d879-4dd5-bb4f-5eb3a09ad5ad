"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useProjects } from "@/hooks/useDashboardData";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useResponsive } from "@/components/mobile/ResponsiveLayout";
import { Plus, Search, Filter, ChevronDown } from "lucide-react";

type Project = {
  id: string;
  title: string;
  type: string;
  status: string;
  quality_status?: string;
  created_at: string;
  updated_at: string;
  designer_name?: string;
  manager_name?: string;
};

export default function Projects() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  const { isMobile, isTablet } = useResponsive();
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "updated">("newest");

  // Use optimized data fetching with correct parameters
  const { data: projects = [], isLoading, error } = useProjects(user?.id || '', 'client');

  // Transform projects data for display
  const formattedProjects = projects.map(project => ({
    id: project.id,
    title: project.title,
    type: project.type || 'Not specified',
    status: project.status,
    quality_status: project.quality_status,
    created_at: project.created_at,
    updated_at: project.updated_at,
    designer_name: project.designer?.full_name || undefined,
    manager_name: project.manager?.full_name || undefined
  }));
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'review':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'submitted':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getQualityStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'pending_review':
        return 'bg-orange-100 text-orange-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'needs_revision':
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'escalated':
        return 'bg-purple-100 text-purple-800';
      case 'in_review':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const filterProjects = () => {
    let filtered = [...formattedProjects];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(project => project.status === statusFilter);
    }

    // Apply type filter
    if (typeFilter) {
      filtered = filtered.filter(project => project.type === typeFilter);
    }

    // Apply sorting
    switch (sortBy) {
      case "newest":
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case "oldest":
        filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        break;
      case "updated":
        filtered.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
        break;
    }

    return filtered;
  };

  const uniqueStatuses = [...new Set(formattedProjects.map(project => project.status))];
  const uniqueTypes = [...new Set(formattedProjects.map(project => project.type))];
  const filteredProjects = filterProjects();

  // Show loading only for initial load or auth loading
  const loading = authLoading || (isLoading && formattedProjects.length === 0);

  return (
    <div className="space-y-4 lg:space-y-6">
      {/* Header - Mobile Responsive */}
      <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'}`}>
        <div>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            My Projects
          </h1>
          <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}>
            Track and manage your architectural projects
          </p>
        </div>
        <Link href="/client/projects/new">
          <Button
            className={`bg-brown-600 hover:bg-brown-700 text-white ${
              isMobile ? 'w-full justify-center' : ''
            }`}
            size={isMobile ? "sm" : "default"}
          >
            <Plus className="mr-2 h-4 w-4" />
            New Project
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow-md mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 p-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div className="flex gap-2">
            <div className="relative">
              <button
                className="flex items-center p-2 border rounded-md bg-white"
                onClick={() => setStatusFilter(null)}
              >
                <Filter className="h-5 w-5 mr-1 text-gray-500" />
                {statusFilter || "Status"}
                <ChevronDown className="h-4 w-4 ml-1 text-gray-500" />
              </button>
              {statusFilter && (
                <span
                  className="absolute -top-2 -right-2 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                  onClick={() => setStatusFilter(null)}
                >
                  ×
                </span>
              )}
              <div className="absolute z-10 mt-1 w-48 bg-white shadow-lg rounded-md p-1 hidden group-focus:block">
                {uniqueStatuses.map(status => (
                  <button
                    key={status}
                    className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 rounded-md"
                    onClick={() => setStatusFilter(status)}
                  >
                    {status.replace('_', ' ')}
                  </button>
                ))}
              </div>
            </div>

            <div className="relative">
              <button
                className="flex items-center p-2 border rounded-md bg-white"
                onClick={() => setTypeFilter(null)}
              >
                <Filter className="h-5 w-5 mr-1 text-gray-500" />
                {typeFilter || "Type"}
                <ChevronDown className="h-4 w-4 ml-1 text-gray-500" />
              </button>
              {typeFilter && (
                <span
                  className="absolute -top-2 -right-2 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                  onClick={() => setTypeFilter(null)}
                >
                  ×
                </span>
              )}
              <div className="absolute z-10 mt-1 w-48 bg-white shadow-lg rounded-md p-1 hidden group-focus:block">
                {uniqueTypes.map(type => (
                  <button
                    key={type}
                    className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 rounded-md"
                    onClick={() => setTypeFilter(type)}
                  >
                    {type}
                  </button>
                ))}
              </div>
            </div>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as "newest" | "oldest" | "updated")}
              className="p-2 border rounded-md bg-white"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="updated">Recently Updated</option>
            </select>
          </div>
        </div>
      </div>

      {/* Projects List */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : filteredProjects.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-xl font-semibold mb-4">No projects found</h2>
          <p className="text-gray-500 mb-6">
            {formattedProjects.length === 0
              ? "You haven't created any projects yet."
              : "No projects match your current filters."}
          </p>
          {formattedProjects.length === 0 ? (
            <Link href="/client/projects/new">
              <Button>Create Your First Project</Button>
            </Link>
          ) : (
            <Button variant="outline" onClick={() => {
              setSearchQuery("");
              setStatusFilter(null);
              setTypeFilter(null);
            }}>
              Clear Filters
            </Button>
          )}
        </div>
      ) : (
        <div className={`grid gap-4 lg:gap-6 ${
          isMobile ? 'grid-cols-1' : 'grid-cols-1'
        }`}>
          {filteredProjects.map((project) => (
            <Link href={`/client/projects/${project.id}`} key={project.id}>
              <div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow ${
                isMobile ? 'p-4' : 'p-6'
              }`}>
                <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'justify-between items-start'}`}>
                  <div className="flex-1">
                    <h2 className={`font-semibold mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
                      {project.title}
                    </h2>
                    <p className={`text-gray-500 mb-4 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      {project.type} • Created on {formatDate(project.created_at)}
                    </p>
                    <div className="flex flex-wrap items-center gap-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                        {project.status.replace('_', ' ')}
                      </span>
                      {project.quality_status && (
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getQualityStatusColor(project.quality_status)}`}>
                          Quality: {project.quality_status.replace('_', ' ')}
                        </span>
                      )}
                    </div>
                    <div className="mt-2 text-sm text-gray-600 space-y-1">
                      {project.designer_name && (
                        <div>Designer: {project.designer_name}</div>
                      )}
                      {project.manager_name && (
                        <div>Manager: {project.manager_name}</div>
                      )}
                    </div>
                  </div>
                  <p className="text-xs text-gray-400">
                    Updated {formatDate(project.updated_at)}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}