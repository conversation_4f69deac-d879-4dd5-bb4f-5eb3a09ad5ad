import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/disputes/[id]
 * Gets a specific dispute by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const disputeId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Get the dispute
    const { data: dispute, error: disputeError } = await supabase
      .from('disputes')
      .select(`
        id,
        project_id,
        client_id,
        designer_id,
        created_by,
        status,
        title,
        description,
        resolution_notes,
        resolved_by,
        resolved_at,
        created_at,
        updated_at,
        projects:project_id (
          title
        ),
        client:client_id (
          full_name,
          avatar_url
        ),
        designer:designer_id (
          full_name,
          avatar_url
        ),
        creator:created_by (
          full_name,
          role
        ),
        resolver:resolved_by (
          full_name,
          role
        )
      `)
      .eq('id', disputeId)
      .single();
    
    if (disputeError) {
      return NextResponse.json(
        { error: 'Dispute not found' },
        { status: 404 }
      );
    }
    
    // Check if the user has permission to view this dispute
    if (profile.role !== 'admin' && dispute.client_id !== user.id && dispute.designer_id !== user.id) {
      return NextResponse.json(
        { error: 'You do not have permission to view this dispute' },
        { status: 403 }
      );
    }
    
    // Get dispute messages
    const { data: messages, error: messagesError } = await supabase
      .from('dispute_messages')
      .select(`
        id,
        dispute_id,
        sender_id,
        content,
        is_read,
        attachment_url,
        attachment_name,
        attachment_type,
        created_at,
        profiles:sender_id (
          full_name,
          avatar_url,
          role
        )
      `)
      .eq('dispute_id', disputeId)
      .order('created_at', { ascending: true });
    
    if (messagesError) {
      console.error('Error fetching dispute messages:', messagesError);
      // Continue anyway, we'll just return the dispute without messages
    }
    
    // Get dispute attachments
    const { data: attachments, error: attachmentsError } = await supabase
      .from('dispute_attachments')
      .select(`
        id,
        dispute_id,
        file_url,
        file_name,
        file_type,
        file_size,
        uploaded_by,
        created_at,
        profiles:uploaded_by (
          full_name,
          role
        )
      `)
      .eq('dispute_id', disputeId)
      .order('created_at', { ascending: true });
    
    if (attachmentsError) {
      console.error('Error fetching dispute attachments:', attachmentsError);
      // Continue anyway, we'll just return the dispute without attachments
    }
    
    // Mark messages as read if the user is not the sender
    if (messages && messages.length > 0) {
      const unreadMessageIds = messages
        .filter(message => message.sender_id !== user.id && !message.is_read)
        .map(message => message.id);
      
      if (unreadMessageIds.length > 0) {
        await supabase
          .from('dispute_messages')
          .update({ is_read: true })
          .in('id', unreadMessageIds);
      }
    }
    
    return NextResponse.json({
      dispute,
      messages: messages || [],
      attachments: attachments || []
    }, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/disputes/[id]:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/disputes/[id]
 * Updates a dispute's status or resolution notes
 * 
 * Request body:
 * {
 *   status?: 'open' | 'under_review' | 'resolved' | 'closed';
 *   resolutionNotes?: string;
 * }
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const disputeId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Only admins can update disputes
    if (profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Only administrators can update disputes' },
        { status: 403 }
      );
    }
    
    const { status, resolutionNotes } = await request.json();
    
    // Validate that at least one field is provided
    if (!status && !resolutionNotes) {
      return NextResponse.json(
        { error: 'At least one field (status or resolutionNotes) must be provided' },
        { status: 400 }
      );
    }
    
    // Prepare update data
    const updateData: any = {};
    
    if (status) {
      updateData.status = status;
      
      // If status is being set to 'resolved', set resolved_by and resolved_at
      if (status === 'resolved') {
        updateData.resolved_by = user.id;
        updateData.resolved_at = new Date().toISOString();
      }
    }
    
    if (resolutionNotes) {
      updateData.resolution_notes = resolutionNotes;
    }
    
    // Update the dispute
    const { data, error } = await supabase
      .from('disputes')
      .update(updateData)
      .eq('id', disputeId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating dispute:', error);
      return NextResponse.json(
        { error: 'Failed to update dispute' },
        { status: 500 }
      );
    }
    
    // Create notifications for the client and designer
    if (status) {
      const statusMessages = {
        open: 'The dispute has been reopened',
        under_review: 'The dispute is now under review',
        resolved: 'The dispute has been resolved',
        closed: 'The dispute has been closed'
      };
      
      const notificationContent = statusMessages[status as keyof typeof statusMessages] || `Dispute status updated to: ${status}`;
      
      // Notifications for client and designer
      await supabase
        .from('notifications')
        .insert([
          {
            user_id: data.client_id,
            type: 'dispute',
            title: 'Dispute Status Updated',
            content: notificationContent,
            related_id: data.id,
            read: false
          },
          {
            user_id: data.designer_id,
            type: 'dispute',
            title: 'Dispute Status Updated',
            content: notificationContent,
            related_id: data.id,
            read: false
          }
        ]);
    }
    
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error in PATCH /api/disputes/[id]:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
