"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Image, Clock, Plus } from "lucide-react";

type InspirationBoard = {
  id: string;
  title: string;
  description: string | null;
  created_at: string;
  image_count: number;
  cover_image: string | null;
};

export default function InspirationBoardsList({ projectId }: { projectId: string }) {
  const [boards, setBoards] = useState<InspirationBoard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchInspirationBoards();
  }, [projectId]);

  const fetchInspirationBoards = async () => {
    setLoading(true);
    try {
      // First get all boards
      const { data: boardsData, error: boardsError } = await supabase
        .from('inspiration_boards')
        .select('id, title, description, created_at')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (boardsError) throw boardsError;

      // For each board, get the count of images and a cover image
      const boardsWithDetails = await Promise.all(
        (boardsData || []).map(async (board) => {
          // Get image count
          const { count, error: countError } = await supabase
            .from('inspiration_images')
            .select('id', { count: 'exact' })
            .eq('board_id', board.id);

          if (countError) throw countError;

          // Get first image as cover
          const { data: imageData, error: imageError } = await supabase
            .from('inspiration_images')
            .select('image_url')
            .eq('board_id', board.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          if (imageError && imageError.code !== 'PGRST116') {
            // PGRST116 is the error code for no rows returned, which is fine
            throw imageError;
          }

          return {
            ...board,
            image_count: count || 0,
            cover_image: imageData?.image_url || null
          };
        })
      );

      setBoards(boardsWithDetails);
    } catch (error: Error | unknown) {
      console.error('Error fetching inspiration boards:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 text-red-500 p-4 rounded-lg">
        <p>Error loading inspiration boards: {error}</p>
      </div>
    );
  }

  if (boards.length === 0) {
    return (
      <div className="col-span-full text-center py-8">
        <p className="text-gray-500 mb-4">No inspiration boards yet.</p>
        <Link href={`/client/projects/${projectId}/inspirations/new`} className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90">
          <Plus className="mr-2 h-4 w-4" />
          Create Your First Board
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {boards.map((board) => (
        <Link href={`/client/projects/${projectId}/inspirations/${board.id}`} key={board.id}>
          <div className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow group">
            <div className="aspect-video relative bg-gray-100">
              {board.cover_image ? (
                <img
                  src={board.cover_image}
                  alt={board.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-200">
                  <Image className="h-12 w-12 text-gray-400" />
                </div>
              )}
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                {board.image_count} {board.image_count === 1 ? 'image' : 'images'}
              </div>
            </div>
            <div className="p-4">
              <h3 className="font-medium truncate">{board.title}</h3>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <Clock className="h-3 w-3 mr-1" />
                <span>{formatDate(board.created_at)}</span>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}