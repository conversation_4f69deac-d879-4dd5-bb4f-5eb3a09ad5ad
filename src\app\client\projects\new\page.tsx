"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  ArrowRight,
  Upload,
  X,
  DollarSign,
  Calendar,
  CheckCircle,
  AlertCircle,
  Loader2,
  Info
} from "lucide-react";
import Link from "next/link";

type ProjectFormData = {
  title: string;
  description: string;
  type: string;
  location: string;
  budget: string;
  timeline: string;
  requirements: string;
  scope: string;
  constraints: string;
  objectives: string[];
  start_date: string;
  end_date: string;
  milestones: Milestone[];
};

type Milestone = {
  id: string;
  title: string;
  description: string;
  amount: number;
  percentage: number;
};

const projectTypes = [
  "Residential - Single Family",
  "Residential - Multi-Family",
  "Commercial - Office",
  "Commercial - Retail",
  "Commercial - Hospitality",
  "Institutional",
  "Industrial",
  "Mixed-Use",
  "Landscape",
  "Interior Design",
  "Other"
];

export default function NewProject() {
  const { user } = useAuth();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<ProjectFormData>({
    title: "",
    description: "",
    type: "",
    location: "",
    budget: "",
    timeline: "",
    requirements: "",
    scope: "",
    constraints: "",
    objectives: [""],
    start_date: "",
    end_date: "",
    milestones: [
      {
        id: "initial-deposit",
        title: "Initial Deposit",
        description: "Initial payment to start the project",
        amount: 0,
        percentage: 30
      },
      {
        id: "milestone-1",
        title: "Milestone 1: Concept Design",
        description: "Completion of concept design phase",
        amount: 0,
        percentage: 30
      },
      {
        id: "milestone-2",
        title: "Milestone 2: Final Delivery",
        description: "Completion of all deliverables",
        amount: 0,
        percentage: 40
      }
    ]
  });
  const [inspirationFiles, setInspirationFiles] = useState<File[]>([]);
  const [filePreviewUrls, setFilePreviewUrls] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Update form data
    setFormData(prev => {
      const updatedData = { ...prev, [name]: value };

      // If budget is updated, recalculate milestone amounts
      if (name === 'budget' && value) {
        const budget = parseFloat(value);
        if (!isNaN(budget)) {
          updatedData.milestones = prev.milestones.map(milestone => ({
            ...milestone,
            amount: Math.round((budget * milestone.percentage) / 100)
          }));
        }
      }

      return updatedData;
    });
  };

  const handleMilestoneChange = (index: number, field: keyof Milestone, value: string | number) => {
    setFormData(prev => {
      const updatedMilestones = [...prev.milestones];
      updatedMilestones[index] = {
        ...updatedMilestones[index],
        [field]: value
      };

      // If percentage is updated, recalculate amount if budget exists
      if (field === 'percentage' && prev.budget) {
        const budget = parseFloat(prev.budget);
        if (!isNaN(budget)) {
          updatedMilestones[index].amount = Math.round((budget * (value as number)) / 100);
        }
      }

      // If amount is updated, recalculate percentage if budget exists
      if (field === 'amount' && prev.budget) {
        const budget = parseFloat(prev.budget);
        if (!isNaN(budget) && budget > 0) {
          updatedMilestones[index].percentage = Math.round(((value as number) / budget) * 100);
        }
      }

      return {
        ...prev,
        milestones: updatedMilestones
      };
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setInspirationFiles(prev => [...prev, ...newFiles]);

      // Create preview URLs
      const newPreviewUrls = newFiles.map(file => URL.createObjectURL(file));
      setFilePreviewUrls(prev => [...prev, ...newPreviewUrls]);
    }
  };

  const removeFile = (index: number) => {
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(filePreviewUrls[index]);

    setInspirationFiles(prev => prev.filter((_, i) => i !== index));
    setFilePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handleObjectiveChange = (index: number, value: string) => {
    setFormData(prev => {
      const updatedObjectives = [...prev.objectives];
      updatedObjectives[index] = value;
      return {
        ...prev,
        objectives: updatedObjectives
      };
    });
  };

  const addObjective = () => {
    setFormData(prev => ({
      ...prev,
      objectives: [...prev.objectives, ""]
    }));
  };

  const removeObjective = (index: number) => {
    setFormData(prev => {
      if (prev.objectives.length <= 1) return prev;

      const updatedObjectives = prev.objectives.filter((_, i) => i !== index);
      return {
        ...prev,
        objectives: updatedObjectives
      };
    });
  };

  const nextStep = () => {
    setCurrentStep(prev => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  const validateStep = () => {
    switch (currentStep) {
      case 1:
        return formData.title.trim() !== "" && formData.description.trim() !== "";
      case 2:
        return formData.type !== "" && formData.location.trim() !== "";
      case 3:
        return true; // Budget and timeline are optional
      case 4:
        // Validate that milestone percentages add up to 100%
        const totalPercentage = formData.milestones.reduce((sum, milestone) => sum + milestone.percentage, 0);
        return totalPercentage === 100;
      case 5:
        return true; // Inspirations are optional
      default:
        return true;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      console.log("Starting project creation...");
      console.log("User:", user);

      // 1. Create the project
      console.log("Creating project record...");
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .insert({
          client_id: user?.id,
          title: formData.title,
          description: formData.description,
          type: formData.type,
          location: formData.location,
          budget: formData.budget ? parseFloat(formData.budget) : null,
          timeline: formData.timeline,
          requirements: formData.requirements,
          scope: formData.scope,
          constraints: formData.constraints,
          objectives: formData.objectives,
          start_date: formData.start_date || null,
          end_date: formData.end_date || null,
          status: 'draft'  // Changed from 'pending' to 'draft' to match the database constraint
        })
        .select()
        .single();

      if (projectError) {
        console.error("Project creation error:", projectError);
        throw projectError;
      }
      console.log("Project created successfully:", projectData);

      // 2. Create milestones for the project
      console.log("Creating project milestones...");
      for (let i = 0; i < formData.milestones.length; i++) {
        const milestone = formData.milestones[i];
        const { error: milestoneError } = await supabase
          .from('project_milestones')
          .insert({
            project_id: projectData.id,
            title: milestone.title,
            description: milestone.description,
            amount: milestone.amount,
            percentage: milestone.percentage,
            status: i === 0 ? 'pending' : 'inactive', // Only the first milestone (deposit) is pending
            order_index: i
          });

        if (milestoneError) {
          console.error("Milestone creation error:", milestoneError);
          throw milestoneError;
        }
      }
      console.log("Milestones created successfully");

      // 2.5. Create project brief for admin/manager review (FIXED WORKFLOW)
      console.log("Creating project brief for admin review...");
      try {
        // Create a project brief instead of auto-assigning to managers
        const { error: briefError } = await supabase
          .from('project_briefs')
          .insert({
            client_id: user?.id,
            title: formData.title,
            description: formData.description,
            requirements: formData.requirements,
            budget_range: formData.budget ?
              (parseFloat(formData.budget) < 5000 ? 'under_5k' :
               parseFloat(formData.budget) < 10000 ? '5k_10k' :
               parseFloat(formData.budget) < 25000 ? '10k_25k' : '25k_plus') : 'flexible',
            timeline_preference: formData.timeline,
            location: formData.location,
            project_type: formData.type,
            urgency: 'medium',
            status: 'pending' // Admin will review and assign to designer
          });

        if (briefError) {
          console.error("Error creating project brief:", briefError);
        } else {
          console.log("Project brief created for admin review");
        }

        // Notify admins about new project
        const { data: admins } = await supabase
          .from('profiles')
          .select('id')
          .eq('role', 'admin')
          .eq('is_active', true);

        if (admins && admins.length > 0) {
          const adminNotifications = admins.map(admin => ({
            user_id: admin.id,
            type: 'project',
            title: 'New Project Needs Assignment',
            content: `New project "${formData.title}" needs to be assigned to a designer`,
            related_id: projectData.id,
            read: false
          }));

          await supabase.from('notifications').insert(adminNotifications);
        }
      } catch (briefError) {
        console.error("Error in project brief creation:", briefError);
        // Don't fail the entire project creation if brief creation fails
      }

      // 4. Create an inspiration board for the project
      console.log("Creating inspiration board...");
      const { data: boardData, error: boardError } = await supabase
        .from('inspiration_boards')
        .insert({
          project_id: projectData.id,
          title: 'Initial Inspirations',
          description: 'Inspirations uploaded during project creation'
        })
        .select()
        .single();

      if (boardError) {
        console.error("Board creation error:", boardError);
        throw boardError;
      }
      console.log("Inspiration board created successfully:", boardData);

      // 5. Upload inspiration files if any
      if (inspirationFiles.length > 0) {
        console.log(`Uploading ${inspirationFiles.length} inspiration files...`);

        for (const file of inspirationFiles) {
          console.log("Processing file:", file.name);

          // Upload file to storage
          const fileExt = file.name.split('.').pop();
          const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
          const filePath = `inspiration-images/${boardData.id}/${fileName}`;

          console.log("Uploading to path:", filePath);
          console.log("Bucket name: project-files");

          const { error: uploadError } = await supabase.storage
            .from('project-files')
            .upload(filePath, file);

          if (uploadError) {
            console.error("File upload error:", uploadError);
            throw uploadError;
          }
          console.log("File uploaded successfully");

          // Get public URL
          console.log("Getting public URL...");
          const { data: urlData } = supabase.storage
            .from('project-files')
            .getPublicUrl(filePath);

          console.log("Public URL:", urlData.publicUrl);

          // Add image to inspiration_images table
          console.log("Adding image record to database...");
          const { error: imageError } = await supabase
            .from('inspiration_images')
            .insert({
              board_id: boardData.id,
              image_url: urlData.publicUrl,
              caption: file.name
            });

          if (imageError) {
            console.error("Image record creation error:", imageError);
            throw imageError;
          }
          console.log("Image record created successfully");
        }
      }

      // Redirect to the project page
      console.log("All operations completed successfully, redirecting...");
      router.push(`/client/projects/${projectData.id}`);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while creating your project';
      console.error('Error creating project:', error);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-8">
        <Link href="/client/projects" className="inline-flex items-center text-gray-600 hover:text-primary">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Projects
        </Link>
        <h1 className="text-2xl font-bold mt-4">New Project Request</h1>
      </div>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 border-l-4 border-red-500">
          {error}
        </div>
      )}

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4, 5].map((step) => (
            <div key={step} className="flex flex-col items-center">
              <div
                className={`w-8 h-8 flex items-center justify-center ${
                  currentStep >= step
                    ? "bg-brown-600 text-white"
                    : "bg-gray-200 text-gray-500"
                }`}
              >
                {step}
              </div>
              <span className="text-xs mt-1 text-gray-500">
                {step === 1 && "Basics"}
                {step === 2 && "Details"}
                {step === 3 && "Requirements"}
                {step === 4 && "Milestones"}
                {step === 5 && "Inspirations"}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute top-0 left-0 h-1 bg-gray-200 w-full"></div>
          <div
            className="absolute top-0 left-0 h-1 bg-brown-600 transition-all duration-300"
            style={{ width: `${((currentStep - 1) / 4) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
        {/* Step 1: Basic Information */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-4">Project Basics</h2>

            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Project Title <span className="text-red-500">*</span>
              </label>
              <input
                id="title"
                name="title"
                type="text"
                value={formData.title}
                onChange={handleChange}
                required
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="e.g., Modern Family Home Renovation"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Project Description <span className="text-red-500">*</span>
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={5}
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Describe your project, goals, and vision..."
              />
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                onClick={nextStep}
                disabled={!validateStep()}
                className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
              >
                Next Step
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: Project Details */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-4">Project Details</h2>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Project Type <span className="text-red-500">*</span>
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                required
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select a project type</option>
                {projectTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                Project Location <span className="text-red-500">*</span>
              </label>
              <input
                id="location"
                name="location"
                type="text"
                value={formData.location}
                onChange={handleChange}
                required
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="e.g., Nairobi, Kenya"
              />
            </div>

            <div>
              <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-1">
                Estimated Budget (USD)
              </label>
              <input
                id="budget"
                name="budget"
                type="number"
                value={formData.budget}
                onChange={handleChange}
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="e.g., 50000"
              />
            </div>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                className="flex items-center"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </Button>
              <Button
                type="button"
                onClick={nextStep}
                disabled={!validateStep()}
                className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
              >
                Next Step
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Step 3: Requirements */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-4">Project Requirements & Scope</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-1">
                  Desired Start Date
                </label>
                <input
                  id="start_date"
                  name="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={handleChange}
                  className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-1">
                  Target Completion Date
                </label>
                <input
                  id="end_date"
                  name="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={handleChange}
                  className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-1">
                Expected Timeline Description
              </label>
              <input
                id="timeline"
                name="timeline"
                type="text"
                value={formData.timeline}
                onChange={handleChange}
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="e.g., 6 months, by end of year, etc."
              />
            </div>

            <div>
              <label htmlFor="scope" className="block text-sm font-medium text-gray-700 mb-1">
                Project Scope
              </label>
              <textarea
                id="scope"
                name="scope"
                value={formData.scope}
                onChange={handleChange}
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Define what is included in the project scope..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Project Objectives
              </label>
              <div className="space-y-2">
                {formData.objectives.map((objective, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <input
                      type="text"
                      value={objective}
                      onChange={(e) => handleObjectiveChange(index, e.target.value)}
                      className="flex-1 p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder={`Objective ${index + 1}`}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeObjective(index)}
                      disabled={formData.objectives.length <= 1}
                      className="h-10 w-10 p-0 flex items-center justify-center"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addObjective}
                  className="mt-2"
                >
                  Add Objective
                </Button>
              </div>
            </div>

            <div>
              <label htmlFor="requirements" className="block text-sm font-medium text-gray-700 mb-1">
                Specific Requirements
              </label>
              <textarea
                id="requirements"
                name="requirements"
                value={formData.requirements}
                onChange={handleChange}
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="List any specific requirements for the project..."
              />
            </div>

            <div>
              <label htmlFor="constraints" className="block text-sm font-medium text-gray-700 mb-1">
                Project Constraints
              </label>
              <textarea
                id="constraints"
                name="constraints"
                value={formData.constraints}
                onChange={handleChange}
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Describe any constraints or limitations (budget, time, regulations, etc.)..."
              />
            </div>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                className="flex items-center"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </Button>
              <Button
                type="button"
                onClick={nextStep}
                className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
              >
                Next Step
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Step 4: Milestones */}
        {currentStep === 4 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Payment Milestones</h2>
              <div className="flex items-center text-sm text-gray-500">
                <Info className="h-4 w-4 mr-1" />
                <span>Total: 100%</span>
              </div>
            </div>

            <div className="bg-amber-50 border border-amber-200 p-4 mb-4">
              <p className="text-sm text-amber-800">
                <strong>How it works:</strong> Payments are held in escrow until each milestone is completed and approved. The initial deposit is required to start the project.
              </p>
            </div>

            {formData.milestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border border-gray-200 p-4"
              >
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
                  <div className="flex-1">
                    <label htmlFor={`milestone-title-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                      Milestone Title
                    </label>
                    <input
                      id={`milestone-title-${index}`}
                      type="text"
                      value={milestone.title}
                      onChange={(e) => handleMilestoneChange(index, 'title', e.target.value)}
                      className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    />
                  </div>

                  <div className="w-24">
                    <label htmlFor={`milestone-percentage-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                      Percentage
                    </label>
                    <div className="flex items-center">
                      <input
                        id={`milestone-percentage-${index}`}
                        type="number"
                        min="1"
                        max="100"
                        value={milestone.percentage}
                        onChange={(e) => handleMilestoneChange(index, 'percentage', parseInt(e.target.value) || 0)}
                        className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      />
                      <span className="ml-1">%</span>
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor={`milestone-description-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id={`milestone-description-${index}`}
                    value={milestone.description}
                    onChange={(e) => handleMilestoneChange(index, 'description', e.target.value)}
                    rows={2}
                    className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                </div>

                {formData.budget && (
                  <div className="bg-gray-50 p-3 flex items-center">
                    <DollarSign className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-700">
                      Amount: <strong>${milestone.amount.toLocaleString()}</strong>
                    </span>
                  </div>
                )}
              </motion.div>
            ))}

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                className="flex items-center"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </Button>
              <Button
                type="button"
                onClick={nextStep}
                disabled={!validateStep()}
                className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
              >
                Next Step
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Step 5: Inspirations */}
        {currentStep === 5 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-4">Inspiration Images</h2>

            <div className="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center">
              <input
                type="file"
                id="inspirationFiles"
                multiple
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
              <label
                htmlFor="inspirationFiles"
                className="cursor-pointer flex flex-col items-center justify-center"
              >
                <Upload className="h-12 w-12 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 mb-1">
                  Drag and drop image files here, or click to select files
                </p>
                <p className="text-xs text-gray-400">
                  Upload images that inspire your project vision
                </p>
              </label>
            </div>

            {filePreviewUrls.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Selected Images ({filePreviewUrls.length})
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {filePreviewUrls.map((url, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={url}
                        alt={`Inspiration ${index + 1}`}
                        className="h-32 w-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={() => removeFile(index)}
                        className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                className="flex items-center"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating Project...
                  </>
                ) : (
                  "Submit Project Request"
                )}
              </Button>
            </div>
          </div>
        )}
      </form>
  </div>
);
}
