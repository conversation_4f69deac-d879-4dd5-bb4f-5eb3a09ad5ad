"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { CheckCircle, AlertCircle } from "lucide-react";

export default function SignUp() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteCode = searchParams.get('invite');

  const [inviteData, setInviteData] = useState<{
    id: string;
    role: 'client' | 'designer';
    createdBy: string;
    inviter: {
      full_name: string;
      avatar_url?: string;
    };
  } | null>(null);

  const [validatingInvite, setValidatingInvite] = useState(false);
  const [inviteError, setInviteError] = useState<string | null>(null);

  // Validate invite code if present
  useEffect(() => {
    if (inviteCode) {
      validateInviteCode(inviteCode);
    }
  }, [inviteCode]);

  const validateInviteCode = async (code: string) => {
    setValidatingInvite(true);
    setInviteError(null);

    try {
      const response = await fetch('/api/invitations/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ inviteCode: code })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Invalid invitation code');
      }

      if (data.valid && data.invitation) {
        setInviteData({
          id: data.invitation.id,
          role: data.invitation.role,
          createdBy: data.invitation.createdBy,
          inviter: data.invitation.inviter
        });

        // Pre-fill email if provided in the invitation
        if (data.invitation.email) {
          setEmail(data.invitation.email);
        }
      }
    } catch (error) {
      console.error('Error validating invite code:', error);
      setInviteError(error instanceof Error ? error.message : 'Failed to validate invitation');
    } finally {
      setValidatingInvite(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Sign up the user
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: name,
            role: inviteData ? inviteData.role : 'client', // Use invite role or default to client
            invite_code: inviteCode,
            invited_by: inviteData ? inviteData.createdBy : null
          }
        }
      });

      if (signUpError) throw new Error(signUpError.message);

      if (!authData.user) {
        throw new Error('Failed to create user account');
      }

      // If this is an invited user, update the invitation status and create connection
      if (inviteCode && authData.user) {
        try {
          // Update invitation status
          await fetch(`/api/invitations/${inviteCode}/accept`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              userId: authData.user.id
            })
          });

          // Create connection if this is a client invited by a designer
          if (inviteData?.role === 'client') {
            await fetch('/api/connections', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                designerId: inviteData.createdBy,
                clientId: authData.user.id,
                createdBy: inviteData.createdBy,
                invitationId: inviteData.id
              })
            });
          }
        } catch (connectionError) {
          console.error('Error creating connection:', connectionError);
          // Continue anyway, as the user account was created successfully
        }
      }

      setSuccess(true);

      // Redirect to confirmation page after a short delay
      setTimeout(() => {
        // Pass invitation information to the confirmation page if available
        const params = new URLSearchParams();
        if (inviteData?.role) {
          params.append('role', inviteData.role);
        }
        if (inviteData?.inviter?.full_name) {
          params.append('inviter', encodeURIComponent(inviteData.inviter.full_name));
        }

        const queryString = params.toString();
        router.push(`/auth/confirmation${queryString ? `?${queryString}` : ''}`);
      }, 1500);
    } catch (error: unknown) {
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An error occurred during sign up");
      }
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-black">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black bg-opacity-70 z-10" aria-hidden="true" />
        <img
          src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c"
          alt="Architectural background"
          className="object-cover w-full h-full"
        />
      </div>

      <div className="relative z-10 bg-white p-10 border border-gray-200 shadow-2xl max-w-md w-full">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <h1 className="text-brown-600 font-bold text-2xl tracking-tight">SENIOR'S ARCHI-FIRM</h1>
          </Link>
          <h2 className="text-2xl font-bold mt-6 mb-2">Create an Account</h2>
          <p className="text-gray-600">Join us to bring your architectural vision to life</p>
        </div>

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="bg-green-50 p-4 mb-6 border-l-4 border-green-500 flex items-start"
          >
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <p className="text-green-800">Account created successfully!</p>
              <p className="text-green-700 text-sm mt-1">Redirecting to confirmation page...</p>
            </div>
          </motion.div>
        )}

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="bg-red-50 p-4 mb-6 border-l-4 border-red-500 flex items-start"
          >
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-red-700">{error}</p>
          </motion.div>
        )}

        {inviteError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="bg-amber-50 p-4 mb-6 border-l-4 border-amber-500 flex items-start"
          >
            <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-amber-700">{inviteError}</p>
          </motion.div>
        )}

        {inviteData && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4 }}
            className="mb-6 p-4 bg-amber-50 border border-amber-200"
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                {inviteData.inviter.avatar_url ? (
                  <img
                    src={inviteData.inviter.avatar_url}
                    alt={inviteData.inviter.full_name}
                    className="w-10 h-10 border border-amber-200 mr-3 object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 bg-amber-200 flex items-center justify-center mr-3">
                    <span className="text-amber-800 font-medium">
                      {inviteData.inviter.full_name.charAt(0)}
                    </span>
                  </div>
                )}
                <div>
                  <p className="text-sm text-amber-800">
                    <span className="font-medium">{inviteData.inviter.full_name}</span> has invited you to join as a <strong>{inviteData.role}</strong>
                  </p>
                  {inviteData.role === 'client' && (
                    <p className="text-xs text-amber-700 mt-1">
                      You'll be automatically connected with your designer after signup.
                    </p>
                  )}
                </div>
              </div>
              <Link
                href={`/invite/preview/${inviteCode}`}
                className="text-xs text-amber-700 hover:text-amber-800 font-medium underline"
              >
                View Profile
              </Link>
            </div>
          </motion.div>
        )}

        <form onSubmit={handleSignUp} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Full Name
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="w-full p-3 border border-gray-300 focus:outline-none focus:border-brown-500"
              placeholder="John Doe"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full p-3 border border-gray-300 focus:outline-none focus:border-brown-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full p-3 border border-gray-300 focus:outline-none focus:border-brown-500"
              placeholder="••••••••"
              minLength={8}
            />
          </div>

          <Button
            type="submit"
            variant="default"
            size="lg"
            className="w-full bg-brown-600 hover:bg-brown-700 text-white border-0"
            disabled={loading}
          >
            {loading ? (
              <motion.div className="flex items-center justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-2"
                >
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </motion.div>
                Creating Account...
              </motion.div>
            ) : (
              "Sign Up"
            )}
          </Button>
        </form>

        <div className="mt-8 text-center">
          <p className="text-gray-600">
            Already have an account?{" "}
            <Link href="/auth/login" className="text-brown-600 font-medium hover:underline">
              Log In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
