"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Calendar,
  Clock,
  Users,
  FileText,
  Upload,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  BarChart,
  Edit,
  Trash,
  AlertTriangle
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  description: string;
  status: string;
  client_id: string;
  client_name: string;
  client_avatar: string | null;
  deadline: string | null;
  created_at: string;
  updated_at: string;
  progress: number;
  requirements: string | null;
};

type Submission = {
  id: string;
  title: string;
  description: string;
  status: string;
  created_at: string;
  revision_requested: boolean;
  feedback: string | null;
};

export default function ProjectDetail() {
  const { id } = useParams();
  const router = useRouter();
  const { user } = useOptimizedAuth();
  const [project, setProject] = useState<Project | null>(null);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  useEffect(() => {
    if (user && id) {
      fetchProjectData();
    }
  }, [user, id]);

  const fetchProjectData = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          status,
          quality_status,
          deadline,
          created_at,
          updated_at,
          progress,
          requirements,
          client_id,
          clients:profiles!client_id(full_name, avatar_url)
        `)
        .eq('id', id)
        .eq('designer_id', user?.id)
        .single();

      if (projectError) throw projectError;

      if (!projectData) {
        router.push('/designer/projects');
        return;
      }

      // Fetch submissions for this project
      const { data: submissionsData, error: submissionsError } = await supabase
        .from('submissions')
        .select(`
          id,
          title,
          description,
          status,
          created_at,
          revision_requested,
          feedback
        `)
        .eq('project_id', id)
        .eq('designer_id', user?.id)
        .order('created_at', { ascending: false });

      if (submissionsError) throw submissionsError;

      setProject({
        id: projectData.id,
        title: projectData.title,
        description: projectData.description,
        status: projectData.status,
        client_id: projectData.client_id,
        client_name: projectData.clients[0]?.full_name || 'Unknown Client',
        client_avatar: projectData.clients[0]?.avatar_url,
        deadline: projectData.deadline,
        created_at: projectData.created_at,
        updated_at: projectData.updated_at,
        progress: projectData.progress,
        requirements: projectData.requirements
      });

      setSubmissions(submissionsData || []);
    } catch (error: Error | unknown) {
      console.error('Error fetching project data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };
  const updateProjectProgress = async (newProgress: number) => {
    try {
      const { error } = await supabase
        .from('projects')
        .update({ progress: newProgress })
        .eq('id', id)
        .eq('designer_id', user?.id);

      if (error) throw error;

      setProject(prev => prev ? { ...prev, progress: newProgress } : null);
      setSuccess('Project progress updated successfully');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: Error | unknown) {
      console.error('Error updating project progress:', error);
      setError(error instanceof Error ? error.message : 'Failed to update project progress');
    }
  };

  const deleteProject = async () => {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', id)
        .eq('designer_id', user?.id);

      if (error) throw error;

      router.push('/designer/projects');
    } catch (error: Error | unknown) {
      console.error('Error deleting project:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete project');
      setDeleteConfirm(false);
    }
  };
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No deadline';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'review':
        return 'bg-purple-100 text-purple-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatusLabel = (status: string) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getQualityStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending_review':
        return 'bg-orange-100 text-orange-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'needs_revision':
        return 'bg-red-100 text-red-800';
      case 'escalated':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatQualityStatusLabel = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending_review':
        return 'Pending Review';
      case 'approved':
        return 'Approved';
      case 'needs_revision':
        return 'Needs Revision';
      case 'escalated':
        return 'Escalated';
      default:
        return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  if (loading && !project) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="bg-red-50 text-red-500 p-4 rounded-lg flex items-center">
        <AlertCircle className="h-5 w-5 mr-2" />
        <p>Project not found or you don't have access to it.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-8">
        <Link href="/designer/projects">
          <Button variant="ghost" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{project.title}</h1>
      </div>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="bg-green-50 text-green-500 p-4 mb-6 rounded-lg flex items-center">
          <CheckCircle className="h-5 w-5 mr-2" />
          <p>{success}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Project Details */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-lg font-semibold">Project Details</h2>
              <div className="flex space-x-2">
                <Link href={`/designer/projects/${id}/edit`}>
                  <Button variant="outline" size="sm" className="flex items-center">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </Link>
                {!deleteConfirm ? (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center text-red-500 hover:bg-red-50"
                    onClick={() => setDeleteConfirm(true)}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={deleteProject}
                      className="bg-red-600 hover:bg-red-700 text-white"
                    >
                      Confirm
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDeleteConfirm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                )}
              </div>
            </div>

            <div className="p-6">
              <div className="flex flex-wrap gap-4 mb-6">
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
                  {formatStatusLabel(project.status)}
                </div>

                {project.quality_status && (
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${getQualityStatusColor(project.quality_status)}`}>
                    Quality: {formatQualityStatusLabel(project.quality_status)}
                  </div>
                )}

                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>Due: {formatDate(project.deadline)}</span>
                </div>

                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>Created: {formatDate(project.created_at)}</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                <p className="text-gray-700 whitespace-pre-line">{project.description}</p>
              </div>

              {project.requirements && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Requirements</h3>
                  <p className="text-gray-700 whitespace-pre-line">{project.requirements}</p>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Progress</h3>
                <div className="flex items-center mb-2">
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mr-4 flex-grow">
                    <div
                      className="bg-primary h-2.5 rounded-full"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-700">{project.progress}%</span>
                </div>

                <div className="flex flex-wrap gap-2 mt-4">
                  {[0, 25, 50, 75, 100].map(progress => (
                    <Button
                      key={progress}
                      variant={project.progress === progress ? "default" : "outline"}
                      size="sm"
                      onClick={() => updateProjectProgress(progress)}
                    >
                      {progress}%
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Submissions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-lg font-semibold">Submissions</h2>
              <Link href={`/designer/projects/${id}/submissions/new`}>
                <Button className="flex items-center">
                  <Upload className="h-4 w-4 mr-2" />
                  New Submission
                </Button>
              </Link>
            </div>

            {submissions.length === 0 ? (
              <div className="p-8 text-center">
                <Upload className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="font-medium mb-1">No submissions yet</h3>
                <p className="text-sm text-gray-500 mb-4">
                  You haven't made any submissions for this project yet.
                </p>
                <Link href={`/designer/projects/${id}/submissions/new`}>
                  <Button>Create Your First Submission</Button>
                </Link>
              </div>
            ) : (
              <div className="divide-y">
                {submissions.map((submission) => (
                  <Link href={`/designer/projects/${id}/submissions/${submission.id}`} key={submission.id}>
                    <div className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{submission.title}</h3>
                        <div className="flex items-center">
                          {submission.revision_requested && (
                            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full mr-2">
                              Revision Requested
                            </span>
                          )}
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(submission.status)}`}>
                            {formatStatusLabel(submission.status)}
                          </span>
                        </div>
                      </div>

                      <p className="text-sm text-gray-500 mb-3 line-clamp-2">{submission.description}</p>

                      <div className="flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>Submitted: {formatDate(submission.created_at)}</span>

                        {submission.feedback && (
                          <>
                            <span className="mx-2">•</span>
                            <FileText className="h-3 w-3 mr-1" />
                            <span>Has feedback</span>
                          </>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          {/* Client Info */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Client</h2>
            </div>

            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-4">
                  {project.client_avatar ? (
                    <img
                      src={project.client_avatar}
                      alt={project.client_name}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <Users className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <div>
                  <h3 className="font-medium">{project.client_name}</h3>
                  <Link href={`/designer/clients/${project.client_id}`}>
                    <p className="text-sm text-primary">View Profile</p>
                  </Link>
                </div>
              </div>

              <Link href={`/designer/messages?client=${project.client_id}`}>
                <Button variant="outline" className="w-full flex items-center justify-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Message Client
                </Button>
              </Link>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Quick Actions</h2>
            </div>

            <div className="p-6 space-y-4">
              <Link href={`/designer/projects/${id}/submissions/new`}>
                <Button className="w-full justify-between">
                  Create New Submission
                  <Upload className="h-4 w-4 ml-2" />
                </Button>
              </Link>

              <Link href={`/designer/projects/${id}/edit`}>
                <Button variant="outline" className="w-full justify-between">
                  Edit Project Details
                  <Edit className="h-4 w-4 ml-2" />
                </Button>
              </Link>

              <Link href={`/designer/messages?client=${project.client_id}`}>
                <Button variant="outline" className="w-full justify-between">
                  Message Client
                  <MessageSquare className="h-4 w-4 ml-2" />
                </Button>
              </Link>

              <Link href={`/projects/${id}/disputes/new`}>
                <Button variant="outline" className="w-full justify-between">
                  Report Issue
                  <AlertTriangle className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Project Timeline */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Project Timeline</h2>
            </div>

            <div className="p-6">
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                <div className="relative mb-6">
                  <div className="flex items-center mb-2">
                    <div className="z-10 flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <Calendar className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium">Project Created</h3>
                      <p className="text-xs text-gray-500">{formatDate(project.created_at)}</p>
                    </div>
                  </div>
                </div>

                {submissions.map((submission, index) => (
                  <div className="relative mb-6" key={submission.id}>
                    <div className="flex items-center mb-2">
                      <div className="z-10 flex items-center justify-center w-8 h-8 bg-purple-100 rounded-full">
                        <Upload className="w-4 h-4 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-sm font-medium">Submission: {submission.title}</h3>
                        <p className="text-xs text-gray-500">{formatDate(submission.created_at)}</p>
                      </div>
                    </div>
                  </div>
                ))}

                {project.deadline && (
                  <div className="relative">
                    <div className="flex items-center mb-2">
                      <div className="z-10 flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                        <Calendar className="w-4 h-4 text-red-600" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-sm font-medium">Project Deadline</h3>
                        <p className="text-xs text-gray-500">{formatDate(project.deadline)}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );}
