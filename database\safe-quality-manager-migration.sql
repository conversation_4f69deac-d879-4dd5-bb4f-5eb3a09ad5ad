-- =====================================================
-- SAFE QUALITY TEAM & MANAGER ROLES MIGRATION
-- This version handles existing role columns safely
-- Run this script in Supabase SQL Editor
-- =====================================================

-- 1. SAFELY UPDATE USER ROLES
-- Handle the role column conversion step by step

-- First, let's check what we're working with
DO $$ 
DECLARE
    role_column_type TEXT;
    enum_exists BOOLEAN;
BEGIN
    -- Check if user_role enum exists
    SELECT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') INTO enum_exists;
    
    -- Get current role column type
    SELECT data_type INTO role_column_type 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'role';
    
    RAISE NOTICE 'Current role column type: %, Enum exists: %', role_column_type, enum_exists;
    
    -- If enum doesn't exist, create it
    IF NOT enum_exists THEN
        CREATE TYPE user_role AS ENUM ('client', 'designer', 'admin', 'quality_team', 'manager');
        RAISE NOTICE 'Created user_role enum';
    ELSE
        -- Add new values to existing enum
        BEGIN
            ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'quality_team';
            RAISE NOTICE 'Added quality_team to enum';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'quality_team already exists in enum or error occurred';
        END;
        
        BEGIN
            ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'manager';
            RAISE NOTICE 'Added manager to enum';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'manager already exists in enum or error occurred';
        END;
    END IF;
    
    -- Handle role column conversion based on current type
    IF role_column_type = 'text' OR role_column_type = 'character varying' THEN
        -- Remove default constraint if it exists
        BEGIN
            ALTER TABLE profiles ALTER COLUMN role DROP DEFAULT;
            RAISE NOTICE 'Dropped default constraint on role column';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'No default constraint to drop or error occurred';
        END;
        
        -- Convert to enum type
        BEGIN
            ALTER TABLE profiles ALTER COLUMN role TYPE user_role USING role::user_role;
            RAISE NOTICE 'Converted role column to user_role enum';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error converting role column: %', SQLERRM;
        END;
        
        -- Set new default
        BEGIN
            ALTER TABLE profiles ALTER COLUMN role SET DEFAULT 'client'::user_role;
            RAISE NOTICE 'Set default value for role column';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error setting default: %', SQLERRM;
        END;
    ELSE
        RAISE NOTICE 'Role column is already enum type or unexpected type: %', role_column_type;
    END IF;
END $$;

-- 2. ADD NEW COLUMNS TO EXISTING TABLES

-- Add quality and manager columns to projects table
ALTER TABLE projects ADD COLUMN IF NOT EXISTS quality_status VARCHAR(50) DEFAULT 'pending';
ALTER TABLE projects ADD COLUMN IF NOT EXISTS assigned_manager_id UUID;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS requires_quality_review BOOLEAN DEFAULT TRUE;

-- Add constraint for quality_status if it doesn't exist
DO $$
BEGIN
    ALTER TABLE projects ADD CONSTRAINT check_quality_status 
    CHECK (quality_status IN ('pending', 'in_review', 'approved', 'rejected', 'revision_needed'));
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Add foreign key constraint for assigned_manager_id if it doesn't exist
DO $$
BEGIN
    ALTER TABLE projects ADD CONSTRAINT fk_projects_assigned_manager 
    FOREIGN KEY (assigned_manager_id) REFERENCES profiles(id);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Add manager approval to project milestones
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS manager_approved_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS manager_approved_by UUID;

-- Add foreign key constraint for manager_approved_by if it doesn't exist
DO $$
BEGIN
    ALTER TABLE project_milestones ADD CONSTRAINT fk_milestones_manager_approved_by 
    FOREIGN KEY (manager_approved_by) REFERENCES profiles(id);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- 3. CREATE NEW TABLES

-- Quality Standards Table
CREATE TABLE IF NOT EXISTS quality_standards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    standard_name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    criteria JSONB NOT NULL,
    is_mandatory BOOLEAN DEFAULT TRUE,
    weight INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES profiles(id)
);

-- Quality Reviews Table
CREATE TABLE IF NOT EXISTS quality_reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    submission_id UUID,
    reviewer_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    review_type VARCHAR(50) DEFAULT 'submission',
    status VARCHAR(50) DEFAULT 'pending',
    overall_score INTEGER,
    standards_checked JSONB,
    feedback TEXT,
    revision_notes TEXT,
    revision_count INTEGER DEFAULT 0,
    time_spent_minutes INTEGER,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_review_type CHECK (review_type IN ('proposal', 'submission', 'revision', 'final')),
    CONSTRAINT check_review_status CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'needs_revision')),
    CONSTRAINT check_overall_score CHECK (overall_score >= 1 AND overall_score <= 5)
);

-- Quality Feedback Table
CREATE TABLE IF NOT EXISTS quality_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID REFERENCES quality_reviews(id) ON DELETE CASCADE,
    standard_id UUID REFERENCES quality_standards(id),
    passed BOOLEAN NOT NULL,
    score INTEGER,
    comments TEXT,
    suggestions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_feedback_score CHECK (score >= 1 AND score <= 5)
);

-- Project Assignments Table
CREATE TABLE IF NOT EXISTS project_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active',
    priority VARCHAR(50) DEFAULT 'normal',
    notes TEXT,
    created_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_assignment_status CHECK (status IN ('active', 'completed', 'transferred')),
    CONSTRAINT check_assignment_priority CHECK (priority IN ('low', 'normal', 'high', 'urgent'))
);

-- Manager Activities Table
CREATE TABLE IF NOT EXISTS manager_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    manager_id UUID REFERENCES profiles(id),
    project_id UUID REFERENCES projects(id),
    activity_type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    participants JSONB,
    outcome VARCHAR(100),
    time_spent_minutes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Negotiation Sessions Table
CREATE TABLE IF NOT EXISTS negotiation_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES profiles(id),
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    session_type VARCHAR(50) DEFAULT 'pricing',
    status VARCHAR(50) DEFAULT 'active',
    initial_terms JSONB,
    final_terms JSONB,
    manager_notes TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_session_type CHECK (session_type IN ('pricing', 'timeline', 'scope', 'terms')),
    CONSTRAINT check_session_status CHECK (status IN ('active', 'completed', 'cancelled'))
);

-- Escrow Management Table
CREATE TABLE IF NOT EXISTS escrow_releases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    milestone_id UUID REFERENCES project_milestones(id),
    manager_id UUID REFERENCES profiles(id),
    amount DECIMAL(10,2) NOT NULL,
    release_type VARCHAR(50) DEFAULT 'milestone',
    status VARCHAR(50) DEFAULT 'pending',
    manager_approval_at TIMESTAMP WITH TIME ZONE,
    admin_approval_at TIMESTAMP WITH TIME ZONE,
    released_at TIMESTAMP WITH TIME ZONE,
    manager_notes TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_release_type CHECK (release_type IN ('milestone', 'partial', 'final', 'dispute')),
    CONSTRAINT check_release_status CHECK (status IN ('pending', 'approved', 'released', 'disputed', 'cancelled'))
);

-- Client Satisfaction Tracking
CREATE TABLE IF NOT EXISTS client_satisfaction (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    manager_id UUID REFERENCES profiles(id),
    overall_rating INTEGER,
    communication_rating INTEGER,
    quality_rating INTEGER,
    timeline_rating INTEGER,
    value_rating INTEGER,
    feedback_text TEXT,
    would_recommend BOOLEAN,
    collected_by UUID REFERENCES profiles(id),
    collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_overall_rating CHECK (overall_rating >= 1 AND overall_rating <= 5),
    CONSTRAINT check_communication_rating CHECK (communication_rating >= 1 AND communication_rating <= 5),
    CONSTRAINT check_quality_rating CHECK (quality_rating >= 1 AND quality_rating <= 5),
    CONSTRAINT check_timeline_rating CHECK (timeline_rating >= 1 AND timeline_rating <= 5),
    CONSTRAINT check_value_rating CHECK (value_rating >= 1 AND value_rating <= 5)
);

-- Workflow Notifications Table
CREATE TABLE IF NOT EXISTS workflow_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    recipient_id UUID REFERENCES profiles(id),
    sender_id UUID REFERENCES profiles(id),
    notification_type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_url TEXT,
    priority VARCHAR(50) DEFAULT 'normal',
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_notification_priority CHECK (priority IN ('low', 'normal', 'high', 'urgent'))
);

-- 4. CREATE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_quality_reviews_project_id ON quality_reviews(project_id);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_reviewer_id ON quality_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_status ON quality_reviews(status);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_created_at ON quality_reviews(created_at);

CREATE INDEX IF NOT EXISTS idx_project_assignments_manager_id ON project_assignments(manager_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON project_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_status ON project_assignments(status);

CREATE INDEX IF NOT EXISTS idx_manager_activities_manager_id ON manager_activities(manager_id);
CREATE INDEX IF NOT EXISTS idx_manager_activities_project_id ON manager_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_manager_activities_activity_type ON manager_activities(activity_type);

CREATE INDEX IF NOT EXISTS idx_escrow_releases_project_id ON escrow_releases(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_manager_id ON escrow_releases(manager_id);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_status ON escrow_releases(status);

CREATE INDEX IF NOT EXISTS idx_workflow_notifications_recipient_id ON workflow_notifications(recipient_id);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_read_at ON workflow_notifications(read_at);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_created_at ON workflow_notifications(created_at);

CREATE INDEX IF NOT EXISTS idx_projects_quality_status ON projects(quality_status);
CREATE INDEX IF NOT EXISTS idx_projects_assigned_manager_id ON projects(assigned_manager_id);

-- 5. ENABLE ROW LEVEL SECURITY
ALTER TABLE quality_standards ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE manager_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE negotiation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_satisfaction ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_notifications ENABLE ROW LEVEL SECURITY;

-- 6. CREATE ROW LEVEL SECURITY POLICIES

-- Quality Standards Policies
DROP POLICY IF EXISTS "Admin and Quality Team can manage quality standards" ON quality_standards;
CREATE POLICY "Admin and Quality Team can manage quality standards" ON quality_standards
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team')
        )
    );

DROP POLICY IF EXISTS "All roles can view quality standards" ON quality_standards;
CREATE POLICY "All roles can view quality standards" ON quality_standards
    FOR SELECT USING (TRUE);

-- Quality Reviews Policies
DROP POLICY IF EXISTS "Quality Team can manage quality reviews" ON quality_reviews;
CREATE POLICY "Quality Team can manage quality reviews" ON quality_reviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team')
        )
    );

DROP POLICY IF EXISTS "Designers can view their quality reviews" ON quality_reviews;
CREATE POLICY "Designers can view their quality reviews" ON quality_reviews
    FOR SELECT USING (
        designer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team', 'manager')
        )
    );

-- Quality Feedback Policies
DROP POLICY IF EXISTS "Quality Team can manage feedback" ON quality_feedback;
CREATE POLICY "Quality Team can manage feedback" ON quality_feedback
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team')
        )
    );

DROP POLICY IF EXISTS "Designers can view their feedback" ON quality_feedback;
CREATE POLICY "Designers can view their feedback" ON quality_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM quality_reviews qr
            WHERE qr.id = quality_feedback.review_id
            AND qr.designer_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team', 'manager')
        )
    );

-- Project Assignments Policies
DROP POLICY IF EXISTS "Admin can manage project assignments" ON project_assignments;
CREATE POLICY "Admin can manage project assignments" ON project_assignments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

DROP POLICY IF EXISTS "Managers can view their assignments" ON project_assignments;
CREATE POLICY "Managers can view their assignments" ON project_assignments
    FOR SELECT USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Manager Activities Policies
DROP POLICY IF EXISTS "Managers can manage their activities" ON manager_activities;
CREATE POLICY "Managers can manage their activities" ON manager_activities
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Negotiation Sessions Policies
DROP POLICY IF EXISTS "Participants can view negotiation sessions" ON negotiation_sessions;
CREATE POLICY "Participants can view negotiation sessions" ON negotiation_sessions
    FOR SELECT USING (
        manager_id = auth.uid() OR
        client_id = auth.uid() OR
        designer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

DROP POLICY IF EXISTS "Managers can manage negotiation sessions" ON negotiation_sessions;
CREATE POLICY "Managers can manage negotiation sessions" ON negotiation_sessions
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Escrow Releases Policies
DROP POLICY IF EXISTS "Managers and Admin can manage escrow releases" ON escrow_releases;
CREATE POLICY "Managers and Admin can manage escrow releases" ON escrow_releases
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

DROP POLICY IF EXISTS "Project participants can view escrow releases" ON escrow_releases;
CREATE POLICY "Project participants can view escrow releases" ON escrow_releases
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = escrow_releases.project_id
            AND (p.client_id = auth.uid() OR p.designer_id = auth.uid())
        ) OR
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Client Satisfaction Policies
DROP POLICY IF EXISTS "Managers and Admin can manage satisfaction" ON client_satisfaction;
CREATE POLICY "Managers and Admin can manage satisfaction" ON client_satisfaction
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

DROP POLICY IF EXISTS "Project participants can view satisfaction" ON client_satisfaction;
CREATE POLICY "Project participants can view satisfaction" ON client_satisfaction
    FOR SELECT USING (
        client_id = auth.uid() OR
        designer_id = auth.uid() OR
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Workflow Notifications Policies
DROP POLICY IF EXISTS "Users can view their notifications" ON workflow_notifications;
CREATE POLICY "Users can view their notifications" ON workflow_notifications
    FOR SELECT USING (recipient_id = auth.uid());

DROP POLICY IF EXISTS "Users can update their notifications" ON workflow_notifications;
CREATE POLICY "Users can update their notifications" ON workflow_notifications
    FOR UPDATE USING (recipient_id = auth.uid());

DROP POLICY IF EXISTS "System can create notifications" ON workflow_notifications;
CREATE POLICY "System can create notifications" ON workflow_notifications
    FOR INSERT WITH CHECK (TRUE);

-- 7. INSERT COMPREHENSIVE QUALITY STANDARDS DATA
INSERT INTO quality_standards (category, standard_name, description, criteria, is_mandatory, weight) VALUES
-- DESIGN FUNDAMENTALS
('design', 'Visual Hierarchy', 'Clear visual hierarchy with proper emphasis and flow',
 '["Clear focal points established", "Proper use of size, color, and contrast", "Logical reading flow", "Important elements stand out", "Secondary elements properly subdued"]',
 true, 5),

('design', 'Color Theory', 'Appropriate color usage and harmony',
 '["Colors align with brand guidelines", "Proper contrast ratios (WCAG compliant)", "Harmonious color palette", "Colors support the message", "Accessibility considerations met"]',
 true, 4),

('design', 'Typography', 'Professional typography choices and implementation',
 '["Readable font choices", "Proper font hierarchy", "Consistent spacing", "Appropriate font sizes", "Good line height and letter spacing"]',
 true, 4),

('design', 'Layout & Composition', 'Well-structured layout with proper spacing',
 '["Balanced composition", "Proper use of white space", "Aligned elements", "Consistent margins and padding", "Grid system followed"]',
 true, 5),

('design', 'Brand Consistency', 'Adherence to brand guidelines and identity',
 '["Logo usage correct", "Brand colors used properly", "Consistent with brand voice", "Style guide followed", "Brand elements properly placed"]',
 true, 5),

-- TECHNICAL QUALITY
('technical', 'File Quality', 'Technical specifications and file integrity',
 '["Correct file formats provided", "Appropriate resolution/DPI", "Clean file organization", "Proper naming conventions", "No corrupted files"]',
 true, 5),

('technical', 'Print Readiness', 'Print-specific technical requirements',
 '["CMYK color mode for print", "Proper bleed and margins", "High resolution (300 DPI minimum)", "Embedded fonts", "Print-safe colors used"]',
 true, 4),

('technical', 'Web Optimization', 'Web-specific technical requirements',
 '["RGB color mode for web", "Optimized file sizes", "Responsive design considerations", "Web-safe formats", "Proper compression applied"]',
 true, 4),

-- CONTENT QUALITY
('content', 'Spelling & Grammar', 'Text accuracy and professionalism',
 '["No spelling errors", "Proper grammar usage", "Consistent terminology", "Professional tone", "Accurate information"]',
 true, 3),

('content', 'Content Hierarchy', 'Logical content organization and flow',
 '["Clear information hierarchy", "Logical content flow", "Proper headings structure", "Scannable content", "Key messages prominent"]',
 true, 4),

-- USER EXPERIENCE
('ux', 'Usability', 'User-friendly design and navigation',
 '["Intuitive navigation", "Clear call-to-actions", "User-friendly interface", "Accessibility considerations", "Mobile-friendly design"]',
 true, 4),

('ux', 'Functionality', 'All interactive elements work properly',
 '["Links work correctly", "Forms function properly", "Interactive elements responsive", "No broken functionality", "Cross-browser compatibility"]',
 true, 5),

-- DELIVERABLE COMPLETENESS
('deliverable', 'Completeness', 'All required deliverables provided',
 '["All requested items delivered", "Proper file formats included", "Source files provided when required", "Documentation included", "Revision history maintained"]',
 true, 5),

('deliverable', 'Organization', 'Professional file organization and delivery',
 '["Files properly organized", "Clear folder structure", "Descriptive file names", "Version control maintained", "Easy to locate assets"]',
 true, 3)
ON CONFLICT DO NOTHING;

-- 8. UPDATE EXISTING DATA
-- Update existing projects to have quality status
UPDATE projects SET quality_status = 'pending' WHERE quality_status IS NULL;

-- 9. VERIFICATION
SELECT 'Migration completed successfully!' as status;
SELECT 'New tables created:' as info;
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
    'quality_standards', 'quality_reviews', 'quality_feedback',
    'project_assignments', 'manager_activities', 'negotiation_sessions',
    'escrow_releases', 'client_satisfaction', 'workflow_notifications'
)
ORDER BY table_name;
