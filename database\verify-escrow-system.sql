-- =====================================================
-- ESCROW SYSTEM VERIFICATION SCRIPT
-- Run this to verify everything is working correctly
-- =====================================================

-- 1. CHECK ALL ESCROW TABLES EXIST
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY['escrow_accounts', 'escrow_holds', 'escrow_releases', 'escrow_disputes', 'escrow_activities'];
    missing_tables TEXT[] := ARRAY[]::TEXT[];
    table_name TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 CHECKING ESCROW TABLES...';
    RAISE NOTICE '';
    
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables 
        WHERE table_name = table_name;
        
        IF table_count = 1 THEN
            RAISE NOTICE '✅ Table % exists', table_name;
        ELSE
            RAISE NOTICE '❌ Table % missing', table_name;
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) IS NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 All escrow tables exist!';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️ Missing tables: %', array_to_string(missing_tables, ', ');
    END IF;
END $$;

-- 2. CHECK ALL FUNCTIONS EXIST
-- =====================================================

DO $$
DECLARE
    function_count INTEGER;
    expected_functions TEXT[] := ARRAY[
        'increment_reviewer_workload',
        'decrement_reviewer_workload', 
        'get_escrow_account_summary',
        'can_process_escrow_release',
        'get_manager_escrow_dashboard',
        'get_quality_team_workload',
        'get_sla_compliance_stats'
    ];
    missing_functions TEXT[] := ARRAY[]::TEXT[];
    function_name TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 CHECKING DATABASE FUNCTIONS...';
    RAISE NOTICE '';
    
    FOREACH function_name IN ARRAY expected_functions
    LOOP
        SELECT COUNT(*) INTO function_count
        FROM information_schema.routines 
        WHERE routine_name = function_name;
        
        IF function_count >= 1 THEN
            RAISE NOTICE '✅ Function % exists', function_name;
        ELSE
            RAISE NOTICE '❌ Function % missing', function_name;
            missing_functions := array_append(missing_functions, function_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_functions, 1) IS NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 All functions exist!';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️ Missing functions: %', array_to_string(missing_functions, ', ');
    END IF;
END $$;

-- 3. CHECK FOREIGN KEY CONSTRAINTS
-- =====================================================

DO $$
DECLARE
    constraint_count INTEGER;
    expected_constraints TEXT[] := ARRAY[
        'fk_milestones_escrow_hold',
        'fk_escrow_holds_milestone',
        'fk_escrow_releases_milestone',
        'fk_escrow_releases_payout',
        'fk_payouts_escrow_release'
    ];
    missing_constraints TEXT[] := ARRAY[]::TEXT[];
    constraint_name TEXT;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 CHECKING FOREIGN KEY CONSTRAINTS...';
    RAISE NOTICE '';
    
    FOREACH constraint_name IN ARRAY expected_constraints
    LOOP
        SELECT COUNT(*) INTO constraint_count
        FROM information_schema.table_constraints 
        WHERE constraint_name = constraint_name AND constraint_type = 'FOREIGN KEY';
        
        IF constraint_count >= 1 THEN
            RAISE NOTICE '✅ Constraint % exists', constraint_name;
        ELSE
            RAISE NOTICE '❌ Constraint % missing', constraint_name;
            missing_constraints := array_append(missing_constraints, constraint_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_constraints, 1) IS NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 All foreign key constraints exist!';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️ Missing constraints: %', array_to_string(missing_constraints, ', ');
    END IF;
END $$;

-- 4. CHECK ROW LEVEL SECURITY
-- =====================================================

DO $$
DECLARE
    rls_enabled BOOLEAN;
    table_name TEXT;
    rls_tables TEXT[] := ARRAY['escrow_accounts', 'escrow_holds', 'escrow_releases', 'escrow_disputes', 'escrow_activities'];
    rls_disabled TEXT[] := ARRAY[]::TEXT[];
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 CHECKING ROW LEVEL SECURITY...';
    RAISE NOTICE '';
    
    FOREACH table_name IN ARRAY rls_tables
    LOOP
        SELECT relrowsecurity INTO rls_enabled
        FROM pg_class 
        WHERE relname = table_name;
        
        IF rls_enabled THEN
            RAISE NOTICE '✅ RLS enabled on %', table_name;
        ELSE
            RAISE NOTICE '❌ RLS disabled on %', table_name;
            rls_disabled := array_append(rls_disabled, table_name);
        END IF;
    END LOOP;
    
    IF array_length(rls_disabled, 1) IS NULL THEN
        RAISE NOTICE '';
        RAISE NOTICE '🎉 RLS enabled on all escrow tables!';
    ELSE
        RAISE NOTICE '';
        RAISE NOTICE '⚠️ RLS disabled on: %', array_to_string(rls_disabled, ', ');
    END IF;
END $$;

-- 5. TEST BASIC FUNCTION CALLS
-- =====================================================

DO $$
DECLARE
    test_result RECORD;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 TESTING FUNCTION CALLS...';
    RAISE NOTICE '';
    
    -- Test quality team workload function
    BEGIN
        SELECT * INTO test_result FROM get_quality_team_workload() LIMIT 1;
        RAISE NOTICE '✅ get_quality_team_workload() works';
    EXCEPTION
        WHEN others THEN
            RAISE NOTICE '❌ get_quality_team_workload() failed: %', SQLERRM;
    END;
    
    -- Test SLA compliance function
    BEGIN
        SELECT * INTO test_result FROM get_sla_compliance_stats() LIMIT 1;
        RAISE NOTICE '✅ get_sla_compliance_stats() works';
    EXCEPTION
        WHEN others THEN
            RAISE NOTICE '❌ get_sla_compliance_stats() failed: %', SQLERRM;
    END;
    
    -- Test escrow release check function
    BEGIN
        SELECT can_process_escrow_release('00000000-0000-0000-0000-000000000000'::UUID) INTO test_result;
        RAISE NOTICE '✅ can_process_escrow_release() works';
    EXCEPTION
        WHEN others THEN
            RAISE NOTICE '❌ can_process_escrow_release() failed: %', SQLERRM;
    END;
    
END $$;

-- 6. CHECK COLUMN ADDITIONS TO EXISTING TABLES
-- =====================================================

DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 CHECKING COLUMN ADDITIONS...';
    RAISE NOTICE '';
    
    -- Check escrow_status in transactions
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'escrow_status'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE '✅ escrow_status column exists in transactions table';
    ELSE
        RAISE NOTICE '❌ escrow_status column missing from transactions table';
    END IF;
    
    -- Check escrow_hold_id in project_milestones
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'project_milestones' AND column_name = 'escrow_hold_id'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE '✅ escrow_hold_id column exists in project_milestones table';
    ELSE
        RAISE NOTICE '❌ escrow_hold_id column missing from project_milestones table';
    END IF;
    
    -- Check escrow_release_id in payouts
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'payouts' AND column_name = 'escrow_release_id'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE '✅ escrow_release_id column exists in payouts table';
    ELSE
        RAISE NOTICE '❌ escrow_release_id column missing from payouts table';
    END IF;
    
END $$;

-- 7. FINAL SUMMARY
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE '🎉 ESCROW SYSTEM VERIFICATION COMPLETE!';
    RAISE NOTICE '================================================';
    RAISE NOTICE '';
    RAISE NOTICE 'If you see mostly ✅ above, your escrow system is ready!';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Test the manager dashboard at /manager/dashboard';
    RAISE NOTICE '2. Make a test payment to verify escrow hold creation';
    RAISE NOTICE '3. Test the approval workflow';
    RAISE NOTICE '4. Check the testing guide: ESCROW_SYSTEM_TESTING_GUIDE.md';
    RAISE NOTICE '';
    RAISE NOTICE 'Happy testing! 🚀';
    RAISE NOTICE '';
END $$;
