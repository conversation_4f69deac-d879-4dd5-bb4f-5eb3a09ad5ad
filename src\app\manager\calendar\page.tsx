"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/Button";
import {
  Calendar,
  Clock,
  Plus,
  ChevronLeft,
  ChevronRight,
  Users,
  MessageSquare,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Filter,
  Eye
} from "lucide-react";

interface CalendarEvent {
  id: string;
  title: string;
  description: string;
  event_type: string;
  start_date: string;
  end_date: string;
  project_id?: string;
  participants: string[];
  status: string;
  project?: {
    title: string;
  };
}

export default function ManagerCalendarPage() {
  const { user, profile } = useAuth();
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'month' | 'week' | 'day'>('month');
  const [filter, setFilter] = useState<string>('all');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchEvents();
    }
  }, [user, profile, currentDate]);

  const fetchEvents = async () => {
    try {
      // In a real app, this would fetch actual calendar events
      // For now, we'll create mock data based on project activities
      const mockEvents: CalendarEvent[] = [
        {
          id: '1',
          title: 'Project Kickoff Meeting',
          description: 'Initial meeting with client and designer',
          event_type: 'meeting',
          start_date: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
          end_date: new Date(Date.now() + 86400000 + 3600000).toISOString(), // Tomorrow + 1 hour
          participants: ['client', 'designer'],
          status: 'scheduled',
          project: { title: 'Brand Identity Design' }
        },
        {
          id: '2',
          title: 'Quality Review Deadline',
          description: 'Final quality review must be completed',
          event_type: 'deadline',
          start_date: new Date(Date.now() + 172800000).toISOString(), // Day after tomorrow
          end_date: new Date(Date.now() + 172800000).toISOString(),
          participants: ['quality_team'],
          status: 'pending',
          project: { title: 'Website Redesign' }
        },
        {
          id: '3',
          title: 'Client Feedback Session',
          description: 'Collect client satisfaction feedback',
          event_type: 'feedback',
          start_date: new Date(Date.now() + 259200000).toISOString(), // 3 days from now
          end_date: new Date(Date.now() + 259200000 + 1800000).toISOString(), // 3 days + 30 min
          participants: ['client'],
          status: 'scheduled',
          project: { title: 'Logo Design Project' }
        },
        {
          id: '4',
          title: 'Escrow Release Review',
          description: 'Review and approve milestone payment',
          event_type: 'escrow',
          start_date: new Date(Date.now() + 345600000).toISOString(), // 4 days from now
          end_date: new Date(Date.now() + 345600000).toISOString(),
          participants: ['admin'],
          status: 'pending',
          project: { title: 'Marketing Materials' }
        },
        {
          id: '5',
          title: 'Negotiation Session',
          description: 'Facilitate pricing discussion',
          event_type: 'negotiation',
          start_date: new Date(Date.now() + 432000000).toISOString(), // 5 days from now
          end_date: new Date(Date.now() + 432000000 + 3600000).toISOString(), // 5 days + 1 hour
          participants: ['client', 'designer'],
          status: 'scheduled',
          project: { title: 'E-commerce Design' }
        }
      ];

      setEvents(mockEvents);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'meeting':
        return <Users className="h-4 w-4 text-blue-500" />;
      case 'deadline':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'feedback':
        return <MessageSquare className="h-4 w-4 text-purple-500" />;
      case 'escrow':
        return <DollarSign className="h-4 w-4 text-green-500" />;
      case 'negotiation':
        return <MessageSquare className="h-4 w-4 text-orange-500" />;
      default:
        return <Calendar className="h-4 w-4 text-gray-500" />;
    }
  };

  const getEventTypeBadge = (type: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (type) {
      case 'meeting':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'deadline':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'feedback':
        return `${baseClasses} bg-purple-100 text-purple-800`;
      case 'escrow':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'negotiation':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (status) {
      case 'scheduled':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredEvents = events.filter(event => {
    if (filter === 'all') return true;
    return event.event_type === filter;
  });

  const upcomingEvents = filteredEvents
    .filter(event => new Date(event.start_date) > new Date())
    .sort((a, b) => new Date(a.start_date).getTime() - new Date(b.start_date).getTime())
    .slice(0, 10);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Calendar</h1>
          <p className="text-gray-600 mt-2">Manage project schedules and deadlines</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => window.location.href = '/manager/calendar/new'}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Event
          </Button>
          <Button
            onClick={fetchEvents}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Calendar Controls */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date(currentDate.setMonth(currentDate.getMonth() - 1)))}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-xl font-semibold text-gray-900 min-w-[200px] text-center">
                {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
              </h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date(currentDate.setMonth(currentDate.getMonth() + 1)))}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date())}
            >
              Today
            </Button>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              >
                <option value="all">All Events</option>
                <option value="meeting">Meetings</option>
                <option value="deadline">Deadlines</option>
                <option value="feedback">Feedback</option>
                <option value="escrow">Escrow</option>
                <option value="negotiation">Negotiations</option>
              </select>
            </div>

            <div className="flex border border-gray-300 rounded-lg">
              {['month', 'week', 'day'].map((viewType) => (
                <button
                  key={viewType}
                  onClick={() => setView(viewType as any)}
                  className={`px-3 py-2 text-sm font-medium capitalize ${
                    view === viewType
                      ? 'bg-brown-600 text-white'
                      : 'text-gray-700 hover:bg-gray-50'
                  } ${viewType === 'month' ? 'rounded-l-md' : viewType === 'day' ? 'rounded-r-md' : ''}`}
                >
                  {viewType}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Calendar View */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Calendar Grid (Simplified) */}
        <div className="lg:col-span-2 bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Calendar View</h3>
          <div className="text-center py-12 text-gray-500">
            <Calendar className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">Calendar Grid View</p>
            <p className="text-sm">Full calendar integration would be implemented here</p>
            <p className="text-xs mt-2">Consider integrating with Google Calendar, Outlook, or a calendar library</p>
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Upcoming Events</h3>
            <p className="text-gray-600 text-sm mt-1">Next 10 events</p>
          </div>

          <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
            {upcomingEvents.length === 0 ? (
              <div className="p-6 text-center">
                <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 text-sm">No upcoming events</p>
              </div>
            ) : (
              upcomingEvents.map((event) => (
                <div key={event.id} className="p-4 hover:bg-gray-50 transition-colors duration-200">
                  <div className="flex items-start gap-3">
                    {getEventTypeIcon(event.event_type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900 text-sm truncate">{event.title}</h4>
                        <span className={getStatusBadge(event.status)}>
                          {event.status}
                        </span>
                      </div>
                      
                      <p className="text-xs text-gray-600 mb-2">{event.description}</p>
                      
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>
                          {new Date(event.start_date).toLocaleDateString()} at{' '}
                          {new Date(event.start_date).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                      
                      {event.project && (
                        <p className="text-xs text-gray-500 mt-1">
                          Project: {event.project.title}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-2 mt-2">
                        <span className={getEventTypeBadge(event.event_type)}>
                          {event.event_type}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Event Types Legend */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">Event Types</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-blue-500" />
            <span className="text-sm text-blue-800">Meetings</span>
          </div>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-blue-800">Deadlines</span>
          </div>
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-purple-500" />
            <span className="text-sm text-blue-800">Feedback Sessions</span>
          </div>
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-green-500" />
            <span className="text-sm text-blue-800">Escrow Reviews</span>
          </div>
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-orange-500" />
            <span className="text-sm text-blue-800">Negotiations</span>
          </div>
        </div>
      </div>
    </div>
  );
}
