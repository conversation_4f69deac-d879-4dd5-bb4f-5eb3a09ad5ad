-- =====================================================
-- PERFORMANCE OPTIMIZATION DATABASE SCHEMA
-- Advanced indexing, caching, and query optimization
-- =====================================================

-- 1. ADVANCED INDEXING STRATEGY
-- =====================================================

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_projects_composite_status_priority 
ON projects(status, priority, created_at DESC) 
WHERE status IN ('active', 'pending', 'in_progress');

CREATE INDEX IF NOT EXISTS idx_projects_composite_manager_status 
ON projects(assigned_manager_id, status, deadline) 
WHERE assigned_manager_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_projects_composite_client_active 
ON projects(client_id, status, created_at DESC) 
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_projects_composite_designer_active 
ON projects(designer_id, status, updated_at DESC) 
WHERE status IN ('active', 'in_progress');

-- Escrow performance indexes
CREATE INDEX IF NOT EXISTS idx_escrow_holds_composite_status_amount 
ON escrow_holds(status, project_id, created_at DESC) 
WHERE status IN ('active', 'pending_release');

CREATE INDEX IF NOT EXISTS idx_escrow_releases_composite_approval 
ON escrow_releases(manager_approval_status, quality_approval_status, created_at DESC) 
WHERE status = 'pending';

-- Activity and audit trail indexes (without time-based predicates)
CREATE INDEX IF NOT EXISTS idx_manager_activities_composite_recent
ON manager_activities(manager_id, created_at DESC, activity_type);

-- Negotiation sessions indexes (using actual table that exists)
CREATE INDEX IF NOT EXISTS idx_negotiation_sessions_composite_recent
ON negotiation_sessions(project_id, created_at DESC, status);

-- Quality review performance indexes (check if table and columns exist)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quality_reviews_new') THEN
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'quality_reviews_new' AND column_name = 'assigned_reviewer_id') THEN
            CREATE INDEX IF NOT EXISTS idx_quality_reviews_composite_pending
            ON quality_reviews_new(status, assigned_reviewer_id, created_at)
            WHERE status IN ('pending', 'in_review');
        ELSE
            -- Create index without assigned_reviewer_id if column doesn't exist
            CREATE INDEX IF NOT EXISTS idx_quality_reviews_composite_pending
            ON quality_reviews_new(status, created_at)
            WHERE status IN ('pending', 'in_review');
        END IF;
    END IF;
END $$;

-- 2. QUERY PERFORMANCE CACHE TABLES
-- =====================================================

-- Project metrics cache for dashboard performance
CREATE TABLE IF NOT EXISTS project_metrics_cache (
    project_id UUID PRIMARY KEY REFERENCES projects(id) ON DELETE CASCADE,
    
    -- Progress metrics
    total_milestones INTEGER DEFAULT 0,
    completed_milestones INTEGER DEFAULT 0,
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Quality metrics
    quality_reviews_count INTEGER DEFAULT 0,
    pending_reviews_count INTEGER DEFAULT 0,
    average_quality_score DECIMAL(3,2) DEFAULT 0,
    
    -- Communication metrics
    total_messages INTEGER DEFAULT 0,
    unread_messages INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    
    -- Financial metrics
    total_payments DECIMAL(10,2) DEFAULT 0,
    escrow_held DECIMAL(10,2) DEFAULT 0,
    escrow_released DECIMAL(10,2) DEFAULT 0,
    
    -- Negotiation metrics
    active_negotiations_count INTEGER DEFAULT 0,
    completed_negotiations_count INTEGER DEFAULT 0,
    
    -- Timeline metrics
    days_since_start INTEGER DEFAULT 0,
    days_until_deadline INTEGER DEFAULT 0,
    is_overdue BOOLEAN DEFAULT FALSE,
    
    -- Cache metadata
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cache_version INTEGER DEFAULT 1
);

-- Create indexes for project_metrics_cache table
CREATE INDEX IF NOT EXISTS idx_project_metrics_cache_updated ON project_metrics_cache (last_updated);
CREATE INDEX IF NOT EXISTS idx_project_metrics_cache_overdue ON project_metrics_cache (is_overdue, days_until_deadline);
CREATE INDEX IF NOT EXISTS idx_project_metrics_cache_progress ON project_metrics_cache (progress_percentage, completed_milestones);

-- Manager dashboard cache for instant loading
CREATE TABLE IF NOT EXISTS manager_dashboard_cache (
    manager_id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Project statistics
    total_projects INTEGER DEFAULT 0,
    active_projects INTEGER DEFAULT 0,
    completed_projects INTEGER DEFAULT 0,
    overdue_projects INTEGER DEFAULT 0,
    high_priority_projects INTEGER DEFAULT 0,
    
    -- Performance metrics
    average_project_progress DECIMAL(5,2) DEFAULT 0,
    average_client_satisfaction DECIMAL(3,2) DEFAULT 0,
    on_time_delivery_rate DECIMAL(5,2) DEFAULT 0,
    budget_variance_average DECIMAL(5,2) DEFAULT 0,
    
    -- Activity metrics
    activities_this_week INTEGER DEFAULT 0,
    activities_this_month INTEGER DEFAULT 0,
    average_response_time_hours DECIMAL(5,2) DEFAULT 0,
    
    -- Escrow metrics
    total_escrow_held DECIMAL(10,2) DEFAULT 0,
    pending_escrow_approvals INTEGER DEFAULT 0,
    escrow_releases_this_month INTEGER DEFAULT 0,
    
    -- Negotiation metrics
    active_negotiations INTEGER DEFAULT 0,
    successful_negotiations_rate DECIMAL(5,2) DEFAULT 0,
    average_negotiation_duration_days DECIMAL(5,2) DEFAULT 0,
    
    -- Cache metadata
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cache_version INTEGER DEFAULT 1,
    next_refresh_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '1 hour'
);

-- Create indexes for manager_dashboard_cache table
CREATE INDEX IF NOT EXISTS idx_manager_dashboard_cache_updated ON manager_dashboard_cache (last_updated);
CREATE INDEX IF NOT EXISTS idx_manager_dashboard_cache_refresh ON manager_dashboard_cache (next_refresh_at);

-- Client satisfaction cache for quick analytics
CREATE TABLE IF NOT EXISTS satisfaction_metrics_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Scope (project, manager, or global)
    scope_type VARCHAR(20) NOT NULL CHECK (scope_type IN ('project', 'manager', 'global')),
    scope_id UUID, -- project_id or manager_id, null for global
    
    -- Time period
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    -- Satisfaction metrics
    total_responses INTEGER DEFAULT 0,
    overall_average DECIMAL(3,2) DEFAULT 0,
    communication_average DECIMAL(3,2) DEFAULT 0,
    quality_average DECIMAL(3,2) DEFAULT 0,
    timeline_average DECIMAL(3,2) DEFAULT 0,
    value_average DECIMAL(3,2) DEFAULT 0,
    
    -- Recommendation metrics
    recommendation_rate DECIMAL(5,2) DEFAULT 0,
    retention_rate DECIMAL(5,2) DEFAULT 0,
    
    -- Trend analysis
    trend_direction VARCHAR(10) CHECK (trend_direction IN ('up', 'down', 'stable')),
    trend_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Cache metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint for cache entries
    UNIQUE(scope_type, scope_id, period_start, period_end)
);

-- Create indexes for satisfaction_metrics_cache table
CREATE INDEX IF NOT EXISTS idx_satisfaction_cache_scope ON satisfaction_metrics_cache (scope_type, scope_id);
CREATE INDEX IF NOT EXISTS idx_satisfaction_cache_period ON satisfaction_metrics_cache (period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_satisfaction_cache_updated ON satisfaction_metrics_cache (last_updated);

-- 3. MATERIALIZED VIEWS FOR COMPLEX QUERIES
-- =====================================================

-- Project overview materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_project_overview AS
SELECT 
    p.id,
    p.title,
    p.status,
    p.priority,
    p.budget,
    p.deadline,
    p.created_at,
    
    -- Client information
    c.full_name as client_name,
    c.email as client_email,
    c.avatar_url as client_avatar,
    
    -- Designer information
    d.full_name as designer_name,
    d.email as designer_email,
    d.avatar_url as designer_avatar,
    
    -- Manager information
    m.full_name as manager_name,
    m.email as manager_email,
    
    -- Cached metrics
    pmc.progress_percentage,
    pmc.total_milestones,
    pmc.completed_milestones,
    pmc.quality_reviews_count,
    pmc.pending_reviews_count,
    pmc.active_negotiations_count,
    pmc.total_payments,
    pmc.escrow_held,
    pmc.is_overdue,
    pmc.days_until_deadline
    
FROM projects p
LEFT JOIN profiles c ON p.client_id = c.id
LEFT JOIN profiles d ON p.designer_id = d.id
LEFT JOIN profiles m ON p.assigned_manager_id = m.id
LEFT JOIN project_metrics_cache pmc ON p.id = pmc.project_id
WHERE p.status != 'deleted';

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_project_overview_id ON mv_project_overview(id);
CREATE INDEX IF NOT EXISTS idx_mv_project_overview_status ON mv_project_overview(status, priority);
CREATE INDEX IF NOT EXISTS idx_mv_project_overview_manager ON mv_project_overview(manager_name, status);

-- Manager performance materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_manager_performance AS
SELECT 
    m.id as manager_id,
    m.full_name as manager_name,
    m.email as manager_email,
    
    -- Project counts
    COUNT(DISTINCT p.id) as total_projects,
    COUNT(DISTINCT CASE WHEN p.status = 'active' THEN p.id END) as active_projects,
    COUNT(DISTINCT CASE WHEN p.status = 'completed' THEN p.id END) as completed_projects,
    COUNT(DISTINCT CASE WHEN pmc.is_overdue = true THEN p.id END) as overdue_projects,
    
    -- Performance metrics
    AVG(pmc.progress_percentage) as avg_progress,
    AVG(cs.overall_rating) as avg_client_satisfaction,
    
    -- Activity metrics
    COUNT(DISTINCT ma.id) as total_activities,
    AVG(ma.time_spent_minutes) as avg_time_per_activity,
    
    -- Financial metrics
    SUM(pmc.total_payments) as total_revenue_managed,
    SUM(pmc.escrow_held) as total_escrow_held,
    
    -- Last updated
    NOW() as calculated_at
    
FROM profiles m
LEFT JOIN projects p ON m.id = p.assigned_manager_id
LEFT JOIN project_metrics_cache pmc ON p.id = pmc.project_id
LEFT JOIN client_satisfaction cs ON p.id = cs.project_id AND cs.manager_id = m.id
LEFT JOIN manager_activities ma ON m.id = ma.manager_id AND ma.created_at >= NOW() - INTERVAL '30 days'
WHERE m.role = 'manager'
GROUP BY m.id, m.full_name, m.email;

-- Create indexes on manager performance view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_manager_performance_id ON mv_manager_performance(manager_id);
CREATE INDEX IF NOT EXISTS idx_mv_manager_performance_satisfaction ON mv_manager_performance(avg_client_satisfaction DESC);

-- 4. CACHE REFRESH FUNCTIONS
-- =====================================================

-- Function to refresh project metrics cache
CREATE OR REPLACE FUNCTION refresh_project_metrics_cache(target_project_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    project_record RECORD;
BEGIN
    -- Loop through projects (all or specific one)
    FOR project_record IN 
        SELECT id FROM projects 
        WHERE (target_project_id IS NULL OR id = target_project_id)
          AND status != 'deleted'
    LOOP
        -- Insert or update metrics cache
        INSERT INTO project_metrics_cache (
            project_id,
            total_milestones,
            completed_milestones,
            progress_percentage,
            quality_reviews_count,
            pending_reviews_count,
            total_messages,
            active_negotiations_count,
            total_payments,
            escrow_held,
            escrow_released,
            days_since_start,
            days_until_deadline,
            is_overdue,
            last_updated
        )
        SELECT 
            project_record.id,
            COALESCE((SELECT COUNT(*) FROM project_milestones WHERE project_id = project_record.id), 0),
            COALESCE((SELECT COUNT(*) FROM project_milestones WHERE project_id = project_record.id AND status = 'completed'), 0),
            COALESCE((SELECT AVG(CASE WHEN status = 'completed' THEN 100 ELSE 0 END) FROM project_milestones WHERE project_id = project_record.id), 0),
            COALESCE((SELECT COUNT(*) FROM quality_reviews_new WHERE project_id = project_record.id), 0),
            COALESCE((SELECT COUNT(*) FROM quality_reviews_new WHERE project_id = project_record.id AND status IN ('pending', 'in_review')), 0),
            COALESCE((SELECT COUNT(*) FROM conversations WHERE project_id = project_record.id), 0),
            COALESCE((SELECT COUNT(*) FROM negotiation_sessions WHERE project_id = project_record.id AND status IN ('pending', 'in_progress')), 0),
            COALESCE((SELECT SUM(amount) FROM transactions WHERE project_id = project_record.id AND status = 'completed'), 0),
            COALESCE((SELECT SUM(net_amount) FROM escrow_holds WHERE project_id = project_record.id AND status = 'active'), 0),
            COALESCE((SELECT SUM(release_amount) FROM escrow_releases WHERE project_id = project_record.id AND status = 'processed'), 0),
            COALESCE((SELECT EXTRACT(DAY FROM NOW() - created_at) FROM projects WHERE id = project_record.id), 0),
            COALESCE((SELECT EXTRACT(DAY FROM deadline - NOW()) FROM projects WHERE id = project_record.id), 0),
            COALESCE((SELECT deadline < NOW() FROM projects WHERE id = project_record.id), false),
            NOW()
        ON CONFLICT (project_id) 
        DO UPDATE SET
            total_milestones = EXCLUDED.total_milestones,
            completed_milestones = EXCLUDED.completed_milestones,
            progress_percentage = EXCLUDED.progress_percentage,
            quality_reviews_count = EXCLUDED.quality_reviews_count,
            pending_reviews_count = EXCLUDED.pending_reviews_count,
            total_messages = EXCLUDED.total_messages,
            active_negotiations_count = EXCLUDED.active_negotiations_count,
            total_payments = EXCLUDED.total_payments,
            escrow_held = EXCLUDED.escrow_held,
            escrow_released = EXCLUDED.escrow_released,
            days_since_start = EXCLUDED.days_since_start,
            days_until_deadline = EXCLUDED.days_until_deadline,
            is_overdue = EXCLUDED.is_overdue,
            last_updated = NOW(),
            cache_version = project_metrics_cache.cache_version + 1;
            
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- Function to refresh manager dashboard cache
CREATE OR REPLACE FUNCTION refresh_manager_dashboard_cache(target_manager_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    manager_record RECORD;
BEGIN
    FOR manager_record IN 
        SELECT id FROM profiles 
        WHERE role = 'manager' 
          AND (target_manager_id IS NULL OR id = target_manager_id)
    LOOP
        INSERT INTO manager_dashboard_cache (
            manager_id,
            total_projects,
            active_projects,
            completed_projects,
            overdue_projects,
            high_priority_projects,
            average_project_progress,
            activities_this_week,
            activities_this_month,
            total_escrow_held,
            pending_escrow_approvals,
            active_negotiations,
            last_updated,
            next_refresh_at
        )
        SELECT 
            manager_record.id,
            COUNT(DISTINCT p.id),
            COUNT(DISTINCT CASE WHEN p.status = 'active' THEN p.id END),
            COUNT(DISTINCT CASE WHEN p.status = 'completed' THEN p.id END),
            COUNT(DISTINCT CASE WHEN pmc.is_overdue = true THEN p.id END),
            COUNT(DISTINCT CASE WHEN p.priority IN ('high', 'urgent') THEN p.id END),
            AVG(pmc.progress_percentage),
            COUNT(DISTINCT CASE WHEN ma.created_at >= NOW() - INTERVAL '7 days' THEN ma.id END),
            COUNT(DISTINCT CASE WHEN ma.created_at >= NOW() - INTERVAL '30 days' THEN ma.id END),
            SUM(pmc.escrow_held),
            COUNT(DISTINCT CASE WHEN er.manager_approval_status = 'pending' THEN er.id END),
            COUNT(DISTINCT CASE WHEN n.status IN ('pending', 'in_progress') THEN n.id END),
            NOW(),
            NOW() + INTERVAL '1 hour'
        FROM profiles m
        LEFT JOIN projects p ON m.id = p.assigned_manager_id
        LEFT JOIN project_metrics_cache pmc ON p.id = pmc.project_id
        LEFT JOIN manager_activities ma ON m.id = ma.manager_id
        LEFT JOIN escrow_releases er ON m.id = er.manager_id
        LEFT JOIN negotiation_sessions n ON m.id = n.manager_id
        WHERE m.id = manager_record.id
        GROUP BY m.id
        ON CONFLICT (manager_id)
        DO UPDATE SET
            total_projects = EXCLUDED.total_projects,
            active_projects = EXCLUDED.active_projects,
            completed_projects = EXCLUDED.completed_projects,
            overdue_projects = EXCLUDED.overdue_projects,
            high_priority_projects = EXCLUDED.high_priority_projects,
            average_project_progress = EXCLUDED.average_project_progress,
            activities_this_week = EXCLUDED.activities_this_week,
            activities_this_month = EXCLUDED.activities_this_month,
            total_escrow_held = EXCLUDED.total_escrow_held,
            pending_escrow_approvals = EXCLUDED.pending_escrow_approvals,
            active_negotiations = EXCLUDED.active_negotiations,
            last_updated = NOW(),
            next_refresh_at = NOW() + INTERVAL '1 hour',
            cache_version = manager_dashboard_cache.cache_version + 1;
            
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- 5. ENABLE ROW LEVEL SECURITY ON CACHE TABLES
-- =====================================================

ALTER TABLE project_metrics_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE manager_dashboard_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE satisfaction_metrics_cache ENABLE ROW LEVEL SECURITY;

-- 6. SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🚀 PERFORMANCE OPTIMIZATION SCHEMA CREATED!';
    RAISE NOTICE '';
    RAISE NOTICE 'Performance enhancements:';
    RAISE NOTICE '✅ Advanced composite indexes for common queries';
    RAISE NOTICE '✅ Project metrics cache for instant dashboard loading';
    RAISE NOTICE '✅ Manager dashboard cache for real-time stats';
    RAISE NOTICE '✅ Satisfaction metrics cache for analytics';
    RAISE NOTICE '✅ Materialized views for complex queries';
    RAISE NOTICE '✅ Cache refresh functions for data consistency';
    RAISE NOTICE '';
    RAISE NOTICE 'Next: Run performance-optimization-triggers.sql';
    RAISE NOTICE '';
END $$;
