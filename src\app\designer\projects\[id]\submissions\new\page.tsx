"use client";

import { useState, useEffect, useRef } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Upload,
  File,
  X,
  AlertCircle,
  Loader2
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  client_id: string;
};

export default function NewSubmission() {
  const { id } = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [project, setProject] = useState<Project | null>(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (user && id) {
      fetchProjectData();
    }
  }, [user, id]);

  const fetchProjectData = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('id, title, client_id')
        .eq('id', id)
        .eq('designer_id', user?.id)
        .single();

      if (error) throw error;

      if (!data) {
        router.push('/designer/projects');
        return;
      }

      setProject(data);
    } catch (error: Error | unknown) {
      console.error('Error fetching project data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setFiles(prev => [...prev, ...newFiles]);
    }
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {
      setError('Please enter a submission title');
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      // 1. Create submission record
      const { data: submission, error: submissionError } = await supabase
        .from('submissions')
        .insert({
          project_id: id,
          designer_id: user?.id,
          title,
          description,
          status: 'pending',
          revision_requested: false
        })
        .select()
        .single();

      if (submissionError) throw submissionError;

      // 2. Upload files if any
      if (files.length > 0) {
        for (const file of files) {
          const fileExt = file.name.split('.').pop();
          const fileName = `${submission.id}/${Date.now()}.${fileExt}`;
          const filePath = `submissions/${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from('project-files')
            .upload(filePath, file);

          if (uploadError) throw uploadError;

          // 3. Create file record
          const { error: fileRecordError } = await supabase
            .from('submission_files')
            .insert({
              submission_id: submission.id,
              file_path: filePath,
              file_name: file.name,
              file_type: file.type,
              file_size: file.size
            });

          if (fileRecordError) throw fileRecordError;
        }
      }

      // 4. Create notification for client
      await supabase
        .from('notifications')
        .insert({
          user_id: project?.client_id,
          type: 'submission',
          title: 'New Submission',
          content: `Designer has submitted: ${title}`,
          related_id: submission.id,
          read: false
        });

      // 5. Create quality review for submission
      await supabase
        .from('quality_reviews_new')
        .insert({
          project_id: id,
          submission_id: submission.id,
          designer_id: user?.id,
          review_type: 'submission',
          status: 'pending',
          priority: 'normal'
        });

      // 6. Update project status if it was draft
      const { data: projectData } = await supabase
        .from('projects')
        .select('status')
        .eq('id', id)
        .single();

      if (projectData && (projectData.status === 'draft' || projectData.status === 'submitted')) {
        await supabase
          .from('projects')
          .update({
            status: 'in_progress',
            quality_status: 'pending_review'
          })
          .eq('id', id);
      }

      // Navigate to the submission detail page
      router.push(`/designer/projects/${id}/submissions/${submission.id}`);

    } catch (error: Error | unknown) {
      console.error('Error creating submission:', error);
      setError(error instanceof Error ? error.message : 'Failed to create submission');
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="bg-red-50 text-red-500 p-4 rounded-lg flex items-center">
        <AlertCircle className="h-5 w-5 mr-2" />
        <p>Project not found or you don't have access to it.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-8">
        <Link href={`/designer/projects/${id}`}>
          <Button variant="ghost" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">New Submission</h1>
      </div>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <p>{error}</p>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">Submit Work for {project.title}</h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              Submission Title *
            </label>
            <input
              id="title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="e.g., Initial Concept, Revised Design, Final Delivery"
              required
            />
          </div>

          <div className="mb-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={4}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Describe what you're submitting and any specific notes for the client..."
            />
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Upload Files
            </label>

            <div
              className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                multiple
                className="hidden"
              />
              <Upload className="h-10 w-10 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">
                Click to upload or drag and drop files here
              </p>
              <p className="text-xs text-gray-400 mt-1">
                PDF, PNG, JPG, AI, PSD, ZIP (max 50MB each)
              </p>
            </div>

            {files.length > 0 && (
              <div className="mt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Selected Files:</h3>
                <ul className="space-y-2">
                  {files.map((file, index) => (
                    <li key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                      <div className="flex items-center">
                        <File className="h-4 w-4 text-gray-500 mr-2" />
                        <span className="text-sm truncate max-w-md">{file.name}</span>
                        <span className="text-xs text-gray-500 ml-2">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeFile(index)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-4">
            <Link href={`/designer/projects/${id}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={submitting}
              className="flex items-center"
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Submit Work
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
