"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Users,
  Shield,
  Edit,
  Trash2,
  Plus,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  UserCheck,
  Settings,
  Eye,
  Lock
} from "lucide-react";

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  user_count: number;
  is_system_role: boolean;
  created_at: string;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

export default function UserRolesPage() {
  const { user } = useOptimizedAuth();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [newRole, setNewRole] = useState({
    name: "",
    description: "",
    permissions: [] as string[]
  });

  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, []);

  const fetchRoles = async () => {
    try {
      // Since we don't have a roles table, we'll simulate with predefined roles
      const mockRoles: Role[] = [
        {
          id: "admin",
          name: "Administrator",
          description: "Full system access with all permissions",
          permissions: ["all"],
          user_count: 2,
          is_system_role: true,
          created_at: new Date().toISOString()
        },
        {
          id: "designer",
          name: "Designer",
          description: "Can manage projects and communicate with clients",
          permissions: ["view_projects", "edit_projects", "view_messages", "send_messages"],
          user_count: 15,
          is_system_role: true,
          created_at: new Date().toISOString()
        },
        {
          id: "client",
          name: "Client",
          description: "Can create projects and communicate with designers",
          permissions: ["create_projects", "view_own_projects", "view_messages", "send_messages"],
          user_count: 45,
          is_system_role: true,
          created_at: new Date().toISOString()
        },
        {
          id: "quality_team",
          name: "Quality Team",
          description: "Reviews and ensures design quality standards compliance",
          permissions: ["view_quality_reviews", "manage_quality_reviews", "view_quality_standards", "provide_feedback"],
          user_count: 3,
          is_system_role: true,
          created_at: new Date().toISOString()
        },
        {
          id: "manager",
          name: "Manager",
          description: "Oversees projects, coordinates teams, and manages negotiations",
          permissions: ["view_all_projects", "manage_projects", "coordinate_negotiations", "manage_escrow", "collect_feedback"],
          user_count: 2,
          is_system_role: true,
          created_at: new Date().toISOString()
        }
      ];
      
      setRoles(mockRoles);
    } catch (error) {
      console.error('Error fetching roles:', error);
      setError('Failed to load roles');
    } finally {
      setLoading(false);
    }
  };

  const fetchPermissions = async () => {
    try {
      const mockPermissions: Permission[] = [
        { id: "all", name: "All Permissions", description: "Complete system access", category: "System" },
        { id: "view_users", name: "View Users", description: "Can view user profiles", category: "User Management" },
        { id: "edit_users", name: "Edit Users", description: "Can modify user profiles", category: "User Management" },
        { id: "delete_users", name: "Delete Users", description: "Can delete user accounts", category: "User Management" },
        { id: "view_projects", name: "View Projects", description: "Can view project details", category: "Project Management" },
        { id: "edit_projects", name: "Edit Projects", description: "Can modify project details", category: "Project Management" },
        { id: "create_projects", name: "Create Projects", description: "Can create new projects", category: "Project Management" },
        { id: "delete_projects", name: "Delete Projects", description: "Can delete projects", category: "Project Management" },
        { id: "view_own_projects", name: "View Own Projects", description: "Can view only own projects", category: "Project Management" },
        { id: "view_messages", name: "View Messages", description: "Can view messages", category: "Communication" },
        { id: "send_messages", name: "Send Messages", description: "Can send messages", category: "Communication" },
        { id: "view_finances", name: "View Finances", description: "Can view financial data", category: "Financial" },
        { id: "manage_finances", name: "Manage Finances", description: "Can manage payments and fees", category: "Financial" },
        { id: "system_settings", name: "System Settings", description: "Can modify system settings", category: "System" }
      ];
      
      setPermissions(mockPermissions);
    } catch (error) {
      console.error('Error fetching permissions:', error);
    }
  };

  const handleCreateRole = async () => {
    if (!newRole.name.trim()) {
      setError('Role name is required');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      // In a real implementation, this would save to database
      const role: Role = {
        id: Date.now().toString(),
        name: newRole.name,
        description: newRole.description,
        permissions: newRole.permissions,
        user_count: 0,
        is_system_role: false,
        created_at: new Date().toISOString()
      };

      setRoles(prev => [...prev, role]);
      setSuccess('Role created successfully');
      setShowCreateModal(false);
      setNewRole({ name: "", description: "", permissions: [] });
    } catch (error) {
      console.error('Error creating role:', error);
      setError('Failed to create role');
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateRole = async () => {
    if (!editingRole) return;

    setSaving(true);
    setError(null);

    try {
      setRoles(prev => prev.map(role => 
        role.id === editingRole.id ? editingRole : role
      ));
      setSuccess('Role updated successfully');
      setEditingRole(null);
    } catch (error) {
      console.error('Error updating role:', error);
      setError('Failed to update role');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (!role) return;

    if (role.is_system_role) {
      setError('Cannot delete system roles');
      return;
    }

    if (role.user_count > 0) {
      setError('Cannot delete role with assigned users');
      return;
    }

    if (confirm('Are you sure you want to delete this role?')) {
      setRoles(prev => prev.filter(r => r.id !== roleId));
      setSuccess('Role deleted successfully');
    }
  };

  const togglePermission = (permissionId: string, isEditing: boolean = false) => {
    if (isEditing && editingRole) {
      const permissions = editingRole.permissions.includes(permissionId)
        ? editingRole.permissions.filter(p => p !== permissionId)
        : [...editingRole.permissions, permissionId];
      
      setEditingRole({ ...editingRole, permissions });
    } else {
      const permissions = newRole.permissions.includes(permissionId)
        ? newRole.permissions.filter(p => p !== permissionId)
        : [...newRole.permissions, permissionId];
      
      setNewRole({ ...newRole, permissions });
    }
  };

  const getPermissionsByCategory = () => {
    const categories: Record<string, Permission[]> = {};
    permissions.forEach(permission => {
      if (!categories[permission.category]) {
        categories[permission.category] = [];
      }
      categories[permission.category].push(permission);
    });
    return categories;
  };

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Roles & Permissions</h1>
          <p className="text-gray-600">Manage user roles and their permissions</p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Role
        </Button>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Roles List */}
      <div className="space-y-4">
        {roles.map((role) => (
          <div key={role.id} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-brown-100 rounded-lg flex items-center justify-center mr-4">
                    {role.is_system_role ? (
                      <Shield className="h-6 w-6 text-brown-600" />
                    ) : (
                      <UserCheck className="h-6 w-6 text-brown-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                      {role.name}
                      {role.is_system_role && (
                        <Lock className="h-4 w-4 ml-2 text-gray-400" title="System Role" />
                      )}
                    </h3>
                    <p className="text-gray-600">{role.description}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      {role.user_count} users assigned
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingRole(role)}
                    disabled={role.is_system_role}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  {!role.is_system_role && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteRole(role.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Permissions:</h4>
                <div className="flex flex-wrap gap-2">
                  {role.permissions.includes('all') ? (
                    <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                      All Permissions
                    </span>
                  ) : (
                    role.permissions.map(permissionId => {
                      const permission = permissions.find(p => p.id === permissionId);
                      return permission ? (
                        <span
                          key={permissionId}
                          className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                        >
                          {permission.name}
                        </span>
                      ) : null;
                    })
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Create Role Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Create New Role</h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="space-y-4 mb-6">
                <div>
                  <label htmlFor="roleName" className="block text-sm font-medium text-gray-700 mb-1">
                    Role Name
                  </label>
                  <input
                    type="text"
                    id="roleName"
                    value={newRole.name}
                    onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    placeholder="Enter role name"
                  />
                </div>
                
                <div>
                  <label htmlFor="roleDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="roleDescription"
                    value={newRole.description}
                    onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    placeholder="Describe this role"
                  />
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
                <div className="space-y-4">
                  {Object.entries(getPermissionsByCategory()).map(([category, categoryPermissions]) => (
                    <div key={category}>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">{category}</h4>
                      <div className="space-y-2 ml-4">
                        {categoryPermissions.map(permission => (
                          <label key={permission.id} className="flex items-start">
                            <input
                              type="checkbox"
                              checked={newRole.permissions.includes(permission.id)}
                              onChange={() => togglePermission(permission.id)}
                              className="mt-1 mr-3"
                            />
                            <div>
                              <span className="text-sm font-medium text-gray-900">{permission.name}</span>
                              <p className="text-xs text-gray-500">{permission.description}</p>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowCreateModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateRole}
                disabled={saving}
                className="flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Creating...' : 'Create Role'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Role Modal */}
      {editingRole && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Edit Role: {editingRole.name}</h2>
              <button
                onClick={() => setEditingRole(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="space-y-4 mb-6">
                <div>
                  <label htmlFor="editRoleName" className="block text-sm font-medium text-gray-700 mb-1">
                    Role Name
                  </label>
                  <input
                    type="text"
                    id="editRoleName"
                    value={editingRole.name}
                    onChange={(e) => setEditingRole({ ...editingRole, name: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="editRoleDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="editRoleDescription"
                    value={editingRole.description}
                    onChange={(e) => setEditingRole({ ...editingRole, description: e.target.value })}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
                <div className="space-y-4">
                  {Object.entries(getPermissionsByCategory()).map(([category, categoryPermissions]) => (
                    <div key={category}>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">{category}</h4>
                      <div className="space-y-2 ml-4">
                        {categoryPermissions.map(permission => (
                          <label key={permission.id} className="flex items-start">
                            <input
                              type="checkbox"
                              checked={editingRole.permissions.includes(permission.id)}
                              onChange={() => togglePermission(permission.id, true)}
                              className="mt-1 mr-3"
                            />
                            <div>
                              <span className="text-sm font-medium text-gray-900">{permission.name}</span>
                              <p className="text-xs text-gray-500">{permission.description}</p>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setEditingRole(null)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdateRole}
                disabled={saving}
                className="flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
