"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Calendar,
  Clock,
  Settings,
  CheckCircle,
  AlertCircle,
  Info,
  Save,
  Activity,
  Users,
  Briefcase,
  MessageSquare
} from "lucide-react";

interface AvailabilitySettings {
  status: 'available' | 'busy' | 'offline';
  custom_message: string;
  auto_accept_briefs: boolean;
  max_concurrent_projects: number;
}

export default function DesignerAvailability() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<AvailabilitySettings>({
    status: 'available',
    custom_message: '',
    auto_accept_briefs: false,
    max_concurrent_projects: 5
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    if (user) {
      fetchAvailabilitySettings();
    }
  }, [user]);

  const fetchAvailabilitySettings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // For now, we'll use mock data since the table doesn't exist yet
      // Later this will fetch from designer_availability table
      setSettings({
        status: 'available',
        custom_message: '',
        auto_accept_briefs: false,
        max_concurrent_projects: 5
      });
    } catch (error) {
      console.error('Error fetching availability settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setSaving(true);
    setMessage(null);

    try {
      // For now, we'll just show a success message
      // Later this will save to designer_availability table
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      setMessage({ type: 'success', text: 'Availability settings saved successfully!' });
    } catch (error) {
      console.error('Error saving availability settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800 border-green-200';
      case 'busy': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'offline': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <CheckCircle className="h-4 w-4" />;
      case 'busy': return <Clock className="h-4 w-4" />;
      case 'offline': return <AlertCircle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Availability Settings</h1>
        <p className="text-gray-600">Manage your availability status and preferences</p>
      </div>

      {message && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`p-4 rounded-lg border ${
            message.type === 'success' 
              ? 'bg-green-50 border-green-200 text-green-700' 
              : 'bg-red-50 border-red-200 text-red-700'
          }`}
        >
          <div className="flex items-center">
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <AlertCircle className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Settings */}
        <div className="lg:col-span-2 space-y-6">
          {/* Current Status */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">Current Status</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Availability Status
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {[
                    { value: 'available', label: 'Available', description: 'Ready for new projects' },
                    { value: 'busy', label: 'Busy', description: 'Limited availability' },
                    { value: 'offline', label: 'Offline', description: 'Not taking new projects' }
                  ].map((option) => (
                    <button
                      key={option.value}
                      onClick={() => setSettings(prev => ({ ...prev, status: option.value as any }))}
                      className={`p-4 border rounded-lg text-left transition-colors ${
                        settings.status === option.value
                          ? 'border-brown-500 bg-brown-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center mb-2">
                        {getStatusIcon(option.value)}
                        <span className="ml-2 font-medium">{option.label}</span>
                      </div>
                      <p className="text-sm text-gray-500">{option.description}</p>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label htmlFor="custom_message" className="block text-sm font-medium text-gray-700 mb-2">
                  Custom Status Message (Optional)
                </label>
                <textarea
                  id="custom_message"
                  value={settings.custom_message}
                  onChange={(e) => setSettings(prev => ({ ...prev, custom_message: e.target.value }))}
                  rows={3}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="Add a custom message about your availability..."
                />
                <p className="text-sm text-gray-500 mt-1">
                  This message will be visible to clients and admin when they view your profile
                </p>
              </div>
            </div>
          </div>

          {/* Project Preferences */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">Project Preferences</h2>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="max_projects" className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Concurrent Projects
                </label>
                <select
                  id="max_projects"
                  value={settings.max_concurrent_projects}
                  onChange={(e) => setSettings(prev => ({ ...prev, max_concurrent_projects: parseInt(e.target.value) }))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                    <option key={num} value={num}>{num} project{num !== 1 ? 's' : ''}</option>
                  ))}
                </select>
                <p className="text-sm text-gray-500 mt-1">
                  Set the maximum number of projects you can handle simultaneously
                </p>
              </div>

              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="auto_accept"
                  checked={settings.auto_accept_briefs}
                  onChange={(e) => setSettings(prev => ({ ...prev, auto_accept_briefs: e.target.checked }))}
                  className="mt-1 h-4 w-4 text-brown-600 focus:ring-brown-500 border-gray-300 rounded"
                />
                <div>
                  <label htmlFor="auto_accept" className="text-sm font-medium text-gray-700">
                    Auto-accept compatible briefs
                  </label>
                  <p className="text-sm text-gray-500">
                    Automatically accept project briefs that match your preferences and availability
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-brown-600 hover:bg-brown-700 text-white"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Current Status Display */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold mb-4">Status Preview</h3>
            <div className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border ${getStatusColor(settings.status)}`}>
              {getStatusIcon(settings.status)}
              <span className="ml-2">{settings.status.charAt(0).toUpperCase() + settings.status.slice(1)}</span>
            </div>
            {settings.custom_message && (
              <p className="text-sm text-gray-600 mt-3 p-3 bg-gray-50 rounded-lg">
                "{settings.custom_message}"
              </p>
            )}
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="h-4 w-4 text-blue-600 mr-2" />
                  <span className="text-sm text-gray-600">Active Projects</span>
                </div>
                <span className="text-sm font-medium">3</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Briefcase className="h-4 w-4 text-purple-600 mr-2" />
                  <span className="text-sm text-gray-600">Pending Briefs</span>
                </div>
                <span className="text-sm font-medium">2</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-green-600 mr-2" />
                  <span className="text-sm text-gray-600">Connected Clients</span>
                </div>
                <span className="text-sm font-medium">8</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <MessageSquare className="h-4 w-4 text-orange-600 mr-2" />
                  <span className="text-sm text-gray-600">Unread Messages</span>
                </div>
                <span className="text-sm font-medium">0</span>
              </div>
            </div>
          </div>

          {/* Tips */}
          <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">Tips</h3>
            <ul className="text-sm text-blue-800 space-y-2">
              <li>• Update your status regularly to keep clients informed</li>
              <li>• Use custom messages to explain temporary unavailability</li>
              <li>• Set realistic project limits to maintain quality</li>
              <li>• Auto-accept can help you respond faster to opportunities</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
