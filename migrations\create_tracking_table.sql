-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tracking_requests table
CREATE TABLE IF NOT EXISTS tracking_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tracking_number TEXT NOT NULL UNIQUE,
  request_type TEXT NOT NULL, -- 'sample_request' or 'vision_builder'
  status TEXT NOT NULL DEFAULT 'submitted', -- submitted, processing, completed, cancelled
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  project_type TEXT,
  description TEXT,
  service_category TEXT,
  sample_type TEXT,
  vision_prompt TEXT,
  selected_style TEXT,
  image_url TEXT,
  file_path TEXT,
  file_name TEXT,
  file_type TEXT,
  file_size INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  linked_project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
  linked_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_tracking_requests_tracking_number ON tracking_requests(tracking_number);
CREATE INDEX IF NOT EXISTS idx_tracking_requests_email ON tracking_requests(email);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_tracking_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at field
DROP TRIGGER IF EXISTS update_tracking_requests_updated_at ON tracking_requests;
CREATE TRIGGER update_tracking_requests_updated_at
BEFORE UPDATE ON tracking_requests
FOR EACH ROW EXECUTE FUNCTION update_tracking_modified_column();

-- Row-Level Security (RLS) Policies for tracking_requests

-- Allow public access for creating tracking requests (no auth required)
CREATE POLICY "Anyone can create tracking requests" ON tracking_requests
  FOR INSERT WITH CHECK (true);

-- Allow public access for reading tracking requests by tracking number (no auth required)
CREATE POLICY "Anyone can read tracking requests by tracking number" ON tracking_requests
  FOR SELECT USING (true);

-- Allow admins to manage all tracking requests
CREATE POLICY "Admins can manage all tracking requests" ON tracking_requests
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Enable RLS on the tracking_requests table
ALTER TABLE tracking_requests ENABLE ROW LEVEL SECURITY;
