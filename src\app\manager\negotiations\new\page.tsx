"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  DollarSign,
  Clock,
  FileText,
  Users,
  ArrowLeft,
  Save,
  Send,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Target,
  MessageSquare,
  Settings
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  client: {
    id: string;
    full_name: string;
    email: string;
  };
  designer: {
    id: string;
    full_name: string;
    email: string;
  };
}

export default function NewNegotiationPage() {
  const { user, profile } = useOptimizedAuth();
  const searchParams = useSearchParams();
  const router = useRouter();
  const negotiationType = searchParams.get('type') || 'pricing';
  
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    session_type: negotiationType,
    priority: 'normal',
    description: '',
    terms: {
      pricing: {
        current_budget: 0,
        proposed_budget: 0,
        payment_schedule: '',
        additional_costs: []
      },
      timeline: {
        current_deadline: '',
        proposed_deadline: '',
        milestones: [],
        buffer_time: 0
      },
      scope: {
        current_scope: '',
        proposed_changes: '',
        deliverables: [],
        exclusions: []
      },
      terms: {
        contract_changes: '',
        legal_requirements: '',
        conditions: [],
        amendments: []
      }
    },
    notes: '',
    auto_start: false
  });

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchProjects();
    }
  }, [user, profile]);

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id, title, description, budget, status,
          client:profiles!projects_client_id_fkey(id, full_name, email),
          designer:profiles!projects_designer_id_fkey(id, full_name, email)
        `)
        .eq('manager_id', user?.id)
        .in('status', ['active', 'in_progress', 'negotiation_required'])
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProjects(data || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectSelect = (projectId: string) => {
    setSelectedProject(projectId);
    const project = projects.find(p => p.id === projectId);
    if (project) {
      // Pre-populate form with project data
      setFormData(prev => ({
        ...prev,
        terms: {
          ...prev.terms,
          pricing: {
            ...prev.terms.pricing,
            current_budget: project.budget
          },
          timeline: {
            ...prev.terms.timeline,
            current_deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          }
        }
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedProject) {
      alert('Please select a project');
      return;
    }

    setSubmitting(true);
    try {
      const project = projects.find(p => p.id === selectedProject);
      if (!project) throw new Error('Project not found');

      // Create negotiation session
      const { data: session, error: sessionError } = await supabase
        .from('negotiation_sessions')
        .insert({
          project_id: selectedProject,
          manager_id: user?.id,
          client_id: project.client.id,
          designer_id: project.designer.id,
          session_type: formData.session_type,
          status: formData.auto_start ? 'active' : 'draft',
          terms: formData.terms,
          priority: formData.priority,
          description: formData.description,
          notes: formData.notes
        })
        .select()
        .single();

      if (sessionError) throw sessionError;

      // Log manager activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: selectedProject,
        activity_type: 'negotiation_created',
        description: `Created ${formData.session_type} negotiation session`,
        outcome: formData.auto_start ? 'session_started' : 'draft_created'
      });

      // Send notifications if auto-starting
      if (formData.auto_start) {
        await supabase.from('notifications').insert([
          {
            user_id: project.client.id,
            type: 'negotiation_started',
            title: 'New Negotiation Session',
            message: `A ${formData.session_type} negotiation has been started for ${project.title}`,
            data: { session_id: session.id, project_id: selectedProject }
          },
          {
            user_id: project.designer.id,
            type: 'negotiation_started',
            title: 'New Negotiation Session',
            message: `A ${formData.session_type} negotiation has been started for ${project.title}`,
            data: { session_id: session.id, project_id: selectedProject }
          }
        ]);
      }

      router.push(`/manager/negotiations/${session.id}`);
    } catch (error) {
      console.error('Error creating negotiation:', error);
      alert('Error creating negotiation. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'pricing':
        return <DollarSign className="h-5 w-5" />;
      case 'timeline':
        return <Clock className="h-5 w-5" />;
      case 'scope':
        return <Target className="h-5 w-5" />;
      case 'terms':
        return <FileText className="h-5 w-5" />;
      default:
        return <MessageSquare className="h-5 w-5" />;
    }
  };

  const getTypeDescription = (type: string) => {
    switch (type) {
      case 'pricing':
        return 'Negotiate project budget, payment terms, and additional costs';
      case 'timeline':
        return 'Discuss project deadlines, milestones, and delivery schedules';
      case 'scope':
        return 'Define project scope, deliverables, and requirements';
      case 'terms':
        return 'Review contract terms, legal requirements, and conditions';
      default:
        return 'General negotiation session';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          {getTypeIcon(negotiationType)}
          <div>
            <h1 className="text-3xl font-bold text-gray-900 capitalize">
              New {negotiationType} Negotiation
            </h1>
            <p className="text-gray-600 mt-1">{getTypeDescription(negotiationType)}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Project Selection */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Select Project</h2>
          <div className="space-y-4">
            {projects.length === 0 ? (
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
                <p className="text-gray-500">No eligible projects found</p>
                <p className="text-sm text-gray-400 mt-1">
                  Projects must be active or require negotiation
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {projects.map((project) => (
                  <div
                    key={project.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedProject === project.id
                        ? 'border-brown-500 bg-brown-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleProjectSelect(project.id)}
                  >
                    <div className="flex items-center gap-3">
                      <input
                        type="radio"
                        name="project"
                        value={project.id}
                        checked={selectedProject === project.id}
                        onChange={() => handleProjectSelect(project.id)}
                        className="text-brown-600 focus:ring-brown-500"
                      />
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{project.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{project.description}</p>
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                          <span>Client: {project.client.full_name}</span>
                          <span>Designer: {project.designer.full_name}</span>
                          <span>Budget: ${project.budget.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Negotiation Details */}
        {selectedProject && (
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Negotiation Details</h2>

            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority Level
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  >
                    <option value="low">Low Priority</option>
                    <option value="normal">Normal Priority</option>
                    <option value="high">High Priority</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Session Type
                  </label>
                  <select
                    value={formData.session_type}
                    onChange={(e) => setFormData(prev => ({ ...prev, session_type: e.target.value }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  >
                    <option value="pricing">Pricing Negotiation</option>
                    <option value="timeline">Timeline Negotiation</option>
                    <option value="scope">Scope Negotiation</option>
                    <option value="terms">Terms Negotiation</option>
                  </select>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Negotiation Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what needs to be negotiated and the expected outcomes..."
                  rows={3}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                />
              </div>

              {/* Type-specific fields */}
              {formData.session_type === 'pricing' && (
                <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                  <h3 className="font-semibold text-green-900 mb-3 flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Pricing Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">
                        Current Budget
                      </label>
                      <input
                        type="number"
                        value={formData.terms.pricing.current_budget}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            pricing: {
                              ...prev.terms.pricing,
                              current_budget: Number(e.target.value)
                            }
                          }
                        }))}
                        className="w-full border border-green-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-green-700 mb-1">
                        Proposed Budget
                      </label>
                      <input
                        type="number"
                        value={formData.terms.pricing.proposed_budget}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            pricing: {
                              ...prev.terms.pricing,
                              proposed_budget: Number(e.target.value)
                            }
                          }
                        }))}
                        className="w-full border border-green-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      />
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-green-700 mb-1">
                      Payment Schedule
                    </label>
                    <textarea
                      value={formData.terms.pricing.payment_schedule}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        terms: {
                          ...prev.terms,
                          pricing: {
                            ...prev.terms.pricing,
                            payment_schedule: e.target.value
                          }
                        }
                      }))}
                      placeholder="Describe the proposed payment schedule..."
                      rows={2}
                      className="w-full border border-green-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                  </div>
                </div>
              )}

              {formData.session_type === 'timeline' && (
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <h3 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Timeline Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">
                        Current Deadline
                      </label>
                      <input
                        type="date"
                        value={formData.terms.timeline.current_deadline}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            timeline: {
                              ...prev.terms.timeline,
                              current_deadline: e.target.value
                            }
                          }
                        }))}
                        className="w-full border border-blue-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">
                        Proposed Deadline
                      </label>
                      <input
                        type="date"
                        value={formData.terms.timeline.proposed_deadline}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            timeline: {
                              ...prev.terms.timeline,
                              proposed_deadline: e.target.value
                            }
                          }
                        }))}
                        className="w-full border border-blue-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {formData.session_type === 'scope' && (
                <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                  <h3 className="font-semibold text-purple-900 mb-3 flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Scope Details
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-purple-700 mb-1">
                        Current Scope
                      </label>
                      <textarea
                        value={formData.terms.scope.current_scope}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            scope: {
                              ...prev.terms.scope,
                              current_scope: e.target.value
                            }
                          }
                        }))}
                        placeholder="Describe the current project scope..."
                        rows={2}
                        className="w-full border border-purple-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-purple-700 mb-1">
                        Proposed Changes
                      </label>
                      <textarea
                        value={formData.terms.scope.proposed_changes}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            scope: {
                              ...prev.terms.scope,
                              proposed_changes: e.target.value
                            }
                          }
                        }))}
                        placeholder="Describe the proposed scope changes..."
                        rows={2}
                        className="w-full border border-purple-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {formData.session_type === 'terms' && (
                <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
                  <h3 className="font-semibold text-amber-900 mb-3 flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Terms & Conditions
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-amber-700 mb-1">
                        Contract Changes
                      </label>
                      <textarea
                        value={formData.terms.terms.contract_changes}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            terms: {
                              ...prev.terms.terms,
                              contract_changes: e.target.value
                            }
                          }
                        }))}
                        placeholder="Describe proposed contract changes..."
                        rows={2}
                        className="w-full border border-amber-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-amber-700 mb-1">
                        Legal Requirements
                      </label>
                      <textarea
                        value={formData.terms.terms.legal_requirements}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          terms: {
                            ...prev.terms,
                            terms: {
                              ...prev.terms.terms,
                              legal_requirements: e.target.value
                            }
                          }
                        }))}
                        placeholder="Specify any legal requirements or compliance needs..."
                        rows={2}
                        className="w-full border border-amber-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Any additional notes or context for this negotiation..."
                  rows={3}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                />
              </div>

              {/* Auto-start option */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="auto_start"
                  checked={formData.auto_start}
                  onChange={(e) => setFormData(prev => ({ ...prev, auto_start: e.target.checked }))}
                  className="text-brown-600 focus:ring-brown-500 rounded"
                />
                <label htmlFor="auto_start" className="text-sm text-gray-700">
                  Start negotiation immediately and notify participants
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Cancel
          </Button>

          <div className="flex gap-3">
            <Button
              type="submit"
              variant="outline"
              disabled={!selectedProject || submitting}
              onClick={(e) => {
                e.preventDefault();
                setFormData(prev => ({ ...prev, auto_start: false }));
                handleSubmit(e as any);
              }}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Save as Draft
            </Button>

            <Button
              type="submit"
              disabled={!selectedProject || submitting}
              className="flex items-center gap-2"
            >
              {submitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Send className="h-4 w-4" />
              )}
              {formData.auto_start ? 'Create & Start' : 'Create Negotiation'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
