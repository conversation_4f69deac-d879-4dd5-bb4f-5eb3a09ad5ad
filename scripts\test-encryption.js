#!/usr/bin/env node

/**
 * Encryption Test Script for Senior's Archi-firm
 * 
 * This script tests the encryption/decryption functionality to ensure it works correctly.
 * Run this after setting up your encryption keys.
 * 
 * Usage:
 *   ENCRYPTION_KEY=your-key node scripts/test-encryption.js
 */

// Set up environment for testing
if (!process.env.ENCRYPTION_KEY) {
  console.error('❌ ENCRYPTION_KEY environment variable is required');
  console.log('Usage: ENCRYPTION_KEY=your-key node scripts/test-encryption.js');
  process.exit(1);
}

// Import our encryption functions
const path = require('path');
const { 
  encryptSensitiveData, 
  decryptSensitiveData, 
  encryptPaymentMethodData, 
  decryptPaymentMethodData,
  maskSensitiveData,
  validateEncryptionKey
} = require('../src/lib/encryption.ts');

function runTests() {
  console.log('🧪 Testing Encryption Implementation');
  console.log('=' .repeat(50));
  console.log();

  let passed = 0;
  let failed = 0;

  function test(name, testFn) {
    try {
      testFn();
      console.log(`✅ ${name}`);
      passed++;
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      failed++;
    }
  }

  // Test 1: Basic encryption/decryption
  test('Basic encryption/decryption', () => {
    const original = '****************';
    const encrypted = encryptSensitiveData(original);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (encrypted === original) {
      throw new Error('Data was not encrypted');
    }
    if (decrypted !== original) {
      throw new Error('Decrypted data does not match original');
    }
  });

  // Test 2: Empty string handling
  test('Empty string handling', () => {
    const encrypted = encryptSensitiveData('');
    const decrypted = decryptSensitiveData('');
    
    if (encrypted !== '' || decrypted !== '') {
      throw new Error('Empty strings should remain empty');
    }
  });

  // Test 3: Payment method data encryption
  test('Payment method data encryption', () => {
    const paymentData = {
      account_number: '**********',
      routing_number: '*********',
      iban: '**********************',
      swift_code: 'DEUTDEFF'
    };

    const encrypted = encryptPaymentMethodData(paymentData);
    const decrypted = decryptPaymentMethodData(encrypted);

    // Check that sensitive fields were encrypted
    if (encrypted.account_number === paymentData.account_number) {
      throw new Error('Account number was not encrypted');
    }
    if (encrypted.routing_number === paymentData.routing_number) {
      throw new Error('Routing number was not encrypted');
    }

    // Check that decryption works
    if (decrypted.account_number !== paymentData.account_number) {
      throw new Error('Account number decryption failed');
    }
    if (decrypted.routing_number !== paymentData.routing_number) {
      throw new Error('Routing number decryption failed');
    }
  });

  // Test 4: Data masking
  test('Data masking', () => {
    const accountNumber = '****************';
    const masked = maskSensitiveData(accountNumber, 4);
    
    if (!masked.includes('****')) {
      throw new Error('Data was not properly masked');
    }
    if (!masked.endsWith('3456')) {
      throw new Error('Last 4 digits not preserved');
    }
  });

  // Test 5: Key validation
  test('Key validation', () => {
    const validKey = 'a'.repeat(64);
    const invalidKey = 'a'.repeat(32);
    const weakKey = '1'.repeat(64);

    if (!validateEncryptionKey(validKey)) {
      throw new Error('Valid key rejected');
    }
    if (validateEncryptionKey(invalidKey)) {
      throw new Error('Invalid key accepted');
    }
    if (validateEncryptionKey(weakKey)) {
      throw new Error('Weak key accepted');
    }
  });

  // Test 6: Large data handling
  test('Large data handling', () => {
    const largeData = 'A'.repeat(1000);
    const encrypted = encryptSensitiveData(largeData);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (decrypted !== largeData) {
      throw new Error('Large data encryption/decryption failed');
    }
  });

  // Test 7: Special characters
  test('Special characters handling', () => {
    const specialData = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    const encrypted = encryptSensitiveData(specialData);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (decrypted !== specialData) {
      throw new Error('Special characters encryption/decryption failed');
    }
  });

  // Test 8: Unicode handling
  test('Unicode handling', () => {
    const unicodeData = '🔐💳🏦💰';
    const encrypted = encryptSensitiveData(unicodeData);
    const decrypted = decryptSensitiveData(encrypted);
    
    if (decrypted !== unicodeData) {
      throw new Error('Unicode encryption/decryption failed');
    }
  });

  console.log();
  console.log('=' .repeat(50));
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Encryption is working correctly.');
    return true;
  } else {
    console.log('⚠️  Some tests failed. Please check your encryption setup.');
    return false;
  }
}

if (require.main === module) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runTests };
