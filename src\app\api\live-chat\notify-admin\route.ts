import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';

const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

export async function POST(request: NextRequest) {
  try {
    const { sessionId, type, message, visitorInfo } = await request.json();

    if (!sessionId || !type || !visitorInfo) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get admin email from profiles
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('email, full_name')
      .eq('role', 'admin')
      .single();

    if (adminError || !adminProfile) {
      console.error('Error fetching admin profile:', adminError);
      return NextResponse.json(
        { error: 'Admin not found' },
        { status: 404 }
      );
    }

    // Skip email sending if no API key is available
    if (!process.env.RESEND_API_KEY || !resend) {
      console.warn('Skipping email sending - RESEND_API_KEY not configured');
      return NextResponse.json({ success: true, message: 'Email skipped - no API key' });
    }

    let subject = '';
    let content = '';

    switch (type) {
      case 'new_chat':
        subject = 'New Live Chat Started - Senior\'s Archi-firm';
        content = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #8B4513; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
              <h2 style="margin: 0; font-size: 24px;">New Live Chat Session</h2>
            </div>
            
            <div style="background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px;">
              <p style="margin-bottom: 15px;"><strong>A new visitor has started a live chat session and is waiting for assistance.</strong></p>
              
              <div style="background-color: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                <h3 style="margin-top: 0; color: #8B4513;">Visitor Information:</h3>
                <p><strong>Name:</strong> ${visitorInfo.name}</p>
                <p><strong>Email:</strong> ${visitorInfo.email}</p>
                <p><strong>Session ID:</strong> ${sessionId}</p>
              </div>
              
              <p style="margin-bottom: 20px;">Please log in to your admin dashboard to respond to this chat session.</p>
              
              <div style="text-align: center;">
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}/admin/live-chat" 
                   style="background-color: #8B4513; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Open Live Chat Dashboard
                </a>
              </div>
            </div>
          </div>
        `;
        break;

      case 'offline_message':
        subject = 'New Message in Live Chat - Senior\'s Archi-firm';
        content = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #8B4513; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
              <h2 style="margin: 0; font-size: 24px;">New Chat Message</h2>
            </div>
            
            <div style="background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px;">
              <p style="margin-bottom: 15px;"><strong>A visitor has sent a new message while you were offline.</strong></p>
              
              <div style="background-color: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                <h3 style="margin-top: 0; color: #8B4513;">Visitor Information:</h3>
                <p><strong>Name:</strong> ${visitorInfo.name}</p>
                <p><strong>Email:</strong> ${visitorInfo.email}</p>
              </div>
              
              <div style="background-color: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                <h3 style="margin-top: 0; color: #8B4513;">Message:</h3>
                <p style="font-style: italic; background-color: #f8f8f8; padding: 10px; border-left: 4px solid #8B4513;">"${message}"</p>
              </div>
              
              <p style="margin-bottom: 20px;">Please log in to your admin dashboard to respond to this message.</p>
              
              <div style="text-align: center;">
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}/admin/live-chat" 
                   style="background-color: #8B4513; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Open Live Chat Dashboard
                </a>
              </div>
            </div>
          </div>
        `;
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid notification type' },
          { status: 400 }
        );
    }

    // Send email notification
    const emailResult = await resend.emails.send({
      from: 'Senior\'s Archi-firm <<EMAIL>>',
      to: [adminProfile.email],
      subject: subject,
      html: content
    });

    // Record notification in database
    const { error: notificationError } = await supabase
      .from('live_chat_notifications')
      .insert({
        session_id: sessionId,
        notification_type: type,
        recipient_email: adminProfile.email,
        subject: subject,
        content: content,
        status: emailResult.error ? 'failed' : 'sent',
        resend_id: emailResult.data?.id,
        error_message: emailResult.error?.message
      });

    if (notificationError) {
      console.error('Error recording notification:', notificationError);
    }

    if (emailResult.error) {
      console.error('Error sending email:', emailResult.error);
      return NextResponse.json(
        { error: 'Failed to send email notification' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Notification sent successfully',
      emailId: emailResult.data?.id
    });

  } catch (error) {
    console.error('Error in live chat notification API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
