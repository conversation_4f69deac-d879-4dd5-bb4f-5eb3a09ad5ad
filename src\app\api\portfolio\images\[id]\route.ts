import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * PATCH /api/portfolio/images/[id]
 * Updates a portfolio image
 * 
 * Request body:
 * {
 *   caption?: string | null;
 *   display_order?: number;
 *   is_cover?: boolean;
 * }
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const imageId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the image to check project ownership
    const { data: image, error: imageError } = await supabase
      .from('portfolio_images')
      .select('project_id')
      .eq('id', imageId)
      .single();
    
    if (imageError) {
      return NextResponse.json(
        { error: 'Portfolio image not found' },
        { status: 404 }
      );
    }
    
    // Get the project to check ownership
    const { data: project, error: projectError } = await supabase
      .from('portfolio_projects')
      .select('designer_id')
      .eq('id', image.project_id)
      .single();
    
    if (projectError) {
      return NextResponse.json(
        { error: 'Portfolio project not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to update this image
    const isAdmin = profile.role === 'admin';
    const isOwner = project.designer_id === user.id;
    
    if (!isAdmin && !isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to update this image' },
        { status: 403 }
      );
    }
    
    const { caption, display_order, is_cover } = await request.json();
    
    // Prepare update data
    const updateData: any = {};
    
    if (caption !== undefined) updateData.caption = caption;
    if (display_order !== undefined) updateData.display_order = display_order;
    if (is_cover !== undefined) updateData.is_cover = is_cover;
    
    // If this is being set as a cover image, update any existing cover images
    if (is_cover) {
      await supabase
        .from('portfolio_images')
        .update({ is_cover: false })
        .eq('project_id', image.project_id)
        .eq('is_cover', true)
        .neq('id', imageId);
    }
    
    // Update the image
    const { data, error } = await supabase
      .from('portfolio_images')
      .update(updateData)
      .eq('id', imageId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating portfolio image:', error);
      return NextResponse.json(
        { error: 'Failed to update portfolio image' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error in PATCH /api/portfolio/images/[id]:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/portfolio/images/[id]
 * Deletes a portfolio image
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const imageId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the image to check project ownership
    const { data: image, error: imageError } = await supabase
      .from('portfolio_images')
      .select('project_id')
      .eq('id', imageId)
      .single();
    
    if (imageError) {
      return NextResponse.json(
        { error: 'Portfolio image not found' },
        { status: 404 }
      );
    }
    
    // Get the project to check ownership
    const { data: project, error: projectError } = await supabase
      .from('portfolio_projects')
      .select('designer_id')
      .eq('id', image.project_id)
      .single();
    
    if (projectError) {
      return NextResponse.json(
        { error: 'Portfolio project not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to delete this image
    const isAdmin = profile.role === 'admin';
    const isOwner = project.designer_id === user.id;
    
    if (!isAdmin && !isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to delete this image' },
        { status: 403 }
      );
    }
    
    // Delete the image
    const { error } = await supabase
      .from('portfolio_images')
      .delete()
      .eq('id', imageId);
    
    if (error) {
      console.error('Error deleting portfolio image:', error);
      return NextResponse.json(
        { error: 'Failed to delete portfolio image' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('Error in DELETE /api/portfolio/images/[id]:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
