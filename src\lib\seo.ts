/**
 * SEO utilities for generating meta tags, structured data, and sitemaps
 */

import { Metadata } from 'next';

// Base site configuration
export const siteConfig = {
  name: "Senior's Archi-Firm",
  description: "Leading architectural design firm specializing in innovative, sustainable, and client-focused solutions. Transform your vision into reality with our expert team.",
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://seniorsarchifirm.com',
  ogImage: '/og-image.jpg',
  twitterHandle: '@seniorsarchifirm',
  keywords: [
    'architecture',
    'architectural design',
    'building design',
    'interior design',
    'urban planning',
    'sustainable architecture',
    'residential design',
    'commercial architecture',
    'architectural visualization',
    'construction planning'
  ]
};

// Generate metadata for pages
export function generateMetadata({
  title,
  description,
  path = '',
  image,
  keywords = [],
  type = 'website'
}: {
  title?: string;
  description?: string;
  path?: string;
  image?: string;
  keywords?: string[];
  type?: 'website' | 'article';
}): Metadata {
  const pageTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.name;
  const pageDescription = description || siteConfig.description;
  const pageUrl = `${siteConfig.url}${path}`;
  const pageImage = image || siteConfig.ogImage;
  const allKeywords = [...siteConfig.keywords, ...keywords].join(', ');

  return {
    metadataBase: new URL(siteConfig.url),
    title: pageTitle,
    description: pageDescription,
    keywords: allKeywords,
    authors: [{ name: siteConfig.name }],
    creator: siteConfig.name,
    publisher: siteConfig.name,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type,
      locale: 'en_US',
      url: pageUrl,
      title: pageTitle,
      description: pageDescription,
      siteName: siteConfig.name,
      images: [
        {
          url: pageImage,
          width: 1200,
          height: 630,
          alt: pageTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      images: [pageImage],
      creator: siteConfig.twitterHandle,
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

// Generate JSON-LD structured data for organization
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    logo: `${siteConfig.url}/seniors-logo.svg`,
    image: `${siteConfig.url}/og-image.jpg`,
    sameAs: [
      // Add social media URLs here
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+966552552260',
      contactType: 'customer service',
      email: '<EMAIL>',
      availableLanguage: ['English', 'Arabic']
    },
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Riyadh',
      addressCountry: 'SA',
      addressRegion: 'Riyadh Province'
    },
    foundingDate: '2020',
    numberOfEmployees: '10-50',
    industry: 'Architecture and Planning',
    services: [
      'Architectural Design',
      'Interior Design',
      'Urban Planning',
      'Landscape Architecture',
      'Sustainable Design',
      'Project Management'
    ]
  };
}

// Generate JSON-LD structured data for services
export function generateServiceSchema(service: {
  name: string;
  description: string;
  url: string;
  image?: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: service.name,
    description: service.description,
    url: service.url,
    image: service.image,
    provider: {
      '@type': 'Organization',
      name: siteConfig.name,
      url: siteConfig.url
    },
    serviceType: 'Architectural Services',
    category: 'Architecture and Planning'
  };
}

// Generate JSON-LD structured data for blog articles
export function generateArticleSchema(article: {
  title: string;
  description: string;
  url: string;
  image?: string;
  publishedAt: string;
  modifiedAt?: string;
  author: string;
  category?: string;
  tags?: string[];
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    url: article.url,
    image: article.image,
    datePublished: article.publishedAt,
    dateModified: article.modifiedAt || article.publishedAt,
    author: {
      '@type': 'Person',
      name: article.author
    },
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
      logo: {
        '@type': 'ImageObject',
        url: `${siteConfig.url}/seniors-logo.svg`
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': article.url
    },
    articleSection: article.category,
    keywords: article.tags?.join(', ')
  };
}

// Generate breadcrumb structured data
export function generateBreadcrumbSchema(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  };
}

// Generate FAQ structured data
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
}

// Utility to generate structured data script tag (use in components)
export function getStructuredDataScript(data: object): string {
  return JSON.stringify(data);
}

// Generate robots.txt content
export function generateRobotsTxt() {
  return `User-agent: *
Allow: /

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/

# Disallow authentication pages
Disallow: /auth/
Disallow: /login/
Disallow: /signup/

# Allow important pages
Allow: /services/
Allow: /about/
Allow: /contact/
Allow: /blog/
Allow: /sample-request/
Allow: /vision-builder/

# Sitemap
Sitemap: ${siteConfig.url}/sitemap.xml`;
}

// Generate sitemap URLs
export function generateSitemapUrls() {
  const staticPages = [
    { url: '/', priority: 1.0, changefreq: 'daily' },
    { url: '/services', priority: 0.9, changefreq: 'weekly' },
    { url: '/about', priority: 0.8, changefreq: 'monthly' },
    { url: '/contact', priority: 0.8, changefreq: 'monthly' },
    { url: '/sample-request', priority: 0.9, changefreq: 'weekly' },
    { url: '/vision-builder', priority: 0.9, changefreq: 'weekly' },
    { url: '/blog', priority: 0.8, changefreq: 'daily' },
    { url: '/join-us', priority: 0.7, changefreq: 'monthly' },
    { url: '/privacy-policy', priority: 0.5, changefreq: 'yearly' },
  ];

  return staticPages.map(page => ({
    url: `${siteConfig.url}${page.url}`,
    lastModified: new Date(),
    changeFrequency: page.changefreq as 'daily' | 'weekly' | 'monthly' | 'yearly',
    priority: page.priority,
  }));
}
