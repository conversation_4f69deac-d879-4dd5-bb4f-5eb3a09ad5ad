import { NextResponse } from 'next/server';
import { createBillingAgreement } from '@/lib/paypal';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  try {
    // Get request data
    const { userId, description } = await request.json();

    // Validate required fields
    if (!userId || !description) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Initialize Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          persistSession: false
        }
      }
    );

    // Verify user exists
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user:', userError);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Create billing agreement with PayPal
    const billingAgreement = await createBillingAgreement(userId, description);
    
    // Extract the approval URL from the links
    const approvalUrl = billingAgreement.links.find((link: any) => link.rel === 'approval_url')?.href;
    
    if (!approvalUrl) {
      throw new Error('Approval URL not found in PayPal response');
    }
    
    // Extract the token from the approval URL
    const token = new URL(approvalUrl).searchParams.get('token');
    
    if (!token) {
      throw new Error('Token not found in approval URL');
    }
    
    // Return the approval URL and token
    return NextResponse.json({
      approvalUrl,
      token
    });
  } catch (error: unknown) {
    console.error('Error creating PayPal billing agreement:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
