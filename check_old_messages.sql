-- Check old messages table data
SELECT 
  COUNT(*) as total_old_messages,
  COUNT(DISTINCT sender_id) as unique_senders,
  COUNT(CASE WHEN project_id IS NOT NULL THEN 1 END) as messages_with_project,
  MIN(created_at) as oldest_message,
  MAX(created_at) as newest_message
FROM messages;

-- Sample old messages
SELECT 
  id,
  sender_id,
  content,
  project_id,
  created_at,
  is_read
FROM messages 
ORDER BY created_at DESC 
LIMIT 5;
