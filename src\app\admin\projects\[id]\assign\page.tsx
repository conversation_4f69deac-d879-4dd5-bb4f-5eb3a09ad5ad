"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/Button";
import Link from "next/link";
import {
  ArrowLeft,
  Save,
  AlertCircle,
  CheckCircle,
  Search,
  User,
  Users,
  Star,
  Clock,
  Briefcase
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  client_name: string;
  designer_id: string | null;
  designer_name: string | null;
  type: string | null;
  status: string;
};

type Designer = {
  id: string;
  full_name: string;
  email: string;
  avatar_url: string | null;
  specialization: string | null;
  years_experience: number | null;
  active_projects: number;
  completed_projects: number;
  is_selected: boolean;
};

export default function AssignDesigner() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [project, setProject] = useState<Project | null>(null);
  const [designers, setDesigners] = useState<Designer[]>([]);
  const [filteredDesigners, setFilteredDesigners] = useState<Designer[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDesignerId, setSelectedDesignerId] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchProjectAndDesigners();
    }
  }, [user, projectId]);

  useEffect(() => {
    // Filter designers based on search term
    if (searchTerm.trim() === "") {
      setFilteredDesigners(designers);
    } else {
      const term = searchTerm.toLowerCase();
      const filtered = designers.filter(
        designer =>
          designer.full_name.toLowerCase().includes(term) ||
          designer.email.toLowerCase().includes(term) ||
          (designer.specialization && designer.specialization.toLowerCase().includes(term))
      );
      setFilteredDesigners(filtered);
    }
  }, [designers, searchTerm]);

  const fetchProjectAndDesigners = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          type,
          status,
          designer_id,
          client_id,
          client:profiles!client_id(full_name),
          designer:profiles!designer_id(full_name)
        `)
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      setProject({
        id: projectData.id,
        title: projectData.title,
        client_name: projectData.client?.full_name || 'Unknown Client',
        designer_id: projectData.designer_id,
        designer_name: projectData.designer?.full_name || null,
        type: projectData.type,
        status: projectData.status
      });

      // Set the initially selected designer if one is assigned
      if (projectData.designer_id) {
        setSelectedDesignerId(projectData.designer_id);
      }

      // Fetch available designers
      const { data: designersData, error: designersError } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          avatar_url,
          specialization,
          years_experience,
          is_active
        `)
        .eq('role', 'designer')
        .eq('is_active', true)
        .eq('application_status', 'approved');

      if (designersError) throw designersError;

      // Fetch project counts for each designer
      const designersWithProjects = await Promise.all(
        (designersData || []).map(async (designer) => {
          // Get active projects count
          const { count: activeCount, error: activeError } = await supabase
            .from('projects')
            .select('id', { count: 'exact' })
            .eq('designer_id', designer.id)
            .in('status', ['active', 'in_progress']);

          if (activeError) throw activeError;

          // Get completed projects count
          const { count: completedCount, error: completedError } = await supabase
            .from('projects')
            .select('id', { count: 'exact' })
            .eq('designer_id', designer.id)
            .eq('status', 'completed');

          if (completedError) throw completedError;

          return {
            ...designer,
            active_projects: activeCount || 0,
            completed_projects: completedCount || 0,
            is_selected: designer.id === projectData.designer_id
          };
        })
      );

      setDesigners(designersWithProjects);
      setFilteredDesigners(designersWithProjects);
    } catch (error: Error | unknown) {
      console.error('Error fetching data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleDesignerSelect = (designerId: string) => {
    setSelectedDesignerId(designerId);

    // Update the is_selected state for UI
    setDesigners(designers.map(designer => ({
      ...designer,
      is_selected: designer.id === designerId
    })));

    setFilteredDesigners(filteredDesigners.map(designer => ({
      ...designer,
      is_selected: designer.id === designerId
    })));
  };

  const handleAssignDesigner = async () => {
    if (!selectedDesignerId || !project) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // Update the project with the selected designer
      const { error: updateError } = await supabase
        .from('projects')
        .update({
          designer_id: selectedDesignerId,
          assigned_at: new Date().toISOString(),
          assigned_by: user?.id,
          status: project.status === 'draft' ? 'in_progress' : project.status
        })
        .eq('id', projectId);

      if (updateError) throw updateError;

      // Create a notification for the designer
      const { error: notificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: selectedDesignerId,
          type: 'assignment',
          title: 'New Project Assignment',
          content: `You have been assigned to the project: ${project.title}`,
          related_id: projectId,
          read: false
        });

      if (notificationError) throw notificationError;

      setSuccess('Designer assigned successfully');

      // Redirect after a short delay
      setTimeout(() => {
        router.push(`/admin/projects/${projectId}`);
      }, 1500);
    } catch (error: Error | unknown) {
      console.error('Error assigning designer:', error);
      setError(error instanceof Error ? error.message : 'Failed to assign designer');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading project data...</p>
        </div>
      </div>
    );
  }

  if (error && !project) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            Project not found
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center">
        <Link href={`/admin/projects/${projectId}`} className="mr-4">
          <Button variant="ghost" className="p-0 h-auto">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Assign Designer</h1>
          <p className="text-gray-500">{project.title} - {project.client_name}</p>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Project Details</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start">
              <Users className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Client</p>
                <p className="font-medium">{project.client_name}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Briefcase className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Project Type</p>
                <p className="font-medium">{project.type || 'Not specified'}</p>
              </div>
            </div>

            <div className="flex items-start">
              <User className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Current Designer</p>
                <p className="font-medium">{project.designer_name || 'None assigned'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b">
          <h2 className="text-lg font-semibold">Available Designers</h2>
        </div>
        <div className="p-6">
          <div className="mb-6">
            <div className="relative">
              <input
                type="text"
                placeholder="Search designers by name, email, or specialization"
                className="w-full px-4 py-2 pl-10 border rounded-md"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>

          {filteredDesigners.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No designers found matching your search criteria.</p>
              <p className="text-sm mt-2">Try adjusting your search or invite new designers to the platform.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredDesigners.map((designer) => (
                <div
                  key={designer.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    designer.is_selected ? 'border-primary bg-primary bg-opacity-5' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleDesignerSelect(designer.id)}
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        {designer.avatar_url ? (
                          <img
                            src={designer.avatar_url}
                            alt={designer.full_name}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-6 w-6 text-gray-400" />
                        )}
                      </div>
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex justify-between">
                        <div>
                          <h3 className="text-lg font-medium">{designer.full_name}</h3>
                          <p className="text-sm text-gray-500">{designer.email}</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="radio"
                            name="designer"
                            checked={designer.is_selected}
                            onChange={() => handleDesignerSelect(designer.id)}
                            className="h-4 w-4 text-primary border-gray-300"
                          />
                        </div>
                      </div>
                      <div className="mt-2 grid grid-cols-3 gap-4">
                        <div className="flex items-center text-sm">
                          <Briefcase className="h-4 w-4 text-gray-400 mr-1" />
                          <span>{designer.specialization || 'No specialization'}</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Clock className="h-4 w-4 text-gray-400 mr-1" />
                          <span>{designer.years_experience || 0} years experience</span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Star className="h-4 w-4 text-gray-400 mr-1" />
                          <span>{designer.completed_projects} completed projects</span>
                        </div>
                      </div>
                      <div className="mt-1 text-sm text-gray-500">
                        Currently working on {designer.active_projects} active projects
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="mt-8 flex justify-end">
            <Link href={`/admin/projects/${projectId}`}>
              <Button variant="outline" className="mr-4">
                Cancel
              </Button>
            </Link>
            <Button
              onClick={handleAssignDesigner}
              disabled={!selectedDesignerId || saving}
              className="flex items-center"
            >
              {saving ? (
                <span className="flex items-center">
                  <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                  Assigning...
                </span>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Assign Designer
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
