"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/Button";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  DollarSign,
  CreditCard,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  Filter,
  Search,
  AlertCircle,
  CheckCircle,
  Clock,
  ArrowRight,
  Loader2,
  BarChart3,
  PieChart
} from "lucide-react";

type FinancialSummary = {
  total_revenue: number;
  pending_payments: number;
  completed_payments: number;
  total_payouts: number;
  pending_payouts: number;
};

type Transaction = {
  id: string;
  transaction_id: string;
  amount: number;
  status: string;
  type: string;
  project_id: string;
  project_title: string;
  client_id: string;
  client_name: string;
  designer_id: string | null;
  designer_name: string | null;
  processed_at: string | null;
  created_at: string;
  notes: string | null;
};

type ChartData = {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string[];
    borderColor: string[];
    borderWidth: number;
  }[];
};

export default function FinanceDashboard() {
  const { user } = useOptimizedAuth();
  const [summary, setSummary] = useState<FinancialSummary | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [dateRange, setDateRange] = useState<string>("30");
  const [revenueChartData, setRevenueChartData] = useState<ChartData | null>(null);
  const [payoutChartData, setPayoutChartData] = useState<ChartData | null>(null);

  useEffect(() => {
    if (user) {
      checkAdminAccess();
      fetchFinancialData();
    }
  }, [user, dateRange]);

  useEffect(() => {
    filterTransactions();
  }, [transactions, searchTerm, statusFilter, typeFilter]);

  const checkAdminAccess = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      if (data.role !== 'admin') {
        setError('You do not have permission to access this page');
        setLoading(false);
      }
    } catch (error) {
      console.error('Error checking admin access:', error);
      setError('Failed to verify admin access');
      setLoading(false);
    }
  };

  const fetchFinancialData = async () => {
    setLoading(true);

    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(dateRange));

      // Fetch transactions
      const { data: transactionsData, error: transactionsError } = await supabase
        .from('transactions')
        .select(`
          id,
          transaction_id,
          amount,
          status,
          type,
          project_id,
          client_id,
          designer_id,
          processed_at,
          created_at,
          notes,
          projects(title),
          profiles!transactions_client_id_fkey(full_name),
          profiles!transactions_designer_id_fkey(full_name)
        `)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .order('created_at', { ascending: false });

      if (transactionsError) throw transactionsError;

      // Format transactions data
      const formattedTransactions = transactionsData.map(transaction => ({
        id: transaction.id,
        transaction_id: transaction.transaction_id,
        amount: transaction.amount,
        status: transaction.status,
        type: transaction.type,
        project_id: transaction.project_id,
        project_title: transaction.projects?.title || 'Unknown Project',
        client_id: transaction.client_id,
        client_name: transaction.profiles?.full_name || 'Unknown Client',
        designer_id: transaction.designer_id,
        designer_name: transaction.profiles?.full_name || null,
        processed_at: transaction.processed_at,
        created_at: transaction.created_at,
        notes: transaction.notes
      }));

      setTransactions(formattedTransactions);

      // Calculate summary
      const totalRevenue = formattedTransactions
        .filter(t => t.type === 'payment' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      const pendingPayments = formattedTransactions
        .filter(t => t.type === 'payment' && t.status === 'pending')
        .reduce((sum, t) => sum + t.amount, 0);

      const completedPayments = formattedTransactions
        .filter(t => t.type === 'payment' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      const totalPayouts = formattedTransactions
        .filter(t => t.type === 'payout' && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      const pendingPayouts = formattedTransactions
        .filter(t => t.type === 'payout' && t.status === 'pending')
        .reduce((sum, t) => sum + t.amount, 0);

      setSummary({
        total_revenue: totalRevenue,
        pending_payments: pendingPayments,
        completed_payments: completedPayments,
        total_payouts: totalPayouts,
        pending_payouts: pendingPayouts
      });

      // Prepare chart data
      prepareChartData(formattedTransactions);
    } catch (error) {
      console.error('Error fetching financial data:', error);
      setError('Failed to load financial data');
    } finally {
      setLoading(false);
    }
  };

  const prepareChartData = (transactions: Transaction[]) => {
    // Group transactions by month for revenue chart
    const revenueByMonth: { [key: string]: number } = {};
    const payoutsByMonth: { [key: string]: number } = {};

    // Get last 6 months
    const months = [];
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      months.unshift(monthKey);
      revenueByMonth[monthKey] = 0;
      payoutsByMonth[monthKey] = 0;
    }

    // Calculate revenue and payouts by month
    transactions.forEach(transaction => {
      const date = new Date(transaction.created_at);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      if (months.includes(monthKey)) {
        if (transaction.type === 'payment' && transaction.status === 'completed') {
          revenueByMonth[monthKey] += transaction.amount;
        } else if (transaction.type === 'payout' && transaction.status === 'completed') {
          payoutsByMonth[monthKey] += transaction.amount;
        }
      }
    });

    // Prepare chart data
    setRevenueChartData({
      labels: months,
      datasets: [
        {
          label: 'Revenue',
          data: months.map(month => revenueByMonth[month]),
          backgroundColor: ['rgba(139, 69, 19, 0.2)'],
          borderColor: ['rgba(139, 69, 19, 1)'],
          borderWidth: 1
        }
      ]
    });

    setPayoutChartData({
      labels: months,
      datasets: [
        {
          label: 'Payouts',
          data: months.map(month => payoutsByMonth[month]),
          backgroundColor: ['rgba(75, 192, 192, 0.2)'],
          borderColor: ['rgba(75, 192, 192, 1)'],
          borderWidth: 1
        }
      ]
    });
  };

  const filterTransactions = () => {
    let filtered = [...transactions];

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(t => t.status === statusFilter);
    }

    // Apply type filter
    if (typeFilter !== "all") {
      filtered = filtered.filter(t => t.type === typeFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(t =>
        t.project_title.toLowerCase().includes(term) ||
        t.client_name.toLowerCase().includes(term) ||
        (t.designer_name && t.designer_name.toLowerCase().includes(term)) ||
        t.transaction_id.toLowerCase().includes(term)
      );
    }

    setFilteredTransactions(filtered);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-amber-50 border border-amber-200 text-amber-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </span>
        );
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-50 border border-green-200 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-red-50 border border-red-200 text-red-800">
            <AlertCircle className="h-3 w-3 mr-1" />
            Failed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-gray-50 border border-gray-200 text-gray-800">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Financial Dashboard</h1>
        <div className="flex items-center space-x-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="180">Last 6 months</option>
            <option value="365">Last year</option>
          </select>
          <Button
            variant="outline"
            className="flex items-center"
            onClick={() => {
              // Generate CSV of transactions
              const headers = ["ID", "Date", "Type", "Status", "Project", "Client", "Designer", "Amount", "Notes"];
              const csvContent = [
                headers.join(","),
                ...filteredTransactions.map(t => [
                  t.transaction_id,
                  formatDate(t.created_at),
                  t.type,
                  t.status,
                  t.project_title,
                  t.client_name,
                  t.designer_name || "N/A",
                  t.amount,
                  t.notes || ""
                ].join(","))
              ].join("\n");

              const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
              const url = URL.createObjectURL(blob);
              const link = document.createElement("a");
              link.setAttribute("href", url);
              link.setAttribute("download", `financial_report_${new Date().toISOString().split('T')[0]}.csv`);
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <div className="h-8 w-8 border-t-2 border-b-2 border-brown-600"></div>
          </motion.div>
        </div>
      ) : (
        <>
          {/* Financial Summary Cards */}
          {summary && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
              <div className="bg-white border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-sm font-medium text-gray-500">Total Revenue</h2>
                  <div className="w-10 h-10 bg-green-50 border border-green-200 flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-green-600" />
                  </div>
                </div>
                <p className="text-2xl font-semibold">{formatCurrency(summary.total_revenue)}</p>
                <div className="flex items-center mt-2 text-sm text-green-600">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  <span>+{formatCurrency(summary.completed_payments)} this period</span>
                </div>
              </div>

              <div className="bg-white border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-sm font-medium text-gray-500">Pending Payments</h2>
                  <div className="w-10 h-10 bg-amber-50 border border-amber-200 flex items-center justify-center">
                    <Clock className="h-5 w-5 text-amber-600" />
                  </div>
                </div>
                <p className="text-2xl font-semibold">{formatCurrency(summary.pending_payments)}</p>
                <div className="flex items-center mt-2 text-sm text-amber-600">
                  <span>{transactions.filter(t => t.type === 'payment' && t.status === 'pending').length} pending transactions</span>
                </div>
              </div>

              <div className="bg-white border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-sm font-medium text-gray-500">Completed Payments</h2>
                  <div className="w-10 h-10 bg-blue-50 border border-blue-200 flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <p className="text-2xl font-semibold">{formatCurrency(summary.completed_payments)}</p>
                <div className="flex items-center mt-2 text-sm text-blue-600">
                  <span>{transactions.filter(t => t.type === 'payment' && t.status === 'completed').length} completed transactions</span>
                </div>
              </div>

              <div className="bg-white border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-sm font-medium text-gray-500">Total Payouts</h2>
                  <div className="w-10 h-10 bg-purple-50 border border-purple-200 flex items-center justify-center">
                    <CreditCard className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <p className="text-2xl font-semibold">{formatCurrency(summary.total_payouts)}</p>
                <div className="flex items-center mt-2 text-sm text-purple-600">
                  <TrendingDown className="h-4 w-4 mr-1" />
                  <span>-{formatCurrency(summary.total_payouts)} this period</span>
                </div>
              </div>

              <div className="bg-white border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-sm font-medium text-gray-500">Pending Payouts</h2>
                  <div className="w-10 h-10 bg-red-50 border border-red-200 flex items-center justify-center">
                    <Clock className="h-5 w-5 text-red-600" />
                  </div>
                </div>
                <p className="text-2xl font-semibold">{formatCurrency(summary.pending_payouts)}</p>
                <div className="flex items-center mt-2 text-sm text-red-600">
                  <span>{transactions.filter(t => t.type === 'payout' && t.status === 'pending').length} pending payouts</span>
                </div>
              </div>
            </div>
          )}

          {/* Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-white border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Revenue Trend</h2>
                <BarChart3 className="h-5 w-5 text-gray-400" />
              </div>
              <div className="h-64 flex items-center justify-center">
                {revenueChartData ? (
                  <p className="text-gray-500">Chart would be rendered here</p>
                ) : (
                  <p className="text-gray-500">No data available</p>
                )}
              </div>
            </div>

            <div className="bg-white border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Payout Trend</h2>
                <PieChart className="h-5 w-5 text-gray-400" />
              </div>
              <div className="h-64 flex items-center justify-center">
                {payoutChartData ? (
                  <p className="text-gray-500">Chart would be rendered here</p>
                ) : (
                  <p className="text-gray-500">No data available</p>
                )}
              </div>
            </div>
          </div>

          {/* Transactions */}
          <div className="bg-white border border-gray-200 mb-6">
            <div className="p-6 border-b border-gray-200">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <h2 className="text-lg font-semibold">Recent Transactions</h2>
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search transactions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 p-2 border border-gray-300 w-full focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Filter className="h-5 w-5 text-gray-400" />
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    >
                      <option value="all">All Statuses</option>
                      <option value="pending">Pending</option>
                      <option value="completed">Completed</option>
                      <option value="failed">Failed</option>
                    </select>

                    <select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      className="p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    >
                      <option value="all">All Types</option>
                      <option value="payment">Payments</option>
                      <option value="payout">Payouts</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transaction
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Project
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client/Designer
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTransactions.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-12 text-center text-gray-500">
                        No transactions found matching your criteria
                      </td>
                    </tr>
                  ) : (
                    filteredTransactions.slice(0, 10).map((transaction) => (
                      <motion.tr
                        key={transaction.id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3 }}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatDate(transaction.created_at)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {transaction.type === 'payment' ? 'Payment' : 'Payout'}
                          </div>
                          <div className="text-xs text-gray-500">
                            {transaction.transaction_id.substring(0, 8)}...
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{transaction.project_title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {transaction.type === 'payment' ? transaction.client_name : transaction.designer_name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {transaction.type === 'payment' ? 'Client' : 'Designer'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatCurrency(transaction.amount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(transaction.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link href={`/admin/finance/transactions/${transaction.id}`}>
                            <Button variant="ghost" size="sm" className="text-brown-600 hover:text-brown-800">
                              Details <ArrowRight className="h-4 w-4 ml-1" />
                            </Button>
                          </Link>
                        </td>
                      </motion.tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {filteredTransactions.length > 10 && (
              <div className="p-4 border-t border-gray-200 text-center">
                <Link href="/admin/finance/transactions">
                  <Button variant="ghost" className="text-brown-600 hover:text-brown-800">
                    View All Transactions <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Quick Links */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link href="/admin/finance/payouts">
              <div className="bg-white border border-gray-200 p-6 hover:border-brown-300 transition-colors">
                <h3 className="font-semibold mb-2">Process Payouts</h3>
                <p className="text-sm text-gray-500 mb-4">
                  Review and process pending payouts to designers
                </p>
                <div className="flex justify-end">
                  <ArrowRight className="h-5 w-5 text-brown-600" />
                </div>
              </div>
            </Link>

            <Link href="/admin/finance/reports">
              <div className="bg-white border border-gray-200 p-6 hover:border-brown-300 transition-colors">
                <h3 className="font-semibold mb-2">Financial Reports</h3>
                <p className="text-sm text-gray-500 mb-4">
                  Generate detailed financial reports and analytics
                </p>
                <div className="flex justify-end">
                  <ArrowRight className="h-5 w-5 text-brown-600" />
                </div>
              </div>
            </Link>

            <Link href="/admin/finance/settings">
              <div className="bg-white border border-gray-200 p-6 hover:border-brown-300 transition-colors">
                <h3 className="font-semibold mb-2">Payment Settings</h3>
                <p className="text-sm text-gray-500 mb-4">
                  Configure payment methods and processing options
                </p>
                <div className="flex justify-end">
                  <ArrowRight className="h-5 w-5 text-brown-600" />
                </div>
              </div>
            </Link>
          </div>
        </>
      )}
    </div>
  );
}