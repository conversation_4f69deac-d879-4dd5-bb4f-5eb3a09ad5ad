-- =====================================================
-- SCRIPT 18: VERIFY AND FIX RELATIONSHIPS
-- =====================================================

-- 1. Check current structure of paypal_escrow_releases
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'paypal_escrow_releases'
ORDER BY ordinal_position;

-- 2. Check foreign key constraints
SELECT tc.constraint_name, tc.table_name, kcu.column_name, 
       ccu.table_name AS foreign_table_name,
       ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name = 'paypal_escrow_releases';

-- 3. Force add the foreign key if it doesn't exist
DO $$
BEGIN
    -- Check if escrow_hold_id column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'paypal_escrow_releases' 
        AND column_name = 'escrow_hold_id'
    ) THEN
        ALTER TABLE paypal_escrow_releases 
        ADD COLUMN escrow_hold_id UUID;
        
        RAISE NOTICE 'Added escrow_hold_id column';
    ELSE
        RAISE NOTICE 'escrow_hold_id column already exists';
    END IF;
    
    -- Check if foreign key constraint exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'paypal_escrow_releases' 
        AND constraint_type = 'FOREIGN KEY'
        AND constraint_name LIKE '%escrow_hold_id%'
    ) THEN
        ALTER TABLE paypal_escrow_releases 
        ADD CONSTRAINT paypal_escrow_releases_escrow_hold_id_fkey 
        FOREIGN KEY (escrow_hold_id) REFERENCES paypal_escrow_holds(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key constraint';
    ELSE
        RAISE NOTICE 'Foreign key constraint already exists';
    END IF;
END $$;

-- 4. Create missing manager_dashboard_cache table
CREATE TABLE IF NOT EXISTS manager_dashboard_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    manager_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    cache_data JSONB NOT NULL DEFAULT '{}',
    cache_type TEXT NOT NULL DEFAULT 'dashboard',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '1 hour'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Disable RLS on all problematic tables (force disable)
ALTER TABLE platform_fee_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE paypal_escrow_holds DISABLE ROW LEVEL SECURITY;
ALTER TABLE paypal_escrow_releases DISABLE ROW LEVEL SECURITY;
ALTER TABLE platform_revenue DISABLE ROW LEVEL SECURITY;
ALTER TABLE designer_payouts DISABLE ROW LEVEL SECURITY;
ALTER TABLE payment_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE manager_dashboard_cache DISABLE ROW LEVEL SECURITY;

-- 6. Grant full permissions to authenticated users (temporary)
GRANT ALL ON platform_fee_settings TO authenticated;
GRANT ALL ON paypal_escrow_holds TO authenticated;
GRANT ALL ON paypal_escrow_releases TO authenticated;
GRANT ALL ON platform_revenue TO authenticated;
GRANT ALL ON designer_payouts TO authenticated;
GRANT ALL ON payment_settings TO authenticated;
GRANT ALL ON manager_dashboard_cache TO authenticated;

-- 7. Create indexes
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_escrow_hold_id ON paypal_escrow_releases(escrow_hold_id);
CREATE INDEX IF NOT EXISTS idx_manager_dashboard_cache_manager_id ON manager_dashboard_cache(manager_id);
CREATE INDEX IF NOT EXISTS idx_manager_dashboard_cache_expires_at ON manager_dashboard_cache(expires_at);

-- 8. Verify the relationship works
SELECT 'Testing relationship...' as status;

-- Try to query the relationship
SELECT COUNT(*) as escrow_releases_count
FROM paypal_escrow_releases per
LEFT JOIN paypal_escrow_holds peh ON per.escrow_hold_id = peh.id;

SELECT 'Script 18 completed: Relationships verified and fixed' as status;
