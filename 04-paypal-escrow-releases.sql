-- =====================================================
-- SCRIPT 4: CREATE PAYPAL ESCROW RELEASES TABLE
-- =====================================================

-- PayPal Escrow Releases Table
CREATE TABLE IF NOT EXISTS paypal_escrow_releases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  escrow_hold_id UUID REFERENCES paypal_escrow_holds(id) ON DELETE CASCADE,
  release_amount DECIMAL(10,2) NOT NULL,
  release_type TEXT NOT NULL DEFAULT 'milestone_completion' CHECK (
    release_type IN ('milestone_completion', 'project_completion', 'partial_release')
  ),
  requested_by UUID REFERENCES profiles(id) ON DELETE CASCADE,
  manager_approval_status TEXT NOT NULL DEFAULT 'pending' CHECK (
    manager_approval_status IN ('pending', 'approved', 'rejected', 'not_required')
  ),
  quality_approval_status TEXT NOT NULL DEFAULT 'not_required' CHECK (
    quality_approval_status IN ('pending', 'approved', 'rejected', 'not_required')
  ),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (
    status IN ('pending', 'approved', 'processed', 'rejected')
  ),
  notes TEXT,
  manager_approved_at TIMESTAMP WITH TIME ZONE,
  manager_notes TEXT,
  quality_approved_at TIMESTAMP WITH TIME ZONE,
  quality_approved_by UUID REFERENCES profiles(id),
  quality_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  CONSTRAINT positive_release_amount CHECK (release_amount > 0)
);

-- Verify completion
SELECT 'Script 4 completed: PayPal escrow releases table created' as status;
