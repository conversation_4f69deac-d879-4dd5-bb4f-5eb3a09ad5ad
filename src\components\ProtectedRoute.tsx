"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  allowedRoles = ['client', 'designer', 'admin'],
  redirectTo 
}: ProtectedRouteProps) {
  const { user, profile, loading } = useOptimizedAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user || !profile) {
        // No user or profile, redirect to login with current path
        const currentPath = window.location.pathname;
        const loginUrl = `/auth/login?redirectedFrom=${encodeURIComponent(currentPath)}`;
        window.location.href = loginUrl; // Use window.location.href to avoid routing loops
        return;
      }

      if (!allowedRoles.includes(profile.role)) {
        // User doesn't have permission, redirect to their dashboard or specified path
        if (redirectTo) {
          window.location.href = redirectTo;
        } else {
          switch (profile.role) {
            case 'admin':
              window.location.href = '/admin/dashboard';
              break;
            case 'designer':
              window.location.href = '/designer/dashboard';
              break;
            case 'client':
              window.location.href = '/client/dashboard';
              break;
            case 'quality_team':
              window.location.href = '/quality/dashboard';
              break;
            case 'manager':
              window.location.href = '/manager/dashboard';
              break;
            default:
              window.location.href = '/auth/login?error=access_denied';
          }
        }
        return;
      }
    }
  }, [user, profile, loading, allowedRoles, redirectTo]);

  // Show loading while checking auth
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!user || !profile || !allowedRoles.includes(profile.role)) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  // User is authenticated and authorized
  return <>{children}</>;
}
