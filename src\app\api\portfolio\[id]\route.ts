import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/portfolio/[id]
 * Gets a specific portfolio project by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the project with images and tags
    const { data: project, error: projectError } = await supabase
      .from('portfolio_projects')
      .select(`
        *,
        images:portfolio_images(*),
        tags:portfolio_project_tags(
          tag:portfolio_tags(*)
        )
      `)
      .eq('id', projectId)
      .single();
    
    if (projectError) {
      return NextResponse.json(
        { error: 'Portfolio project not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to view this project
    const isAdmin = profile.role === 'admin';
    const isOwner = project.designer_id === user.id;
    
    if (!isAdmin && !isOwner && profile.role !== 'client') {
      return NextResponse.json(
        { error: 'You do not have permission to view this project' },
        { status: 403 }
      );
    }
    
    // Process tags to make them easier to work with
    const processedTags = project.tags.map(tagObj => tagObj.tag);
    
    // Find the cover image
    const images = project.images || [];
    const coverImage = images.find(img => img.is_cover);
    
    // Return the processed project
    const processedProject = {
      ...project,
      tags: processedTags,
      cover_image: coverImage ? coverImage.image_url : (images.length > 0 ? images[0].image_url : null)
    };
    
    return NextResponse.json(processedProject, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/portfolio/[id]:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/portfolio/[id]
 * Updates a portfolio project
 * 
 * Request body:
 * {
 *   title?: string;
 *   description?: string | null;
 *   category?: string | null;
 *   client_name?: string | null;
 *   completion_date?: string | null;
 *   featured?: boolean;
 * }
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the project to check ownership
    const { data: project, error: projectError } = await supabase
      .from('portfolio_projects')
      .select('designer_id')
      .eq('id', projectId)
      .single();
    
    if (projectError) {
      return NextResponse.json(
        { error: 'Portfolio project not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to update this project
    const isAdmin = profile.role === 'admin';
    const isOwner = project.designer_id === user.id;
    
    if (!isAdmin && !isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to update this project' },
        { status: 403 }
      );
    }
    
    const { title, description, category, client_name, completion_date, featured } = await request.json();
    
    // Prepare update data
    const updateData: any = {};
    
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (category !== undefined) updateData.category = category;
    if (client_name !== undefined) updateData.client_name = client_name;
    if (completion_date !== undefined) updateData.completion_date = completion_date;
    if (featured !== undefined) updateData.featured = featured;
    
    // Update the project
    const { data, error } = await supabase
      .from('portfolio_projects')
      .update(updateData)
      .eq('id', projectId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating portfolio project:', error);
      return NextResponse.json(
        { error: 'Failed to update portfolio project' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error in PATCH /api/portfolio/[id]:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/portfolio/[id]
 * Deletes a portfolio project
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the project to check ownership
    const { data: project, error: projectError } = await supabase
      .from('portfolio_projects')
      .select('designer_id')
      .eq('id', projectId)
      .single();
    
    if (projectError) {
      return NextResponse.json(
        { error: 'Portfolio project not found' },
        { status: 404 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to delete this project
    const isAdmin = profile.role === 'admin';
    const isOwner = project.designer_id === user.id;
    
    if (!isAdmin && !isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to delete this project' },
        { status: 403 }
      );
    }
    
    // Delete the project (cascade will handle images and tags)
    const { error } = await supabase
      .from('portfolio_projects')
      .delete()
      .eq('id', projectId);
    
    if (error) {
      console.error('Error deleting portfolio project:', error);
      return NextResponse.json(
        { error: 'Failed to delete portfolio project' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    console.error('Error in DELETE /api/portfolio/[id]:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
