-- Create interview_reminders table for scheduling 24-hour reminder emails
CREATE TABLE IF NOT EXISTS interview_reminders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  application_id UUID NOT NULL REFERENCES designer_applications(id) ON DELETE CASCADE,
  reminder_scheduled_at TIMESTAMPTZ NOT NULL,
  interview_scheduled_at TIMESTAMPTZ NOT NULL,
  applicant_email TEXT NOT NULL,
  applicant_name TEXT NOT NULL,
  interview_type TEXT NOT NULL,
  meeting_details TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
  sent_at TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for efficient querying of pending reminders
CREATE INDEX IF NOT EXISTS idx_interview_reminders_status_scheduled 
ON interview_reminders(status, reminder_scheduled_at) 
WHERE status = 'pending';

-- Create index for application lookups
CREATE INDEX IF NOT EXISTS idx_interview_reminders_application_id 
ON interview_reminders(application_id);

-- Add RLS policies
ALTER TABLE interview_reminders ENABLE ROW LEVEL SECURITY;

-- Policy for admins to manage all reminders
CREATE POLICY "Admins can manage interview reminders" ON interview_reminders
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_interview_reminders_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_interview_reminders_updated_at
  BEFORE UPDATE ON interview_reminders
  FOR EACH ROW
  EXECUTE FUNCTION update_interview_reminders_updated_at();

-- Add new columns to designer_applications table for interview details
ALTER TABLE designer_applications 
ADD COLUMN IF NOT EXISTS interview_type TEXT,
ADD COLUMN IF NOT EXISTS interview_duration INTEGER,
ADD COLUMN IF NOT EXISTS meeting_link TEXT,
ADD COLUMN IF NOT EXISTS phone_number TEXT,
ADD COLUMN IF NOT EXISTS interview_address TEXT,
ADD COLUMN IF NOT EXISTS interview_agenda TEXT,
ADD COLUMN IF NOT EXISTS interviewer_name TEXT,
ADD COLUMN IF NOT EXISTS interviewer_email TEXT,
ADD COLUMN IF NOT EXISTS interview_notes TEXT;
