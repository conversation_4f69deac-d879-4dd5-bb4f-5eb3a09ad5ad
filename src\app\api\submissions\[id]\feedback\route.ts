import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/submissions/[id]/feedback
 * Creates feedback for a submission
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const submissionId = params.id;
    const { content, is_approved, user_id, action } = await request.json();

    if (!content || !user_id) {
      return NextResponse.json(
        { error: 'Content and user_id are required' },
        { status: 400 }
      );
    }

    // Verify submission exists
    const { data: submission, error: submissionError } = await supabase
      .from('submissions')
      .select('*')
      .eq('id', submissionId)
      .single();

    if (submissionError || !submission) {
      return NextResponse.json(
        { error: 'Submission not found' },
        { status: 404 }
      );
    }

    // Create feedback record
    const { data: feedback, error: feedbackError } = await supabase
      .from('submission_feedback')
      .insert({
        submission_id: submissionId,
        content,
        is_approved: is_approved || false,
        user_id
      })
      .select()
      .single();

    if (feedbackError) {
      console.error('Error creating feedback:', feedbackError);
      return NextResponse.json(
        { error: 'Failed to create feedback' },
        { status: 500 }
      );
    }

    // Update submission status based on action
    let newStatus = submission.status;
    let revisionRequested = submission.revision_requested;

    if (action) {
      switch (action) {
        case 'approve':
          newStatus = 'approved';
          revisionRequested = false;
          break;
        case 'reject':
          newStatus = 'rejected';
          revisionRequested = false;
          break;
        case 'revision':
          newStatus = 'needs_revision';
          revisionRequested = true;
          break;
      }

      const { error: updateError } = await supabase
        .from('submissions')
        .update({
          status: newStatus,
          revision_requested: revisionRequested,
          feedback: content,
          updated_at: new Date().toISOString()
        })
        .eq('id', submissionId);

      if (updateError) {
        console.error('Error updating submission:', updateError);
        // Don't fail the request if this fails
      }
    }

    return NextResponse.json({
      success: true,
      feedback,
      message: 'Feedback created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error in submission feedback API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/submissions/[id]/feedback
 * Gets all feedback for a submission
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const submissionId = params.id;

    const { data: feedback, error } = await supabase
      .from('submission_feedback')
      .select(`
        *,
        user:profiles!user_id(full_name, role, avatar_url)
      `)
      .eq('submission_id', submissionId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching feedback:', error);
      return NextResponse.json(
        { error: 'Failed to fetch feedback' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      feedback: feedback || []
    }, { status: 200 });

  } catch (error) {
    console.error('Error in get submission feedback API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
