import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const { data, error } = await supabaseServerClient
      .from('admin_chat_status')
      .select('*')
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching admin status:', error);
      return NextResponse.json(
        { error: 'Failed to fetch admin status' },
        { status: 500 }
      );
    }

    // If no status exists, return default offline status
    if (!data) {
      return NextResponse.json({
        is_online: false,
        status_message: 'Available for chat',
        last_activity: new Date().toISOString()
      });
    }

    // Check if admin is actually online (active within last 5 minutes)
    const lastActivity = new Date(data.last_activity);
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const isActuallyOnline = data.is_online && lastActivity > fiveMinutesAgo;

    return NextResponse.json({
      is_online: isActuallyOnline,
      status_message: data.status_message,
      last_activity: data.last_activity
    });

  } catch (error) {
    console.error('Error in admin status GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { is_online, status_message, admin_id } = await request.json();

    if (!admin_id) {
      return NextResponse.json(
        { error: 'Admin ID is required' },
        { status: 400 }
      );
    }

    // Verify admin exists
    const { data: adminProfile, error: adminError } = await supabaseServerClient
      .from('profiles')
      .select('id, role')
      .eq('id', admin_id)
      .eq('role', 'admin')
      .single();

    if (adminError || !adminProfile) {
      return NextResponse.json(
        { error: 'Admin not found or insufficient permissions' },
        { status: 403 }
      );
    }

    // Update or create admin status
    const { data, error } = await supabaseServerClient
      .from('admin_chat_status')
      .upsert({
        admin_id: admin_id,
        is_online: is_online ?? true,
        status_message: status_message ?? 'Available for chat',
        last_activity: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating admin status:', error);
      return NextResponse.json(
        { error: 'Failed to update admin status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data
    });

  } catch (error) {
    console.error('Error in admin status POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { admin_id } = await request.json();

    if (!admin_id) {
      return NextResponse.json(
        { error: 'Admin ID is required' },
        { status: 400 }
      );
    }

    // Update last activity timestamp
    const { data, error } = await supabaseServerClient
      .from('admin_chat_status')
      .update({
        last_activity: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('admin_id', admin_id)
      .select()
      .single();

    if (error) {
      console.error('Error updating admin activity:', error);
      return NextResponse.json(
        { error: 'Failed to update admin activity' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data
    });

  } catch (error) {
    console.error('Error in admin activity PUT API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
