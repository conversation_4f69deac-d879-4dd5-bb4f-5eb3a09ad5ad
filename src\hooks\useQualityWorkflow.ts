import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useOptimizedAuth } from './useOptimizedAuth';

interface QualityReview {
  id: string;
  project_id: string;
  submission_id?: string;
  milestone_id?: string;
  reviewer_id?: string;
  designer_id: string;
  review_type: string;
  status: 'pending' | 'in_review' | 'approved' | 'needs_revision' | 'rejected';
  overall_score?: number;
  feedback?: string;
  revision_notes?: string;
  revision_count: number;
  sla_deadline?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

interface QualityStandard {
  id: string;
  category: string;
  standard_name: string;
  description: string;
  criteria: string[];
  is_mandatory: boolean;
  weight: number;
}

interface ProjectSubmission {
  id: string;
  project_id: string;
  milestone_id?: string;
  designer_id: string;
  submission_type: string;
  status: string;
  files?: any[];
  description?: string;
  submitted_at: string;
}

export function useQualityWorkflow() {
  const { user, profile } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create submission when milestone is completed
  const createSubmissionForMilestone = async (milestoneId: string, projectId: string, designerId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Create submission record
      const { data: submission, error: submissionError } = await supabase
        .from('project_submissions')
        .insert({
          project_id: projectId,
          milestone_id: milestoneId,
          designer_id: designerId,
          submission_type: 'milestone',
          status: 'submitted',
          description: 'Milestone completion submission'
        })
        .select()
        .single();

      if (submissionError) throw submissionError;

      // Create quality review
      const { data: review, error: reviewError } = await supabase
        .from('quality_reviews_new')
        .insert({
          project_id: projectId,
          submission_id: submission.id,
          milestone_id: milestoneId,
          designer_id: designerId,
          review_type: 'submission',
          status: 'pending'
        })
        .select()
        .single();

      if (reviewError) throw reviewError;

      return { submission, review };
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create submission');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Submit quality review
  const submitQualityReview = async (
    reviewId: string,
    reviewData: {
      status: 'approved' | 'needs_revision' | 'rejected';
      overall_score?: number;
      feedback?: string;
      revision_notes?: string;
      standards_feedback?: Array<{
        standard_id: string;
        passed: boolean;
        score: number;
        comments?: string;
        suggestions?: string;
      }>;
      time_spent_minutes?: number;
    }
  ) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('Authentication required');

      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error('No access token available');
      }

      const response = await fetch(`/api/quality/reviews/${reviewId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reviewData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit review');
      }

      const updatedReview = await response.json();
      return updatedReview;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit review');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get quality reviews for current user
  const getQualityReviews = async (filters?: {
    status?: string;
    project_id?: string;
    limit?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('Authentication required');

      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error('No access token available');
      }

      const params = new URLSearchParams();
      if (filters?.status) params.append('status', filters.status);
      if (filters?.project_id) params.append('project_id', filters.project_id);
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/quality/reviews?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch reviews');
      }

      const data = await response.json();
      return data.reviews;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch reviews');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get quality standards
  const getQualityStandards = async (category?: string) => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('quality_standards')
        .select('*')
        .order('weight', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data as QualityStandard[];
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch standards');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Check if project requires quality review
  const checkQualityRequirement = async (projectId: string) => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('requires_quality_review, quality_status')
        .eq('id', projectId)
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check quality requirement');
      return null;
    }
  };

  // Trigger quality review for milestone completion
  const triggerQualityReview = async (milestoneId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Get milestone details
      const { data: milestone, error: milestoneError } = await supabase
        .from('project_milestones')
        .select(`
          id,
          project_id,
          title,
          projects (
            id,
            designer_id,
            requires_quality_review
          )
        `)
        .eq('id', milestoneId)
        .single();

      if (milestoneError) throw milestoneError;

      // Check if quality review is required
      if (!milestone.projects.requires_quality_review) {
        return null; // No quality review needed
      }

      // Create submission and quality review
      const result = await createSubmissionForMilestone(
        milestoneId,
        milestone.project_id,
        milestone.projects.designer_id
      );

      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger quality review');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    createSubmissionForMilestone,
    submitQualityReview,
    getQualityReviews,
    getQualityStandards,
    checkQualityRequirement,
    triggerQualityReview
  };
}
