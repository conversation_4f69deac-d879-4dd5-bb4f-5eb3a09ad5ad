-- Enhanced Payment System Schema
-- This migration adds all missing pieces for a complete payment system

-- 1. Designer Stripe Connect Accounts
CREATE TABLE IF NOT EXISTS designer_stripe_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    designer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    stripe_account_id TEXT UNIQUE NOT NULL,
    account_status TEXT DEFAULT 'pending' CHECK (account_status IN ('pending', 'active', 'restricted', 'inactive')),
    country TEXT DEFAULT 'US',
    business_type TEXT DEFAULT 'individual' CHECK (business_type IN ('individual', 'company')),
    capabilities JSONB DEFAULT '{}',
    requirements JSONB DEFAULT '{}',
    onboarding_completed BOOLEAN DEFAULT FALSE,
    payouts_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(designer_id)
);

-- 2. Platform Fee Settings
CREATE TABLE IF NOT EXISTS platform_fee_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    platform_commission_rate DECIMAL(5,2) DEFAULT 15.00, -- 15%
    payment_processing_fee DECIMAL(5,2) DEFAULT 2.90,    -- 2.9%
    designer_payout_rate DECIMAL(5,2) DEFAULT 82.10,     -- Calculated: 100 - 15 - 2.9
    minimum_project_value DECIMAL(10,2) DEFAULT 100.00,
    maximum_commission_cap DECIMAL(10,2), -- Optional cap on platform fees
    minimum_payout_amount DECIMAL(10,2) DEFAULT 50.00,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES profiles(id)
);

-- 3. Platform Settings
CREATE TABLE IF NOT EXISTS platform_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    enable_automatic_payouts BOOLEAN DEFAULT TRUE,
    payout_schedule TEXT DEFAULT 'weekly' CHECK (payout_schedule IN ('daily', 'weekly', 'monthly')),
    payout_day_of_week INTEGER DEFAULT 1, -- Monday = 1, Sunday = 7
    payout_day_of_month INTEGER DEFAULT 1, -- For monthly payouts
    require_milestone_approval BOOLEAN DEFAULT TRUE,
    minimum_payout_amount DECIMAL(10,2) DEFAULT 50.00,
    payout_processing_time TEXT DEFAULT '2-3 business days',
    auto_approve_milestones BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES profiles(id)
);

-- 4. Designer Payout Queue
CREATE TABLE IF NOT EXISTS designer_payout_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    designer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    transaction_id UUID REFERENCES transactions(id),
    milestone_id UUID, -- References project_milestones
    amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'processed', 'failed', 'cancelled')),
    payout_transaction_id UUID REFERENCES transactions(id),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Platform Revenue Tracking
CREATE TABLE IF NOT EXISTS platform_revenue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID REFERENCES transactions(id),
    revenue_type TEXT NOT NULL CHECK (revenue_type IN ('platform_fee', 'processing_fee', 'subscription', 'other')),
    amount DECIMAL(10,2) NOT NULL,
    project_id UUID REFERENCES projects(id),
    designer_id UUID REFERENCES profiles(id),
    client_id UUID REFERENCES profiles(id),
    date_recorded TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT
);

-- 6. Payout Batch Processing
CREATE TABLE IF NOT EXISTS payout_batches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id TEXT UNIQUE NOT NULL,
    provider TEXT NOT NULL, -- 'stripe', 'paypal', 'manual'
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    total_amount DECIMAL(10,2) NOT NULL,
    total_fee DECIMAL(10,2) DEFAULT 0.00,
    designer_count INTEGER DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    processed_by UUID REFERENCES profiles(id),
    processed_at TIMESTAMP WITH TIME ZONE,
    external_batch_id TEXT, -- Provider's batch ID
    failure_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Payout Batch Items
CREATE TABLE IF NOT EXISTS payout_batch_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID REFERENCES payout_batches(id) ON DELETE CASCADE,
    designer_id UUID REFERENCES profiles(id),
    queue_item_id UUID REFERENCES designer_payout_queue(id),
    amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    external_transfer_id TEXT, -- Stripe transfer ID, PayPal payout ID, etc.
    failure_reason TEXT,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- 8. Enhanced Transactions Table Updates
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS platform_fee DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS processing_fee DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS designer_amount DECIMAL(10,2);
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS fee_processed BOOLEAN DEFAULT FALSE;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS fee_processed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS stripe_transfer_id TEXT;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payout_method TEXT;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS parent_transaction_id UUID REFERENCES transactions(id);
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS batch_id UUID REFERENCES payout_batches(id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_designer_stripe_accounts_designer_id ON designer_stripe_accounts(designer_id);
CREATE INDEX IF NOT EXISTS idx_designer_stripe_accounts_stripe_id ON designer_stripe_accounts(stripe_account_id);
CREATE INDEX IF NOT EXISTS idx_payout_queue_designer_id ON designer_payout_queue(designer_id);
CREATE INDEX IF NOT EXISTS idx_payout_queue_status ON designer_payout_queue(status);
CREATE INDEX IF NOT EXISTS idx_payout_queue_scheduled_for ON designer_payout_queue(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_platform_revenue_date ON platform_revenue(date_recorded);
CREATE INDEX IF NOT EXISTS idx_platform_revenue_type ON platform_revenue(revenue_type);
CREATE INDEX IF NOT EXISTS idx_transactions_fee_processed ON transactions(fee_processed);
CREATE INDEX IF NOT EXISTS idx_transactions_platform_fee ON transactions(platform_fee);

-- Insert default platform fee settings (only if table is empty)
INSERT INTO platform_fee_settings (
    platform_commission_rate,
    payment_processing_fee,
    designer_payout_rate,
    minimum_project_value,
    minimum_payout_amount
)
SELECT
    15.00,  -- 15% platform commission
    2.90,   -- 2.9% payment processing
    82.10,  -- 82.1% to designer
    100.00, -- $100 minimum project
    50.00   -- $50 minimum payout
WHERE NOT EXISTS (SELECT 1 FROM platform_fee_settings);

-- Insert default platform settings (only if table is empty)
INSERT INTO platform_settings (
    enable_automatic_payouts,
    payout_schedule,
    payout_day_of_week,
    require_milestone_approval,
    minimum_payout_amount,
    auto_approve_milestones
)
SELECT
    TRUE,     -- Enable automatic payouts
    'weekly', -- Weekly schedule
    1,        -- Monday
    TRUE,     -- Require milestone approval
    50.00,    -- $50 minimum payout
    FALSE     -- Don't auto-approve milestones
WHERE NOT EXISTS (SELECT 1 FROM platform_settings);

-- Functions for automatic fee calculation
CREATE OR REPLACE FUNCTION calculate_platform_fees(gross_amount DECIMAL)
RETURNS TABLE(
    platform_fee DECIMAL,
    processing_fee DECIMAL,
    designer_amount DECIMAL
) AS $$
DECLARE
    settings RECORD;
BEGIN
    -- Get current fee settings
    SELECT * INTO settings FROM platform_fee_settings ORDER BY updated_at DESC LIMIT 1;
    
    IF settings IS NULL THEN
        -- Use default values if no settings found
        platform_fee := gross_amount * 0.15;  -- 15%
        processing_fee := gross_amount * 0.029; -- 2.9%
    ELSE
        platform_fee := gross_amount * (settings.platform_commission_rate / 100);
        processing_fee := gross_amount * (settings.payment_processing_fee / 100);
        
        -- Apply maximum commission cap if set
        IF settings.maximum_commission_cap IS NOT NULL AND platform_fee > settings.maximum_commission_cap THEN
            platform_fee := settings.maximum_commission_cap;
        END IF;
    END IF;
    
    designer_amount := gross_amount - platform_fee - processing_fee;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Function to queue designer payout
CREATE OR REPLACE FUNCTION queue_designer_payout(
    p_designer_id UUID,
    p_transaction_id UUID,
    p_milestone_id UUID,
    p_amount DECIMAL
)
RETURNS UUID AS $$
DECLARE
    queue_id UUID;
    settings RECORD;
    scheduled_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get platform settings
    SELECT * INTO settings FROM platform_settings ORDER BY updated_at DESC LIMIT 1;
    
    -- Calculate next payout date based on schedule
    IF settings.payout_schedule = 'daily' THEN
        scheduled_date := CURRENT_DATE + INTERVAL '1 day';
    ELSIF settings.payout_schedule = 'weekly' THEN
        -- Schedule for next occurrence of payout_day_of_week
        scheduled_date := date_trunc('week', CURRENT_DATE) + 
                         INTERVAL '1 week' + 
                         (settings.payout_day_of_week - 1) * INTERVAL '1 day';
    ELSE -- monthly
        -- Schedule for payout_day_of_month of next month
        scheduled_date := date_trunc('month', CURRENT_DATE) + 
                         INTERVAL '1 month' + 
                         (settings.payout_day_of_month - 1) * INTERVAL '1 day';
    END IF;
    
    -- Insert into payout queue
    INSERT INTO designer_payout_queue (
        designer_id,
        transaction_id,
        milestone_id,
        amount,
        scheduled_for
    ) VALUES (
        p_designer_id,
        p_transaction_id,
        p_milestone_id,
        p_amount,
        scheduled_date
    ) RETURNING id INTO queue_id;
    
    RETURN queue_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get designers ready for payout
CREATE OR REPLACE FUNCTION get_designers_ready_for_payout()
RETURNS TABLE(
    designer_id UUID,
    total_amount DECIMAL,
    item_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dpq.designer_id,
        SUM(dpq.amount) as total_amount,
        COUNT(*)::INTEGER as item_count
    FROM designer_payout_queue dpq
    JOIN designer_stripe_accounts dsa ON dpq.designer_id = dsa.designer_id
    WHERE dpq.status = 'pending'
    AND dpq.scheduled_for <= NOW()
    AND dsa.account_status = 'active'
    AND dsa.payouts_enabled = TRUE
    GROUP BY dpq.designer_id
    HAVING SUM(dpq.amount) >= (
        SELECT minimum_payout_amount 
        FROM platform_settings 
        ORDER BY updated_at DESC 
        LIMIT 1
    );
END;
$$ LANGUAGE plpgsql;

-- RLS Policies
ALTER TABLE designer_stripe_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_fee_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE designer_payout_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_revenue ENABLE ROW LEVEL SECURITY;
ALTER TABLE payout_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE payout_batch_items ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Designers can view own stripe accounts" ON designer_stripe_accounts;
DROP POLICY IF EXISTS "Designers can view own payout queue" ON designer_payout_queue;
DROP POLICY IF EXISTS "Admins can view all payment data" ON designer_stripe_accounts;
DROP POLICY IF EXISTS "Admins can manage fee settings" ON platform_fee_settings;
DROP POLICY IF EXISTS "Admins can manage platform settings" ON platform_settings;
DROP POLICY IF EXISTS "Admins can view all payout data" ON designer_payout_queue;
DROP POLICY IF EXISTS "Admins can view platform revenue" ON platform_revenue;
DROP POLICY IF EXISTS "Admins can manage payout batches" ON payout_batches;
DROP POLICY IF EXISTS "Admins can manage payout batch items" ON payout_batch_items;

-- Designers can view their own Stripe accounts
CREATE POLICY "Designers can view own stripe accounts" ON designer_stripe_accounts
    FOR SELECT USING (designer_id = auth.uid());

-- Designers can view their own payout queue
CREATE POLICY "Designers can view own payout queue" ON designer_payout_queue
    FOR SELECT USING (designer_id = auth.uid());

-- Admins can view everything
CREATE POLICY "Admins can view all payment data" ON designer_stripe_accounts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage fee settings" ON platform_fee_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage platform settings" ON platform_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can view all payout data" ON designer_payout_queue
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can view platform revenue" ON platform_revenue
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage payout batches" ON payout_batches
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can manage payout batch items" ON payout_batch_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON designer_stripe_accounts TO authenticated;
GRANT SELECT ON platform_fee_settings TO authenticated;
GRANT SELECT ON platform_settings TO authenticated;
GRANT SELECT ON designer_payout_queue TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
