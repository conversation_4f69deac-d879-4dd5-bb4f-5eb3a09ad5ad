'use client';

import { useState, useRef, ReactNode } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { toast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/lib/supabase';

interface FileUploaderProps {
  children: ReactNode;
  onUploadComplete: (url: string, fileName: string, fileType: string) => void;
  allowedFileTypes?: string[];
  maxSizeMB?: number;
  bucketName?: string;
  folderPath?: string;
}

export function FileUploader({
  children,
  onUploadComplete,
  allowedFileTypes = ['*'],
  maxSizeMB = 5,
  bucketName = 'dispute-attachments',
  folderPath = '',
}: FileUploaderProps) {
  const { user } = useOptimizedAuth();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      toast({
        title: 'File too large',
        description: `File size must be less than ${maxSizeMB}MB`,
        variant: 'destructive',
      });
      return;
    }

    // Check file type if specific types are allowed
    if (allowedFileTypes[0] !== '*') {
      const fileTypeMatches = allowedFileTypes.some(type => {
        if (type.startsWith('.')) {
          // Check file extension
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        } else {
          // Check MIME type
          return type.includes('*')
            ? file.type.startsWith(type.split('*')[0])
            : file.type === type;
        }
      });

      if (!fileTypeMatches) {
        toast({
          title: 'Invalid file type',
          description: `Allowed file types: ${allowedFileTypes.join(', ')}`,
          variant: 'destructive',
        });
        return;
      }
    }

    setUploading(true);
    setProgress(0);

    try {
      // Generate a unique file name
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          onUploadProgress: (progress) => {
            const percent = Math.round((progress.loaded / progress.total) * 100);
            setProgress(percent);
          },
        });

      if (error) {
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(data.path);

      onUploadComplete(publicUrl, file.name, file.type);

      toast({
        title: 'File uploaded',
        description: 'Your file has been uploaded successfully',
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload file',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
      setProgress(0);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        accept={allowedFileTypes[0] === '*' ? undefined : allowedFileTypes.join(',')}
        disabled={uploading}
      />
      <div onClick={handleClick}>
        {children}
      </div>
      {uploading && (
        <div className="mt-2">
          <Progress value={progress} className="h-2" />
          <p className="text-xs text-center mt-1">{progress}% uploaded</p>
        </div>
      )}
    </div>
  );
}
