import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenAI, Modality, HarmBlockThreshold, HarmCategory } from '@google/genai';
import { Buffer } from 'buffer'; // Import Buffer for base64 encoding/decoding

// Initialize the Google Generative AI client
const apiKey = process.env.GOOGLE_API_KEY;
const enableAiGeneration = process.env.ENABLE_AI_GENERATION === 'true';
const maxDailyRequests = parseInt(process.env.MAX_DAILY_AI_REQUESTS || '100', 10);

// --- IMPORTANT: In-memory storage ---
// This rate limit and cache storage is IN-MEMORY and will reset
// whenever your Next.js server restarts (e.g., during development, deployment).
// For a production application, you MUST use a persistent storage
// like a database (PostgreSQL, MongoDB), Redis, or a dedicated
// rate-limiting service.
// -----------------------------------
interface RateLimitStorage {
  count: number;
  lastReset: Date;
}

const rateLimitStorage: RateLimitStorage = {
  count: 0,
  lastReset: new Date()
};

interface ImageCache {
  [key: string]: {
    imageData: string;
    timestamp: Date;
  };
}

const imageCache: ImageCache = {};

// Cache expiration time (24 hours)
const CACHE_EXPIRATION_MS = 24 * 60 * 60 * 1000;

// Function to check and update rate limits
function checkRateLimit(): boolean {
  const now = new Date();
  // Create a Date object representing the start of today in local time
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  // Reset counter if it's a new day
  // Use getTime() for reliable comparison
  if (rateLimitStorage.lastReset.getTime() < today.getTime()) {
    console.log('[API Route] Resetting daily rate limit counter.');
    rateLimitStorage.count = 0;
    rateLimitStorage.lastReset = now; // Store the time of the reset
  }

  // Check if we've exceeded the daily limit
  if (rateLimitStorage.count >= maxDailyRequests) {
    console.warn(`[API Route] Daily rate limit (${maxDailyRequests}) exceeded. Current count: ${rateLimitStorage.count}`);
    return false;
  }

  // Increment the counter
  rateLimitStorage.count++;
  console.log(`[API Route] Rate limit check passed. Current daily count: ${rateLimitStorage.count}/${maxDailyRequests}`);
  return true;
}

// Function to generate a cache key from the prompt
function generateCacheKey(prompt: string): string {
   // Using URL-safe base64 might be better if storing in URLs, but standard is fine for keys
  return Buffer.from(prompt).toString('base64');
}

// Function to check if a cached image exists and is valid
function getCachedImage(prompt: string): string | null {
  const cacheKey = generateCacheKey(prompt);
  const cachedItem = imageCache[cacheKey];

  if (!cachedItem) {
    console.log('[API Route] Cache miss for prompt:', prompt);
    return null;
  }

  // Check if the cached item has expired
  const now = new Date();
  if (now.getTime() - cachedItem.timestamp.getTime() > CACHE_EXPIRATION_MS) {
    console.log('[API Route] Cached item expired for prompt:', prompt);
    delete imageCache[cacheKey]; // Remove expired item
    return null;
  }

  console.log('[API Route] Cache hit for prompt:', prompt);
  return cachedItem.imageData;
}

// Function to cache an image
function cacheImage(prompt: string, imageData: string): void {
  const cacheKey = generateCacheKey(prompt);
  imageCache[cacheKey] = {
    imageData,
    timestamp: new Date()
  };
  console.log('[API Route] Cached image for prompt:', prompt);
}

// Add a helper function to validate base64 data (as included in your code)
function isValidBase64(str: string): boolean {
  if (typeof str !== 'string') return false;
  // Basic validation: check if the string contains only valid base64 characters
  // and has a reasonable length for an image. Base64 strings length must be a multiple of 4.
  const base64Regex = /^[A-Za-z0-9+/=]*$/; // Updated regex to include trailing '='
  return base64Regex.test(str) && str.length > 100 && str.length % 4 === 0;
}


// Function to generate an image using Google Gemini AI
async function generateImage(prompt: string): Promise<{ imageData: string; textDescription?: string }> {
  if (!apiKey) {
    // This case is also handled by the initial check outside this function
    throw new Error('Google API key is not configured');
  }

  // Check if AI generation is enabled via environment variable
  if (!enableAiGeneration) {
    throw new Error('AI image generation is disabled via environment variable');
  }

  // Check rate limits
  if (!checkRateLimit()) {
    throw new Error('Daily rate limit exceeded for AI image generation');
  }

  // Check cache first
  const cachedImage = getCachedImage(prompt);
  if (cachedImage) {
    console.log('[API Route] Returning cached image for prompt:', prompt);
    return { imageData: cachedImage };
  }

  try {
    // Initialize the Google GenAI client using the new package
    const genAI = new GoogleGenAI({ apiKey });

    console.log('[API Route] Calling Gemini API with prompt:', prompt);
    console.log('[API Route] Using gemini-2.0-flash-preview-image-generation model for image generation');

    // Make sure the prompt clearly requests an image
    const enhancedPrompt = `${prompt} Please create a visual design and provide a brief description.`;

    // Log the request for debugging
    console.log('[API Route] Enhanced prompt:', enhancedPrompt);

    // Using the exact format from the example code
    // Set responseModalities to include both TEXT and IMAGE
    const response = await genAI.models.generateContent({
      model: "gemini-2.0-flash-preview-image-generation",
      contents: enhancedPrompt,
      config: {
        temperature: 0.4,
        topP: 0.8,
        topK: 40,
        responseModalities: [Modality.TEXT, Modality.IMAGE],
        // Safety settings for the new package
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      },
    });

    console.log('[API Route] Response received from Gemini API');

    // Check if we have a valid response
    if (!response.candidates || response.candidates.length === 0) {
      console.error('[API Route] No candidates found in the response');
      throw new Error('No generation candidates returned from the model');
    }

    // Get the parts from the response using the new API structure
    const parts = response.candidates[0]?.content?.parts || [];

    if (!parts || parts.length === 0) {
      console.error('[API Route] No parts found in the response');
      throw new Error('No content parts returned from the image generation model');
    }

    console.log(`[API Route] Received ${parts.length} parts in the response`);

    // Find the part that contains the image data and the text description
    let imageData = '';
    let textDescription = '';
    let imageMimeType = '';

    // Process each part based on its type
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];

      // Check if this part contains text
      if (part.text) {
        textDescription = part.text;
        console.log(`[API Route] Found text description in part ${i}: ${textDescription.substring(0, 100)}...`);
      }
      // Check if this part contains image data
      else if (part.inlineData) {
        // Extract the mime type and data
        const mimeType = part.inlineData.mimeType;
        const base64Data = part.inlineData.data;

        if (mimeType) {
          console.log(`[API Route] Found image data in part ${i} with MIME type: ${mimeType}`);
        } else {
          console.log(`[API Route] Found image data in part ${i} but no MIME type`);
        }

        if (base64Data) {
          // Validate that this is actually base64 data
          if (isValidBase64(base64Data)) {
            // Store the base64 data and mime type
            imageData = base64Data;
            imageMimeType = mimeType || 'image/png'; // Default to image/png if no MIME type
            console.log('[API Route] Successfully extracted base64 image data');
          } else {
            console.warn(`[API Route] Invalid base64 data detected in part ${i}`);
          }
        } else {
          console.warn(`[API Route] No base64 data found in part ${i}`);
        }
      }
    }

    // Check if we found any valid image data
    if (!imageData) {
      console.error('[API Route] No valid image data found in any response part');
      throw new Error('No image data found in the response');
    }

    // Construct the final data URL
    const fullDataUrl = `data:${imageMimeType || 'image/png'};base64,${imageData}`;
    console.log('[API Route] Constructed full data URL');


    // Cache the generated image using the full prompt as the key
    cacheImage(prompt, fullDataUrl); // Cache the full data URL

    // Return both the image data URL and text description
    return {
      imageData: fullDataUrl,
      textDescription: textDescription || undefined // Ensure it's undefined if empty
    };
  } catch (error) {
    console.error('[API Route] Error during generateImage call:', error);
    // Re-throw the error to be caught by the POST handler's specific error checks
    throw error;
  }
}

// API route handler
export async function POST(request: NextRequest) {
  // Ensure only POST requests are allowed if necessary, although Next.js App Router handles this based on exported functions
  // if (request.method !== 'POST') {
  //   return NextResponse.json(
  //     { error: 'Method Not Allowed', fallback: true }, // Add fallback for clarity
  //     { status: 405 }
  //   );
  // }

  // Check for API Key existence early
    if (!apiKey) {
       console.error('[API Route] GOOGLE_API_KEY is not set.');
       return NextResponse.json({ error: 'Server configuration error: Google API key is missing.', fallback: true }, { status: 500 });
    }


  try {
    const body = await request.json();
    const { prompt, service, style } = body; // Receive prompt, service, style from frontend

    if (!prompt) {
      console.warn('[API Route] Received request with no prompt.');
      return NextResponse.json(
        { error: 'Prompt is required', fallback: true }, // Indicate fallback on client error
        { status: 400 }
      );
    }

    // Enhance the prompt with service and style information if provided by the frontend.
    // Note: Your frontend code might already construct the *full* enhanced prompt before calling generateAiImage.
    // If your frontend utility *always* sends the final prompt in the 'prompt' field
    // and sends 'service'/'style' separately just for logging/context, you can
    // remove the prompt enhancement logic here and just use `const finalPrompt = prompt;`.
    // Assuming your utility sends the core prompt, service, and style:
    let finalPrompt = prompt;
    if (service && style) {
      // Reconstruct the enhanced prompt that the AI will understand
      // This structure should match how your frontend utilities are building the prompt string
      finalPrompt = `Generate an image of a professional ${style} design for a ${service} project. ${prompt}`;
    }
    // If your frontend utilities already fully enhance the 'prompt' before sending,
    // just use: const finalPrompt = prompt;

    console.log('[API Route] Processing request for prompt:', finalPrompt);


    // Generate the image and get the result (imageDataURL and textDescription)
    const result = await generateImage(finalPrompt);

    // Return both the image data (as a data URL) and text description (if available)
    console.log('[API Route] Image generation successful, returning response.');
    return NextResponse.json({
      imageData: result.imageData, // This is now the full data URL string
      textDescription: result.textDescription || null // Ensure it's null if undefined/empty
    }, { status: 200 });

  } catch (error: unknown) { // Catch any errors thrown by generateImage or earlier in POST handler
    console.error('[API Route] Error caught in POST handler:', error);

    // Default error response
    let status = 500;
    let errorMessage = 'Failed to generate image';
    let errorDetails = error instanceof Error ? error.message : 'An unknown error occurred.';
    const triggerFallback = true; // Assume most server errors trigger fallback

    // Check for specific error types or messages to return appropriate status/message
    if (error instanceof Error) {
        const msg = error.message;
         errorDetails = msg; // Use the specific error message as details

      if (msg.includes('Prompt is required')) { // From initial check
        status = 400;
        errorMessage = 'Bad Request';
      } else if (msg.includes('rate limit') || msg.includes('quota')) {
        status = 429;
        errorMessage = 'Rate limit exceeded';
      } else if (msg.includes('disabled')) {
        status = 503;
        errorMessage = 'AI generation service disabled';
      } else if (msg.includes('API key') || msg.includes('auth') || msg.includes('authenticate')) {
         status = 401;
         errorMessage = 'Authentication failed';
         // Maybe don't fallback for auth errors? Depends on how you want to handle it.
         // Leaving fallback: true to show a broken state might be better.
      } else if (msg.includes('model') || msg.includes('Model')) {
         status = 400; // Client error because requested model config is wrong
         errorMessage = 'Invalid AI model configuration';
      } else if (msg.includes('content policy') || msg.includes('safety') || msg.includes('blocked')) {
         status = 400; // Client error due to prompt
         errorMessage = 'Content policy violation';
      } else if (msg.includes('response modalities') || msg.includes('responseModalities')) {
          status = 500; // Server configuration issue or transient API issue
          errorMessage = 'API response modality configuration error';
      } else if (msg.includes('No image data') || msg.includes('No content parts') || msg.includes('No generation candidates')) {
           status = 500; // The AI didn't return an image part as expected
           errorMessage = 'AI did not produce an image';
      }
       // Add more specific error checks as needed
    } else {
         // If the error is not an Error instance
         errorDetails = JSON.stringify(error); // Stringify unknown error structure
    }


    console.error(`[API Route] Returning error response (Status: ${status}, Error: ${errorMessage}, Details: ${errorDetails})`);
    return NextResponse.json(
      {
        error: errorMessage,
        message: errorDetails, // Provide details in message field
        fallback: triggerFallback // Indicate whether frontend should use fallback
      },
      { status: status }
    );
  }
}