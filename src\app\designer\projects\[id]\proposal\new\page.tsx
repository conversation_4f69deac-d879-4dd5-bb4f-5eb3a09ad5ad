"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  ArrowLeft,
  Save,
  AlertCircle,
  CheckCircle,
  Loader2,
  Calendar,
  DollarSign,
  Clock,
  FileText,
  Upload,
  X,
  Plus,
  Trash
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  description: string;
  client_id: string;
  client_name: string;
  budget: number | null;
  type: string | null;
  location: string | null;
  requirements: string | null;
};

type Milestone = {
  id: string;
  title: string;
  description: string;
  amount: number;
  percentage: number;
  deliverables: string[];
  estimated_days: number;
};

export default function NewProposal() {
  const { user } = useAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Form fields
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [scope, setScope] = useState("");
  const [timeline, setTimeline] = useState("");
  const [totalBudget, setTotalBudget] = useState<number | null>(null);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [attachments, setAttachments] = useState<File[]>([]);

  // Template selection
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  useEffect(() => {
    if (user && projectId) {
      fetchProjectData();
      fetchProposalTemplates();
    }
  }, [user, projectId]);

  const fetchProposalTemplates = async () => {
    setLoadingTemplates(true);
    try {
      // Fetch active templates
      const { data, error } = await supabase
        .from('proposal_templates')
        .select(`
          id,
          title,
          description,
          project_type,
          is_default
        `)
        .eq('is_active', true)
        .order('is_default', { ascending: false })
        .order('title');

      if (error) throw error;
      setTemplates(data || []);

      // If there's a default template, select it
      const defaultTemplate = data?.find(t => t.is_default);
      if (defaultTemplate) {
        setSelectedTemplateId(defaultTemplate.id);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    } finally {
      setLoadingTemplates(false);
    }
  };

  const fetchProjectData = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          client_id,
          budget,
          type,
          location,
          requirements,
          profiles!projects_client_id_fkey(full_name)
        `)
        .eq('id', projectId)
        .single();

      if (error) throw error;

      const projectData: Project = {
        id: data.id,
        title: data.title,
        description: data.description,
        client_id: data.client_id,
        client_name: data.profiles.full_name,
        budget: data.budget,
        type: data.type,
        location: data.location,
        requirements: data.requirements
      };

      setProject(projectData);

      // Initialize form with project data
      setTitle(`Proposal for ${projectData.title}`);
      setDescription(`This proposal outlines our approach to the ${projectData.title} project.`);
      if (projectData.budget) {
        setTotalBudget(projectData.budget);

        // Create default milestones based on budget
        const defaultMilestones: Milestone[] = [
          {
            id: "initial-deposit",
            title: "Initial Deposit",
            description: "Initial payment to start the project",
            amount: Math.round(projectData.budget * 0.3),
            percentage: 30,
            deliverables: ["Project kickoff", "Initial concepts"],
            estimated_days: 7
          },
          {
            id: "milestone-1",
            title: "Concept Design",
            description: "Completion of concept design phase",
            amount: Math.round(projectData.budget * 0.3),
            percentage: 30,
            deliverables: ["Detailed concept designs", "3D renderings", "Material specifications"],
            estimated_days: 14
          },
          {
            id: "milestone-2",
            title: "Final Delivery",
            description: "Completion of all deliverables",
            amount: Math.round(projectData.budget * 0.4),
            percentage: 40,
            deliverables: ["Final design documents", "Construction drawings", "Technical specifications"],
            estimated_days: 21
          }
        ];

        setMilestones(defaultMilestones);
      }
    } catch (error) {
      console.error('Error fetching project:', error);
      setError('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const handleMilestoneChange = (index: number, field: keyof Milestone, value: any) => {
    setMilestones(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };

      // If percentage is updated, recalculate amount
      if (field === 'percentage' && totalBudget) {
        updated[index].amount = Math.round((totalBudget * (value as number)) / 100);
      }

      // If amount is updated, recalculate percentage
      if (field === 'amount' && totalBudget && totalBudget > 0) {
        updated[index].percentage = Math.round(((value as number) / totalBudget) * 100);
      }

      return updated;
    });
  };

  const addMilestone = () => {
    const newMilestone: Milestone = {
      id: `milestone-${milestones.length}`,
      title: `Milestone ${milestones.length + 1}`,
      description: "",
      amount: 0,
      percentage: 0,
      deliverables: [""],
      estimated_days: 7
    };

    setMilestones([...milestones, newMilestone]);
  };

  const removeMilestone = (index: number) => {
    setMilestones(prev => prev.filter((_, i) => i !== index));
  };

  const addDeliverable = (milestoneIndex: number) => {
    setMilestones(prev => {
      const updated = [...prev];
      updated[milestoneIndex].deliverables.push("");
      return updated;
    });
  };

  const removeDeliverable = (milestoneIndex: number, deliverableIndex: number) => {
    setMilestones(prev => {
      const updated = [...prev];
      updated[milestoneIndex].deliverables = updated[milestoneIndex].deliverables.filter((_, i) => i !== deliverableIndex);
      return updated;
    });
  };

  const updateDeliverable = (milestoneIndex: number, deliverableIndex: number, value: string) => {
    setMilestones(prev => {
      const updated = [...prev];
      updated[milestoneIndex].deliverables[deliverableIndex] = value;
      return updated;
    });
  };

  const handleAttachmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setAttachments(prev => [...prev, ...newFiles]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const applyTemplate = async (templateId: string) => {
    if (!templateId) return;

    try {
      // Fetch the template details
      const { data: template, error: templateError } = await supabase
        .from('proposal_templates')
        .select(`
          id,
          title,
          description,
          scope_template,
          timeline_template
        `)
        .eq('id', templateId)
        .single();

      if (templateError) throw templateError;

      // Apply template values to form
      if (template.scope_template) {
        // Replace placeholders with actual values
        let processedScope = template.scope_template
          .replace(/{PROJECT_NAME}/g, project?.title || '')
          .replace(/{CLIENT_NAME}/g, project?.client_name || '')
          .replace(/{PROJECT_TYPE}/g, project?.type || '')
          .replace(/{LOCATION}/g, project?.location || '');

        setScope(processedScope);
      }

      if (template.timeline_template) {
        let processedTimeline = template.timeline_template
          .replace(/{PROJECT_NAME}/g, project?.title || '')
          .replace(/{CLIENT_NAME}/g, project?.client_name || '');

        setTimeline(processedTimeline);
      }

      // Fetch template milestones
      const { data: templateMilestones, error: milestonesError } = await supabase
        .from('proposal_template_milestones')
        .select(`
          title,
          description,
          percentage,
          estimated_days,
          deliverables,
          order_index
        `)
        .eq('template_id', templateId)
        .order('order_index');

      if (milestonesError) throw milestonesError;

      if (templateMilestones && templateMilestones.length > 0) {
        // Convert template milestones to proposal milestones
        const newMilestones: Milestone[] = templateMilestones.map((tm, index) => {
          const amount = totalBudget ? Math.round((totalBudget * tm.percentage) / 100) : 0;

          return {
            id: `milestone-${index}`,
            title: tm.title,
            description: tm.description || '',
            amount,
            percentage: tm.percentage,
            estimated_days: tm.estimated_days || 0,
            deliverables: Array.isArray(tm.deliverables) ? tm.deliverables : [tm.deliverables || '']
          };
        });

        setMilestones(newMilestones);
      }

    } catch (error) {
      console.error('Error applying template:', error);
      setError('Failed to apply template. Please try again.');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !project) return;

    // Validate form
    if (!title.trim()) {
      setError('Please enter a proposal title');
      return;
    }

    if (milestones.length === 0) {
      setError('Please add at least one milestone');
      return;
    }

    // Check if milestone percentages add up to 100%
    const totalPercentage = milestones.reduce((sum, m) => sum + m.percentage, 0);
    if (totalPercentage !== 100) {
      setError(`Milestone percentages must add up to 100%. Current total: ${totalPercentage}%`);
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      // 1. Create the proposal
      const { data: proposal, error: proposalError } = await supabase
        .from('project_proposals')
        .insert({
          project_id: projectId,
          designer_id: user.id,
          title,
          description,
          scope,
          timeline,
          total_budget: totalBudget,
          status: 'pending'
        })
        .select()
        .single();

      if (proposalError) throw proposalError;

      // 2. Create proposal milestones
      for (const milestone of milestones) {
        const { error: milestoneError } = await supabase
          .from('proposal_milestones')
          .insert({
            proposal_id: proposal.id,
            title: milestone.title,
            description: milestone.description,
            amount: milestone.amount,
            percentage: milestone.percentage,
            estimated_days: milestone.estimated_days,
            deliverables: milestone.deliverables
          });

        if (milestoneError) throw milestoneError;
      }

      // 3. Upload attachments
      for (const file of attachments) {
        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `proposal-attachments/${proposal.id}/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from('project-files')
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('project-files')
          .getPublicUrl(filePath);

        // Add attachment to proposal_attachments table
        const { error: attachmentError } = await supabase
          .from('proposal_attachments')
          .insert({
            proposal_id: proposal.id,
            file_url: urlData.publicUrl,
            file_name: file.name,
            file_type: file.type,
            file_size: file.size
          });

        if (attachmentError) throw attachmentError;
      }

      // 4. Create notification for admin (not client - admin reviews first)
      const { data: adminUsers, error: adminError } = await supabase
        .from('profiles')
        .select('id')
        .eq('role', 'admin');

      if (!adminError && adminUsers && adminUsers.length > 0) {
        const adminNotifications = adminUsers.map(admin => ({
          user_id: admin.id,
          type: 'proposal',
          title: 'New Proposal Needs Review',
          content: `A new proposal has been submitted for ${project.title} and needs admin review`,
          related_id: proposal.id,
          read: false
        }));

        const { error: notificationError } = await supabase
          .from('notifications')
          .insert(adminNotifications);

        if (notificationError) console.error('Error creating admin notifications:', notificationError);
      }

      // Show success message and redirect
      setSuccess(true);
      setTimeout(() => {
        router.push(`/designer/projects/${projectId}`);
      }, 2000);
    } catch (error) {
      console.error('Error submitting proposal:', error);
      setError('Failed to submit proposal. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center mb-6">
        <Link href={`/designer/projects/${projectId}`}>
          <Button variant="ghost" className="p-0 h-auto mr-4">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Create Project Proposal</h1>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      {success ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-white border border-gray-200 p-8 text-center"
        >
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mx-auto w-20 h-20 bg-green-50 border border-green-200 flex items-center justify-center mb-4"
          >
            <CheckCircle className="h-10 w-10 text-green-600" />
          </motion.div>
          <h2 className="text-xl font-semibold mb-2">Proposal Submitted!</h2>
          <p className="text-gray-600 mb-6">
            Your proposal has been submitted successfully. The client will be notified.
          </p>
          <p className="text-gray-500 text-sm mb-6">
            Redirecting to project page...
          </p>
        </motion.div>
      ) : loading ? (
        <div className="flex justify-center items-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <div className="h-8 w-8 border-t-2 border-b-2 border-brown-600"></div>
          </motion.div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Project Info */}
          <div className="bg-white border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">Project Information</h2>

            {project && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-gray-500">Project</p>
                  <p className="font-medium">{project.title}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Client</p>
                  <p className="font-medium">{project.client_name}</p>
                </div>
                {project.type && (
                  <div>
                    <p className="text-sm text-gray-500">Type</p>
                    <p className="font-medium">{project.type}</p>
                  </div>
                )}
                {project.location && (
                  <div>
                    <p className="text-sm text-gray-500">Location</p>
                    <p className="font-medium">{project.location}</p>
                  </div>
                )}
                {project.budget && (
                  <div>
                    <p className="text-sm text-gray-500">Budget</p>
                    <p className="font-medium">${project.budget.toLocaleString()}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Proposal Details */}
          <div className="bg-white border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">Proposal Details</h2>

            {/* Template Selection */}
            <div className="mb-6 border border-gray-200 p-4 bg-gray-50">
              <h3 className="text-md font-medium mb-3">Use a Proposal Template</h3>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-grow">
                  <select
                    value={selectedTemplateId || ''}
                    onChange={(e) => setSelectedTemplateId(e.target.value || null)}
                    className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    disabled={loadingTemplates}
                  >
                    <option value="">Select a template...</option>
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.title} {template.is_default ? '(Default)' : ''}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Templates provide pre-defined milestones and content
                  </p>
                </div>
                <Button
                  type="button"
                  onClick={() => selectedTemplateId && applyTemplate(selectedTemplateId)}
                  disabled={!selectedTemplateId || loadingTemplates}
                  className="flex items-center whitespace-nowrap"
                >
                  {loadingTemplates ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      Apply Template
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Proposal Title *
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                  className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  required
                  rows={3}
                  className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="scope" className="block text-sm font-medium text-gray-700 mb-1">
                  Scope of Work *
                </label>
                <textarea
                  id="scope"
                  value={scope}
                  onChange={(e) => setScope(e.target.value)}
                  required
                  rows={4}
                  className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="Describe the scope of work for this project..."
                />
              </div>

              <div>
                <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Timeline *
                </label>
                <textarea
                  id="timeline"
                  value={timeline}
                  onChange={(e) => setTimeline(e.target.value)}
                  required
                  rows={3}
                  className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="Describe the overall timeline for the project..."
                />
              </div>

              <div>
                <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-1">
                  Total Budget *
                </label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
                  <input
                    type="number"
                    id="budget"
                    value={totalBudget || ""}
                    onChange={(e) => {
                      const value = e.target.value ? parseFloat(e.target.value) : null;
                      setTotalBudget(value);

                      // Update milestone amounts based on percentages
                      if (value) {
                        setMilestones(prev => prev.map(milestone => ({
                          ...milestone,
                          amount: Math.round((value * milestone.percentage) / 100)
                        })));
                      }
                    }}
                    required
                    min="0"
                    step="100"
                    className="w-full p-2 pl-8 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Milestones */}
          <div className="bg-white border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Payment Milestones</h2>
              <Button
                type="button"
                onClick={addMilestone}
                variant="outline"
                className="flex items-center text-sm"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Milestone
              </Button>
            </div>

            {milestones.length === 0 ? (
              <div className="text-center py-8 border border-dashed border-gray-300 rounded">
                <p className="text-gray-500">No milestones added yet. Click "Add Milestone" to create one.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {milestones.map((milestone, index) => (
                  <motion.div
                    key={milestone.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border border-gray-200 p-4"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="font-medium">Milestone {index + 1}</h3>
                      {milestones.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeMilestone(index)}
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700 p-0 h-auto"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Title *
                        </label>
                        <input
                          type="text"
                          value={milestone.title}
                          onChange={(e) => handleMilestoneChange(index, 'title', e.target.value)}
                          required
                          className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Percentage *
                          </label>
                          <div className="flex items-center">
                            <input
                              type="number"
                              value={milestone.percentage}
                              onChange={(e) => handleMilestoneChange(index, 'percentage', parseInt(e.target.value) || 0)}
                              required
                              min="1"
                              max="100"
                              className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                            />
                            <span className="ml-2">%</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Amount *
                          </label>
                          <div className="relative">
                            <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
                            <input
                              type="number"
                              value={milestone.amount}
                              onChange={(e) => handleMilestoneChange(index, 'amount', parseInt(e.target.value) || 0)}
                              required
                              min="0"
                              className="w-full p-2 pl-8 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description *
                      </label>
                      <textarea
                        value={milestone.description}
                        onChange={(e) => handleMilestoneChange(index, 'description', e.target.value)}
                        required
                        rows={2}
                        className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Estimated Days *
                      </label>
                      <div className="flex items-center">
                        <input
                          type="number"
                          value={milestone.estimated_days}
                          onChange={(e) => handleMilestoneChange(index, 'estimated_days', parseInt(e.target.value) || 0)}
                          required
                          min="1"
                          className="w-32 p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                        />
                        <span className="ml-2 text-gray-500">days</span>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Deliverables *
                        </label>
                        <Button
                          type="button"
                          onClick={() => addDeliverable(index)}
                          variant="ghost"
                          size="sm"
                          className="text-sm p-0 h-auto"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Add
                        </Button>
                      </div>

                      {milestone.deliverables.map((deliverable, dIndex) => (
                        <div key={dIndex} className="flex items-center mb-2">
                          <input
                            type="text"
                            value={deliverable}
                            onChange={(e) => updateDeliverable(index, dIndex, e.target.value)}
                            required
                            className="flex-1 p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                            placeholder="Deliverable item"
                          />
                          {milestone.deliverables.length > 1 && (
                            <Button
                              type="button"
                              onClick={() => removeDeliverable(index, dIndex)}
                              variant="ghost"
                              size="sm"
                              className="ml-2 text-red-500 hover:text-red-700 p-0 h-auto"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>

          {/* Attachments */}
          <div className="bg-white border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">Attachments</h2>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="file"
                  id="attachments"
                  multiple
                  onChange={handleAttachmentChange}
                  className="hidden"
                />
                <label
                  htmlFor="attachments"
                  className="cursor-pointer flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Choose Files
                </label>
                <span className="ml-3 text-sm text-gray-500">
                  {attachments.length > 0
                    ? `${attachments.length} file(s) selected`
                    : "No files chosen"}
                </span>
              </div>

              <p className="text-xs text-gray-500 mt-1">
                Upload relevant documents such as design references, specifications, or examples.
              </p>
            </div>

            {attachments.length > 0 && (
              <div className="space-y-2">
                {attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 border border-gray-200">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm truncate max-w-xs">{file.name}</span>
                    </div>
                    <Button
                      type="button"
                      onClick={() => removeAttachment(index)}
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-700 p-0 h-auto"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={submitting}
              className="bg-brown-600 hover:bg-brown-700 text-white border-0 flex items-center"
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Submit Proposal
                </>
              )}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}