/**
 * Utility functions for working with AI-generated images
 */
import { fallbackImages } from './r2-images'; // Assuming this exists

// Counter for tracking AI generation attempts in the current session
let aiGenerationAttempts = 0;

// Define the expected successful response structure from your API route
interface GenerateImageSuccessResponse {
  imageData: string; // Complete data URL (e.g., data:image/png;base64,...)
  textDescription?: string; // Optional text description of the image
}

// Define the expected error response structure from your API route
interface GenerateImageErrorResponse {
  error?: string; // Error message
  fallback?: boolean; // Optional: Flag to indicate fallback should be used
  message?: string; // Alias for error, depending on your API route's naming
}


/**
 * Generates an image using the Gemini AI API (via your serverless function)
 * @param prompt The text prompt for image generation
 * @param service The service category (optional, sent to API but not used by Gemini directly)
 * @param style The design style (optional, sent to API but not used by Gemini directly)
 * @returns A data URL containing the generated image or null if generation fails/fallback is indicated
 */
export async function generateAiImage(
  prompt: string,
  service?: string,
  style?: string
): Promise<string | null> {
  try {
    // Increment the counter
    aiGenerationAttempts++;

    // Log the generation attempt
    console.log(`AI image generation attempt #${aiGenerationAttempts}`);
    console.log(`Prompt: ${prompt}`);
    if (service) console.log(`Service: ${service}`);
    if (style) console.log(`Style: ${style}`);

    // Call your server-side API endpoint
    const response = await fetch('/api/generate-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      // Send prompt, service, and style - the API route might only use 'prompt'
      body: JSON.stringify({ prompt, service, style }),
    });

    // Check if the response was NOT OK
    if (!response.ok) {
      try {
        const errorData: GenerateImageErrorResponse = await response.json(); // Cast to error interface
        console.error('Error generating AI image:', errorData);

        // Only return null if the API explicitly indicates fallback or if it's a rate limit/disabled error
        if (errorData.fallback ||
            (errorData.error && (
              errorData.error.includes('rate limit') ||
              errorData.error.includes('disabled')
            ))) {
          console.log('Using fallback due to API response:', errorData.error || errorData.message);
          return null;
        }

        // For other errors, throw to be caught by the outer catch block
        throw new Error(errorData.error || errorData.message || 'Failed to generate image');
      } catch (parseError) {
        // If we can't parse the error response, log and return null to trigger fallback
        console.error('Error parsing error response:', parseError);
        return null;
      }
    }

    // If the response is OK, parse the success data
    const data: GenerateImageSuccessResponse = await response.json(); // Cast to success interface

    // Check if the expected imageData property exists
    if (data.imageData) {
      // Log success and the first 100 characters of the data URL for debugging
      console.log('Successfully received image data from API');
      console.log('Image data preview:', data.imageData.substring(0, 100) + '...');

      // Log the text description if available
      if (data.textDescription) {
        console.log('Text description from API:', data.textDescription.substring(0, 200) +
          (data.textDescription.length > 200 ? '...' : ''));
      }

      // The server already returns a complete data URL, so we can just return it directly
      return data.imageData;
    } else {
      // Response was OK, but data format was unexpected
      console.error('API returned successful response, but missing imageData:', data);
      return null; // Treat as a failure, trigger fallback
    }

  } catch (error) {
    console.error('Error in generateAiImage fetch or processing:', error);
    return null; // Any unexpected error triggers fallback
  }
}

// The getAiGeneratedImage and regenerateAiImage functions can remain mostly unchanged
// as they correctly handle the null return value from generateAiImage.

/**
 * Gets a random AI-generated image based on a prompt
 * @param prompt The text prompt for image generation
 * @param service The service category
 * @param style The design style
 * @returns An image URL (either AI-generated or a fallback)
 */
export async function getAiGeneratedImage(
  prompt: string,
  service: string,
  style: string
): Promise<string> {
  try {
    // Create an enhanced prompt that requests an image generation
    // Note: The model requires both text and image in the response
    const enhancedPrompt = `Generate an image of a professional ${style} design for a ${service} project. ${prompt}. Please provide both a description and a visual representation.`;

    console.log('Attempting to generate AI image with prompt:', enhancedPrompt);

    // Generate the image using the modified generateAiImage
    const imageDataUrl = await generateAiImage(enhancedPrompt, service, style); // Will return data URL or null

    if (!imageDataUrl) {
      console.log('AI image generation returned null, using fallback image');
      // Return a random fallback image if AI generation fails or indicates fallback
      const randomIndex = Math.floor(Math.random() * fallbackImages.length);
      const fallbackImage = fallbackImages[randomIndex];
      console.log('Using fallback image:', fallbackImage.substring(0, 50) + '...');
      return fallbackImage;
    }

    console.log('Successfully generated AI image, returning data URL');
    return imageDataUrl; // Return the generated data URL
  } catch (error) {
    console.error('Error in getAiGeneratedImage:', error);

    // Return a fallback image if anything goes wrong during the process
    const randomIndex = Math.floor(Math.random() * fallbackImages.length);
    const fallbackImage = fallbackImages[randomIndex];
    console.log('Error occurred, using fallback image:', fallbackImage.substring(0, 50) + '...');
    return fallbackImage;
  }
}

/**
 * Regenerates an AI image with a slightly modified prompt for variety
 * @param originalPrompt The original text prompt
 * @param service The service category
 * @param style The design style
 * @returns A new image URL (either AI-generated or a fallback)
 */
export async function regenerateAiImage(
  originalPrompt: string,
  service: string,
  style: string
): Promise<string> {
  try {
    // Add some variety to the prompt to get a different result
    const variations = [
      'with a different perspective',
      'with alternative colors',
      'with a different composition',
      'with a unique approach',
      'with a fresh interpretation'
    ];

    const randomVariation = variations[Math.floor(Math.random() * variations.length)];
    const enhancedPrompt = `Generate an image of a professional ${style} design for a ${service} project. ${originalPrompt} ${randomVariation}. Please provide both a description and a visual representation.`;

    console.log('Attempting to regenerate AI image with prompt:', enhancedPrompt);

    // Generate the image using the modified generateAiImage
    const imageDataUrl = await generateAiImage(enhancedPrompt, service, style); // Will return data URL or null

    if (!imageDataUrl) {
      console.log('AI image regeneration returned null, using fallback image');
      // Return a random fallback image if AI generation fails or indicates fallback
      const randomIndex = Math.floor(Math.random() * fallbackImages.length);
      const fallbackImage = fallbackImages[randomIndex];
      console.log('Using fallback image for regeneration:', fallbackImage.substring(0, 50) + '...');
      return fallbackImage;
    }

    console.log('Successfully regenerated AI image, returning data URL');
    return imageDataUrl; // Return the generated data URL
  } catch (error) {
    console.error('Error in regenerateAiImage:', error);

    // Return a fallback image if anything goes wrong
    const randomIndex = Math.floor(Math.random() * fallbackImages.length);
    const fallbackImage = fallbackImages[randomIndex];
    console.log('Error occurred during regeneration, using fallback image:', fallbackImage.substring(0, 50) + '...');
    return fallbackImage;
  }
}

// Assume './r2-images' exports an array of image URLs, e.g.:
// export const fallbackImages = [ '/images/fallback1.png', '/images/fallback2.jpg' ];