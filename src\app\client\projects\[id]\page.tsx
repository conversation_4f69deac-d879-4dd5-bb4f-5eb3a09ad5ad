"use client";

import { useEffect, useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import MilestonesList from "@/components/projects/MilestonesList";
import { ProjectTimeline } from "@/components/projects/ProjectTimeline";
import InspirationBoardsList from "@/components/client/InspirationBoardsList";
import {
  ArrowLeft,
  MessageSquare,
  Image,
  FileText,
  CreditCard,
  Clock,
  MapPin,
  User,
  Calendar,
  Edit,
  Trash2,
  CheckSquare,
  AlertTriangle,
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  description: string;
  type: string;
  location: string;
  budget: number | null;
  status: string;
  created_at: string;
  updated_at: string;
  designer_id: string | null;
  designer_name?: string;
  scope?: string;
  constraints?: string;
  objectives?: string[];
  start_date?: string | null;
  end_date?: string | null;
  timeline?: string;
  requirements?: string;
};

type DesignSubmission = {
  id: string;
  title: string;
  description: string;
  version: number;
  status: string;
  created_at: string;
};

type Milestone = {
  id: string;
  title: string;
  description: string;
  amount: number;
  percentage: number;
  status: string;
  order_index: number;
  due_date: string | null;
  completed_at: string | null;
  approved_at: string | null;
  paid_at: string | null;
};

export default function ProjectDetail() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [project, setProject] = useState<Project | null>(null);
  const [submissions, setSubmissions] = useState<DesignSubmission[]>([]);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [inspirationBoards, setInspirationBoards] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<
    "overview" | "submissions" | "inspirations" | "messages" | "milestones"
  >("overview");
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  useEffect(() => {
    if (user && projectId) {
      fetchProjectData();
    }
  }, [user, projectId]);

  const fetchProjectData = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from("projects")
        .select(
          `
          id,
          title,
          description,
          type,
          location,
          budget,
          status,
          created_at,
          updated_at,
          designer_id,
          scope,
          constraints,
          objectives,
          start_date,
          end_date,
          timeline,
          requirements,
          profiles!projects_designer_id_fkey(full_name)
        `
        )
        .eq("id", projectId)
        .eq("client_id", user?.id)
        .single();

      if (projectError) throw projectError;

      setProject({
        ...projectData,
        designer_name: projectData.profiles?.[0]?.full_name,
      });

      // Fetch design submissions
      const { data: submissionsData, error: submissionsError } = await supabase
        .from("submissions")
        .select("id, title, description, status, created_at")
        .eq("project_id", projectId)
        .order("created_at", { ascending: false });

      if (submissionsError) throw submissionsError;
      setSubmissions(submissionsData || []);

      // Count inspiration boards
      const { count, error: boardsError } = await supabase
        .from("inspiration_boards")
        .select("id", { count: "exact" })
        .eq("project_id", projectId);

      if (boardsError) throw boardsError;
      setInspirationBoards(count || 0);

      // Fetch milestones
      const { data: milestonesData, error: milestonesError } = await supabase
        .from("project_milestones")
        .select(`
          id,
          title,
          description,
          amount,
          percentage,
          status,
          order_index,
          due_date,
          completed_at,
          approved_at,
          paid_at
        `)
        .eq("project_id", projectId)
        .order("order_index", { ascending: true });

      if (milestonesError) throw milestonesError;
      setMilestones(milestonesData || []);
    } catch (error: Error | unknown) {
      console.error("Error fetching project data:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load project data"
      );
    } finally {
      setLoading(false);
    }
  };
  const handleDeleteProject = async () => {
    if (!deleteConfirm) {
      setDeleteConfirm(true);
      return;
    }

    try {
      const { error } = await supabase
        .from("projects")
        .delete()
        .eq("id", projectId)
        .eq("client_id", user?.id);

      if (error) throw error;

      router.push("/client/projects");
    } catch (error: Error | unknown) {
      console.error("Error deleting project:", error);
      setError(
        error instanceof Error ? error.message : "Failed to delete project"
      );
    }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return "Not specified";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-brown-100 text-brown-800";
      case "review":
        return "bg-yellow-100 text-yellow-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "submitted":
        return "bg-brown-50 text-brown-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getSubmissionStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "needs_revision":
        return "bg-orange-100 text-orange-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="bg-red-50 text-red-500 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Error</h2>
        <p>{error || "Project not found"}</p>
        <Link href="/client/projects">
          <Button variant="outline" className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <Link
          href="/client/projects"
          className="inline-flex items-center text-gray-600 hover:text-primary"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Projects
        </Link>
      </div>

      {/* Project Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold mb-2">{project.title}</h1>
            <div className="flex items-center gap-2 mb-4">
              <span
                className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
                  project.status
                )}`}
              >
                {project.status.replace("_", " ")}
              </span>
              <span className="text-sm text-gray-500">
                Created on {formatDate(project.created_at)}
              </span>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex items-center">
              <Edit className="mr-1 h-4 w-4" />
              Edit
            </Button>
            <Button
              variant="outline"
              size="sm"
              className={`flex items-center ${
                deleteConfirm ? "bg-red-50 text-red-500 border-red-300" : ""
              }`}
              onClick={handleDeleteProject}
            >
              <Trash2 className="mr-1 h-4 w-4" />
              {deleteConfirm ? "Confirm Delete" : "Delete"}
            </Button>
          </div>
        </div>

        {/* Admin Review Notification */}
        {project.status === "pending" && (
          <div className="mt-4 p-4 bg-blue-50 text-blue-700 rounded-md">
            <div className="flex items-start">
              <Clock className="h-5 w-5 mr-2 mt-0.5" />
              <div>
                <p className="font-medium">
                  Your project is pending admin review
                </p>
                <p className="mt-1 text-sm">
                  Our admin team will review your project and assign a designer
                  soon. You'll be notified once a designer is assigned to your
                  project.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Designer Assignment Notification */}
        {project.status !== "pending" && !project.designer_id && (
          <div className="mt-4 p-4 bg-yellow-50 text-yellow-700 rounded-md">
            <div className="flex items-start">
              <User className="h-5 w-5 mr-2 mt-0.5" />
              <div>
                <p className="font-medium">Designer assignment in progress</p>
                <p className="mt-1 text-sm">
                  Your project has been approved and our admin team is in the
                  process of assigning the best designer for your needs.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-md mb-6">
        <div className="flex border-b">
          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === "overview"
                ? "border-b-2 border-primary text-primary"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("overview")}
          >
            Overview
          </button>
          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === "submissions"
                ? "border-b-2 border-primary text-primary"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("submissions")}
          >
            Design Submissions
            {submissions.length > 0 && (
              <span className="ml-2 bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-xs">
                {submissions.length}
              </span>
            )}
          </button>
          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === "inspirations"
                ? "border-b-2 border-primary text-primary"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("inspirations")}
          >
            Inspiration Boards
            {inspirationBoards > 0 && (
              <span className="ml-2 bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-xs">
                {inspirationBoards}
              </span>
            )}
          </button>
          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === "messages"
                ? "border-b-2 border-primary text-primary"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("messages")}
          >
            Messages
          </button>
          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === "milestones"
                ? "border-b-2 border-primary text-primary"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("milestones")}
          >
            Milestones
            <CheckSquare className="inline-block ml-2 h-4 w-4" />
          </button>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === "overview" && (
            <div className="space-y-8">
              <div>
                <h2 className="text-lg font-semibold mb-4">Project Details</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Project Type
                      </h3>
                      <p>{project.type || "Not specified"}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Location
                      </h3>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                        <p>{project.location || "Not specified"}</p>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Budget
                      </h3>
                      <p>{formatCurrency(project.budget)}</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Assigned Designer
                      </h3>
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-gray-400 mr-1" />
                        <p>{project.designer_name || "Not assigned yet"}</p>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Created
                      </h3>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                        <p>{formatDate(project.created_at)}</p>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Last Updated
                      </h3>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-gray-400 mr-1" />
                        <p>{formatDate(project.updated_at)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h2 className="text-lg font-semibold mb-4">Description</h2>
                <p className="text-gray-700 whitespace-pre-line">
                  {project.description}
                </p>
              </div>

              {project.scope && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">Project Scope</h2>
                  <p className="text-gray-700 whitespace-pre-line">
                    {project.scope}
                  </p>
                </div>
              )}

              {project.objectives && project.objectives.length > 0 && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">Project Objectives</h2>
                  <ul className="list-disc pl-5 space-y-2">
                    {project.objectives.map((objective, index) => (
                      <li key={index} className="text-gray-700">
                        {objective}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {project.constraints && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">Project Constraints</h2>
                  <p className="text-gray-700 whitespace-pre-line">
                    {project.constraints}
                  </p>
                </div>
              )}

              {project.start_date && project.end_date && (
                <div>
                  <h2 className="text-lg font-semibold mb-4">Project Timeline</h2>
                  <ProjectTimeline
                    startDate={project.start_date}
                    endDate={project.end_date}
                    milestones={[]}
                    currentStep={0}
                  />
                </div>
              )}

              <div className="flex flex-col md:flex-row gap-4">
                <Button className="flex items-center">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Contact Designer
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center"
                  onClick={() => setActiveTab("milestones")}
                >
                  <CreditCard className="mr-2 h-4 w-4" />
                  View Milestones & Payments
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center"
                  onClick={() => router.push(`/projects/${projectId}/disputes/new`)}
                >
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Report Issue
                </Button>
              </div>
            </div>
          )}

          {/* Design Submissions Tab */}
          {activeTab === "submissions" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Design Submissions</h2>
                {submissions.length > 0 && (
                  <Link href={`/client/projects/${projectId}/submissions`}>
                    <Button variant="outline" size="sm" className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      View All Submissions
                    </Button>
                  </Link>
                )}
              </div>

              {submissions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    No design submissions yet.
                  </p>
                  <p className="text-sm text-gray-400">
                    Your designer will upload design submissions here once they
                    start working on your project.
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  {submissions.map((submission) => (
                    <div
                      key={submission.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{submission.title}</h3>
                          <p className="text-sm text-gray-500 mb-2">
                            Submitted on {formatDate(submission.created_at)}
                          </p>
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${getSubmissionStatusColor(
                              submission.status
                            )}`}
                          >
                            {submission.status.replace("_", " ")}
                          </span>
                        </div>
                        <Link
                          href={`/client/projects/${projectId}/submissions/${submission.id}`}
                        >
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </Link>
                      </div>
                      {submission.description && (
                        <p className="mt-4 text-sm text-gray-700">
                          {submission.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Inspiration Boards Tab */}
          {activeTab === "inspirations" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Inspiration Boards</h2>
                <Link href={`/client/projects/${projectId}/inspirations/new`}>
                  <Button size="sm" className="flex items-center">
                    <Image className="mr-2 h-4 w-4" />
                    New Board
                  </Button>
                </Link>
              </div>

              {inspirationBoards === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">
                    No inspiration boards yet.
                  </p>
                  <Link href={`/client/projects/${projectId}/inspirations/new`}>
                    <Button>Create Your First Board</Button>
                  </Link>
                </div>
              ) : (
                <InspirationBoardsList projectId={projectId} />
              )}
            </div>
          )}

          {/* Messages Tab */}
          {activeTab === "messages" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Project Messages</h2>
              </div>

              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">
                  Message functionality coming soon.
                </p>
                <Button variant="outline">Contact Support</Button>
              </div>
            </div>
          )}

          {/* Milestones Tab */}
          {activeTab === "milestones" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Project Milestones</h2>
              </div>

              {project.start_date && project.end_date && (
                <div className="mb-8">
                  <ProjectTimeline
                    startDate={project.start_date}
                    endDate={project.end_date}
                    milestones={milestones.map(m => ({
                      id: m.id,
                      title: m.title,
                      status: m.status,
                      order_index: m.order_index
                    }))}
                    currentStep={milestones.findIndex(m => m.status === 'active') !== -1
                      ? milestones.findIndex(m => m.status === 'active')
                      : milestones.filter(m => m.status === 'completed' || m.status === 'approved' || m.status === 'paid').length
                    }
                  />
                </div>
              )}

              <MilestonesList
                projectId={projectId}
                designerId={project.designer_id || undefined}
                userRole="client"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
