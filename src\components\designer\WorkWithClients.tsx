"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  MessageSquare,
  User,
  Calendar,
  FileText,
  ArrowRight,
  Clock,
  CheckCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface ClientInteraction {
  id: string;
  client_id: string;
  client_name: string;
  client_avatar: string | null;
  type: 'message' | 'project_update' | 'milestone' | 'meeting';
  title: string;
  description: string;
  created_at: string;
  status?: 'pending' | 'completed' | 'in_progress';
  project_id?: string;
  project_title?: string;
}

export function WorkWithClients() {
  const { user } = useOptimizedAuth();
  const [interactions, setInteractions] = useState<ClientInteraction[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchClientInteractions();
    }
  }, [user]);

  const fetchClientInteractions = async () => {
    try {
      // Fetch recent messages
      const { data: messagesData } = await supabase
        .from('project_messages')
        .select(`
          id,
          content,
          created_at,
          sender_id,
          project_id,
          projects(title, client_id),
          profiles!sender_id(full_name, avatar_url)
        `)
        .neq('sender_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(3);

      // Fetch recent project updates
      const { data: projectsData } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          status,
          updated_at,
          client_id,
          profiles!client_id(full_name, avatar_url)
        `)
        .eq('designer_id', user?.id)
        .order('updated_at', { ascending: false })
        .limit(3);

      // Fetch recent milestones
      const { data: milestonesData } = await supabase
        .from('project_milestones')
        .select(`
          id,
          title,
          status,
          due_date,
          created_at,
          project_id,
          projects(title, client_id, profiles!client_id(full_name, avatar_url))
        `)
        .in('status', ['pending', 'active'])
        .order('created_at', { ascending: false })
        .limit(3);

      const allInteractions: ClientInteraction[] = [];

      // Process messages
      if (messagesData) {
        messagesData.forEach(message => {
          if (message.projects && message.profiles) {
            allInteractions.push({
              id: `message-${message.id}`,
              client_id: message.sender_id,
              client_name: message.profiles.full_name,
              client_avatar: message.profiles.avatar_url,
              type: 'message',
              title: 'New Message',
              description: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : ''),
              created_at: message.created_at,
              project_id: message.project_id,
              project_title: message.projects.title
            });
          }
        });
      }

      // Process project updates
      if (projectsData) {
        projectsData.forEach(project => {
          if (project.profiles) {
            allInteractions.push({
              id: `project-${project.id}`,
              client_id: project.client_id,
              client_name: project.profiles.full_name,
              client_avatar: project.profiles.avatar_url,
              type: 'project_update',
              title: 'Project Update',
              description: `Project "${project.title}" status: ${project.status}`,
              created_at: project.updated_at,
              status: project.status as any,
              project_id: project.id,
              project_title: project.title
            });
          }
        });
      }

      // Process milestones
      if (milestonesData) {
        milestonesData.forEach(milestone => {
          if (milestone.projects && milestone.projects.profiles) {
            allInteractions.push({
              id: `milestone-${milestone.id}`,
              client_id: milestone.projects.client_id,
              client_name: milestone.projects.profiles.full_name,
              client_avatar: milestone.projects.profiles.avatar_url,
              type: 'milestone',
              title: 'Milestone Due',
              description: `"${milestone.title}" in ${milestone.projects.title}`,
              created_at: milestone.due_date || milestone.created_at,
              status: milestone.status as any,
              project_id: milestone.project_id,
              project_title: milestone.projects.title
            });
          }
        });
      }

      // Sort by date and take the most recent
      allInteractions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      setInteractions(allInteractions.slice(0, 6));

    } catch (error) {
      console.error('Error fetching client interactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getInteractionIcon = (type: ClientInteraction['type']) => {
    switch (type) {
      case 'message':
        return <MessageSquare className="h-4 w-4" />;
      case 'project_update':
        return <FileText className="h-4 w-4" />;
      case 'milestone':
        return <Calendar className="h-4 w-4" />;
      case 'meeting':
        return <Clock className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getInteractionColor = (type: ClientInteraction['type']) => {
    switch (type) {
      case 'message':
        return 'text-blue-600 bg-blue-50';
      case 'project_update':
        return 'text-green-600 bg-green-50';
      case 'milestone':
        return 'text-purple-600 bg-purple-50';
      case 'meeting':
        return 'text-orange-600 bg-orange-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Work with Your Clients</h3>
          <Link href="/designer/messages">
            <Button variant="ghost" size="sm">
              View All <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </div>
      </div>

      <div className="p-6">
        {interactions.length === 0 ? (
          <div className="text-center py-8">
            <User className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Recent Activity</h4>
            <p className="text-gray-600">You'll see client interactions here once projects are assigned to you by the admin or when clients contact you.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {interactions.map((interaction) => (
              <motion.div
                key={interaction.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
                className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              >
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                    {interaction.client_avatar ? (
                      <img
                        src={interaction.client_avatar}
                        alt={interaction.client_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-xs font-medium text-gray-600">
                        {interaction.client_name.split(' ').map(n => n[0]).join('')}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      {interaction.client_name}
                    </h4>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getInteractionColor(interaction.type)}`}>
                        {getInteractionIcon(interaction.type)}
                        <span className="ml-1 capitalize">{interaction.type.replace('_', ' ')}</span>
                      </span>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-1">{interaction.description}</p>

                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      {interaction.project_title && (
                        <span className="mr-2">in {interaction.project_title}</span>
                      )}
                      <span>{formatTimeAgo(interaction.created_at)}</span>
                    </div>

                    {interaction.status && (
                      <div className="flex items-center text-xs">
                        {interaction.status === 'completed' ? (
                          <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                        ) : (
                          <Clock className="h-3 w-3 text-yellow-500 mr-1" />
                        )}
                        <span className="capitalize">{interaction.status}</span>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}
