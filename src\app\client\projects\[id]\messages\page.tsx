"use client";

import { useState, useEffect, useRef } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  ArrowLeft,
  Send,
  AlertCircle,
  Paperclip,
  Download,
  Image as ImageIcon,
  FileText,
  User,
  Loader2,
  X,
  Clock,
  MessageSquare
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  client_id: string;
  designer_id: string | null;
  designer_name: string | null;
  designer_avatar: string | null;
};

type Message = {
  id: string;
  content: string;
  sender_id: string;
  sender_name: string;
  sender_avatar: string | null;
  sender_role: string;
  created_at: string;
  attachment_url: string | null;
  attachment_name: string | null;
  attachment_type: string | null;
  is_read: boolean;
};

export default function ProjectMessages() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [project, setProject] = useState<Project | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [attachment, setAttachment] = useState<File | null>(null);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [realtimeSubscription, setRealtimeSubscription] = useState<any>(null);

  useEffect(() => {
    if (user && projectId) {
      fetchProjectData();
      fetchMessages();
      setupRealtimeSubscription();
    }

    return () => {
      // Clean up subscription when component unmounts
      if (realtimeSubscription) {
        realtimeSubscription.unsubscribe();
      }
    };
  }, [user, projectId]);

  useEffect(() => {
    // Scroll to bottom when messages change
    scrollToBottom();
  }, [messages]);

  const fetchProjectData = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          client_id,
          designer_id,
          profiles!projects_designer_id_fkey(full_name, avatar_url)
        `)
        .eq('id', projectId)
        .single();

      if (error) throw error;

      setProject({
        id: data.id,
        title: data.title,
        client_id: data.client_id,
        designer_id: data.designer_id,
        designer_name: data.profiles?.full_name || null,
        designer_avatar: data.profiles?.avatar_url || null
      });
    } catch (error) {
      console.error('Error fetching project:', error);
      setError('Failed to load project data');
    }
  };

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('project_messages')
        .select(`
          id,
          content,
          sender_id,
          created_at,
          attachment_url,
          attachment_name,
          attachment_type,
          is_read,
          profiles!project_messages_sender_id_fkey(full_name, avatar_url, role)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const formattedMessages = data.map(msg => ({
        id: msg.id,
        content: msg.content,
        sender_id: msg.sender_id,
        sender_name: msg.profiles.full_name,
        sender_avatar: msg.profiles.avatar_url,
        sender_role: msg.profiles.role,
        created_at: msg.created_at,
        attachment_url: msg.attachment_url,
        attachment_name: msg.attachment_name,
        attachment_type: msg.attachment_type,
        is_read: msg.is_read
      }));

      setMessages(formattedMessages);

      // Mark unread messages as read
      const unreadMessages = data
        .filter(msg => !msg.is_read && msg.sender_id !== user?.id)
        .map(msg => msg.id);

      if (unreadMessages.length > 0) {
        await supabase
          .from('project_messages')
          .update({ is_read: true })
          .in('id', unreadMessages);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const setupRealtimeSubscription = () => {
    const subscription = supabase
      .channel('project_messages_channel')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'project_messages',
          filter: `project_id=eq.${projectId}`
        },
        (payload) => {
          handleNewMessage(payload.new);
        }
      )
      .subscribe();

    setRealtimeSubscription(subscription);
  };

  const handleNewMessage = async (newMsg: any) => {
    // Fetch the sender details
    const { data: senderData, error: senderError } = await supabase
      .from('profiles')
      .select('full_name, avatar_url, role')
      .eq('id', newMsg.sender_id)
      .single();

    if (senderError) {
      console.error('Error fetching sender details:', senderError);
      return;
    }

    const formattedMessage: Message = {
      id: newMsg.id,
      content: newMsg.content,
      sender_id: newMsg.sender_id,
      sender_name: senderData.full_name,
      sender_avatar: senderData.avatar_url,
      sender_role: senderData.role,
      created_at: newMsg.created_at,
      attachment_url: newMsg.attachment_url,
      attachment_name: newMsg.attachment_name,
      attachment_type: newMsg.attachment_type,
      is_read: newMsg.is_read
    };

    // Only add the message if it's not from the current user
    // (to avoid duplicates since we already add the message optimistically)
    if (newMsg.sender_id !== user?.id) {
      setMessages(prev => [...prev, formattedMessage]);

      // Mark the message as read
      await supabase
        .from('project_messages')
        .update({ is_read: true })
        .eq('id', newMsg.id);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleAttachmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setAttachment(e.target.files[0]);
    }
  };

  const removeAttachment = () => {
    setAttachment(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !project) return;
    if (!newMessage.trim() && !attachment) return;

    setSending(true);
    setError(null);

    try {
      let attachmentUrl = null;
      let attachmentName = null;
      let attachmentType = null;

      // Upload attachment if exists
      if (attachment) {
        const fileExt = attachment.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `project-messages/${projectId}/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from('project-files')
          .upload(filePath, attachment);

        if (uploadError) throw uploadError;

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('project-files')
          .getPublicUrl(filePath);

        attachmentUrl = urlData.publicUrl;
        attachmentName = attachment.name;
        attachmentType = attachment.type;
      }

      // Create message
      const { data: message, error: messageError } = await supabase
        .from('project_messages')
        .insert({
          project_id: projectId,
          content: newMessage.trim(),
          sender_id: user.id,
          attachment_url: attachmentUrl,
          attachment_name: attachmentName,
          attachment_type: attachmentType,
          is_read: false
        })
        .select()
        .single();

      if (messageError) throw messageError;

      // Add message to state optimistically
      const newMsg: Message = {
        id: message.id,
        content: message.content,
        sender_id: user.id,
        sender_name: user.user_metadata?.full_name || user.email || 'User',
        sender_avatar: user.user_metadata?.avatar_url || null,
        sender_role: 'client',
        created_at: message.created_at,
        attachment_url: message.attachment_url,
        attachment_name: message.attachment_name,
        attachment_type: message.attachment_type,
        is_read: message.is_read
      };

      setMessages(prev => [...prev, newMsg]);
      setNewMessage("");
      setAttachment(null);

      // Create notification for the designer
      if (project.designer_id) {
        const { error: notificationError } = await supabase
          .from('notifications')
          .insert({
            user_id: project.designer_id,
            type: 'message',
            title: 'New Message',
            content: `New message in project: ${project.title}`,
            related_id: projectId,
            read: false
          });

        if (notificationError) {
          console.error('Error creating notification:', notificationError);
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Group messages by date
  const groupMessagesByDate = () => {
    const groups: { [key: string]: Message[] } = {};

    messages.forEach(message => {
      const date = new Date(message.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });

      if (!groups[date]) {
        groups[date] = [];
      }

      groups[date].push(message);
    });

    return groups;
  };

  const messageGroups = groupMessagesByDate();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center mb-6">
        <Link href={`/client/projects/${projectId}`}>
          <Button variant="ghost" className="p-0 h-auto mr-4">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Project Messages</h1>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {/* Project Info */}
        <div className="border-b border-gray-200 p-4">
          <h2 className="font-semibold">{project?.title || 'Loading...'}</h2>
          {project?.designer_name && (
            <div className="flex items-center mt-2 text-sm text-gray-500">
              <User className="h-4 w-4 mr-1" />
              <span>Designer: {project.designer_name}</span>
            </div>
          )}
        </div>

        {/* Messages */}
        <div className="h-[500px] overflow-y-auto p-4 bg-gray-50">
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <div className="h-8 w-8 border-t-2 border-b-2 border-brown-600"></div>
              </motion.div>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <MessageSquare className="h-12 w-12 text-gray-300 mb-4" />
              <p>No messages yet</p>
              <p className="text-sm mt-2">Start the conversation by sending a message below</p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(messageGroups).map(([date, msgs]) => (
                <div key={date}>
                  <div className="flex justify-center mb-4">
                    <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">
                      {date}
                    </span>
                  </div>

                  <div className="space-y-4">
                    {msgs.map(message => (
                      <div
                        key={message.id}
                        className={`flex ${message.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-[80%] ${message.sender_id === user?.id ? 'order-2' : 'order-1'}`}>
                          <div className="flex items-center mb-1">
                            <div className="w-6 h-6 bg-gray-100 flex items-center justify-center border border-gray-200 mr-2">
                              {message.sender_avatar ? (
                                <img
                                  src={message.sender_avatar}
                                  alt={message.sender_name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <User className="h-3 w-3 text-gray-400" />
                              )}
                            </div>
                            <span className="text-xs font-medium">{message.sender_name}</span>
                            <span className="text-xs text-gray-500 ml-2">{formatTime(message.created_at)}</span>
                          </div>

                          <div
                            className={`p-3 rounded-lg ${
                              message.sender_id === user?.id
                                ? 'bg-brown-600 text-white'
                                : 'bg-white border border-gray-200 text-gray-900'
                            }`}
                          >
                            {message.content && (
                              <p className="whitespace-pre-wrap">{message.content}</p>
                            )}

                            {message.attachment_url && (
                              <div className="mt-2">
                                {message.attachment_type?.startsWith('image/') ? (
                                  <div className="mt-2">
                                    <a
                                      href={message.attachment_url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="block"
                                    >
                                      <img
                                        src={message.attachment_url}
                                        alt="Attachment"
                                        className="max-w-full max-h-48 rounded"
                                      />
                                    </a>
                                  </div>
                                ) : (
                                  <a
                                    href={message.attachment_url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className={`flex items-center mt-2 ${
                                      message.sender_id === user?.id
                                        ? 'text-white/90 hover:text-white'
                                        : 'text-blue-600 hover:text-blue-800'
                                    }`}
                                  >
                                    <FileText className="h-4 w-4 mr-1" />
                                    <span className="text-sm underline">
                                      {message.attachment_name || 'Attachment'}
                                    </span>
                                    <Download className="h-3 w-3 ml-1" />
                                  </a>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Message Input */}
        <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200">
          {attachment && (
            <div className="mb-3 p-2 bg-gray-50 border border-gray-200 rounded flex items-center justify-between">
              <div className="flex items-center">
                {attachment.type.startsWith('image/') ? (
                  <ImageIcon className="h-4 w-4 text-gray-400 mr-2" />
                ) : (
                  <FileText className="h-4 w-4 text-gray-400 mr-2" />
                )}
                <span className="text-sm truncate max-w-xs">{attachment.name}</span>
              </div>
              <Button
                type="button"
                onClick={removeAttachment}
                variant="ghost"
                size="sm"
                className="text-red-500 hover:text-red-700 p-0 h-auto"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}

          <div className="flex items-center">
            <div className="relative flex-1">
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message..."
                className="w-full p-3 pr-12 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent resize-none"
                rows={1}
                disabled={sending}
              />
              <div className="absolute right-2 bottom-2">
                <input
                  type="file"
                  id="attachment"
                  onChange={handleAttachmentChange}
                  className="hidden"
                  disabled={sending}
                />
                <label
                  htmlFor="attachment"
                  className="cursor-pointer p-1 text-gray-500 hover:text-gray-700"
                >
                  <Paperclip className="h-5 w-5" />
                </label>
              </div>
            </div>
            <Button
              type="submit"
              disabled={sending || (!newMessage.trim() && !attachment)}
              className="bg-brown-600 hover:bg-brown-700 text-white border-0 rounded-r flex items-center h-[50px]"
            >
              {sending ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Send className="h-5 w-5" />
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}