'use client';

import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import UnifiedMessaging from '@/components/messaging/UnifiedMessaging';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminConversations() {
  const { user, loading } = useOptimizedAuth();
  const router = useRouter();
  const [isAdmin, setIsAdmin] = useState(false);
  const [checkingRole, setCheckingRole] = useState(true);

  useEffect(() => {
    const checkAdminRole = async () => {
      if (!user) {
        setCheckingRole(false);
        return;
      }

      try {
        const { supabase } = await import('@/lib/supabase');
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error checking user role:', error);
          router.push('/');
          return;
        }

        if (profile?.role !== 'admin') {
          router.push('/');
          return;
        }

        setIsAdmin(true);
      } catch (error) {
        console.error('Error checking admin role:', error);
        router.push('/');
      } finally {
        setCheckingRole(false);
      }
    };

    checkAdminRole();
  }, [user, router]);

  if (loading || checkingRole) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
        </div>
      </div>
    );
  }

  if (!user || !isAdmin) {
    return (
      <div className="container mx-auto py-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">All Conversations</h1>
        <p className="text-gray-600">Monitor and manage all user conversations</p>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Admin Monitoring Mode
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                You are viewing all conversations in the system. You can see both direct conversations between clients and designers, as well as project-based conversations. Use this interface to monitor communications and provide support when needed.
              </p>
            </div>
          </div>
        </div>
      </div>

      <UnifiedMessaging />
    </div>
  );
}
