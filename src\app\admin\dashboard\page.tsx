"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useDashboardStats, useProjects } from "@/hooks/useDashboardData";
import { usePrefetchDashboardData } from "@/hooks/usePrefetch";
import { useResponsive } from "@/components/mobile/ResponsiveLayout";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Users,
  FolderK<PERSON>ban,
  CheckCircle,
  Clock,
  CreditCard,
  AlertCircle,
  ArrowRight,
  BarChart3,
  UserPlus,
  FileText,
  Calculator,
  TrendingUp,
  Target,
  Settings
} from "lucide-react";
import { DesignerApplicationReview } from "@/components/admin/DesignerApplicationReview";
import { ProjectAssignmentInterface } from "@/components/admin/ProjectAssignmentInterface";
import { FeeCalculationSystem } from "@/components/admin/FeeCalculationSystem";
import { FinancialReportingDashboard } from "@/components/admin/FinancialReportingDashboard";
import { MilestoneTemplates } from "@/components/admin/MilestoneTemplates";
import { QuickActions } from "@/components/admin/QuickActions";
import { DashboardNotifications } from "@/components/shared/DashboardNotifications";
import QualityIntegration from "@/components/integration/QualityIntegration";
import ManagerIntegration from "@/components/integration/ManagerIntegration";
import UnifiedCommunication from "@/components/integration/UnifiedCommunication";
import UnifiedFileManager from "@/components/files/UnifiedFileManager";
import SLAMonitorDashboard from "@/components/quality/SLAMonitorDashboard";
import SystemMigrationPanel from "@/components/admin/SystemMigrationPanel";

type DashboardStats = {
  total_users: number;
  client_count: number;
  designer_count: number;
  active_projects: number;
  completed_projects: number;
  pending_projects: number;
  total_payments: number;
  pending_payments: number;
};

type RecentUser = {
  id: string;
  full_name: string;
  email: string;
  role: string;
  created_at: string;
  avatar_url: string | null;
};

type RecentProject = {
  id: string;
  title: string;
  status: string;
  client_name: string;
  designer_name: string | null;
  created_at: string;
};

export default function AdminDashboard() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'applications' | 'assignments' | 'fees' | 'reports' | 'milestones' | 'quality' | 'management' | 'files' | 'system'>('overview');
  const { isMobile, isTablet } = useResponsive();

  // Use optimized data hooks
  const { data: stats, isLoading: statsLoading, error: statsError } = useDashboardStats(user?.id || '', 'admin');
  const { data: projects, isLoading: projectsLoading } = useProjects(user?.id || '', 'admin');

  // Prefetch dashboard data for faster navigation
  usePrefetchDashboardData();

  // Derived data
  const recentProjects = projects?.slice(0, 5) || [];
  const loading = authLoading || statsLoading || projectsLoading;
  const error = statsError;  const formatDate = (dateString: string) => {    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'applications', label: 'Applications', icon: UserPlus },
    { id: 'assignments', label: 'Assignments', icon: FolderKanban },
    { id: 'fees', label: 'Fee Management', icon: Calculator },
    { id: 'milestones', label: 'Milestone Templates', icon: Target },
    { id: 'reports', label: 'Financial Reports', icon: TrendingUp },
    { id: 'quality', label: 'Quality', icon: CheckCircle },
    { id: 'management', label: 'Management', icon: Users },
    { id: 'files', label: 'File Manager', icon: FileText },
    { id: 'system', label: 'System', icon: Settings }
  ];

  return (
    <div className={isMobile ? 'p-4' : 'p-8'}>
      <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'} ${isMobile ? 'mb-6' : 'mb-8'}`}>
        <div>
          <h1 className={`font-bold mb-2 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            Admin Dashboard
          </h1>
          <p className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
            Comprehensive platform management and analytics
          </p>
        </div>

        {/* Notifications in Dashboard Header */}
        <div className="flex items-center space-x-4">
          <DashboardNotifications variant="header" role="admin" />
        </div>
      </div>

      {/* Tab Navigation - Mobile Responsive */}
      <div className={isMobile ? 'mb-6' : 'mb-8'}>
        <div className="border-b border-gray-200">
          <nav className={`-mb-px flex ${isMobile ? 'overflow-x-auto space-x-4' : 'space-x-8'}`}>
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center border-b-2 font-medium transition-colors whitespace-nowrap ${
                    isMobile ? 'py-2 px-2 text-xs' : 'py-2 px-1 text-sm'
                  } ${
                    activeTab === tab.id
                      ? 'border-brown-500 text-brown-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                  {isMobile ? tab.label.split(' ')[0] : tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div>
          {/* Quick Actions */}
          <div className="mb-8">
            <QuickActions />
          </div>

      {/* Stats Cards - Mobile Responsive */}
      <div className={`grid gap-4 ${isMobile ? 'mb-6' : 'mb-8'} ${
        isMobile
          ? 'grid-cols-2'
          : isTablet
            ? 'grid-cols-2'
            : 'grid-cols-4'
      }`}>
        <div className={`bg-white rounded-lg shadow-md ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className={`flex items-center justify-between ${isMobile ? 'mb-2' : 'mb-4'}`}>
            <h3 className={`text-gray-500 font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Total Users
            </h3>
            <Users className={`text-blue-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
          </div>
          <p className={`font-bold ${isMobile ? 'text-xl' : 'text-3xl'}`}>
            {stats?.totalUsers || 0}
          </p>
          <div className={`flex items-center ${isMobile ? 'mt-1 text-xs' : 'mt-2 text-sm'}`}>
            <span className="text-gray-500">Platform Users</span>
          </div>
        </div>

        <div className={`bg-white rounded-lg shadow-md ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className={`flex items-center justify-between ${isMobile ? 'mb-2' : 'mb-4'}`}>
            <h3 className={`text-gray-500 font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Active Projects
            </h3>
            <FolderKanban className={`text-green-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
          </div>
          <p className={`font-bold ${isMobile ? 'text-xl' : 'text-3xl'}`}>
            {stats?.activeProjects || 0}
          </p>
          <div className={`flex items-center ${isMobile ? 'mt-1 text-xs' : 'mt-2 text-sm'}`}>
            <span className="text-gray-500">{stats?.totalProjects || 0} Total Projects</span>
          </div>
        </div>

        <div className={`bg-white rounded-lg shadow-md ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className={`flex items-center justify-between ${isMobile ? 'mb-2' : 'mb-4'}`}>
            <h3 className={`text-gray-500 font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Total Revenue
            </h3>
            <CreditCard className={`text-purple-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
          </div>
          <p className={`font-bold ${isMobile ? 'text-xl' : 'text-3xl'}`}>
            {formatCurrency(0)}
          </p>
          <div className={`flex items-center ${isMobile ? 'mt-1 text-xs' : 'mt-2 text-sm'}`}>
            <span className="text-gray-500">Revenue Tracking</span>
          </div>
        </div>

        <div className={`bg-white rounded-lg shadow-md ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className={`flex items-center justify-between ${isMobile ? 'mb-2' : 'mb-4'}`}>
            <h3 className={`text-gray-500 font-medium ${isMobile ? 'text-xs' : 'text-sm'}`}>
              System Status
            </h3>
            <CheckCircle className={`text-green-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
          </div>
          <p className={`font-bold ${isMobile ? 'text-xl' : 'text-3xl'}`}>
            Healthy
          </p>
          <div className={`flex items-center ${isMobile ? 'mt-1 text-xs' : 'mt-2 text-sm'}`}>
            <span className="text-gray-500">All systems operational</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8">
        {/* Recent Projects - Mobile Responsive */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className={`border-b flex ${isMobile ? 'flex-col space-y-3 p-4' : 'justify-between items-center px-6 py-4'}`}>
            <h2 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
              Recent Projects
            </h2>
            <Link href="/admin/projects">
              <Button
                variant="ghost"
                className={`flex items-center ${isMobile ? 'w-full justify-center text-sm' : 'text-sm'}`}
              >
                View All
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </Link>
          </div>
          <div className={isMobile ? 'p-4' : 'p-6'}>
            {recentProjects.length === 0 ? (
              <div className={`text-center text-gray-500 ${isMobile ? 'py-6' : 'py-8'}`}>
                No projects found
              </div>
            ) : (
              <div className={`space-y-${isMobile ? '3' : '4'}`}>
                {recentProjects.map((project) => (
                  <div key={project.id} className={`border rounded-lg ${isMobile ? 'p-3' : 'p-4'}`}>
                    <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'justify-between items-start'} mb-2`}>
                      <h3 className={`font-medium ${isMobile ? 'text-sm' : 'text-base'}`}>
                        {project.title}
                      </h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${isMobile ? 'self-start' : ''} ${
                        project.status === 'completed' ? 'bg-green-100 text-green-800' :
                        project.status === 'active' || project.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {project.status.replace('_', ' ').charAt(0).toUpperCase() + project.status.replace('_', ' ').slice(1)}
                      </span>
                    </div>
                    <div className={`text-gray-500 mb-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      <p>Client: {project.client?.full_name || 'Unknown Client'}</p>
                      <p>Designer: {project.designer?.full_name || 'Not assigned'}</p>
                    </div>
                    <div className={`text-gray-400 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                      Created on {formatDate(project.created_at)}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions - Mobile Responsive */}
      <div className={`bg-white rounded-lg shadow-md overflow-hidden ${isMobile ? 'mt-6' : 'mt-8'}`}>
        <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
          <h2 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
            Quick Actions
          </h2>
        </div>
        <div className={`grid gap-4 ${isMobile ? 'p-4 grid-cols-1' : 'p-6 grid-cols-1 md:grid-cols-3'}`}>
          <Link href="/admin/users/new">
            <Button
              className="w-full justify-between"
              size={isMobile ? "sm" : "default"}
            >
              <span className={isMobile ? 'text-sm' : 'text-base'}>Add New Designer</span>
              <UserPlus className="h-4 w-4 ml-2" />
            </Button>
          </Link>
          <Link href="/admin/projects">
            <Button
              variant="outline"
              className="w-full justify-between"
              size={isMobile ? "sm" : "default"}
            >
              <span className={isMobile ? 'text-sm' : 'text-base'}>Manage Projects</span>
              <FolderKanban className="h-4 w-4 ml-2" />
            </Button>
          </Link>
          <Link href="/admin/finance/reports">
            <Button
              variant="outline"
              className="w-full justify-between"
              size={isMobile ? "sm" : "default"}
            >
              <span className={isMobile ? 'text-sm' : 'text-base'}>Financial Reports</span>
              <FileText className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </div>
      </div>
        </div>
      )}

      {/* Applications Tab */}
      {activeTab === 'applications' && (
        <DesignerApplicationReview />
      )}

      {/* Assignments Tab */}
      {activeTab === 'assignments' && (
        <ProjectAssignmentInterface />
      )}

      {/* Fee Management Tab */}
      {activeTab === 'fees' && (
        <FeeCalculationSystem />
      )}

      {/* Milestone Templates Tab */}
      {activeTab === 'milestones' && (
        <MilestoneTemplates />
      )}

      {/* Financial Reports Tab */}
      {activeTab === 'reports' && (
        <FinancialReportingDashboard />
      )}

      {/* Quality Management Tab */}
      {activeTab === 'quality' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
              <h2 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
                Quality Management
              </h2>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              <SLAMonitorDashboard role="admin" compact={true} />
              <div className="mt-6">
                <QualityIntegration role="admin" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Management Tab */}
      {activeTab === 'management' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
              <h2 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
                Project Management
              </h2>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              <ManagerIntegration role="admin" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
              <h2 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
                Communication Center
              </h2>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              <UnifiedCommunication role="admin" />
            </div>
          </div>
        </div>
      )}

      {/* File Manager Tab */}
      {activeTab === 'files' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
              <h2 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
                File Management
              </h2>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              <UnifiedFileManager role="admin" />
            </div>
          </div>
        </div>
      )}

      {/* System Management Tab */}
      {activeTab === 'system' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
              <h2 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>
                System Management
              </h2>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              <SystemMigrationPanel />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
