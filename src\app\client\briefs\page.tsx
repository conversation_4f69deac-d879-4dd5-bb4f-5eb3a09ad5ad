"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useProjectBriefs } from "@/hooks/useDashboardData";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Briefcase,
  Plus,
  Clock,
  DollarSign,
  MapPin,
  User,
  Eye,
  FileText,
  AlertCircle,
  Filter,
  Search,
  Calendar,
  Target,
  ChevronRight,
  Send,
  CheckCircle,
  XCircle
} from "lucide-react";

interface ProjectBrief {
  id: string;
  title: string;
  description: string;
  budget_range: string;
  timeline_preference: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'assigned' | 'proposal_received' | 'accepted' | 'rejected';
  assigned_designer_id: string | null;
  assigned_designer_name: string | null;
  created_at: string;
  proposal_count: number;
  project_type: string;
  location: string;
}

export default function ClientBriefs() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [urgencyFilter, setUrgencyFilter] = useState<string>('all');

  // Use optimized data fetching with correct parameters
  const { data: briefs = [], isLoading, error } = useProjectBriefs(user?.id || '', 'client');

  // Transform briefs data for display
  const formattedBriefs: ProjectBrief[] = briefs.map(brief => ({
    id: brief.id,
    title: brief.title,
    description: brief.description,
    budget_range: brief.budget_range,
    timeline_preference: brief.timeline_preference,
    urgency: brief.urgency,
    status: brief.status,
    assigned_designer_id: brief.assigned_designer_id,
    assigned_designer_name: brief.assigned_designer?.full_name || null,
    created_at: brief.created_at,
    proposal_count: brief.proposal_count || 0,
    project_type: brief.project_type || 'residential_interior',
    location: brief.location || 'Not specified'
  }));

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'text-emerald-600 bg-emerald-50 border-emerald-200';
      case 'proposal_received':
        return 'text-brown-600 bg-brown-50 border-brown-200';
      case 'assigned':
        return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'pending':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'proposal_received':
        return <FileText className="h-4 w-4" />;
      case 'assigned':
        return <User className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Briefcase className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getBudgetDisplay = (budgetRange: string) => {
    return budgetRange.replace(/_/g, ' - $').replace('k', 'K');
  };

  const filteredBriefs = formattedBriefs.filter(brief => {
    const matchesSearch = brief.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         brief.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || brief.status === statusFilter;
    const matchesUrgency = urgencyFilter === 'all' || brief.urgency === urgencyFilter;

    return matchesSearch && matchesStatus && matchesUrgency;
  });

  // Show loading only for initial load or auth loading
  const loading = authLoading || (isLoading && formattedBriefs.length === 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Project Briefs</h1>
          <p className="text-gray-600">Manage your project requirements and review proposals</p>
        </div>
        <Link href="/client/briefs/new">
          <Button className="bg-brown-600 hover:bg-brown-700 text-white">
            <Plus className="h-4 w-4 mr-2" />
            Submit New Brief
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search briefs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="assigned">Assigned</option>
            <option value="proposal_received">Proposal Received</option>
            <option value="accepted">Accepted</option>
            <option value="rejected">Rejected</option>
          </select>

          <select
            value={urgencyFilter}
            onChange={(e) => setUrgencyFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Urgencies</option>
            <option value="urgent">Urgent</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
      </div>

      {/* Briefs List */}
      {filteredBriefs.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <Briefcase className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {formattedBriefs.length === 0 ? 'No project briefs yet' : 'No briefs match your filters'}
          </h3>
          <p className="text-gray-500 mb-4">
            {formattedBriefs.length === 0
              ? 'Submit your first brief to get started with our designers'
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {formattedBriefs.length === 0 && (
            <Link href="/client/briefs/new">
              <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                <Briefcase className="h-4 w-4 mr-2" />
                Submit Your First Brief
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredBriefs.map((brief) => (
            <motion.div
              key={brief.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{brief.title}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getUrgencyColor(brief.urgency)}`}>
                      {brief.urgency.toUpperCase()}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border flex items-center ${getStatusColor(brief.status)}`}>
                      {getStatusIcon(brief.status)}
                      <span className="ml-1">{brief.status.replace('_', ' ').toUpperCase()}</span>
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4 line-clamp-2">{brief.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center text-sm text-gray-500">
                  <DollarSign className="h-4 w-4 mr-2" />
                  {getBudgetDisplay(brief.budget_range)}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="h-4 w-4 mr-2" />
                  {brief.timeline_preference.replace(/_/g, ' ')}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <MapPin className="h-4 w-4 mr-2" />
                  {brief.location}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  {formatDate(brief.created_at)}
                </div>
              </div>

              {brief.assigned_designer_name && (
                <div className="flex items-center text-sm text-gray-600 mb-4">
                  <User className="h-4 w-4 mr-2" />
                  Assigned to: {brief.assigned_designer_name}
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {brief.proposal_count > 0 && (
                    <span className="text-sm text-gray-500">
                      {brief.proposal_count} proposal{brief.proposal_count !== 1 ? 's' : ''} received
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Link href={`/client/briefs/${brief.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </Link>
                  {brief.proposal_count > 0 && (
                    <Link href={`/client/briefs/${brief.id}/proposals`}>
                      <Button size="sm" className="bg-brown-600 hover:bg-brown-700 text-white">
                        <FileText className="h-4 w-4 mr-2" />
                        Review Proposals ({brief.proposal_count})
                      </Button>
                    </Link>
                  )}
                  {brief.status === 'pending' && (
                    <Link href={`/client/briefs/${brief.id}/send`}>
                      <Button size="sm" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                        <Send className="h-4 w-4 mr-2" />
                        Send to Designer
                      </Button>
                    </Link>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
