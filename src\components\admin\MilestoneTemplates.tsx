"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  Target,
  Plus,
  Edit,
  Trash2,
  Copy,
  CheckCircle,
  Clock,
  DollarSign,
  Calendar,
  Save,
  X,
  FileText
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface MilestoneTemplate {
  id: string;
  name: string;
  description: string;
  service_category: string;
  milestones: Milestone[];
  is_default: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  percentage: number;
  order_index: number;
  estimated_days: number;
  deliverables: string[];
}

export function MilestoneTemplates() {
  const { user } = useOptimizedAuth();
  const [templates, setTemplates] = useState<MilestoneTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MilestoneTemplate | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const serviceCategories = [
    'Interior Design',
    'Architectural Design',
    'Landscape Architecture',
    'Urban Planning',
    'Residential Projects',
    'Commercial Projects',
    'Educational Spaces'
  ];

  useEffect(() => {
    fetchTemplates();
  }, [selectedCategory]);

  const fetchTemplates = async () => {
    try {
      let query = supabase
        .from('milestone_templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (selectedCategory !== 'all') {
        query = query.eq('service_category', selectedCategory);
      }

      const { data, error } = await query;

      if (error && !error.message?.includes('does not exist')) {
        throw error;
      }

      if (error?.message?.includes('does not exist')) {
        console.warn('Milestone templates table not found');
        setTemplates([]);
      } else {
        setTemplates(data || []);
      }
    } catch (error: any) {
      console.error('Error fetching templates:', error);
      if (error.message?.includes('does not exist')) {
        console.warn('Milestone templates table not found');
        setTemplates([]);
      }
    } finally {
      setLoading(false);
    }
  };

  const createDefaultTemplates = async () => {
    const defaultTemplates = [
      {
        name: 'Interior Design - Residential',
        description: 'Standard milestone template for residential interior design projects',
        service_category: 'Interior Design',
        is_default: true,
        milestones: [
          {
            title: 'Initial Consultation & Planning',
            description: 'Client consultation, space assessment, and initial design concepts',
            percentage: 20,
            order_index: 1,
            estimated_days: 7,
            deliverables: ['Space assessment report', 'Initial design concepts', 'Project timeline']
          },
          {
            title: 'Design Development',
            description: 'Detailed design development and material selection',
            percentage: 30,
            order_index: 2,
            estimated_days: 14,
            deliverables: ['Detailed floor plans', 'Material palette', '3D renderings']
          },
          {
            title: 'Final Design & Documentation',
            description: 'Final design approval and construction documentation',
            percentage: 25,
            order_index: 3,
            estimated_days: 10,
            deliverables: ['Final design package', 'Construction drawings', 'Specifications']
          },
          {
            title: 'Project Completion',
            description: 'Final walkthrough and project handover',
            percentage: 25,
            order_index: 4,
            estimated_days: 5,
            deliverables: ['Final walkthrough', 'Project documentation', 'Maintenance guide']
          }
        ],
        created_by: user?.id
      },
      {
        name: 'Architectural Design - Commercial',
        description: 'Comprehensive milestone template for commercial architectural projects',
        service_category: 'Architectural Design',
        is_default: true,
        milestones: [
          {
            title: 'Schematic Design',
            description: 'Initial design concepts and space programming',
            percentage: 15,
            order_index: 1,
            estimated_days: 14,
            deliverables: ['Site analysis', 'Conceptual drawings', 'Space program']
          },
          {
            title: 'Design Development',
            description: 'Detailed design development and system coordination',
            percentage: 25,
            order_index: 2,
            estimated_days: 21,
            deliverables: ['Detailed drawings', 'System coordination', 'Material specifications']
          },
          {
            title: 'Construction Documents',
            description: 'Complete construction documentation and permit drawings',
            percentage: 35,
            order_index: 3,
            estimated_days: 28,
            deliverables: ['Construction drawings', 'Specifications', 'Permit documents']
          },
          {
            title: 'Construction Administration',
            description: 'Construction oversight and project completion',
            percentage: 25,
            order_index: 4,
            estimated_days: 60,
            deliverables: ['Site visits', 'Progress reports', 'Final inspection']
          }
        ],
        created_by: user?.id
      }
    ];

    try {
      for (const template of defaultTemplates) {
        const { error } = await supabase
          .from('milestone_templates')
          .insert(template);

        if (error) throw error;
      }

      fetchTemplates();
      console.log('Default templates created successfully');
    } catch (error) {
      console.error('Error creating default templates:', error);
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    try {
      const { error } = await supabase
        .from('milestone_templates')
        .delete()
        .eq('id', templateId);

      if (error) throw error;

      setTemplates(prev => prev.filter(t => t.id !== templateId));
      console.log('Template deleted successfully');
    } catch (error) {
      console.error('Error deleting template:', error);
    }
  };

  const handleDuplicateTemplate = async (template: MilestoneTemplate) => {
    try {
      const duplicatedTemplate = {
        ...template,
        name: `${template.name} (Copy)`,
        is_default: false,
        created_by: user?.id
      };

      delete (duplicatedTemplate as any).id;
      delete (duplicatedTemplate as any).created_at;
      delete (duplicatedTemplate as any).updated_at;

      const { error } = await supabase
        .from('milestone_templates')
        .insert(duplicatedTemplate);

      if (error) throw error;

      fetchTemplates();
      console.log('Template duplicated successfully');
    } catch (error) {
      console.error('Error duplicating template:', error);
    }
  };

  const calculateTotalPercentage = (milestones: Milestone[]) => {
    return milestones.reduce((sum, milestone) => sum + milestone.percentage, 0);
  };

  const calculateTotalDays = (milestones: Milestone[]) => {
    return milestones.reduce((sum, milestone) => sum + milestone.estimated_days, 0);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-brown-600 mr-3" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Milestone Templates</h3>
              <p className="text-sm text-gray-600">Pre-built milestone structures for different project types</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              {serviceCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-brown-600 hover:bg-brown-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {templates.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Templates Found</h4>
            <p className="text-gray-600 mb-6">Get started by creating default templates for common project types.</p>
            <Button
              onClick={createDefaultTemplates}
              className="bg-brown-600 hover:bg-brown-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Default Templates
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {templates.map((template) => (
              <motion.div
                key={template.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
                className="border border-gray-200 rounded-lg p-6 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-semibold text-gray-900">{template.name}</h4>
                      {template.is_default && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-brown-100 text-brown-800 border border-brown-200">
                          Default
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="bg-gray-100 px-2 py-1 rounded text-xs">{template.service_category}</span>
                      <span>{template.milestones.length} milestones</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      onClick={() => setEditingTemplate(template)}
                      variant="outline"
                      size="sm"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={() => handleDuplicateTemplate(template)}
                      variant="outline"
                      size="sm"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    {!template.is_default && (
                      <Button
                        onClick={() => handleDeleteTemplate(template.id)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Milestone Summary */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Progress Distribution:</span>
                    <span className="font-medium">{calculateTotalPercentage(template.milestones)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Estimated Duration:</span>
                    <span className="font-medium">{calculateTotalDays(template.milestones)} days</span>
                  </div>

                  {/* Milestone List */}
                  <div className="space-y-2">
                    {template.milestones.slice(0, 3).map((milestone, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span className="text-gray-700">{milestone.title}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-500">{milestone.percentage}%</span>
                          <span className="text-gray-400">•</span>
                          <span className="text-gray-500">{milestone.estimated_days}d</span>
                        </div>
                      </div>
                    ))}
                    {template.milestones.length > 3 && (
                      <div className="text-sm text-gray-500 text-center">
                        +{template.milestones.length - 3} more milestones
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}
