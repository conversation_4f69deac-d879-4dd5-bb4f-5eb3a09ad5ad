"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import Link from "next/link";
import ProposalComments from "@/components/proposals/ProposalComments";
import ChangeRequestsList from "@/components/proposals/ChangeRequestsList";
import RequestChangesForm from "@/components/proposals/RequestChangesForm";
import AgreementConfirmation from "@/components/proposals/AgreementConfirmation";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertCircle,
  Download,
  FileText,
  Calendar,
  DollarSign,
  Clock,
  User,
  Briefcase,
  Loader2,
  MessageSquare,
  RefreshCw,
  Lock
} from "lucide-react";

type Proposal = {
  id: string;
  title: string;
  description: string;
  scope: string;
  timeline: string;
  total_budget: number;
  status: string;
  created_at: string;
  designer_id: string;
  designer_name: string;
  designer_avatar: string | null;
};

type ProposalMilestone = {
  id: string;
  title: string;
  description: string;
  amount: number;
  percentage: number;
  estimated_days: number;
  deliverables: string[];
};

type ProposalAttachment = {
  id: string;
  file_url: string;
  file_name: string;
  file_type: string;
  file_size: number;
};

export default function ProposalDetail() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const proposalId = params.proposalId as string;

  const [proposal, setProposal] = useState<Proposal | null>(null);
  const [milestones, setMilestones] = useState<ProposalMilestone[]>([]);
  const [attachments, setAttachments] = useState<ProposalAttachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'details' | 'negotiation' | 'attachments'>('details');
  const [showRequestChanges, setShowRequestChanges] = useState(false);
  const [showAgreementConfirmation, setShowAgreementConfirmation] = useState(false);

  useEffect(() => {
    if (user && projectId && proposalId) {
      fetchProposalData();
    }
  }, [user, projectId, proposalId]);

  const fetchProposalData = async () => {
    setLoading(true);
    try {
      // Fetch proposal details
      const { data: proposalData, error: proposalError } = await supabase
        .from('project_proposals')
        .select(`
          id,
          title,
          description,
          scope,
          timeline,
          total_budget,
          status,
          created_at,
          designer_id,
          profiles!project_proposals_designer_id_fkey(full_name, avatar_url)
        `)
        .eq('id', proposalId)
        .eq('project_id', projectId)
        .single();

      if (proposalError) throw proposalError;

      // Format proposal data
      const formattedProposal: Proposal = {
        id: proposalData.id,
        title: proposalData.title,
        description: proposalData.description,
        scope: proposalData.scope,
        timeline: proposalData.timeline,
        total_budget: proposalData.total_budget,
        status: proposalData.status,
        created_at: proposalData.created_at,
        designer_id: proposalData.designer_id,
        designer_name: proposalData.profiles.full_name,
        designer_avatar: proposalData.profiles.avatar_url
      };

      setProposal(formattedProposal);

      // Fetch proposal milestones
      const { data: milestonesData, error: milestonesError } = await supabase
        .from('proposal_milestones')
        .select('*')
        .eq('proposal_id', proposalId)
        .order('id', { ascending: true });

      if (milestonesError) throw milestonesError;

      setMilestones(milestonesData);

      // Fetch proposal attachments
      const { data: attachmentsData, error: attachmentsError } = await supabase
        .from('proposal_attachments')
        .select('*')
        .eq('proposal_id', proposalId);

      if (attachmentsError) throw attachmentsError;

      setAttachments(attachmentsData);
    } catch (error) {
      console.error('Error fetching proposal data:', error);
      setError('Failed to load proposal data');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = () => {
    setShowAgreementConfirmation(true);
  };

  const handleReject = async () => {
    if (!user || !proposal) return;

    setProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      // 1. Update proposal status
      const { error: proposalError } = await supabase
        .from('project_proposals')
        .update({
          status: 'client_rejected',
          client_rejected_at: new Date().toISOString(),
          client_rejected_by: user.id
        })
        .eq('id', proposalId);

      if (proposalError) throw proposalError;

      // 2. Create notification for designer
      const { error: notificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: proposal.designer_id,
          type: 'proposal',
          title: 'Proposal Rejected',
          content: `Your proposal for project has been rejected. Please contact the client for more information.`,
          related_id: proposalId,
          read: false
        });

      if (notificationError) throw notificationError;

      setSuccess('Proposal rejected successfully.');

      // Refresh proposal data
      await fetchProposalData();
    } catch (error) {
      console.error('Error rejecting proposal:', error);
      setError('Failed to reject proposal. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-amber-50 border border-amber-200 text-amber-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending Admin Review
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-50 border border-green-200 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Ready for Client Review
          </span>
        );
      case 'client_approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-50 border border-green-200 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved by Client
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-red-50 border border-red-200 text-red-800">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected by Admin
          </span>
        );
      case 'client_rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-red-50 border border-red-200 text-red-800">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected by Client
          </span>
        );
      case 'revision_requested':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-yellow-50 border border-yellow-200 text-yellow-800">
            <RefreshCw className="h-3 w-3 mr-1" />
            Changes Requested
          </span>
        );
      case 'revision_in_progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-blue-50 border border-blue-200 text-blue-800">
            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            Revision in Progress
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-gray-50 border border-gray-200 text-gray-800">
            {status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
        );
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center mb-6">
        <Link href={`/client/projects/${projectId}`}>
          <Button variant="ghost" className="p-0 h-auto mr-4">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Project Proposal</h1>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      {success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-green-50 border border-green-200 p-4 mb-6 flex items-start"
        >
          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-green-700">{success}</p>
        </motion.div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <div className="h-8 w-8 border-t-2 border-b-2 border-brown-600"></div>
          </motion.div>
        </div>
      ) : proposal ? (
        <div className="space-y-8">
          {/* Proposal Header */}
          <div className="bg-white border border-gray-200 p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
              <div className="mb-4 md:mb-0">
                <h2 className="text-xl font-semibold">{proposal.title}</h2>
                <div className="flex items-center text-gray-500 mt-1">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>Submitted on {formatDate(proposal.created_at)}</span>
                </div>
                {proposal.status === 'pending' && (
                  <div className="flex items-center mt-2 text-yellow-600">
                    <Clock className="h-4 w-4 mr-1" />
                    <span className="text-sm">Pending admin review</span>
                  </div>
                )}
                {proposal.status === 'rejected' && (
                  <div className="flex items-center mt-2 text-red-600">
                    <XCircle className="h-4 w-4 mr-1" />
                    <span className="text-sm">Rejected by admin</span>
                  </div>
                )}
              </div>
              <div className="flex flex-col items-start md:items-end">
                <div className="mb-2">
                  {getStatusBadge(proposal.status)}
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {formatCurrency(proposal.total_budget)}
                </div>
              </div>
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200 mb-6">
              <div className="flex space-x-8">
                <button
                  onClick={() => setActiveTab('details')}
                  className={`pb-2 px-1 ${
                    activeTab === 'details'
                      ? 'border-b-2 border-brown-600 text-brown-600 font-medium'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    Proposal Details
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('negotiation')}
                  className={`pb-2 px-1 ${
                    activeTab === 'negotiation'
                      ? 'border-b-2 border-brown-600 text-brown-600 font-medium'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Negotiation
                    {proposal.status === 'revision_requested' && (
                      <span className="ml-2 bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full">
                        Changes Requested
                      </span>
                    )}
                  </div>
                </button>
                {attachments.length > 0 && (
                  <button
                    onClick={() => setActiveTab('attachments')}
                    className={`pb-2 px-1 ${
                      activeTab === 'attachments'
                        ? 'border-b-2 border-brown-600 text-brown-600 font-medium'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      Attachments ({attachments.length})
                    </div>
                  </button>
                )}
              </div>
            </div>

            <div className="flex items-center mb-6 pb-6 border-b border-gray-200">
              <div className="w-10 h-10 bg-gray-100 flex items-center justify-center border border-gray-200 mr-3">
                {proposal.designer_avatar ? (
                  <img
                    src={proposal.designer_avatar}
                    alt={proposal.designer_name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <User className="h-5 w-5 text-gray-400" />
                )}
              </div>
              <div>
                <p className="font-medium">{proposal.designer_name}</p>
                <p className="text-sm text-gray-500">Designer</p>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'details' && (
              <>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Description</h3>
                    <p className="text-gray-900">{proposal.description}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Scope of Work</h3>
                    <p className="text-gray-900 whitespace-pre-line">{proposal.scope}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Project Timeline</h3>
                    <p className="text-gray-900 whitespace-pre-line">{proposal.timeline}</p>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'negotiation' && (
              <div className="space-y-6">
                {showRequestChanges ? (
                  <RequestChangesForm
                    proposalId={proposalId}
                    onSuccess={() => {
                      setShowRequestChanges(false);
                      fetchProposalData();
                    }}
                    onCancel={() => setShowRequestChanges(false)}
                  />
                ) : (
                  <>
                    <ChangeRequestsList
                      proposalId={proposalId}
                      userRole="client"
                      onRequestChanges={() => setShowRequestChanges(true)}
                    />

                    <ProposalComments
                      proposalId={proposalId}
                      userRole="client"
                    />
                  </>
                )}
              </div>
            )}

            {activeTab === 'attachments' && attachments.length > 0 && (
              <div className="space-y-2">
                {attachments.map((attachment) => (
                  <a
                    key={attachment.id}
                    href={attachment.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-3 border border-gray-200 hover:bg-gray-50"
                  >
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="font-medium">{attachment.file_name}</p>
                        <p className="text-xs text-gray-500">
                          {(attachment.file_size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Download className="h-4 w-4 text-gray-500" />
                  </a>
                ))}
              </div>
            )}
          </div>

          {activeTab === 'details' && (
            <>
              {/* Milestones */}
              <div className="bg-white border border-gray-200 p-6">
                <h2 className="text-lg font-semibold mb-4">Payment Milestones</h2>

                {milestones.length === 0 ? (
                  <p className="text-gray-500">No milestones defined for this proposal.</p>
                ) : (
                  <div className="space-y-6">
                    {milestones.map((milestone, index) => (
                      <div key={milestone.id} className="border border-gray-200 p-4">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                          <div>
                            <h3 className="font-medium">Milestone {index + 1}: {milestone.title}</h3>
                            <p className="text-sm text-gray-500">{milestone.description}</p>
                          </div>
                          <div className="mt-2 md:mt-0 flex flex-col items-start md:items-end">
                            <div className="text-lg font-semibold">{formatCurrency(milestone.amount)}</div>
                            <div className="text-sm text-gray-500">{milestone.percentage}% of total</div>
                          </div>
                        </div>

                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-500 mb-1">Estimated Time</h4>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 text-gray-400 mr-1" />
                            <span>{milestone.estimated_days} days</span>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-sm font-medium text-gray-500 mb-1">Deliverables</h4>
                          <ul className="list-disc pl-5 space-y-1">
                            {milestone.deliverables.map((deliverable, dIndex) => (
                              <li key={dIndex} className="text-gray-900">{deliverable}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}

          {/* Agreement Confirmation */}
          {showAgreementConfirmation && (
            <div className="mb-8">
              <AgreementConfirmation
                proposalId={proposalId}
                projectId={projectId}
                onSuccess={() => {
                  setShowAgreementConfirmation(false);
                  fetchProposalData();
                  // Redirect to project page after a delay
                  setTimeout(() => {
                    router.push(`/client/projects/${projectId}`);
                  }, 3000);
                }}
                onCancel={() => setShowAgreementConfirmation(false)}
              />
            </div>
          )}

          {/* Action Buttons */}
          {proposal.status === 'approved' && !showAgreementConfirmation && (
            <div className="flex justify-end space-x-4">
              <Button
                variant="outline"
                className="flex items-center border-red-200 text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleReject}
                disabled={processing}
              >
                {processing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <XCircle className="h-4 w-4 mr-2" />
                )}
                Reject Proposal
              </Button>

              <Button
                variant="outline"
                className="flex items-center"
                onClick={() => {
                  setActiveTab('negotiation');
                  setShowRequestChanges(true);
                }}
                disabled={processing}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Request Changes
              </Button>

              <Button
                className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
                onClick={handleApprove}
                disabled={processing}
              >
                {processing ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Lock className="h-4 w-4 mr-2" />
                )}
                Approve & Sign Agreement
              </Button>
            </div>
          )}

          {/* Show action buttons for revision_requested status */}
          {proposal.status === 'revision_requested' && !showAgreementConfirmation && (
            <div className="flex justify-end space-x-4">
              <Button
                className="flex items-center"
                onClick={() => setActiveTab('negotiation')}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                View Requested Changes
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-white border border-gray-200 p-12 text-center">
          <AlertCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-medium text-gray-900 mb-1">Proposal Not Found</h2>
          <p className="text-gray-500 mb-6">
            The proposal you are looking for does not exist or has been removed.
          </p>
          <Link href={`/client/projects/${projectId}`}>
            <Button className="bg-brown-600 hover:bg-brown-700 text-white border-0">
              Back to Project
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}