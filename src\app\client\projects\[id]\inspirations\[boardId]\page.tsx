"use client";

import { useEffect, useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  ArrowLeft,
  Upload,
  Edit,
  Trash2,
  Plus,
  X,
  Download,
  MessageSquare
} from "lucide-react";

type InspirationBoard = {
  id: string;
  title: string;
  description: string | null;
  created_at: string;
  project_id: string;
};

type InspirationImage = {
  id: string;
  image_url: string;
  caption: string | null;
  created_at: string;
};

export default function InspirationBoardDetail() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const boardId = params.boardId as string;

  const [board, setBoard] = useState<InspirationBoard | null>(null);
  const [images, setImages] = useState<InspirationImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [newFiles, setNewFiles] = useState<File[]>([]);
  const [filePreviewUrls, setFilePreviewUrls] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  useEffect(() => {
    if (user && projectId && boardId) {
      fetchBoardData();
    }
  }, [user, projectId, boardId]);

  const fetchBoardData = async () => {
    setLoading(true);
    try {
      // Fetch board details
      const { data: boardData, error: boardError } = await supabase
        .from('inspiration_boards')
        .select('id, title, description, created_at, project_id')
        .eq('id', boardId)
        .single();

      if (boardError) throw boardError;
      setBoard(boardData);

      // Fetch images
      const { data: imagesData, error: imagesError } = await supabase
        .from('inspiration_images')
        .select('id, image_url, caption, created_at')
        .eq('board_id', boardId)
        .order('created_at', { ascending: false });

      if (imagesError) throw imagesError;
      setImages(imagesData || []);

    } catch (error: Error | unknown) {
      console.error('Error fetching board data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load inspiration board');
    } finally {
      setLoading(false);
    }
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setNewFiles(files);

      // Create preview URLs
      const previewUrls = files.map(file => URL.createObjectURL(file));
      setFilePreviewUrls(previewUrls);
    }
  };

  const removeFile = (index: number) => {
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(filePreviewUrls[index]);

    setNewFiles(prev => prev.filter((_, i) => i !== index));
    setFilePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (newFiles.length === 0) return;

    setUploading(true);
    try {
      for (const file of newFiles) {
        // Upload file to storage
        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `inspiration_images/${boardId}/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from('project-files')
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('project-files')
          .getPublicUrl(filePath);

        // Add image to inspiration_images table
        const { error: imageError } = await supabase
          .from('inspiration_images')
          .insert({
            board_id: boardId,
            image_url: urlData.publicUrl,
            caption: file.name
          });

        if (imageError) throw imageError;
      }

      // Clear files and refresh data
      setNewFiles([]);
      setFilePreviewUrls([]);
      setShowUploadModal(false);
      fetchBoardData();
    } catch (error: unknown) {
      console.error('Error uploading images:', error);
      let errorMessage = 'Failed to upload images';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        // Handle Supabase error objects
        const supabaseError = error as any;
        if (supabaseError.message) {
          errorMessage = supabaseError.message;
        } else if (supabaseError.error) {
          errorMessage = supabaseError.error;
        } else {
          errorMessage = JSON.stringify(error);
        }
      }

      setError(errorMessage);
    } finally {
      setUploading(false);
    }
  };
  const handleDeleteImage = async (imageId: string) => {
    try {
      const { error } = await supabase
        .from('inspiration_images')
        .delete()
        .eq('id', imageId);

      if (error) throw error;

      // Refresh images
      setImages(prev => prev.filter(img => img.id !== imageId));
    } catch (error: unknown) {
      console.error('Error deleting image:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete image');
    }
  };  const handleDeleteBoard = async () => {    if (!deleteConfirm) {
      setDeleteConfirm(true);
      return;
    }

    try {
      const { error } = await supabase
        .from('inspiration_boards')
        .delete()
        .eq('id', boardId);

      if (error) throw error;

      router.push(`/client/projects/${projectId}`);
    } catch (error: unknown) {
      console.error('Error deleting board:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete board');
    }
  };  const formatDate = (dateString: string) => {    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !board) {
    return (
      <div className="bg-red-50 text-red-500 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Error</h2>
        <p>{error || 'Inspiration board not found'}</p>
        <Link href={`/client/projects/${projectId}`}>
          <Button variant="outline" className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <Link href={`/client/projects/${projectId}`} className="inline-flex items-center text-gray-600 hover:text-primary">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Project
        </Link>
      </div>

      {/* Board Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold mb-2">{board.title}</h1>
            <p className="text-sm text-gray-500 mb-2">
              Created on {formatDate(board.created_at)}
            </p>
            {board.description && (
              <p className="text-gray-700 mt-4">{board.description}</p>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={() => setShowUploadModal(true)}
            >
              <Upload className="mr-1 h-4 w-4" />
              Add Images
            </Button>
            <Button variant="outline" size="sm" className="flex items-center">
              <Edit className="mr-1 h-4 w-4" />
              Edit
            </Button>
            <Button
              variant="outline"
              size="sm"
              className={`flex items-center ${deleteConfirm ? 'bg-red-50 text-red-500 border-red-300' : ''}`}
              onClick={handleDeleteBoard}
            >
              <Trash2 className="mr-1 h-4 w-4" />
              {deleteConfirm ? 'Confirm Delete' : 'Delete'}
            </Button>
          </div>
        </div>
      </div>

      {/* Images Gallery */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-6">
          Inspiration Images ({images.length})
        </h2>

        {images.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No images in this board yet.</p>
            <Button
              onClick={() => setShowUploadModal(true)}
              className="flex items-center mx-auto"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Images
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {images.map((image) => (
              <div key={image.id} className="group relative">
                <div className="aspect-square overflow-hidden bg-gray-100">
                  <img
                    src={image.image_url}
                    alt={image.caption || 'Inspiration image'}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                  <button
                    className="bg-black bg-opacity-50 text-white p-1.5 rounded-full hover:bg-opacity-70"
                    onClick={() => window.open(image.image_url, '_blank')}
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  <button
                    className="bg-black bg-opacity-50 text-white p-1.5 rounded-full hover:bg-opacity-70"
                    onClick={() => handleDeleteImage(image.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
                {image.caption && (
                  <p className="mt-2 text-sm text-gray-600 truncate">{image.caption}</p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Add Inspiration Images</h2>
                <button
                  onClick={() => {
                    setShowUploadModal(false);
                    setNewFiles([]);
                    setFilePreviewUrls([]);
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center mb-6">
                <input
                  type="file"
                  id="newInspirationFiles"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <label
                  htmlFor="newInspirationFiles"
                  className="cursor-pointer flex flex-col items-center justify-center"
                >
                  <Upload className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500 mb-1">
                    Drag and drop image files here, or click to select files
                  </p>
                  <p className="text-xs text-gray-400">
                    Upload images that inspire your design vision
                  </p>
                </label>
              </div>

              {filePreviewUrls.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    Selected Images ({filePreviewUrls.length})
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {filePreviewUrls.map((url, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={url}
                          alt={`New inspiration ${index + 1}`}
                          className="h-32 w-full object-cover"
                        />
                        <button
                          type="button"
                          onClick={() => removeFile(index)}
                          className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowUploadModal(false);
                    setNewFiles([]);
                    setFilePreviewUrls([]);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUpload}
                  disabled={newFiles.length === 0 || uploading}
                  className="flex items-center"
                >
                  {uploading ? "Uploading..." : "Upload Images"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
