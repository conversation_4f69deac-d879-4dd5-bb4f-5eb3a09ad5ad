# 📋 PAGE MIGRATION IMPLEMENTATION GUIDE
## Converting Direct Database Calls to Centralized Hooks

---

## 🎯 **MIGRATION STRATEGY**

### **Before Migration Pattern** ❌
```typescript
// OLD: Direct database calls in useEffect
const [data, setData] = useState([]);
const [loading, setLoading] = useState(true);

useEffect(() => {
  const fetchData = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('table')
        .select('*')
        .eq('user_id', user.id);
      
      if (error) throw error;
      setData(data);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  
  if (user) fetchData();
}, [user]);
```

### **After Migration Pattern** ✅
```typescript
// NEW: Centralized hooks with caching
const { data, isLoading, error } = useProjectDetails(projectId, user?.id || '', role);
```

---

## 🔧 **SPECIFIC PAGE MIGRATIONS**

### **1. Client Project Details** (`/client/projects/[id]/page.tsx`)

#### **Current Issues:**
- Direct supabase calls in `fetchProjectData()`
- Manual loading state management
- No caching or prefetching
- Repeated data fetching on navigation

#### **Migration Steps:**
```typescript
// REPLACE THIS:
const fetchProjectData = async () => {
  setLoading(true);
  try {
    const { data: projectData, error: projectError } = await supabase
      .from("projects")
      .select(`
        id, title, description, type, location, budget, status,
        created_at, updated_at, designer_id, scope, constraints,
        objectives, start_date, end_date, timeline, requirements,
        profiles!projects_designer_id_fkey(full_name)
      `)
      .eq("id", projectId)
      .eq("client_id", user?.id)
      .single();
    // ... more code
  } catch (error) {
    // error handling
  }
};

// WITH THIS:
const { data: project, isLoading, error } = useProjectDetails(
  projectId as string, 
  user?.id || '', 
  'client'
);
const { data: milestones } = useProjectMilestones(projectId as string);
const { data: inspirationBoards } = useInspirationBoards(projectId as string);
```

#### **Benefits:**
- ✅ Automatic caching and background refresh
- ✅ Built-in loading states and error handling
- ✅ Access control verification
- ✅ Prefetching for related data

### **2. Designer Briefs Page** (`/designer/briefs/page.tsx`)

#### **Current Issues:**
- Manual data fetching in `fetchBriefs()`
- No real-time updates
- Complex filtering logic

#### **Migration Steps:**
```typescript
// REPLACE THIS:
const fetchBriefs = async () => {
  setLoading(true);
  try {
    const { data, error } = await supabase
      .from('project_briefs')
      .select(`
        id, title, description, project_type, location,
        budget_range, timeline_preference, urgency, status,
        created_at, assigned_designer_id,
        profiles!project_briefs_client_id_fkey(
          id, full_name, avatar_url
        )
      `)
      .or(`assigned_designer_id.eq.${user.id},and(assigned_designer_id.is.null,status.eq.pending)`)
      .order('created_at', { ascending: false });
    // ... processing logic
  } catch (error) {
    // error handling
  }
};

// WITH THIS:
const { data: briefs, isLoading, error } = useProjectBriefs(
  user?.id || '', 
  'designer'
);
```

### **3. Brief Proposals Page** (`/client/briefs/[id]/proposals/page.tsx`)

#### **Migration Steps:**
```typescript
// REPLACE THIS:
const fetchData = async () => {
  setLoading(true);
  try {
    // Fetch brief info
    const { data: briefData, error: briefError } = await supabase
      .from('project_briefs')
      .select('id, title, description, budget_range, status')
      .eq('id', params.briefId)
      .eq('client_id', user.id)
      .single();

    // Fetch proposals for this brief
    const { data: proposalsData, error: proposalsError } = await supabase
      .from('project_proposals_enhanced')
      .select(`
        id, title, description, total_budget, timeline_weeks,
        status, submitted_at, expires_at, designer_id, milestones,
        profiles!project_proposals_enhanced_designer_id_fkey(
          full_name, avatar_url
        )
      `)
      .eq('brief_id', params.briefId)
      .order('submitted_at', { ascending: false });
    // ... processing
  } catch (error) {
    // error handling
  }
};

// WITH THIS:
const { data: briefInfo } = useProjectBriefs(user?.id || '', 'client');
const { data: proposals, isLoading } = useBriefProposals(
  params.briefId as string,
  user?.id || '',
  'client'
);

// Filter brief info from the briefs data
const currentBrief = briefInfo?.find(brief => brief.id === params.briefId);
```

### **4. Admin Project Details** (`/admin/projects/[id]/page.tsx`)

#### **Migration Steps:**
```typescript
// REPLACE THIS:
const fetchProjectData = async () => {
  setLoading(true);
  try {
    const { data: projectData, error: projectError } = await supabase
      .from('projects')
      .select(`
        id, title, description, status, deadline, created_at,
        updated_at, progress, requirements, budget, type,
        location, client_id, designer_id,
        client:profiles!client_id(full_name, email, avatar_url),
        designer:profiles!designer_id(full_name, email, avatar_url)
      `)
      .eq('id', projectId)
      .single();
    // ... more fetching
  } catch (error) {
    // error handling
  }
};

// WITH THIS:
const { data: project, isLoading, error } = useProjectDetails(
  projectId as string,
  user?.id || '',
  'admin'
);
const { data: milestones } = useProjectMilestones(projectId as string);
```

### **5. Inspiration Board Images** (`/client/projects/[id]/inspirations/[boardId]/page.tsx`)

#### **Migration Steps:**
```typescript
// REPLACE THIS:
const fetchBoardData = async () => {
  setLoading(true);
  try {
    // Fetch board details
    const { data: boardData, error: boardError } = await supabase
      .from('inspiration_boards')
      .select('id, title, description, created_at, project_id')
      .eq('id', boardId)
      .single();

    // Fetch images
    const { data: imagesData, error: imagesError } = await supabase
      .from('inspiration_images')
      .select('id, image_url, caption, created_at')
      .eq('board_id', boardId)
      .order('created_at', { ascending: false });
    // ... processing
  } catch (error) {
    // error handling
  }
};

// WITH THIS:
const { data: boards } = useInspirationBoards(projectId as string);
const { data: images, isLoading } = useInspirationBoardImages(boardId as string);

// Filter current board from boards data
const currentBoard = boards?.find(board => board.id === boardId);
```

---

## 🚀 **IMPLEMENTATION CHECKLIST**

### **For Each Page Migration:**

#### **Step 1: Identify Current Data Operations** ✅
- [ ] List all direct supabase calls
- [ ] Identify data dependencies
- [ ] Note access control requirements
- [ ] Document current loading states

#### **Step 2: Replace with Centralized Hooks** 🔄
- [ ] Import appropriate hooks from `useDashboardData`
- [ ] Replace useEffect + useState with hook calls
- [ ] Remove manual loading state management
- [ ] Remove direct error handling (hooks handle this)

#### **Step 3: Update Component Logic** 📝
- [ ] Update loading conditions
- [ ] Simplify error handling
- [ ] Remove manual data fetching functions
- [ ] Update data access patterns

#### **Step 4: Add Optimizations** ⚡
- [ ] Add prefetching for related data
- [ ] Implement optimistic updates where appropriate
- [ ] Add hover-based prefetching for navigation
- [ ] Configure appropriate cache times

#### **Step 5: Testing** 🧪
- [ ] Verify all data loads correctly
- [ ] Test loading states and error handling
- [ ] Confirm access control works
- [ ] Check performance improvements

---

## 📊 **PERFORMANCE VERIFICATION**

### **Before Migration Metrics:**
```typescript
// Measure current performance
const startTime = performance.now();
// ... page load
const loadTime = performance.now() - startTime;
console.log(`Page load time: ${loadTime}ms`);
```

### **After Migration Metrics:**
```typescript
// Verify improvements
const { trackPageLoad } = usePerformanceMonitoring();
useEffect(() => {
  trackPageLoad();
}, []);
```

### **Expected Improvements:**
- **First Load**: 30-50% faster due to optimized queries
- **Subsequent Loads**: 80-90% faster due to caching
- **Navigation**: Near-instant due to prefetching
- **Data Freshness**: Automatic background updates

---

## ⚠️ **MIGRATION SAFETY GUIDELINES**

### **Preserve Functionality:**
- ✅ Test each page thoroughly after migration
- ✅ Verify all user interactions work correctly
- ✅ Ensure access controls are maintained
- ✅ Check error handling edge cases

### **Rollback Plan:**
- 🔄 Keep original code commented out initially
- 🔄 Test in development environment first
- 🔄 Deploy incrementally (one page at a time)
- 🔄 Monitor performance metrics closely

### **Data Integrity:**
- 🛡️ No changes to database operations
- 🛡️ Same queries, just centralized
- 🛡️ Identical access control logic
- 🛡️ Preserved error handling patterns

---

## 🎯 **SUCCESS CRITERIA**

### **Technical Metrics:**
- [ ] Page load times < 1 second
- [ ] Cache hit rate > 80%
- [ ] Error rate < 2%
- [ ] Navigation time < 100ms

### **User Experience:**
- [ ] Instant sidebar navigation
- [ ] Persistent data across tabs
- [ ] Real-time data updates
- [ ] Smooth loading transitions

### **Code Quality:**
- [ ] Reduced code duplication
- [ ] Centralized error handling
- [ ] Consistent loading patterns
- [ ] Improved maintainability
