-- Phase 3: Admin Review Interfaces & Financial Management
-- Database Schema Updates

-- 1. <PERSON><PERSON> Settings Table
CREATE TABLE IF NOT EXISTS fee_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    platform_commission_rate DECIMAL(5,2) NOT NULL DEFAULT 15.00, -- Percentage (e.g., 15.00 for 15%)
    payment_processing_fee DECIMAL(5,2) NOT NULL DEFAULT 2.90, -- Percentage (e.g., 2.90 for 2.9%)
    designer_payout_rate DECIMAL(5,2) NOT NULL DEFAULT 85.00, -- Percentage (e.g., 85.00 for 85%)
    minimum_project_value DECIMAL(10,2) NOT NULL DEFAULT 100.00, -- Minimum project value in USD
    maximum_commission_cap DECIMAL(10,2) NULL, -- Optional maximum commission amount
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES profiles(id)
);

-- 2. Miles<PERSON> Templates Table
CREATE TABLE IF NOT EXISTS milestone_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    service_category VARCHAR(100) NOT NULL,
    milestones JSONB NOT NULL, -- Array of milestone objects
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES profiles(id)
);

-- 3. Enhanced Transactions Table (if not exists)
CREATE TABLE IF NOT EXISTS transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    amount DECIMAL(10,2) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('payment', 'payout', 'refund', 'fee')),
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    payment_method VARCHAR(50), -- 'stripe', 'paypal', 'bank_transfer', etc.
    stripe_payment_intent_id VARCHAR(255),
    paypal_order_id VARCHAR(255),
    project_id UUID REFERENCES projects(id),
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    milestone_id UUID, -- References project milestones
    platform_fee DECIMAL(10,2) DEFAULT 0.00,
    processing_fee DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2), -- Amount after fees
    description TEXT,
    metadata JSONB, -- Additional payment metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Project Milestones Table (Enhanced)
CREATE TABLE IF NOT EXISTS project_milestones (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    percentage DECIMAL(5,2) NOT NULL, -- Percentage of total project value
    amount DECIMAL(10,2), -- Calculated amount based on percentage
    order_index INTEGER NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'approved')),
    estimated_days INTEGER,
    actual_days INTEGER,
    deliverables JSONB, -- Array of deliverable items
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Designer Performance Metrics Table
CREATE TABLE IF NOT EXISTS designer_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    designer_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    total_projects INTEGER DEFAULT 0,
    completed_projects INTEGER DEFAULT 0,
    active_projects INTEGER DEFAULT 0,
    total_earnings DECIMAL(10,2) DEFAULT 0.00,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    on_time_completion_rate DECIMAL(5,2) DEFAULT 0.00, -- Percentage
    client_satisfaction_score DECIMAL(3,2) DEFAULT 0.00,
    response_time_hours DECIMAL(8,2) DEFAULT 0.00, -- Average response time in hours
    last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Platform Analytics Table
CREATE TABLE IF NOT EXISTS platform_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    total_revenue DECIMAL(10,2) DEFAULT 0.00,
    total_payouts DECIMAL(10,2) DEFAULT 0.00,
    platform_fees DECIMAL(10,2) DEFAULT 0.00,
    processing_fees DECIMAL(10,2) DEFAULT 0.00,
    net_profit DECIMAL(10,2) DEFAULT 0.00,
    new_users INTEGER DEFAULT 0,
    new_projects INTEGER DEFAULT 0,
    completed_projects INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date)
);

-- 7. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_transactions_project_id ON transactions(project_id);
CREATE INDEX IF NOT EXISTS idx_transactions_client_id ON transactions(client_id);
CREATE INDEX IF NOT EXISTS idx_transactions_designer_id ON transactions(designer_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

CREATE INDEX IF NOT EXISTS idx_project_milestones_project_id ON project_milestones(project_id);
CREATE INDEX IF NOT EXISTS idx_project_milestones_status ON project_milestones(status);
CREATE INDEX IF NOT EXISTS idx_project_milestones_order_index ON project_milestones(order_index);

CREATE INDEX IF NOT EXISTS idx_milestone_templates_service_category ON milestone_templates(service_category);
CREATE INDEX IF NOT EXISTS idx_milestone_templates_is_default ON milestone_templates(is_default);

CREATE INDEX IF NOT EXISTS idx_designer_metrics_designer_id ON designer_metrics(designer_id);
CREATE INDEX IF NOT EXISTS idx_platform_analytics_date ON platform_analytics(date);

-- 8. Add RLS (Row Level Security) policies
ALTER TABLE fee_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE milestone_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_milestones ENABLE ROW LEVEL SECURITY;
ALTER TABLE designer_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_analytics ENABLE ROW LEVEL SECURITY;

-- Fee Settings Policies (Admin only)
CREATE POLICY "Admin can manage fee settings" ON fee_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Milestone Templates Policies
CREATE POLICY "Admin can manage milestone templates" ON milestone_templates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Designers can view milestone templates" ON milestone_templates
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('designer', 'admin')
        )
    );

-- Transactions Policies
CREATE POLICY "Users can view their own transactions" ON transactions
    FOR SELECT USING (
        client_id = auth.uid() OR 
        designer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Admin can manage all transactions" ON transactions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Project Milestones Policies
CREATE POLICY "Project participants can view milestones" ON project_milestones
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE projects.id = project_milestones.project_id 
            AND (projects.client_id = auth.uid() OR projects.designer_id = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Admin can manage all milestones" ON project_milestones
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Designer Metrics Policies
CREATE POLICY "Designers can view their own metrics" ON designer_metrics
    FOR SELECT USING (
        designer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Admin can manage designer metrics" ON designer_metrics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Platform Analytics Policies (Admin only)
CREATE POLICY "Admin can view platform analytics" ON platform_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Admin can manage platform analytics" ON platform_analytics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- 9. Add availability column to profiles if not exists
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'availability') THEN
        ALTER TABLE profiles ADD COLUMN availability BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- 10. Create functions for automated calculations
CREATE OR REPLACE FUNCTION calculate_milestone_amount()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate milestone amount based on percentage and project budget
    IF NEW.percentage IS NOT NULL AND EXISTS (
        SELECT 1 FROM projects 
        WHERE projects.id = NEW.project_id 
        AND projects.budget IS NOT NULL
    ) THEN
        SELECT (projects.budget * NEW.percentage / 100) INTO NEW.amount
        FROM projects 
        WHERE projects.id = NEW.project_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for milestone amount calculation
DROP TRIGGER IF EXISTS trigger_calculate_milestone_amount ON project_milestones;
CREATE TRIGGER trigger_calculate_milestone_amount
    BEFORE INSERT OR UPDATE ON project_milestones
    FOR EACH ROW
    EXECUTE FUNCTION calculate_milestone_amount();

-- 11. Create function to update designer metrics
CREATE OR REPLACE FUNCTION update_designer_metrics(designer_uuid UUID)
RETURNS VOID AS $$
DECLARE
    total_proj INTEGER;
    completed_proj INTEGER;
    active_proj INTEGER;
    total_earn DECIMAL(10,2);
BEGIN
    -- Calculate metrics
    SELECT COUNT(*) INTO total_proj
    FROM projects WHERE designer_id = designer_uuid;
    
    SELECT COUNT(*) INTO completed_proj
    FROM projects WHERE designer_id = designer_uuid AND status = 'completed';
    
    SELECT COUNT(*) INTO active_proj
    FROM projects WHERE designer_id = designer_uuid AND status IN ('assigned', 'in_progress');
    
    SELECT COALESCE(SUM(amount), 0) INTO total_earn
    FROM transactions 
    WHERE designer_id = designer_uuid AND type = 'payout' AND status = 'completed';
    
    -- Insert or update metrics
    INSERT INTO designer_metrics (
        designer_id, total_projects, completed_projects, active_projects, total_earnings, last_calculated_at
    ) VALUES (
        designer_uuid, total_proj, completed_proj, active_proj, total_earn, NOW()
    )
    ON CONFLICT (designer_id) DO UPDATE SET
        total_projects = EXCLUDED.total_projects,
        completed_projects = EXCLUDED.completed_projects,
        active_projects = EXCLUDED.active_projects,
        total_earnings = EXCLUDED.total_earnings,
        last_calculated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 12. Insert default fee settings if none exist
INSERT INTO fee_settings (platform_commission_rate, payment_processing_fee, designer_payout_rate, minimum_project_value)
SELECT 15.00, 2.90, 85.00, 100.00
WHERE NOT EXISTS (SELECT 1 FROM fee_settings);

-- 13. Comments for documentation
COMMENT ON TABLE fee_settings IS 'Platform fee configuration and commission rates';
COMMENT ON TABLE milestone_templates IS 'Pre-built milestone templates for different project types';
COMMENT ON TABLE transactions IS 'All financial transactions including payments, payouts, and fees';
COMMENT ON TABLE project_milestones IS 'Project milestones with payment tracking';
COMMENT ON TABLE designer_metrics IS 'Performance metrics and statistics for designers';
COMMENT ON TABLE platform_analytics IS 'Daily platform analytics and financial metrics';
