"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  MessageSquare,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  Calendar,
  Star,
  FileText,
  Settings,
  TrendingUp,
  RefreshCw,
  Send,
  Eye
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  description: string;
  status: string;
  budget: number;
  client_id: string;
  designer_id: string;
  client_name: string;
  designer_name: string;
  created_at: string;
  quality_status: string;
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  percentage: number;
  amount: number;
  status: string;
  due_date: string;
  order_index: number;
}

interface EscrowRelease {
  id: string;
  milestone_id: string;
  amount: number;
  status: string;
  created_at: string;
  milestone: {
    title: string;
  };
}

interface NegotiationSession {
  id: string;
  session_type: string;
  status: string;
  initial_terms: any;
  final_terms: any;
  started_at: string;
}

export default function ProjectCoordinationPage({ params }: { params: { id: string } }) {
  const { user, profile } = useOptimizedAuth();
  const [project, setProject] = useState<Project | null>(null);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [escrowReleases, setEscrowReleases] = useState<EscrowRelease[]>([]);
  const [negotiations, setNegotiations] = useState<NegotiationSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'milestones' | 'escrow' | 'negotiations'>('overview');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchProjectData();
    }
  }, [user, profile, params.id]);

  const fetchProjectData = async () => {
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', params.id)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // Fetch milestones
      const { data: milestonesData } = await supabase
        .from('project_milestones')
        .select('*')
        .eq('project_id', params.id)
        .order('order_index');

      setMilestones(milestonesData || []);

      // Fetch escrow releases
      const { data: escrowData } = await supabase
        .from('escrow_releases')
        .select(`
          *,
          milestone:project_milestones(title)
        `)
        .eq('project_id', params.id)
        .eq('manager_id', user?.id);

      setEscrowReleases(escrowData || []);

      // Fetch negotiations
      const { data: negotiationsData } = await supabase
        .from('negotiation_sessions')
        .select('*')
        .eq('project_id', params.id)
        .eq('manager_id', user?.id);

      setNegotiations(negotiationsData || []);

    } catch (error) {
      console.error('Error fetching project data:', error);
    } finally {
      setLoading(false);
    }
  };

  const approveEscrowRelease = async (releaseId: string) => {
    try {
      const { error } = await supabase
        .from('escrow_releases')
        .update({
          status: 'approved',
          manager_approval_at: new Date().toISOString()
        })
        .eq('id', releaseId);

      if (error) throw error;

      // Log manager activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: params.id,
        activity_type: 'escrow_approval',
        description: 'Approved escrow release for milestone completion'
      });

      fetchProjectData();
    } catch (error) {
      console.error('Error approving escrow release:', error);
    }
  };

  const startNegotiation = async (type: string) => {
    try {
      const { data, error } = await supabase
        .from('negotiation_sessions')
        .insert({
          project_id: params.id,
          manager_id: user?.id,
          client_id: project?.client_id,
          designer_id: project?.designer_id,
          session_type: type,
          status: 'active'
        })
        .select()
        .single();

      if (error) throw error;

      // Navigate to negotiation interface
      window.location.href = `/manager/negotiations/${data.id}`;
    } catch (error) {
      console.error('Error starting negotiation:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800`;
      case 'in_progress':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Project Not Found</h1>
          <Button onClick={() => window.location.href = '/manager/dashboard'}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => window.location.href = '/manager/dashboard'}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">{project.title}</h1>
          <p className="text-gray-600 mt-1">Project Coordination & Management</p>
        </div>
        <Button
          onClick={fetchProjectData}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Project Overview */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Project Status</h3>
            <span className={getStatusBadge(project.status)}>
              {project.status.replace('_', ' ').toUpperCase()}
            </span>
            <div className="mt-4">
              <p className="text-sm text-gray-600">Quality Status:</p>
              <span className={getStatusBadge(project.quality_status)}>
                {project.quality_status.replace('_', ' ').toUpperCase()}
              </span>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Team</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Client:</span> {project.client_name}
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Designer:</span> {project.designer_name}
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Budget</h3>
            <p className="text-2xl font-bold text-green-600">${project.budget?.toLocaleString()}</p>
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                Started: {new Date(project.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => startNegotiation('pricing')}
          >
            <DollarSign className="h-5 w-5" />
            Start Pricing Negotiation
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => startNegotiation('timeline')}
          >
            <Clock className="h-5 w-5" />
            Discuss Timeline
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = `/manager/projects/${params.id}/communication`}
          >
            <MessageSquare className="h-5 w-5" />
            Manage Communication
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = `/manager/projects/${params.id}/satisfaction`}
          >
            <Star className="h-5 w-5" />
            Collect Feedback
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: Eye },
              { id: 'milestones', label: 'Milestones', icon: CheckCircle },
              { id: 'escrow', label: 'Escrow Releases', icon: DollarSign },
              { id: 'negotiations', label: 'Negotiations', icon: MessageSquare }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-brown-500 text-brown-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Project Description</h3>
                <p className="text-gray-600">{project.description}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Recent Activities</h4>
                  <p className="text-sm text-gray-600">No recent activities to display</p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Next Actions</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Monitor milestone progress</li>
                    <li>• Review quality submissions</li>
                    <li>• Coordinate team communication</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'milestones' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Project Milestones</h3>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = `/manager/projects/${params.id}/milestones`}
                >
                  Manage Milestones
                </Button>
              </div>
              
              {milestones.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No milestones defined for this project</p>
              ) : (
                <div className="space-y-4">
                  {milestones.map((milestone) => (
                    <div key={milestone.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{milestone.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{milestone.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                            <span>{milestone.percentage}% of project</span>
                            <span>${milestone.amount?.toLocaleString()}</span>
                            {milestone.due_date && (
                              <span>Due: {new Date(milestone.due_date).toLocaleDateString()}</span>
                            )}
                          </div>
                        </div>
                        <span className={getStatusBadge(milestone.status)}>
                          {milestone.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'escrow' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Escrow Releases</h3>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = `/manager/escrow`}
                >
                  View All Escrow
                </Button>
              </div>
              
              {escrowReleases.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No pending escrow releases</p>
              ) : (
                <div className="space-y-4">
                  {escrowReleases.map((release) => (
                    <div key={release.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{release.milestone.title}</h4>
                          <p className="text-lg font-semibold text-green-600">${release.amount.toLocaleString()}</p>
                          <p className="text-sm text-gray-500">
                            Requested: {new Date(release.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className={getStatusBadge(release.status)}>
                            {release.status.toUpperCase()}
                          </span>
                          {release.status === 'pending' && (
                            <Button
                              onClick={() => approveEscrowRelease(release.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              Approve Release
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'negotiations' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Negotiation Sessions</h3>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => startNegotiation('pricing')}
                  >
                    Start Pricing Discussion
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => startNegotiation('timeline')}
                  >
                    Discuss Timeline
                  </Button>
                </div>
              </div>
              
              {negotiations.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No negotiation sessions yet</p>
              ) : (
                <div className="space-y-4">
                  {negotiations.map((negotiation) => (
                    <div key={negotiation.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 capitalize">
                            {negotiation.session_type} Negotiation
                          </h4>
                          <p className="text-sm text-gray-500">
                            Started: {new Date(negotiation.started_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className={getStatusBadge(negotiation.status)}>
                            {negotiation.status.toUpperCase()}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.location.href = `/manager/negotiations/${negotiation.id}`}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
