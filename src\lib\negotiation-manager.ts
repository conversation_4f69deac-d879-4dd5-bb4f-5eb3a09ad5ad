'use client';

import { supabase } from '@/lib/supabase';

/**
 * Advanced Negotiation Management System
 * Handles complex negotiations between clients, designers, and managers
 */

export interface Negotiation {
  id: string;
  project_id: string;
  initiated_by: string;
  negotiation_type: 'scope_change' | 'timeline_extension' | 'budget_adjustment' | 'milestone_modification' | 'quality_revision';
  status: 'draft' | 'pending' | 'in_progress' | 'accepted' | 'rejected' | 'expired';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  current_terms: any;
  proposed_terms: any;
  manager_id?: string;
  client_approval_required: boolean;
  designer_approval_required: boolean;
  manager_approval_required: boolean;
  client_approved_at?: string;
  designer_approved_at?: string;
  manager_approved_at?: string;
  deadline: string;
  created_at: string;
  updated_at: string;
  metadata: any;
}

export interface NegotiationMessage {
  id: string;
  negotiation_id: string;
  sender_id: string;
  message_type: 'proposal' | 'counter_proposal' | 'clarification' | 'approval' | 'rejection' | 'comment';
  content: string;
  attachments?: string[];
  terms_changes?: any;
  created_at: string;
  metadata: any;
}

export interface NegotiationTemplate {
  id: string;
  name: string;
  negotiation_type: string;
  template_content: any;
  default_terms: any;
  approval_workflow: any;
  created_by: string;
  is_active: boolean;
}

/**
 * Negotiation Manager Class
 */
export class NegotiationManager {
  /**
   * Create a new negotiation
   */
  static async createNegotiation(params: {
    projectId: string;
    initiatedBy: string;
    negotiationType: string;
    title: string;
    description: string;
    currentTerms: any;
    proposedTerms: any;
    priority?: string;
    deadline?: string;
    managerId?: string;
  }): Promise<{ success: boolean; negotiation?: Negotiation; error?: string }> {
    try {
      const {
        projectId,
        initiatedBy,
        negotiationType,
        title,
        description,
        currentTerms,
        proposedTerms,
        priority = 'medium',
        deadline,
        managerId
      } = params;

      // Calculate deadline if not provided (default 7 days)
      const finalDeadline = deadline || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();

      // Determine approval requirements based on negotiation type
      const approvalRequirements = this.getApprovalRequirements(negotiationType, proposedTerms);

      // Get project manager if not provided
      let finalManagerId = managerId;
      if (!finalManagerId) {
        const { data: assignment } = await supabase
          .from('project_assignments')
          .select('manager_id')
          .eq('project_id', projectId)
          .eq('status', 'active')
          .single();
        
        finalManagerId = assignment?.manager_id;
      }

      // Create negotiation
      const { data: negotiation, error } = await supabase
        .from('negotiations')
        .insert({
          project_id: projectId,
          initiated_by: initiatedBy,
          negotiation_type: negotiationType,
          status: 'pending',
          priority,
          title,
          description,
          current_terms: currentTerms,
          proposed_terms: proposedTerms,
          manager_id: finalManagerId,
          client_approval_required: approvalRequirements.client,
          designer_approval_required: approvalRequirements.designer,
          manager_approval_required: approvalRequirements.manager,
          deadline: finalDeadline
        })
        .select()
        .single();

      if (error) throw error;

      // Create initial negotiation message
      await this.addNegotiationMessage({
        negotiationId: negotiation.id,
        senderId: initiatedBy,
        messageType: 'proposal',
        content: `Negotiation initiated: ${title}`,
        termsChanges: {
          from: currentTerms,
          to: proposedTerms
        }
      });

      // Send notifications to relevant parties
      await this.sendNegotiationNotifications(negotiation, 'created');

      // Log activity
      await this.logNegotiationActivity({
        negotiation_id: negotiation.id,
        project_id: projectId,
        activity_type: 'negotiation_created',
        description: `Negotiation created: ${title}`,
        performed_by: initiatedBy,
        metadata: {
          negotiation_type: negotiationType,
          priority
        }
      });

      return { success: true, negotiation };
    } catch (error) {
      console.error('Error creating negotiation:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Add message to negotiation
   */
  static async addNegotiationMessage(params: {
    negotiationId: string;
    senderId: string;
    messageType: string;
    content: string;
    attachments?: string[];
    termsChanges?: any;
  }): Promise<{ success: boolean; message?: NegotiationMessage; error?: string }> {
    try {
      const { negotiationId, senderId, messageType, content, attachments, termsChanges } = params;

      const { data: message, error } = await supabase
        .from('negotiation_messages')
        .insert({
          negotiation_id: negotiationId,
          sender_id: senderId,
          message_type: messageType,
          content,
          attachments: attachments || [],
          terms_changes: termsChanges,
          metadata: {}
        })
        .select()
        .single();

      if (error) throw error;

      // Update negotiation status if needed
      if (messageType === 'counter_proposal') {
        await supabase
          .from('negotiations')
          .update({ 
            status: 'in_progress',
            updated_at: new Date().toISOString()
          })
          .eq('id', negotiationId);
      }

      return { success: true, message };
    } catch (error) {
      console.error('Error adding negotiation message:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Approve negotiation by role
   */
  static async approveNegotiation(params: {
    negotiationId: string;
    approverId: string;
    approverRole: 'client' | 'designer' | 'manager';
    notes?: string;
  }): Promise<{ success: boolean; negotiation?: Negotiation; error?: string }> {
    try {
      const { negotiationId, approverId, approverRole, notes } = params;

      // Get current negotiation
      const { data: negotiation, error: fetchError } = await supabase
        .from('negotiations')
        .select('*')
        .eq('id', negotiationId)
        .single();

      if (fetchError || !negotiation) {
        throw new Error('Negotiation not found');
      }

      // Update approval based on role
      const updateData: any = { updated_at: new Date().toISOString() };
      
      switch (approverRole) {
        case 'client':
          updateData.client_approved_at = new Date().toISOString();
          break;
        case 'designer':
          updateData.designer_approved_at = new Date().toISOString();
          break;
        case 'manager':
          updateData.manager_approved_at = new Date().toISOString();
          break;
      }

      const { data: updatedNegotiation, error: updateError } = await supabase
        .from('negotiations')
        .update(updateData)
        .eq('id', negotiationId)
        .select()
        .single();

      if (updateError) throw updateError;

      // Check if all required approvals are complete
      const allApproved = this.checkAllApprovalsComplete(updatedNegotiation);
      
      if (allApproved) {
        // Mark negotiation as accepted
        await supabase
          .from('negotiations')
          .update({ status: 'accepted' })
          .eq('id', negotiationId);

        // Apply the negotiated terms
        await this.applyNegotiatedTerms(updatedNegotiation);
      }

      // Add approval message
      await this.addNegotiationMessage({
        negotiationId,
        senderId: approverId,
        messageType: 'approval',
        content: notes || `Approved by ${approverRole}`
      });

      // Log activity
      await this.logNegotiationActivity({
        negotiation_id: negotiationId,
        project_id: negotiation.project_id,
        activity_type: `${approverRole}_approval`,
        description: `Negotiation approved by ${approverRole}`,
        performed_by: approverId,
        metadata: { notes }
      });

      return { success: true, negotiation: updatedNegotiation };
    } catch (error) {
      console.error('Error approving negotiation:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Reject negotiation
   */
  static async rejectNegotiation(params: {
    negotiationId: string;
    rejectedBy: string;
    reason: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const { negotiationId, rejectedBy, reason } = params;

      // Update negotiation status
      const { error } = await supabase
        .from('negotiations')
        .update({
          status: 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', negotiationId);

      if (error) throw error;

      // Add rejection message
      await this.addNegotiationMessage({
        negotiationId,
        senderId: rejectedBy,
        messageType: 'rejection',
        content: reason
      });

      return { success: true };
    } catch (error) {
      console.error('Error rejecting negotiation:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get approval requirements based on negotiation type
   */
  static getApprovalRequirements(negotiationType: string, proposedTerms: any): {
    client: boolean;
    designer: boolean;
    manager: boolean;
  } {
    switch (negotiationType) {
      case 'scope_change':
        return { client: true, designer: true, manager: true };
      case 'timeline_extension':
        return { client: true, designer: false, manager: true };
      case 'budget_adjustment':
        return { client: true, designer: false, manager: true };
      case 'milestone_modification':
        return { client: true, designer: true, manager: true };
      case 'quality_revision':
        return { client: false, designer: true, manager: true };
      default:
        return { client: true, designer: true, manager: true };
    }
  }

  /**
   * Check if all required approvals are complete
   */
  static checkAllApprovalsComplete(negotiation: any): boolean {
    const clientApproved = !negotiation.client_approval_required || negotiation.client_approved_at;
    const designerApproved = !negotiation.designer_approval_required || negotiation.designer_approved_at;
    const managerApproved = !negotiation.manager_approval_required || negotiation.manager_approved_at;

    return clientApproved && designerApproved && managerApproved;
  }

  /**
   * Apply negotiated terms to project
   */
  static async applyNegotiatedTerms(negotiation: any): Promise<void> {
    try {
      const { project_id, negotiation_type, proposed_terms } = negotiation;

      switch (negotiation_type) {
        case 'budget_adjustment':
          if (proposed_terms.new_budget) {
            await supabase
              .from('projects')
              .update({ budget: proposed_terms.new_budget })
              .eq('id', project_id);
          }
          break;

        case 'timeline_extension':
          if (proposed_terms.new_deadline) {
            await supabase
              .from('projects')
              .update({ deadline: proposed_terms.new_deadline })
              .eq('id', project_id);
          }
          break;

        case 'milestone_modification':
          if (proposed_terms.milestone_changes) {
            // Apply milestone changes
            for (const change of proposed_terms.milestone_changes) {
              await supabase
                .from('project_milestones')
                .update(change.updates)
                .eq('id', change.milestone_id);
            }
          }
          break;
      }
    } catch (error) {
      console.error('Error applying negotiated terms:', error);
    }
  }

  /**
   * Send negotiation notifications
   */
  static async sendNegotiationNotifications(negotiation: any, eventType: string): Promise<void> {
    try {
      const notifications = [];

      // Get project participants
      const { data: project } = await supabase
        .from('projects')
        .select('client_id, designer_id')
        .eq('id', negotiation.project_id)
        .single();

      if (!project) return;

      // Notify relevant parties based on event type
      if (eventType === 'created') {
        // Notify all parties except initiator
        const recipients = [project.client_id, project.designer_id, negotiation.manager_id]
          .filter(id => id && id !== negotiation.initiated_by);

        recipients.forEach(recipientId => {
          notifications.push({
            recipient_id: recipientId,
            notification_type: 'negotiation_created',
            title: 'New Negotiation Started',
            message: `A new negotiation has been started: ${negotiation.title}`,
            priority: negotiation.priority === 'urgent' ? 'urgent' : 'high',
            metadata: {
              negotiation_id: negotiation.id,
              project_id: negotiation.project_id,
              negotiation_type: negotiation.negotiation_type
            }
          });
        });
      }

      if (notifications.length > 0) {
        await supabase
          .from('workflow_notifications')
          .insert(notifications);
      }
    } catch (error) {
      console.error('Error sending negotiation notifications:', error);
    }
  }

  /**
   * Log negotiation activity
   */
  static async logNegotiationActivity(params: {
    negotiation_id: string;
    project_id: string;
    activity_type: string;
    description: string;
    performed_by: string;
    metadata?: any;
  }): Promise<void> {
    try {
      await supabase
        .from('negotiation_activities')
        .insert({
          negotiation_id: params.negotiation_id,
          project_id: params.project_id,
          activity_type: params.activity_type,
          description: params.description,
          performed_by: params.performed_by,
          metadata: params.metadata || {}
        });
    } catch (error) {
      console.error('Error logging negotiation activity:', error);
    }
  }
}

// Export convenience functions
export const createNegotiation = NegotiationManager.createNegotiation;
export const approveNegotiation = NegotiationManager.approveNegotiation;
export const rejectNegotiation = NegotiationManager.rejectNegotiation;
