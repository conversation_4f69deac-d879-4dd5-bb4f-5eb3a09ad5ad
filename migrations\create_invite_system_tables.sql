-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create invitations table
CREATE TABLE IF NOT EXISTS invitations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invite_code TEXT NOT NULL UNIQUE,
  created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('client', 'designer')),
  email TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  accepted_at TIMESTAMP WITH TIME ZONE,
  accepted_by UUID REFERENCES profiles(id) ON DELETE SET NULL
);

-- Create connections table to track designer-client relationships
CREATE TABLE IF NOT EXISTS connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  invitation_id UUID REFERENCES invitations(id) ON DELETE SET NULL,
  UNIQUE(designer_id, client_id)
);

-- Add invite-related fields to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS invite_code TEXT UNIQUE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS invited_by UUID REFERENCES profiles(id);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS availability BOOLEAN DEFAULT TRUE;

-- Add additional fields for milestone workflow
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS feedback TEXT;
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS revision_count INTEGER DEFAULT 0;
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS deliverable_url TEXT;

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_invitations_invite_code ON invitations(invite_code);
CREATE INDEX IF NOT EXISTS idx_invitations_created_by ON invitations(created_by);
CREATE INDEX IF NOT EXISTS idx_invitations_status ON invitations(status);
CREATE INDEX IF NOT EXISTS idx_connections_designer_id ON connections(designer_id);
CREATE INDEX IF NOT EXISTS idx_connections_client_id ON connections(client_id);
CREATE INDEX IF NOT EXISTS idx_profiles_invite_code ON profiles(invite_code);
CREATE INDEX IF NOT EXISTS idx_profiles_invited_by ON profiles(invited_by);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at fields
CREATE TABLE IF NOT EXISTS temp_check (name TEXT);

-- Only create trigger if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_invitations_updated_at') THEN
        ALTER TABLE invitations ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        CREATE TRIGGER update_invitations_updated_at
        BEFORE UPDATE ON invitations
        FOR EACH ROW EXECUTE FUNCTION update_modified_column();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_connections_updated_at') THEN
        ALTER TABLE connections ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        CREATE TRIGGER update_connections_updated_at
        BEFORE UPDATE ON connections
        FOR EACH ROW EXECUTE FUNCTION update_modified_column();
    END IF;
END
$$;

-- Row-Level Security (RLS) Policies for invitations

-- Enable RLS on invitations table
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own invitations
CREATE POLICY "Users can read their own invitations" ON invitations
  FOR SELECT USING (created_by = auth.uid() OR email = auth.jwt() ->> 'email');

-- Allow users to create invitations
CREATE POLICY "Users can create invitations" ON invitations
  FOR INSERT WITH CHECK (created_by = auth.uid());

-- Allow users to update their own invitations
CREATE POLICY "Users can update their own invitations" ON invitations
  FOR UPDATE USING (created_by = auth.uid());

-- Allow admins to manage all invitations
CREATE POLICY "Admins can manage all invitations" ON invitations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Row-Level Security (RLS) Policies for connections

-- Enable RLS on connections table
ALTER TABLE connections ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own connections
CREATE POLICY "Users can read their own connections" ON connections
  FOR SELECT USING (designer_id = auth.uid() OR client_id = auth.uid());

-- Allow designers to create connections
CREATE POLICY "Designers can create connections" ON connections
  FOR INSERT WITH CHECK (
    designer_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'designer'
    )
  );

-- Allow admins to create connections
CREATE POLICY "Admins can create connections" ON connections
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Allow users to update their own connections
CREATE POLICY "Users can update their own connections" ON connections
  FOR UPDATE USING (designer_id = auth.uid() OR client_id = auth.uid());

-- Allow admins to manage all connections
CREATE POLICY "Admins can manage all connections" ON connections
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );
