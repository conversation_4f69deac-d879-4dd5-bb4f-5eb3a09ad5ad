"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/Button";
import {
  Target,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  FileText,
  Copy,
  Eye,
  Settings
} from "lucide-react";

interface Milestone {
  id: string;
  name: string;
  description: string;
  order_index: number;
  estimated_duration_days: number;
  payment_percentage: number;
  deliverables: string[];
  is_template: boolean;
  template_category: string;
  created_at: string;
  usage_count: number;
}

interface MilestoneTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  milestones: Milestone[];
  usage_count: number;
  created_at: string;
}

export default function MilestonesPage() {
  const { user } = useAuth();
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [templates, setTemplates] = useState<MilestoneTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'milestones' | 'templates'>('milestones');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingItem, setEditingItem] = useState<Milestone | MilestoneTemplate | null>(null);
  const [newMilestone, setNewMilestone] = useState({
    name: "",
    description: "",
    estimated_duration_days: 7,
    payment_percentage: 20,
    deliverables: [""],
    template_category: "residential"
  });

  useEffect(() => {
    fetchMilestones();
    fetchTemplates();
  }, []);

  const fetchMilestones = async () => {
    try {
      // Mock milestone data
      const mockMilestones: Milestone[] = [
        {
          id: "1",
          name: "Initial Consultation & Site Analysis",
          description: "Meet with client, analyze site conditions, and establish project requirements",
          order_index: 1,
          estimated_duration_days: 7,
          payment_percentage: 15,
          deliverables: ["Site analysis report", "Initial concept sketches", "Project timeline"],
          is_template: true,
          template_category: "residential",
          created_at: "2024-01-01",
          usage_count: 25
        },
        {
          id: "2",
          name: "Schematic Design",
          description: "Develop preliminary design concepts and floor plans",
          order_index: 2,
          estimated_duration_days: 14,
          payment_percentage: 25,
          deliverables: ["Floor plans", "Elevation drawings", "3D renderings"],
          is_template: true,
          template_category: "residential",
          created_at: "2024-01-01",
          usage_count: 23
        },
        {
          id: "3",
          name: "Design Development",
          description: "Refine design details and finalize material selections",
          order_index: 3,
          estimated_duration_days: 21,
          payment_percentage: 30,
          deliverables: ["Detailed drawings", "Material specifications", "Cost estimates"],
          is_template: true,
          template_category: "residential",
          created_at: "2024-01-01",
          usage_count: 20
        },
        {
          id: "4",
          name: "Construction Documents",
          description: "Prepare detailed construction drawings and specifications",
          order_index: 4,
          estimated_duration_days: 28,
          payment_percentage: 25,
          deliverables: ["Construction drawings", "Technical specifications", "Permit applications"],
          is_template: true,
          template_category: "residential",
          created_at: "2024-01-01",
          usage_count: 18
        },
        {
          id: "5",
          name: "Project Completion & Handover",
          description: "Final inspections and project documentation",
          order_index: 5,
          estimated_duration_days: 7,
          payment_percentage: 5,
          deliverables: ["As-built drawings", "Project documentation", "Maintenance guidelines"],
          is_template: true,
          template_category: "residential",
          created_at: "2024-01-01",
          usage_count: 15
        }
      ];

      setMilestones(mockMilestones);
    } catch (error) {
      console.error('Error fetching milestones:', error);
      setError('Failed to load milestones');
    } finally {
      setLoading(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      // Mock template data
      const mockTemplates: MilestoneTemplate[] = [
        {
          id: "template1",
          name: "Residential Design Template",
          description: "Standard milestone template for residential projects",
          category: "residential",
          milestones: milestones.filter(m => m.template_category === "residential"),
          usage_count: 15,
          created_at: "2024-01-01"
        },
        {
          id: "template2",
          name: "Commercial Design Template",
          description: "Comprehensive template for commercial projects",
          category: "commercial",
          milestones: [],
          usage_count: 8,
          created_at: "2024-01-01"
        },
        {
          id: "template3",
          name: "Interior Design Template",
          description: "Specialized template for interior design projects",
          category: "interior",
          milestones: [],
          usage_count: 12,
          created_at: "2024-01-01"
        }
      ];

      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const handleCreateMilestone = async () => {
    if (!newMilestone.name.trim()) {
      setError('Milestone name is required');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const milestone: Milestone = {
        id: Date.now().toString(),
        name: newMilestone.name,
        description: newMilestone.description,
        order_index: milestones.length + 1,
        estimated_duration_days: newMilestone.estimated_duration_days,
        payment_percentage: newMilestone.payment_percentage,
        deliverables: newMilestone.deliverables.filter(d => d.trim()),
        is_template: true,
        template_category: newMilestone.template_category,
        created_at: new Date().toISOString(),
        usage_count: 0
      };

      setMilestones(prev => [...prev, milestone]);
      setSuccess('Milestone created successfully');
      setShowCreateModal(false);
      setNewMilestone({
        name: "",
        description: "",
        estimated_duration_days: 7,
        payment_percentage: 20,
        deliverables: [""],
        template_category: "residential"
      });
    } catch (error) {
      console.error('Error creating milestone:', error);
      setError('Failed to create milestone');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteMilestone = async (milestoneId: string) => {
    if (confirm('Are you sure you want to delete this milestone?')) {
      setMilestones(prev => prev.filter(m => m.id !== milestoneId));
      setSuccess('Milestone deleted successfully');
    }
  };

  const addDeliverable = () => {
    setNewMilestone(prev => ({
      ...prev,
      deliverables: [...prev.deliverables, ""]
    }));
  };

  const updateDeliverable = (index: number, value: string) => {
    setNewMilestone(prev => ({
      ...prev,
      deliverables: prev.deliverables.map((d, i) => i === index ? value : d)
    }));
  };

  const removeDeliverable = (index: number) => {
    setNewMilestone(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter((_, i) => i !== index)
    }));
  };

  const duplicateMilestone = (milestone: Milestone) => {
    const duplicated: Milestone = {
      ...milestone,
      id: Date.now().toString(),
      name: `${milestone.name} (Copy)`,
      usage_count: 0,
      created_at: new Date().toISOString()
    };
    setMilestones(prev => [...prev, duplicated]);
    setSuccess('Milestone duplicated successfully');
  };

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Milestone Management</h1>
          <p className="text-gray-600">Create and manage project milestone templates</p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Milestone
        </Button>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('milestones')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'milestones'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Individual Milestones
            </button>
            <button
              onClick={() => setActiveTab('templates')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'templates'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Milestone Templates
            </button>
          </nav>
        </div>
      </div>

      {/* Milestones Tab */}
      {activeTab === 'milestones' && (
        <div className="space-y-4">
          {milestones.map((milestone, index) => (
            <div key={milestone.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <span className="bg-brown-100 text-brown-800 text-xs font-medium px-2 py-1 rounded-full mr-3">
                        Step {milestone.order_index}
                      </span>
                      <h3 className="text-lg font-semibold text-gray-900">{milestone.name}</h3>
                    </div>
                    <p className="text-gray-600 mb-3">{milestone.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>{milestone.estimated_duration_days} days</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <DollarSign className="h-4 w-4 mr-2" />
                        <span>{milestone.payment_percentage}% payment</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Target className="h-4 w-4 mr-2" />
                        <span>Used {milestone.usage_count} times</span>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Deliverables:</h4>
                      <div className="flex flex-wrap gap-2">
                        {milestone.deliverables.map((deliverable, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                          >
                            {deliverable}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => duplicateMilestone(milestone)}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Duplicate
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingItem(milestone)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteMilestone(milestone.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Templates Tab */}
      {activeTab === 'templates' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div key={template.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{template.name}</h3>
                    <p className="text-gray-600 text-sm mb-3">{template.description}</p>
                    <span className="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      {template.category}
                    </span>
                  </div>
                </div>
                
                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Target className="h-4 w-4 mr-2" />
                    <span>{template.milestones?.length || 0} milestones</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Eye className="h-4 w-4 mr-2" />
                    <span>Used {template.usage_count} times</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Milestone Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Create New Milestone</h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label htmlFor="milestoneName" className="block text-sm font-medium text-gray-700 mb-1">
                    Milestone Name
                  </label>
                  <input
                    type="text"
                    id="milestoneName"
                    value={newMilestone.name}
                    onChange={(e) => setNewMilestone({ ...newMilestone, name: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    placeholder="Enter milestone name"
                  />
                </div>
                
                <div>
                  <label htmlFor="milestoneDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="milestoneDescription"
                    value={newMilestone.description}
                    onChange={(e) => setNewMilestone({ ...newMilestone, description: e.target.value })}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    placeholder="Describe this milestone"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
                      Duration (days)
                    </label>
                    <input
                      type="number"
                      id="duration"
                      value={newMilestone.estimated_duration_days}
                      onChange={(e) => setNewMilestone({ ...newMilestone, estimated_duration_days: parseInt(e.target.value) })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      min="1"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="payment" className="block text-sm font-medium text-gray-700 mb-1">
                      Payment %
                    </label>
                    <input
                      type="number"
                      id="payment"
                      value={newMilestone.payment_percentage}
                      onChange={(e) => setNewMilestone({ ...newMilestone, payment_percentage: parseInt(e.target.value) })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      min="0"
                      max="100"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      id="category"
                      value={newMilestone.template_category}
                      onChange={(e) => setNewMilestone({ ...newMilestone, template_category: e.target.value })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    >
                      <option value="residential">Residential</option>
                      <option value="commercial">Commercial</option>
                      <option value="interior">Interior</option>
                      <option value="landscape">Landscape</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Deliverables
                  </label>
                  <div className="space-y-2">
                    {newMilestone.deliverables.map((deliverable, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={deliverable}
                          onChange={(e) => updateDeliverable(index, e.target.value)}
                          className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                          placeholder="Enter deliverable"
                        />
                        {newMilestone.deliverables.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeDeliverable(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addDeliverable}
                      className="flex items-center"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Deliverable
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowCreateModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateMilestone}
                disabled={saving}
                className="flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Creating...' : 'Create Milestone'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
