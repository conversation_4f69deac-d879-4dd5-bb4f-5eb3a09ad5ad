"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RotateCcw,
  Eye,
  MessageSquare,
  User,
  Calendar,
  Filter,
  Search,
  Loader2,
  AlertCircle as AlertCircleIcon
} from "lucide-react";

interface Submission {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'needs_revision' | 'rejected';
  revision_requested: boolean;
  feedback?: string;
  created_at: string;
  updated_at: string;
  designer: {
    full_name: string;
    email: string;
    avatar_url?: string;
  };
}

interface Project {
  id: string;
  title: string;
  status: string;
  client_id: string;
  designer: {
    full_name: string;
    email: string;
  } | null;
}

export default function ProjectSubmissionsPage() {
  const { id: projectId } = useParams();
  const router = useRouter();
  const { user } = useOptimizedAuth();
  
  const [project, setProject] = useState<Project | null>(null);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (projectId && user) {
      fetchProjectAndSubmissions();
    }
  }, [projectId, user]);

  useEffect(() => {
    filterSubmissions();
  }, [submissions, statusFilter, searchTerm]);

  const fetchProjectAndSubmissions = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          status,
          client_id,
          designer:profiles!designer_id(full_name, email)
        `)
        .eq('id', projectId)
        .eq('client_id', user?.id)
        .single();

      if (projectError) throw projectError;
      if (!projectData) {
        setError('Project not found or you do not have access to it');
        return;
      }

      setProject(projectData);

      // Fetch submissions for this project
      const { data: submissionsData, error: submissionsError } = await supabase
        .from('submissions')
        .select(`
          *,
          designer:profiles!designer_id(full_name, email, avatar_url)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (submissionsError) throw submissionsError;

      setSubmissions(submissionsData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project submissions');
    } finally {
      setLoading(false);
    }
  };

  const filterSubmissions = () => {
    let filtered = submissions;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(submission => submission.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(submission =>
        submission.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        submission.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredSubmissions(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'needs_revision':
        return 'bg-orange-100 text-orange-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'needs_revision':
        return <RotateCcw className="h-4 w-4 text-orange-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Loader2 className="animate-spin h-12 w-12 text-primary mx-auto mb-4" />
          <p className="text-gray-500">Loading project submissions...</p>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircleIcon className="h-5 w-5 mr-2 mt-0.5" />
          <div>
            <h3 className="font-medium">Error Loading Project</h3>
            <p className="mt-1">{error || 'Project not found'}</p>
          </div>
        </div>
        <div className="mt-6">
          <Link href="/client/projects">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link href={`/client/projects/${projectId}`} className="mr-4">
            <Button variant="ghost" className="p-0 h-auto">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Design Submissions</h1>
            <p className="text-gray-500 mt-1">
              {project.title} • {project.designer?.full_name || 'No designer assigned'}
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search Submissions
            </label>
            <div className="relative">
              <input
                type="text"
                id="search"
                placeholder="Search by title or description"
                className="w-full px-4 py-2 border rounded-md pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>
          
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status Filter
            </label>
            <select
              id="status"
              className="w-full px-4 py-2 border rounded-md"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Submissions</option>
              <option value="pending">Pending Review</option>
              <option value="approved">Approved</option>
              <option value="needs_revision">Needs Revision</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <Button 
              variant="outline" 
              onClick={fetchProjectAndSubmissions}
              className="flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
        
        <div className="mt-4">
          <p className="text-sm text-gray-500">
            Showing {filteredSubmissions.length} of {submissions.length} submissions
          </p>
        </div>
      </div>

      {/* Submissions List */}
      {filteredSubmissions.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-medium mb-2">
            {submissions.length === 0 ? 'No Submissions Yet' : 'No Matching Submissions'}
          </h2>
          <p className="text-gray-500 mb-6">
            {submissions.length === 0 
              ? "Your designer hasn't submitted any work for this project yet."
              : "No submissions match your current filter criteria."
            }
          </p>
          {statusFilter !== 'all' && (
            <Button onClick={() => setStatusFilter('all')}>
              View All Submissions
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {filteredSubmissions.map((submission) => (
            <div key={submission.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Submission Header */}
              <div className="p-6 border-b">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex items-center mb-4 md:mb-0">
                    <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                      {submission.designer.avatar_url ? (
                        <img
                          src={submission.designer.avatar_url}
                          alt={submission.designer.full_name}
                          className="h-10 w-10 rounded-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">{submission.title}</h3>
                      <p className="text-sm text-gray-500">
                        by {submission.designer.full_name} • {formatDate(submission.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(submission.status)}
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}>
                      {submission.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Submission Content */}
              <div className="p-6">
                {/* Description */}
                <div className="mb-4">
                  <p className="text-gray-700 text-sm leading-relaxed">{submission.description}</p>
                </div>

                {/* Revision Notice */}
                {submission.revision_requested && (
                  <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <div className="flex items-center">
                      <RotateCcw className="h-4 w-4 text-orange-600 mr-2" />
                      <span className="text-sm font-medium text-orange-800">Revision Requested</span>
                    </div>
                    {submission.feedback && (
                      <p className="text-sm text-orange-700 mt-2">{submission.feedback}</p>
                    )}
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-between items-center">
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>Last updated: {formatDate(submission.updated_at)}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Link href={`/client/projects/${projectId}/submissions/${submission.id}`}>
                      <Button variant="outline" size="sm" className="flex items-center">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </Link>
                    
                    {submission.status === 'pending' && (
                      <Link href={`/client/projects/${projectId}/submissions/${submission.id}#feedback`}>
                        <Button size="sm" className="flex items-center">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Provide Feedback
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Quick Actions Sidebar */}
      <div className="fixed bottom-6 right-6">
        <div className="bg-white rounded-lg shadow-lg p-4 space-y-3">
          <Link href={`/client/projects/${projectId}`}>
            <Button variant="outline" size="sm" className="w-full">
              Back to Project
            </Button>
          </Link>
          {project.designer && (
            <Link href={`/client/messages?designer=${project.designer.email}`}>
              <Button variant="outline" size="sm" className="w-full">
                <MessageSquare className="h-4 w-4 mr-2" />
                Message Designer
              </Button>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
