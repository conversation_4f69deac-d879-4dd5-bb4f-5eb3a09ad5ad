import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface DesignerApplicationData {
  email: string;
  full_name: string;
  phone?: string;
  location?: string;
  specialization: string;
  experience: string;
  portfolio_url?: string;
  bio: string;
  resume_url?: string;
  portfolio_files?: string[];
  certificates?: string[];
}

/**
 * API route for submitting designer applications
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Designer application submission API called');
    
    const applicationData: DesignerApplicationData = await request.json();
    
    // Validate required fields
    const requiredFields = ['email', 'full_name', 'specialization', 'experience', 'bio'];
    for (const field of requiredFields) {
      if (!applicationData[field as keyof DesignerApplicationData]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(applicationData.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const { data: existingApplication, error: checkError } = await supabase
      .from('designer_applications')
      .select('id, application_status')
      .eq('email', applicationData.email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing application:', checkError);
      return NextResponse.json(
        { error: 'Failed to check existing applications' },
        { status: 500 }
      );
    }

    if (existingApplication) {
      if (existingApplication.application_status === 'pending') {
        return NextResponse.json(
          { error: 'An application with this email is already pending review' },
          { status: 409 }
        );
      } else if (existingApplication.application_status === 'approved') {
        return NextResponse.json(
          { error: 'An application with this email has already been approved' },
          { status: 409 }
        );
      }
      // If rejected, allow reapplication
    }

    // Insert the application
    const { data: application, error: insertError } = await supabase
      .from('designer_applications')
      .insert({
        email: applicationData.email,
        full_name: applicationData.full_name,
        phone: applicationData.phone,
        location: applicationData.location,
        specialization: applicationData.specialization,
        experience: applicationData.experience,
        portfolio_url: applicationData.portfolio_url,
        bio: applicationData.bio,
        resume_url: applicationData.resume_url,
        portfolio_files: applicationData.portfolio_files || [],
        certificates: applicationData.certificates || [],
        application_status: 'pending'
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting application:', insertError);
      return NextResponse.json(
        { error: 'Failed to submit application' },
        { status: 500 }
      );
    }

    // Send confirmation email to applicant
    try {
      await resend.emails.send({
        from: 'Seniors Architecture Firm <<EMAIL>>',
        to: [applicationData.email],
        subject: 'Application Received - Seniors Architecture Firm',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #8B4513;">Thank you for your application!</h2>
            
            <p>Dear ${applicationData.full_name},</p>
            
            <p>We have received your application to join Seniors Architecture Firm as a designer. Thank you for your interest in working with us!</p>
            
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #8B4513;">Application Details:</h3>
              <p><strong>Name:</strong> ${applicationData.full_name}</p>
              <p><strong>Email:</strong> ${applicationData.email}</p>
              <p><strong>Specialization:</strong> ${applicationData.specialization}</p>
              <p><strong>Experience:</strong> ${applicationData.experience}</p>
              <p><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</p>
            </div>
            
            <p>Our team will review your application and portfolio. We typically respond within 5-7 business days.</p>
            
            <p>If you have any questions, please don't hesitate to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            
            <p>Best regards,<br>
            The Seniors Architecture Firm Team</p>
            
            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
            <p style="font-size: 12px; color: #666;">
              This is an automated message. Please do not reply to this email.
            </p>
          </div>
        `
      });
      console.log('Confirmation email sent successfully');
    } catch (emailError) {
      console.error('Error sending confirmation email:', emailError);
      // Don't fail the application submission if email fails
    }

    // Send notification email to admin
    try {
      await resend.emails.send({
        from: 'Seniors Architecture Firm <<EMAIL>>',
        to: ['<EMAIL>'], // Replace with actual admin email
        subject: 'New Designer Application Received',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #8B4513;">New Designer Application</h2>
            
            <p>A new designer application has been submitted and is pending review.</p>
            
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #8B4513;">Applicant Details:</h3>
              <p><strong>Name:</strong> ${applicationData.full_name}</p>
              <p><strong>Email:</strong> ${applicationData.email}</p>
              <p><strong>Phone:</strong> ${applicationData.phone || 'Not provided'}</p>
              <p><strong>Location:</strong> ${applicationData.location || 'Not provided'}</p>
              <p><strong>Specialization:</strong> ${applicationData.specialization}</p>
              <p><strong>Experience:</strong> ${applicationData.experience}</p>
              <p><strong>Portfolio URL:</strong> ${applicationData.portfolio_url || 'Not provided'}</p>
              <p><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</p>
            </div>
            
            <div style="background-color: #e8f4f8; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h4 style="margin-top: 0; color: #8B4513;">Bio:</h4>
              <p style="margin-bottom: 0;">${applicationData.bio}</p>
            </div>
            
            <p>
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/designers/applications/${application.id}" 
                 style="background-color: #8B4513; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Review Application
              </a>
            </p>
            
            <p>Please review the application and make a decision within 5-7 business days.</p>
          </div>
        `
      });
      console.log('Admin notification email sent successfully');
    } catch (emailError) {
      console.error('Error sending admin notification email:', emailError);
      // Don't fail the application submission if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Application submitted successfully',
      applicationId: application.id
    }, { status: 201 });

  } catch (error) {
    console.error('Error in designer application API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * API route for getting designer applications (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    
    let query = supabase
      .from('designer_applications')
      .select('*')
      .order('applied_at', { ascending: false });

    if (status && status !== 'all') {
      query = query.eq('application_status', status);
    }

    const { data: applications, error } = await query;

    if (error) {
      console.error('Error fetching applications:', error);
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      applications
    }, { status: 200 });

  } catch (error) {
    console.error('Error in GET designer application API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
