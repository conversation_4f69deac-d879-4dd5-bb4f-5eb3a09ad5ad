-- Debug script to check client-designer relationships
-- Run this in Supabase SQL Editor to understand the data

-- 1. Check if connections table exists and has data
SELECT 'connections' as table_name, COUNT(*) as row_count 
FROM connections;

-- 2. Check if invitations table exists and has data  
SELECT 'invitations' as table_name, COUNT(*) as row_count 
FROM invitations;

-- 3. Check projects with client-designer relationships
SELECT 'projects' as table_name, COUNT(*) as row_count 
FROM projects 
WHERE client_id IS NOT NULL AND designer_id IS NOT NULL;

-- 4. Check specific designer's data (replace with actual designer ID)
-- First, let's see all designers
SELECT id, full_name, email, role 
FROM profiles 
WHERE role = 'designer' 
LIMIT 5;

-- 5. Check connections for a specific designer (you'll need to replace the ID)
-- SELECT * FROM connections WHERE designer_id = 'YOUR_DESIGNER_ID';

-- 6. Check invitations for a specific designer
-- SELECT * FROM invitations WHERE invited_by = 'YOUR_DESIGNER_ID';

-- 7. Check projects for a specific designer
-- SELECT * FROM projects WHERE designer_id = 'YOUR_DESIGNER_ID';

-- 8. Check if there are any clients at all
SELECT id, full_name, email, role 
FROM profiles 
WHERE role = 'client' 
LIMIT 5;

-- 9. Check for any project-client-designer relationships
SELECT 
  p.id as project_id,
  p.title,
  p.status,
  c.full_name as client_name,
  d.full_name as designer_name
FROM projects p
LEFT JOIN profiles c ON p.client_id = c.id
LEFT JOIN profiles d ON p.designer_id = d.id
WHERE p.designer_id IS NOT NULL AND p.client_id IS NOT NULL
LIMIT 10;

-- 10. Check for missing connections that should exist based on projects
SELECT DISTINCT
  p.client_id,
  p.designer_id,
  c.full_name as client_name,
  d.full_name as designer_name,
  'Missing connection' as issue
FROM projects p
JOIN profiles c ON p.client_id = c.id
JOIN profiles d ON p.designer_id = d.id
WHERE NOT EXISTS (
  SELECT 1 FROM connections conn 
  WHERE conn.client_id = p.client_id 
  AND conn.designer_id = p.designer_id
);
