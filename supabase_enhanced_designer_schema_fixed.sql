-- =====================================================
-- COMPREHENSIVE ENHANCED DESIGNER FEATURES SCHEMA - FIXED
-- Run this in Supabase Dashboard SQL Editor
-- =====================================================

-- First, let's create the portfolio_projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS portfolio_projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT,
  featured BOOLEAN DEFAULT FALSE,
  completion_date DATE,
  client_name TEXT,
  project_value DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 1. PROJECT BRIEFS TABLE
-- Stores client project requirements without pricing
CREATE TABLE IF NOT EXISTS project_briefs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  requirements TEXT,
  preferred_style TEXT,
  budget_range TEXT CHECK (budget_range IN ('under_5k', '5k_10k', '10k_25k', '25k_plus', 'flexible')),
  timeline_preference TEXT,
  location TEXT,
  project_type TEXT,
  urgency TEXT DEFAULT 'medium' CHECK (urgency IN ('low', 'medium', 'high', 'urgent')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'assigned', 'proposal_received', 'accepted', 'rejected')),
  assigned_designer_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  assigned_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  assigned_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. DESIGNER AVAILABILITY TABLE
-- Manages designer availability status and preferences
CREATE TABLE IF NOT EXISTS designer_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID UNIQUE NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'available' CHECK (status IN ('available', 'busy', 'offline')),
  custom_message TEXT,
  auto_accept_briefs BOOLEAN DEFAULT FALSE,
  max_concurrent_projects INTEGER DEFAULT 5,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. PROJECT REVIEWS TABLE
-- Stores client reviews and ratings for completed projects
CREATE TABLE IF NOT EXISTS project_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
  quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
  timeliness_rating INTEGER CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
  would_recommend BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(project_id, client_id)
);

-- 4. ADMIN MESSAGES TABLE
-- System messages and notifications from admin to users
CREATE TABLE IF NOT EXISTS admin_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  recipient_role TEXT CHECK (recipient_role IN ('designer', 'client', 'all')),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'info' CHECK (message_type IN ('info', 'warning', 'success', 'urgent', 'announcement')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  read_at TIMESTAMP WITH TIME ZONE,
  action_required BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. DESIGNER SPECIALIZATIONS TABLE
-- Stores designer skills and specializations
CREATE TABLE IF NOT EXISTS designer_specializations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  specialization TEXT NOT NULL,
  skill_level TEXT DEFAULT 'intermediate' CHECK (skill_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
  years_experience INTEGER DEFAULT 0,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. PORTFOLIO IMAGES TABLE
-- Enhanced portfolio project images
CREATE TABLE IF NOT EXISTS portfolio_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  portfolio_project_id UUID NOT NULL REFERENCES portfolio_projects(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  image_name TEXT,
  is_cover BOOLEAN DEFAULT FALSE,
  order_index INTEGER DEFAULT 0,
  alt_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. DESIGNER QUICK ACTIONS TABLE
-- Customizable quick actions for designer dashboard
CREATE TABLE IF NOT EXISTS designer_quick_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL CHECK (action_type IN ('create_proposal', 'message_client', 'update_portfolio', 'set_availability', 'view_briefs')),
  action_label TEXT NOT NULL,
  action_url TEXT NOT NULL,
  icon_name TEXT,
  is_enabled BOOLEAN DEFAULT TRUE,
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. PROJECT PROPOSALS TABLE (Enhanced)
-- Enhanced proposals linked to briefs
CREATE TABLE IF NOT EXISTS project_proposals_enhanced (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  brief_id UUID REFERENCES project_briefs(id) ON DELETE CASCADE,
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  total_budget DECIMAL(10,2),
  timeline_weeks INTEGER,
  milestones JSONB,
  terms_and_conditions TEXT,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'under_review', 'accepted', 'rejected', 'withdrawn')),
  submitted_at TIMESTAMP WITH TIME ZONE,
  reviewed_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Portfolio Projects Indexes
CREATE INDEX IF NOT EXISTS idx_portfolio_projects_designer_id ON portfolio_projects(designer_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_projects_featured ON portfolio_projects(featured);
CREATE INDEX IF NOT EXISTS idx_portfolio_projects_category ON portfolio_projects(category);

-- Project Briefs Indexes
CREATE INDEX IF NOT EXISTS idx_project_briefs_client_id ON project_briefs(client_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_assigned_designer_id ON project_briefs(assigned_designer_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_status ON project_briefs(status);
CREATE INDEX IF NOT EXISTS idx_project_briefs_created_at ON project_briefs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_project_briefs_urgency ON project_briefs(urgency);

-- Designer Availability Indexes
CREATE INDEX IF NOT EXISTS idx_designer_availability_designer_id ON designer_availability(designer_id);
CREATE INDEX IF NOT EXISTS idx_designer_availability_status ON designer_availability(status);

-- Project Reviews Indexes
CREATE INDEX IF NOT EXISTS idx_project_reviews_designer_id ON project_reviews(designer_id);
CREATE INDEX IF NOT EXISTS idx_project_reviews_project_id ON project_reviews(project_id);
CREATE INDEX IF NOT EXISTS idx_project_reviews_rating ON project_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_project_reviews_created_at ON project_reviews(created_at DESC);

-- Admin Messages Indexes
CREATE INDEX IF NOT EXISTS idx_admin_messages_recipient_id ON admin_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_admin_messages_recipient_role ON admin_messages(recipient_role);
CREATE INDEX IF NOT EXISTS idx_admin_messages_read_at ON admin_messages(read_at);
CREATE INDEX IF NOT EXISTS idx_admin_messages_message_type ON admin_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_admin_messages_priority ON admin_messages(priority);
CREATE INDEX IF NOT EXISTS idx_admin_messages_created_at ON admin_messages(created_at DESC);

-- Portfolio Images Indexes
CREATE INDEX IF NOT EXISTS idx_portfolio_images_portfolio_project_id ON portfolio_images(portfolio_project_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_images_is_cover ON portfolio_images(is_cover);

-- Designer Specializations Indexes
CREATE INDEX IF NOT EXISTS idx_designer_specializations_designer_id ON designer_specializations(designer_id);
CREATE INDEX IF NOT EXISTS idx_designer_specializations_specialization ON designer_specializations(specialization);

-- Quick Actions Indexes
CREATE INDEX IF NOT EXISTS idx_designer_quick_actions_designer_id ON designer_quick_actions(designer_id);
CREATE INDEX IF NOT EXISTS idx_designer_quick_actions_enabled ON designer_quick_actions(is_enabled);

-- Project Proposals Enhanced Indexes
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_brief_id ON project_proposals_enhanced(brief_id);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_designer_id ON project_proposals_enhanced(designer_id);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_status ON project_proposals_enhanced(status);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE portfolio_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_briefs ENABLE ROW LEVEL SECURITY;
ALTER TABLE designer_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE designer_specializations ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE designer_quick_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_proposals_enhanced ENABLE ROW LEVEL SECURITY;

-- Portfolio Projects Policies
CREATE POLICY "Designers can manage their portfolio" ON portfolio_projects
  FOR ALL USING (auth.uid() = designer_id);

CREATE POLICY "Public can view portfolio projects" ON portfolio_projects
  FOR SELECT USING (true);

-- Project Briefs Policies
CREATE POLICY "Users can view their own briefs" ON project_briefs
  FOR SELECT USING (auth.uid() = client_id OR auth.uid() = assigned_designer_id);

CREATE POLICY "Clients can create briefs" ON project_briefs
  FOR INSERT WITH CHECK (auth.uid() = client_id);

CREATE POLICY "Clients can update their own briefs" ON project_briefs
  FOR UPDATE USING (auth.uid() = client_id);

-- Designer Availability Policies
CREATE POLICY "Designers can manage their availability" ON designer_availability
  FOR ALL USING (auth.uid() = designer_id);

CREATE POLICY "Public can view designer availability" ON designer_availability
  FOR SELECT USING (true);

-- Project Reviews Policies
CREATE POLICY "Users can view reviews for their projects" ON project_reviews
  FOR SELECT USING (auth.uid() = client_id OR auth.uid() = designer_id);

CREATE POLICY "Clients can create reviews" ON project_reviews
  FOR INSERT WITH CHECK (auth.uid() = client_id);

-- Admin Messages Policies
CREATE POLICY "Users can view their messages" ON admin_messages
  FOR SELECT USING (
    auth.uid() = recipient_id OR
    recipient_role = 'all' OR
    (recipient_role = 'designer' AND EXISTS (
      SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'designer'
    )) OR
    (recipient_role = 'client' AND EXISTS (
      SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'client'
    ))
  );

CREATE POLICY "Users can update read status" ON admin_messages
  FOR UPDATE USING (auth.uid() = recipient_id)
  WITH CHECK (auth.uid() = recipient_id);

-- Designer Specializations Policies
CREATE POLICY "Designers can manage their specializations" ON designer_specializations
  FOR ALL USING (auth.uid() = designer_id);

CREATE POLICY "Public can view specializations" ON designer_specializations
  FOR SELECT USING (true);

-- Portfolio Images Policies
CREATE POLICY "Designers can manage portfolio images" ON portfolio_images
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE id = portfolio_project_id AND designer_id = auth.uid()
    )
  );

CREATE POLICY "Public can view portfolio images" ON portfolio_images
  FOR SELECT USING (true);

-- Quick Actions Policies
CREATE POLICY "Designers can manage their quick actions" ON designer_quick_actions
  FOR ALL USING (auth.uid() = designer_id);

-- Project Proposals Enhanced Policies
CREATE POLICY "Designers can manage their proposals" ON project_proposals_enhanced
  FOR ALL USING (auth.uid() = designer_id);

CREATE POLICY "Clients can view proposals for their briefs" ON project_proposals_enhanced
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_briefs
      WHERE id = brief_id AND client_id = auth.uid()
    )
  );

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default availability for existing designers
INSERT INTO designer_availability (designer_id, status)
SELECT id, 'available'
FROM profiles
WHERE role = 'designer'
AND id NOT IN (SELECT designer_id FROM designer_availability WHERE designer_id IS NOT NULL)
ON CONFLICT (designer_id) DO NOTHING;

-- Insert default quick actions for existing designers
INSERT INTO designer_quick_actions (designer_id, action_type, action_label, action_url, icon_name, order_index)
SELECT
  p.id,
  action_type,
  action_label,
  action_url,
  icon_name,
  order_index
FROM profiles p
CROSS JOIN (
  VALUES
    ('view_briefs', 'View New Briefs', '/designer/briefs', 'Briefcase', 1),
    ('create_proposal', 'Create Proposal', '/designer/proposals/new', 'FileText', 2),
    ('message_client', 'Message Clients', '/designer/messages', 'MessageSquare', 3),
    ('update_portfolio', 'Update Portfolio', '/designer/portfolio', 'Image', 4),
    ('set_availability', 'Set Availability', '/designer/availability', 'Settings', 5)
) AS actions(action_type, action_label, action_url, icon_name, order_index)
WHERE p.role = 'designer'
ON CONFLICT DO NOTHING;

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_portfolio_projects_updated_at
  BEFORE UPDATE ON portfolio_projects
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_briefs_updated_at
  BEFORE UPDATE ON project_briefs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_designer_availability_updated_at
  BEFORE UPDATE ON designer_availability
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_reviews_updated_at
  BEFORE UPDATE ON project_reviews
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_proposals_enhanced_updated_at
  BEFORE UPDATE ON project_proposals_enhanced
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

-- This will show in the SQL editor results
SELECT 'Enhanced Designer Features Schema Successfully Created!' as status,
       'All tables, indexes, policies, and initial data have been set up.' as message;
