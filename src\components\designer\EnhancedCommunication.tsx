"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  MessageSquare,
  Send,
  Paperclip,
  Phone,
  Video,
  MoreHorizontal,
  Clock,
  CheckCircle2,
  User,
  ArrowRight
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface Message {
  id: string;
  content: string;
  sender_id: string;
  sender_name: string;
  sender_avatar: string | null;
  is_read: boolean;
  created_at: string;
  project_id: string;
  project_title: string;
  attachment_url?: string;
  attachment_name?: string;
}

interface Conversation {
  id: string;
  client_id: string;
  client_name: string;
  client_avatar: string | null;
  project_id: string;
  project_title: string;
  last_message: string;
  last_message_time: string;
  unread_count: number;
  is_online: boolean;
}

export function EnhancedCommunication() {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [recentMessages, setRecentMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalUnread, setTotalUnread] = useState(0);

  useEffect(() => {
    if (user) {
      fetchCommunicationData();
    }
  }, [user]);

  const fetchCommunicationData = async () => {
    try {
      // Fetch recent messages from project_messages
      const { data: messagesData, error: messagesError } = await supabase
        .from('project_messages')
        .select(`
          id,
          content,
          sender_id,
          is_read,
          created_at,
          project_id,
          attachment_url,
          attachment_name,
          projects(title, client_id),
          profiles!sender_id(full_name, avatar_url)
        `)
        .neq('sender_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (messagesError) throw messagesError;

      // Process messages
      const messages = (messagesData || []).map(msg => ({
        id: msg.id,
        content: msg.content,
        sender_id: msg.sender_id,
        sender_name: msg.profiles?.full_name || 'Unknown',
        sender_avatar: msg.profiles?.avatar_url || null,
        is_read: msg.is_read,
        created_at: msg.created_at,
        project_id: msg.project_id,
        project_title: msg.projects?.title || 'Unknown Project',
        attachment_url: msg.attachment_url,
        attachment_name: msg.attachment_name
      }));

      setRecentMessages(messages);

      // Group messages by client to create conversations
      const conversationMap = new Map<string, Conversation>();
      
      messages.forEach(msg => {
        if (msg.projects?.client_id) {
          const clientId = msg.projects.client_id;
          const existing = conversationMap.get(clientId);
          
          if (!existing || new Date(msg.created_at) > new Date(existing.last_message_time)) {
            conversationMap.set(clientId, {
              id: `conv-${clientId}`,
              client_id: clientId,
              client_name: msg.sender_name,
              client_avatar: msg.sender_avatar,
              project_id: msg.project_id,
              project_title: msg.project_title,
              last_message: msg.content,
              last_message_time: msg.created_at,
              unread_count: messages.filter(m => 
                m.projects?.client_id === clientId && !m.is_read
              ).length,
              is_online: Math.random() > 0.5 // Mock online status
            });
          }
        }
      });

      const conversationsList = Array.from(conversationMap.values())
        .sort((a, b) => new Date(b.last_message_time).getTime() - new Date(a.last_message_time).getTime());

      setConversations(conversationsList);
      setTotalUnread(messages.filter(m => !m.is_read).length);

    } catch (error) {
      console.error('Error fetching communication data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const truncateMessage = (message: string, maxLength: number = 50) => {
    return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.8 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-gray-900">Messages</h3>
            {totalUnread > 0 && (
              <span className="ml-2 bg-blue-500 text-white text-xs font-medium px-2 py-1 rounded-full">
                {totalUnread}
              </span>
            )}
          </div>
          <Link href="/designer/messages">
            <Button variant="ghost" size="sm">
              View All <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </div>
      </div>
      
      <div className="p-6">
        {conversations.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Messages</h4>
            <p className="text-gray-600">You'll see client messages here when they contact you about projects.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Quick Actions */}
            <div className="flex items-center space-x-2 pb-4 border-b">
              <Button variant="outline" size="sm" className="flex items-center">
                <Phone className="h-4 w-4 mr-1" />
                Call
              </Button>
              <Button variant="outline" size="sm" className="flex items-center">
                <Video className="h-4 w-4 mr-1" />
                Video
              </Button>
              <Button variant="outline" size="sm" className="flex items-center">
                <Paperclip className="h-4 w-4 mr-1" />
                Share
              </Button>
            </div>

            {/* Recent Conversations */}
            <div className="space-y-3">
              {conversations.slice(0, 4).map((conversation) => (
                <motion.div
                  key={conversation.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                >
                  <div className="relative">
                    <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                      {conversation.client_avatar ? (
                        <img
                          src={conversation.client_avatar}
                          alt={conversation.client_name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    {conversation.is_online && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {conversation.client_name}
                      </h4>
                      <div className="flex items-center space-x-1">
                        {conversation.unread_count > 0 && (
                          <span className="bg-blue-500 text-white text-xs font-medium px-2 py-1 rounded-full">
                            {conversation.unread_count}
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          {formatTimeAgo(conversation.last_message_time)}
                        </span>
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 truncate mb-1">
                      {truncateMessage(conversation.last_message)}
                    </p>
                    
                    <p className="text-xs text-gray-500 truncate">
                      Project: {conversation.project_title}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Quick Reply Section */}
            {conversations.length > 0 && (
              <div className="pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      placeholder="Type a quick reply..."
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    />
                  </div>
                  <Button size="sm" className="bg-brown-600 hover:bg-brown-700 text-white">
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
}
