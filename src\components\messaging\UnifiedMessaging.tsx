"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { motion, AnimatePresence } from "framer-motion";
import { messagingKeys, messagingCacheUtils, messagingQueryConfig } from "@/hooks/useMessagingKeys";
import { toast } from "react-hot-toast";
import {
  Send,
  Paperclip,
  User,
  MessageSquare,
  ArrowLeft,
  MoreVertical,
  Search,
  Phone,
  Video,
  Info,
  Loader2
} from "lucide-react";

interface Message {
  id: string;
  content: string;
  sender_id: string;
  message_type: string;
  created_at: string;
  profiles: {
    id: string;
    full_name: string;
    avatar_url?: string;
    role: string;
  };
  message_attachments?: Array<{
    id: string;
    file_url: string;
    file_name: string;
    file_type?: string;
  }>;
}

interface Conversation {
  id: string;
  type: string;
  title: string;
  project?: {
    title: string;
    status: string;
  };
  participants: Array<{
    id: string;
    full_name: string;
    avatar_url?: string;
    role: string;
  }>;
  latest_message?: {
    content: string;
    created_at: string;
    profiles: {
      full_name: string;
    };
  };
  unread_count: number;
  last_message_at: string;
  created_at: string;
}

interface UnifiedMessagingProps {
  projectId?: string;
  otherUserId?: string;
  conversationType?: 'direct' | 'project';
}

export default function UnifiedMessaging({ projectId, otherUserId, conversationType = 'project' }: UnifiedMessagingProps) {
  const { user } = useOptimizedAuth();
  const queryClient = useQueryClient();
  const [activeConversation, setActiveConversation] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [sending, setSending] = useState(false);
  const [showMobileConversations, setShowMobileConversations] = useState(!projectId && !otherUserId);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Optimized conversations query with standardized caching
  const { data: conversations = [], isLoading: conversationsLoading, error: conversationsError } = useQuery({
    queryKey: messagingKeys.conversations(user?.id || ''),
    queryFn: async () => {
      if (!user) return [];

      const response = await fetch('/api/conversations', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch conversations: ${errorText}`);
      }
      return response.json();
    },
    enabled: !!user,
    ...messagingQueryConfig.conversations,
  });

  // Optimized messages query with standardized caching
  const { data: messages = [], isLoading: messagesLoading, error: messagesError } = useQuery({
    queryKey: messagingKeys.messages(activeConversation || ''),
    queryFn: async () => {
      if (!activeConversation) return [];

      const response = await fetch(`/api/conversations/${activeConversation}/messages`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch messages: ${errorText}`);
      }
      const data = await response.json();
      return data.messages || [];
    },
    enabled: !!activeConversation,
    ...messagingQueryConfig.messages,
  });

  // Auto-select first conversation or create one if needed
  useEffect(() => {
    if (user && (projectId || otherUserId)) {
      handleAutoConversation();
    } else if (conversations.length > 0 && !activeConversation) {
      setActiveConversation(conversations[0].id);
    }
  }, [user, projectId, otherUserId, conversations, activeConversation]);

  useEffect(() => {
    if (activeConversation) {
      setShowMobileConversations(false);
    }
  }, [activeConversation]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleAutoConversation = async () => {
    if (!user) return;

    try {
      const { data: { session } } = await supabase.auth.getSession();

      let requestBody: Record<string, unknown> = {};

      if (projectId) {
        requestBody = {
          type: 'project',
          project_id: projectId
        };
      } else if (otherUserId) {
        requestBody = {
          type: 'direct',
          other_user_id: otherUserId
        };
      }

      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token}`
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        const conversation = await response.json();
        setActiveConversation(conversation.id);
        // Invalidate and refetch conversations
        queryClient.invalidateQueries({ queryKey: ['conversations', user.id] });
      }
    } catch (error) {
      console.error('Error creating/finding conversation:', error);
    }
  };

  // Optimized send message function
  const sendMessage = async () => {
    if (!newMessage.trim() || !activeConversation || sending || !user) return;

    const messageContent = newMessage.trim();
    setSending(true);

    // Add optimistic update
    const removeOptimistic = messagingCacheUtils.addOptimisticMessage(
      queryClient,
      activeConversation,
      {
        content: messageContent,
        sender_id: user.id,
        profiles: {
          id: user.id,
          full_name: user.user_metadata?.full_name || 'You',
          avatar_url: user.user_metadata?.avatar_url,
          role: user.user_metadata?.role
        }
      }
    );

    try {
      const { data: { session } } = await supabase.auth.getSession();

      const response = await fetch(`/api/conversations/${activeConversation}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session?.access_token}`
        },
        body: JSON.stringify({
          content: messageContent
        })
      });

      if (response.ok) {
        const realMessage = await response.json();
        setNewMessage("");

        // Update optimistic message with real data
        messagingCacheUtils.updateOptimisticMessage(
          queryClient,
          activeConversation,
          `temp_${Date.now()}`,
          realMessage
        );

        // Immediate cache refresh - NO DELAYS
        await messagingCacheUtils.refreshAfterSend(queryClient, activeConversation, user.id);

        toast.success('Message sent');
      } else {
        removeOptimistic();
        const errorText = await response.text();
        toast.error(`Failed to send message: ${errorText}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      removeOptimistic();
      toast.error('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };



  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Memoized helper functions for better performance
  const formatTime = useMemo(() => (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }, []);

  const getConversationDisplayInfo = useMemo(() => (conversation: Conversation): { title: string; subtitle: string; avatar: string | null } => {
    if (conversation.type === 'project') {
      return {
        title: conversation.title,
        subtitle: conversation.project?.title || 'Project',
        avatar: null
      };
    } else if (conversation.type === 'direct' && conversation.participants.length > 0) {
      const otherParticipant = conversation.participants[0];
      return {
        title: otherParticipant.full_name,
        subtitle: `${otherParticipant.role.charAt(0).toUpperCase() + otherParticipant.role.slice(1)}`,
        avatar: otherParticipant.avatar_url || null
      };
    } else {
      return {
        title: conversation.title,
        subtitle: 'Direct Message',
        avatar: null
      };
    }
  }, []);

  const activeConversationData = useMemo(() =>
    conversations.find(c => c.id === activeConversation),
    [conversations, activeConversation]
  );

  const isLoading = conversationsLoading || messagesLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px] lg:h-[600px]">
        <div className="flex flex-col items-center space-y-3">
          <Loader2 className="h-8 w-8 animate-spin text-brown-600" />
          <p className="text-sm text-gray-500">Loading conversations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col lg:flex-row min-h-[400px] lg:h-[600px] bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Conversations Sidebar */}
      <div className={`${showMobileConversations ? 'flex' : 'hidden'} lg:flex w-full lg:w-80 border-r border-gray-200 flex-col`}>
        <div className="p-3 lg:p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-base lg:text-lg font-semibold text-gray-900">Messages</h2>
            <Search className="h-4 w-4 lg:h-5 lg:w-5 text-gray-400" />
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No conversations</p>
            </div>
          ) : (
            conversations.map((conversation) => {
              const displayInfo = getConversationDisplayInfo(conversation);
              return (
                <div
                  key={conversation.id}
                  onClick={() => setActiveConversation(conversation.id)}
                  className={`p-3 lg:p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 active:bg-gray-100 transition-colors touch-manipulation ${
                    activeConversation === conversation.id ? 'bg-brown-50 border-brown-200' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 lg:w-12 lg:h-12 bg-brown-100 rounded-full flex items-center justify-center flex-shrink-0">
                      {displayInfo.avatar ? (
                        <img
                          src={displayInfo.avatar}
                          alt={displayInfo.title}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 lg:h-6 lg:w-6 text-brown-600" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm lg:text-base font-medium text-gray-900 truncate">
                          {displayInfo.title}
                        </p>
                        {conversation.unread_count > 0 && (
                          <span className="bg-brown-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center flex-shrink-0">
                            {conversation.unread_count}
                          </span>
                        )}
                      </div>
                      <p className="text-xs lg:text-sm text-gray-500 truncate">
                        {displayInfo.subtitle}
                      </p>
                      {conversation.latest_message && (
                        <p className="text-xs text-gray-400 truncate mt-1">
                          {conversation.latest_message.content}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Messages Area */}
      <div className={`flex-1 flex flex-col ${showMobileConversations ? 'hidden' : 'flex'} lg:flex`}>
        {activeConversation && activeConversationData ? (
          <>
            {/* Chat Header */}
            <div className="p-3 lg:p-4 border-b border-gray-200 bg-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setShowMobileConversations(true)}
                    className="lg:hidden p-2 rounded-full hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
                  >
                    <ArrowLeft className="h-5 w-5 text-gray-600" />
                  </button>
                  <div className="w-8 h-8 lg:w-10 lg:h-10 bg-brown-100 rounded-full flex items-center justify-center">
                    {getConversationDisplayInfo(activeConversationData).avatar ? (
                      <img
                        src={getConversationDisplayInfo(activeConversationData).avatar || undefined}
                        alt={getConversationDisplayInfo(activeConversationData).title}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <User className="h-4 w-4 lg:h-5 lg:w-5 text-brown-600" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-medium text-gray-900 text-sm lg:text-base truncate">
                      {getConversationDisplayInfo(activeConversationData).title}
                    </h3>
                    <p className="text-xs lg:text-sm text-gray-500 truncate">
                      {getConversationDisplayInfo(activeConversationData).subtitle}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-1 lg:space-x-2">
                  <button className="p-2 rounded-full hover:bg-gray-100 active:bg-gray-200 touch-manipulation">
                    <Phone className="h-4 w-4 text-gray-600" />
                  </button>
                  <button className="p-2 rounded-full hover:bg-gray-100 active:bg-gray-200 touch-manipulation">
                    <Video className="h-4 w-4 text-gray-600" />
                  </button>
                  <button className="p-2 rounded-full hover:bg-gray-100 active:bg-gray-200 touch-manipulation">
                    <Info className="h-4 w-4 text-gray-600" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-3 lg:p-4 space-y-3 lg:space-y-4">
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className={`flex ${message.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[85%] lg:max-w-[70%] ${message.sender_id === user?.id ? 'order-2' : 'order-1'}`}>
                      <div className="flex items-center mb-1">
                        <div className="w-6 h-6 lg:w-7 lg:h-7 bg-gray-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                          {message.profiles.avatar_url ? (
                            <img
                              src={message.profiles.avatar_url}
                              alt={message.profiles.full_name}
                              className="w-full h-full rounded-full object-cover"
                            />
                          ) : (
                            <User className="h-3 w-3 lg:h-4 lg:w-4 text-gray-400" />
                          )}
                        </div>
                        <span className="text-xs lg:text-sm font-medium text-gray-700 truncate">
                          {message.profiles.full_name}
                        </span>
                        <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                          {formatTime(message.created_at)}
                        </span>
                      </div>
                      <div className={`p-3 lg:p-4 rounded-lg ${
                        message.sender_id === user?.id
                          ? 'bg-brown-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <p className="text-sm lg:text-base whitespace-pre-wrap break-words">{message.content}</p>
                        {message.message_attachments && message.message_attachments.length > 0 && (
                          <div className="mt-2">
                            {message.message_attachments.map((attachment) => (
                              <a
                                key={attachment.id}
                                href={attachment.file_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs lg:text-sm underline block hover:no-underline"
                              >
                                📎 {attachment.file_name}
                              </a>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-3 lg:p-4 border-t border-gray-200 bg-white">
              <div className="flex items-center space-x-2">
                <button className="p-2 lg:p-3 rounded-full hover:bg-gray-100 active:bg-gray-200 touch-manipulation">
                  <Paperclip className="h-4 w-4 lg:h-5 lg:w-5 text-gray-600" />
                </button>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a message..."
                  className="flex-1 p-3 lg:p-4 border border-gray-300 rounded-full focus:outline-none focus:border-brown-500 focus:ring-2 focus:ring-brown-200 text-sm lg:text-base"
                />
                <button
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || sending}
                  className="flex-shrink-0 bg-brown-600 hover:bg-brown-700 active:bg-brown-800 text-white rounded-full p-3 lg:p-4 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation transition-colors"
                >
                  {sending ? (
                    <Loader2 className="h-4 w-4 lg:h-5 lg:w-5 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4 lg:h-5 lg:w-5" />
                  )}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center">
              <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageSquare className="h-8 w-8 lg:h-10 lg:w-10 text-gray-400" />
              </div>
              <h3 className="text-base lg:text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
              <p className="text-sm lg:text-base text-gray-500">Choose a conversation to start messaging</p>
              <button
                onClick={() => setShowMobileConversations(true)}
                className="lg:hidden mt-4 px-4 py-2 bg-brown-600 text-white rounded-lg hover:bg-brown-700 active:bg-brown-800 touch-manipulation"
              >
                View Conversations
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );}
