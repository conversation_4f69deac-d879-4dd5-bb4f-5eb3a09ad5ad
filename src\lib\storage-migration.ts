/**
 * Storage Migration Utility
 * Helps migrate files from Supabase Storage to Cloudflare R2
 */

import { supabase } from './supabase';
import { 
  uploadProjectFile, 
  uploadMilestoneDeliverable, 
  uploadPortfolioImage,
  uploadDisputeAttachment,
  getProjectFileUrl,
  getDeliverableFileUrl,
  getPortfolioImageUrl,
  getDisputeAttachmentUrl
} from './r2-upload';

interface MigrationResult {
  success: boolean;
  originalUrl: string;
  newUrl?: string;
  error?: string;
}

interface FileRecord {
  id: string;
  file_url: string;
  file_name?: string;
  project_id?: string;
  milestone_id?: string;
  dispute_id?: string;
  file_type?: string;
}

/**
 * Downloads a file from Supabase Storage
 */
async function downloadSupabaseFile(url: string): Promise<{ buffer: Buffer; fileName: string }> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }
    
    const buffer = Buffer.from(await response.arrayBuffer());
    
    // Extract filename from URL or generate one
    const urlParts = url.split('/');
    const fileName = urlParts[urlParts.length - 1] || `file-${Date.now()}`;
    
    return { buffer, fileName };
  } catch (error) {
    console.error('Error downloading file from Supabase:', error);
    throw error;
  }
}

/**
 * Migrates a single file from Supabase Storage to R2
 */
export async function migrateFileToR2(
  supabaseUrl: string,
  projectId: string,
  fileType: 'deliverable' | 'attachment' | 'submission' | 'message' | 'inspiration' | 'portfolio' | 'dispute',
  milestoneId?: string,
  disputeId?: string
): Promise<MigrationResult> {
  try {
    // Skip if already an R2 URL
    if (supabaseUrl.includes('r2.dev') || supabaseUrl.includes('cloudflare')) {
      return {
        success: true,
        originalUrl: supabaseUrl,
        newUrl: supabaseUrl
      };
    }

    // Download file from Supabase
    const { buffer, fileName } = await downloadSupabaseFile(supabaseUrl);
    
    let r2Key: string;
    let newUrl: string;

    // Upload to appropriate R2 bucket based on file type
    switch (fileType) {
      case 'deliverable':
        if (!milestoneId) throw new Error('Milestone ID required for deliverable files');
        r2Key = await uploadMilestoneDeliverable(buffer, fileName, projectId, milestoneId);
        newUrl = getDeliverableFileUrl(r2Key);
        break;
        
      case 'portfolio':
        r2Key = await uploadPortfolioImage(buffer, fileName, projectId);
        newUrl = getPortfolioImageUrl(r2Key);
        break;
        
      case 'dispute':
        if (!disputeId) throw new Error('Dispute ID required for dispute files');
        r2Key = await uploadDisputeAttachment(buffer, fileName, disputeId);
        newUrl = getDisputeAttachmentUrl(r2Key);
        break;
        
      default:
        // For other file types, use general project files
        r2Key = await uploadProjectFile(buffer, fileName, projectId, fileType);
        newUrl = getProjectFileUrl(r2Key);
        break;
    }

    return {
      success: true,
      originalUrl: supabaseUrl,
      newUrl
    };
  } catch (error) {
    console.error('Error migrating file to R2:', error);
    return {
      success: false,
      originalUrl: supabaseUrl,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Migrates project deliverable files to R2
 */
export async function migrateProjectDeliverables(): Promise<MigrationResult[]> {
  try {
    // Get all project milestones with deliverable files
    const { data: milestones, error } = await supabase
      .from('project_milestones')
      .select(`
        id,
        project_id,
        deliverable_files,
        title
      `)
      .not('deliverable_files', 'is', null);

    if (error) throw error;

    const results: MigrationResult[] = [];

    for (const milestone of milestones || []) {
      if (milestone.deliverable_files && Array.isArray(milestone.deliverable_files)) {
        for (const fileUrl of milestone.deliverable_files) {
          if (typeof fileUrl === 'string') {
            const result = await migrateFileToR2(
              fileUrl,
              milestone.project_id,
              'deliverable',
              milestone.id
            );
            results.push(result);

            // Update database if migration successful
            if (result.success && result.newUrl) {
              const updatedFiles = milestone.deliverable_files.map((url: string) =>
                url === fileUrl ? result.newUrl : url
              );
              
              await supabase
                .from('project_milestones')
                .update({ deliverable_files: updatedFiles })
                .eq('id', milestone.id);
            }
          }
        }
      }
    }

    return results;
  } catch (error) {
    console.error('Error migrating project deliverables:', error);
    return [{
      success: false,
      originalUrl: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    }];
  }
}

/**
 * Migrates conversation message files to R2
 */
export async function migrateConversationFiles(): Promise<MigrationResult[]> {
  try {
    // Get all conversation messages with file attachments
    const { data: messages, error } = await supabase
      .from('conversation_messages')
      .select(`
        id,
        file_url,
        file_name,
        conversation_id,
        conversations:conversation_id (
          id,
          type,
          project_id
        )
      `)
      .not('file_url', 'is', null);

    if (error) throw error;

    const results: MigrationResult[] = [];

    for (const message of messages || []) {
      if (message.file_url) {
        // Use conversation ID as project ID for organization
        const result = await migrateFileToR2(
          message.file_url,
          message.conversation_id,
          'message'
        );
        results.push(result);

        // Update database if migration successful
        if (result.success && result.newUrl) {
          await supabase
            .from('conversation_messages')
            .update({ file_url: result.newUrl })
            .eq('id', message.id);
        }
      }
    }

    return results;
  } catch (error) {
    console.error('Error migrating conversation files:', error);
    return [{
      success: false,
      originalUrl: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    }];
  }
}

/**
 * Migrates designer portfolio files to R2
 */
export async function migrateDesignerPortfolios(): Promise<MigrationResult[]> {
  try {
    // Get all designer applications with portfolio files
    const { data: applications, error } = await supabase
      .from('designer_applications')
      .select(`
        id,
        portfolio_files,
        designer_id
      `)
      .not('portfolio_files', 'is', null);

    if (error) throw error;

    const results: MigrationResult[] = [];

    for (const application of applications || []) {
      if (application.portfolio_files && Array.isArray(application.portfolio_files)) {
        for (const fileUrl of application.portfolio_files) {
          if (typeof fileUrl === 'string') {
            const result = await migrateFileToR2(
              fileUrl,
              application.designer_id,
              'portfolio'
            );
            results.push(result);

            // Update database if migration successful
            if (result.success && result.newUrl) {
              const updatedFiles = application.portfolio_files.map((url: string) =>
                url === fileUrl ? result.newUrl : url
              );
              
              await supabase
                .from('designer_applications')
                .update({ portfolio_files: updatedFiles })
                .eq('id', application.id);
            }
          }
        }
      }
    }

    return results;
  } catch (error) {
    console.error('Error migrating designer portfolios:', error);
    return [{
      success: false,
      originalUrl: '',
      error: error instanceof Error ? error.message : 'Unknown error'
    }];
  }
}

/**
 * Runs complete migration from Supabase Storage to R2
 */
export async function runCompleteStorageMigration(): Promise<{
  deliverables: MigrationResult[];
  conversations: MigrationResult[];
  portfolios: MigrationResult[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}> {
  console.log('Starting complete storage migration to R2...');

  const deliverables = await migrateProjectDeliverables();
  console.log(`Migrated ${deliverables.length} deliverable files`);

  const conversations = await migrateConversationFiles();
  console.log(`Migrated ${conversations.length} conversation files`);

  const portfolios = await migrateDesignerPortfolios();
  console.log(`Migrated ${portfolios.length} portfolio files`);

  const allResults = [...deliverables, ...conversations, ...portfolios];
  const successful = allResults.filter(r => r.success).length;
  const failed = allResults.filter(r => !r.success).length;

  console.log(`Migration complete: ${successful} successful, ${failed} failed`);

  return {
    deliverables,
    conversations,
    portfolios,
    summary: {
      total: allResults.length,
      successful,
      failed
    }
  };
}
