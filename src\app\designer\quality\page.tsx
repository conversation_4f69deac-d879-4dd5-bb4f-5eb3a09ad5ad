"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Star,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  AlertTriangle,
  Eye,
  RefreshCw,
  Filter,
  Search,
  Calendar,
  FileText
} from "lucide-react";

interface QualityReview {
  id: string;
  project_id: string;
  status: string;
  overall_score: number | null;
  feedback: string | null;
  revision_notes: string | null;
  revision_count: number;
  reviewed_at: string | null;
  created_at: string;
  project: {
    title: string;
  };
  reviewer: {
    full_name: string;
  } | null;
}

export default function DesignerQualityPage() {
  const { user, profile } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'designer') {
      fetchQualityReviews();
    }
  }, [user, profile, filter]);

  const fetchQualityReviews = async () => {
    try {
      let query = supabase
        .from('quality_reviews')
        .select(`
          *,
          project:projects(title),
          reviewer:profiles!quality_reviews_reviewer_id_fkey(full_name)
        `)
        .eq('designer_id', user?.id);

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false });

      if (error) throw error;
      setReviews(data || []);
    } catch (error) {
      console.error('Error fetching quality reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'in_review':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'needs_revision':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'escalated':
        return <AlertTriangle className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'in_review':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'needs_revision':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case 'escalated':
        return `${baseClasses} bg-purple-100 text-purple-800 border border-purple-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getQualityStats = () => {
    const total = reviews.length;
    const approved = reviews.filter(r => r.status === 'approved').length;
    const needsRevision = reviews.filter(r => r.status === 'needs_revision').length;
    const avgScore = reviews.filter(r => r.overall_score !== null)
      .reduce((sum, r) => sum + (r.overall_score || 0), 0) / 
      reviews.filter(r => r.overall_score !== null).length || 0;

    return { total, approved, needsRevision, avgScore };
  };

  const filteredReviews = reviews.filter(review =>
    review.project?.title?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const stats = getQualityStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quality Reviews</h1>
          <p className="text-gray-600 mt-2">Track your design quality performance and feedback</p>
        </div>
        <Button
          onClick={fetchQualityReviews}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Reviews</p>
              <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-2xl font-bold text-purple-600">{stats.avgScore.toFixed(1)}/5</p>
            </div>
            <Star className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approval Rate</p>
              <p className="text-2xl font-bold text-orange-600">
                {stats.total > 0 ? Math.round((stats.approved / stats.total) * 100) : 0}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="all">All Reviews</option>
              <option value="pending">Pending</option>
              <option value="in_review">In Review</option>
              <option value="approved">Approved</option>
              <option value="needs_revision">Needs Revision</option>
              <option value="escalated">Escalated</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Quality Review History</h2>
          <p className="text-gray-600 mt-1">Your design submissions and quality feedback</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredReviews.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No quality reviews found</p>
              <p className="text-sm text-gray-400 mt-2">Submit your first project to get quality feedback</p>
            </div>
          ) : (
            filteredReviews.map((review) => (
              <div key={review.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {getStatusIcon(review.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {review.project?.title || 'Untitled Project'}
                      </h3>
                      <span className={getStatusBadge(review.status)}>
                        {review.status.replace('_', ' ').toUpperCase()}
                      </span>
                      {review.overall_score && (
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="font-semibold text-gray-900">{review.overall_score}/5</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">Submitted:</span> {new Date(review.created_at).toLocaleDateString()}
                      </div>
                      {review.reviewed_at && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4" />
                          <span className="font-medium">Reviewed:</span> {new Date(review.reviewed_at).toLocaleDateString()}
                        </div>
                      )}
                      {review.reviewer && (
                        <div className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          <span className="font-medium">Reviewer:</span> {review.reviewer.full_name}
                        </div>
                      )}
                    </div>

                    {review.revision_count > 0 && (
                      <div className="mb-3">
                        <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-orange-100 text-orange-800">
                          {review.revision_count} revision{review.revision_count > 1 ? 's' : ''} requested
                        </span>
                      </div>
                    )}

                    {review.feedback && (
                      <div className="bg-blue-50 rounded-lg p-4 mb-3">
                        <h4 className="font-medium text-blue-900 mb-2">Quality Feedback:</h4>
                        <p className="text-sm text-blue-800">{review.feedback}</p>
                      </div>
                    )}

                    {review.revision_notes && (
                      <div className="bg-red-50 rounded-lg p-4">
                        <h4 className="font-medium text-red-900 mb-2">Revision Notes:</h4>
                        <p className="text-sm text-red-800">{review.revision_notes}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/designer/projects/${review.project_id}`}
                    >
                      <Eye className="h-4 w-4" />
                      View Project
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Quality Tips */}
      <div className="bg-green-50 rounded-xl p-6 border border-green-200">
        <div className="flex items-start gap-3">
          <Star className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-green-900 mb-2">Quality Improvement Tips</h3>
            <div className="text-green-800 space-y-2">
              <p>• <strong>Follow Standards:</strong> Review quality standards before submitting</p>
              <p>• <strong>Address Feedback:</strong> Carefully implement revision suggestions</p>
              <p>• <strong>Consistent Quality:</strong> Maintain high standards across all projects</p>
              <p>• <strong>Learn & Improve:</strong> Use feedback to enhance your design skills</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
