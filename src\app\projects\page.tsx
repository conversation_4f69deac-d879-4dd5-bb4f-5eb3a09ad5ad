"use client";

import { useState } from "react";
import Layout from "@/components/Layout";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Grid, List } from "lucide-react";
import Link from "next/link";

const projects = [
  {
    id: "1",
    title: "The Glass Pavilion",
    category: "Residential",
    location: "Cape Town",
    year: "2023",
    image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c",
    description: "A minimalist glass structure with panoramic ocean views."
  },
  {
    id: "2",
    title: "Urban Heights Tower",
    category: "Commercial",
    location: "London",
    year: "2022",
    image: "https://images.unsplash.com/photo-1600573472550-8090b5e0745e",
    description: "Mixed-use high-rise combining office and retail spaces."
  },
  {
    id: "3",
    title: "The Eco Hub",
    category: "Public",
    location: "Amsterdam",
    year: "2023",
    image: "https://images.unsplash.com/photo-1600585154526-990dced4db0d",
    description: "Sustainable community center with innovative green technologies."
  },
  // Add more projects as needed
];

const categories = ["All", "Residential", "Commercial", "Public", "Cultural"];

export default function Projects() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredProjects = selectedCategory === "All"
    ? projects
    : projects.filter(project => project.category === selectedCategory);

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative h-[60vh] flex items-center">
        <Image
          src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c"
          alt="Projects Hero"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/50" />
        <div className="container mx-auto px-4 relative z-10 text-white">
          <h1 className="text-5xl md:text-7xl font-bold mb-6">Our Projects</h1>
          <p className="text-xl md:text-2xl max-w-2xl">
            Exploring the boundaries of architecture through innovative design solutions.
          </p>
        </div>
      </section>

      {/* Filter and View Toggle */}
      <section className="py-8 border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex gap-4">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "ghost"}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="icon"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="icon"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid/List */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredProjects.map((project) => (
                <Link href={`/projects/${project.id}`} key={project.id}>
                  <div className="group cursor-pointer">
                    <div className="relative aspect-[4/3] overflow-hidden mb-4">
                      <Image
                        src={project.image}
                        alt={project.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <h3 className="text-xl font-bold mb-2">{project.title}</h3>
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                      <span>{project.category}</span>
                      <span className="mx-2">•</span>
                      <span>{project.location}</span>
                      <span className="mx-2">•</span>
                      <span>{project.year}</span>
                    </div>
                    <p className="text-gray-600">{project.description}</p>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="space-y-8">
              {filteredProjects.map((project) => (
                <Link href={`/projects/${project.id}`} key={project.id}>
                  <div className="group cursor-pointer flex gap-8">
                    <div className="relative w-72 aspect-[4/3] overflow-hidden">
                      <Image
                        src={project.image}
                        alt={project.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold mb-2">{project.title}</h3>
                      <div className="flex items-center text-sm text-gray-600 mb-4">
                        <span>{project.category}</span>
                        <span className="mx-2">•</span>
                        <span>{project.location}</span>
                        <span className="mx-2">•</span>
                        <span>{project.year}</span>
                      </div>
                      <p className="text-gray-600 mb-4">{project.description}</p>
                      <Button className="group">
                        View Project
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </section>
    </Layout>
  );
}

