"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import Link from "next/link";

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (resetError) throw new Error(resetError.message);

      setSuccess(true);
    } catch (error: unknown) {
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An error occurred while sending the reset link");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-black">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black bg-opacity-70 z-10" aria-hidden="true" />
        <img
          src="https://images.unsplash.com/photo-1600573472550-8090b5e0745e"
          alt="Architectural background"
          className="object-cover w-full h-full"
        />
      </div>

      <div className="relative z-10 bg-white p-10 rounded-none shadow-2xl max-w-md w-full">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <h1 className="text-primary font-bold text-2xl tracking-tight">SENIOR'S ARCHI-FIRM</h1>
          </Link>
          <h2 className="text-2xl font-bold mt-6 mb-2">Reset Your Password</h2>
          <p className="text-gray-600">Enter your email to receive a password reset link</p>
        </div>

        {error && (
          <div className="bg-red-50 text-red-500 p-4 mb-6 border-l-4 border-red-500">
            {error}
          </div>
        )}

        {success ? (
          <div className="text-center">
            <div className="bg-green-50 text-green-600 p-4 mb-6 border-l-4 border-green-500">
              Password reset link sent! Check your email inbox.
            </div>
            <Link href="/auth/login">
              <Button variant="default" size="lg" className="w-full">
                Return to Login
              </Button>
            </Link>
          </div>
        ) : (
          <form onSubmit={handleResetPassword} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>

            <Button
              type="submit"
              variant="default"
              size="lg"
              className="w-full"
              disabled={loading}
            >
              {loading ? "Sending..." : "Send Reset Link"}
            </Button>

            <div className="text-center">
              <Link href="/auth/login" className="text-primary text-sm hover:underline">
                Back to Login
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
