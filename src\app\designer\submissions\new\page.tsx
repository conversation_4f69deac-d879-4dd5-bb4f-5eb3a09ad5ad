"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  FolderKanban,
  Calendar,
  Users,
  AlertCircle,
  Search,
  Clock
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  status: string;
  deadline: string | null;
  client_name: string;
  updated_at: string;
};

export default function SelectProjectForSubmission() {
  const router = useRouter();
  const { user } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (user) {
      fetchActiveProjects();
    }
  }, [user]);

  useEffect(() => {
    if (searchTerm) {
      setFilteredProjects(
        projects.filter(project =>
          project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          project.client_name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    } else {
      setFilteredProjects(projects);
    }
  }, [projects, searchTerm]);

  const fetchActiveProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          status,
          deadline,
          updated_at,
          profiles:client_id(full_name)
        `)
        .eq('designer_id', user?.id)
        .in('status', ['pending', 'in_progress', 'review'])
        .order('updated_at', { ascending: false });

      if (error) throw error;

      const formattedProjects = data.map(project => ({
        id: project.id,
        title: project.title,
        status: project.status,
        deadline: project.deadline,
        client_name: project.profiles?.[0]?.full_name || 'Unknown Client',
        updated_at: project.updated_at
      }));

      setProjects(formattedProjects);
      setFilteredProjects(formattedProjects);
    } catch (error: unknown) {
      console.error('Error fetching projects:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('Failed to load projects');
      }
    } finally {
      setLoading(false);
    }
  };  const formatDate = (dateString: string | null) => {    if (!dateString) return 'No deadline';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.round(diffMs / 1000);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);

    if (diffSec < 60) return `${diffSec} sec ago`;
    if (diffMin < 60) return `${diffMin} min ago`;
    if (diffHour < 24) return `${diffHour} hr ago`;
    if (diffDay < 7) return `${diffDay} day ago`;

    return formatDate(dateString);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in_progress':
        return 'bg-brown-100 text-brown-800';
      case 'review':
        return 'bg-brown-50 text-brown-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatusLabel = (status: string) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-8">
        <Link href="/designer/submissions">
          <Button variant="ghost" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Submissions
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Select Project for New Submission</h1>
      </div>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <p>{error}</p>
        </div>
      )}

      {/* Search bar */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search projects by name or client..."
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {filteredProjects.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <FolderKanban className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="font-medium mb-1">No active projects found</h3>
          <p className="text-sm text-gray-500 mb-4">
            {searchTerm
              ? "No projects match your search criteria."
              : "You don't have any active projects to create submissions for."}
          </p>
          <Link href="/designer/projects">
            <Button>View All Projects</Button>
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold">Select a Project</h2>
            <p className="text-sm text-gray-500 mt-1">
              Choose the project you want to create a submission for
            </p>
          </div>

          <div className="divide-y">
            {filteredProjects.map((project) => (
              <Link href={`/designer/projects/${project.id}/submissions/new`} key={project.id}>
                <div className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium">{project.title}</h3>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(project.status)}`}>
                      {formatStatusLabel(project.status)}
                    </span>
                  </div>

                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <Users className="h-4 w-4 mr-1" />
                    <span>Client: {project.client_name}</span>

                    <span className="mx-2">•</span>
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>Due: {formatDate(project.deadline)}</span>

                    <span className="mx-2">•</span>
                    <Clock className="h-4 w-4 mr-1" />
                    <span>Updated: {getTimeAgo(project.updated_at)}</span>
                  </div>

                  <Button variant="outline" size="sm" className="mt-2">
                    Create Submission for this Project
                  </Button>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}