import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) {
    try {
      const authHeader = request.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const token = authHeader.split(' ')[1];

      // Verify the token and get the user
      const { data: { user }, error: authError } = await supabase.auth.getUser(token);

      if (authError || !user) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const { id } = await params;
      const conversationId = id;
      const { searchParams } = new URL(request.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');
      const offset = (page - 1) * limit;

      // Get user profile to check role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (profileError || !profile) {
        return NextResponse.json({ error: 'User profile not found' }, { status: 403 });
      }

      // Check if user is admin or participant in this conversation
      const isAdmin = profile.role === 'admin';

      if (!isAdmin) {
        // For non-admin users, verify they are participants in this conversation
        const { data: participant, error: participantError } = await supabase
          .from('conversation_participants')
          .select('id')
          .eq('conversation_id', conversationId)
          .eq('user_id', user.id)
          .single();

        if (participantError || !participant) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }
      }

      // Get messages with sender info, attachments, read status, and admin fields
      const { data: messages, error } = await supabase
        .from('conversation_messages')
        .select(`
          id,
          content,
          message_type,
          reply_to_id,
          edited_at,
          created_at,
          sender_id,
          is_flagged,
          flagged_by,
          flagged_at,
          flag_reason,
          admin_reviewed,
          admin_reviewed_by,
          admin_reviewed_at,
          admin_notes,
          profiles:sender_id (
            id,
            full_name,
            avatar_url,
            role
          ),
          message_attachments (
            id,
            file_url,
            file_name,
            file_type
          ),
          message_reactions (
            id,
            emoji,
            user_id,
            profiles:user_id (
              full_name,
              avatar_url
            )
          ),
          reply_to:reply_to_id (
            id,
            content,
            message_type,
            sender_id,
            profiles:sender_id (
              full_name
            )
          ),
          flagged_by_profile:flagged_by (
            full_name
          ),
          admin_reviewed_by_profile:admin_reviewed_by (
            full_name
          )
        `)
        .eq('conversation_id', conversationId)
        .is('deleted_at', null)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching messages:', error);
        return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
      }

      // Get read status for each message
      const messageIds = messages?.map(m => m.id) || [];
      const { data: readStatuses } = await supabase
        .from('message_read_status')
        .select(`
          message_id,
          user_id,
          read_at,
          profiles:user_id (
            full_name,
            avatar_url
          )
        `)
        .in('message_id', messageIds);

      // Combine messages with read status
      const messagesWithReadStatus = messages?.map(message => ({
        ...message,
        read_by: readStatuses?.filter(rs => rs.message_id === message.id) || [],
        is_read_by_me: readStatuses?.some(rs => rs.message_id === message.id && rs.user_id === user.id) || false
      }));

      // Mark messages as read by current user (only for non-admin participants)
      if (messages && messages.length > 0 && !isAdmin) {
        const unreadMessageIds = messages
          .filter(m => m.sender_id !== user.id)
          .map(m => m.id);

        if (unreadMessageIds.length > 0) {
          // Insert read status for unread messages
          const readStatusData = unreadMessageIds.map(messageId => ({
            message_id: messageId,
            user_id: user.id
          }));

          await supabase
            .from('message_read_status')
            .upsert(readStatusData, { onConflict: 'message_id,user_id' });

          // Update participant's last_read_at
          await supabase
            .from('conversation_participants')
            .update({ last_read_at: new Date().toISOString() })
            .eq('conversation_id', conversationId)
            .eq('user_id', user.id);
        }
      }

      return NextResponse.json({
        messages: messagesWithReadStatus?.reverse() || [], // Reverse to show oldest first
        page,
        limit,
        has_more: messages?.length === limit
      });
    } catch (error) {
      console.error('Error in GET /api/conversations/[id]/messages:', error);
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
  }

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
    try {
      const authHeader = request.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const token = authHeader.split(' ')[1];

      // Verify the token and get the user
      const { data: { user }, error: authError } = await supabase.auth.getUser(token);

      if (authError || !user) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const { id } = await params;
      const conversationId = id;
      const { content, message_type = 'text', reply_to_id, attachments } = await request.json();

      // Validate required fields
      if (!content || content.trim() === '') {
        return NextResponse.json({ error: 'Message content is required' }, { status: 400 });
      }

      // Get user profile to check role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (profileError || !profile) {
        return NextResponse.json({ error: 'User profile not found' }, { status: 403 });
      }

      // Check if user is admin or participant in this conversation
      const isAdmin = profile.role === 'admin';

      if (!isAdmin) {
        // For non-admin users, verify they are participants in this conversation
        const { data: participant, error: participantError } = await supabase
          .from('conversation_participants')
          .select('id')
          .eq('conversation_id', conversationId)
          .eq('user_id', user.id)
          .single();

        if (participantError || !participant) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }
      }

      // Create message with complete response data
      const { data: message, error: messageError } = await supabase
        .from('conversation_messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          content: content.trim(),
          message_type,
          reply_to_id
        })
        .select(`
          id,
          content,
          message_type,
          reply_to_id,
          created_at,
          sender_id,
          edited_at,
          deleted_at,
          is_flagged,
          flag_reason,
          flagged_by,
          admin_reviewed,
          admin_notes,
          admin_reviewed_by,
          admin_reviewed_at,
          profiles:sender_id (
            id,
            full_name,
            avatar_url,
            role
          ),
          reply_to:reply_to_id (
            id,
            content,
            message_type,
            sender_id,
            profiles:sender_id (
              id,
              full_name,
              avatar_url
            )
          )
        `)
        .single();

      if (messageError) {
        console.error('Error creating message:', messageError);
        return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
      }

      // Handle attachments if provided
      if (attachments && attachments.length > 0) {
        const attachmentData = attachments.map((att: {
          file_url: string;
          file_name: string;
          file_type: string;
          file_size: number;
          thumbnail_url?: string;
        }) => ({
          message_id: message.id,
          file_url: att.file_url,
          file_name: att.file_name,
          file_type: att.file_type,
          file_size: att.file_size,
          thumbnail_url: att.thumbnail_url
        }));

        const { error: attachmentError } = await supabase
          .from('message_attachments')
          .insert(attachmentData);

        if (attachmentError) {
          console.error('Error adding attachments:', attachmentError);
        }
      }

      // Get other participants for notifications
      const { data: otherParticipants } = await supabase
        .from('conversation_participants')
        .select(`
          user_id,
          profiles:user_id (
            full_name,
            email
          )
        `)
        .eq('conversation_id', conversationId)
        .neq('user_id', user.id);

      // Create notifications for other participants
      if (otherParticipants && otherParticipants.length > 0) {
        const notifications = otherParticipants.map(participant => ({
          user_id: participant.user_id,
          type: 'message',
          content: `New message from ${
            Array.isArray(message.profiles)
              ? (message.profiles[0] as { full_name: string })?.full_name
              : (message.profiles as { full_name: string } | undefined)?.full_name
          }: ${content.length > 50 ? content.substring(0, 50) + '...' : content}`,
          related_id: conversationId,
          read: false
        }));

        await supabase
          .from('notifications')
          .insert(notifications);
      }

    // Get read status for the new message
    const { data: readStatuses } = await supabase
      .from('message_read_status')
      .select(`
        message_id,
        user_id,
        read_at,
        profiles:user_id (
          full_name,
          avatar_url
        )
      `)
      .eq('message_id', message.id);

    // Get attachments for the new message
    const { data: messageAttachments } = await supabase
      .from('message_attachments')
      .select('id, file_name, file_url, file_type, file_size, thumbnail_url')
      .eq('message_id', message.id);

    // Return complete message object with all relationships
    const completeMessage = {
      ...message,
      read_by: readStatuses || [],
      is_read_by_me: readStatuses?.some(rs => rs.user_id === user.id) || false,
      message_attachments: messageAttachments || []
    };

    return NextResponse.json(completeMessage, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/conversations/[id]/messages:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
