'use client';

import React, { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  Users, 
  MessageSquare, 
  Clock,
  CheckCircle,
  AlertTriangle,
  Star,
  BarChart3,
  Target,
  Handshake,
  ThumbsUp,
  Calendar,
  DollarSign,
  Award,
  Activity
} from 'lucide-react';

interface ManagerMetrics {
  totalProjects: number;
  activeNegotiations: number;
  pendingApprovals: number;
  clientSatisfactionAvg: number;
  performanceScore: number;
  onTimeDelivery: number;
  budgetVariance: number;
  communicationScore: number;
}

interface ProjectPerformance {
  id: string;
  title: string;
  client_name: string;
  designer_name: string;
  performance_score: number;
  performance_grade: string;
  timeline_variance: number;
  budget_variance: number;
  client_satisfaction: number;
  status: string;
  completion_percentage: number;
}

interface NegotiationSummary {
  id: string;
  title: string;
  project_title: string;
  negotiation_type: string;
  status: string;
  priority: string;
  deadline: string;
  initiated_by_name: string;
  days_remaining: number;
}

interface ClientFeedback {
  id: string;
  project_title: string;
  client_name: string;
  overall_rating: number;
  feedback_text: string;
  completed_at: string;
  survey_type: string;
}

export default function AdvancedManagerDashboard() {
  const { user } = useOptimizedAuth();
  const [metrics, setMetrics] = useState<ManagerMetrics>({
    totalProjects: 0,
    activeNegotiations: 0,
    pendingApprovals: 0,
    clientSatisfactionAvg: 0,
    performanceScore: 0,
    onTimeDelivery: 0,
    budgetVariance: 0,
    communicationScore: 0
  });
  const [projectPerformance, setProjectPerformance] = useState<ProjectPerformance[]>([]);
  const [negotiations, setNegotiations] = useState<NegotiationSummary[]>([]);
  const [clientFeedback, setClientFeedback] = useState<ClientFeedback[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchManagerData();
    }
  }, [user]);

  const fetchManagerData = async () => {
    try {
      setLoading(true);
      
      // Fetch manager metrics
      await fetchManagerMetrics();
      
      // Fetch project performance data
      await fetchProjectPerformance();
      
      // Fetch active negotiations
      await fetchActiveNegotiations();
      
      // Fetch recent client feedback
      await fetchClientFeedback();
      
    } catch (error) {
      console.error('Error fetching manager data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchManagerMetrics = async () => {
    try {
      // Get projects managed by this manager
      const { data: managedProjects } = await supabase
        .from('project_assignments')
        .select(`
          project_id,
          projects!inner(*)
        `)
        .eq('manager_id', user?.id)
        .eq('status', 'active');

      const totalProjects = managedProjects?.length || 0;

      // Get active negotiations
      const { data: activeNegotiations } = await supabase
        .from('negotiations')
        .select('id')
        .eq('manager_id', user?.id)
        .in('status', ['pending', 'in_progress']);

      // Get pending approvals (escrow + negotiations)
      const { data: pendingEscrowApprovals } = await supabase
        .from('paypal_escrow_releases')
        .select('id')
        .eq('manager_approval_status', 'pending');

      const { data: pendingNegotiationApprovals } = await supabase
        .from('negotiations')
        .select('id')
        .eq('manager_id', user?.id)
        .eq('status', 'pending')
        .eq('manager_approval_required', true)
        .is('manager_approved_at', null);

      // Get client satisfaction average
      const { data: satisfactionData } = await supabase
        .from('client_satisfaction')
        .select('overall_rating')
        .eq('manager_id', user?.id)
        .not('overall_rating', 'is', null);

      const avgSatisfaction = satisfactionData?.length 
        ? satisfactionData.reduce((sum, item) => sum + item.overall_rating, 0) / satisfactionData.length
        : 0;

      // Get performance metrics
      const { data: performanceData } = await supabase
        .from('project_performance_metrics')
        .select('performance_score, timeline_variance_percentage, budget_variance_percentage')
        .eq('manager_id', user?.id)
        .order('calculated_at', { ascending: false })
        .limit(10);

      const avgPerformance = performanceData?.length
        ? performanceData.reduce((sum, item) => sum + (item.performance_score || 0), 0) / performanceData.length
        : 0;

      const avgTimelineVariance = performanceData?.length
        ? performanceData.reduce((sum, item) => sum + Math.abs(item.timeline_variance_percentage || 0), 0) / performanceData.length
        : 0;

      const avgBudgetVariance = performanceData?.length
        ? performanceData.reduce((sum, item) => sum + Math.abs(item.budget_variance_percentage || 0), 0) / performanceData.length
        : 0;

      setMetrics({
        totalProjects,
        activeNegotiations: activeNegotiations?.length || 0,
        pendingApprovals: (pendingEscrowApprovals?.length || 0) + (pendingNegotiationApprovals?.length || 0),
        clientSatisfactionAvg: Math.round(avgSatisfaction * 10) / 10,
        performanceScore: Math.round(avgPerformance * 10) / 10,
        onTimeDelivery: Math.max(0, 100 - avgTimelineVariance),
        budgetVariance: avgBudgetVariance,
        communicationScore: Math.min(10, avgSatisfaction * 1.2) // Derived metric
      });

    } catch (error) {
      console.error('Error fetching manager metrics:', error);
    }
  };

  const fetchProjectPerformance = async () => {
    try {
      const { data: performanceData } = await supabase
        .from('project_performance_metrics')
        .select(`
          *,
          project:projects!inner(
            id,
            title,
            status,
            client:profiles!projects_client_id_fkey(full_name),
            designer:profiles!projects_designer_id_fkey(full_name)
          )
        `)
        .eq('manager_id', user?.id)
        .order('calculated_at', { ascending: false })
        .limit(10);

      if (performanceData) {
        const formattedData: ProjectPerformance[] = performanceData.map(item => ({
          id: item.project.id,
          title: item.project.title,
          client_name: item.project.client?.full_name || 'Unknown',
          designer_name: item.project.designer?.full_name || 'Unknown',
          performance_score: item.performance_score || 0,
          performance_grade: item.performance_grade || 'N/A',
          timeline_variance: item.timeline_variance_percentage || 0,
          budget_variance: item.budget_variance_percentage || 0,
          client_satisfaction: 0, // Will be populated separately
          status: item.project.status,
          completion_percentage: 0 // Will be calculated
        }));

        setProjectPerformance(formattedData);
      }
    } catch (error) {
      console.error('Error fetching project performance:', error);
    }
  };

  const fetchActiveNegotiations = async () => {
    try {
      const { data: negotiationsData } = await supabase
        .from('negotiations')
        .select(`
          *,
          project:projects!inner(title),
          initiator:profiles!negotiations_initiated_by_fkey(full_name)
        `)
        .eq('manager_id', user?.id)
        .in('status', ['pending', 'in_progress'])
        .order('deadline', { ascending: true })
        .limit(10);

      if (negotiationsData) {
        const formattedNegotiations: NegotiationSummary[] = negotiationsData.map(item => {
          const deadline = new Date(item.deadline);
          const now = new Date();
          const daysRemaining = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

          return {
            id: item.id,
            title: item.title,
            project_title: item.project.title,
            negotiation_type: item.negotiation_type,
            status: item.status,
            priority: item.priority,
            deadline: item.deadline,
            initiated_by_name: item.initiator?.full_name || 'Unknown',
            days_remaining: daysRemaining
          };
        });

        setNegotiations(formattedNegotiations);
      }
    } catch (error) {
      console.error('Error fetching negotiations:', error);
    }
  };

  const fetchClientFeedback = async () => {
    try {
      const { data: feedbackData } = await supabase
        .from('client_satisfaction')
        .select(`
          *,
          project:projects!inner(title),
          client:profiles!client_satisfaction_client_id_fkey(full_name)
        `)
        .eq('manager_id', user?.id)
        .not('feedback_text', 'is', null)
        .order('completed_at', { ascending: false })
        .limit(5);

      if (feedbackData) {
        const formattedFeedback: ClientFeedback[] = feedbackData.map(item => ({
          id: item.id,
          project_title: item.project.title,
          client_name: item.client?.full_name || 'Unknown',
          overall_rating: item.overall_rating,
          feedback_text: item.feedback_text,
          completed_at: item.completed_at,
          survey_type: item.survey_type
        }));

        setClientFeedback(formattedFeedback);
      }
    } catch (error) {
      console.error('Error fetching client feedback:', error);
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading advanced dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Advanced Manager Dashboard</h1>
          <p className="text-gray-600">Comprehensive project oversight and performance analytics</p>
        </div>
        <Button onClick={fetchManagerData} variant="outline" size="sm">
          <Activity className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Projects</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.totalProjects}</p>
              </div>
              <BarChart3 className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Performance Score</p>
                <p className={`text-2xl font-bold ${getPerformanceColor(metrics.performanceScore)}`}>
                  {metrics.performanceScore}/10
                </p>
              </div>
              <Award className="h-6 w-6 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Client Satisfaction</p>
                <p className={`text-2xl font-bold ${getPerformanceColor(metrics.clientSatisfactionAvg)}`}>
                  {metrics.clientSatisfactionAvg}/10
                </p>
              </div>
              <Star className="h-6 w-6 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending Actions</p>
                <p className="text-2xl font-bold text-orange-600">{metrics.pendingApprovals}</p>
              </div>
              <AlertTriangle className="h-6 w-6 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Project Performance</TabsTrigger>
          <TabsTrigger value="negotiations">Active Negotiations</TabsTrigger>
          <TabsTrigger value="feedback">Client Feedback</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-500" />
                Project Performance Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              {projectPerformance.length === 0 ? (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h4 className="font-medium text-gray-900 mb-2">No Performance Data</h4>
                  <p className="text-sm text-gray-600">Performance metrics will appear as projects progress</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {projectPerformance.map((project) => (
                    <div key={project.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{project.title}</h4>
                          <p className="text-sm text-gray-600">
                            Client: {project.client_name} • Designer: {project.designer_name}
                          </p>
                        </div>
                        <Badge className={`${getPerformanceColor(project.performance_score)} bg-opacity-10`}>
                          Grade: {project.performance_grade}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <Target className="h-4 w-4 text-gray-400" />
                          <span>Performance: {project.performance_score}/10</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>Timeline: {project.timeline_variance > 0 ? '+' : ''}{project.timeline_variance}%</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <span>Budget: {project.budget_variance > 0 ? '+' : ''}{project.budget_variance}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="negotiations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Handshake className="h-5 w-5 text-green-500" />
                Active Negotiations ({metrics.activeNegotiations})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {negotiations.length === 0 ? (
                <div className="text-center py-8">
                  <Handshake className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h4 className="font-medium text-gray-900 mb-2">No Active Negotiations</h4>
                  <p className="text-sm text-gray-600">All negotiations are up to date</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {negotiations.map((negotiation) => (
                    <div key={negotiation.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{negotiation.title}</h4>
                          <p className="text-sm text-gray-600">
                            Project: {negotiation.project_title} • Initiated by: {negotiation.initiated_by_name}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getPriorityColor(negotiation.priority)}>
                            {negotiation.priority}
                          </Badge>
                          <Badge variant="outline">
                            {negotiation.negotiation_type.replace('_', ' ')}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            {negotiation.days_remaining > 0 
                              ? `${negotiation.days_remaining} days remaining`
                              : `${Math.abs(negotiation.days_remaining)} days overdue`
                            }
                          </span>
                          <Badge variant={negotiation.status === 'pending' ? 'destructive' : 'default'}>
                            {negotiation.status}
                          </Badge>
                        </div>
                        <Button variant="outline" size="sm">
                          Review Negotiation
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="feedback" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ThumbsUp className="h-5 w-5 text-blue-500" />
                Recent Client Feedback
              </CardTitle>
            </CardHeader>
            <CardContent>
              {clientFeedback.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h4 className="font-medium text-gray-900 mb-2">No Recent Feedback</h4>
                  <p className="text-sm text-gray-600">Client feedback will appear here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {clientFeedback.map((feedback) => (
                    <div key={feedback.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{feedback.project_title}</h4>
                          <p className="text-sm text-gray-600">
                            {feedback.client_name} • {formatDate(feedback.completed_at)}
                          </p>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="font-medium">{feedback.overall_rating}/10</span>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-700 italic">"{feedback.feedback_text}"</p>
                      
                      <div className="mt-2">
                        <Badge variant="outline" className="text-xs">
                          {feedback.survey_type.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">On-Time Delivery</span>
                    <span className="font-medium">{metrics.onTimeDelivery.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Budget Variance</span>
                    <span className="font-medium">{metrics.budgetVariance.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Communication Score</span>
                    <span className="font-medium">{metrics.communicationScore.toFixed(1)}/10</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Active Negotiations</span>
                    <span className="font-medium">{metrics.activeNegotiations}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button className="w-full justify-start" variant="outline">
                    <Users className="h-4 w-4 mr-2" />
                    View All Projects
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Handshake className="h-4 w-4 mr-2" />
                    Start New Negotiation
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Generate Report
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Send Client Survey
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
