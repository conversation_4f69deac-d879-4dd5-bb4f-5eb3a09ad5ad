"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Briefcase,
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  Settings,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  Star,
  MessageSquare,
  Plus,
  UserPlus,
  Target,
  TrendingUp
} from "lucide-react";

interface ProjectAssignment {
  id: string;
  project_id: string;
  manager_id: string;
  status: string;
  priority: string;
  assigned_at: string;
  notes: string;
  completion_percentage: number;
  estimated_completion: string | null;
  project: {
    title: string;
    description: string;
    status: string;
    budget: number;
    created_at: string;
    quality_status: string;
    client: {
      full_name: string;
      email: string;
    };
    designer: {
      full_name: string;
      email: string;
    };
  };
}

interface AssignmentStats {
  total: number;
  active: number;
  completed: number;
  overdue: number;
  totalBudget: number;
  averageCompletion: number;
}

export default function ManagerAssignmentsPage() {
  const { user, profile } = useOptimizedAuth();
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('active');
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState<AssignmentStats>({
    total: 0,
    active: 0,
    completed: 0,
    overdue: 0,
    totalBudget: 0,
    averageCompletion: 0
  });

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchAssignments();
    }
  }, [user, profile, filter]);

  const fetchAssignments = async () => {
    try {
      let query = supabase
        .from('project_assignments')
        .select(`
          *,
          project:projects(
            title, description, status, budget, created_at, quality_status, client_id, designer_id,
            client:profiles!projects_client_id_fkey(full_name, email),
            designer:profiles!projects_designer_id_fkey(full_name, email)
          )
        `)
        .eq('manager_id', user?.id);

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query
        .order('assigned_at', { ascending: false });

      if (error) throw error;
      
      const assignmentData = data || [];
      setAssignments(assignmentData);

      // Calculate stats
      const total = assignmentData.length;
      const active = assignmentData.filter(a => a.status === 'active').length;
      const completed = assignmentData.filter(a => a.status === 'completed').length;
      const overdue = assignmentData.filter(a => {
        if (!a.estimated_completion) return false;
        return new Date(a.estimated_completion) < new Date() && a.status === 'active';
      }).length;
      const totalBudget = assignmentData.reduce((sum, a) => sum + (a.project?.budget || 0), 0);
      const averageCompletion = total > 0 
        ? assignmentData.reduce((sum, a) => sum + a.completion_percentage, 0) / total 
        : 0;

      setStats({
        total,
        active,
        completed,
        overdue,
        totalBudget,
        averageCompletion
      });

    } catch (error) {
      console.error('Error fetching assignments:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateAssignmentStatus = async (assignmentId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('project_assignments')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', assignmentId);

      if (error) throw error;

      // Log manager activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: assignments.find(a => a.id === assignmentId)?.project_id,
        activity_type: 'assignment_update',
        description: `Updated assignment status to ${newStatus}`,
        outcome: newStatus
      });

      fetchAssignments();
    } catch (error) {
      console.error('Error updating assignment:', error);
    }
  };

  const updateCompletionPercentage = async (assignmentId: string, percentage: number) => {
    try {
      const { error } = await supabase
        .from('project_assignments')
        .update({ 
          completion_percentage: percentage,
          updated_at: new Date().toISOString()
        })
        .eq('id', assignmentId);

      if (error) throw error;
      fetchAssignments();
    } catch (error) {
      console.error('Error updating completion:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Briefcase className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'paused':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'transferred':
        return <Users className="h-4 w-4 text-purple-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'paused':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'transferred':
        return `${baseClasses} bg-purple-100 text-purple-800 border border-purple-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 50) return 'text-blue-600';
    if (percentage >= 25) return 'text-amber-600';
    return 'text-red-600';
  };

  const filteredAssignments = assignments.filter(assignment =>
    assignment.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.project?.client?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.project?.designer?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Project Oversight</h1>
          <p className="text-gray-600 mt-2">Monitor and oversee all projects across the platform</p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={fetchAssignments}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Assignments</p>
              <p className="text-2xl font-bold text-blue-600">{stats.active}</p>
              <p className="text-xs text-gray-500 mt-1">
                {stats.overdue > 0 && `${stats.overdue} overdue`}
              </p>
            </div>
            <Briefcase className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
              <p className="text-xs text-gray-500 mt-1">
                {stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0}% completion rate
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Budget</p>
              <p className="text-2xl font-bold text-purple-600">${stats.totalBudget.toLocaleString()}</p>
              <p className="text-xs text-gray-500 mt-1">
                Avg: ${stats.total > 0 ? Math.round(stats.totalBudget / stats.total).toLocaleString() : '0'}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Progress</p>
              <p className="text-2xl font-bold text-orange-600">{Math.round(stats.averageCompletion)}%</p>
              <p className="text-xs text-gray-500 mt-1">
                Across all assignments
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Manager Oversight Tools</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/projects'}
          >
            <Briefcase className="h-5 w-5" />
            View All Projects
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/escrow'}
          >
            <DollarSign className="h-5 w-5" />
            Escrow Management
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/assignments/reports'}
          >
            <Target className="h-5 w-5" />
            Assignment Reports
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = '/manager/assignments/analytics'}
          >
            <TrendingUp className="h-5 w-5" />
            Performance Analytics
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="active">Active Assignments</option>
              <option value="completed">Completed</option>
              <option value="paused">Paused</option>
              <option value="transferred">Transferred</option>
              <option value="all">All Assignments</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects, clients, or designers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Assignments List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Project Assignments</h2>
          <p className="text-gray-600 mt-1">Manage and track your assigned projects</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredAssignments.length === 0 ? (
            <div className="p-8 text-center">
              <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No project assignments found</p>
              <Button
                onClick={() => window.location.href = '/manager/assignments/new'}
                className="mt-4 flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create First Assignment
              </Button>
            </div>
          ) : (
            filteredAssignments.map((assignment) => (
              <div key={assignment.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {getStatusIcon(assignment.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {assignment.project?.title || 'Untitled Project'}
                      </h3>
                      <span className={getStatusBadge(assignment.status)}>
                        {assignment.status.toUpperCase()}
                      </span>
                      <span className={getPriorityBadge(assignment.priority)}>
                        {assignment.priority.toUpperCase()}
                      </span>
                    </div>

                    <p className="text-gray-600 mb-3">{assignment.project?.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Client:</span> {assignment.project?.client?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Designer:</span> {assignment.project?.designer?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        <span className="font-medium">Budget:</span> ${assignment.project?.budget?.toLocaleString()}
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">Assigned:</span> {new Date(assignment.assigned_at).toLocaleDateString()}
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-700">Progress</span>
                        <span className={`text-sm font-semibold ${getCompletionColor(assignment.completion_percentage)}`}>
                          {assignment.completion_percentage}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${assignment.completion_percentage}%` }}
                        ></div>
                      </div>
                    </div>

                    {assignment.notes && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-3">
                        <p className="text-sm text-gray-700">
                          <span className="font-medium">Notes:</span> {assignment.notes}
                        </p>
                      </div>
                    )}

                    {assignment.estimated_completion && (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Clock className="h-4 w-4" />
                        <span>Estimated completion: {new Date(assignment.estimated_completion).toLocaleDateString()}</span>
                        {new Date(assignment.estimated_completion) < new Date() && assignment.status === 'active' && (
                          <span className="text-red-600 font-medium">(Overdue)</span>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/assignments/${assignment.id}`}
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/projects/${assignment.project_id}`}
                    >
                      <MessageSquare className="h-4 w-4" />
                      Manage Project
                    </Button>

                    {assignment.status === 'active' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/manager/assignments/${assignment.id}/edit`}
                      >
                        <Settings className="h-4 w-4" />
                        Edit Assignment
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Guidelines */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <Target className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Assignment Management Best Practices</h3>
            <div className="text-blue-800 space-y-2">
              <p>• <strong>Clear Expectations:</strong> Set clear goals and deadlines for each assignment</p>
              <p>• <strong>Regular Updates:</strong> Track progress and update completion percentages regularly</p>
              <p>• <strong>Priority Management:</strong> Use priority levels to focus on critical projects</p>
              <p>• <strong>Communication:</strong> Maintain regular contact with clients and designers</p>
              <p>• <strong>Documentation:</strong> Keep detailed notes for each assignment</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
