-- SIMPLE MESSAGING SYSTEM DIAGNOSTIC
-- Copy and paste this entire script into Supabase SQL Editor

-- 1. Check which messaging tables exist
SELECT 
  'TABLE_EXISTS' as check_type,
  table_name,
  CASE 
    WHEN table_name = 'conversations' THEN 'New Unified System'
    WHEN table_name = 'conversation_participants' THEN 'New Unified System'
    WHEN table_name = 'conversation_messages' THEN 'New Unified System'
    WHEN table_name = 'project_messages' THEN 'Project-Based System'
    WHEN table_name = 'messages' THEN 'Old Legacy System'
    ELSE 'Other'
  END as system_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('conversations', 'conversation_participants', 'conversation_messages', 'project_messages', 'messages')
ORDER BY system_type, table_name;

-- 2. Count records in conversations table
SELECT 
  'CONVERSATIONS_COUNT' as check_type,
  COUNT(*) as record_count,
  'conversations' as table_name
FROM conversations;

-- 3. Count records in conversation_participants (if exists)
SELECT 
  'PARTICIPANTS_COUNT' as check_type,
  COUNT(*) as record_count,
  'conversation_participants' as table_name
FROM conversation_participants
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_name = 'conversation_participants'
);

-- 4. Count records in conversation_messages (if exists)
SELECT 
  'CONV_MESSAGES_COUNT' as check_type,
  COUNT(*) as record_count,
  'conversation_messages' as table_name
FROM conversation_messages
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_name = 'conversation_messages'
);

-- 5. Count records in project_messages (if exists)
SELECT 
  'PROJECT_MESSAGES_COUNT' as check_type,
  COALESCE(COUNT(*), 0) as record_count,
  'project_messages' as table_name
FROM project_messages
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_name = 'project_messages'
);

-- 6. Count records in old messages table (if exists)
SELECT 
  'OLD_MESSAGES_COUNT' as check_type,
  COALESCE(COUNT(*), 0) as record_count,
  'messages' as table_name
FROM messages
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_name = 'messages'
);

-- 7. Check for old schema columns in conversations
SELECT 
  'OLD_SCHEMA_CHECK' as check_type,
  column_name,
  'OLD_COLUMN_FOUND' as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'conversations'
AND column_name IN ('participant_one_id', 'participant_two_id', 'client_id', 'designer_id');

-- 8. Sample conversations data (first 3 records)
SELECT 
  'SAMPLE_CONVERSATIONS' as check_type,
  id,
  type,
  title,
  project_id,
  created_at
FROM conversations 
ORDER BY created_at DESC
LIMIT 3;

-- 9. Sample conversation participants (first 5 records)
SELECT 
  'SAMPLE_PARTICIPANTS' as check_type,
  conversation_id,
  user_id,
  role,
  joined_at
FROM conversation_participants
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_name = 'conversation_participants'
)
ORDER BY joined_at DESC
LIMIT 5;
