# Database Migrations

This directory contains SQL migration files for the Seniors Archi Firm application.

## Running Migrations

To apply these migrations to your Supabase database, follow these steps:

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Create a new query
4. Copy and paste the contents of the migration file you want to run
5. Execute the query

## Migration Files

- `create_invite_system_tables.sql`: Creates tables for the invitation system
- `create_messaging_tables.sql`: Creates tables for the messaging system
- `create_payment_methods_table.sql`: Creates tables for payment methods
- `create_tracking_table.sql`: Creates tables for tracking
- `add_stripe_customer_id.sql`: Adds Stripe customer ID to profiles

## Messaging System Tables

The messaging system uses the following tables:

1. `conversations`: Stores conversations between designers and clients
   - `id`: UUID (primary key)
   - `client_id`: UUID (foreign key to profiles)
   - `designer_id`: UUID (foreign key to profiles)
   - `created_at`: Timestamp
   - `updated_at`: Timestamp

2. `messages`: Stores individual messages
   - `id`: UUID (primary key)
   - `conversation_id`: UUID (foreign key to conversations)
   - `sender_id`: UUID (foreign key to profiles)
   - `content`: Text
   - `is_read`: Boolean
   - `created_at`: Timestamp
   - `updated_at`: Timestamp

3. `attachments`: Stores file attachments for messages
   - `id`: UUID (primary key)
   - `message_id`: UUID (foreign key to messages)
   - `file_url`: Text
   - `file_name`: Text
   - `file_type`: Text
   - `file_size`: Integer
   - `created_at`: Timestamp

## Important Notes

- Make sure to run migrations in the correct order
- Always back up your database before running migrations
- Test migrations in a development environment before applying to production
