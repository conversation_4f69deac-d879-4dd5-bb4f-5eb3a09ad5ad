import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

/**
 * API route for fetching communication history for tracking requests (public access)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ trackingNumber: string }> }
) {
  try {
    const { trackingNumber } = await params;

    // First verify the tracking request exists and get its ID
    const { data: trackingRequest, error: trackingError } = await supabaseServerClient
      .from('tracking_requests')
      .select('id, tracking_number')
      .eq('tracking_number', trackingNumber)
      .single();

    if (trackingError || !trackingRequest) {
      return NextResponse.json({ error: 'Tracking request not found' }, { status: 404 });
    }

    // Fetch communication history (only non-internal communications)
    const { data: communications, error } = await supabaseServerClient
      .from('tracking_communications')
      .select(`
        id,
        communication_type,
        subject,
        content,
        sent_at,
        status
      `)
      .eq('tracking_request_id', trackingRequest.id)
      .neq('communication_type', 'internal_note')
      .order('sent_at', { ascending: false });

    if (error) {
      console.error('Error fetching communications:', error);
      return NextResponse.json({ error: 'Failed to fetch communications' }, { status: 500 });
    }

    return NextResponse.json({ 
      communications: communications || [],
      tracking_number: trackingRequest.tracking_number
    });

  } catch (error) {
    console.error('Error in GET /api/tracking/[trackingNumber]/communications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
