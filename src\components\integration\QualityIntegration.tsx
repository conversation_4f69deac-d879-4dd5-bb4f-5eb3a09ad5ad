'use client';

import React, { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  Star, 
  Eye,
  MessageSquare,
  FileText,
  RefreshCw
} from 'lucide-react';

interface QualityReview {
  id: string;
  project_id: string;
  status: string;
  overall_score: number | null;
  feedback: string | null;
  revision_count: number;
  sla_deadline: string | null;
  created_at: string;
  reviewed_at: string | null;
  projects: {
    title: string;
    status: string;
  };
  reviewer: {
    full_name: string;
  } | null;
}

interface QualityIntegrationProps {
  role: 'designer' | 'client' | 'admin';
  userId?: string;
  projectId?: string;
}

export default function QualityIntegration({ role, userId, projectId }: QualityIntegrationProps) {
  const { user } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    pending: 0,
    approved: 0,
    needs_revision: 0,
    average_score: 0
  });

  useEffect(() => {
    if (user) {
      fetchQualityReviews();
    }
  }, [user, userId, projectId]);

  const fetchQualityReviews = async () => {
    try {
      let query = supabase
        .from('quality_reviews_new')
        .select(`
          id,
          project_id,
          status,
          overall_score,
          feedback,
          revision_count,
          sla_deadline,
          created_at,
          reviewed_at,
          projects:project_id (
            title,
            status
          ),
          reviewer:reviewer_id (
            full_name
          )
        `);

      // Filter based on role and context
      if (role === 'designer') {
        query = query.eq('designer_id', userId || user?.id);
      } else if (role === 'client' && projectId) {
        query = query.eq('project_id', projectId);
      } else if (role === 'admin') {
        // Admin sees all reviews
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      const reviewData = data || [];
      setReviews(reviewData);

      // Calculate stats
      const pending = reviewData.filter(r => r.status === 'pending').length;
      const approved = reviewData.filter(r => r.status === 'approved').length;
      const needs_revision = reviewData.filter(r => r.status === 'needs_revision').length;
      const scores = reviewData.filter(r => r.overall_score !== null).map(r => r.overall_score!);
      const average_score = scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;

      setStats({ pending, approved, needs_revision, average_score });
    } catch (error) {
      console.error('Error fetching quality reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'in_review':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'needs_revision':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'in_review':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'needs_revision':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const isOverdue = (slaDeadline: string | null) => {
    if (!slaDeadline) return false;
    return new Date(slaDeadline) < new Date();
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-brown-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Quality Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-amber-600">{stats.pending}</p>
              </div>
              <Clock className="h-6 w-6 text-amber-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
              </div>
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Revisions</p>
                <p className="text-2xl font-bold text-red-600">{stats.needs_revision}</p>
              </div>
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Score</p>
                <p className="text-2xl font-bold text-blue-600">{stats.average_score.toFixed(1)}</p>
              </div>
              <Star className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Quality Reviews */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-brown-600" />
              Quality Reviews
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchQualityReviews}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {reviews.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No quality reviews found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {reviews.map((review) => (
                <div
                  key={review.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(review.status)}
                      <h4 className="font-semibold text-gray-900">
                        {review.projects?.title || 'Untitled Project'}
                      </h4>
                      <Badge className={`${getStatusColor(review.status)} border`}>
                        {review.status.replace('_', ' ').toUpperCase()}
                      </Badge>
                      {isOverdue(review.sla_deadline) && review.status === 'pending' && (
                        <Badge className="bg-red-100 text-red-800 border-red-200">
                          OVERDUE
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      {review.reviewer && (
                        <span>Reviewer: {review.reviewer.full_name}</span>
                      )}
                      {review.overall_score && (
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span>{review.overall_score}/5</span>
                        </div>
                      )}
                      <span>Revisions: {review.revision_count}</span>
                    </div>

                    {review.feedback && (
                      <p className="text-sm text-gray-700 mt-2 line-clamp-2">
                        {review.feedback}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                      className="flex items-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      View
                    </Button>
                    
                    {role === 'designer' && review.status === 'needs_revision' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/projects/${review.project_id}/revise`}
                      >
                        <MessageSquare className="h-4 w-4" />
                        Revise
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
