import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { feeSettingsManager, FeeSettings } from '@/lib/fee-settings';

interface SettingsState {
  feeSettings: FeeSettings | null;
  paymentMethods: string[];
  platformSettings: any;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

/**
 * Real-time settings hook for admin-manager communication
 * Ensures all roles use the latest admin-configured settings
 */
export function useRealtimeSettings() {
  const [state, setState] = useState<SettingsState>({
    feeSettings: null,
    paymentMethods: ['paypal', 'credit_card', 'bank_transfer'],
    platformSettings: null,
    loading: true,
    error: null,
    lastUpdated: null
  });

  // Fetch initial settings
  const fetchSettings = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Get fee settings
      const feeSettings = await feeSettingsManager.getFeeSettings();

      // Get platform settings
      const { data: platformSettings, error: platformError } = await supabase
        .from('platform_settings')
        .select('*')
        .single();

      if (platformError && platformError.code !== 'PGRST116') {
        console.warn('Platform settings not found, using defaults');
      }

      // Get payment methods from admin settings
      const { data: paymentSettings, error: paymentError } = await supabase
        .from('payment_settings')
        .select('payment_methods')
        .single();

      const paymentMethods = paymentSettings?.payment_methods || ['paypal', 'credit_card', 'bank_transfer'];

      setState({
        feeSettings,
        paymentMethods,
        platformSettings: platformSettings || {},
        loading: false,
        error: null,
        lastUpdated: new Date()
      });
    } catch (error) {
      console.error('Error fetching settings:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch settings'
      }));
    }
  }, []);

  // Set up real-time subscriptions
  useEffect(() => {
    fetchSettings();

    // Subscribe to fee settings changes
    const feeSettingsSubscription = supabase
      .channel('fee_settings_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'platform_fee_settings'
        },
        (payload) => {
          console.log('Fee settings changed:', payload);
          // Clear cache and refetch
          feeSettingsManager.clearCache();
          fetchSettings();
        }
      )
      .subscribe();

    // Subscribe to platform settings changes
    const platformSettingsSubscription = supabase
      .channel('platform_settings_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'platform_settings'
        },
        (payload) => {
          console.log('Platform settings changed:', payload);
          fetchSettings();
        }
      )
      .subscribe();

    // Subscribe to payment settings changes
    const paymentSettingsSubscription = supabase
      .channel('payment_settings_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'payment_settings'
        },
        (payload) => {
          console.log('Payment settings changed:', payload);
          fetchSettings();
        }
      )
      .subscribe();

    // Cleanup subscriptions
    return () => {
      supabase.removeChannel(feeSettingsSubscription);
      supabase.removeChannel(platformSettingsSubscription);
      supabase.removeChannel(paymentSettingsSubscription);
    };
  }, [fetchSettings]);

  // Calculate payment breakdown using current settings
  const calculatePaymentBreakdown = useCallback(async (amount: number) => {
    if (!state.feeSettings) {
      throw new Error('Fee settings not loaded');
    }
    return await feeSettingsManager.calculatePaymentBreakdown(amount);
  }, [state.feeSettings]);

  // Get current commission rate
  const getCommissionRate = useCallback(() => {
    return state.feeSettings?.platform_commission_rate || 15;
  }, [state.feeSettings]);

  // Get current processing fee rate
  const getProcessingFeeRate = useCallback(() => {
    return state.feeSettings?.payment_processing_fee || 2.9;
  }, [state.feeSettings]);

  // Get designer payout rate
  const getDesignerPayoutRate = useCallback(() => {
    return state.feeSettings?.designer_payout_rate || 82.1;
  }, [state.feeSettings]);

  // Check if PayPal is enabled
  const isPayPalEnabled = useCallback(() => {
    return state.paymentMethods.includes('paypal');
  }, [state.paymentMethods]);

  // Check if Stripe is enabled
  const isStripeEnabled = useCallback(() => {
    return state.paymentMethods.includes('credit_card');
  }, [state.paymentMethods]);

  // Get minimum project value
  const getMinimumProjectValue = useCallback(() => {
    return state.feeSettings?.minimum_project_value || 100;
  }, [state.feeSettings]);

  // Get minimum payout amount
  const getMinimumPayoutAmount = useCallback(() => {
    return state.feeSettings?.minimum_payout_amount || 50;
  }, [state.feeSettings]);

  // Refresh settings manually
  const refreshSettings = useCallback(() => {
    feeSettingsManager.clearCache();
    fetchSettings();
  }, [fetchSettings]);

  // Validate settings consistency
  const validateSettings = useCallback(() => {
    if (!state.feeSettings) return { valid: false, errors: ['Settings not loaded'] };

    const errors: string[] = [];
    const { platform_commission_rate, payment_processing_fee, designer_payout_rate } = state.feeSettings;

    // Check math adds up to 100%
    const total = platform_commission_rate + payment_processing_fee + designer_payout_rate;
    if (Math.abs(total - 100) > 0.1) {
      errors.push(`Fee percentages don't add up to 100% (current: ${total.toFixed(1)}%)`);
    }

    // Check reasonable ranges
    if (platform_commission_rate < 0 || platform_commission_rate > 50) {
      errors.push('Platform commission rate should be between 0% and 50%');
    }

    if (payment_processing_fee < 0 || payment_processing_fee > 10) {
      errors.push('Payment processing fee should be between 0% and 10%');
    }

    if (designer_payout_rate < 50 || designer_payout_rate > 100) {
      errors.push('Designer payout rate should be between 50% and 100%');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }, [state.feeSettings]);

  return {
    // State
    ...state,
    
    // Actions
    refreshSettings,
    calculatePaymentBreakdown,
    
    // Getters
    getCommissionRate,
    getProcessingFeeRate,
    getDesignerPayoutRate,
    getMinimumProjectValue,
    getMinimumPayoutAmount,
    
    // Checks
    isPayPalEnabled,
    isStripeEnabled,
    validateSettings,
    
    // Computed values
    isReady: !state.loading && !state.error && state.feeSettings !== null,
    hasValidSettings: validateSettings().valid
  };
}

/**
 * Hook for manager-specific settings with fee transparency
 */
export function useManagerSettings() {
  const settings = useRealtimeSettings();

  // Get fee breakdown for display in manager UI
  const getFeeBreakdownDisplay = useCallback((amount: number) => {
    if (!settings.feeSettings) return null;

    const platformFee = (amount * settings.feeSettings.platform_commission_rate) / 100;
    const processingFee = (amount * settings.feeSettings.payment_processing_fee) / 100;
    const designerPayout = amount - platformFee - processingFee;

    return {
      grossAmount: amount,
      platformFee: {
        amount: platformFee,
        percentage: settings.feeSettings.platform_commission_rate
      },
      processingFee: {
        amount: processingFee,
        percentage: settings.feeSettings.payment_processing_fee
      },
      designerPayout: {
        amount: designerPayout,
        percentage: settings.feeSettings.designer_payout_rate
      }
    };
  }, [settings.feeSettings]);

  // Check if manager can approve escrow releases
  const canApproveEscrowReleases = useCallback(() => {
    return settings.platformSettings?.require_manager_approval !== false;
  }, [settings.platformSettings]);

  return {
    ...settings,
    getFeeBreakdownDisplay,
    canApproveEscrowReleases
  };
}
