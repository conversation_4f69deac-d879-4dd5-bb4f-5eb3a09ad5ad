"use client";

import { useState, useEffect, useCallback } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import <PERSON>Viewer from "@/components/ui/FileViewer";
import { getDesignerApplicationFileUrl } from "@/lib/r2-upload";
import {
  User,
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  Filter,
  Eye,
  Briefcase,
  Calendar,
  ExternalLink,
  Star,
  FileText,
  Image,
  Download,
  MessageSquare,
  Phone,
  MapPin,
  Loader2,
  Mail,
  Send,
  Tag,
  Clock,
  Users,
  MoreHorizontal
} from "lucide-react";

interface DesignerApplication {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  location?: string;
  specialization: string;
  experience: string;
  portfolio_url?: string;
  bio: string;
  resume_url?: string;
  portfolio_files?: string[];
  certificates?: string[];
  application_status: 'pending' | 'under_review' | 'interview_scheduled' | 'approved' | 'rejected' | 'withdrawn' | 'on_hold';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  admin_notes?: string;
  tags?: string[];
  interview_scheduled_at?: string;
  interview_notes?: string;
  rejection_reason?: string;
  status_updated_at?: string;
  status_updated_by?: string;
  applied_at: string;
  approved_at?: string;
  rejected_at?: string;
  approved_by?: string;
  rejected_by?: string;
  notes?: string;
  created_user_id?: string;
  created_at: string;
}

interface CommunicationModalProps {
  application: DesignerApplication | null;
  isOpen: boolean;
  onClose: () => void;
  onSent: () => void;
}

const CommunicationModal = ({ application, isOpen, onClose, onSent }: CommunicationModalProps) => {
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [sending, setSending] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);

  useEffect(() => {
    if (isOpen) {
      fetchEmailTemplates();
    }
  }, [isOpen]);

  const fetchEmailTemplates = async () => {
    const { data } = await supabase
      .from('email_templates')
      .select('*')
      .eq('category', 'designer_application')
      .eq('is_active', true);

    setTemplates(data || []);
  };

  const handleTemplateSelect = (template: any) => {
    setSubject(template.subject);
    setContent(template.content);
  };

  const handleSend = async () => {
    if (!application || !subject.trim() || !content.trim()) return;

    setSending(true);
    try {
      const response = await fetch(`/api/admin/applications/${application.id}/communicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subject: subject.trim(),
          content: content.trim(),
          communication_type: 'email'
        })
      });

      if (response.ok) {
        onSent();
        onClose();
        setSubject('');
        setContent('');
      }
    } catch (error) {
      console.error('Error sending communication:', error);
    } finally {
      setSending(false);
    }
  };

  if (!isOpen || !application) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Send Message to {application.full_name}</h3>
          <Button variant="ghost" onClick={onClose}>
            <XCircle className="h-5 w-5" />
          </Button>
        </div>

        {/* Email Templates */}
        {templates.length > 0 && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Quick Templates</label>
            <div className="flex flex-wrap gap-2">
              {templates.map((template) => (
                <Button
                  key={template.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleTemplateSelect(template)}
                >
                  {template.name}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Subject */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Subject</label>
          <input
            type="text"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            className="w-full p-2 border rounded-md"
            placeholder="Email subject..."
          />
        </div>

        {/* Content */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Message</label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={8}
            className="w-full p-2 border rounded-md"
            placeholder="Your message..."
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSend}
            disabled={sending || !subject.trim() || !content.trim()}
          >
            {sending ? 'Sending...' : 'Send Email'}
          </Button>
        </div>
      </div>
    </div>
  );
};

interface InterviewSchedulingModalProps {
  application: DesignerApplication | null;
  isOpen: boolean;
  onClose: () => void;
  onScheduled: () => void;
}

const InterviewSchedulingModal = ({ application, isOpen, onClose, onScheduled }: InterviewSchedulingModalProps) => {
  const [interviewDate, setInterviewDate] = useState('');
  const [interviewTime, setInterviewTime] = useState('');
  const [duration, setDuration] = useState('60');
  const [interviewType, setInterviewType] = useState('video_call');
  const [meetingLink, setMeetingLink] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [address, setAddress] = useState('');
  const [agenda, setAgenda] = useState('');
  const [interviewerName, setInterviewerName] = useState('');
  const [interviewerEmail, setInterviewerEmail] = useState('');
  const [notes, setNotes] = useState('');
  const [scheduling, setScheduling] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      setInterviewDate(tomorrow.toISOString().split('T')[0]);
      setInterviewTime('10:00');
      setDuration('60');
      setInterviewType('video_call');
      setMeetingLink('');
      setPhoneNumber('');
      setAddress('');
      setAgenda('General interview to discuss your background, experience, and fit for our team.');
      setInterviewerName('');
      setInterviewerEmail('');
      setNotes('');
    }
  }, [isOpen]);

  const handleSchedule = async () => {
    if (!application || !interviewDate || !interviewTime) return;

    setScheduling(true);
    try {
      const interviewDateTime = new Date(`${interviewDate}T${interviewTime}`);

      const interviewData = {
        interview_scheduled_at: interviewDateTime.toISOString(),
        interview_type: interviewType,
        interview_duration: parseInt(duration),
        meeting_link: meetingLink,
        phone_number: phoneNumber,
        interview_address: address,
        interview_agenda: agenda,
        interviewer_name: interviewerName,
        interviewer_email: interviewerEmail,
        interview_notes: notes
      };

      const response = await fetch(`/api/admin/applications/${application.id}/schedule-interview`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(interviewData)
      });

      if (response.ok) {
        onScheduled();
        onClose();
      } else {
        throw new Error('Failed to schedule interview');
      }
    } catch (error) {
      console.error('Error scheduling interview:', error);
      alert('Failed to schedule interview. Please try again.');
    } finally {
      setScheduling(false);
    }
  };

  if (!isOpen || !application) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold">Schedule Interview - {application.full_name}</h3>
          <Button variant="ghost" onClick={onClose}>
            <XCircle className="h-5 w-5" />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column - Basic Details */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Interview Details
            </h4>

            {/* Date and Time */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Interview Date *</label>
                <input
                  type="date"
                  value={interviewDate}
                  onChange={(e) => setInterviewDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full p-2 border rounded-md"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Interview Time *</label>
                <input
                  type="time"
                  value={interviewTime}
                  onChange={(e) => setInterviewTime(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  required
                />
              </div>
            </div>

            {/* Duration and Type */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Duration</label>
                <select
                  value={duration}
                  onChange={(e) => setDuration(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="30">30 minutes</option>
                  <option value="60">1 hour</option>
                  <option value="90">1.5 hours</option>
                  <option value="120">2 hours</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Interview Type</label>
                <select
                  value={interviewType}
                  onChange={(e) => setInterviewType(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="video_call">Video Call</option>
                  <option value="phone_call">Phone Call</option>
                  <option value="in_person">In Person</option>
                </select>
              </div>
            </div>

            {/* Meeting Details based on type */}
            {interviewType === 'video_call' && (
              <div>
                <label className="block text-sm font-medium mb-2">Meeting Link</label>
                <input
                  type="url"
                  value={meetingLink}
                  onChange={(e) => setMeetingLink(e.target.value)}
                  placeholder="https://zoom.us/j/... or https://teams.microsoft.com/..."
                  className="w-full p-2 border rounded-md"
                />
              </div>
            )}

            {interviewType === 'phone_call' && (
              <div>
                <label className="block text-sm font-medium mb-2">Phone Number</label>
                <input
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  placeholder="+****************"
                  className="w-full p-2 border rounded-md"
                />
              </div>
            )}

            {interviewType === 'in_person' && (
              <div>
                <label className="block text-sm font-medium mb-2">Address</label>
                <textarea
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  rows={3}
                  placeholder="Office address for in-person interview"
                  className="w-full p-2 border rounded-md"
                />
              </div>
            )}
          </div>

          {/* Right Column - Additional Details */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 flex items-center">
              <User className="h-4 w-4 mr-2" />
              Interviewer & Agenda
            </h4>

            {/* Interviewer Details */}
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Interviewer Name</label>
                <input
                  type="text"
                  value={interviewerName}
                  onChange={(e) => setInterviewerName(e.target.value)}
                  placeholder="Primary interviewer name"
                  className="w-full p-2 border rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Interviewer Email</label>
                <input
                  type="email"
                  value={interviewerEmail}
                  onChange={(e) => setInterviewerEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full p-2 border rounded-md"
                />
              </div>
            </div>

            {/* Agenda */}
            <div>
              <label className="block text-sm font-medium mb-2">Interview Agenda</label>
              <textarea
                value={agenda}
                onChange={(e) => setAgenda(e.target.value)}
                rows={4}
                placeholder="What will be covered in the interview..."
                className="w-full p-2 border rounded-md"
              />
            </div>

            {/* Internal Notes */}
            <div>
              <label className="block text-sm font-medium mb-2">Internal Notes</label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                placeholder="Internal notes (not shared with applicant)"
                className="w-full p-2 border rounded-md"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSchedule}
            disabled={scheduling || !interviewDate || !interviewTime}
            className="bg-purple-600 hover:bg-purple-700"
          >
            {scheduling ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Scheduling...
              </>
            ) : (
              <>
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Interview
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default function DesignerApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<DesignerApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<DesignerApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState<DesignerApplication | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showCommunicationModal, setShowCommunicationModal] = useState(false);
  const [communicationApplication, setCommunicationApplication] = useState<DesignerApplication | null>(null);
  const [showInterviewModal, setShowInterviewModal] = useState(false);
  const [interviewApplication, setInterviewApplication] = useState<DesignerApplication | null>(null);

  // File viewer state
  const [fileViewerOpen, setFileViewerOpen] = useState(false);
  const [currentFile, setCurrentFile] = useState<{
    url: string;
    name: string;
    type: string;
  } | null>(null);

  useEffect(() => {
    fetchApplications();
  }, []);

  useEffect(() => {
    filterApplications();
  }, [applications, searchTerm, statusFilter]);

  const fetchApplications = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('designer_applications')
        .select('*')
        .order('applied_at', { ascending: false });

      if (error) throw error;

      setApplications(data || []);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching applications:', error);
        setError(error.message || 'Failed to load applications');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const filterApplications = useCallback(() => {
    let filtered = applications;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(app =>
        app.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.specialization.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.application_status === statusFilter);
    }

    setFilteredApplications(filtered);
  }, [applications, searchTerm, statusFilter]);

  const handleApprove = async (applicationId: string) => {
    // Prevent double-clicks
    if (processing) {
      console.log('Approval already in progress, ignoring duplicate request');
      return;
    }

    setProcessing(true);
    setError(null);
    setSuccess(null);

    console.log(`Starting approval for application: ${applicationId}`);

    try {
      const response = await fetch(`/api/designer-application/${applicationId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          approved_by: user?.id
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve application');
      }

      setSuccess('Application approved successfully! User account created and welcome email sent.');
      await fetchApplications();

      setTimeout(() => setSuccess(null), 5000);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error approving application:', error);
        setError(error.message || 'Failed to approve application');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!selectedApplication) return;

    setProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/designer-application/${selectedApplication.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          rejected_by: user?.id,
          rejection_reason: rejectionReason
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject application');
      }

      setSuccess('Application rejected successfully. Rejection email sent to applicant.');
      await fetchApplications();

      setShowRejectModal(false);
      setSelectedApplication(null);
      setRejectionReason('');

      setTimeout(() => setSuccess(null), 5000);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error rejecting application:', error);
        setError(error.message || 'Failed to reject application');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'under_review':
        return 'bg-blue-100 text-blue-800';
      case 'interview_scheduled':
        return 'bg-purple-100 text-purple-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'on_hold':
        return 'bg-gray-100 text-gray-800';
      case 'withdrawn':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'normal':
        return 'bg-blue-100 text-blue-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const openRejectModal = (application: DesignerApplication) => {
    setSelectedApplication(application);
    setShowRejectModal(true);
  };

  const handleCommunicate = (application: DesignerApplication) => {
    setCommunicationApplication(application);
    setShowCommunicationModal(true);
  };

  const handleScheduleInterview = (application: DesignerApplication) => {
    setInterviewApplication(application);
    setShowInterviewModal(true);
  };

  const handleStatusUpdate = async (applicationId: string, newStatus: string) => {
    setProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await supabase
        .from('designer_applications')
        .update({
          application_status: newStatus,
          status_updated_by: user?.id,
          status_updated_at: new Date().toISOString()
        })
        .eq('id', applicationId);

      if (error) throw error;

      setSuccess(`Application status updated to ${newStatus.replace('_', ' ')}`);
      await fetchApplications();

      setTimeout(() => setSuccess(null), 5000);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error updating status:', error);
        setError(error.message || 'Failed to update status');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setProcessing(false);
    }
  };

  const handleViewFile = (fileKey: string, fileName: string) => {
    const fileUrl = getDesignerApplicationFileUrl(fileKey);
    const fileType = getFileType(fileName);

    setCurrentFile({
      url: fileUrl,
      name: fileName,
      type: fileType
    });
    setFileViewerOpen(true);
  };

  const getFileType = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'application/octet-stream';
    }
  };

  if (loading && applications.length === 0) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Loader2 className="animate-spin h-12 w-12 text-primary mx-auto mb-4" />
          <p className="text-gray-500">Loading designer applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <div className="flex items-center">
            <Link href="/admin/designers" className="mr-4">
              <Button variant="ghost" className="p-0 h-auto">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Designer Applications</h1>
          </div>
          <p className="text-gray-500 mt-2">Review and manage designer applications</p>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <input
                type="text"
                id="search"
                placeholder="Search by name, email, or specialization"
                className="w-full px-4 py-2 border rounded-md pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              className="w-full px-4 py-2 border rounded-md"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Applications</option>
              <option value="pending">Pending Review</option>
              <option value="under_review">Under Review</option>
              <option value="interview_scheduled">Interview Scheduled</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="on_hold">On Hold</option>
              <option value="withdrawn">Withdrawn</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button
              variant="outline"
              onClick={fetchApplications}
              className="flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        <div className="mt-4">
          <p className="text-sm text-gray-500">
            Showing {filteredApplications.length} applications
          </p>
        </div>
      </div>

      {/* Applications List */}
      {filteredApplications.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <User className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-medium mb-2">No Applications Found</h2>
          <p className="text-gray-500 mb-6">
            There are no designer applications matching your filter criteria.
          </p>
          <Button onClick={() => setStatusFilter('all')}>
            View All Applications
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredApplications.map((application) => (
            <div key={application.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6 border-b">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex items-center mb-4 md:mb-0">
                    <div className="flex-shrink-0 h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                      <User className="h-6 w-6 text-gray-400" />
                    </div>
                    <div>
                      <h2 className="text-lg font-medium">{application.full_name}</h2>
                      <p className="text-sm text-gray-500">{application.email}</p>
                    </div>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(application.application_status)}`}>
                      {application.application_status.replace('_', ' ').charAt(0).toUpperCase() + application.application_status.replace('_', ' ').slice(1)}
                    </span>
                    {application.priority && (
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(application.priority)}`}>
                        {application.priority.toUpperCase()}
                      </span>
                    )}
                    <span className="text-sm text-gray-500">
                      Applied on {formatDate(application.applied_at || application.created_at)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  <div className="flex items-start">
                    <Briefcase className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Specialization</p>
                      <p className="font-medium">{application.specialization}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Experience</p>
                      <p className="font-medium">{application.experience}</p>
                    </div>
                  </div>

                  {application.phone && (
                    <div className="flex items-start">
                      <Phone className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="font-medium">{application.phone}</p>
                      </div>
                    </div>
                  )}

                  {application.location && (
                    <div className="flex items-start">
                      <MapPin className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Location</p>
                        <p className="font-medium">{application.location}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Bio */}
                <div className="mb-6">
                  <h3 className="text-md font-medium mb-2 flex items-center">
                    <MessageSquare className="h-4 w-4 text-gray-400 mr-2" />
                    Bio
                  </h3>
                  <p className="text-gray-700 text-sm leading-relaxed">{application.bio}</p>
                </div>

                {/* Portfolio URL */}
                {application.portfolio_url && (
                  <div className="mb-6">
                    <h3 className="text-md font-medium mb-2 flex items-center">
                      <ExternalLink className="h-4 w-4 text-gray-400 mr-2" />
                      Portfolio
                    </h3>
                    <a
                      href={application.portfolio_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline font-medium"
                    >
                      View Online Portfolio
                    </a>
                  </div>
                )}

                {/* Files */}
                <div className="mb-6">
                  <h3 className="text-md font-medium mb-3 flex items-center">
                    <FileText className="h-4 w-4 text-gray-400 mr-2" />
                    Uploaded Files
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Resume */}
                    {application.resume_url && (
                      <div className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FileText className="h-5 w-5 text-red-600 mr-2" />
                            <span className="text-sm font-medium">Resume</span>
                          </div>
                          <button
                            onClick={() => handleViewFile(application.resume_url, 'Resume.pdf')}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Portfolio Files */}
                    {application.portfolio_files && application.portfolio_files.length > 0 && (
                      application.portfolio_files.map((file, index) => {
                        const fileName = file.split('/').pop() || `Portfolio-${index + 1}`;
                        return (
                          <div key={index} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <Image className="h-5 w-5 text-blue-600 mr-2" />
                                <span className="text-sm font-medium">Portfolio {index + 1}</span>
                              </div>
                              <button
                                onClick={() => handleViewFile(file, fileName)}
                                className="text-blue-600 hover:text-blue-700"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>

                {/* Admin Notes */}
                {application.admin_notes && (
                  <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="text-md font-medium mb-2 text-blue-800 flex items-center">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Admin Notes
                    </h3>
                    <p className="text-blue-700 text-sm">{application.admin_notes}</p>
                  </div>
                )}

                {/* Interview Information */}
                {application.interview_scheduled_at && (
                  <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                    <h3 className="text-md font-medium mb-2 text-purple-800 flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      Interview Scheduled
                    </h3>
                    <p className="text-purple-700 text-sm">
                      {formatDate(application.interview_scheduled_at)}
                    </p>
                    {application.interview_notes && (
                      <p className="text-purple-700 text-sm mt-2">{application.interview_notes}</p>
                    )}
                  </div>
                )}

                {/* Rejection Reason */}
                {application.application_status === 'rejected' && application.rejection_reason && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 className="text-md font-medium mb-2 text-red-800">Rejection Reason</h3>
                    <p className="text-red-700 text-sm">{application.rejection_reason}</p>
                  </div>
                )}

                <div className="flex justify-end space-x-3">
                  {/* View Full Application Button */}
                  <Link href={`/admin/designers/applications/${application.id}`}>
                    <Button
                      variant="outline"
                      className="flex items-center"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </Link>

                  {/* Communication Button - Always Available */}
                  <Button
                    variant="outline"
                    className="flex items-center"
                    onClick={() => handleCommunicate(application)}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Message
                  </Button>

                  {/* Status-specific Actions */}
                  {application.application_status === 'pending' && (
                    <>
                      <Button
                        variant="outline"
                        className="flex items-center text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300"
                        onClick={() => handleStatusUpdate(application.id, 'under_review')}
                        disabled={processing}
                      >
                        <Clock className="h-4 w-4 mr-2" />
                        Under Review
                      </Button>

                      <Button
                        variant="outline"
                        className="flex items-center text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                        onClick={() => openRejectModal(application)}
                        disabled={processing}
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </Button>

                      <Button
                        className="flex items-center"
                        onClick={() => handleApprove(application.id)}
                        disabled={processing}
                      >
                        {processing ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <CheckCircle className="h-4 w-4 mr-2" />
                        )}
                        Approve
                      </Button>
                    </>
                  )}

                  {application.application_status === 'under_review' && (
                    <>
                      <Button
                        variant="outline"
                        className="flex items-center text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300"
                        onClick={() => handleScheduleInterview(application)}
                        disabled={processing}
                      >
                        <Calendar className="h-4 w-4 mr-2" />
                        Schedule Interview
                      </Button>

                      <Button
                        className="flex items-center"
                        onClick={() => handleApprove(application.id)}
                        disabled={processing}
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                    </>
                  )}

                  {application.application_status === 'interview_scheduled' && (
                    <Button
                      className="flex items-center"
                      onClick={() => handleApprove(application.id)}
                      disabled={processing}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Reject Application</h2>
            </div>

            <div className="p-6">
              <p className="text-gray-600 mb-4">
                Are you sure you want to reject {selectedApplication.full_name}'s application?
              </p>

              <div>
                <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700 mb-2">
                  Rejection Reason (Optional)
                </label>
                <textarea
                  id="rejectionReason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  placeholder="Provide feedback to help the applicant improve..."
                />
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowRejectModal(false);
                  setSelectedApplication(null);
                  setRejectionReason('');
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleReject}
                disabled={processing}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {processing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Rejecting...
                  </>
                ) : (
                  'Reject Application'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Communication Modal */}
      <CommunicationModal
        application={communicationApplication}
        isOpen={showCommunicationModal}
        onClose={() => {
          setShowCommunicationModal(false);
          setCommunicationApplication(null);
        }}
        onSent={() => {
          fetchApplications();
          setSuccess('Message sent successfully!');
          setTimeout(() => setSuccess(null), 5000);
        }}
      />

      {/* Interview Scheduling Modal */}
      <InterviewSchedulingModal
        application={interviewApplication}
        isOpen={showInterviewModal}
        onClose={() => {
          setShowInterviewModal(false);
          setInterviewApplication(null);
        }}
        onScheduled={() => {
          fetchApplications();
          setSuccess('Interview scheduled successfully! Invitation email sent to applicant.');
          setTimeout(() => setSuccess(null), 5000);
        }}
      />

      {/* File Viewer Modal */}
      {currentFile && (
        <FileViewer
          isOpen={fileViewerOpen}
          onClose={() => {
            setFileViewerOpen(false);
            setCurrentFile(null);
          }}
          fileUrl={currentFile.url}
          fileName={currentFile.name}
          fileType={currentFile.type}
        />
      )}
    </div>
  );
}
