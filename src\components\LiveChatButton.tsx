"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MessageSquare, X, Send, Smile, User, Clock, CheckCircle } from "lucide-react";
import { supabase, supabaseLiveChat } from "@/lib/supabase";

// Simple ID generator to avoid adding nanoid dependency
function generateSessionId() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Safe hook to use auth context - returns null if not available
function useSafeAuth() {
  const [user, setUser] = useState<any>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Set client-side flag
    setIsClient(true);

    // Only run on client side
    if (typeof window === 'undefined') return;

    // Try to get current user from Supabase directly
    const getCurrentUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
      } catch (error) {
        console.log('No auth session available');
        setUser(null);
      }
    };

    getCurrentUser();

    // Listen for auth changes
    const { data: { subscription } } = supabaseLiveChat.auth.onAuthStateChange((event, session) => {
      setUser(session?.user || null);
    });

    return () => subscription.unsubscribe();
  }, []);

  return { user, isClient };
}

interface ChatMessage {
  id: string;
  content: string;
  sender_type: 'visitor' | 'admin' | 'system';
  created_at: string;
  is_read: boolean;
  message_type: 'text' | 'system';
}

interface ChatSession {
  id: string;
  session_id: string;
  status: 'waiting' | 'active' | 'ended' | 'abandoned';
  admin_joined_at?: string;
  visitor_name?: string;
  visitor_email?: string;
}

const LiveChatButton = () => {
  const { user, isClient } = useSafeAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [chatSession, setChatSession] = useState<ChatSession | null>(null);
  const [sessionId] = useState(() => generateSessionId()); // Unique session ID for anonymous users
  const [adminOnline, setAdminOnline] = useState(false);
  const [visitorInfo, setVisitorInfo] = useState({ name: "", email: "" });
  const [showVisitorForm, setShowVisitorForm] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const realtimeChannelRef = useRef<any>(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Check admin online status
  const checkAdminStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/live-chat/admin-status');
      if (response.ok) {
        const data = await response.json();
        setAdminOnline(data.is_online);
      } else {
        // If API fails, assume admin is offline
        setAdminOnline(false);
      }
    } catch (error) {
      console.warn('Live chat system not available:', error);
      setAdminOnline(false);
    }
  }, []);

  // Initialize chat session
  const initializeChatSession = useCallback(async () => {
    if (chatSession || typeof window === 'undefined') return chatSession;

    setIsConnecting(true);
    try {
      // Check if user is authenticated
      const userId = user?.id || null;

      // Create new chat session via API
      const response = await fetch('/api/live-chat/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: sessionId,
          user_id: userId,
          visitor_name: userId ? null : visitorInfo.name || null,
          visitor_email: userId ? null : visitorInfo.email || null,
          user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
          referrer_url: typeof document !== 'undefined' ? document.referrer : '',
          current_page: typeof window !== 'undefined' ? window.location.href : ''
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create chat session');
      }

      const result = await response.json();
      const session = result.data;

      setChatSession(session);
      setAdminOnline(result.admin_online);

      // Fetch initial messages
      const messagesResponse = await fetch(`/api/live-chat/messages?session_id=${session.id}`);
      if (messagesResponse.ok) {
        const messagesResult = await messagesResponse.json();
        setMessages(messagesResult.data || []);
      }

      // Send email notification to admin if offline
      if (!result.admin_online) {
        await fetch('/api/live-chat/notify-admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sessionId: session.id,
            type: 'new_chat',
            visitorInfo: userId ? {
              name: user?.user_metadata?.full_name || user?.email || 'Authenticated User',
              email: user?.email || 'No email'
            } : visitorInfo
          })
        });
      }

      return session;
    } catch (error) {
      console.error('Error initializing chat session:', error);
      return null;
    } finally {
      setIsConnecting(false);
    }
  }, [sessionId, user, visitorInfo, chatSession]);

  const toggleChat = async () => {
    if (!isOpen) {
      // Opening chat
      if (!user && (!visitorInfo.name || !visitorInfo.email)) {
        setShowVisitorForm(true);
      } else {
        await checkAdminStatus();
        await initializeChatSession();
        setIsOpen(true);
        setupRealtimeSubscription();
      }
    } else {
      // Closing chat
      setIsOpen(false);
      if (realtimeChannelRef.current) {
        supabase.removeChannel(realtimeChannelRef.current);
        realtimeChannelRef.current = null;
      }
    }
  };

  // Setup realtime subscription for messages
  const setupRealtimeSubscription = useCallback(() => {
    if (!chatSession?.id || realtimeChannelRef.current || typeof window === 'undefined') return;

    const channel = supabaseLiveChat
      .channel(`live_chat_${chatSession.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_chat_messages',
          filter: `session_id=eq.${chatSession.id}`
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage;
          if (newMessage.sender_type !== 'visitor') {
            setMessages(prev => [...prev, newMessage]);

            // Mark message as read if it's from admin
            if (newMessage.sender_type === 'admin') {
              supabaseLiveChat
                .from('live_chat_messages')
                .update({ is_read: true, read_at: new Date().toISOString() })
                .eq('id', newMessage.id)
                .then(() => {});
            }
          }
        }
      )
      .subscribe();

    realtimeChannelRef.current = channel;
  }, [chatSession?.id]);

  // Send message
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !chatSession) return;

    const messageContent = message.trim();
    setMessage(""); // Clear input immediately for better UX

    try {
      const response = await fetch('/api/live-chat/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: chatSession.id,
          sender_type: 'visitor',
          sender_id: user?.id || null,
          content: messageContent,
          message_type: 'text'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const result = await response.json();
      const newMessage = result.data;

      // Add message to local state
      setMessages(prev => [...prev, newMessage]);

      // Update session status to active if it was waiting
      if (chatSession.status === 'waiting') {
        setChatSession(prev => prev ? { ...prev, status: 'active' } : null);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      // Restore message on error
      setMessage(messageContent);
    }
  };

  // Handle visitor form submission
  const handleVisitorFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!visitorInfo.name.trim() || !visitorInfo.email.trim()) return;

    setShowVisitorForm(false);
    await checkAdminStatus();
    await initializeChatSession();
    setIsOpen(true);
    setupRealtimeSubscription();
  };

  // Close chat when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isOpen && !target.closest('.chat-container') && !target.closest('.chat-button')) {
        setIsOpen(false);
        if (realtimeChannelRef.current) {
          supabaseLiveChat.removeChannel(realtimeChannelRef.current);
          realtimeChannelRef.current = null;
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (realtimeChannelRef.current) {
        supabaseLiveChat.removeChannel(realtimeChannelRef.current);
      }
    };
  }, []);

  // Don't render on server side
  if (!isClient) {
    return null;
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Chat Button */}
      <motion.button
        className={`chat-button relative w-14 h-14 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 ${
          adminOnline
            ? 'bg-green-600 hover:bg-green-700'
            : 'bg-primary hover:bg-primary/90'
        } text-white`}
        onClick={toggleChat}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label="Open chat"
        disabled={isConnecting}
      >
        {isConnecting ? (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Clock className="h-5 w-5" />
          </motion.div>
        ) : isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <MessageSquare className="h-6 w-6" />
        )}

        {/* Online status indicator */}
        {!isOpen && (
          <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
            adminOnline ? 'bg-green-400' : 'bg-gray-400'
          }`} />
        )}
      </motion.button>

      {/* Visitor Information Form */}
      <AnimatePresence>
        {showVisitorForm && (
          <motion.div
            className="chat-container absolute bottom-20 right-0 w-80 sm:w-96 bg-white rounded-lg shadow-2xl overflow-hidden"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.3 }}
          >
            <div className="bg-primary text-white p-4">
              <div className="flex items-center">
                <User className="h-5 w-5 mr-2" />
                <h3 className="font-bold">Start Chat</h3>
              </div>
            </div>

            <form onSubmit={handleVisitorFormSubmit} className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Your Name
                </label>
                <input
                  type="text"
                  value={visitorInfo.name}
                  onChange={(e) => setVisitorInfo(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Enter your name"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Your Email
                </label>
                <input
                  type="email"
                  value={visitorInfo.email}
                  onChange={(e) => setVisitorInfo(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Enter your email"
                  required
                />
              </div>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => setShowVisitorForm(false)}
                  className="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
                >
                  Start Chat
                </button>
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="chat-container absolute bottom-20 right-0 w-80 sm:w-96 bg-white rounded-lg shadow-2xl overflow-hidden"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.3 }}
          >
            {/* Chat Header */}
            <div className={`text-white p-4 ${adminOnline ? 'bg-green-600' : 'bg-primary'}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                    adminOnline ? 'bg-white/20' : 'bg-white/20'
                  }`}>
                    <MessageSquare className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-bold">Senior's Archi-firm</h3>
                    <div className="text-xs opacity-80 flex items-center">
                      <div className={`w-2 h-2 rounded-full mr-1 ${
                        adminOnline ? 'bg-green-300' : 'bg-gray-300'
                      }`} />
                      {adminOnline ? 'Online' : 'Offline - We\'ll respond soon'}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-white/70 hover:text-white p-1"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="h-80 overflow-y-auto p-4 bg-gray-50">
              <div className="space-y-4">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex ${
                      msg.sender_type === "visitor" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        msg.sender_type === "visitor"
                          ? "bg-primary text-white rounded-br-none"
                          : msg.sender_type === "system"
                          ? "bg-blue-100 text-blue-800 rounded-lg text-center text-sm"
                          : "bg-white shadow-md rounded-bl-none"
                      }`}
                    >
                      <p className={`${msg.sender_type === "system" ? "text-xs" : "text-sm"}`}>
                        {msg.content}
                      </p>
                      {msg.sender_type !== "system" && (
                        <div className="flex items-center justify-between mt-1">
                          <p className={`text-xs ${
                            msg.sender_type === "visitor" ? "text-white/70" : "text-gray-500"
                          }`}>
                            {new Date(msg.created_at).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                          {msg.sender_type === "visitor" && (
                            <div className="ml-2">
                              {msg.is_read ? (
                                <CheckCircle className="h-3 w-3 text-white/70" />
                              ) : (
                                <div className="w-3 h-3 rounded-full border border-white/70" />
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                
                {/* Typing indicator */}
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-white shadow-md rounded-lg rounded-bl-none p-3 max-w-[80%]">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
                      </div>
                    </div>
                  </div>
                )}

                {messages.length === 0 && !isConnecting && (
                  <div className="text-center text-gray-500 text-sm py-8">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p>Start a conversation with us!</p>
                  </div>
                )}
              </div>
              <div ref={messagesEndRef} />
            </div>

            {/* Chat Input */}
            <form onSubmit={handleSubmit} className="p-3 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder={
                    chatSession?.status === 'waiting'
                      ? "Send a message to start the conversation..."
                      : "Type your message..."
                  }
                  className="flex-1 p-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  disabled={!chatSession}
                />
                <button
                  type="submit"
                  className={`p-2 rounded-full transition-colors ${
                    message.trim() && chatSession
                      ? "bg-primary text-white hover:bg-primary/90"
                      : "bg-gray-200 text-gray-400 cursor-not-allowed"
                  }`}
                  disabled={!message.trim() || !chatSession}
                  aria-label="Send message"
                >
                  <Send className="h-4 w-4" />
                </button>
              </div>

              {/* Connection status */}
              {chatSession && (
                <div className="mt-2 text-xs text-gray-500 flex items-center">
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    chatSession.status === 'active' ? 'bg-green-400' : 'bg-yellow-400'
                  }`} />
                  {chatSession.status === 'active'
                    ? 'Connected'
                    : 'Waiting for admin to join...'
                  }
                </div>
              )}
            </form>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LiveChatButton;