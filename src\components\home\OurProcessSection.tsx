"use client";

import { useState, useRef } from "react";
import { motion } from "framer-motion";
import {
  ArrowR<PERSON>,
  Pencil,
  Users,
  ClipboardCheck,
  HardHat
} from "lucide-react";
import Link from "next/link";
import { Button } from "../ui/button";


const processSteps = [
  {
    icon: <Pencil className="h-12 w-12 stroke-[1.5px] fill-primary/20 text-primary" />,
    title: "Share Your Vision",
    description: "Discuss your ideas and goals"
  },
  {
    icon: <Users className="h-12 w-12 stroke-[1.5px] fill-primary/20 text-primary" />,
    title: "Collaborate",
    description: "Work with our designers"
  },
  {
    icon: <ClipboardCheck className="h-12 w-12 stroke-[1.5px] fill-primary/20 text-primary" />,
    title: "Review & Approve",
    description: "Finalize the details"
  },
  {
    icon: <HardHat className="h-12 w-12 stroke-[1.5px] fill-primary/20 text-primary" />,
    title: "Build",
    description: "Watch your vision come to life"
  },
];

const OurProcessSection = () => {
  const [activeStep, setActiveStep] = useState(0);
  const sectionRef = useRef(null);

  return (
    <section
      ref={sectionRef}
      className="py-32 bg-white relative"
    >
      <div className="container mx-auto px-6 md:px-8 lg:px-12">

        {/* Section header with modern typography */}
        <div className="max-w-3xl mx-auto mb-28">
          <motion.div
            className="mb-6 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center justify-center mb-6">
              <div className="h-[1px] w-12 bg-primary mr-4"></div>
              <span className="text-primary uppercase tracking-widest text-sm font-medium">How we work</span>
              <div className="h-[1px] w-12 bg-primary ml-4"></div>
            </div>

            <motion.h2
              className="text-4xl md:text-6xl font-bold tracking-tight leading-tight"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Our Process
            </motion.h2>

            <motion.p
              className="text-gray-600 mt-6 text-lg max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              A streamlined approach to bringing your architectural vision to life
            </motion.p>
          </motion.div>
        </div>

        {/* Process Steps with modern design */}
        <div className="relative max-w-6xl mx-auto">
          {/* Horizontal line connecting steps (desktop only) */}
          <div className="hidden md:block absolute top-24 left-0 right-0 h-[2px] bg-gray-200 z-0">
            <motion.div
              className="h-full bg-primary origin-left"
              initial={{ scaleX: 0 }}
              whileInView={{ scaleX: 1 }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
              viewport={{ once: true }}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-16 md:gap-8 relative">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                className="relative flex flex-col items-center md:items-start"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
                viewport={{ once: true }}
                onHoverStart={() => setActiveStep(index)}
              >
                {/* Step number */}
                <div className="absolute -top-12 md:left-0 text-7xl font-black text-gray-100 select-none hidden md:block z-0">
                  {index + 1}
                </div>

                {/* Icon container */}
                <div className="flex justify-center">
                  <div
                    className={`w-20 h-20 flex items-center justify-center mb-6 relative z-10
                      ${index === activeStep ? 'text-primary' : 'text-gray-700'}`}
                  >
                    {/* Decorative elements */}
                    <div className="absolute inset-0 bg-primary/5 transform rotate-45 transition-all duration-300"></div>
                    <div className="absolute inset-0 border-[1px] border-primary/20 transform rotate-45 transition-all duration-300"></div>

                    {/* Icon */}
                    <div className="relative z-10">
                      {step.icon}
                    </div>

                    {/* Connector dot for desktop */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-primary rounded-full hidden md:block"></div>
                  </div>
                </div>

                {/* Content */}
                <div className="text-center md:text-left">
                  <h3 className="text-2xl font-bold mb-3 tracking-tight">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 max-w-xs mx-auto md:mx-0">
                    {step.description}
                  </p>
                </div>

                {/* Vertical connector for mobile */}
                {index < processSteps.length - 1 && (
                  <div className="h-12 w-[1px] bg-gray-200 my-4 md:hidden">
                    <motion.div
                      className="h-full w-full bg-primary origin-top"
                      initial={{ scaleY: 0 }}
                      whileInView={{ scaleY: 1 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      viewport={{ once: true }}
                    />
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Aesthetic connectors between steps (desktop only) */}
          <div className="hidden md:block">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={`connector-${index}`}
                className="absolute top-24 h-[2px] bg-primary z-0"
                style={{
                  left: `${(100 / 4) * (index + 1)}%`,
                  width: '20px',
                  transform: 'translateX(-50%)'
                }}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.8 + (index * 0.2) }}
                viewport={{ once: true }}
              />
            ))}
          </div>
        </div>

        {/* CTA Button with modern styling */}
        <motion.div
          className="text-center mt-32"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          viewport={{ once: true }}
        >
          <Link href="/process">
            <div className="inline-block group relative overflow-hidden">
              <div className="absolute inset-0 w-full h-full bg-primary transform -translate-y-full transition-transform duration-300 group-hover:translate-y-0"></div>
              <Button
                variant="default"
                size="lg"
                className="relative z-10 px-10 py-6 text-white bg-primary group-hover:bg-transparent transition-colors duration-300 inline-flex items-center"
              >
                <span className="mr-2">Learn More</span>
                <ArrowRight className="h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default OurProcessSection;
