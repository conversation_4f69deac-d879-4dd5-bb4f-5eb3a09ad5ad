import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import Stripe from 'https://esm.sh/stripe@12.0.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2022-11-15',
})

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const { projectId, milestoneId, amount, description, clientId, designerId, paymentType } = await req.json()

    // Validate required fields
    if (!projectId || !amount || !description || !clientId || !paymentType) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields',
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get the client's email
    const { data: clientData, error: clientError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', clientId)
      .single()

    if (clientError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to get client information',
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: 'usd',
      description,
      metadata: {
        projectId,
        milestoneId: milestoneId || '',
        clientId,
        designerId: designerId || '',
        paymentType,
      },
      receipt_email: clientData.email,
      automatic_payment_methods: {
        enabled: true,
      },
    })

    // Create a transaction record
    const { error: transactionError } = await supabase.from('transactions').insert({
      transaction_id: paymentIntent.id,
      amount: amount / 100, // Convert from cents
      status: 'pending',
      type: 'payment',
      project_id: projectId,
      milestone_id: milestoneId,
      client_id: clientId,
      designer_id: designerId,
      notes: `${paymentType} payment via Stripe`,
    })

    if (transactionError) {
      console.error('Error creating transaction record:', transactionError)
    }

    return new Response(
      JSON.stringify({
        paymentIntent: {
          id: paymentIntent.id,
          client_secret: paymentIntent.client_secret,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status,
        },
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error creating payment intent:', error)
    return new Response(
      JSON.stringify({
        error: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
