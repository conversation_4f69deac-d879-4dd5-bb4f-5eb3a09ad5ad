"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, CheckCircle, Clock, ArrowRight } from 'lucide-react';

interface ProjectTimelineProps {
  startDate: string | null;
  endDate: string | null;
  milestones: {
    id: string;
    title: string;
    status: string;
    order_index: number;
  }[];
  currentStep?: number;
}

export function ProjectTimeline({ startDate, endDate, milestones, currentStep = 0 }: ProjectTimelineProps) {
  // Sort milestones by order_index
  const sortedMilestones = [...milestones].sort((a, b) => a.order_index - b.order_index);
  
  // Calculate total duration in days
  const start = startDate ? new Date(startDate) : new Date();
  const end = endDate ? new Date(endDate) : new Date(start.getTime() + 30 * 24 * 60 * 60 * 1000); // Default to 30 days if no end date
  const totalDays = Math.max(1, Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)));
  
  // Format dates for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Calendar className="h-5 w-5 text-brown-600 mr-2" />
          <h3 className="text-lg font-semibold">Project Timeline</h3>
        </div>
        <div className="flex items-center text-sm text-gray-500">
          <Clock className="h-4 w-4 mr-1" />
          <span>Duration: {totalDays} days</span>
        </div>
      </div>
      
      <div className="flex items-center justify-between mb-2 text-sm">
        <span>{formatDate(start)}</span>
        <span>{formatDate(end)}</span>
      </div>
      
      {/* Timeline bar */}
      <div className="relative h-3 bg-gray-200 rounded-full mb-8">
        <motion.div 
          className="absolute top-0 left-0 h-full bg-brown-600 rounded-full"
          initial={{ width: '0%' }}
          animate={{ width: `${Math.min(100, (currentStep / sortedMilestones.length) * 100)}%` }}
          transition={{ duration: 0.5 }}
        />
        
        {/* Milestone markers */}
        {sortedMilestones.map((milestone, index) => {
          const position = `${(index / (sortedMilestones.length - 1)) * 100}%`;
          const isCompleted = milestone.status === 'completed';
          const isCurrent = index === currentStep;
          
          return (
            <div 
              key={milestone.id}
              className="absolute top-0 transform -translate-y-1/2"
              style={{ left: position }}
            >
              <div 
                className={`h-5 w-5 rounded-full flex items-center justify-center ${
                  isCompleted 
                    ? 'bg-green-500' 
                    : isCurrent 
                      ? 'bg-brown-600 ring-4 ring-brown-200' 
                      : 'bg-gray-400'
                }`}
              >
                {isCompleted && <CheckCircle className="h-3 w-3 text-white" />}
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Milestone list */}
      <div className="space-y-4">
        {sortedMilestones.map((milestone, index) => {
          const isCompleted = milestone.status === 'completed';
          const isCurrent = index === currentStep;
          
          return (
            <div 
              key={milestone.id}
              className={`flex items-center p-3 border-l-4 ${
                isCompleted 
                  ? 'border-green-500 bg-green-50' 
                  : isCurrent 
                    ? 'border-brown-600 bg-brown-50' 
                    : 'border-gray-300 bg-gray-50'
              }`}
            >
              <div className="mr-4">
                <div 
                  className={`h-8 w-8 rounded-full flex items-center justify-center ${
                    isCompleted 
                      ? 'bg-green-500' 
                      : isCurrent 
                        ? 'bg-brown-600' 
                        : 'bg-gray-400'
                  }`}
                >
                  {isCompleted ? (
                    <CheckCircle className="h-5 w-5 text-white" />
                  ) : (
                    <span className="text-white font-medium">{index + 1}</span>
                  )}
                </div>
              </div>
              <div>
                <h4 className="font-medium">{milestone.title}</h4>
                <p className="text-sm text-gray-500">
                  {isCompleted 
                    ? 'Completed' 
                    : isCurrent 
                      ? 'In Progress' 
                      : 'Pending'}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
