'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { CreateDisputeForm } from '@/components/disputes/CreateDisputeForm';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';

interface NewClientDisputePageProps {
  params: {
    id: string;
  };
}

export default function NewClientDisputePage({ params }: NewClientDisputePageProps) {
  const { id: projectId } = params;
  const { user, loading } = useOptimizedAuth();
  const router = useRouter();
  const [projectTitle, setProjectTitle] = useState<string | null>(null);
  const [loadingProject, setLoadingProject] = useState(true);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (!user) return;

    const fetchProject = async () => {
      setLoadingProject(true);
      try {
        const { data, error } = await supabase
          .from('projects')
          .select('title')
          .eq('id', projectId)
          .eq('client_id', user.id) // Ensure client owns the project
          .single();

        if (error) {
          throw error;
        }

        setProjectTitle(data.title);
      } catch (error) {
        console.error('Error fetching project:', error);
        router.push('/client/projects');
      } finally {
        setLoadingProject(false);
      }
    };

    fetchProject();
  }, [projectId, user, router]);

  const handleSuccess = () => {
    router.push('/client/disputes');
  };

  if (loading || !user || loadingProject) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href={`/client/projects/${projectId}`}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Project
          </Link>
        </Button>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Create New Dispute</CardTitle>
          <CardDescription>
            {projectTitle
              ? `Submit a dispute for project: ${projectTitle}`
              : 'Submit a dispute for this project'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CreateDisputeForm
            projectId={projectId}
            onSuccess={handleSuccess}
            onCancel={() => router.back()}
          />
        </CardContent>
      </Card>
    </div>
  );
}
