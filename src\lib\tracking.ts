import { supabase } from './supabase';
import { nanoid } from 'nanoid';

// Note: Resend client is now initialized in API routes to avoid client-side issues

// Generate a unique tracking number
export const generateTrackingNumber = (): string => {
  // Format: TR-XXXX-XXXX-XXXX (where X is alphanumeric)
  return `TR-${nanoid(4).toUpperCase()}-${nanoid(4).toUpperCase()}-${nanoid(4).toUpperCase()}`;
};

// Create a new tracking request for Sample Request (client-side helper)
export const createSampleRequestTracking = async (data: {
  name: string;
  email: string;
  projectType: string;
  description: string;
  sampleType: string;
  serviceCategory: string;
  filePath?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
  recaptchaToken?: string;
}) => {
  try {
    console.log('Making API request to /api/tracking/sample-request with data:', data);

    const response = await fetch('/api/tracking/sample-request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    console.log('API response status:', response.status);
    console.log('API response headers:', Object.fromEntries(response.headers.entries()));

    const result = await response.json();
    console.log('API response body:', result);

    if (!response.ok) {
      const errorMessage = result.error || `HTTP error! status: ${response.status}`;
      const errorDetails = result.details ? ` Details: ${JSON.stringify(result.details)}` : '';
      throw new Error(`${errorMessage}${errorDetails}`);
    }

    if (!result.success) {
      throw new Error(result.error || 'Failed to create tracking request');
    }

    return result.data;
  } catch (error) {
    console.error('Error in createSampleRequestTracking:', error);
    throw error;
  }
};

// Create a new tracking request for Vision Builder (client-side helper)
export const createVisionBuilderTracking = async (data: {
  name: string;
  email: string;
  projectType: string;
  visionPrompt: string;
  selectedStyle?: string;
  serviceCategory?: string;
  imageUrl?: string;
}) => {
  try {
    const response = await fetch('/api/tracking/vision-builder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to create tracking request');
    }

    return result.data;
  } catch (error) {
    console.error('Error in createVisionBuilderTracking:', error);
    throw error;
  }
};

// Get tracking request by tracking number (moved to API route)
// Use GET /api/tracking/[trackingNumber] instead

// Update tracking request status (moved to API route)
// Use PATCH /api/tracking/[trackingNumber] instead

// Link tracking request to a user account (moved to API route)
// Use PATCH /api/tracking/[trackingNumber] instead

// Link tracking request to a project (moved to API route)
// Use PATCH /api/tracking/[trackingNumber] instead

// Get tracking requests by email (moved to API route)
// Use GET /api/tracking?email=[email] instead

// Update tracking request file path (moved to API route)
// Use PATCH /api/tracking/[trackingNumber] with { filePath } instead

// Update tracking request image URL (moved to API route)
// Use PATCH /api/tracking/[trackingNumber] with { imageUrl } instead

// Email notification functions are now handled in API routes


