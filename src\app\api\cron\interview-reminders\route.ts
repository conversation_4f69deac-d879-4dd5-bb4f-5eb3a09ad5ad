import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * Cron job API route for sending interview reminder emails
 * This should be called every hour to check for pending reminders
 */
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret for security
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const now = new Date();
    console.log(`Interview reminder cron job started at ${now.toISOString()}`);

    // Get pending reminders that should be sent now
    const { data: reminders, error: fetchError } = await supabase
      .from('interview_reminders')
      .select('*')
      .eq('status', 'pending')
      .lte('reminder_scheduled_at', now.toISOString())
      .order('reminder_scheduled_at', { ascending: true });

    if (fetchError) {
      console.error('Error fetching reminders:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch reminders' }, { status: 500 });
    }

    if (!reminders || reminders.length === 0) {
      console.log('No pending reminders found');
      return NextResponse.json({ 
        success: true, 
        message: 'No pending reminders',
        processed: 0 
      });
    }

    console.log(`Found ${reminders.length} pending reminders to process`);

    let successCount = 0;
    let failureCount = 0;

    // Process each reminder
    for (const reminder of reminders) {
      try {
        // Format interview date and time
        const interviewDate = new Date(reminder.interview_scheduled_at);
        const formattedDate = interviewDate.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        const formattedTime = interviewDate.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        });

        // Calculate hours until interview
        const hoursUntil = Math.round((interviewDate.getTime() - now.getTime()) / (1000 * 60 * 60));

        // Send reminder email
        await resend.emails.send({
          from: 'Seniors Architecture Firm <<EMAIL>>',
          to: [reminder.applicant_email],
          subject: 'Interview Reminder - Tomorrow at Seniors Architecture Firm',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #8B4513;">Interview Reminder</h2>
              
              <p>Dear ${reminder.applicant_name},</p>
              
              <p>This is a friendly reminder about your upcoming interview with Seniors Architecture Firm.</p>
              
              <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
                <h3 style="margin-top: 0; color: #856404;">Your Interview is in ${hoursUntil} hours!</h3>
                <p style="margin-bottom: 0; color: #856404;"><strong>Date:</strong> ${formattedDate}</p>
                <p style="margin-bottom: 0; color: #856404;"><strong>Time:</strong> ${formattedTime}</p>
                <p style="margin-bottom: 0; color: #856404;"><strong>Type:</strong> ${reminder.interview_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
              </div>
              
              ${reminder.meeting_details ? `
              <div style="background-color: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #8B4513;">Meeting Details:</h4>
                ${reminder.meeting_details}
              </div>
              ` : ''}
              
              <div style="background-color: #e8f4f8; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #8B4513;">Preparation Tips:</h4>
                <ul style="margin-bottom: 0;">
                  <li>Review your portfolio and be ready to discuss your projects</li>
                  <li>Prepare questions about our company and the role</li>
                  <li>Test your technology if it's a video call</li>
                  <li>Have a copy of your resume handy</li>
                  <li>Arrive 5-10 minutes early</li>
                </ul>
              </div>
              
              <p>We're looking forward to meeting you tomorrow!</p>
              
              <p>If you need to reschedule or have any questions, please contact us immediately at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
              
              <p>Best regards,<br>
              The Seniors Architecture Firm Team</p>
              
              <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
              <p style="font-size: 12px; color: #666;">
                This is an automated reminder for your scheduled interview. Please do not reply to this email.
              </p>
            </div>
          `
        });

        // Mark reminder as sent
        await supabase
          .from('interview_reminders')
          .update({
            status: 'sent',
            sent_at: now.toISOString()
          })
          .eq('id', reminder.id);

        successCount++;
        console.log(`Reminder sent successfully to ${reminder.applicant_email}`);

      } catch (error) {
        console.error(`Error sending reminder to ${reminder.applicant_email}:`, error);
        
        // Mark reminder as failed
        await supabase
          .from('interview_reminders')
          .update({
            status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unknown error'
          })
          .eq('id', reminder.id);

        failureCount++;
      }
    }

    console.log(`Interview reminder cron job completed. Success: ${successCount}, Failures: ${failureCount}`);

    return NextResponse.json({
      success: true,
      message: 'Interview reminders processed',
      processed: reminders.length,
      successful: successCount,
      failed: failureCount
    });

  } catch (error) {
    console.error('Error in interview reminder cron job:', error);
    return NextResponse.json(
      { error: 'Failed to process interview reminders' },
      { status: 500 }
    );
  }
}

/**
 * Manual trigger for testing (POST method)
 */
export async function POST(request: NextRequest) {
  // Allow manual triggering for testing purposes
  return GET(request);
}
