"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  HelpCircle,
  Search,
  Book,
  MessageSquare,
  Mail,
  Phone,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  Users,
  Settings,
  CreditCard,
  FolderKanban,
  AlertTriangle,
  FileText,
  Video,
  Download
} from "lucide-react";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

interface HelpCategory {
  id: string;
  name: string;
  icon: any;
  description: string;
  articles: number;
}

export default function AdminHelpPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const helpCategories: HelpCategory[] = [
    {
      id: "user-management",
      name: "User Management",
      icon: Users,
      description: "Managing users, roles, and permissions",
      articles: 12
    },
    {
      id: "project-management",
      name: "Project Management",
      icon: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      description: "Project oversight and assignment",
      articles: 8
    },
    {
      id: "financial-management",
      name: "Financial Management",
      icon: CreditCard,
      description: "Payments, fees, and financial reporting",
      articles: 15
    },
    {
      id: "system-settings",
      name: "System Settings",
      icon: Settings,
      description: "Platform configuration and preferences",
      articles: 6
    },
    {
      id: "dispute-resolution",
      name: "Dispute Resolution",
      icon: AlertTriangle,
      description: "Handling disputes and conflicts",
      articles: 4
    }
  ];

  const faqItems: FAQItem[] = [
    {
      id: "1",
      question: "How do I approve a designer application?",
      answer: "Navigate to User Management > Designer Applications, review the application details, portfolio, and qualifications. Click 'Approve' to accept or 'Reject' with a reason if declining.",
      category: "user-management"
    },
    {
      id: "2",
      question: "How do I assign a project to a designer?",
      answer: "Go to Project Management > All Projects, select the project, and click 'Assign Designer'. Choose from available designers based on their skills and availability.",
      category: "project-management"
    },
    {
      id: "3",
      question: "How do I process designer payouts?",
      answer: "In Financial Management > Payouts, review completed milestones, verify work quality, and approve payments. The system will automatically calculate fees and taxes.",
      category: "financial-management"
    },
    {
      id: "4",
      question: "How do I handle a project dispute?",
      answer: "Go to Disputes section, review both parties' claims, examine project details and communications, then make a decision. You can also facilitate mediation between parties.",
      category: "dispute-resolution"
    },
    {
      id: "5",
      question: "How do I configure platform fees?",
      answer: "Navigate to Financial Management > Fee Management to set platform commission rates, payment processing fees, and other charges.",
      category: "financial-management"
    },
    {
      id: "6",
      question: "How do I manage user roles and permissions?",
      answer: "In User Management > User Roles, you can create custom roles, assign permissions, and modify access levels for different user types.",
      category: "user-management"
    }
  ];

  const quickActions = [
    {
      name: "Video Tutorials",
      description: "Watch step-by-step guides",
      icon: Video,
      href: "#tutorials"
    },
    {
      name: "Download Manual",
      description: "Complete admin guide PDF",
      icon: Download,
      href: "#manual"
    },
    {
      name: "Contact Support",
      description: "Get direct help from our team",
      icon: MessageSquare,
      href: "#contact"
    },
    {
      name: "System Status",
      description: "Check platform health",
      icon: ExternalLink,
      href: "#status"
    }
  ];

  const filteredFAQs = faqItems.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "all" || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Help & Support</h1>
        <p className="text-gray-600">Find answers, guides, and get support for admin tasks</p>
      </div>

      {/* Search Bar */}
      <div className="mb-8">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search help articles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-brown-500 focus:border-brown-500"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
            </div>
            <div className="p-6 space-y-3">
              {quickActions.map((action) => (
                <a
                  key={action.name}
                  href={action.href}
                  className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                >
                  <action.icon className="h-5 w-5 text-gray-400 group-hover:text-brown-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{action.name}</p>
                    <p className="text-xs text-gray-500">{action.description}</p>
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* Categories */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Categories</h2>
            </div>
            <div className="p-6">
              <button
                onClick={() => setSelectedCategory("all")}
                className={`w-full text-left p-3 rounded-lg mb-2 transition-colors ${
                  selectedCategory === "all" ? "bg-brown-50 text-brown-700" : "hover:bg-gray-50"
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium">All Categories</span>
                  <span className="text-sm text-gray-500">{faqItems.length}</span>
                </div>
              </button>
              
              {helpCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full text-left p-3 rounded-lg mb-2 transition-colors ${
                    selectedCategory === category.id ? "bg-brown-50 text-brown-700" : "hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center mb-1">
                    <category.icon className="h-4 w-4 mr-2" />
                    <span className="font-medium">{category.name}</span>
                  </div>
                  <p className="text-xs text-gray-500 ml-6">{category.description}</p>
                  <div className="flex justify-between items-center mt-1 ml-6">
                    <span className="text-xs text-gray-400">{category.articles} articles</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Help Categories Grid */}
          {selectedCategory === "all" && !searchQuery && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {helpCategories.map((category) => (
                <div
                  key={category.id}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-brown-100 rounded-lg flex items-center justify-center mr-4">
                        <category.icon className="h-6 w-6 text-brown-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                        <p className="text-sm text-gray-500">{category.articles} articles</p>
                      </div>
                    </div>
                    <p className="text-gray-600">{category.description}</p>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* FAQ Section */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {selectedCategory === "all" ? "Frequently Asked Questions" : 
                 helpCategories.find(cat => cat.id === selectedCategory)?.name || "FAQ"}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                {filteredFAQs.length} {filteredFAQs.length === 1 ? 'result' : 'results'}
              </p>
            </div>
            
            <div className="divide-y divide-gray-200">
              {filteredFAQs.length === 0 ? (
                <div className="p-8 text-center">
                  <HelpCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-600">
                    Try adjusting your search terms or browse different categories.
                  </p>
                </div>
              ) : (
                filteredFAQs.map((faq) => (
                  <div key={faq.id} className="p-6">
                    <button
                      onClick={() => toggleFAQ(faq.id)}
                      className="w-full text-left flex items-center justify-between hover:text-brown-600 transition-colors"
                    >
                      <h3 className="text-lg font-medium text-gray-900 pr-4">{faq.question}</h3>
                      {expandedFAQ === faq.id ? (
                        <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      ) : (
                        <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      )}
                    </button>
                    
                    {expandedFAQ === faq.id && (
                      <div className="mt-4 text-gray-600 leading-relaxed">
                        {faq.answer}
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Contact Support */}
          <div className="mt-8 bg-gradient-to-r from-brown-50 to-brown-100 rounded-lg p-6">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 text-brown-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Still need help?</h3>
              <p className="text-gray-600 mb-6">
                Our support team is here to help you with any questions or issues.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  Email Support
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Phone className="h-4 w-4 mr-2" />
                  Call Support
                </Button>
              </div>
              
              <div className="mt-4 text-sm text-gray-500">
                <p>Email: <EMAIL></p>
                <p>Phone: +****************</p>
                <p>Hours: Monday - Friday, 9 AM - 6 PM EST</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
