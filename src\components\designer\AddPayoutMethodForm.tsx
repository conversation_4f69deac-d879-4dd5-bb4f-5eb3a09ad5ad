"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  X,
  Building2,
  CreditCard,
  Globe,
  AlertCircle,
  CheckCircle,
  Loader2,
  Info
} from "lucide-react";

interface AddPayoutMethodFormProps {
  onClose: () => void;
  onSuccess: () => void;
  editingMethod?: any;
}

interface FormData {
  method_type: 'bank_account' | 'paypal' | 'stripe_connect' | 'wise' | 'international_wire';
  account_holder_name: string;
  bank_name: string;
  account_number: string;
  routing_number: string;
  swift_code: string;
  iban: string;
  bank_address: string;
  bank_country: string;
  bank_currency: string;
  account_type: 'checking' | 'savings' | 'business';
  paypal_email: string;
  minimum_payout_amount: number;
  payout_frequency: 'daily' | 'weekly' | 'bi_weekly' | 'monthly';
  auto_payout_enabled: boolean;
  is_default: boolean;
}

const COUNTRIES = [
  { code: 'US', name: 'United States', currency: 'USD' },
  { code: 'SA', name: 'Saudi Arabia', currency: 'SAR' },
  { code: 'AE', name: 'United Arab Emirates', currency: 'AED' },
  { code: 'GB', name: 'United Kingdom', currency: 'GBP' },
  { code: 'EU', name: 'European Union', currency: 'EUR' },
  { code: 'CA', name: 'Canada', currency: 'CAD' },
  { code: 'AU', name: 'Australia', currency: 'AUD' },
  { code: 'IN', name: 'India', currency: 'INR' },
  { code: 'SG', name: 'Singapore', currency: 'SGD' },
  { code: 'HK', name: 'Hong Kong', currency: 'HKD' },
];

export function AddPayoutMethodForm({ onClose, onSuccess, editingMethod }: AddPayoutMethodFormProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  
  const [formData, setFormData] = useState<FormData>({
    method_type: editingMethod?.method_type || 'bank_account',
    account_holder_name: editingMethod?.account_holder_name || '',
    bank_name: editingMethod?.bank_name || '',
    account_number: '',
    routing_number: editingMethod?.routing_number || '',
    swift_code: editingMethod?.swift_code || '',
    iban: editingMethod?.iban || '',
    bank_address: editingMethod?.bank_address || '',
    bank_country: editingMethod?.bank_country || 'SA',
    bank_currency: editingMethod?.bank_currency || 'SAR',
    account_type: editingMethod?.account_type || 'checking',
    paypal_email: editingMethod?.paypal_email || '',
    minimum_payout_amount: editingMethod?.minimum_payout_amount || 100,
    payout_frequency: editingMethod?.payout_frequency || 'weekly',
    auto_payout_enabled: editingMethod?.auto_payout_enabled ?? true,
    is_default: editingMethod?.is_default || false,
  });

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-set currency when country changes
    if (field === 'bank_country') {
      const country = COUNTRIES.find(c => c.code === value);
      if (country) {
        setFormData(prev => ({ ...prev, bank_currency: country.currency }));
      }
    }
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!formData.method_type;
      case 2:
        if (formData.method_type === 'paypal') {
          return !!formData.paypal_email && !!formData.account_holder_name;
        }
        return !!formData.account_holder_name && !!formData.bank_name;
      case 3:
        if (formData.method_type === 'paypal') return true;
        if (formData.method_type === 'bank_account') {
          return !!formData.account_number && !!formData.routing_number;
        }
        if (formData.method_type === 'international_wire') {
          return !!formData.account_number && !!formData.swift_code;
        }
        return !!formData.account_number;
      default:
        return true;
    }
  };

  const handleSubmit = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Prepare sensitive data for server-side encryption
      const sensitiveData = {
        account_number: formData.account_number || undefined,
        routing_number: formData.routing_number || undefined,
        swift_code: formData.swift_code || undefined,
        iban: formData.iban || undefined,
      };

      // Get user session for API authentication
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Authentication required');
      }

      // Encrypt sensitive fields on server-side
      const encryptResponse = await fetch('/api/encrypt-payment-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action: 'encrypt',
          data: sensitiveData
        })
      });

      if (!encryptResponse.ok) {
        const errorData = await encryptResponse.json();
        throw new Error(errorData.error || 'Failed to encrypt payment data');
      }

      const { data: encryptedData } = await encryptResponse.json();

      const payoutMethodData = {
        designer_id: user.id,
        method_type: formData.method_type,
        account_holder_name: formData.account_holder_name,
        bank_name: formData.bank_name || null,
        account_number_encrypted: encryptedData.account_number || null,
        routing_number: encryptedData.routing_number || null,
        swift_code: encryptedData.swift_code || null,
        iban: encryptedData.iban || null,
        bank_address: formData.bank_address || null,
        bank_country: formData.bank_country || null,
        bank_currency: formData.bank_currency || 'USD',
        account_type: formData.account_type || null,
        paypal_email: formData.paypal_email || null,
        minimum_payout_amount: formData.minimum_payout_amount,
        payout_frequency: formData.payout_frequency,
        auto_payout_enabled: formData.auto_payout_enabled,
        is_default: formData.is_default,
        verification_status: 'pending',
      };

      if (editingMethod) {
        const { error } = await supabase
          .from('designer_payout_methods')
          .update(payoutMethodData)
          .eq('id', editingMethod.id);
        
        if (error) throw error;
      } else {
        // If this is set as default, unset other defaults first
        if (formData.is_default) {
          await supabase
            .from('designer_payout_methods')
            .update({ is_default: false })
            .eq('designer_id', user.id);
        }

        const { error } = await supabase
          .from('designer_payout_methods')
          .insert(payoutMethodData);
        
        if (error) throw error;
      }

      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error saving payout method:', error);
      setError(error.message || 'Failed to save payout method');
    } finally {
      setLoading(false);
    }
  };

  const getMethodIcon = (methodType: string) => {
    switch (methodType) {
      case 'bank_account':
        return <Building2 className="h-6 w-6" />;
      case 'paypal':
        return <CreditCard className="h-6 w-6" />;
      case 'stripe_connect':
        return <CreditCard className="h-6 w-6" />;
      case 'wise':
        return <Globe className="h-6 w-6" />;
      case 'international_wire':
        return <Globe className="h-6 w-6" />;
      default:
        return <CreditCard className="h-6 w-6" />;
    }
  };

  const getMethodDescription = (methodType: string) => {
    switch (methodType) {
      case 'bank_account':
        return 'Direct bank transfer (domestic)';
      case 'paypal':
        return 'PayPal account for international transfers';
      case 'stripe_connect':
        return 'Stripe Connect for fast payouts';
      case 'wise':
        return 'Wise for low-cost international transfers';
      case 'international_wire':
        return 'International wire transfer via SWIFT';
      default:
        return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {editingMethod ? 'Edit Payout Method' : 'Add Payout Method'}
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              Step {currentStep} of 4
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep
                    ? 'bg-brown-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                {step < 4 && (
                  <div className={`w-12 h-1 mx-2 ${
                    step < currentStep ? 'bg-brown-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mx-6 mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
          </div>
        )}

        {/* Form Content */}
        <div className="p-6">
          {/* Step 1: Method Type */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Choose Payout Method Type
                </h3>
                <div className="grid grid-cols-1 gap-4">
                  {[
                    { type: 'bank_account', name: 'Bank Account', available: true },
                    { type: 'paypal', name: 'PayPal', available: true },
                    { type: 'stripe_connect', name: 'Stripe Connect', available: false },
                    { type: 'wise', name: 'Wise (TransferWise)', available: false },
                    { type: 'international_wire', name: 'International Wire', available: true },
                  ].map((method) => (
                    <div
                      key={method.type}
                      className={`border rounded-lg p-4 cursor-pointer transition-all ${
                        formData.method_type === method.type
                          ? 'border-brown-500 bg-brown-50'
                          : method.available
                          ? 'border-gray-200 hover:border-gray-300'
                          : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-50'
                      }`}
                      onClick={() => method.available && handleInputChange('method_type', method.type)}
                    >
                      <div className="flex items-center space-x-3">
                        {getMethodIcon(method.type)}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium text-gray-900">{method.name}</h4>
                            {!method.available && (
                              <span className="px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded-full">
                                Coming Soon
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-500">
                            {getMethodDescription(method.type)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Basic Information */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Basic Information
              </h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Account Holder Name *
                </label>
                <input
                  type="text"
                  value={formData.account_holder_name}
                  onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  placeholder="Full name as it appears on the account"
                />
              </div>

              {formData.method_type === 'paypal' ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    PayPal Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.paypal_email}
                    onChange={(e) => handleInputChange('paypal_email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              ) : (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bank Name *
                    </label>
                    <input
                      type="text"
                      value={formData.bank_name}
                      onChange={(e) => handleInputChange('bank_name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      placeholder="Name of your bank"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Bank Country *
                      </label>
                      <select
                        value={formData.bank_country}
                        onChange={(e) => handleInputChange('bank_country', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      >
                        {COUNTRIES.map((country) => (
                          <option key={country.code} value={country.code}>
                            {country.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                      </label>
                      <input
                        type="text"
                        value={formData.bank_currency}
                        onChange={(e) => handleInputChange('bank_currency', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                        placeholder="USD"
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Step 3: Account Details */}
          {currentStep === 3 && formData.method_type !== 'paypal' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Account Details
              </h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Account Number *
                </label>
                <input
                  type="text"
                  value={formData.account_number}
                  onChange={(e) => handleInputChange('account_number', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  placeholder="Your account number"
                />
                <div className="mt-1 flex items-start space-x-2">
                  <Info className="h-4 w-4 text-blue-500 mt-0.5" />
                  <p className="text-xs text-gray-500">
                    This information will be encrypted and stored securely
                  </p>
                </div>
              </div>

              {formData.method_type === 'bank_account' && formData.bank_country === 'US' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Routing Number *
                  </label>
                  <input
                    type="text"
                    value={formData.routing_number}
                    onChange={(e) => handleInputChange('routing_number', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    placeholder="9-digit routing number"
                    maxLength={9}
                  />
                </div>
              )}

              {formData.method_type === 'international_wire' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SWIFT Code *
                    </label>
                    <input
                      type="text"
                      value={formData.swift_code}
                      onChange={(e) => handleInputChange('swift_code', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      placeholder="8 or 11 character SWIFT code"
                      maxLength={11}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      IBAN (if applicable)
                    </label>
                    <input
                      type="text"
                      value={formData.iban}
                      onChange={(e) => handleInputChange('iban', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      placeholder="International Bank Account Number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bank Address
                    </label>
                    <textarea
                      value={formData.bank_address}
                      onChange={(e) => handleInputChange('bank_address', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      placeholder="Full bank address including city, state/province, postal code, and country"
                    />
                  </div>
                </>
              )}

              {formData.method_type === 'bank_account' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Account Type
                  </label>
                  <select
                    value={formData.account_type}
                    onChange={(e) => handleInputChange('account_type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  >
                    <option value="checking">Checking</option>
                    <option value="savings">Savings</option>
                    <option value="business">Business</option>
                  </select>
                </div>
              )}
            </div>
          )}

          {/* Step 4: Preferences */}
          {currentStep === 4 || (currentStep === 3 && formData.method_type === 'paypal') && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Payout Preferences
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Payout Amount
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-2 text-gray-500">$</span>
                    <input
                      type="number"
                      min="50"
                      step="10"
                      value={formData.minimum_payout_amount}
                      onChange={(e) => handleInputChange('minimum_payout_amount', parseFloat(e.target.value) || 100)}
                      className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Minimum: $50
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payout Frequency
                  </label>
                  <select
                    value={formData.payout_frequency}
                    onChange={(e) => handleInputChange('payout_frequency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  >
                    <option value="weekly">Weekly</option>
                    <option value="bi_weekly">Bi-weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="auto_payout"
                    checked={formData.auto_payout_enabled}
                    onChange={(e) => handleInputChange('auto_payout_enabled', e.target.checked)}
                    className="h-4 w-4 text-brown-600 focus:ring-brown-500 border-gray-300 rounded"
                  />
                  <label htmlFor="auto_payout" className="ml-2 block text-sm text-gray-900">
                    Enable automatic payouts
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_default"
                    checked={formData.is_default}
                    onChange={(e) => handleInputChange('is_default', e.target.checked)}
                    className="h-4 w-4 text-brown-600 focus:ring-brown-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_default" className="ml-2 block text-sm text-gray-900">
                    Set as default payout method
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-2">
                  <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900">Verification Required</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Your payout method will need to be verified before you can receive payments.
                      We'll send you instructions after you save this information.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <div className="flex space-x-3">
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={() => setCurrentStep(currentStep - 1)}
                disabled={loading}
              >
                Previous
              </Button>
            )}
          </div>
          
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            
            {currentStep < 4 ? (
              <Button
                onClick={() => setCurrentStep(currentStep + 1)}
                disabled={!validateStep(currentStep) || loading}
                className="bg-brown-600 hover:bg-brown-700 text-white"
              >
                Next
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={loading}
                className="bg-brown-600 hover:bg-brown-700 text-white"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  editingMethod ? 'Update Method' : 'Add Method'
                )}
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
