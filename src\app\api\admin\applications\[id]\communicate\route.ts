import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * API route for sending custom communications to designer applicants
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { 
      subject, 
      content, 
      communication_type = 'email',
      template_id,
      send_copy_to_admin = false 
    } = await request.json();

    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Get application details
    const { data: application, error: appError } = await supabase
      .from('designer_applications')
      .select('*')
      .eq('id', id)
      .single();

    if (appError || !application) {
      return NextResponse.json({ error: 'Application not found' }, { status: 404 });
    }

    let emailContent = content;
    let emailSubject = subject;

    // If using a template, fetch and process it
    if (template_id) {
      const { data: template, error: templateError } = await supabase
        .from('email_templates')
        .select('*')
        .eq('id', template_id)
        .single();

      if (!templateError && template) {
        emailSubject = template.subject;
        emailContent = template.content;

        // Replace template variables
        const variables = {
          full_name: application.full_name,
          email: application.email,
          specialization: application.specialization,
          application_date: new Date(application.created_at).toLocaleDateString(),
          review_timeframe: '5-7 business days'
        };

        Object.entries(variables).forEach(([key, value]) => {
          const regex = new RegExp(`{{${key}}}`, 'g');
          emailSubject = emailSubject.replace(regex, value || '');
          emailContent = emailContent.replace(regex, value || '');
        });
      }
    }

    // Send email
    let emailResult;
    if (communication_type === 'email') {
      const recipients = [application.email];
      if (send_copy_to_admin) {
        recipients.push('<EMAIL>');
      }

      emailResult = await resend.emails.send({
        from: 'Seniors Architecture Firm <<EMAIL>>',
        to: recipients,
        subject: emailSubject,
        html: emailContent
      });
    }

    // Record communication in database
    const { data: communication, error: commError } = await supabase
      .from('application_communications')
      .insert({
        application_id: id,
        communication_type,
        subject: emailSubject,
        content: emailContent,
        sent_by: user.id,
        recipient_email: application.email,
        status: emailResult?.error ? 'failed' : 'sent',
        template_used: template_id,
        metadata: emailResult ? { resend_id: emailResult.data?.id } : null
      })
      .select()
      .single();

    if (commError) {
      console.error('Error recording communication:', commError);
    }

    // Update application if status change is implied
    if (subject.toLowerCase().includes('under review') || content.toLowerCase().includes('under review')) {
      await supabase
        .from('designer_applications')
        .update({ 
          application_status: 'under_review',
          status_updated_by: user.id
        })
        .eq('id', id);
    }

    return NextResponse.json({
      success: true,
      communication_id: communication?.id,
      email_id: emailResult?.data?.id,
      message: 'Communication sent successfully'
    });

  } catch (error) {
    console.error('Error sending communication:', error);
    return NextResponse.json(
      { error: 'Failed to send communication' },
      { status: 500 }
    );
  }
}

/**
 * GET - Fetch communication history for an application
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Fetch communication history
    const { data: communications, error } = await supabase
      .from('application_communications')
      .select(`
        *,
        sent_by_profile:profiles!sent_by(full_name, email)
      `)
      .eq('application_id', id)
      .order('sent_at', { ascending: false });

    if (error) {
      console.error('Error fetching communications:', error);
      return NextResponse.json({ error: 'Failed to fetch communications' }, { status: 500 });
    }

    return NextResponse.json({ communications });

  } catch (error) {
    console.error('Error in GET /api/admin/applications/[id]/communicate:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
