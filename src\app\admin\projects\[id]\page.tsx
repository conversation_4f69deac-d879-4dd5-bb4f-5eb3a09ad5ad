"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import {
  ArrowLeft,
  Calendar,
  Clock,
  Users,
  FileText,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  BarChart,
  Edit,
  MapPin,
  Tag,
  DollarSign,
  FileImage,
  Layers,
  Plus
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  description: string;
  status: string;
  client_id: string;
  client_name: string;
  client_email: string;
  client_avatar: string | null;
  designer_id: string | null;
  designer_name: string | null;
  designer_email: string | null;
  designer_avatar: string | null;
  deadline: string | null;
  created_at: string;
  updated_at: string;
  progress: number;
  requirements: string | null;
  budget: number | null;
  type: string | null;
  location: string | null;
};

type Submission = {
  id: string;
  title: string;
  description: string | null;
  status: string;
  created_at: string;
  version: number;
};

type Milestone = {
  id: string;
  title: string;
  description: string | null;
  amount: number;
  status: string;
  due_date: string | null;
  completed_at: string | null;
  paid_at: string | null;
};

export default function ProjectDetail() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [project, setProject] = useState<Project | null>(null);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user && projectId) {
      fetchProjectData();
    }
  }, [user, projectId]);

  const fetchProjectData = async () => {
    setLoading(true);
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          status,
          deadline,
          created_at,
          updated_at,
          progress,
          requirements,
          budget,
          type,
          location,
          client_id,
          designer_id,
          client:profiles!client_id(full_name, email, avatar_url),
          designer:profiles!designer_id(full_name, email, avatar_url)
        `)
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      setProject({
        id: projectData.id,
        title: projectData.title,
        description: projectData.description || "",
        status: projectData.status,
        client_id: projectData.client_id,
        client_name: projectData.client?.full_name || 'Unknown Client',
        client_email: projectData.client?.email || '',
        client_avatar: projectData.client?.avatar_url || null,
        designer_id: projectData.designer_id,
        designer_name: projectData.designer?.full_name || null,
        designer_email: projectData.designer?.email || null,
        designer_avatar: projectData.designer?.avatar_url || null,
        deadline: projectData.deadline,
        created_at: projectData.created_at,
        updated_at: projectData.updated_at,
        progress: projectData.progress || 0,
        requirements: projectData.requirements,
        budget: projectData.budget,
        type: projectData.type,
        location: projectData.location
      });

      // Fetch design submissions
      const { data: submissionsData, error: submissionsError } = await supabase
        .from('design_submissions')
        .select('id, title, description, status, created_at, version')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (submissionsError) throw submissionsError;
      setSubmissions(submissionsData || []);

      // Fetch milestones (if they exist in your database)
      // This is a placeholder - you'll need to implement the actual milestone table
      try {
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('project_milestones')
          .select('*')
          .eq('project_id', projectId)
          .order('due_date', { ascending: true });

        if (!milestonesError) {
          setMilestones(milestonesData || []);
        }
      } catch (error) {
        console.log('Milestones table may not exist yet:', error);
        // Don't throw error here, as milestones might not be implemented yet
      }
    } catch (error: Error | unknown) {
      console.error('Error fetching project data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return 'Not set';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
      case 'in_progress':
        return 'bg-brown-100 text-brown-800';
      case 'draft':
      case 'submitted':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'needs_revision':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatus = (status: string) => {
    return status.replace('_', ' ').split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading project data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            Project not found
          </p>
          <div className="mt-4">
            <Link href="/admin/projects">
              <Button variant="outline">
                Back to Projects
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center">
        <Link href="/admin/projects" className="mr-4">
          <Button variant="ghost" className="p-0 h-auto">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-2xl font-bold">{project.title}</h1>
          <div className="flex items-center mt-1">
            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.status)}`}>
              {formatStatus(project.status)}
            </span>
            <span className="mx-2 text-gray-400">•</span>
            <span className="text-sm text-gray-500">Created on {formatDate(project.created_at)}</span>
          </div>
        </div>
        <Link href={`/admin/projects/${projectId}/edit`}>
          <Button className="flex items-center">
            <Edit className="h-4 w-4 mr-2" />
            Edit Project
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-8">
          {/* Project Details */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Project Details</h2>
            </div>
            <div className="p-6">
              <p className="text-gray-700 mb-6">{project.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-start">
                  <Tag className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Project Type</p>
                    <p className="font-medium">{project.type || 'Not specified'}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Location</p>
                    <p className="font-medium">{project.location || 'Not specified'}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <DollarSign className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Budget</p>
                    <p className="font-medium">{formatCurrency(project.budget)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Deadline</p>
                    <p className="font-medium">{formatDate(project.deadline)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Progress</p>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                      <div
                        className="bg-primary h-2.5 rounded-full"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{project.progress}% Complete</p>
                  </div>
                </div>
              </div>

              {project.requirements && (
                <div className="mt-6">
                  <h3 className="text-md font-medium mb-2">Project Requirements</h3>
                  <p className="text-gray-700 whitespace-pre-line">{project.requirements}</p>
                </div>
              )}
            </div>
          </div>

          {/* Design Submissions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b flex justify-between items-center">
              <h2 className="text-lg font-semibold">Design Submissions</h2>
              <span className="text-sm text-gray-500">{submissions.length} submissions</span>
            </div>
            <div className="p-6">
              {submissions.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No submissions yet
                </div>
              ) : (
                <div className="space-y-6">
                  {submissions.map((submission) => (
                    <div key={submission.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{submission.title}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(submission.status)}`}>
                          {formatStatus(submission.status)}
                        </span>
                      </div>
                      {submission.description && (
                        <p className="text-sm text-gray-700 mb-3">{submission.description}</p>
                      )}
                      <div className="flex justify-between items-center text-sm text-gray-500">
                        <div>Version {submission.version}</div>
                        <div>{formatDate(submission.created_at)}</div>
                      </div>
                      <div className="mt-3">
                        <Link href={`/admin/projects/${projectId}/submissions/${submission.id}`}>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Payment Milestones */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b flex justify-between items-center">
              <h2 className="text-lg font-semibold">Payment Milestones</h2>
              <Link href={`/admin/projects/${projectId}/milestones/new`}>
                <Button size="sm" className="flex items-center">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Milestone
                </Button>
              </Link>
            </div>
            <div className="p-6">
              {milestones.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No payment milestones defined yet.</p>
                  <p className="text-sm mt-2">Define milestones to track project payments.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {milestones.map((milestone) => (
                    <div key={milestone.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{milestone.title}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(milestone.status)}`}>
                          {formatStatus(milestone.status)}
                        </span>
                      </div>
                      {milestone.description && (
                        <p className="text-sm text-gray-700 mb-3">{milestone.description}</p>
                      )}
                      <div className="flex justify-between items-center text-sm">
                        <div className="font-medium">{formatCurrency(milestone.amount)}</div>
                        <div className="text-gray-500">Due: {formatDate(milestone.due_date)}</div>
                      </div>
                      {milestone.completed_at && (
                        <div className="mt-2 text-xs text-gray-500">
                          Completed on: {formatDate(milestone.completed_at)}
                        </div>
                      )}
                      {milestone.paid_at && (
                        <div className="mt-1 text-xs text-green-600">
                          Paid on: {formatDate(milestone.paid_at)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-8">
          {/* Client & Designer Info */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Project Team</h2>
            </div>
            <div className="p-6">
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 mb-3">Client</h3>
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                    {project.client_avatar ? (
                      <img
                        src={project.client_avatar}
                        alt={project.client_name}
                        className="h-10 w-10 rounded-full object-cover"
                      />
                    ) : (
                      <Users className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium">{project.client_name}</p>
                    <p className="text-xs text-gray-500">{project.client_email}</p>
                  </div>
                </div>
                <div className="mt-3">
                  <Link href={`/admin/users/${project.client_id}`}>
                    <Button variant="outline" size="sm" className="w-full">
                      View Client Profile
                    </Button>
                  </Link>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-3">Designer</h3>
                {project.designer_id ? (
                  <div>
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                        {project.designer_avatar ? (
                          <img
                            src={project.designer_avatar}
                            alt={project.designer_name || ''}
                            className="h-10 w-10 rounded-full object-cover"
                          />
                        ) : (
                          <Users className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium">{project.designer_name}</p>
                        <p className="text-xs text-gray-500">{project.designer_email}</p>
                      </div>
                    </div>
                    <div className="mt-3">
                      <Link href={`/admin/users/${project.designer_id}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          View Designer Profile
                        </Button>
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4 border rounded-lg">
                    <p className="text-gray-500 mb-3">No designer assigned</p>
                    <Link href={`/admin/projects/${projectId}/assign`}>
                      <Button size="sm">
                        Assign Designer
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Project Actions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Project Actions</h2>
            </div>
            <div className="p-6 space-y-3">
              <Link href={`/admin/projects/${projectId}/edit`}>
                <Button variant="outline" className="w-full justify-between">
                  Edit Project Details
                  <Edit className="h-4 w-4 ml-2" />
                </Button>
              </Link>

              <Link href={`/admin/projects/${projectId}/milestones`}>
                <Button variant="outline" className="w-full justify-between">
                  Manage Milestones
                  <Layers className="h-4 w-4 ml-2" />
                </Button>
              </Link>

              <Link href={`/admin/projects/${projectId}/submissions`}>
                <Button variant="outline" className="w-full justify-between">
                  View All Submissions
                  <FileImage className="h-4 w-4 ml-2" />
                </Button>
              </Link>

              <Link href={`/admin/messages?project=${projectId}`}>
                <Button variant="outline" className="w-full justify-between">
                  Project Messages
                  <MessageSquare className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Project Timeline */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Project Timeline</h2>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="relative pl-6 border-l-2 border-gray-200">
                  <div className="absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full bg-primary"></div>
                  <p className="text-sm font-medium">Project Created</p>
                  <p className="text-xs text-gray-500">{formatDate(project.created_at)}</p>
                </div>

                {submissions.length > 0 && (
                  <div className="relative pl-6 border-l-2 border-gray-200">
                    <div className="absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full bg-blue-500"></div>
                    <p className="text-sm font-medium">First Submission</p>
                    <p className="text-xs text-gray-500">{formatDate(submissions[submissions.length - 1].created_at)}</p>
                  </div>
                )}

                {submissions.length > 0 && submissions[0].status === 'approved' && (
                  <div className="relative pl-6 border-l-2 border-gray-200">
                    <div className="absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full bg-green-500"></div>
                    <p className="text-sm font-medium">Latest Submission Approved</p>
                    <p className="text-xs text-gray-500">{formatDate(submissions[0].created_at)}</p>
                  </div>
                )}

                {project.status === 'completed' && (
                  <div className="relative pl-6">
                    <div className="absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full bg-green-500"></div>
                    <p className="text-sm font-medium">Project Completed</p>
                    <p className="text-xs text-gray-500">{formatDate(project.updated_at)}</p>
                  </div>
                )}

                {project.status !== 'completed' && project.deadline && (
                  <div className="relative pl-6">
                    <div className="absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full bg-yellow-500"></div>
                    <p className="text-sm font-medium">Project Deadline</p>
                    <p className="text-xs text-gray-500">{formatDate(project.deadline)}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
