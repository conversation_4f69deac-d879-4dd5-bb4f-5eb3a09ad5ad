-- Add payment_type column to payment_methods table
ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS payment_type VARCHAR(20) DEFAULT 'card';

-- Add PayPal-specific columns
ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS paypal_email VARCHAR(255);
ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS paypal_payer_id VARCHAR(255);

-- Add last_used_at column
ALTER TABLE payment_methods ADD COLUMN IF NOT EXISTS last_used_at TIMESTAMP WITH TIME ZONE;

-- Update existing records to set payment_type to 'card'
UPDATE payment_methods SET payment_type = 'card' WHERE payment_type IS NULL;

-- Create an index on payment_type for faster queries
CREATE INDEX IF NOT EXISTS idx_payment_methods_payment_type ON payment_methods(payment_type);

-- Create an index on user_id and payment_type for faster lookups
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_payment_type ON payment_methods(user_id, payment_type);

-- Add a comment to the table
COMMENT ON TABLE payment_methods IS 'Stores payment methods for users, including credit cards and PayPal accounts';

-- Add comments to the columns
COMMENT ON COLUMN payment_methods.payment_type IS 'Type of payment method (card, paypal, etc.)';
COMMENT ON COLUMN payment_methods.paypal_email IS 'Email address associated with PayPal account';
COMMENT ON COLUMN payment_methods.paypal_payer_id IS 'PayPal payer ID for the account';
COMMENT ON COLUMN payment_methods.last_used_at IS 'Timestamp when this payment method was last used';
