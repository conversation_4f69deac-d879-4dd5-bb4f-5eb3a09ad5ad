"use client";

import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import WorkflowNotifications from "@/components/WorkflowNotifications";
import { LogoIcon } from "@/components/shared/LogoIcon";
import { CollapsibleSidebar, useSidebarCollapsed } from "@/components/shared/CollapsibleSidebar";
import { EnhancedQualitySidebar } from "@/components/quality/EnhancedQualitySidebar";
import { UnifiedMobileNavigation } from "@/components/mobile/UnifiedMobileNavigation";
import { useMobileNavigation, useResponsiveNavigation } from "@/hooks/useMobileNavigation";
import {
  Star,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  CheckCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  Users,
  Home,
  User
} from "lucide-react";

const navigation = [
  { name: "Dashboard", href: "/quality/dashboard", icon: BarChart3 },
  { name: "Pending Reviews", href: "/quality/reviews", icon: Clock },
  { name: "Quality Standards", href: "/quality/standards", icon: Star },
  { name: "Review History", href: "/quality/history", icon: FileText },
  { name: "Analytics", href: "/quality/analytics", icon: BarChart3 },
  { name: "Team Performance", href: "/quality/performance", icon: TrendingUp },
  { name: "Settings", href: "/quality/settings", icon: Settings },
];

export default function QualityLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, profile, isLoading, signOut } = useOptimizedAuth();
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Use unified mobile navigation - disable auto-close since UnifiedMobileNavigation handles it
  const mobileNav = useMobileNavigation({ autoCloseOnRouteChange: false });
  const { isMobile } = useResponsiveNavigation();

  // Track sidebar collapsed state
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Load initial collapsed state
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-collapsed-quality');
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState));
    }
  }, []);

  // Only show loading if session is actually loading (prevents loading screens on tab changes)
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-brown-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Check authentication and role access
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h1>
          <p className="text-gray-600 mb-6">Please sign in to access the quality dashboard.</p>
          <a
            href="/auth/login"
            className="bg-brown-600 text-white px-6 py-2 rounded-lg hover:bg-brown-700 transition-colors"
          >
            Sign In
          </a>
        </div>
      </div>
    );
  }

  // Only check role if profile loaded successfully AND we're not loading
  // This prevents cached role issues during authentication transitions
  if (!isLoading && profile && profile.role !== "quality_team" && profile.role !== "admin") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You don't have permission to access the quality dashboard.</p>
          <p className="text-sm text-gray-500 mb-6">Your role: {profile.role}</p>
          <p className="text-xs text-gray-400 mb-6">
            If you just logged in, please <a href="/auth/login" className="text-brown-600 underline">try logging in again</a>
          </p>
          <a
            href="/"
            className="bg-brown-600 text-white px-6 py-2 rounded-lg hover:bg-brown-700 transition-colors"
          >
            Go Home
          </a>
        </div>
      </div>
    );
  }

  // If profile is null (failed to load), show a warning but allow access
  if (user && !profile) {
    console.warn('Quality dashboard: User authenticated but profile failed to load');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Unified Mobile Navigation */}
      {isMobile && (
        <UnifiedMobileNavigation
          isOpen={mobileNav.isOpen}
          onToggle={mobileNav.toggle}
          onClose={mobileNav.close}
          variant="quality"
        />
      )}

      {/* Enhanced Quality Sidebar - Hidden on Mobile */}
      <div className="hidden lg:block">
        <CollapsibleSidebar
          role="quality"
          className="bg-white"
          onCollapseChange={setIsCollapsed}
        >
          <EnhancedQualitySidebar />
        </CollapsibleSidebar>
      </div>





      {/* Main content - Responsive to sidebar state */}
      <div className={`${isCollapsed ? 'lg:pl-16' : 'lg:pl-80'}`}>
        {/* Top bar - Hidden on mobile since UnifiedMobileNavigation handles it */}
        <div className="sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm hidden lg:block">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-brown-600 flex items-center justify-center">
                  <span className="text-sm font-medium text-white">
                    {profile?.full_name?.charAt(0) || 'Q'}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-700">{profile?.full_name}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className={`flex-1 ${isMobile ? 'pt-16' : ''}`}>
          {children}
        </main>
      </div>
    </div>
  );
}
