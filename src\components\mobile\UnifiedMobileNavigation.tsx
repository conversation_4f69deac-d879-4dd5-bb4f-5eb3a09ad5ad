"use client";

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import {
  Menu,
  X,
  Home,
  FolderKanban,
  Users,
  MessageSquare,
  FileText,
  Settings,
  User,
  LogOut,
  Bell,
  Search,
  ChevronRight,
  Calendar,
  BarChart3,
  Briefcase,
  DollarSign,
  FileSearch,
  Clock,
  Star,
  TrendingUp,
  CreditCard,
  Palette,
  UserCheck,
  Image,
  Target
} from 'lucide-react';

interface UnifiedMobileNavigationProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
  variant?: 'designer' | 'client' | 'admin' | 'quality' | 'manager';
}

interface NavItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  children?: NavItem[];
}

export function UnifiedMobileNavigation({ 
  isOpen, 
  onToggle, 
  onClose, 
  variant = 'designer' 
}: UnifiedMobileNavigationProps) {
  const pathname = usePathname();
  const { user, profile, signOut } = useOptimizedAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Stable close function to prevent unnecessary re-renders
  const stableClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Enhanced body scroll management with conflict prevention
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const body = document.body;
    const originalOverflow = body.style.overflow;
    const originalPosition = body.style.position;
    const originalWidth = body.style.width;
    const originalTop = body.style.top;

    if (isOpen) {
      // Store current scroll position
      const scrollY = window.scrollY;

      // Prevent scroll on mobile with better iOS support
      body.style.overflow = 'hidden';
      body.style.position = 'fixed';
      body.style.width = '100%';
      body.style.top = `-${scrollY}px`;
    } else {
      // Restore scroll position
      const scrollY = body.style.top;
      body.style.overflow = originalOverflow;
      body.style.position = originalPosition;
      body.style.width = originalWidth;
      body.style.top = originalTop;

      // Restore scroll position
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0') * -1);
      }
    }

    return () => {
      // Cleanup on unmount
      body.style.overflow = originalOverflow;
      body.style.position = originalPosition;
      body.style.width = originalWidth;
      body.style.top = originalTop;
    };
  }, [isOpen]);

  const getNavigationItems = (): NavItem[] => {
    switch (variant) {
      case 'designer':
        return [
          {
            label: 'Dashboard',
            href: '/designer/dashboard',
            icon: Home,
          },
          {
            label: 'Projects',
            href: '/designer/projects',
            icon: FolderKanban,
            children: [
              { label: 'All Projects', href: '/designer/projects', icon: FolderKanban },
              { label: 'Active Projects', href: '/designer/projects?status=active', icon: Briefcase },
              { label: 'Completed', href: '/designer/projects?status=completed', icon: BarChart3 },
            ]
          },
          {
            label: 'Clients',
            href: '/designer/clients',
            icon: Users,
          },
          {
            label: 'Messages',
            href: '/designer/messages',
            icon: MessageSquare,
            badge: 0, // TODO: Get from real data
          },
          {
            label: 'Proposals',
            href: '/designer/proposals',
            icon: FileText,
          },
          {
            label: 'Briefs',
            href: '/designer/briefs',
            icon: Briefcase,
          },
          {
            label: 'Portfolio',
            href: '/designer/portfolio',
            icon: Image,
          },
          {
            label: 'Availability',
            href: '/designer/availability',
            icon: Calendar,
          },
          {
            label: 'Settings',
            href: '/designer/settings',
            icon: Settings,
          },
        ];
      case 'client':
        return [
          {
            label: 'Dashboard',
            href: '/client/dashboard',
            icon: Home,
          },
          {
            label: 'Projects',
            href: '/client/projects',
            icon: FolderKanban,
          },
          {
            label: 'Proposals',
            href: '/client/proposals',
            icon: FileText,
          },
          {
            label: 'Briefs',
            href: '/client/briefs',
            icon: Briefcase,
          },
          {
            label: 'Designers',
            href: '/client/designers',
            icon: UserCheck,
          },
          {
            label: 'Messages',
            href: '/client/messages',
            icon: MessageSquare,
          },
          {
            label: 'Payments',
            href: '/client/payments',
            icon: CreditCard,
          },
          {
            label: 'Profile',
            href: '/client/profile',
            icon: User,
          },
        ];
      case 'admin':
        return [
          {
            label: 'Dashboard',
            href: '/admin/dashboard',
            icon: Home,
          },
          {
            label: 'Users',
            href: '/admin/users',
            icon: Users,
          },
          {
            label: 'Projects',
            href: '/admin/projects',
            icon: FolderKanban,
          },
          {
            label: 'Proposals',
            href: '/admin/proposals',
            icon: FileText,
          },
          {
            label: 'Designers',
            href: '/admin/designers',
            icon: Palette,
          },
          {
            label: 'Messages',
            href: '/admin/messages',
            icon: MessageSquare,
          },
          {
            label: 'Finance',
            href: '/admin/finance',
            icon: DollarSign,
          },
          {
            label: 'Settings',
            href: '/admin/settings',
            icon: Settings,
          },
        ];
      case 'quality':
        return [
          {
            label: 'Dashboard',
            href: '/quality/dashboard',
            icon: BarChart3,
          },
          {
            label: 'Pending Reviews',
            href: '/quality/reviews',
            icon: Clock,
          },
          {
            label: 'Quality Standards',
            href: '/quality/standards',
            icon: Star,
          },
          {
            label: 'Review History',
            href: '/quality/history',
            icon: FileText,
          },
          {
            label: 'Analytics',
            href: '/quality/analytics',
            icon: BarChart3,
          },
          {
            label: 'Team Performance',
            href: '/quality/performance',
            icon: TrendingUp,
          },
          {
            label: 'Settings',
            href: '/quality/settings',
            icon: Settings,
          },
        ];
      case 'manager':
        return [
          {
            label: 'Dashboard',
            href: '/manager/dashboard',
            icon: BarChart3,
          },
          {
            label: 'My Projects',
            href: '/manager/projects',
            icon: Briefcase,
          },
          {
            label: 'Negotiations',
            href: '/manager/negotiations',
            icon: MessageSquare,
          },
          {
            label: 'Escrow Management',
            href: '/manager/escrow',
            icon: DollarSign,
          },
          {
            label: 'Client Satisfaction',
            href: '/manager/satisfaction',
            icon: Star,
          },
          {
            label: 'Team Coordination',
            href: '/manager/coordination',
            icon: Users,
          },
          {
            label: 'Reports & Analytics',
            href: '/manager/reports',
            icon: BarChart3,
          },
          {
            label: 'Calendar',
            href: '/manager/calendar',
            icon: Calendar,
          },
          {
            label: 'Settings',
            href: '/manager/settings',
            icon: Settings,
          },
        ];
      default:
        return [];
    }
  };

  const navigationItems = getNavigationItems();

  const toggleExpanded = (label: string) => {
    setExpandedItems(prev => 
      prev.includes(label) 
        ? prev.filter(item => item !== label)
        : [...prev, label]
    );
  };

  const handleSignOut = async () => {
    await signOut();
    stableClose();
  };

  const handleMenuToggle = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onToggle();
  };

  const handleBackdropClick = (e: React.MouseEvent | React.TouchEvent) => {
    // Only close if clicking the backdrop itself, not child elements
    if (e.target === e.currentTarget) {
      e.preventDefault();
      e.stopPropagation();
      stableClose();
    }
  };

  const handleCloseClick = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    stableClose();
  };

  const handleNavItemClick = (e: React.MouseEvent | React.TouchEvent) => {
    // Don't prevent default for navigation links
    e.stopPropagation();
    // Close menu immediately when navigation link is clicked
    stableClose();
  };

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-[60] bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={handleMenuToggle}
            className="p-2 -ml-2 rounded-lg hover:bg-gray-100 transition-colors touch-manipulation"
            aria-label="Toggle navigation"
          >
            <Menu className="h-6 w-6 text-gray-700" />
          </button>
          
          <div className="flex items-center space-x-3">
            <button 
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors touch-manipulation"
              onClick={() => {
                console.log('Search clicked - functionality coming soon');
              }}
              title="Search (Coming Soon)"
            >
              <Search className="h-5 w-5 text-gray-400" />
            </button>
            <button 
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative touch-manipulation"
              onClick={() => {
                console.log('Notifications clicked - functionality coming soon');
              }}
              title="Notifications (Coming Soon)"
            >
              <Bell className="h-5 w-5 text-gray-400" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-gray-400 rounded-full text-xs text-white flex items-center justify-center opacity-50">
                !
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="lg:hidden fixed inset-0 z-[70] bg-black bg-opacity-50"
            onClick={handleBackdropClick}
            onTouchEnd={handleBackdropClick}
          />
        )}
      </AnimatePresence>

      {/* Slide-out Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{
              type: 'tween',
              duration: 0.3,
              ease: [0.4, 0.0, 0.2, 1] // Custom easing for smoother animation
            }}
            className="lg:hidden fixed top-0 left-0 bottom-0 z-[80] w-80 max-w-[85vw] bg-white shadow-xl"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-brown-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">
                    {variant === 'designer' ? 'D' :
                     variant === 'client' ? 'C' :
                     variant === 'admin' ? 'A' :
                     variant === 'quality' ? 'Q' : 'M'}
                  </span>
                </div>
                <span className="font-semibold text-gray-900">
                  {variant === 'designer' ? 'Designer Portal' :
                   variant === 'client' ? 'Client Portal' :
                   variant === 'admin' ? 'Admin Portal' :
                   variant === 'quality' ? 'Quality Portal' : 'Manager Portal'}
                </span>
              </div>
              <button
                onClick={handleCloseClick}
                onTouchEnd={handleCloseClick}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors touch-manipulation"
                aria-label="Close navigation"
              >
                <X className="h-6 w-6 text-gray-600" />
              </button>
            </div>

            {/* User Profile */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.full_name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm text-gray-900">
                    {profile?.full_name || 'User'}
                  </p>
                  <p className="text-xs text-gray-500 capitalize">
                    {profile?.role || variant}
                  </p>
                </div>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 overflow-y-auto py-2">
              {navigationItems.map((item) => (
                <div key={item.label}>
                  {item.children ? (
                    <div>
                      <button
                        onClick={() => toggleExpanded(item.label)}
                        className="w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 transition-colors touch-manipulation"
                      >
                        <div className="flex items-center space-x-3">
                          <item.icon className="h-5 w-5 text-gray-600" />
                          <span className="text-sm font-medium text-gray-900">
                            {item.label}
                          </span>
                          {item.badge && item.badge > 0 && (
                            <span className="bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </div>
                        <ChevronRight 
                          className={`h-4 w-4 text-gray-400 transition-transform ${
                            expandedItems.includes(item.label) ? 'rotate-90' : ''
                          }`}
                        />
                      </button>
                      <AnimatePresence>
                        {expandedItems.includes(item.label) && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="overflow-hidden bg-gray-50"
                          >
                            {item.children.map((child) => (
                              <Link
                                key={child.href}
                                href={child.href}
                                onClick={handleNavItemClick}
                                className={`flex items-center space-x-3 px-4 py-2 pl-12 text-sm hover:bg-gray-100 transition-colors touch-manipulation ${
                                  pathname === child.href
                                    ? 'text-brown-600 bg-brown-50 border-r-2 border-brown-600'
                                    : 'text-gray-700'
                                }`}
                              >
                                <child.icon className="h-4 w-4" />
                                <span>{child.label}</span>
                              </Link>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      onClick={handleNavItemClick}
                      className={`flex items-center justify-between px-4 py-3 hover:bg-gray-50 transition-colors touch-manipulation ${
                        pathname === item.href
                          ? 'text-brown-600 bg-brown-50 border-r-2 border-brown-600'
                          : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <item.icon className="h-5 w-5" />
                        <span className="text-sm font-medium">{item.label}</span>
                      </div>
                      {item.badge && item.badge > 0 && (
                        <span className="bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}
                </div>
              ))}
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200">
              <button
                onClick={handleSignOut}
                className="flex items-center w-full px-4 py-3 text-sm text-gray-700 rounded-md hover:bg-gray-100 transition-colors touch-manipulation"
              >
                <LogOut className="h-5 w-5 mr-3" />
                <span>Sign Out</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
