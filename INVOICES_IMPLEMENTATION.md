# Invoices Implementation Guide

This guide explains how to implement the invoices table and related functionality in your Supabase database.

## Overview

The client payments page (`src/app/client/payments/page.tsx`) references an `invoices` table that doesn't exist in your Supabase database. This implementation adds:

1. An `invoices` table to store invoice data
2. A `payment_methods` table (if it doesn't already exist)
3. Utility functions for working with invoices
4. Database triggers to automatically generate invoices when milestones are approved
5. A script to generate initial invoices from existing milestones

## Implementation Steps

### 1. Create the Database Tables

Run the SQL in `invoices_table_migration.sql` in your Supabase SQL Editor to create:
- The `invoices` table
- The `payment_methods` table (if it doesn't exist)
- Appropriate indexes and RLS policies

### 2. Set Up Database Triggers

Run the SQL in `supabase_triggers.sql` in your Supabase SQL Editor to create:
- A trigger that automatically creates an invoice when a milestone is approved
- A trigger that updates invoice status when a milestone is paid

### 3. Deploy the Supabase Function (Optional)

If you want to use the Edge Function approach instead of database triggers:

```bash
cd supabase
supabase functions deploy generate-invoice
```

### 4. Generate Initial Invoices

To populate your invoices table with data from existing milestones:

1. Make sure you have the required environment variables:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`

2. Run the script:
   ```bash
   node scripts/generate-initial-invoices.js
   ```

### 5. Use the Invoice Utilities

The `src/lib/invoiceUtils.ts` file contains utility functions for:
- Generating invoice numbers
- Creating invoices for milestones
- Updating invoice status
- Fetching invoices for clients and projects
- Checking for overdue invoices

You can import and use these functions in your application code.

## Data Model

### Invoice

```typescript
type Invoice = {
  id: string;
  invoice_number: string;
  amount: number;
  status: string; // 'pending', 'paid', 'overdue'
  due_date: string;
  issued_date: string;
  description: string;
  project_id: string;
  client_id: string;
  created_at: string;
  updated_at: string;
};
```

### Payment Method

```typescript
type PaymentMethod = {
  id: string;
  user_id: string;
  card_brand: string;
  last_four: string;
  expiry_date: string;
  is_default: boolean;
  stripe_payment_method_id?: string;
  created_at: string;
  updated_at: string;
};
```

## Workflow

1. When a milestone is approved, an invoice is automatically generated
2. The client can view and pay the invoice from their dashboard
3. When payment is made, the invoice status is updated to 'paid'
4. If an invoice is not paid by the due date, its status is updated to 'overdue'

## Testing

After implementing these changes, you should:

1. Check that the invoices table is created in your Supabase database
2. Verify that the RLS policies are working correctly
3. Test the automatic invoice generation by approving a milestone
4. Test the invoice payment flow
5. Verify that the client payments page displays invoices correctly
