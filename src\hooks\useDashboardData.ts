"use client";

import { useQuery, useMutation, useQueryClient, useQueries } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { toast } from 'react-hot-toast';

// Types
export type Project = {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  quality_status?: string;
  client_id: string;
  designer_id?: string;
  assigned_manager_id?: string;
  created_at: string;
  updated_at: string;
  budget?: number;
  deadline?: string;
  client?: {
    full_name: string;
    email: string;
  };
  designer?: {
    full_name: string;
    email: string;
  };
  manager?: {
    full_name: string;
    email: string;
  };
};

export type Proposal = {
  id: string;
  project_id: string;
  designer_id: string;
  title: string;
  description: string;
  budget: number;
  timeline: string;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  project?: {
    title: string;
    client_id: string;
  };
  designer?: {
    full_name: string;
    email: string;
  };
};

export type Message = {
  id: string;
  project_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  sender?: {
    full_name: string;
    role: string;
  };
};

// Enhanced query key factory with hierarchical structure for optimal cache management
export const dashboardKeys = {
  // Root keys
  all: ['dashboard'] as const,
  auth: ['auth'] as const,
  navigation: ['navigation'] as const,

  // Authentication keys
  session: () => [...dashboardKeys.auth, 'session'] as const,
  profile: (userId: string) => [...dashboardKeys.auth, 'profile', userId] as const,

  // Dashboard stats with role-based caching
  stats: (userId: string, role: string) => [...dashboardKeys.all, 'stats', userId, role] as const,
  batchedStats: (userId: string, role: string) => [...dashboardKeys.all, 'batched-stats', userId, role] as const,

  // Projects with enhanced filtering
  projects: () => [...dashboardKeys.all, 'projects'] as const,
  projectsByUser: (userId: string, role: string) => [...dashboardKeys.projects(), 'user', userId, role] as const,
  project: (projectId: string) => [...dashboardKeys.projects(), 'detail', projectId] as const,
  projectMilestones: (projectId: string) => [...dashboardKeys.projects(), 'milestones', projectId] as const,
  projectDetails: (projectId: string) => [...dashboardKeys.projects(), 'full-details', projectId] as const,

  // Proposals with enhanced relationships
  proposals: () => [...dashboardKeys.all, 'proposals'] as const,
  proposalsByUser: (userId: string, role: string) => [...dashboardKeys.proposals(), 'user', userId, role] as const,
  proposal: (proposalId: string) => [...dashboardKeys.proposals(), 'detail', proposalId] as const,
  proposalsByProject: (projectId: string) => [...dashboardKeys.proposals(), 'project', projectId] as const,

  // Messages with real-time considerations
  messages: () => [...dashboardKeys.all, 'messages'] as const,
  messagesByProject: (projectId: string) => [...dashboardKeys.messages(), 'project', projectId] as const,
  messagesByUser: (userId: string) => [...dashboardKeys.messages(), 'user', userId] as const,
  conversations: (userId: string) => [...dashboardKeys.messages(), 'conversations', userId] as const,

  // Project briefs
  briefs: () => [...dashboardKeys.all, 'briefs'] as const,
  briefsByUser: (userId: string, role: string) => [...dashboardKeys.briefs(), 'user', userId, role] as const,
  brief: (briefId: string) => [...dashboardKeys.briefs(), 'detail', briefId] as const,
  briefProposals: (briefId: string) => [...dashboardKeys.briefs(), 'proposals', briefId] as const,

  // Designers and users
  designers: () => [...dashboardKeys.all, 'designers'] as const,
  availableDesigners: () => [...dashboardKeys.designers(), 'available'] as const,
  designerProfile: (designerId: string) => [...dashboardKeys.designers(), 'profile', designerId] as const,
  connectedDesigners: (userId: string) => [...dashboardKeys.designers(), 'connected', userId] as const,
  connectedClients: (userId: string, role: string) => [...dashboardKeys.all, 'connected-clients', userId, role] as const,

  // Users management
  users: () => [...dashboardKeys.all, 'users'] as const,
  usersByRole: (role: string) => [...dashboardKeys.users(), 'role', role] as const,
  user: (userId: string) => [...dashboardKeys.users(), 'detail', userId] as const,

  // Portfolio
  portfolio: () => [...dashboardKeys.all, 'portfolio'] as const,
  portfolioByDesigner: (designerId: string) => [...dashboardKeys.portfolio(), 'designer', designerId] as const,
  portfolioProject: (projectId: string) => [...dashboardKeys.portfolio(), 'project', projectId] as const,

  // Navigation and routes
  routes: (role: string) => [...dashboardKeys.navigation, 'routes', role] as const,
  routeData: (route: string, userId: string, role: string) => [...dashboardKeys.navigation, 'route-data', route, userId, role] as const,

  // Notifications and admin messages
  notifications: (userId: string) => [...dashboardKeys.all, 'notifications', userId] as const,
  adminMessages: (userId: string) => [...dashboardKeys.all, 'admin-messages', userId] as const,

  // Payments and financial
  payments: () => [...dashboardKeys.all, 'payments'] as const,
  paymentsByUser: (userId: string) => [...dashboardKeys.payments(), 'user', userId] as const,
  paymentMethods: (userId: string) => [...dashboardKeys.payments(), 'methods', userId] as const,

  // Inspiration boards (client-specific)
  inspirations: () => [...dashboardKeys.all, 'inspirations'] as const,
  inspirationsByUser: (userId: string) => [...dashboardKeys.inspirations(), 'user', userId] as const,

  // Reviews and ratings
  reviews: () => [...dashboardKeys.all, 'reviews'] as const,
  reviewsByDesigner: (designerId: string) => [...dashboardKeys.reviews(), 'designer', designerId] as const,

  // Availability (designer-specific)
  availability: (designerId: string) => [...dashboardKeys.all, 'availability', designerId] as const,
};

// Projects hooks
export function useProjects(userId: string, role: 'client' | 'designer' | 'admin') {
  return useQuery({
    queryKey: dashboardKeys.projectsByUser(userId, role),
    queryFn: async () => {
      let query = supabase
        .from('projects')
        .select(`
          *,
          client:profiles!projects_client_id_fkey(full_name, email),
          designer:profiles!projects_designer_id_fkey(full_name, email),
          manager:profiles!projects_assigned_manager_id_fkey(full_name, email)
        `);

      // Filter based on role
      if (role === 'client') {
        query = query.eq('client_id', userId);
      } else if (role === 'designer') {
        query = query.eq('designer_id', userId);
      }
      // Admin sees all projects

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data as Project[];
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useProject(projectId: string) {
  return useQuery({
    queryKey: dashboardKeys.project(projectId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          client:profiles!projects_client_id_fkey(full_name, email),
          designer:profiles!projects_designer_id_fkey(full_name, email)
        `)
        .eq('id', projectId)
        .single();

      if (error) throw error;
      return data as Project;
    },
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Proposals hooks
export function useProposals(userId: string, role: 'client' | 'designer' | 'admin') {
  return useQuery({
    queryKey: dashboardKeys.proposalsByUser(userId, role),
    queryFn: async () => {
      if (!userId) return [];

      if (role === 'designer') {
        // For designers, get their proposals
        const { data, error } = await supabase
          .from('project_proposals_enhanced')
          .select(`
            *,
            project_briefs!brief_id(
              title,
              client_id,
              profiles!client_id(full_name, email)
            )
          `)
          .eq('designer_id', userId)
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data;
      } else if (role === 'client') {
        // For clients, get proposals for their briefs
        const { data: briefIds, error: briefError } = await supabase
          .from('project_briefs')
          .select('id')
          .eq('client_id', userId);

        if (briefError) throw briefError;
        if (!briefIds || briefIds.length === 0) return [];

        const { data, error } = await supabase
          .from('project_proposals_enhanced')
          .select(`
            *,
            project_briefs!brief_id(title),
            profiles!designer_id(full_name, email, avatar_url)
          `)
          .in('brief_id', briefIds.map(brief => brief.id))
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data;
      } else {
        // For admin, get all proposals
        const { data, error } = await supabase
          .from('project_proposals_enhanced')
          .select(`
            *,
            project_briefs!brief_id(title),
            profiles!designer_id(full_name, email)
          `)
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data;
      }
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// NEW CONVERSATIONS MODEL ONLY - Updated Messages hook
export function useMessages(conversationId: string) {
  return useQuery({
    queryKey: ['conversation_messages', conversationId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('conversation_messages')
        .select(`
          id,
          content,
          created_at,
          sender_id,
          message_type,
          reply_to_id,
          edited_at,
          profiles:sender_id(id, full_name, role, avatar_url)
        `)
        .eq('conversation_id', conversationId)
        .is('deleted_at', null)
        .order('created_at', { ascending: true })
        .limit(100);

      if (error) throw error;
      return data;
    },
    enabled: !!conversationId,
    staleTime: 2 * 60 * 1000, // 2 minutes for better performance
    gcTime: 10 * 60 * 1000,
  });
}

// NEW CONVERSATIONS MODEL - Get user's conversations
export function useConversations(userId: string, role: string) {
  return useQuery({
    queryKey: ['conversations', userId, role],
    queryFn: async () => {
      if (!userId) return [];

      const { data, error } = await supabase
        .from('conversations')
        .select(`
          id,
          type,
          title,
          project_id,
          created_at,
          updated_at,
          last_message_at,
          is_active,
          conversation_participants!inner(
            user_id,
            role,
            profiles:user_id(id, full_name, avatar_url, role)
          )
        `)
        .eq('conversation_participants.user_id', userId)
        .eq('is_active', true)
        .order('last_message_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

// NEW CONVERSATIONS MODEL - Get conversation messages (replaces useConversationMessages)
export function useConversationMessages(conversationId: string) {
  return useQuery({
    queryKey: ['conversation_messages', conversationId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('conversation_messages')
        .select(`
          id,
          content,
          created_at,
          sender_id,
          message_type,
          reply_to_id,
          edited_at,
          profiles:sender_id(id, full_name, role, avatar_url)
        `)
        .eq('conversation_id', conversationId)
        .is('deleted_at', null)
        .order('created_at', { ascending: true })
        .limit(100);

      if (error) throw error;
      return data;
    },
    enabled: !!conversationId,
    staleTime: 1 * 60 * 1000, // 1 minute for more real-time feel
    gcTime: 5 * 60 * 1000,
  });
}



// New: Portfolio projects hook
export function usePortfolioProjects(designerId: string) {
  return useQuery({
    queryKey: dashboardKeys.portfolioByDesigner(designerId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('portfolio_projects')
        .select(`
          id,
          title,
          description,
          category,
          featured,
          completion_date,
          client_name,
          portfolio_images(id, image_url, is_cover)
        `)
        .eq('designer_id', designerId)
        .order('featured', { ascending: false })
        .order('completion_date', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!designerId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000,
  });
}

// New: Admin messages hook (FIXED: removed non-existent columns)
export function useAdminMessages(userId: string) {
  return useQuery({
    queryKey: dashboardKeys.adminMessages(userId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('admin_messages')
        .select(`
          id,
          title,
          content,
          message_type,
          priority,
          expires_at,
          created_at,
          read_at,
          action_required
        `)
        .or(`recipient_id.eq.${userId},recipient_id.is.null`)
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000,
  });
}

// New: Designer availability hook
export function useDesignerAvailability(designerId: string) {
  return useQuery({
    queryKey: dashboardKeys.availability(designerId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('designer_availability')
        .select('*')
        .eq('designer_id', designerId)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // Ignore "not found" errors
      return data;
    },
    enabled: !!designerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000,
  });
}

// New: Reviews and ratings hook
export function useDesignerReviews(designerId: string) {
  return useQuery({
    queryKey: dashboardKeys.reviewsByDesigner(designerId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('project_reviews')
        .select(`
          id,
          rating,
          review_text,
          created_at,
          project:projects(title),
          client:profiles!client_id(full_name, avatar_url)
        `)
        .eq('designer_id', designerId)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      return data;
    },
    enabled: !!designerId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000,
  });
}

// Optimized batched stats hook - single query for better performance
export function useDashboardStats(userId: string, role: 'client' | 'designer' | 'admin') {
  return useQuery({
    queryKey: dashboardKeys.batchedStats(userId, role),
    queryFn: async () => {
      try {
        // Use Supabase RPC for optimized batch queries
        const { data, error } = await supabase.rpc('get_dashboard_stats_optimized', {
          user_id: userId,
          user_role: role
        });

        if (error) {
          // Fallback to individual queries if RPC doesn't exist
          console.warn('RPC function not found, falling back to individual queries');
          return await fetchStatsIndividually(userId, role);
        }

        return data;
      } catch (error) {
        // Fallback to individual queries
        console.warn('Error with RPC, falling back to individual queries:', error);
        return await fetchStatsIndividually(userId, role);
      }
    },
    enabled: !!userId,
    staleTime: 15 * 60 * 1000, // 15 minutes - stats don't change frequently
    gcTime: 30 * 60 * 1000, // 30 minutes
    // Performance optimizations
    refetchOnWindowFocus: false, // Reduce unnecessary refetches
    refetchOnMount: false, // Use cached data when available
    retry: 2, // Reduce retry attempts
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
}

// Fallback function for individual queries (optimized)
async function fetchStatsIndividually(userId: string, role: 'client' | 'designer' | 'admin') {
  const stats: any = {};

  if (role === 'client') {
    // Batch client queries using Promise.all for parallel execution
    const [
      projectsResult,
      connectionsResult,
      briefsResult,
      briefIdsResult
    ] = await Promise.all([
      // Get all project counts in one query
      supabase
        .from('projects')
        .select('status')
        .eq('client_id', userId),

      // Get connections count
      supabase
        .from('connections')
        .select('*', { count: 'exact', head: true })
        .eq('client_id', userId)
        .eq('status', 'active'),

      // Get briefs count
      supabase
        .from('project_briefs')
        .select('status')
        .eq('client_id', userId),

      // Get brief IDs for proposals
      supabase
        .from('project_briefs')
        .select('id')
        .eq('client_id', userId)
    ]);

    // Use database aggregation instead of JavaScript filtering
    const projects = projectsResult.data || [];
    stats.totalProjects = projects.length;

    // Count statuses efficiently
    const statusCounts = projects.reduce((acc, p) => {
      acc[p.status] = (acc[p.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    stats.activeProjects = statusCounts['in_progress'] || 0;
    stats.completedProjects = statusCounts['completed'] || 0;

    // Process briefs stats efficiently
    const briefs = briefsResult.data || [];
    const briefStatusCounts = briefs.reduce((acc, b) => {
      acc[b.status] = (acc[b.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    stats.activeBriefs = (briefStatusCounts['pending'] || 0) +
                        (briefStatusCounts['assigned'] || 0) +
                        (briefStatusCounts['proposal_received'] || 0);

    // Connected designers
    stats.connectedDesigners = connectionsResult.count || 0;

    // Get pending proposals count if we have briefs
    let pendingProposals = 0;
    const briefIds = briefIdsResult.data || [];
    if (briefIds.length > 0) {
      const { count } = await supabase
        .from('project_proposals_enhanced')
        .select('*', { count: 'exact', head: true })
        .in('brief_id', briefIds.map(brief => brief.id))
        .in('status', ['submitted', 'under_review']);
      pendingProposals = count || 0;
    }
    stats.pendingProposals = pendingProposals;

  } else if (role === 'designer') {
    // Batch designer queries
    const [projectsResult, proposalsResult] = await Promise.all([
      supabase
        .from('projects')
        .select('status')
        .eq('designer_id', userId),

      supabase
        .from('project_proposals_enhanced')
        .select('status')
        .eq('designer_id', userId)
    ]);

    // Process results efficiently with reduce instead of filter
    const projects = projectsResult.data || [];
    stats.totalProjects = projects.length;

    const projectStatusCounts = projects.reduce((acc, p) => {
      acc[p.status] = (acc[p.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    stats.activeProjects = projectStatusCounts['in_progress'] || 0;
    stats.completedProjects = projectStatusCounts['completed'] || 0;

    const proposals = proposalsResult.data || [];
    stats.totalProposals = proposals.length;

    const proposalStatusCounts = proposals.reduce((acc, p) => {
      acc[p.status] = (acc[p.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    stats.pendingProposals = proposalStatusCounts['submitted'] || 0;

  } else if (role === 'admin') {
    // Batch admin queries
    const [usersResult, projectsResult] = await Promise.all([
      supabase
        .from('profiles')
        .select('role')
        .eq('is_active', true),

      supabase
        .from('projects')
        .select('status')
    ]);

    // Process results efficiently with reduce instead of filter
    const users = usersResult.data || [];
    stats.totalUsers = users.length;

    const userRoleCounts = users.reduce((acc, u) => {
      acc[u.role] = (acc[u.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    stats.totalClients = userRoleCounts['client'] || 0;
    stats.totalDesigners = userRoleCounts['designer'] || 0;

    const projects = projectsResult.data || [];
    stats.totalProjects = projects.length;

    const projectStatusCounts = projects.reduce((acc, p) => {
      acc[p.status] = (acc[p.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    stats.activeProjects = projectStatusCounts['in_progress'] || 0;
    stats.completedProjects = projectStatusCounts['completed'] || 0;
  }

  return stats;
}

// Legacy stats hook for backward compatibility
export function useDashboardStatsLegacy(userId: string, role: 'client' | 'designer' | 'admin') {
  return useQuery({
    queryKey: dashboardKeys.stats(userId, role),
    queryFn: () => fetchStatsIndividually(userId, role),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  });
}

// Mutations
export function useCreateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (projectData: Partial<Project>) => {
      const { data, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: dashboardKeys.projects() });
      toast.success('Project created successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create project');
    },
  });
}
export function useUpdateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updates }: Partial<Project> & { id: string }) => {
      const { data, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: dashboardKeys.projects() });
      queryClient.invalidateQueries({ queryKey: dashboardKeys.project(data.id) });
      toast.success('Project updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update project');
    },
  });
}
// Create proposal mutation
export function useCreateProposal() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (proposalData: any) => {
      const { data, error } = await supabase
        .from('project_proposals_enhanced')
        .insert([proposalData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: dashboardKeys.proposals() });
      toast.success('Proposal submitted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to submit proposal');
    },
  });
}

// Update proposal mutation
export function useUpdateProposal() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updates }: any) => {
      const { data, error } = await supabase
        .from('project_proposals_enhanced')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: dashboardKeys.proposals() });
      toast.success('Proposal updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update proposal');
    },
  });
}

// Accept proposal mutation
export function useAcceptProposal(userId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ proposalId, briefId }: { proposalId: string; briefId?: string }) => {
      // Update proposal status
      const { error: proposalError } = await supabase
        .from('project_proposals_enhanced')
        .update({
          status: 'accepted',
          reviewed_at: new Date().toISOString()
        })
        .eq('id', proposalId);

      if (proposalError) throw proposalError;

      // Update brief status if briefId provided
      if (briefId) {
        const { error: briefError } = await supabase
          .from('project_briefs')
          .update({ status: 'accepted' })
          .eq('id', briefId);

        if (briefError) throw briefError;
      }

      return { proposalId, briefId };
    },
    onSuccess: () => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: dashboardKeys.proposals() });
      queryClient.invalidateQueries({ queryKey: dashboardKeys.briefs() });
      queryClient.invalidateQueries({ queryKey: dashboardKeys.stats(userId, 'client') });
      toast.success('Proposal accepted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to accept proposal');
    },
  });
}
// Reject proposal mutation
export function useRejectProposal() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ proposalId, userId }: { proposalId: string; userId: string }) => {
      const { error } = await supabase
        .from('project_proposals_enhanced')
        .update({
          status: 'rejected',
          reviewed_at: new Date().toISOString()
        })
        .eq('id', proposalId);

      if (error) throw error;
      return { proposalId, userId };
    },
    onSuccess: (data) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: dashboardKeys.proposals() });
      queryClient.invalidateQueries({ queryKey: dashboardKeys.briefs() });
      queryClient.invalidateQueries({ queryKey: dashboardKeys.batchedStats(data.userId, 'client') });
      toast.success('Proposal rejected successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reject proposal');
    },
  });
}

// Mark proposal as under review when client views it
export function useMarkProposalUnderReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ proposalId }: { proposalId: string }) => {
      // Only update if status is currently 'submitted'
      const { data: currentProposal } = await supabase
        .from('project_proposals_enhanced')
        .select('status')
        .eq('id', proposalId)
        .single();

      if (currentProposal?.status === 'submitted') {
        const { error } = await supabase
          .from('project_proposals_enhanced')
          .update({
            status: 'under_review',
            reviewed_at: new Date().toISOString()
          })
          .eq('id', proposalId);

        if (error) throw error;
      }

      return { proposalId };
    },
    onSuccess: () => {
      // Silently invalidate queries without showing toast
      queryClient.invalidateQueries({ queryKey: dashboardKeys.proposals() });
    },
    onError: (error: any) => {
      console.error('Failed to mark proposal as under review:', error);
    },
  });
}

// Removed old useSendMessage - use unified messaging system instead

// New mutation for conversation messages (doesn't break existing code)

export function useSendConversationMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (messageData: { conversation_id: string; sender_id: string; content: string; message_type?: string }) => {
      const { data, error } = await supabase
        .from('conversation_messages')
        .insert([{
          conversation_id: messageData.conversation_id,
          sender_id: messageData.sender_id,
          content: messageData.content,
          message_type: messageData.message_type || 'text'
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['conversation_messages', data.conversation_id] });
      queryClient.invalidateQueries({ queryKey: dashboardKeys.conversations(data.sender_id) });
      toast.success('Message sent!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send message');
    },
  });

}// Project briefs hook
export function useProjectBriefs(userId: string, role: string) {
  return useQuery({
    queryKey: dashboardKeys.briefsByUser(userId, role),
    queryFn: async () => {
      if (!userId) return [];

      let query = supabase
        .from('project_briefs')
        .select(`
          id,
          title,
          description,
          budget_range,
          timeline_preference,
          urgency,
          status,
          assigned_designer_id,
          created_at,
          profiles:profiles!project_briefs_assigned_designer_id_fkey(full_name)
        `);

      if (role === 'client') {
        query = query.eq('client_id', userId);
      } else if (role === 'designer') {
        query = query.eq('assigned_designer_id', userId);
      }

      const { data, error } = await query.order('created_at', { ascending: false });
      if (error) throw error;

      // Optimize proposal counts - only fetch if we have briefs
      let proposalCounts: { [key: string]: number } = {};

      if (data && data.length > 0) {
        const briefIds = data.map((brief) => brief.id);

        // Use database aggregation instead of JavaScript reduce
        const { data: proposalsData } = await supabase
          .from('project_proposals_enhanced')
          .select('brief_id')
          .in('brief_id', briefIds);

        if (proposalsData && proposalsData.length > 0) {
          // Use Map for better performance than object
          const countsMap = new Map<string, number>();

          for (const proposal of proposalsData) {
            const currentCount = countsMap.get(proposal.brief_id) || 0;
            countsMap.set(proposal.brief_id, currentCount + 1);
          }

          // Convert Map to object for compatibility
          proposalCounts = Object.fromEntries(countsMap);
        }
      }

      // Map the data to include proposal counts
      return (data || []).map((brief) => ({
        ...brief,
        assigned_designer_name: Array.isArray(brief.profiles)
          ? brief.profiles[0]?.full_name || null
          : (brief.profiles as { full_name?: string } | undefined)?.full_name || null,
        proposal_count: proposalCounts[brief.id] || 0,
      }));
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Connected clients hook for designers - SIMPLIFIED AND FIXED
export function useConnectedClients(userId: string, role: string) {
  return useQuery({
    queryKey: dashboardKeys.connectedClients(userId, role),
    queryFn: async () => {
      if (!userId || role !== 'designer') {
        console.log('❌ useConnectedClients: Invalid params', { userId, role });
        return [];
      }

      console.log('🔍 Fetching connected clients for designer:', userId);
      let allClients: any[] = [];

      // PRIMARY STRATEGY: Use connections table (the correct approach)
      try {
        console.log('� Querying connections table...');
        const { data: connectionsData, error: connectionsError } = await supabase
          .from('connections')
          .select(`
            id,
            client_id,
            status,
            created_at,
            updated_at
          `)
          .eq('designer_id', userId)
          .eq('status', 'active');

        if (connectionsError) {
          console.error('❌ Connections query error:', {
            error: connectionsError,
            message: connectionsError?.message || 'Unknown error',
            details: connectionsError?.details || 'No details',
            code: connectionsError?.code || 'No code'
          });
          return [];
        }

        if (!connectionsData || connectionsData.length === 0) {
          console.log('� No active connections found for designer');
          return [];
        }

        console.log(`🔗 Found ${connectionsData.length} active connections`);
        const clientIds = connectionsData.map(conn => conn.client_id);
        console.log('👥 Client IDs from connections:', clientIds);

        // Get client profiles from profiles table (FIXED: removed non-existent columns)
        console.log('👤 Querying profiles table for client details...');
        const { data: clientProfiles, error: profilesError } = await supabase
          .from('profiles')
          .select(`
            id,
            full_name,
            email,
            avatar_url,
            role
          `)
          .in('id', clientIds)
          .eq('role', 'client');

        if (profilesError) {
          console.error('❌ Profiles query error:', {
            error: profilesError,
            message: profilesError?.message || 'Unknown error',
            details: profilesError?.details || 'No details',
            code: profilesError?.code || 'No code',
            clientIds: clientIds
          });
          return [];
        }

        if (!clientProfiles || clientProfiles.length === 0) {
          console.warn('⚠️ No client profiles found for connection IDs:', clientIds);
          return [];
        }

        console.log(`✅ Found ${clientProfiles.length} client profiles`);

        // Combine connection data with profile data
        allClients = clientProfiles.map(profile => {
          const connection = connectionsData.find(conn => conn.client_id === profile.id);
          return {
            ...profile,
            connection_id: connection?.id,
            connection_date: connection?.created_at,
            connection_status: connection?.status || 'active',
            last_updated: connection?.updated_at,
            source: 'connections'
          };
        });

      } catch (error) {
        console.error('❌ Unexpected error in useConnectedClients:', error);
        return [];
      }

      console.log('📊 Final connected clients result:', {
        count: allClients.length,
        clients: allClients.map(c => ({
          id: c.id,
          name: c.full_name,
          source: c.source,
          email: c.email
        }))
      });

      return allClients;
    },
    enabled: !!userId && role === 'designer',
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
}

// Connected designers hook
export function useConnectedDesigners(userId: string, role: string) {
  return useQuery({
    queryKey: dashboardKeys.connectedDesigners(userId),
    queryFn: async () => {
      if (!userId || role !== 'client') return [];

      // Get connections
      const { data: connectionsData, error: connectionsError } = await supabase
        .from('connections')
        .select('designer_id')
        .eq('client_id', userId)
        .eq('status', 'active');

      if (connectionsError) throw connectionsError;
      if (!connectionsData || connectionsData.length === 0) return [];

      // Optimize data fetching with Maps for O(1) lookups instead of O(n) finds
      const designerIds = connectionsData.map((connection) => connection.designer_id);

      // Fetch all data in parallel
      const [designersResult, availabilityResult, specializationsResult] = await Promise.all([
        supabase
          .from('profiles')
          .select('id, full_name, avatar_url, skills')
          .in('id', designerIds),

        supabase
          .from('designer_availability')
          .select('designer_id, status')
          .in('designer_id', designerIds),

        supabase
          .from('designer_specializations')
          .select('designer_id, specialization, is_primary')
          .in('designer_id', designerIds)
          .eq('is_primary', true)
      ]);

      if (designersResult.error) throw designersResult.error;

      // Create Maps for O(1) lookups instead of O(n) array.find operations
      const availabilityMap = new Map(
        (availabilityResult.data || []).map(a => [a.designer_id, a.status])
      );

      const specializationMap = new Map(
        (specializationsResult.data || []).map(s => [s.designer_id, s.specialization])
      );

      // Process designers efficiently
      return (designersResult.data || []).map((designer) => ({
        id: designer.id,
        full_name: designer.full_name,
        avatar_url: designer.avatar_url,
        specialization: specializationMap.get(designer.id) ||
          (Array.isArray(designer.skills) && designer.skills.length > 0
            ? designer.skills[0]
            : 'Interior Design'),
        availability_status: availabilityMap.get(designer.id) || 'available',
        rating: 4.8, // TODO: Calculate from reviews
        completed_projects: 12, // TODO: Calculate from projects
      }));
    },
    enabled: !!userId && role === 'client',
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// New: Payment methods hook
export function usePaymentMethods(userId: string) {
  return useQuery({
    queryKey: dashboardKeys.paymentMethods(userId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('payment_methods')
        .select(`
          id,
          type,
          is_default,
          last_four,
          brand,
          expires_at,
          created_at
        `)
        .eq('user_id', userId)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000,
  });
}

// New: Payments history hook
export function usePayments(userId: string, role: string) {
  return useQuery({
    queryKey: dashboardKeys.paymentsByUser(userId),
    queryFn: async () => {
      let query = supabase
        .from('transactions')
        .select(`
          id,
          amount,
          status,
          type,
          description,
          created_at,
          project:projects(title)
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (role === 'client') {
        query = query.eq('client_id', userId);
      } else if (role === 'designer') {
        query = query.eq('designer_id', userId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000,
  });
}

// New: Inspiration boards hook (client-specific)
export function useInspirationBoards(userId: string) {
  return useQuery({
    queryKey: dashboardKeys.inspirationsByUser(userId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('inspiration_boards')
        .select(`
          id,
          title,
          description,
          is_public,
          created_at,
          inspiration_items(id, image_url, title, source_url)
        `)
        .eq('client_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000,
  });
}

// New: Notifications hook
export function useNotifications(userId: string) {
  return useQuery({
    queryKey: dashboardKeys.notifications(userId),
    queryFn: async () => {
      const { data, error } = await supabase
        .from('notifications')
        .select(`
          id,
          title,
          content,
          type,
          is_read,
          created_at,
          related_id
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      return data;
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes for real-time feel
    gcTime: 10 * 60 * 1000,
  });
}

// New: Batch data hook for dashboard initialization
export function useBatchDashboardData(
  userId: string,
  role: 'client' | 'designer' | 'admin',
  options: { enabled?: boolean } = {}
) {
  const { enabled = true } = options;

  return useQueries({
    queries: [
      {
        queryKey: dashboardKeys.batchedStats(userId, role),
        queryFn: () => fetchStatsIndividually(userId, role),
        staleTime: 15 * 60 * 1000,
        enabled: !!userId && enabled,
        refetchOnWindowFocus: false, // Prevent refetch on tab switch
        refetchOnMount: false, // Use cached data when available
      },
      {
        queryKey: dashboardKeys.projectsByUser(userId, role),
        queryFn: async () => {
          let query = supabase
            .from('projects')
            .select(`
              id, title, status, created_at, budget,
              client:profiles!projects_client_id_fkey(full_name),
              designer:profiles!projects_designer_id_fkey(full_name)
            `);

          if (role === 'client') query = query.eq('client_id', userId);
          else if (role === 'designer') query = query.eq('designer_id', userId);

          const { data, error } = await query.order('created_at', { ascending: false }).limit(10);
          if (error) throw error;
          return data;
        },
        staleTime: 10 * 60 * 1000,
        enabled: !!userId && enabled,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
      },
      {
        queryKey: dashboardKeys.proposalsByUser(userId, role),
        queryFn: async () => {
          if (role === 'designer') {
            const { data, error } = await supabase
              .from('project_proposals_enhanced')
              .select(`
                id, title, status, total_budget, created_at,
                project_briefs!brief_id(title, client:profiles!client_id(full_name))
              `)
              .eq('designer_id', userId)
              .order('created_at', { ascending: false })
              .limit(10);
            if (error) throw error;
            return data;
          }
          return [];
        },
        staleTime: 10 * 60 * 1000,
        enabled: !!userId && enabled && role === 'designer',
        refetchOnWindowFocus: false,
        refetchOnMount: false,
      },
    ],
  });
}
