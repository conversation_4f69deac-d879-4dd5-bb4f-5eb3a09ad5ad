# Payment Method Implementation Guide

This guide explains how to implement the "Add Payment Method" functionality in your application.

## Overview

The implementation adds the ability for users to save and manage payment methods (credit/debit cards) in your application. This is separate from making payments - it allows users to store their payment information for future use.

## Components Created

1. **AddPaymentMethodForm**: A form component that uses Stripe Elements to securely collect card information
2. **AddPaymentMethodWrapper**: A wrapper component that handles the Stripe setup and API calls
3. **API Route**: A Next.js API route to create Stripe SetupIntents
4. **Supabase Edge Function**: A function to handle payment method operations

## Implementation Steps

### 1. Set Up Stripe

Make sure you have the following environment variables set:

```
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

### 2. Add the Required Packages

Make sure you have the Stripe packages installed:

```bash
npm install @stripe/react-stripe-js @stripe/stripe-js stripe
# or
yarn add @stripe/react-stripe-js @stripe/stripe-js stripe
```

### 3. Deploy the Supabase Edge Function

```bash
cd supabase
supabase functions deploy manage-payment-methods
```

### 4. Update the Database Schema

Make sure your `profiles` table has a `stripe_customer_id` column:

```sql
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;
```

### 5. Test the Implementation

1. Go to the client payments page
2. Click on "Add Payment Method"
3. Enter test card details (e.g., ************** 4242)
4. Submit the form
5. Verify that the payment method appears in the list

## How It Works

1. When a user clicks "Add Payment Method", the `AddPaymentMethodWrapper` component is shown
2. The component creates a Stripe SetupIntent via the API route
3. The user enters their card details in the Stripe Elements form
4. When submitted, the card is tokenized by Stripe and a payment method is created
5. The payment method reference is saved in your database
6. The UI updates to show the new payment method

## Stripe Test Cards

Use these test cards for development:

- **Visa**: ************** 4242
- **Mastercard**: ************** 4444
- **American Express**: 3782 822463 10005

Use any future expiration date, any 3-digit CVC (4 digits for Amex), and any postal code.

## Security Considerations

- Never log or store full card details
- Always use Stripe Elements to collect card information
- Use HTTPS for all API calls
- Follow PCI compliance guidelines

## Customization

You can customize the appearance of the payment form by modifying the `appearance` object in the `AddPaymentMethodWrapper` component.

## Troubleshooting

If you encounter issues:

1. Check the browser console for errors
2. Verify that your Stripe API keys are correct
3. Make sure the Supabase Edge Function is deployed
4. Check that the database schema is correct
5. Verify that the user has a valid Stripe customer ID
