"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

export type UserProfile = {
  id: string;
  full_name: string;
  role: 'client' | 'designer' | 'admin' | 'quality_team' | 'manager';
  email: string;
  avatar_url?: string;
  last_sign_in_at?: string | null;
};

// Query keys for consistent caching
export const authKeys = {
  session: ['auth', 'session'] as const,
  profile: (userId: string) => ['auth', 'profile', userId] as const,
};

// Helper function to clear all authentication-related cache
export function clearAuthCache(queryClient: any) {
  // Clear auth queries
  queryClient.removeQueries({ queryKey: authKeys.session });
  queryClient.removeQueries({ queryKey: ['auth'] });

  // Clear dashboard data that might be role-specific
  queryClient.removeQueries({ queryKey: ['dashboard'] });
  queryClient.removeQueries({ queryKey: ['stats'] });
  queryClient.removeQueries({ queryKey: ['projects'] });
  queryClient.removeQueries({ queryKey: ['proposals'] });
  queryClient.removeQueries({ queryKey: ['messages'] });

  // Clear localStorage cache
  if (typeof window !== 'undefined') {
    localStorage.removeItem('query_cache');
    localStorage.removeItem('user_preferences');
  }
}

// Get current session (original working approach)
export function useSession() {
  return useQuery({
    queryKey: authKeys.session,
    queryFn: async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Session fetch error:', error);
        return null; // Don't throw, return null for graceful handling
      }
      return session;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes (reduced from 10 minutes)
    gcTime: 10 * 60 * 1000, // 10 minutes cache time (reduced from 30 minutes)
    retry: false, // Don't retry auth failures
    refetchOnWindowFocus: false, // Prevent loading screens on tab changes
    refetchOnReconnect: false, // Prevent loading screens on network reconnect
    refetchOnMount: false, // Prevent loading screens on component remount
    // This ensures sessions persist across tab changes and browser minimization
  });
}

// Get user profile (enhanced for persistence)
export function useProfile(userId?: string) {
  return useQuery({
    queryKey: authKeys.profile(userId || ''),
    queryFn: async () => {
      if (!userId) throw new Error('User ID required');

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data as UserProfile;
    },
    enabled: !!userId,
    staleTime: 3 * 60 * 1000, // 3 minutes (reduced from 15 minutes)
    gcTime: 15 * 60 * 1000, // 15 minutes cache time (reduced from 1 hour)
    refetchOnWindowFocus: false, // Prevent loading screens on tab changes
    refetchOnReconnect: false, // Prevent loading screens on network reconnect
    refetchOnMount: false, // Prevent loading screens on component remount
    // This ensures profiles persist across tab changes and browser minimization
  });
}

// Combined auth hook for convenience (original working structure)
export function useOptimizedAuth() {
  const { data: session, isLoading: sessionLoading, error: sessionError } = useSession();
  const { data: profile, isLoading: profileLoading, error: profileError } = useProfile(session?.user?.id);
  const logoutMutation = useLogout();

  // Simple loading logic - only show loading if session is actually loading
  const isLoading = sessionLoading;

  return {
    session,
    user: session?.user || null,
    profile,
    loading: isLoading,
    isLoading: isLoading, // For compatibility
    error: sessionError || profileError,
    isAuthenticated: !!session?.user,
    signOut: logoutMutation.mutate,
    isSigningOut: logoutMutation.isPending,
  };
}

// Login mutation
export function useLogin() {
  const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ email, password, redirectedFrom }: {
      email: string;
      password: string;
      redirectedFrom?: string;
    }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { ...data, redirectedFrom };
    },
    onSuccess: async (data) => {
      if (data.user) {
        // CRITICAL: Clear ALL cached data before proceeding
        clearAuthCache(queryClient);

        // Show success message
        toast.success('Welcome back!');

        // Get user role for redirect with fresh data
        const { data: userData } = await supabase
          .from('profiles')
          .select('role, temp_password')
          .eq('id', data.user.id)
          .single();

        // Check if user needs to change password
        if (userData?.temp_password) {
          let settingsPath;
          switch (userData.role) {
            case 'admin':
              settingsPath = '/admin/settings?tab=security&force_password_change=true';
              break;
            case 'designer':
              settingsPath = '/designer/settings?tab=security&force_password_change=true';
              break;
            case 'quality_team':
              settingsPath = '/quality/settings?tab=security&force_password_change=true';
              break;
            case 'manager':
              settingsPath = '/manager/settings?tab=security&force_password_change=true';
              break;
            default:
              settingsPath = '/client/settings?tab=security&force_password_change=true';
          }
          window.location.href = settingsPath;
          return;
        }

        // Determine redirect path
        let redirectPath;
        if (data.redirectedFrom && data.redirectedFrom !== '/auth/login') {
          redirectPath = data.redirectedFrom;
        } else {
          switch (userData?.role) {
            case 'admin':
              redirectPath = '/admin/dashboard';
              break;
            case 'designer':
              redirectPath = '/designer/dashboard';
              break;
            case 'quality_team':
              redirectPath = '/quality/dashboard';
              break;
            case 'manager':
              redirectPath = '/manager/dashboard';
              break;
            default:
              redirectPath = '/client/dashboard';
          }
        }

        // Force a complete page reload to ensure clean state
        window.location.href = redirectPath;
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Login failed');
    },
  });
}

// Logout mutation (enhanced)
export function useLogout() {
  const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    },
    onSuccess: () => {
      // Clear all cached data using helper
      clearAuthCache(queryClient);

      // Clear Supabase auth token as well
      if (typeof window !== 'undefined') {
        localStorage.removeItem('supabase.auth.token');
      }

      // Force page reload to ensure complete cleanup
      window.location.href = '/';
    },
    onError: (error: any) => {
      toast.error(error.message || 'Logout failed');
    },
  });
}

// Refresh profile mutation
export function useRefreshProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data as UserProfile;
    },
    onSuccess: (data) => {
      // Update the cached profile
      queryClient.setQueryData(authKeys.profile(data.id), data);
    },
  });
}
