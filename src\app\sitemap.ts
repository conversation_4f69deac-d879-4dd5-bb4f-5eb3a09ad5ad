import { MetadataRoute } from 'next';
import { generateSitemapUrls } from '@/lib/seo';
import { supabase } from '@/lib/supabase';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Get static pages
  const staticUrls = generateSitemapUrls();

  // Get dynamic blog posts
  const { data: blogPosts } = await supabase
    .from('blog_posts')
    .select('slug, updated_at')
    .eq('status', 'published')
    .order('updated_at', { ascending: false });

  const blogUrls = blogPosts?.map(post => ({
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${post.slug}`,
    lastModified: new Date(post.updated_at),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  })) || [];

  // Get service pages (if you have dynamic service pages)
  const serviceUrls = [
    'creative-design-branding',
    'innovative-architectural-design',
    'interior-design',
    'urban-architectural-planning',
    'residential-commercial-projects',
    'landscape-architecture-integration',
    'educational-community-spaces'
  ].map(service => ({
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/services/${service}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }));

  return [
    ...staticUrls,
    ...serviceUrls,
    ...blogUrls,
  ];
}
