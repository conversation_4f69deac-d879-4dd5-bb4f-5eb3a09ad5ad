'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { PortfolioProject } from '@/types/portfolio';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { StarIcon, EditIcon, TrashIcon } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface PortfolioProjectCardProps {
  project: PortfolioProject;
  isOwner?: boolean;
  onEdit?: (project: PortfolioProject) => void;
  onDelete?: (projectId: string) => void;
}

export function PortfolioProjectCard({
  project,
  isOwner = false,
  onEdit,
  onDelete,
}: PortfolioProjectCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleEdit = () => {
    if (onEdit) {
      onEdit(project);
    }
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    if (onDelete) {
      onDelete(project.id);
    }
    setShowDeleteDialog(false);
  };

  return (
    <>
      <Card className="overflow-hidden h-full flex flex-col">
        <div className="relative aspect-video overflow-hidden">
          {project.cover_image && !imageError ? (
            <Image
              src={project.cover_image}
              alt={project.title}
              fill
              className="object-cover transition-transform hover:scale-105"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <p className="text-muted-foreground">No image available</p>
            </div>
          )}
          {project.featured && (
            <div className="absolute top-2 right-2">
              <Badge variant="default" className="bg-amber-500 hover:bg-amber-600">
                <StarIcon className="h-3 w-3 mr-1" />
                Featured
              </Badge>
            </div>
          )}
        </div>
        <CardContent className="flex-1 p-4">
          <h3 className="text-lg font-semibold mb-2 line-clamp-1">{project.title}</h3>
          {project.category && (
            <Badge variant="outline" className="mb-2">
              {project.category}
            </Badge>
          )}
          {project.description && (
            <p className="text-sm text-muted-foreground line-clamp-3 mb-2">
              {project.description}
            </p>
          )}
          {project.client_name && (
            <p className="text-xs text-muted-foreground">
              Client: {project.client_name}
            </p>
          )}
        </CardContent>
        <CardFooter className="p-4 pt-0 flex justify-between">
          <Button asChild variant="default" size="sm">
            <Link href={`/portfolio/${project.id}`}>View Details</Link>
          </Button>
          {isOwner && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={handleEdit}
                className="h-8 w-8"
              >
                <EditIcon className="h-4 w-4" />
                <span className="sr-only">Edit</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={handleDelete}
                className="h-8 w-8 text-destructive hover:text-destructive"
              >
                <TrashIcon className="h-4 w-4" />
                <span className="sr-only">Delete</span>
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the project "{project.title}" and all associated images.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export function PortfolioProjectCardSkeleton() {
  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <div className="relative aspect-video overflow-hidden">
        <Skeleton className="w-full h-full" />
      </div>
      <CardContent className="flex-1 p-4">
        <Skeleton className="h-6 w-3/4 mb-2" />
        <Skeleton className="h-4 w-1/4 mb-2" />
        <Skeleton className="h-4 w-full mb-1" />
        <Skeleton className="h-4 w-5/6 mb-1" />
        <Skeleton className="h-4 w-4/6 mb-2" />
        <Skeleton className="h-3 w-1/3" />
      </CardContent>
      <CardFooter className="p-4 pt-0 flex justify-between">
        <Skeleton className="h-9 w-24" />
      </CardFooter>
    </Card>
  );
}
