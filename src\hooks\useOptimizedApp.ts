"use client";

import { useEffect, useState, useMemo } from 'react';
import { useOptimizedAuth } from './useOptimizedAuth';
import { usePrefetchDashboardData } from './usePrefetch';
import { useDataPersistence } from './useDataPersistence';
import { useNavigationPrefetch } from './useNavigationPrefetch';
import { usePerformanceMonitoring } from './usePerformanceMonitoring';

// Master optimization hook that orchestrates all performance enhancements
export function useOptimizedApp() {
  const { user, profile, loading } = useOptimizedAuth();
  const { trackPageLoad } = usePerformanceMonitoring();
  
  // Initialize all optimization systems
  usePrefetchDashboardData();
  useDataPersistence();
  const { prefetchAllRoutes } = useNavigationPrefetch();

  // Track app initialization performance
  useEffect(() => {
    if (!loading && user && profile) {
      // App is fully loaded, track performance
      trackPageLoad();
      
      // Start comprehensive prefetching
      prefetchAllRoutes();
      
      // Log optimization status
      if (process.env.NODE_ENV === 'development') {
        console.log('🚀 App Optimization Active:', {
          user: user.id,
          role: profile.role,
          timestamp: new Date().toISOString(),
          features: [
            'Enhanced Caching',
            'Smart Prefetching', 
            'Data Persistence',
            'Performance Monitoring',
            'Optimistic Updates'
          ]
        });
      }
    }
  }, [loading, user, profile, trackPageLoad, prefetchAllRoutes]);

  return {
    isOptimized: !loading && !!user && !!profile,
    user,
    profile,
    loading,
  };
}

// Hook for measuring and optimizing component performance
export function useComponentOptimization(componentName: string) {
  const startTime = performance.now();
  
  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // Log slow components in development
    if (process.env.NODE_ENV === 'development' && renderTime > 50) {
      console.warn(`⚠️ Slow component render: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }
    
    // Track in performance monitoring
    if (renderTime > 100) {
      console.warn(`🐌 Very slow component: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }
  });

  return {
    renderTime: performance.now() - startTime,
    componentName,
  };
}

// Hook for optimized data fetching with automatic retries and caching
export function useOptimizedQuery<T>(
  queryKey: any[],
  queryFn: () => Promise<T>,
  options: {
    staleTime?: number;
    cacheTime?: number;
    retry?: number;
    enabled?: boolean;
  } = {}
) {
  const {
    staleTime = 10 * 60 * 1000, // 10 minutes default
    cacheTime = 30 * 60 * 1000, // 30 minutes default
    retry = 2,
    enabled = true,
  } = options;

  // This would integrate with React Query
  // Implementation would depend on the specific query library setup
  return {
    data: null as T | null,
    isLoading: false,
    error: null,
    refetch: () => Promise.resolve(),
  };
}

// Hook for optimized mutations with optimistic updates
export function useOptimizedMutation<T, V>(
  mutationFn: (variables: V) => Promise<T>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    optimisticUpdate?: (variables: V) => T;
  } = {}
) {
  const { onSuccess, onError, optimisticUpdate } = options;

  const mutate = async (variables: V) => {
    try {
      // Apply optimistic update if provided
      if (optimisticUpdate) {
        const optimisticData = optimisticUpdate(variables);
        // Apply optimistic update to UI
      }

      const result = await mutationFn(variables);
      onSuccess?.(result);
      return result;
    } catch (error) {
      onError?.(error as Error);
      throw error;
    }
  };

  return {
    mutate,
    isLoading: false,
    error: null,
  };
}

// Hook for smart loading states that prevent layout shift
export function useSmartLoading(isLoading: boolean, minLoadingTime: number = 200) {
  const [showLoading, setShowLoading] = useState(false);
  const [hasShownContent, setHasShownContent] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isLoading && !hasShownContent) {
      // Show loading after minimum time to prevent flashing
      timeoutId = setTimeout(() => {
        setShowLoading(true);
      }, minLoadingTime);
    } else if (!isLoading) {
      setShowLoading(false);
      setHasShownContent(true);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isLoading, hasShownContent, minLoadingTime]);

  return {
    showLoading: showLoading && isLoading,
    hasShownContent,
  };
}

// Hook for optimized image loading with lazy loading and preloading
export function useOptimizedImages() {
  const preloadImage = (src: string, priority: boolean = false) => {
    if (typeof window === 'undefined') return;

    const link = document.createElement('link');
    link.rel = priority ? 'preload' : 'prefetch';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  };

  const preloadCriticalImages = (images: string[]) => {
    images.forEach(src => preloadImage(src, true));
  };

  return {
    preloadImage,
    preloadCriticalImages,
  };
}

// Hook for optimized form handling with debouncing and validation
export function useOptimizedForm<T extends Record<string, any>>(
  initialValues: T,
  validationSchema?: any,
  debounceMs: number = 300
) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const debouncedValidation = useMemo(
    () => debounce((vals: T) => {
      if (validationSchema) {
        try {
          validationSchema.parse(vals);
          setErrors({});
        } catch (error: any) {
          const fieldErrors: Partial<Record<keyof T, string>> = {};
          error.errors?.forEach((err: any) => {
            if (err.path?.[0]) {
              fieldErrors[err.path[0] as keyof T] = err.message;
            }
          });
          setErrors(fieldErrors);
        }
      }
    }, debounceMs),
    [validationSchema, debounceMs]
  );

  useEffect(() => {
    debouncedValidation(values);
  }, [values, debouncedValidation]);

  const updateField = (field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (onSubmit: (values: T) => Promise<void>) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    values,
    errors,
    isSubmitting,
    updateField,
    handleSubmit,
    isValid: Object.keys(errors).length === 0,
  };
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Export all optimization hooks
export {
  useComponentOptimization,
  useOptimizedQuery,
  useOptimizedMutation,
  useSmartLoading,
  useOptimizedImages,
  useOptimizedForm,
};
