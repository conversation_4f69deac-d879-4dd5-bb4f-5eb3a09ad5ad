-- =====================================================
-- ESCROW SYSTEM ROW LEVEL SECURITY POLICIES
-- Secure access to escrow data based on user roles
-- =====================================================

-- 1. ESCROW ACCOUNTS POLICIES
-- =====================================================

-- Policy for escrow accounts - users can see accounts for their projects
CREATE POLICY "Users can view escrow accounts for their projects" ON escrow_accounts
  FOR SELECT USING (
    auth.uid() = client_id OR 
    auth.uid() = designer_id OR 
    auth.uid() = manager_id OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'quality_team')
    )
  );

-- Policy for creating escrow accounts - only system/admin can create
CREATE POLICY "Only system can create escrow accounts" ON escrow_accounts
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for updating escrow accounts - only admin/system
CREATE POLICY "Only admin can update escrow accounts" ON escrow_accounts
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 2. ESCROW HOLDS POLICIES
-- =====================================================

-- Policy for viewing escrow holds
CREATE POLICY "Users can view escrow holds for their projects" ON escrow_holds
  FOR SELECT USING (
    -- Client can see holds for their projects
    EXISTS (
      SELECT 1 FROM escrow_accounts ea 
      WHERE ea.id = escrow_account_id AND ea.client_id = auth.uid()
    ) OR
    -- Designer can see holds for their projects
    EXISTS (
      SELECT 1 FROM escrow_accounts ea 
      WHERE ea.id = escrow_account_id AND ea.designer_id = auth.uid()
    ) OR
    -- Manager can see holds for assigned projects
    EXISTS (
      SELECT 1 FROM project_assignments pa 
      WHERE pa.project_id = escrow_holds.project_id 
        AND pa.manager_id = auth.uid() 
        AND pa.status = 'active'
    ) OR
    -- Admin and quality team can see all
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'quality_team')
    )
  );

-- Policy for creating escrow holds - only system/admin
CREATE POLICY "Only system can create escrow holds" ON escrow_holds
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role IN ('admin')
    )
  );

-- Policy for updating escrow holds - admin and managers
CREATE POLICY "Authorized users can update escrow holds" ON escrow_holds
  FOR UPDATE USING (
    -- Admin can update any hold
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    ) OR
    -- Manager can update holds for assigned projects
    EXISTS (
      SELECT 1 FROM project_assignments pa 
      WHERE pa.project_id = escrow_holds.project_id 
        AND pa.manager_id = auth.uid() 
        AND pa.status = 'active'
    )
  );

-- 3. ESCROW RELEASES POLICIES
-- =====================================================

-- Policy for viewing escrow releases
CREATE POLICY "Users can view escrow releases for their projects" ON escrow_releases
  FOR SELECT USING (
    -- Users involved in the project can see releases
    EXISTS (
      SELECT 1 FROM escrow_accounts ea 
      WHERE ea.id = escrow_account_id 
        AND (ea.client_id = auth.uid() OR ea.designer_id = auth.uid())
    ) OR
    -- Manager assigned to project can see releases
    manager_id = auth.uid() OR
    -- Admin and quality team can see all
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'quality_team')
    ) OR
    -- User who requested the release can see it
    requested_by = auth.uid()
  );

-- Policy for creating escrow releases - project participants
CREATE POLICY "Project participants can create escrow releases" ON escrow_releases
  FOR INSERT WITH CHECK (
    -- Designer can request release for their projects
    EXISTS (
      SELECT 1 FROM escrow_accounts ea 
      WHERE ea.id = escrow_account_id AND ea.designer_id = auth.uid()
    ) OR
    -- Manager can request release for assigned projects
    EXISTS (
      SELECT 1 FROM project_assignments pa 
      WHERE pa.project_id = escrow_releases.project_id 
        AND pa.manager_id = auth.uid() 
        AND pa.status = 'active'
    ) OR
    -- Admin can create any release
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for updating escrow releases - approvers only
CREATE POLICY "Authorized approvers can update escrow releases" ON escrow_releases
  FOR UPDATE USING (
    -- Manager assigned to the release can approve
    manager_id = auth.uid() OR
    -- Quality team can approve quality aspects
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'quality_team'
    ) OR
    -- Admin can update any release
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 4. ESCROW DISPUTES POLICIES
-- =====================================================

-- Policy for viewing escrow disputes
CREATE POLICY "Users can view disputes for their projects" ON escrow_disputes
  FOR SELECT USING (
    -- Users involved in the dispute can see it
    initiated_by = auth.uid() OR
    initiated_against = auth.uid() OR
    assigned_mediator = auth.uid() OR
    -- Users involved in the project can see disputes
    EXISTS (
      SELECT 1 FROM escrow_holds eh
      JOIN escrow_accounts ea ON eh.escrow_account_id = ea.id
      WHERE eh.id = escrow_hold_id 
        AND (ea.client_id = auth.uid() OR ea.designer_id = auth.uid())
    ) OR
    -- Manager assigned to project can see disputes
    EXISTS (
      SELECT 1 FROM project_assignments pa 
      WHERE pa.project_id = escrow_disputes.project_id 
        AND pa.manager_id = auth.uid() 
        AND pa.status = 'active'
    ) OR
    -- Admin can see all disputes
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for creating escrow disputes - project participants
CREATE POLICY "Project participants can create disputes" ON escrow_disputes
  FOR INSERT WITH CHECK (
    -- Users involved in the project can create disputes
    EXISTS (
      SELECT 1 FROM escrow_holds eh
      JOIN escrow_accounts ea ON eh.escrow_account_id = ea.id
      WHERE eh.id = escrow_hold_id 
        AND (ea.client_id = auth.uid() OR ea.designer_id = auth.uid())
    ) OR
    -- Manager can create disputes for assigned projects
    EXISTS (
      SELECT 1 FROM project_assignments pa 
      WHERE pa.project_id = escrow_disputes.project_id 
        AND pa.manager_id = auth.uid() 
        AND pa.status = 'active'
    ) OR
    -- Admin can create any dispute
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for updating escrow disputes - mediators and admin
CREATE POLICY "Mediators can update disputes" ON escrow_disputes
  FOR UPDATE USING (
    assigned_mediator = auth.uid() OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 5. ESCROW ACTIVITIES POLICIES
-- =====================================================

-- Policy for viewing escrow activities - project participants and admin
CREATE POLICY "Users can view escrow activities for their projects" ON escrow_activities
  FOR SELECT USING (
    -- Users involved in the project can see activities
    EXISTS (
      SELECT 1 FROM escrow_accounts ea 
      WHERE ea.id = escrow_account_id 
        AND (ea.client_id = auth.uid() OR ea.designer_id = auth.uid())
    ) OR
    -- Manager assigned to project can see activities
    EXISTS (
      SELECT 1 FROM project_assignments pa 
      WHERE pa.project_id = escrow_activities.project_id 
        AND pa.manager_id = auth.uid() 
        AND pa.status = 'active'
    ) OR
    -- Admin and quality team can see all activities
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'quality_team')
    ) OR
    -- User who performed the activity can see it
    performed_by = auth.uid()
  );

-- Policy for creating escrow activities - system logging
CREATE POLICY "System can create escrow activities" ON escrow_activities
  FOR INSERT WITH CHECK (
    -- Any authenticated user can create activity logs for actions they perform
    performed_by = auth.uid() OR
    -- Admin can create any activity log
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- No updates or deletes allowed on activity logs for audit integrity
-- Activities are append-only for audit trail

-- 6. GRANT PERMISSIONS
-- =====================================================

-- Grant table permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON escrow_accounts TO authenticated;
GRANT SELECT, INSERT, UPDATE ON escrow_holds TO authenticated;
GRANT SELECT, INSERT, UPDATE ON escrow_releases TO authenticated;
GRANT SELECT, INSERT, UPDATE ON escrow_disputes TO authenticated;
GRANT SELECT, INSERT ON escrow_activities TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
