"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Settings,
  Save,
  AlertCircle,
  CheckCircle,
  Globe,
  Shield,
  CreditCard,
  Mail,
  Upload,
  Eye,
  EyeOff,
  Key,
  Database,
  Server,
  Zap
} from "lucide-react";

interface PlatformSettings {
  // General Settings
  site_name: string;
  site_description: string;
  site_url: string;
  admin_email: string;
  support_email: string;
  
  // Feature Toggles
  user_registration_enabled: boolean;
  designer_applications_enabled: boolean;
  project_creation_enabled: boolean;
  messaging_enabled: boolean;
  file_uploads_enabled: boolean;
  
  // Payment Settings
  stripe_publishable_key: string;
  stripe_secret_key: string;
  payment_methods: string[];
  auto_payout_enabled: boolean;
  payout_schedule: string;
  
  // Email Settings
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_password: string;
  email_from_name: string;
  email_from_address: string;
  
  // File Upload Settings
  max_file_size_mb: number;
  allowed_file_types: string[];
  storage_provider: string;
  
  // Security Settings
  session_timeout_minutes: number;
  password_min_length: number;
  require_email_verification: boolean;
  enable_two_factor: boolean;
  
  // Maintenance
  maintenance_mode: boolean;
  maintenance_message: string;
}

export default function PlatformConfigurationPage() {
  const { user } = useOptimizedAuth();
  const [settings, setSettings] = useState<PlatformSettings>({
    // General Settings
    site_name: "Seniors Architecture Firm",
    site_description: "Premium architectural design services connecting clients with expert designers",
    site_url: "https://seniorsarchifirm.com",
    admin_email: "<EMAIL>",
    support_email: "<EMAIL>",
    
    // Feature Toggles
    user_registration_enabled: true,
    designer_applications_enabled: true,
    project_creation_enabled: true,
    messaging_enabled: true,
    file_uploads_enabled: true,
    
    // Payment Settings
    stripe_publishable_key: "",
    stripe_secret_key: "",
    payment_methods: ["paypal", "card", "bank_transfer"], // ADDED PayPal as primary
    auto_payout_enabled: true,
    payout_schedule: "weekly",
    
    // Email Settings
    smtp_host: "",
    smtp_port: 587,
    smtp_username: "",
    smtp_password: "",
    email_from_name: "Seniors Architecture Firm",
    email_from_address: "<EMAIL>",
    
    // File Upload Settings
    max_file_size_mb: 50,
    allowed_file_types: ["jpg", "jpeg", "png", "pdf", "dwg", "dxf"],
    storage_provider: "supabase",
    
    // Security Settings
    session_timeout_minutes: 480,
    password_min_length: 8,
    require_email_verification: true,
    enable_two_factor: false,
    
    // Maintenance
    maintenance_mode: false,
    maintenance_message: "We're performing scheduled maintenance. Please check back soon."
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'features' | 'payments' | 'email' | 'files' | 'security' | 'maintenance'>('general');
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});

  const handleInputChange = (field: keyof PlatformSettings, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleArrayChange = (field: keyof PlatformSettings, value: string[]) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const toggleSecret = (field: string) => {
    setShowSecrets(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // In a real implementation, this would save to database
      // await supabase.from('platform_settings').upsert(settings);
      
      setSuccess('Platform settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const testEmailConnection = async () => {
    setError(null);
    setSuccess(null);
    
    try {
      // In a real implementation, this would test the SMTP connection
      setSuccess('Email connection test successful');
    } catch (error) {
      setError('Email connection test failed');
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: Globe },
    { id: 'features', label: 'Features', icon: Zap },
    { id: 'payments', label: 'Payments', icon: CreditCard },
    { id: 'email', label: 'Email', icon: Mail },
    { id: 'files', label: 'File Storage', icon: Upload },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'maintenance', label: 'Maintenance', icon: Server }
  ];

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Platform Configuration</h1>
          <p className="text-gray-600">Configure platform-wide settings and preferences</p>
        </div>
        <Button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center"
        >
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save All Changes'}
        </Button>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-brown-100 text-brown-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-5 w-5 mr-3" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">General Settings</h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                      <input
                        type="text"
                        value={settings.site_name}
                        onChange={(e) => handleInputChange('site_name', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Site URL</label>
                      <input
                        type="url"
                        value={settings.site_url}
                        onChange={(e) => handleInputChange('site_url', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Site Description</label>
                    <textarea
                      value={settings.site_description}
                      onChange={(e) => handleInputChange('site_description', e.target.value)}
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Admin Email</label>
                      <input
                        type="email"
                        value={settings.admin_email}
                        onChange={(e) => handleInputChange('admin_email', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Support Email</label>
                      <input
                        type="email"
                        value={settings.support_email}
                        onChange={(e) => handleInputChange('support_email', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Feature Toggles */}
            {activeTab === 'features' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Feature Toggles</h2>
                <div className="space-y-4">
                  {[
                    { key: 'user_registration_enabled', label: 'User Registration', description: 'Allow new users to register accounts' },
                    { key: 'designer_applications_enabled', label: 'Designer Applications', description: 'Accept new designer applications' },
                    { key: 'project_creation_enabled', label: 'Project Creation', description: 'Allow clients to create new projects' },
                    { key: 'messaging_enabled', label: 'Messaging System', description: 'Enable communication between users' },
                    { key: 'file_uploads_enabled', label: 'File Uploads', description: 'Allow users to upload files' }
                  ].map((feature) => (
                    <div key={feature.key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{feature.label}</h3>
                        <p className="text-sm text-gray-600">{feature.description}</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings[feature.key as keyof PlatformSettings] as boolean}
                          onChange={(e) => handleInputChange(feature.key as keyof PlatformSettings, e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brown-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brown-600"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Payment Settings */}
            {activeTab === 'payments' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Settings</h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Stripe Publishable Key</label>
                      <input
                        type="text"
                        value={settings.stripe_publishable_key}
                        onChange={(e) => handleInputChange('stripe_publishable_key', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                        placeholder="pk_..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Stripe Secret Key</label>
                      <div className="relative">
                        <input
                          type={showSecrets.stripe_secret ? "text" : "password"}
                          value={settings.stripe_secret_key}
                          onChange={(e) => handleInputChange('stripe_secret_key', e.target.value)}
                          className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                          placeholder="sk_..."
                        />
                        <button
                          type="button"
                          onClick={() => toggleSecret('stripe_secret')}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          {showSecrets.stripe_secret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Payout Schedule</label>
                      <select
                        value={settings.payout_schedule}
                        onChange={(e) => handleInputChange('payout_schedule', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      >
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                      </select>
                    </div>
                    <div className="flex items-center">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.auto_payout_enabled}
                          onChange={(e) => handleInputChange('auto_payout_enabled', e.target.checked)}
                          className="mr-2"
                        />
                        <span className="text-sm font-medium text-gray-700">Enable Auto Payouts</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Email Settings */}
            {activeTab === 'email' && (
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900">Email Settings</h2>
                  <Button
                    onClick={testEmailConnection}
                    variant="outline"
                    size="sm"
                  >
                    Test Connection
                  </Button>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">SMTP Host</label>
                      <input
                        type="text"
                        value={settings.smtp_host}
                        onChange={(e) => handleInputChange('smtp_host', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                        placeholder="smtp.gmail.com"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">SMTP Port</label>
                      <input
                        type="number"
                        value={settings.smtp_port}
                        onChange={(e) => handleInputChange('smtp_port', parseInt(e.target.value))}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                        placeholder="587"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">SMTP Username</label>
                      <input
                        type="text"
                        value={settings.smtp_username}
                        onChange={(e) => handleInputChange('smtp_username', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">SMTP Password</label>
                      <div className="relative">
                        <input
                          type={showSecrets.smtp_password ? "text" : "password"}
                          value={settings.smtp_password}
                          onChange={(e) => handleInputChange('smtp_password', e.target.value)}
                          className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                        />
                        <button
                          type="button"
                          onClick={() => toggleSecret('smtp_password')}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          {showSecrets.smtp_password ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">From Name</label>
                      <input
                        type="text"
                        value={settings.email_from_name}
                        onChange={(e) => handleInputChange('email_from_name', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">From Address</label>
                      <input
                        type="email"
                        value={settings.email_from_address}
                        onChange={(e) => handleInputChange('email_from_address', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* File Storage Settings */}
            {activeTab === 'files' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">File Storage Settings</h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Max File Size (MB)</label>
                      <input
                        type="number"
                        value={settings.max_file_size_mb}
                        onChange={(e) => handleInputChange('max_file_size_mb', parseInt(e.target.value))}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                        min="1"
                        max="1000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Storage Provider</label>
                      <select
                        value={settings.storage_provider}
                        onChange={(e) => handleInputChange('storage_provider', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      >
                        <option value="supabase">Supabase Storage</option>
                        <option value="aws">AWS S3</option>
                        <option value="gcp">Google Cloud Storage</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Allowed File Types</label>
                    <input
                      type="text"
                      value={settings.allowed_file_types.join(', ')}
                      onChange={(e) => handleArrayChange('allowed_file_types', e.target.value.split(',').map(s => s.trim()))}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      placeholder="jpg, png, pdf, dwg"
                    />
                    <p className="text-sm text-gray-500 mt-1">Separate file extensions with commas</p>
                  </div>
                </div>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Security Settings</h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Session Timeout (minutes)</label>
                      <input
                        type="number"
                        value={settings.session_timeout_minutes}
                        onChange={(e) => handleInputChange('session_timeout_minutes', parseInt(e.target.value))}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                        min="30"
                        max="1440"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Minimum Password Length</label>
                      <input
                        type="number"
                        value={settings.password_min_length}
                        onChange={(e) => handleInputChange('password_min_length', parseInt(e.target.value))}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                        min="6"
                        max="20"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.require_email_verification}
                        onChange={(e) => handleInputChange('require_email_verification', e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-sm font-medium text-gray-700">Require Email Verification</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.enable_two_factor}
                        onChange={(e) => handleInputChange('enable_two_factor', e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-sm font-medium text-gray-700">Enable Two-Factor Authentication</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Maintenance Settings */}
            {activeTab === 'maintenance' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Maintenance Mode</h2>
                <div className="space-y-4">
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                      <span className="text-sm font-medium text-yellow-800">
                        Maintenance mode will make the site unavailable to regular users
                      </span>
                    </div>
                  </div>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.maintenance_mode}
                      onChange={(e) => handleInputChange('maintenance_mode', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Enable Maintenance Mode</span>
                  </label>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Maintenance Message</label>
                    <textarea
                      value={settings.maintenance_message}
                      onChange={(e) => handleInputChange('maintenance_message', e.target.value)}
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      placeholder="Message to display to users during maintenance"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
