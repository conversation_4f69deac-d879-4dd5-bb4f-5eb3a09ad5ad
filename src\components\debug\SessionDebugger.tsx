"use client";

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

export function SessionDebugger() {
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkSession = async () => {
      try {
        console.log('🔍 SessionDebugger: Checking session...');
        
        // Check session
        const { data: { session }, error } = await supabase.auth.getSession();
        
        // Check user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        const info = {
          timestamp: new Date().toISOString(),
          session: {
            exists: !!session,
            hasUser: !!session?.user,
            userEmail: session?.user?.email,
            userId: session?.user?.id,
            accessToken: session?.access_token ? 'Present' : 'Missing',
            refreshToken: session?.refresh_token ? 'Present' : 'Missing',
            expiresAt: session?.expires_at,
            error: error?.message
          },
          user: {
            exists: !!user,
            email: user?.email,
            id: user?.id,
            error: userError?.message
          },
          localStorage: {
            hasSupabaseAuth: !!localStorage.getItem('sb-' + process.env.NEXT_PUBLIC_SUPABASE_URL?.split('//')[1]?.split('.')[0] + '-auth-token'),
            keys: Object.keys(localStorage).filter(key => key.includes('supabase') || key.includes('auth'))
          }
        };
        
        console.log('🔍 SessionDebugger results:', info);
        setSessionInfo(info);
      } catch (error) {
        console.error('SessionDebugger error:', error);
        setSessionInfo({ error: error.message });
      } finally {
        setLoading(false);
      }
    };

    checkSession();
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">🔍 Session Debug</h3>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <pre className="whitespace-pre-wrap text-xs">
          {JSON.stringify(sessionInfo, null, 2)}
        </pre>
      )}
    </div>
  );
}
