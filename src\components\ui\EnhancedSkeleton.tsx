"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

// Enhanced skeleton component with animations
function EnhancedSkeleton({
  className,
  variant = "default",
  animate = true,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  variant?: "default" | "text" | "circular" | "rectangular" | "card";
  animate?: boolean;
}) {
  const baseClasses = "bg-gray-200 dark:bg-gray-700";
  
  const variantClasses = {
    default: "rounded-md",
    text: "rounded h-4",
    circular: "rounded-full",
    rectangular: "rounded-none",
    card: "rounded-lg",
  };

  const skeletonClasses = cn(
    baseClasses,
    variantClasses[variant],
    animate && "animate-pulse",
    className
  );

  if (animate) {
    return (
      <motion.div
        className={skeletonClasses}
        initial={{ opacity: 0.6 }}
        animate={{ opacity: [0.6, 1, 0.6] }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        {...props}
      />
    );
  }

  return <div className={skeletonClasses} {...props} />;
}

// Dashboard skeleton components
export function DashboardStatsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[1, 2, 3, 4].map((i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <EnhancedSkeleton variant="text" className="w-20" />
              <EnhancedSkeleton variant="text" className="w-16 h-8" />
            </div>
            <EnhancedSkeleton variant="circular" className="w-12 h-12" />
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function ProjectListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="space-y-2">
              <EnhancedSkeleton variant="text" className="w-48 h-6" />
              <EnhancedSkeleton variant="text" className="w-32" />
            </div>
            <EnhancedSkeleton variant="rectangular" className="w-20 h-8 rounded" />
          </div>
          <div className="space-y-2">
            <EnhancedSkeleton variant="text" className="w-full" />
            <EnhancedSkeleton variant="text" className="w-3/4" />
          </div>
          <div className="flex items-center justify-between mt-4">
            <EnhancedSkeleton variant="text" className="w-24" />
            <EnhancedSkeleton variant="rectangular" className="w-16 h-6 rounded" />
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function ProposalListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-start justify-between mb-4">
            <div className="space-y-2 flex-1">
              <div className="flex items-center space-x-3">
                <EnhancedSkeleton variant="text" className="w-40 h-6" />
                <EnhancedSkeleton variant="rectangular" className="w-20 h-6 rounded-full" />
              </div>
              <EnhancedSkeleton variant="text" className="w-32" />
            </div>
            <EnhancedSkeleton variant="rectangular" className="w-16 h-8 rounded" />
          </div>
          <div className="space-y-2">
            <EnhancedSkeleton variant="text" className="w-full" />
            <EnhancedSkeleton variant="text" className="w-2/3" />
          </div>
          <div className="flex items-center justify-between mt-4">
            <EnhancedSkeleton variant="text" className="w-20" />
            <EnhancedSkeleton variant="text" className="w-24" />
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function UserListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
          className="bg-white p-4 rounded-lg shadow-sm border"
        >
          <div className="flex items-center space-x-4">
            <EnhancedSkeleton variant="circular" className="w-12 h-12" />
            <div className="flex-1 space-y-2">
              <EnhancedSkeleton variant="text" className="w-32 h-5" />
              <EnhancedSkeleton variant="text" className="w-48" />
            </div>
            <div className="space-y-2">
              <EnhancedSkeleton variant="rectangular" className="w-16 h-6 rounded-full" />
              <EnhancedSkeleton variant="text" className="w-20" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function MessageListSkeleton({ count = 4 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
          className="bg-white p-4 rounded-lg shadow-sm border"
        >
          <div className="flex items-start space-x-3">
            <EnhancedSkeleton variant="circular" className="w-10 h-10" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center space-x-2">
                <EnhancedSkeleton variant="text" className="w-24 h-4" />
                <EnhancedSkeleton variant="text" className="w-16 h-3" />
              </div>
              <EnhancedSkeleton variant="text" className="w-full h-16" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function TableSkeleton({ rows = 5, columns = 4 }: { rows?: number; columns?: number }) {
  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* Header */}
      <div className="border-b bg-gray-50 p-4">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, i) => (
            <EnhancedSkeleton key={i} variant="text" className="w-20 h-5" />
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="divide-y">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <motion.div
            key={rowIndex}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: rowIndex * 0.05 }}
            className="p-4"
          >
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <EnhancedSkeleton
                  key={colIndex}
                  variant="text"
                  className={cn(
                    "h-4",
                    colIndex === 0 ? "w-32" : colIndex === columns - 1 ? "w-16" : "w-24"
                  )}
                />
              ))}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}

export function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-brown-600 to-brown-700 rounded-lg p-6">
        <div className="flex justify-between items-start">
          <div className="space-y-2">
            <EnhancedSkeleton variant="text" className="w-48 h-8 bg-brown-500" />
            <EnhancedSkeleton variant="text" className="w-64 bg-brown-500" />
          </div>
          <EnhancedSkeleton variant="rectangular" className="w-32 h-10 rounded bg-brown-500" />
        </div>
      </div>

      {/* Stats */}
      <DashboardStatsSkeleton />

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <ProjectListSkeleton />
        </div>
        <div className="space-y-6">
          <UserListSkeleton count={3} />
        </div>
      </div>
    </div>
  );
}

export { EnhancedSkeleton };
