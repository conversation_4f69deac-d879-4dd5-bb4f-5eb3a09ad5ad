"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { DashboardNotifications } from "@/components/shared/DashboardNotifications";
import QualityReviewDashboard from "@/components/quality/QualityReviewDashboard";
import SLAMonitorDashboard from "@/components/quality/SLAMonitorDashboard";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Star,
  MessageSquare,
  Calendar,
  TrendingUp,
  Eye,
  Filter,
  Search,
  RefreshCw
} from "lucide-react";

interface QualityReview {
  id: string;
  project_id: string;
  designer_id: string;
  review_type: string;
  status: string;
  overall_score: number | null;
  feedback: string | null;
  revision_count: number;
  created_at: string;
  project: {
    title: string;
    client: {
      full_name: string;
    };
  };
  designer: {
    full_name: string;
    email: string;
  };
}

interface QualityStats {
  pending_reviews: number;
  completed_today: number;
  average_score: number;
  revision_rate: number;
}

export default function QualityDashboard() {
  const { user, profile } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReview[]>([]);
  const [stats, setStats] = useState<QualityStats>({
    pending_reviews: 0,
    completed_today: 0,
    average_score: 0,
    revision_rate: 0
  });
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('pending');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchReviews();
      fetchStats();
    }
  }, [user, profile, filter]);

  const fetchReviews = async () => {
    try {
      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        console.error('No access token available');
        return;
      }

      // Use API endpoint with proper authentication
      const params = new URLSearchParams({
        status: filter,
        limit: '50',
        page: '1'
      });

      const response = await fetch(`/api/quality/reviews?${params}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      setReviews(result.reviews || []);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    }
  };

  const fetchStats = async () => {
    try {
      // Get pending reviews count
      const { count: pendingCount } = await supabase
        .from('quality_reviews_new')
        .select('*', { count: 'exact', head: true })
        .eq('reviewer_id', user?.id)
        .eq('status', 'pending');

      // Get today's completed reviews
      const today = new Date().toISOString().split('T')[0];
      const { count: todayCount } = await supabase
        .from('quality_reviews_new')
        .select('*', { count: 'exact', head: true })
        .eq('reviewer_id', user?.id)
        .eq('status', 'approved')
        .gte('reviewed_at', today);

      // Get average score
      const { data: scoreData } = await supabase
        .from('quality_reviews_new')
        .select('overall_score')
        .eq('reviewer_id', user?.id)
        .not('overall_score', 'is', null);

      const avgScore = scoreData?.length
        ? scoreData.reduce((sum, item) => sum + (item.overall_score || 0), 0) / scoreData.length
        : 0;

      // Get revision rate
      const { count: totalReviews } = await supabase
        .from('quality_reviews_new')
        .select('*', { count: 'exact', head: true })
        .eq('reviewer_id', user?.id);

      const { count: revisionsNeeded } = await supabase
        .from('quality_reviews_new')
        .select('*', { count: 'exact', head: true })
        .eq('reviewer_id', user?.id)
        .gt('revision_count', 0);

      const revisionRate = totalReviews ? (revisionsNeeded || 0) / totalReviews * 100 : 0;

      setStats({
        pending_reviews: pendingCount || 0,
        completed_today: todayCount || 0,
        average_score: avgScore,
        revision_rate: revisionRate
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'in_review':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'in_review':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const filteredReviews = reviews.filter(review =>
    review.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    review.designer?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quality Dashboard</h1>
          <p className="text-gray-600 mt-2">Review and ensure design quality standards</p>
        </div>

        <div className="flex items-center space-x-4">
          <DashboardNotifications variant="header" role="quality" />
          <Button
            onClick={() => {
              fetchReviews();
              fetchStats();
            }}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* SLA Monitor Dashboard */}
      <SLAMonitorDashboard role="quality" />

      {/* New Integrated Quality Review Dashboard */}
      <QualityReviewDashboard />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
              <p className="text-3xl font-bold text-amber-600">{stats.pending_reviews}</p>
            </div>
            <Clock className="h-8 w-8 text-amber-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Today</p>
              <p className="text-3xl font-bold text-green-600">{stats.completed_today}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-3xl font-bold text-blue-600">{stats.average_score.toFixed(1)}</p>
            </div>
            <Star className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Revision Rate</p>
              <p className="text-3xl font-bold text-purple-600">{stats.revision_rate.toFixed(1)}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="pending">Pending Reviews</option>
              <option value="in_review">In Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="all">All Reviews</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects or designers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Quality Reviews</h2>
          <p className="text-gray-600 mt-1">Manage and track quality reviews</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredReviews.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No reviews found matching your criteria</p>
            </div>
          ) : (
            filteredReviews.map((review) => (
              <div key={review.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(review.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {review.project?.title || 'Untitled Project'}
                      </h3>
                      <span className={getStatusBadge(review.status)}>
                        {review.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Designer:</span> {review.designer?.full_name}
                      </div>
                      <div>
                        <span className="font-medium">Client:</span> {review.project?.client?.full_name}
                      </div>
                      <div>
                        <span className="font-medium">Revisions:</span> {review.revision_count}
                      </div>
                    </div>

                    {review.overall_score && (
                      <div className="flex items-center gap-2 mt-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm font-medium">Score: {review.overall_score}/5</span>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                    >
                      <Eye className="h-4 w-4" />
                      Review
                    </Button>
                    
                    {review.status === 'pending' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                      >
                        <CheckCircle className="h-4 w-4" />
                        Start Review
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
