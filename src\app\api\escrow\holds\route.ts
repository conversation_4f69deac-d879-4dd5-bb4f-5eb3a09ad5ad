import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { EscrowManager } from '@/lib/escrow-manager';

/**
 * API endpoint for escrow holds management
 * Handles creation, retrieval, and status updates of escrow holds
 */

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile and role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const managerId = searchParams.get('managerId');

    // Build query based on user role and parameters
    let query = supabase
      .from('paypal_escrow_holds')
      .select(`
        *,
        milestone:project_milestones(title),
        project:projects(title, status),
        client:profiles!paypal_escrow_holds_client_id_fkey(full_name),
        designer:profiles!paypal_escrow_holds_designer_id_fkey(full_name)
      `);

    // Apply filters based on role
    switch (profile.role) {
      case 'admin':
        // Admins can see all holds
        break;
      case 'manager':
        // Managers can see holds for their assigned projects
        const { data: managerProjects } = await supabase
          .from('project_assignments')
          .select('project_id')
          .eq('manager_id', user.id)
          .eq('status', 'active');

        if (managerProjects && managerProjects.length > 0) {
          const projectIds = managerProjects.map(p => p.project_id);
          query = query.in('project_id', projectIds);
        } else {
          // No projects assigned, return empty result
          return NextResponse.json({ success: true, holds: [] });
        }
        break;
      case 'client':
        // Clients can see holds for their projects
        query = query.eq('escrow_account.client_id', user.id);
        break;
      case 'designer':
        // Designers can see holds for their projects
        query = query.eq('escrow_account.designer_id', user.id);
        break;
      default:
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Apply additional filters
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (managerId && profile.role === 'admin') {
      query = query.eq('escrow_account.manager_id', managerId);
    }

    const { data: holds, error } = await query
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching escrow holds:', error);
      return NextResponse.json({ error: 'Failed to fetch escrow holds' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      holds: holds || []
    });

  } catch (error) {
    console.error('Error in escrow holds GET API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has permission to create escrow holds
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['admin', 'manager'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const {
      transactionId,
      projectId,
      milestoneId,
      grossAmount,
      platformFee,
      processingFee,
      holdReason,
      requiresManagerApproval = true,
      requiresQualityApproval = false,
      autoReleaseDays = 30
    } = body;

    // Validate required fields
    if (!transactionId || !projectId || !grossAmount) {
      return NextResponse.json(
        { error: 'Transaction ID, project ID, and gross amount are required' },
        { status: 400 }
      );
    }

    // Create escrow hold
    const result = await EscrowManager.createEscrowHold({
      transactionId,
      projectId,
      milestoneId,
      grossAmount,
      platformFee: platformFee || 0,
      processingFee: processingFee || 0,
      holdReason,
      requiresManagerApproval,
      requiresQualityApproval,
      autoReleaseDays
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to create escrow hold' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      hold: result.hold,
      message: 'Escrow hold created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error in escrow holds POST API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
