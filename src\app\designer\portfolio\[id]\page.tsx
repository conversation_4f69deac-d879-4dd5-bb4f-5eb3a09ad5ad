'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { getPortfolioProject } from '@/lib/api/portfolio';
import { PortfolioProject } from '@/types/portfolio';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { PortfolioGallery } from '@/components/portfolio/PortfolioGallery';
import { PortfolioImageUpload } from '@/components/portfolio/PortfolioImageUpload';
import { PortfolioProjectForm } from '@/components/portfolio/PortfolioProjectForm';
import {
  ArrowLeftIcon,
  CalendarIcon,
  UserIcon,
  StarIcon,
  EditIcon
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

export default function DesignerPortfolioProjectDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, profile } = useOptimizedAuth();
  const [project, setProject] = useState<PortfolioProject | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [refreshGallery, setRefreshGallery] = useState(0);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    // Ensure only designers can access this page
    if (profile && profile.role !== 'designer') {
      router.push('/');
      return;
    }

    fetchProject();
  }, [user, id, router, profile]);

  const fetchProject = async () => {
    if (!token || !id) return;

    setLoading(true);
    try {
      const projectData = await getPortfolioProject(token, id as string);

      // Ensure the designer owns this project
      if (user && projectData.designer_id !== user.id) {
        router.push('/designer/portfolio');
        return;
      }

      setProject(projectData);
    } catch (error) {
      console.error('Error fetching portfolio project:', error);
      toast({
        title: 'Error',
        description: 'Failed to load project details',
        variant: 'destructive',
      });
      router.push('/designer/portfolio');
    } finally {
      setLoading(false);
    }
  };

  const handleEditSuccess = (updatedProject: PortfolioProject) => {
    setProject(prev => ({
      ...prev!,
      ...updatedProject,
    }));
    setIsEditDialogOpen(false);
  };

  const handleImageAdded = () => {
    setRefreshGallery(prev => prev + 1);
    fetchProject(); // Refresh to get updated cover image
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <Skeleton className="h-10 w-40" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-60 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
        <p className="mb-6">The project you're looking for doesn't exist or you don't have permission to view it.</p>
        <Button onClick={() => router.push('/designer/portfolio')}>
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Portfolio
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push('/designer/portfolio')}
          className="w-fit"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Portfolio
        </Button>

        <div className="flex gap-2">
          <PortfolioImageUpload
            projectId={project.id}
            onImageAdded={handleImageAdded}
          />
          <Button
            variant="outline"
            onClick={() => setIsEditDialogOpen(true)}
          >
            <EditIcon className="h-4 w-4 mr-2" />
            Edit Details
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">{project.title}</h1>
            <div className="flex flex-wrap gap-2">
              {project.category && (
                <Badge variant="outline">{project.category}</Badge>
              )}
              {project.featured && (
                <Badge variant="default" className="bg-amber-500 hover:bg-amber-600">
                  <StarIcon className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
            </div>
          </div>

          {project.description && (
            <div className="prose max-w-none">
              <p>{project.description}</p>
            </div>
          )}

          <div>
            <h2 className="text-xl font-semibold mb-4">Project Gallery</h2>
            <PortfolioGallery
              projectId={project.id}
              isOwner={true}
              onImagesChange={() => setRefreshGallery(prev => prev + 1)}
              key={`gallery-${refreshGallery}`}
            />
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-muted/40 rounded-lg p-6 space-y-4">
            <h2 className="text-xl font-semibold">Project Details</h2>

            {project.client_name && (
              <div className="flex items-start gap-2">
                <UserIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Client</p>
                  <p className="text-muted-foreground">{project.client_name}</p>
                </div>
              </div>
            )}

            {project.completion_date && (
              <div className="flex items-start gap-2">
                <CalendarIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Completion Date</p>
                  <p className="text-muted-foreground">
                    {format(new Date(project.completion_date), 'MMMM yyyy')}
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-start gap-2">
              <CalendarIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-sm font-medium">Added On</p>
                <p className="text-muted-foreground">
                  {format(new Date(project.created_at), 'MMMM d, yyyy')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Project Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Portfolio Project</DialogTitle>
            <DialogDescription>
              Update the details of your project.
            </DialogDescription>
          </DialogHeader>
          <PortfolioProjectForm
            project={project}
            onSuccess={handleEditSuccess}
            onCancel={() => setIsEditDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
