"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Home, ArrowLeft, Search, Mail } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Animated Logo */}
        <motion.div
          className="mb-8"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            duration: 0.8, 
            ease: "easeOut",
            type: "spring",
            stiffness: 120
          }}
        >
          <Image
            src="/seniors-icon.svg"
            alt="Senior's Archi-Firm"
            width={96}
            height={96}
            className="h-20 w-20 md:h-24 md:w-24 mx-auto opacity-60"
            priority
          />
        </motion.div>

        {/* 404 Text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="mb-6"
        >
          <h1 className="text-8xl md:text-9xl font-bold text-primary/20 mb-4">
            404
          </h1>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Page Not Found
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
            The architectural blueprint you're looking for seems to have been misplaced. 
            Let's get you back to solid ground.
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
        >
          <Link href="/">
            <Button variant="default" size="lg" className="group">
              <Home className="mr-2 h-5 w-5 transition-transform group-hover:scale-110" />
              Back to Home
            </Button>
          </Link>
          
          <Link href="/services">
            <Button variant="outline" size="lg" className="group">
              <Search className="mr-2 h-5 w-5 transition-transform group-hover:scale-110" />
              Explore Services
            </Button>
          </Link>
        </motion.div>

        {/* Additional Help */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          className="border-t border-gray-200 pt-8"
        >
          <p className="text-sm text-gray-500 mb-4">
            Still can't find what you're looking for?
          </p>
          <Link href="/contact">
            <Button variant="ghost" size="sm" className="group">
              <Mail className="mr-2 h-4 w-4 transition-transform group-hover:scale-110" />
              Contact Our Team
            </Button>
          </Link>
        </motion.div>

        {/* Decorative Elements */}
        <motion.div
          className="absolute top-10 left-10 w-20 h-20 bg-primary/5 rounded-full"
          animate={{ 
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360]
          }}
          transition={{ 
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-10 right-10 w-16 h-16 bg-primary/10 rounded-full"
          animate={{ 
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0]
          }}
          transition={{ 
            duration: 6,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>
    </div>
  );
}
