"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Star,
  Eye,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  User
} from "lucide-react";

interface QualityReview {
  id: string;
  project_id: string;
  designer_id: string;
  review_type: string;
  status: string;
  overall_score: number | null;
  feedback: string | null;
  revision_count: number;
  created_at: string;
  project: {
    title: string;
    client: {
      full_name: string;
    };
  };
  designer: {
    full_name: string;
    email: string;
  };
}

export default function QualityReviewsPage() {
  const { user, profile } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('pending');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchReviews();
    }
  }, [user, profile, filter]);

  const fetchReviews = async () => {
    try {
      let query = supabase
        .from('quality_reviews')
        .select(`
          *,
          project:projects(title, client:profiles!client_id(full_name)),
          designer:profiles!quality_reviews_designer_id_fkey(full_name, email)
        `)
        .eq('reviewer_id', user?.id);

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      setReviews(data || []);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'in_review':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'in_review':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getPriorityBadge = (revisionCount: number) => {
    if (revisionCount >= 2) {
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
          High Priority
        </span>
      );
    } else if (revisionCount >= 1) {
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded">
          Medium Priority
        </span>
      );
    }
    return null;
  };

  const filteredReviews = reviews.filter(review =>
    review.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    review.designer?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Quality Reviews</h1>
          <p className="text-gray-600 mt-2">Manage and track all quality reviews</p>
        </div>
        <Button
          onClick={fetchReviews}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-amber-600">
                {reviews.filter(r => r.status === 'pending').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-amber-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Review</p>
              <p className="text-2xl font-bold text-blue-600">
                {reviews.filter(r => r.status === 'in_review').length}
              </p>
            </div>
            <Eye className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">
                {reviews.filter(r => r.status === 'approved').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-red-600">
                {reviews.filter(r => r.status === 'rejected').length}
              </p>
            </div>
            <XCircle className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="pending">Pending Reviews</option>
              <option value="in_review">In Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="all">All Reviews</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects or designers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Reviews Queue</h2>
          <p className="text-gray-600 mt-1">Click on any review to start or continue reviewing</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredReviews.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No reviews found matching your criteria</p>
            </div>
          ) : (
            filteredReviews.map((review) => (
              <div key={review.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(review.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {review.project?.title || 'Untitled Project'}
                      </h3>
                      <span className={getStatusBadge(review.status)}>
                        {review.status.replace('_', ' ').toUpperCase()}
                      </span>
                      {getPriorityBadge(review.revision_count)}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium">Designer:</span> {review.designer?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium">Client:</span> {review.project?.client?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">Submitted:</span> {new Date(review.created_at).toLocaleDateString()}
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-sm">
                      <div>
                        <span className="font-medium">Type:</span> {review.review_type}
                      </div>
                      <div>
                        <span className="font-medium">Revisions:</span> {review.revision_count}
                      </div>
                      {review.overall_score && (
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="font-medium">Score: {review.overall_score}/5</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                    >
                      <Eye className="h-4 w-4" />
                      {review.status === 'pending' ? 'Start Review' : 'View Review'}
                    </Button>
                    
                    {review.status === 'pending' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                      >
                        <CheckCircle className="h-4 w-4" />
                        Begin Review
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
