-- Create table for tracking read status of broadcast admin messages
-- This is needed because broadcast messages (to all users or all users of a role)
-- need individual read tracking per user

CREATE TABLE IF NOT EXISTS admin_message_reads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID REFERENCES admin_messages(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one read record per user per message
  UNIQUE(message_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_message_reads_message_id ON admin_message_reads(message_id);
CREATE INDEX IF NOT EXISTS idx_admin_message_reads_user_id ON admin_message_reads(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_message_reads_read_at ON admin_message_reads(read_at);

-- Create table for message templates
CREATE TABLE IF NOT EXISTS admin_message_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'info' CHECK (message_type IN ('info', 'warning', 'success', 'urgent', 'announcement')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  action_required BOOLEAN DEFAULT FALSE,
  category TEXT DEFAULT 'general',
  variables JSONB DEFAULT '[]', -- Array of variable names used in the template
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0
);

-- Create indexes for message templates
CREATE INDEX IF NOT EXISTS idx_admin_message_templates_category ON admin_message_templates(category);
CREATE INDEX IF NOT EXISTS idx_admin_message_templates_message_type ON admin_message_templates(message_type);
CREATE INDEX IF NOT EXISTS idx_admin_message_templates_created_by ON admin_message_templates(created_by);

-- Function to get unread admin messages count for a user
CREATE OR REPLACE FUNCTION get_unread_admin_messages_count(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  user_role TEXT;
  unread_count INTEGER := 0;
BEGIN
  -- Get user role
  SELECT role INTO user_role FROM profiles WHERE id = user_uuid;
  
  IF user_role IS NULL THEN
    RETURN 0;
  END IF;
  
  -- Count unread messages
  SELECT COUNT(*) INTO unread_count
  FROM admin_messages am
  WHERE (
    -- Direct messages to user
    (am.recipient_id = user_uuid AND am.read_at IS NULL)
    OR
    -- Broadcast messages not read by user
    (
      (am.recipient_role = user_role OR am.recipient_role = 'all')
      AND am.recipient_id IS NULL
      AND NOT EXISTS (
        SELECT 1 FROM admin_message_reads amr 
        WHERE amr.message_id = am.id AND amr.user_id = user_uuid
      )
    )
  )
  AND (am.expires_at IS NULL OR am.expires_at > NOW());
  
  RETURN unread_count;
END;
$$ LANGUAGE plpgsql;

-- Function to mark broadcast message as read for a user
CREATE OR REPLACE FUNCTION mark_broadcast_message_read(message_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  message_exists BOOLEAN := FALSE;
  is_broadcast BOOLEAN := FALSE;
BEGIN
  -- Check if message exists and is a broadcast message
  SELECT 
    TRUE,
    (recipient_id IS NULL AND recipient_role IS NOT NULL)
  INTO message_exists, is_broadcast
  FROM admin_messages 
  WHERE id = message_uuid;
  
  IF NOT message_exists THEN
    RETURN FALSE;
  END IF;
  
  -- If it's a direct message, update the read_at field
  IF NOT is_broadcast THEN
    UPDATE admin_messages 
    SET read_at = NOW() 
    WHERE id = message_uuid AND recipient_id = user_uuid;
    RETURN TRUE;
  END IF;
  
  -- If it's a broadcast message, insert into read tracking table
  INSERT INTO admin_message_reads (message_id, user_id, read_at)
  VALUES (message_uuid, user_uuid, NOW())
  ON CONFLICT (message_id, user_id) DO UPDATE SET read_at = NOW();
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to increment template usage count
CREATE OR REPLACE FUNCTION increment_template_usage(template_uuid UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE admin_message_templates 
  SET usage_count = usage_count + 1,
      updated_at = NOW()
  WHERE id = template_uuid;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies for admin_message_reads
ALTER TABLE admin_message_reads ENABLE ROW LEVEL SECURITY;

-- Users can only see their own read status
CREATE POLICY "Users can view own read status" ON admin_message_reads
  FOR SELECT USING (user_id = auth.uid());

-- Users can only insert their own read status
CREATE POLICY "Users can insert own read status" ON admin_message_reads
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Admins can see all read statuses
CREATE POLICY "Admins can view all read statuses" ON admin_message_reads
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Add RLS policies for admin_message_templates
ALTER TABLE admin_message_templates ENABLE ROW LEVEL SECURITY;

-- Only admins can manage templates
CREATE POLICY "Only admins can manage templates" ON admin_message_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Grant necessary permissions
GRANT SELECT, INSERT ON admin_message_reads TO authenticated;
GRANT SELECT ON admin_message_templates TO authenticated;
GRANT ALL ON admin_message_templates TO service_role;
GRANT ALL ON admin_message_reads TO service_role;
