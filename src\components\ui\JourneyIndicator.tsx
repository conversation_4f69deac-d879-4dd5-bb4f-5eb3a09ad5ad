interface JourneyIndicatorProps {
  step: string | number;
  title: string;
  description: string;
  progress: number; // 0 to 1
}

const JourneyIndicator = ({ step, title, description, progress }: JourneyIndicatorProps) => {
  // Convert progress to percentage width (0-100%)
  const progressWidth = `${Math.min(Math.max(progress * 100, 0), 100)}%`;

  return (
    <div className="container mx-auto px-4 mb-8">
      <div className="bg-primary/10 py-2 px-4 md:px-6 rounded-md flex items-center max-w-4xl mx-auto overflow-x-auto whitespace-nowrap">
        <div className="flex items-center flex-shrink-0">
          <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-primary text-white flex items-center justify-center font-bold text-xs md:text-sm">
            {step}
          </div>
          <div className="ml-2 font-medium text-primary text-xs md:text-sm">{title}</div>
        </div>
        <div className="flex-1 mx-3 md:mx-4 h-1 bg-gray-200 relative min-w-[40px]">
          <div
            className="absolute left-0 top-0 h-full bg-primary transition-all duration-500 ease-in-out"
            style={{ width: progressWidth }}
          />
        </div>
        <div className="text-gray-600 text-xs md:text-sm flex-shrink-0">{description}</div>
      </div>
    </div>
  );
};

export default JourneyIndicator;
