'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  Cloud, 
  Wifi, 
  AlertTriangle, 
  CheckCircle, 
  RefreshCw,
  Download,
  Upload,
  Settings,
  Monitor,
  Smartphone
} from 'lucide-react';

interface MigrationStatus {
  storage: {
    total: number;
    migrated: number;
    failed: number;
    inProgress: boolean;
  };
  realtime: {
    subscriptions: number;
    active: number;
    failed: number;
    status: 'connected' | 'disconnected' | 'reconnecting';
  };
  mobile: {
    optimized: boolean;
    touchTargets: number;
    responsivePages: number;
    issues: string[];
  };
}

export default function SystemMigrationPanel() {
  const [status, setStatus] = useState<MigrationStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [migrating, setMigrating] = useState(false);
  const [activeTab, setActiveTab] = useState<'storage' | 'realtime' | 'mobile'>('storage');

  useEffect(() => {
    fetchMigrationStatus();
  }, []);

  const fetchMigrationStatus = async () => {
    try {
      // Get storage migration preview
      const storageResponse = await fetch('/api/admin/migrate-storage');
      const storageData = await storageResponse.json();

      // Mock realtime status (you can implement actual status check)
      const realtimeStatus = {
        subscriptions: 8,
        active: 6,
        failed: 2,
        status: 'connected' as const
      };

      // Mock mobile optimization status
      const mobileStatus = {
        optimized: false,
        touchTargets: 45,
        responsivePages: 12,
        issues: [
          'Small touch targets detected on admin pages',
          'Horizontal scrolling on mobile tables',
          'Non-optimized form inputs'
        ]
      };

      setStatus({
        storage: {
          total: storageData.preview?.totalFilesToMigrate || 0,
          migrated: 0,
          failed: 0,
          inProgress: false
        },
        realtime: realtimeStatus,
        mobile: mobileStatus
      });
    } catch (error) {
      console.error('Error fetching migration status:', error);
    } finally {
      setLoading(false);
    }
  };

  const runStorageMigration = async () => {
    setMigrating(true);
    try {
      const response = await fetch('/api/admin/migrate-storage', {
        method: 'POST'
      });
      const result = await response.json();
      
      if (result.success) {
        setStatus(prev => prev ? {
          ...prev,
          storage: {
            ...prev.storage,
            migrated: result.results.summary.successful,
            failed: result.results.summary.failed,
            inProgress: false
          }
        } : null);
      }
    } catch (error) {
      console.error('Migration failed:', error);
    } finally {
      setMigrating(false);
    }
  };

  const optimizeRealtimeSubscriptions = async () => {
    // Implement realtime optimization
    console.log('Optimizing realtime subscriptions...');
  };

  const optimizeMobileExperience = async () => {
    // Implement mobile optimization
    console.log('Optimizing mobile experience...');
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-brown-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Migration Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('storage')}
          className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'storage'
              ? 'bg-white text-brown-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Cloud className="h-4 w-4" />
          Storage Migration
        </button>
        <button
          onClick={() => setActiveTab('realtime')}
          className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'realtime'
              ? 'bg-white text-brown-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Wifi className="h-4 w-4" />
          Realtime Optimization
        </button>
        <button
          onClick={() => setActiveTab('mobile')}
          className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'mobile'
              ? 'bg-white text-brown-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Smartphone className="h-4 w-4" />
          Mobile Optimization
        </button>
      </div>

      {/* Storage Migration Tab */}
      {activeTab === 'storage' && status && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cloud className="h-5 w-5 text-brown-600" />
              Cloudflare R2 Storage Migration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Migration Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-blue-600">Total Files</p>
                      <p className="text-2xl font-bold text-blue-700">{status.storage.total}</p>
                    </div>
                    <Database className="h-8 w-8 text-blue-500" />
                  </div>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-green-600">Migrated</p>
                      <p className="text-2xl font-bold text-green-700">{status.storage.migrated}</p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  </div>
                </div>
                
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-red-600">Failed</p>
                      <p className="text-2xl font-bold text-red-700">{status.storage.failed}</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                </div>
              </div>

              {/* Migration Progress */}
              {status.storage.total > 0 && (
                <div>
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span>Migration Progress</span>
                    <span>{Math.round((status.storage.migrated / status.storage.total) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-brown-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(status.storage.migrated / status.storage.total) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Migration Actions */}
              <div className="flex gap-4">
                <Button
                  onClick={runStorageMigration}
                  disabled={migrating || status.storage.inProgress}
                  className="flex items-center gap-2"
                >
                  {migrating ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4" />
                  )}
                  {migrating ? 'Migrating...' : 'Start Migration'}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={fetchMigrationStatus}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh Status
                </Button>
              </div>

              {/* Migration Instructions */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <h4 className="font-semibold text-amber-800 mb-2">Required Cloudflare Setup:</h4>
                <ol className="list-decimal list-inside text-sm text-amber-700 space-y-1">
                  <li>Create the following R2 buckets in your Cloudflare dashboard:</li>
                  <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                    <li><code>project-files</code> - For project attachments and submissions</li>
                    <li><code>deliverables</code> - For milestone deliverables</li>
                    <li><code>portfolio-images</code> - For portfolio and inspiration images</li>
                    <li><code>dispute-attachments</code> - For dispute-related files</li>
                  </ul>
                  <li>Ensure your <code>CLOUDFLARE_R2_PUBLIC_URL</code> environment variable is set</li>
                  <li>Verify R2 API credentials are properly configured</li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Realtime Optimization Tab */}
      {activeTab === 'realtime' && status && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wifi className="h-5 w-5 text-brown-600" />
              Realtime Subscriptions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Realtime Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-blue-600">Total Subscriptions</p>
                      <p className="text-2xl font-bold text-blue-700">{status.realtime.subscriptions}</p>
                    </div>
                    <Database className="h-8 w-8 text-blue-500" />
                  </div>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-green-600">Active</p>
                      <p className="text-2xl font-bold text-green-700">{status.realtime.active}</p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  </div>
                </div>
                
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-red-600">Failed</p>
                      <p className="text-2xl font-bold text-red-700">{status.realtime.failed}</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                </div>
              </div>

              {/* Connection Status */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Connection Status:</span>
                <Badge className={
                  status.realtime.status === 'connected' 
                    ? 'bg-green-100 text-green-800 border-green-200'
                    : status.realtime.status === 'reconnecting'
                    ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                    : 'bg-red-100 text-red-800 border-red-200'
                }>
                  {status.realtime.status.toUpperCase()}
                </Badge>
              </div>

              {/* Optimization Actions */}
              <div className="flex gap-4">
                <Button
                  onClick={optimizeRealtimeSubscriptions}
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  Optimize Subscriptions
                </Button>
                
                <Button
                  variant="outline"
                  onClick={fetchMigrationStatus}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Check Status
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mobile Optimization Tab */}
      {activeTab === 'mobile' && status && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5 text-brown-600" />
              Mobile Experience
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Mobile Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-blue-600">Touch Targets</p>
                      <p className="text-2xl font-bold text-blue-700">{status.mobile.touchTargets}</p>
                    </div>
                    <Monitor className="h-8 w-8 text-blue-500" />
                  </div>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-green-600">Responsive Pages</p>
                      <p className="text-2xl font-bold text-green-700">{status.mobile.responsivePages}</p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  </div>
                </div>
                
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-red-600">Issues</p>
                      <p className="text-2xl font-bold text-red-700">{status.mobile.issues.length}</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                </div>
              </div>

              {/* Mobile Issues */}
              {status.mobile.issues.length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Mobile Issues to Fix:</h4>
                  <div className="space-y-2">
                    {status.mobile.issues.map((issue, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-red-700">{issue}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Optimization Actions */}
              <div className="flex gap-4">
                <Button
                  onClick={optimizeMobileExperience}
                  className="flex items-center gap-2"
                >
                  <Smartphone className="h-4 w-4" />
                  Optimize Mobile
                </Button>
                
                <Button
                  variant="outline"
                  onClick={fetchMigrationStatus}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Scan Issues
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
