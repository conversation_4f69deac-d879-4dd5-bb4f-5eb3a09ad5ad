"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  User,
  Calendar,
  ArrowRight
} from "lucide-react";
import { Button } from "@/components/ui/Button";

interface AdminTask {
  id: string;
  title: string;
  description: string;
  type: 'application' | 'project_assignment' | 'milestone_review' | 'dispute' | 'general';
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed';
  due_date: string | null;
  created_at: string;
  related_id?: string;
  related_type?: string;
}

export function AdminTasks() {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<AdminTask[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchAdminTasks();
    }
  }, [user]);

  const fetchAdminTasks = async () => {
    try {
      // For now, we'll create mock admin tasks based on real data
      // In a real implementation, you'd have an admin_tasks table
      
      // Check for pending application status
      const { data: profileData } = await supabase
        .from('profiles')
        .select('application_status, application_date')
        .eq('id', user?.id)
        .single();

      // Check for pending project assignments
      const { data: projectsData } = await supabase
        .from('projects')
        .select('id, title, status, assigned_at')
        .eq('designer_id', user?.id)
        .is('assigned_at', null)
        .limit(3);

      // Check for pending milestones
      const { data: milestonesData } = await supabase
        .from('project_milestones')
        .select(`
          id,
          title,
          status,
          due_date,
          projects(title)
        `)
        .in('status', ['pending', 'active'])
        .limit(3);

      const mockTasks: AdminTask[] = [];

      // Add application status task if pending
      if (profileData?.application_status === 'pending') {
        mockTasks.push({
          id: 'app-status',
          title: 'Application Under Review',
          description: 'Your designer application is being reviewed by our admin team.',
          type: 'application',
          priority: 'high',
          status: 'pending',
          due_date: null,
          created_at: profileData.application_date || new Date().toISOString()
        });
      }

      // Add project assignment tasks
      if (projectsData && projectsData.length > 0) {
        projectsData.forEach((project, index) => {
          mockTasks.push({
            id: `project-${project.id}`,
            title: 'New Project Assignment',
            description: `Project "${project.title}" is awaiting admin assignment.`,
            type: 'project_assignment',
            priority: 'medium',
            status: 'pending',
            due_date: null,
            created_at: new Date().toISOString(),
            related_id: project.id,
            related_type: 'project'
          });
        });
      }

      // Add milestone review tasks
      if (milestonesData && milestonesData.length > 0) {
        milestonesData.forEach((milestone) => {
          if (milestone.status === 'active') {
            mockTasks.push({
              id: `milestone-${milestone.id}`,
              title: 'Milestone Due Soon',
              description: `Milestone "${milestone.title}" in project "${milestone.projects?.title}" is due soon.`,
              type: 'milestone_review',
              priority: 'medium',
              status: 'in_progress',
              due_date: milestone.due_date,
              created_at: new Date().toISOString(),
              related_id: milestone.id,
              related_type: 'milestone'
            });
          }
        });
      }

      // Add some general tasks if no specific tasks
      if (mockTasks.length === 0) {
        mockTasks.push(
          {
            id: 'profile-complete',
            title: 'Complete Your Profile',
            description: 'Add more details to your designer profile to attract clients.',
            type: 'general',
            priority: 'low',
            status: 'pending',
            due_date: null,
            created_at: new Date().toISOString()
          },
          {
            id: 'portfolio-update',
            title: 'Update Portfolio',
            description: 'Add recent projects to showcase your work.',
            type: 'general',
            priority: 'low',
            status: 'pending',
            due_date: null,
            created_at: new Date().toISOString()
          }
        );
      }

      setTasks(mockTasks.slice(0, 5)); // Limit to 5 tasks
    } catch (error) {
      console.error('Error fetching admin tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTaskIcon = (type: AdminTask['type']) => {
    switch (type) {
      case 'application':
        return <User className="h-4 w-4" />;
      case 'project_assignment':
        return <FileText className="h-4 w-4" />;
      case 'milestone_review':
        return <Calendar className="h-4 w-4" />;
      case 'dispute':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: AdminTask['priority']) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: AdminTask['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Admin Tasks</h3>
          <span className="text-sm text-gray-500">{tasks.length} tasks</span>
        </div>
      </div>
      
      <div className="p-6">
        {tasks.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h4>
            <p className="text-gray-600">No pending admin tasks at the moment.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {tasks.map((task) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
                className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="flex-shrink-0 mt-1">
                  {getStatusIcon(task.status)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {task.title}
                    </h4>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                        {getTaskIcon(task.type)}
                        <span className="ml-1 capitalize">{task.priority}</span>
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{task.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-xs text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      {task.due_date ? (
                        <span>Due {formatDate(task.due_date)}</span>
                      ) : (
                        <span>Created {formatDate(task.created_at)}</span>
                      )}
                    </div>
                    
                    {task.related_type && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs h-6 px-2"
                      >
                        View <ArrowRight className="h-3 w-3 ml-1" />
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}
