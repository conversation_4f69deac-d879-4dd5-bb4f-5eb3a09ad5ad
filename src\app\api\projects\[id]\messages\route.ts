import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/projects/[id]/messages
 * Get all messages for a project
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    // Get auth header
    const authHeader = request.headers.get('authorization');
    let user = null;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(token);
      if (!authError && authUser) {
        user = authUser;
      }
    }

    // Verify user has access to this project
    if (user) {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('client_id, designer_id')
        .eq('id', projectId)
        .single();

      if (projectError || !project) {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 });
      }

      // Check if user is part of this project
      if (project.client_id !== user.id && project.designer_id !== user.id) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
      }
    }

    // Get messages with sender info
    const { data: messages, error } = await supabase
      .from('project_messages')
      .select(`
        id,
        content,
        sender_id,
        attachment_url,
        attachment_name,
        attachment_type,
        is_read,
        created_at,
        profiles:sender_id (
          id,
          full_name,
          avatar_url,
          role
        )
      `)
      .eq('project_id', projectId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching messages:', error);
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }

    // Mark messages as read for the current user
    if (user) {
      const unreadMessages = messages
        .filter(msg => !msg.is_read && msg.sender_id !== user.id)
        .map(msg => msg.id);

      if (unreadMessages.length > 0) {
        await supabase
          .from('project_messages')
          .update({ is_read: true })
          .in('id', unreadMessages);
      }
    }

    return NextResponse.json({ messages }, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/projects/[id]/messages:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/projects/[id]/messages
 * Send a new message in a project
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    
    // Get auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify user has access to this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('client_id, designer_id, title')
      .eq('id', projectId)
      .single();

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Check if user is part of this project
    if (project.client_id !== user.id && project.designer_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { content, attachment_url, attachment_name, attachment_type } = await request.json();

    if (!content || content.trim() === '') {
      return NextResponse.json({ error: 'Message content is required' }, { status: 400 });
    }

    // Create message
    const { data: message, error: messageError } = await supabase
      .from('project_messages')
      .insert({
        project_id: projectId,
        sender_id: user.id,
        content: content.trim(),
        attachment_url: attachment_url || null,
        attachment_name: attachment_name || null,
        attachment_type: attachment_type || null,
        is_read: false
      })
      .select(`
        id,
        content,
        sender_id,
        attachment_url,
        attachment_name,
        attachment_type,
        is_read,
        created_at,
        profiles:sender_id (
          id,
          full_name,
          avatar_url,
          role
        )
      `)
      .single();

    if (messageError) {
      console.error('Error creating message:', messageError);
      return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
    }

    // Create notification for the other participant
    const recipientId = project.client_id === user.id ? project.designer_id : project.client_id;
    
    if (recipientId) {
      const { error: notificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: recipientId,
          type: 'message',
          title: 'New Message',
          content: `New message in project: ${project.title}`,
          related_id: projectId,
          read: false
        });

      if (notificationError) {
        console.error('Error creating notification:', notificationError);
      }
    }

    return NextResponse.json(message, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/projects/[id]/messages:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
