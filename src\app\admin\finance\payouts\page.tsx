"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { EnhancedPayoutSystem } from "@/components/admin/EnhancedPayoutSystem";
import {
  ArrowLeft,
  CreditCard,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Calendar,
  User,
  FileText,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  Filter
} from "lucide-react";

type Designer = {
  id: string;
  full_name: string;
  email: string;
  avatar_url: string | null;
  pending_amount: number;
  completed_projects: number;
  active_projects: number;
  last_payout_date: string | null;
  milestones: Milestone[];
  is_expanded: boolean;
};

type Milestone = {
  id: string;
  project_id: string;
  project_title: string;
  title: string;
  amount: number;
  status: string;
  completed_at: string | null;
  is_selected: boolean;
};

export default function ProcessPayouts() {
  const { user } = useOptimizedAuth();

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Access Denied</h2>
          <p className="text-gray-500 mt-2">Please log in to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center">
        <Link href="/admin/finance" className="mr-4">
          <Button variant="ghost" className="p-0 h-auto">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Designer Payouts</h1>
          <p className="text-gray-500">Manage and process designer payments with integrated payout methods</p>
        </div>
      </div>

      <EnhancedPayoutSystem />
    </div>
  );
}
