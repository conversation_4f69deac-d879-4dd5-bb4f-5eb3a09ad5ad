import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    detectSessionInUrl: false,
    // Enhanced session persistence - only use localStorage on client side
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    storageKey: 'supabase.auth.token',
    autoRefreshToken: true,
    // Prevent session loss on tab changes/minimization
    flowType: 'pkce'
  },
  // Disable realtime for better performance and reliability
  realtime: {
    params: {
      eventsPerSecond: 0
    }
  }
});

// Separate client for live chat with realtime enabled
export const supabaseLiveChat = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    detectSessionInUrl: false,
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    storageKey: 'supabase.auth.token',
    autoRefreshToken: true,
    flowType: 'pkce'
  },
  // Enable realtime for live chat functionality
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});
