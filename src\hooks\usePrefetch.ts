"use client";

import { useQueryClient } from '@tanstack/react-query';
import { useOptimizedAuth } from './useOptimizedAuth';
import { dashboardKeys } from './useDashboardData';
import { supabase } from '@/lib/supabase';
import { useEffect } from 'react';

export function usePrefetchDashboardData() {
  const queryClient = useQueryClient();
  const { user, profile } = useOptimizedAuth();

  useEffect(() => {
    if (!user || !profile) return;

    const prefetchData = async () => {
      const role = profile.role;

      // Prefetch projects
      queryClient.prefetchQuery({
        queryKey: dashboardKeys.projectsByUser(user.id, role),
        queryFn: async () => {
          let query = supabase
            .from('projects')
            .select(`
              *,
              client:profiles!projects_client_id_fkey(full_name, email),
              designer:profiles!projects_designer_id_fkey(full_name, email)
            `);

          if (role === 'client') {
            query = query.eq('client_id', user.id);
          } else if (role === 'designer') {
            query = query.eq('designer_id', user.id);
          }

          const { data, error } = await query.order('created_at', { ascending: false });
          if (error) throw error;
          return data;
        },
        staleTime: 2 * 60 * 1000, // 2 minutes
      });

      // Prefetch proposals
      queryClient.prefetchQuery({
        queryKey: dashboardKeys.proposalsByUser(user.id, role),
        queryFn: async () => {
          let query = supabase
            .from('proposals')
            .select(`
              *,
              project:projects(title, client_id),
              designer:profiles!proposals_designer_id_fkey(full_name, email)
            `);

          if (role === 'designer') {
            query = query.eq('designer_id', user.id);
          } else if (role === 'client') {
            query = query.eq('project.client_id', user.id);
          }

          const { data, error } = await query.order('created_at', { ascending: false });
          if (error) throw error;
          return data;
        },
        staleTime: 2 * 60 * 1000, // 2 minutes
      });

      // Prefetch stats
      queryClient.prefetchQuery({
        queryKey: dashboardKeys.stats(user.id, role),
        queryFn: async () => {
          const stats: any = {};

          if (role === 'client') {
            const { count: totalProjects } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true })
              .eq('client_id', user.id);

            const { count: activeProjects } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true })
              .eq('client_id', user.id)
              .eq('status', 'in_progress');

            const { count: completedProjects } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true })
              .eq('client_id', user.id)
              .eq('status', 'completed');

            stats.totalProjects = totalProjects || 0;
            stats.activeProjects = activeProjects || 0;
            stats.completedProjects = completedProjects || 0;

          } else if (role === 'designer') {
            const { count: totalProjects } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true })
              .eq('designer_id', user.id);

            const { count: activeProjects } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true })
              .eq('designer_id', user.id)
              .eq('status', 'in_progress');

            const { count: pendingProposals } = await supabase
              .from('proposals')
              .select('*', { count: 'exact', head: true })
              .eq('designer_id', user.id)
              .eq('status', 'pending');

            stats.totalProjects = totalProjects || 0;
            stats.activeProjects = activeProjects || 0;
            stats.pendingProposals = pendingProposals || 0;

          } else if (role === 'admin') {
            const { count: totalUsers } = await supabase
              .from('profiles')
              .select('*', { count: 'exact', head: true });

            const { count: totalProjects } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true });

            const { count: activeProjects } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true })
              .eq('status', 'in_progress');

            stats.totalUsers = totalUsers || 0;
            stats.totalProjects = totalProjects || 0;
            stats.activeProjects = activeProjects || 0;
          }

          return stats;
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
      });
    };

    // Prefetch data after a short delay to avoid blocking initial render
    const timeoutId = setTimeout(prefetchData, 100);

    return () => clearTimeout(timeoutId);
  }, [user, profile, queryClient]);
}

// Hook for prefetching specific project data
export function usePrefetchProject(projectId: string) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!projectId) return;

    queryClient.prefetchQuery({
      queryKey: dashboardKeys.project(projectId),
      queryFn: async () => {
        const { data, error } = await supabase
          .from('projects')
          .select(`
            *,
            client:profiles!projects_client_id_fkey(full_name, email),
            designer:profiles!projects_designer_id_fkey(full_name, email)
          `)
          .eq('id', projectId)
          .single();

        if (error) throw error;
        return data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });

    // Prefetch project messages using the project_messages table
    queryClient.prefetchQuery({
      queryKey: dashboardKeys.messagesByProject(projectId),
      queryFn: async () => {
        const { data, error } = await supabase
          .from('project_messages')
          .select(`
            id,
            content,
            sender_id,
            attachment_url,
            attachment_name,
            attachment_type,
            is_read,
            created_at,
            profiles:sender_id(full_name, role, avatar_url)
          `)
          .eq('project_id', projectId)
          .order('created_at', { ascending: true });

        if (error) throw error;
        return data;
      },
      staleTime: 30 * 1000, // 30 seconds
    });
  }, [projectId, queryClient]);
}
