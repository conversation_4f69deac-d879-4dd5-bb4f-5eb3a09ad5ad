-- =====================================================
-- SCRIPT 5: CREATE PLATFORM REVENUE TRACKING TABLE
-- =====================================================

-- Platform Revenue Table
CREATE TABLE IF NOT EXISTS platform_revenue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id TEXT NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  revenue_type TEXT NOT NULL CHECK (
    revenue_type IN ('commission', 'processing_fee', 'subscription', 'other')
  ),
  amount DECIMAL(10,2) NOT NULL,
  source TEXT NOT NULL CHECK (
    source IN ('paypal_escrow', 'stripe_escrow', 'direct_payment', 'subscription')
  ),
  status TEXT NOT NULL DEFAULT 'pending_release' CHECK (
    status IN ('pending_release', 'released', 'refunded')
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  released_at TIMESTAMP WITH TIME ZONE,
  CONSTRAINT positive_revenue CHECK (amount >= 0)
);

-- Verify completion
SELECT 'Script 5 completed: Platform revenue table created' as status;
