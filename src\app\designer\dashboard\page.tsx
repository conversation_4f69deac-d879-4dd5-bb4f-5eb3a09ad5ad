"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useDashboardStats, useProjects, useProposals, useConnectedClients } from "@/hooks/useDashboardData";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useResponsive } from "@/components/mobile/ResponsiveLayout";
import { DashboardNotifications } from "@/components/shared/DashboardNotifications";
import QualityIntegration from "@/components/integration/QualityIntegration";
import UnifiedCommunication from "@/components/integration/UnifiedCommunication";
import UnifiedFileManager from "@/components/files/UnifiedFileManager";
import {
  DollarSign,
  FolderKanban,
  Users,
  Settings,
  Award,
  Briefcase,
  FileText,
  MessageSquare,
  Image,
  ArrowRight,
  Plus,
  Eye,
  User,
  Bell,
  CheckCircle,
  AlertCircle,
  Star,
} from "lucide-react";

export default function DesignerDashboard() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [availability, setAvailability] = useState<'available' | 'busy' | 'offline'>('available');
  const { isMobile, isTablet } = useResponsive();

  // Use optimized data hooks
  const { data: stats, isLoading: statsLoading } = useDashboardStats(user?.id || '', 'designer');
  const { data: projects, isLoading: projectsLoading } = useProjects(user?.id || '', 'designer');
  const { data: proposals, isLoading: proposalsLoading } = useProposals(user?.id || '', 'designer');
  const { data: connectedClients = [], isLoading: clientsLoading } = useConnectedClients(user?.id || '', 'designer');

  // Derived data
  const recentProjects = projects?.slice(0, 5) || [];
  const recentProposals = proposals?.slice(0, 5) || [];
  const loading = authLoading || statsLoading || projectsLoading || proposalsLoading || clientsLoading;

  const getAvailabilityColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'busy': return 'bg-yellow-100 text-yellow-800';
      case 'offline': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="space-y-4 lg:space-y-6">
        {/* Header with Availability Status */}
        <div className="bg-gradient-to-r from-brown-600 to-brown-700 text-white border-0 rounded-lg p-6">
          <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-start'}`}>
            <div>
              <h1 className={`font-bold mb-2 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
                Welcome back!
              </h1>
              <p className={`text-brown-100 ${isMobile ? 'text-sm' : 'text-base'}`}>
                Here's what's happening with your projects today.
              </p>
            </div>
            <div className={`flex items-center ${isMobile ? 'justify-between w-full' : 'space-x-4'}`}>
              <DashboardNotifications variant="header" role="designer" />
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getAvailabilityColor(availability)}`}>
                {availability.charAt(0).toUpperCase() + availability.slice(1)}
              </span>
              <Link href="/designer/settings/availability">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-white border-white hover:bg-white hover:text-brown-600"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Update Status
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Grid - Mobile Optimized */}
        <div className={`grid gap-4 ${
          isMobile
            ? 'grid-cols-2'
            : isTablet
              ? 'grid-cols-3'
              : 'grid-cols-5'
        }`}>
          <div
            className={`bg-white rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow ${
              isMobile ? 'p-4' : 'p-6'
            }`}
            onClick={() => window.location.href = '/designer/projects'}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  Active Projects
                </p>
                <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {stats?.activeProjects || 0}
                </p>
              </div>
              <div className={`bg-blue-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
                <FolderKanban className={`text-blue-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow ${
              isMobile ? 'p-4' : 'p-6'
            }`}
            onClick={() => window.location.href = '/designer/briefs'}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  Pending Briefs
                </p>
                <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {stats?.pendingProposals || 0}
                </p>
              </div>
              <div className={`bg-orange-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
                <Briefcase className={`text-orange-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow ${
              isMobile ? 'p-4' : 'p-6'
            }`}
            onClick={() => window.location.href = '/designer/earnings'}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  Total Earnings
                </p>
                <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  $0
                </p>
              </div>
              <div className={`bg-green-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
                <DollarSign className={`text-green-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow ${
              isMobile ? 'p-4' : 'p-6'
            }`}
            onClick={() => window.location.href = '/designer/clients'}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  Connected Clients
                </p>
                <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  {connectedClients.length}
                </p>
              </div>
              <div className={`bg-purple-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
                <Users className={`text-purple-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow ${
              isMobile ? 'p-4' : 'p-6'
            }`}
            onClick={() => window.location.href = '/designer/reviews'}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  Average Rating
                </p>
                <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  4.8
                </p>
              </div>
              <div className={`bg-blue-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
                <Award className={`text-blue-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
              </div>
            </div>
          </div>

          <div
            className={`bg-white rounded-lg shadow-sm border cursor-pointer hover:shadow-md transition-shadow ${
              isMobile ? 'p-4' : 'p-6'
            }`}
            onClick={() => window.location.href = '/designer/quality'}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  Quality Score
                </p>
                <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                  4.2/5
                </p>
              </div>
              <div className={`bg-green-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
                <CheckCircle className={`text-green-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
              </div>
            </div>
          </div>
        </div>

        {/* Main Dashboard Grid - Mobile Responsive */}
        <div className={`grid gap-4 lg:gap-6 ${
          isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-3'
        }`}>
          {/* Left Column - Main Content */}
          <div className={`space-y-4 lg:space-y-6 ${
            isMobile ? 'order-1' : 'lg:col-span-2'
          }`}>
            {/* Connected Clients */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              className="bg-white rounded-lg shadow-sm border"
            >
              <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
                <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'justify-between items-center'}`}>
                  <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                    Connected Clients
                  </h3>
                  <Link href="/designer/clients">
                    <Button
                      variant="outline"
                      size="sm"
                      className={isMobile ? 'w-full justify-center' : ''}
                    >
                      View All
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
              <div className={isMobile ? 'p-4' : 'p-6'}>
                {clientsLoading ? (
                  <div className={`text-center ${isMobile ? 'py-6' : 'py-8'}`}>
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown-600 mx-auto mb-4"></div>
                    <p className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
                      Loading clients...
                    </p>
                  </div>
                ) : connectedClients.length === 0 ? (
                  <div className={`text-center ${isMobile ? 'py-6' : 'py-8'}`}>
                    <Users className={`text-gray-400 mx-auto mb-4 ${isMobile ? 'h-8 w-8' : 'h-12 w-12'}`} />
                    <p className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
                      No connected clients yet
                    </p>
                    <p className={`text-gray-400 mb-4 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      Clients will appear here when you have active projects or connections
                    </p>
                  </div>
                ) : (
                  <div className={`space-y-${isMobile ? '3' : '4'}`}>
                    {connectedClients.slice(0, 4).map((client: any) => (
                      <div key={client.id} className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-3'}`}>
                        <div className="flex-shrink-0">
                          {client.avatar_url ? (
                            <img
                              src={client.avatar_url}
                              alt={client.full_name}
                              className={`rounded-full object-cover ${isMobile ? 'h-8 w-8' : 'h-10 w-10'}`}
                            />
                          ) : (
                            <div className={`rounded-full bg-gray-200 flex items-center justify-center ${
                              isMobile ? 'h-8 w-8' : 'h-10 w-10'
                            }`}>
                              <User className={`text-gray-500 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className={`font-medium text-gray-900 truncate ${isMobile ? 'text-sm' : 'text-sm'}`}>
                            {client.full_name}
                          </p>
                          <p className={`text-gray-400 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                            via {client.source}
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          <Link href={`/designer/messages?client=${client.id}`}>
                            <Button
                              size="sm"
                              variant="outline"
                              className={isMobile ? 'p-2' : ''}
                            >
                              <MessageSquare className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                    {connectedClients.length > 4 && (
                      <div className="text-center pt-2">
                        <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          +{connectedClients.length - 4} more clients
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </motion.div>

            {/* Quality Integration */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
              className="bg-white rounded-lg shadow-sm border"
            >
              <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
                <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                  Quality Reviews
                </h3>
              </div>
              <div className={isMobile ? 'p-4' : 'p-6'}>
                <QualityIntegration role="designer" userId={user?.id} />
              </div>
            </motion.div>
          </div>

          {/* Right Column - Sidebar */}
          <div className={`space-y-4 lg:space-y-6 ${
            isMobile ? 'order-2' : ''
          }`}>
            {/* Recent Projects */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
              className="bg-white rounded-lg shadow-sm border"
            >
              <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
                <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'justify-between items-center'}`}>
                  <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                    Recent Projects
                  </h3>
                  <Link href="/designer/projects">
                    <Button
                      variant="outline"
                      size="sm"
                      className={isMobile ? 'w-full justify-center' : ''}
                    >
                      View All
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
              <div className={isMobile ? 'p-4' : 'p-6'}>
                {recentProjects.length === 0 ? (
                  <div className={`text-center ${isMobile ? 'py-6' : 'py-8'}`}>
                    <FolderKanban className={`text-gray-400 mx-auto mb-4 ${isMobile ? 'h-8 w-8' : 'h-12 w-12'}`} />
                    <p className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
                      No active projects
                    </p>
                    <p className={`text-gray-400 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      Your active projects will appear here
                    </p>
                  </div>
                ) : (
                  <div className={`space-y-${isMobile ? '3' : '4'}`}>
                    {recentProjects.map((project: any) => (
                      <div
                        key={project.id}
                        className={`flex items-center justify-between border border-gray-200 rounded-lg hover:bg-gray-50 ${
                          isMobile ? 'p-3' : 'p-4'
                        }`}
                      >
                        <div className="flex-1 min-w-0">
                          <h4 className={`font-medium text-gray-900 truncate ${isMobile ? 'text-sm' : 'text-base'}`}>
                            {project.title}
                          </h4>
                          <p className={`text-gray-500 truncate ${isMobile ? 'text-xs' : 'text-sm'}`}>
                            Client: {project.client?.full_name}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2 flex-shrink-0 ml-3">
                          <Link href={`/designer/projects/${project.id}`}>
                            <Button
                              size="sm"
                              variant="outline"
                              className={isMobile ? 'px-2 py-1' : ''}
                            >
                              <Eye className={`${isMobile ? 'h-3 w-3 mr-1' : 'h-4 w-4 mr-2'}`} />
                              {isMobile ? '' : 'View'}
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>

            {/* File Manager */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.8 }}
              className="bg-white rounded-lg shadow-sm border"
            >
              <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
                <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                  File Manager
                </h3>
              </div>
              <div className={isMobile ? 'p-4' : 'p-6'}>
                <UnifiedFileManager role="designer" compact={true} />
              </div>
            </motion.div>

            {/* Communication Center */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.9 }}
              className="bg-white rounded-lg shadow-sm border"
            >
              <div className={`border-b ${isMobile ? 'p-4' : 'p-6'}`}>
                <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                  Communication
                </h3>
              </div>
              <div className={isMobile ? 'p-4' : 'p-6'}>
                <UnifiedCommunication role="designer" compact={true} />
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
