"use client";

import { useState, useEffect, useRef } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { usePollingMessages, useOptimisticMessages, useMessageNotifications } from '@/hooks/usePollingMessages';
import { motion, AnimatePresence } from "framer-motion";
import { messagingKeys, messagingCacheUtils, messagingQueryConfig } from "@/hooks/useMessagingKeys";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import {
  Send,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Search,
  ArrowLeft,
  Check,
  CheckCheck,
  Reply,
  Forward,
  Copy,
  Trash2,
  Edit3,
  MessageSquare,
  X,
  RefreshCw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";

interface Conversation {
  id: string;
  type: string;
  title: string;
  participants: Array<{
    id: string;
    full_name: string;
    avatar_url: string | null;
    role: string;
  }>;
  latest_message?: {
    content: string;
    created_at: string;
    sender_id: string;
    profiles: {
      full_name: string;
    };
  };
  unread_count: number;
  is_muted: boolean;
  last_message_at: string;
}

interface Message {
  id: string;
  content: string;
  message_type: string;
  created_at: string;
  sender_id: string;
  reply_to_id?: string;
  edited_at?: string;
  profiles: {
    id: string;
    full_name: string;
    avatar_url: string | null;
    role: string;
  };
  message_attachments?: Array<{
    id: string;
    file_url: string;
    file_name: string;
    file_type: string;
    thumbnail_url?: string;
  }>;
  message_reactions?: Array<{
    emoji: string;
    user_id: string;
    profiles: {
      full_name: string;
    };
  }>;
  reply_to?: {
    id: string;
    content: string;
    profiles: {
      full_name: string;
    };
  };
  read_by: Array<{
    user_id: string;
    read_at: string;
    profiles: {
      full_name: string;
      avatar_url: string | null;
    };
  }>;
  is_read_by_me: boolean;
}

interface ModernMessagingProps {
  initialConversationId?: string;
  designerId?: string; // For direct messaging - can be designer ID (from client) or client ID (from designer)
}

export function ModernMessaging({ initialConversationId, designerId }: ModernMessagingProps) {
  const { user } = useOptimizedAuth();
  const queryClient = useQueryClient();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<string | null>(initialConversationId || null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showMobileConversations, setShowMobileConversations] = useState(true);
  const [conversationsLoaded, setConversationsLoaded] = useState(false);
  const [creatingConversation, setCreatingConversation] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);

  // Polling-based message sync
  const { refresh: refreshMessages } = usePollingMessages({
    conversationId: activeConversation || undefined,
    onNewMessage: (message) => {
      console.log('📨 New message received:', message);
      fetchMessages(); // Refresh messages when new message arrives
      fetchConversations(); // Update conversation list
    },
    onConversationUpdate: () => {
      fetchConversations(); // Refresh conversations
    },
    enabled: !!activeConversation,
    pollInterval: 5000 // Poll every 5 seconds
  });

  // Optimistic updates for better UX
  const { addOptimisticMessage, updateOptimisticMessage } = useOptimisticMessages(activeConversation || '');

  // Message notifications
  const { showNotification, requestPermission } = useMessageNotifications();

  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user]);

  useEffect(() => {
    if (activeConversation) {
      fetchMessages();
      setShowMobileConversations(false);

      // Request notification permission when starting a conversation
      requestPermission();
    }
  }, [activeConversation, requestPermission]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle direct messaging from client dashboard
  useEffect(() => {
    if (designerId && user && conversationsLoaded) {
      findOrCreateDirectConversation(designerId);
    }
  }, [designerId, user, conversationsLoaded]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const fetchConversations = async () => {
    try {
      // Get the current session to include in the request
      const { data: { session: currentSession } } = await supabase.auth.getSession();

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // Add authorization header if session exists
      if (currentSession?.access_token) {
        headers['Authorization'] = `Bearer ${currentSession.access_token}`;
      }

      const response = await fetch('/api/conversations', {
        headers
      });

      if (response.ok) {
        const data = await response.json();
        setConversations(data);
        setConversationsLoaded(true);

        // Auto-select first conversation if none selected and no designerId provided
        if (!activeConversation && data.length > 0 && !designerId) {
          setActiveConversation(data[0].id);
        }
      } else {
        console.error('Failed to fetch conversations:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async () => {
    if (!activeConversation) return;

    try {
      // Get the current session to include in the request
      const { data: { session: messageSession } } = await supabase.auth.getSession();

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // Add authorization header if session exists
      if (messageSession?.access_token) {
        headers['Authorization'] = `Bearer ${messageSession.access_token}`;
      }

      const response = await fetch(`/api/conversations/${activeConversation}/messages`, {
        headers
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const findOrCreateDirectConversation = async (otherUserId: string) => {
    if (creatingConversation) return; // Prevent multiple simultaneous calls

    try {
      setCreatingConversation(true);
      console.log('Looking for conversation with user:', otherUserId);
      console.log('Current conversations:', conversations);

      // First, try to find existing conversation (either direct or project-based)
      const existingConv = conversations.find(conv =>
        conv.participants.some(p => p.id === otherUserId)
      );

      if (existingConv) {
        console.log('Found existing conversation:', existingConv.id);
        setActiveConversation(existingConv.id);
        setShowMobileConversations(false); // Show chat on mobile
        return;
      }

      console.log('No existing conversation found, creating new one...');

      // Check if there's a project relationship first
      // Get the current session to include in the request
      const { data: { session: createSession } } = await supabase.auth.getSession();

      const projectHeaders: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // Add authorization header if session exists
      if (createSession?.access_token) {
        projectHeaders['Authorization'] = `Bearer ${createSession.access_token}`;
      }

      const projectResponse = await fetch(`/api/projects/find-by-participants?user1=${user?.id}&user2=${otherUserId}`, {
        headers: projectHeaders
      });

      let projectId = null;
      let projectTitle = null;

      if (projectResponse.ok) {
        const projectData = await projectResponse.json();
        if (projectData) {
          projectId = projectData.id;
          projectTitle = projectData.title;
        }
      }

      // Create new conversation (project-based if project exists, otherwise direct)
      const conversationData = {
        type: projectId ? 'project' : 'direct',
        participant_ids: [otherUserId],
        ...(projectId && { project_id: projectId }),
        ...(projectTitle && { title: `Project: ${projectTitle}` })
      };

      console.log('Creating conversation with data:', conversationData);

      // Use the same session for conversation creation
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // Add authorization header if session exists
      if (createSession?.access_token) {
        headers['Authorization'] = `Bearer ${createSession.access_token}`;
      }

      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers,
        body: JSON.stringify(conversationData)
      });

      if (response.ok) {
        const newConv = await response.json();
        console.log('Created new conversation:', newConv.id);
        setActiveConversation(newConv.id);
        setShowMobileConversations(false); // Show chat on mobile
        await fetchConversations(); // Refresh conversations list
      } else {
        const errorData = await response.json();
        console.error('Failed to create conversation:', errorData);
        alert('Failed to create conversation. Please try again.');
      }
    } catch (error) {
      console.error('Error creating direct conversation:', error);
      alert('Error creating conversation. Please try again.');
    } finally {
      setCreatingConversation(false);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !activeConversation || sending) return;

    const messageContent = newMessage.trim();
    const replyToId = replyingTo?.id;

    // Clear input immediately for better UX
    setNewMessage("");
    setReplyingTo(null);
    setSending(true);

    // Add optimistic message
    const optimisticMessage = {
      content: messageContent,
      sender_id: user?.id,
      reply_to_id: replyToId,
      profiles: {
        id: user?.id,
        full_name: user?.user_metadata?.full_name || 'You',
        avatar_url: user?.user_metadata?.avatar_url,
        role: user?.user_metadata?.role
      }
    };

    const removeOptimistic = addOptimisticMessage(optimisticMessage);

    try {
      // Get the current session to include in the request
      const { data: { session: sendSession } } = await supabase.auth.getSession();

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // Add authorization header if session exists
      if (sendSession?.access_token) {
        headers['Authorization'] = `Bearer ${sendSession.access_token}`;
      }

      const response = await fetch(`/api/conversations/${activeConversation}/messages`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          content: messageContent,
          reply_to_id: replyToId
        })
      });

      if (response.ok) {
        const realMessage = await response.json();
        // Update optimistic message with real data
        updateOptimisticMessage(`temp_${Date.now()}`, realMessage);

        // Immediate cache refresh - NO DELAYS
        if (activeConversation && user?.id) {
          await messagingCacheUtils.refreshAfterSend(queryClient, activeConversation, user.id);
        }

        // Also refresh local state
        await fetchMessages();
        await fetchConversations();

        toast.success('Message sent');
      } else {
        // Remove optimistic message on error
        removeOptimistic();
        const errorText = await response.text();
        console.error('Failed to send message:', errorText);
        toast.error(`Failed to send message: ${errorText}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Remove optimistic message on error
      removeOptimistic();
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const getMessageStatus = (message: Message) => {
    if (message.sender_id !== user?.id) return null;

    const readByOthers = message.read_by.filter(r => r.user_id !== user?.id);

    if (readByOthers.length === 0) {
      return <Check className="h-4 w-4 text-gray-400" />;
    } else {
      return <CheckCheck className="h-4 w-4 text-blue-500" />;
    }
  };

  const activeConversationData = conversations.find(c => c.id === activeConversation);
  const otherParticipant = activeConversationData?.participants[0];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="flex h-[600px] bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* Conversations List */}
      <div className={`w-full md:w-1/3 border-r border-gray-200 flex flex-col ${showMobileConversations ? 'block' : 'hidden md:block'}`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Messages</h2>
            <Button variant="ghost" size="sm">
              <MoreVertical className="h-5 w-5" />
            </Button>
          </div>

          {/* Search */}
          <div className="mt-3 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Conversations */}
        <div className="flex-1 overflow-y-auto">
          {conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <p>No conversations yet</p>
              <p className="text-sm">Start messaging with your designer or clients</p>
            </div>
          ) : (
            conversations
              .filter(conv =>
                searchQuery === "" ||
                conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                conv.participants.some(p => p.full_name.toLowerCase().includes(searchQuery.toLowerCase()))
              )
              .map((conversation) => (
                <motion.div
                  key={conversation.id}
                  whileHover={{ backgroundColor: "#f9fafb" }}
                  onClick={() => setActiveConversation(conversation.id)}
                  className={`p-4 cursor-pointer border-b border-gray-100 ${
                    activeConversation === conversation.id ? 'bg-brown-50 border-l-4 border-l-brown-600' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {/* Avatar */}
                    <div className="relative">
                      {conversation.participants[0]?.avatar_url ? (
                        <img
                          src={conversation.participants[0].avatar_url}
                          alt={conversation.participants[0].full_name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-brown-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-medium">
                            {conversation.participants[0]?.full_name?.charAt(0) || '?'}
                          </span>
                        </div>
                      )}
                      {conversation.unread_count > 0 && (
                        <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                          {conversation.unread_count > 9 ? '9+' : conversation.unread_count}
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {conversation.participants[0]?.full_name || conversation.title}
                        </h3>
                        <span className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(conversation.last_message_at), { addSuffix: true })}
                        </span>
                      </div>

                      {conversation.latest_message && (
                        <p className="text-sm text-gray-600 truncate mt-1">
                          {conversation.latest_message.sender_id === user?.id ? 'You: ' : ''}
                          {conversation.latest_message.content}
                        </p>
                      )}

                      {conversation.type === 'project' && (
                        <p className="text-xs text-brown-600 mt-1">Project Conversation</p>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))
          )}
        </div>
      </div>

      {/* Chat Area */}
      <div className={`flex-1 flex flex-col ${showMobileConversations ? 'hidden md:flex' : 'flex'}`}>
        {activeConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="md:hidden"
                    onClick={() => setShowMobileConversations(true)}
                  >
                    <ArrowLeft className="h-5 w-5" />
                  </Button>

                  {otherParticipant?.avatar_url ? (
                    <img
                      src={otherParticipant.avatar_url}
                      alt={otherParticipant.full_name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-brown-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-medium">
                        {otherParticipant?.full_name?.charAt(0) || '?'}
                      </span>
                    </div>
                  )}

                  <div>
                    <h3 className="font-medium text-gray-900">
                      {otherParticipant?.full_name || activeConversationData?.title}
                    </h3>
                    <p className="text-sm text-gray-500 capitalize">
                      {otherParticipant?.role || 'User'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={refreshMessages}
                    title="Refresh messages"
                  >
                    <RefreshCw className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Phone className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Video className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Reply Banner */}
            <AnimatePresence>
              {replyingTo && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="px-4 py-2 bg-brown-50 border-b border-brown-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Reply className="h-4 w-4 text-brown-600" />
                      <span className="text-sm text-brown-800">
                        Replying to {replyingTo.profiles.full_name}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setReplyingTo(null)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-brown-700 truncate mt-1 ml-6">
                    {replyingTo.content}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageSquare className="h-8 w-8 text-gray-400" />
                  </div>
                  <p className="text-gray-500">No messages yet</p>
                  <p className="text-sm text-gray-400">Start the conversation by sending a message</p>
                </div>
              ) : (
                messages.map((message, index) => {
                  const isFromMe = message.sender_id === user?.id;
                  const showAvatar = !isFromMe && (
                    index === 0 ||
                    messages[index - 1].sender_id !== message.sender_id ||
                    new Date(message.created_at).getTime() - new Date(messages[index - 1].created_at).getTime() > 300000 // 5 minutes
                  );

                  return (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2 }}
                      className={`flex ${isFromMe ? 'justify-end' : 'justify-start'} group`}
                    >
                      <div className={`flex items-end space-x-2 max-w-[70%] ${isFromMe ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        {/* Avatar */}
                        {showAvatar && !isFromMe && (
                          <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                            {message.profiles.avatar_url ? (
                              <img
                                src={message.profiles.avatar_url}
                                alt={message.profiles.full_name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                                <span className="text-xs font-medium text-gray-600">
                                  {message.profiles.full_name.charAt(0)}
                                </span>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Message Bubble */}
                        <div
                          className={`relative px-4 py-2 rounded-2xl ${
                            isFromMe
                              ? 'bg-brown-600 text-white'
                              : 'bg-gray-100 text-gray-900'
                          } ${showAvatar || isFromMe ? '' : 'ml-10'}`}
                        >
                          {/* Reply Reference */}
                          {message.reply_to && (
                            <div className={`mb-2 p-2 rounded-lg border-l-4 ${
                              isFromMe
                                ? 'bg-brown-700 border-brown-400'
                                : 'bg-gray-200 border-gray-400'
                            }`}>
                              <p className={`text-xs font-medium ${isFromMe ? 'text-brown-200' : 'text-gray-600'}`}>
                                {message.reply_to.profiles.full_name}
                              </p>
                              <p className={`text-sm ${isFromMe ? 'text-brown-100' : 'text-gray-700'}`}>
                                {message.reply_to.content}
                              </p>
                            </div>
                          )}

                          {/* Message Content */}
                          <p className="text-sm whitespace-pre-wrap break-words">
                            {message.content}
                          </p>

                          {/* Message Info */}
                          <div className={`flex items-center justify-end space-x-1 mt-1 ${
                            isFromMe ? 'text-brown-200' : 'text-gray-500'
                          }`}>
                            <span className="text-xs">
                              {formatMessageTime(message.created_at)}
                            </span>
                            {message.edited_at && (
                              <span className="text-xs">(edited)</span>
                            )}
                            {getMessageStatus(message)}
                          </div>

                          {/* Message Actions */}
                          <div className={`absolute top-0 ${isFromMe ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} opacity-0 group-hover:opacity-100 transition-opacity`}>
                            <div className="flex items-center space-x-1 bg-white shadow-lg rounded-lg p-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setReplyingTo(message)}
                                className="h-8 w-8 p-0"
                              >
                                <Reply className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <Forward className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              {isFromMe && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                  >
                                    <Edit3 className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-end space-x-2">
                <Button variant="ghost" size="sm" className="flex-shrink-0">
                  <Paperclip className="h-5 w-5" />
                </Button>

                <div className="flex-1 relative">
                  <textarea
                    ref={messageInputRef}
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type a message..."
                    className="w-full px-4 py-2 border border-gray-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent resize-none max-h-32"
                    rows={1}
                    style={{
                      height: 'auto',
                      minHeight: '40px'
                    }}
                    onInput={(e) => {
                      const target = e.target as HTMLTextAreaElement;
                      target.style.height = 'auto';
                      target.style.height = target.scrollHeight + 'px';
                    }}
                  />
                </div>

                <Button variant="ghost" size="sm" className="flex-shrink-0">
                  <Smile className="h-5 w-5" />
                </Button>

                <Button
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || sending}
                  className="flex-shrink-0 bg-brown-600 hover:bg-brown-700 text-white rounded-full p-2"
                >
                  <Send className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              {creatingConversation ? (
                <>
                  <div className="w-16 h-16 bg-brown-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown-600"></div>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Creating conversation...</h3>
                  <p className="text-gray-500">Please wait while we set up your chat</p>
                </>
              ) : (
                <>
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageSquare className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
                  <p className="text-gray-500">Choose a conversation from the list to start messaging</p>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
