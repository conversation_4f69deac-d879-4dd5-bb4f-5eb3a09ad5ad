"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Briefcase,
  Clock,
  DollarSign,
  MapPin,
  User,
  Eye,
  FileText,
  AlertCircle,
  Filter,
  Search,
  Calendar,
  Target,
  ChevronRight
} from "lucide-react";

interface ProjectBrief {
  id: string;
  title: string;
  description: string;
  project_type: string;
  location: string;
  budget_range: string;
  timeline_preference: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  client_name: string;
  client_avatar: string | null;
  created_at: string;
  status: string;
  proposal_status?: 'none' | 'draft' | 'submitted' | 'under_review' | 'accepted' | 'rejected' | 'withdrawn';
  proposal_id?: string;
}

export default function DesignerBriefs() {
  const { user } = useOptimizedAuth();
  const [briefs, setBriefs] = useState<ProjectBrief[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [urgencyFilter, setUrgencyFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  useEffect(() => {
    if (user) {
      fetchBriefs();
    }
  }, [user]);

  const fetchBriefs = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Fetch briefs from the project_briefs table
      // Show briefs that are either assigned to this designer OR pending platform assignment
      const { data, error } = await supabase
        .from('project_briefs')
        .select(`
          id,
          title,
          description,
          project_type,
          location,
          budget_range,
          timeline_preference,
          urgency,
          status,
          created_at,
          assigned_designer_id,
          profiles!project_briefs_client_id_fkey(
            id,
            full_name,
            avatar_url
          )
        `)
        .or(`assigned_designer_id.eq.${user.id},and(assigned_designer_id.is.null,status.eq.pending)`)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Fetch proposal status for each brief
      const briefIds = (data || []).map(brief => brief.id);
      const { data: proposalsData } = await supabase
        .from('project_proposals_enhanced')
        .select('id, brief_id, status')
        .eq('designer_id', user.id)
        .in('brief_id', briefIds);

      const proposalsByBrief = (proposalsData || []).reduce((acc, proposal) => {
        acc[proposal.brief_id] = proposal;
        return acc;
      }, {} as Record<string, any>);

      const formattedBriefs: ProjectBrief[] = (data || []).map(brief => {
        const proposal = proposalsByBrief[brief.id];
        return {
          id: brief.id,
          title: brief.title,
          description: brief.description,
          project_type: brief.project_type || 'other',
          location: brief.location || 'Not specified',
          budget_range: brief.budget_range || 'Not specified',
          timeline_preference: brief.timeline_preference || 'Not specified',
          urgency: brief.urgency,
          client_name: Array.isArray(brief.profiles) && brief.profiles[0]?.full_name ? brief.profiles[0].full_name : 'Unknown Client',
          client_avatar: Array.isArray(brief.profiles) && brief.profiles[0]?.avatar_url ? brief.profiles[0].avatar_url : null,
          created_at: brief.created_at,
          status: brief.status,
          proposal_status: proposal ? proposal.status : 'none',
          proposal_id: proposal ? proposal.id : undefined
        };
      });

      setBriefs(formattedBriefs);
    } catch (error) {
      console.error('Error fetching briefs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProposalStatusColor = (status: string) => {
    switch (status) {
      case 'none': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'draft': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'submitted': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'under_review': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'accepted': return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      case 'withdrawn': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProposalStatusText = (status: string) => {
    switch (status) {
      case 'none': return 'No Proposal';
      case 'draft': return 'Draft Saved';
      case 'submitted': return 'Proposal Sent';
      case 'under_review': return 'Under Review';
      case 'accepted': return 'Accepted';
      case 'rejected': return 'Rejected';
      case 'withdrawn': return 'Withdrawn';
      default: return 'Unknown';
    }
  };

  const getBudgetDisplay = (budgetRange: string) => {
    return budgetRange.replace(/_/g, ' - $').replace('k', 'K');
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return date.toLocaleDateString();
  };

  const filteredBriefs = briefs.filter(brief => {
    const matchesSearch = brief.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         brief.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         brief.client_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesUrgency = urgencyFilter === 'all' || brief.urgency === urgencyFilter;
    const matchesType = typeFilter === 'all' || brief.project_type === typeFilter;

    return matchesSearch && matchesUrgency && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Project Briefs</h1>
          <p className="text-gray-600">Review and respond to new project opportunities</p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500">
            {filteredBriefs.length} brief{filteredBriefs.length !== 1 ? 's' : ''} available
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search briefs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={urgencyFilter}
            onChange={(e) => setUrgencyFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Urgencies</option>
            <option value="urgent">Urgent</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Types</option>
            <option value="residential_interior">Residential Interior</option>
            <option value="commercial_interior">Commercial Interior</option>
            <option value="renovation">Renovation</option>
            <option value="new_construction">New Construction</option>
            <option value="consultation">Consultation</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      {/* Briefs List */}
      {filteredBriefs.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <Briefcase className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {briefs.length === 0 ? 'No briefs available' : 'No briefs match your filters'}
          </h3>
          <p className="text-gray-500 mb-4">
            {briefs.length === 0
              ? 'New project briefs will appear here when clients submit them'
              : 'Try adjusting your search terms or filters'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredBriefs.map((brief) => (
            <motion.div
              key={brief.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">{brief.title}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getUrgencyColor(brief.urgency)}`}>
                      {brief.urgency.toUpperCase()}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getProposalStatusColor(brief.proposal_status || 'none')}`}>
                      {getProposalStatusText(brief.proposal_status || 'none')}
                    </span>
                  </div>

                  <p className="text-gray-600 mb-4 line-clamp-2">{brief.description}</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="h-4 w-4 mr-2" />
                      {brief.client_name}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="h-4 w-4 mr-2" />
                      {brief.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <DollarSign className="h-4 w-4 mr-2" />
                      {getBudgetDisplay(brief.budget_range)}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-2" />
                      {brief.timeline_preference.replace(/_/g, ' ')}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">
                      Posted {getTimeAgo(brief.created_at)}
                    </span>
                    <div className="flex items-center space-x-2">
                      <Link href={`/designer/briefs/${brief.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </Link>
                      {brief.proposal_status === 'none' && (
                        <Link href={`/designer/briefs/${brief.id}/proposal`}>
                          <Button size="sm" className="bg-brown-600 hover:bg-brown-700 text-white">
                            <FileText className="h-4 w-4 mr-2" />
                            Create Proposal
                          </Button>
                        </Link>
                      )}
                      {brief.proposal_status === 'draft' && (
                        <Link href={`/designer/proposals/${brief.proposal_id}/edit`}>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                            <FileText className="h-4 w-4 mr-2" />
                            Continue Draft
                          </Button>
                        </Link>
                      )}
                      {(brief.proposal_status === 'submitted' || brief.proposal_status === 'under_review' || brief.proposal_status === 'accepted' || brief.proposal_status === 'rejected') && (
                        <Link href={`/designer/proposals/${brief.proposal_id}`}>
                          <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                            <Eye className="h-4 w-4 mr-2" />
                            View Proposal
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
