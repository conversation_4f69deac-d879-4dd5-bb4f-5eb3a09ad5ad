"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";
import {
  LayoutDashboard,
  FolderKanban,
  Users,
  MessageSquare,
  Calendar,
  Image,
  Star,
  FileText,
  Settings,
  Bell,
  Briefcase,
  Award,
  Activity,
  ChevronDown,
  ChevronRight,
  Circle,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";

interface SidebarItem {
  name: string;
  href: string;
  icon: any;
  active: boolean;
  badge?: number;
  submenu?: SidebarItem[];
}

interface QuickStats {
  pendingBriefs: number;
  activeProjects: number;
  unreadMessages: number;
  pendingReviews: number;
  connectedClients: number;
}

export function EnhancedDesignerSidebar() {
  const { user } = useOptimizedAuth();
  const pathname = usePathname();
  const [quickStats, setQuickStats] = useState<QuickStats>({
    pendingBriefs: 0,
    activeProjects: 0,
    unreadMessages: 0,
    pendingReviews: 0,
    connectedClients: 0
  });
  const [expandedMenus, setExpandedMenus] = useState<string[]>(['projects']);

  useEffect(() => {
    if (user) {
      fetchQuickStats();
    }
  }, [user]);

  const fetchQuickStats = async () => {
    if (!user) return;

    try {
      // Fetch active projects count with better error handling
      const { count: activeProjectsCount, error: projectsError } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .eq('designer_id', user.id)
        .in('status', ['assigned', 'in_progress']);

      if (projectsError) {
        console.error('❌ Projects count error:', {
          error: projectsError,
          message: projectsError?.message || 'Unknown error',
          code: projectsError?.code || 'No code'
        });
      }

      // Fetch pending briefs count with better error handling
      const { count: pendingBriefsCount, error: briefsError } = await supabase
        .from('project_briefs')
        .select('*', { count: 'exact', head: true })
        .or(`assigned_designer_id.eq.${user.id},and(assigned_designer_id.is.null,status.eq.pending)`);

      if (briefsError) {
        console.error('❌ Briefs count error:', {
          error: briefsError,
          message: briefsError?.message || 'Unknown error',
          code: briefsError?.code || 'No code'
        });
      }

      // Fetch connected clients count with better error handling
      const { count: connectedClientsCount, error: connectionsError } = await supabase
        .from('connections')
        .select('*', { count: 'exact', head: true })
        .eq('designer_id', user.id)
        .eq('status', 'active');

      if (connectionsError) {
        console.error('❌ Connections count error:', {
          error: connectionsError,
          message: connectionsError?.message || 'Unknown error',
          code: connectionsError?.code || 'No code',
          designerId: user.id
        });
      } else {
        console.log('✅ Connected clients count:', connectedClientsCount);
      }

      // Fetch unread admin messages count (mock for now)
      // TODO: Replace with real admin messages table when implemented
      const unreadAdminMessages = 0; // No hardcoded count

      setQuickStats({
        pendingBriefs: pendingBriefsCount || 0,
        activeProjects: activeProjectsCount || 0,
        unreadMessages: 0, // Will come from conversations table
        pendingReviews: unreadAdminMessages, // Dynamic count
        connectedClients: connectedClientsCount || 0
      });

      console.log('📊 Quick stats updated:', {
        pendingBriefs: pendingBriefsCount || 0,
        activeProjects: activeProjectsCount || 0,
        connectedClients: connectedClientsCount || 0
      });

    } catch (error) {
      console.error('❌ Error fetching quick stats:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        userId: user.id
      });
    }
  };

  const toggleMenu = (menuName: string) => {
    setExpandedMenus(prev => 
      prev.includes(menuName) 
        ? prev.filter(name => name !== menuName)
        : [...prev, menuName]
    );
  };

  const sidebarItems: SidebarItem[] = [
    {
      name: "Dashboard",
      href: "/designer/dashboard",
      icon: LayoutDashboard,
      active: pathname === "/designer/dashboard"
    },
    {
      name: "Project Briefs",
      href: "/designer/briefs",
      icon: Briefcase,
      active: pathname.startsWith("/designer/briefs"),
      badge: quickStats.pendingBriefs > 0 ? quickStats.pendingBriefs : undefined
    },
    {
      name: "My Proposals",
      href: "/designer/proposals",
      icon: FileText,
      active: pathname.startsWith("/designer/proposals")
    },
    {
      name: "My Projects",
      href: "/designer/projects",
      icon: FolderKanban,
      active: pathname.startsWith("/designer/projects"),
      badge: quickStats.activeProjects > 0 ? quickStats.activeProjects : undefined,
      submenu: [
        {
          name: "All Projects",
          href: "/designer/projects",
          icon: FolderKanban,
          active: pathname === "/designer/projects"
        },
        {
          name: "Active Projects",
          href: "/designer/projects/active",
          icon: Activity,
          active: pathname === "/designer/projects/active",
          badge: quickStats.activeProjects > 0 ? quickStats.activeProjects : undefined
        },
        {
          name: "Completed Projects",
          href: "/designer/projects/completed",
          icon: CheckCircle,
          active: pathname === "/designer/projects/completed"
        }
      ]
    },
    {
      name: "Connections",
      href: "/designer/connections",
      icon: Users,
      active: pathname.startsWith("/designer/connections"),
      badge: quickStats.connectedClients > 0 ? quickStats.connectedClients : undefined
    },
    {
      name: "Messages",
      href: "/designer/messages",
      icon: MessageSquare,
      active: pathname.startsWith("/designer/messages"),
      badge: quickStats.unreadMessages > 0 ? quickStats.unreadMessages : undefined
    },
    {
      name: "Availability",
      href: "/designer/availability",
      icon: Calendar,
      active: pathname.startsWith("/designer/availability")
    },
    {
      name: "Portfolio",
      href: "/designer/portfolio",
      icon: Image,
      active: pathname.startsWith("/designer/portfolio")
    },
    {
      name: "Reviews & Ratings",
      href: "/designer/reviews",
      icon: Star,
      active: pathname.startsWith("/designer/reviews")
    },
    {
      name: "Admin Messages",
      href: "/designer/admin-messages",
      icon: Bell,
      active: pathname.startsWith("/designer/admin-messages"),
      badge: quickStats.pendingReviews > 0 ? quickStats.pendingReviews : undefined
    },
    {
      name: "Settings",
      href: "/designer/settings",
      icon: Settings,
      active: pathname.startsWith("/designer/settings")
    }
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full overflow-y-auto">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Designer Portal</h2>
        <p className="text-sm text-gray-500 mt-1">Manage your projects & clients</p>
      </div>

      {/* Quick Stats */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
          Quick Overview
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center">
              <FolderKanban className="h-4 w-4 text-blue-600" />
              <span className="ml-2 text-sm font-medium text-blue-900">
                {quickStats.activeProjects}
              </span>
            </div>
            <p className="text-xs text-blue-600 mt-1">Active</p>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="flex items-center">
              <Briefcase className="h-4 w-4 text-purple-600" />
              <span className="ml-2 text-sm font-medium text-purple-900">
                {quickStats.pendingBriefs}
              </span>
            </div>
            <p className="text-xs text-purple-600 mt-1">Briefs</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <ul className="space-y-1">
          {sidebarItems.map((item) => (
            <li key={item.name}>
              {item.submenu ? (
                <div>
                  <button
                    onClick={() => toggleMenu(item.name)}
                    className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      item.active
                        ? 'bg-brown-100 text-brown-900'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center">
                      <item.icon className="h-5 w-5 mr-3" />
                      {item.name}
                      {item.badge && (
                        <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                          {item.badge}
                        </span>
                      )}
                    </div>
                    {expandedMenus.includes(item.name) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </button>
                  {expandedMenus.includes(item.name) && (
                    <motion.ul
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="ml-6 mt-2 space-y-1"
                    >
                      {item.submenu.map((subItem) => (
                        <li key={subItem.name}>
                          <Link
                            href={subItem.href}
                            className={`flex items-center px-3 py-2 text-sm rounded-lg transition-colors ${
                              subItem.active
                                ? 'bg-brown-100 text-brown-900'
                                : 'text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            <Circle className="h-3 w-3 mr-3" />
                            {subItem.name}
                            {subItem.badge && (
                              <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                                {subItem.badge}
                              </span>
                            )}
                          </Link>
                        </li>
                      ))}
                    </motion.ul>
                  )}
                </div>
              ) : (
                <Link
                  href={item.href}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    item.active
                      ? 'bg-brown-100 text-brown-900'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <item.icon className="h-5 w-5 mr-3" />
                  {item.name}
                  {item.badge && (
                    <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                      {item.badge}
                    </span>
                  )}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
