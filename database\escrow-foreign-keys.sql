-- =====================================================
-- ESCROW SYSTEM FOREIGN KEY CONSTRAINTS
-- Run this AFTER escrow-system-schema.sql
-- =====================================================

-- This script adds foreign key constraints for the escrow system
-- It should be run after all tables and columns have been created

-- 1. ADD FOREIGN KEY FOR PROJECT MILESTONES
-- =====================================================

-- Add foreign key for escrow_hold_id in project_milestones
DO $$
BEGIN
    -- Check if both the column and target table exist
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'project_milestones' AND column_name = 'escrow_hold_id'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'escrow_holds'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_milestones_escrow_hold'
    ) THEN
        ALTER TABLE project_milestones ADD CONSTRAINT fk_milestones_escrow_hold
            FOREIGN KEY (escrow_hold_id) REFERENCES escrow_holds(id);
        RAISE NOTICE 'Added foreign key constraint: fk_milestones_escrow_hold';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_milestones_escrow_hold already exists or prerequisites not met';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add foreign key constraint fk_milestones_escrow_hold: %', SQLERRM;
END $$;

-- 2. ADD FOREIGN KEY FOR ESCROW HOLDS TO MILESTONES
-- =====================================================

-- Add foreign key for milestone_id in escrow_holds
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'escrow_holds' AND column_name = 'milestone_id'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'project_milestones'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_escrow_holds_milestone'
    ) THEN
        ALTER TABLE escrow_holds ADD CONSTRAINT fk_escrow_holds_milestone
            FOREIGN KEY (milestone_id) REFERENCES project_milestones(id);
        RAISE NOTICE 'Added foreign key constraint: fk_escrow_holds_milestone';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_escrow_holds_milestone already exists or prerequisites not met';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add foreign key constraint fk_escrow_holds_milestone: %', SQLERRM;
END $$;

-- 3. ADD FOREIGN KEY FOR ESCROW RELEASES TO MILESTONES
-- =====================================================

-- Add foreign key for milestone_id in escrow_releases
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'escrow_releases' AND column_name = 'milestone_id'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'project_milestones'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_escrow_releases_milestone'
    ) THEN
        ALTER TABLE escrow_releases ADD CONSTRAINT fk_escrow_releases_milestone
            FOREIGN KEY (milestone_id) REFERENCES project_milestones(id);
        RAISE NOTICE 'Added foreign key constraint: fk_escrow_releases_milestone';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_escrow_releases_milestone already exists or prerequisites not met';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add foreign key constraint fk_escrow_releases_milestone: %', SQLERRM;
END $$;

-- 4. ADD FOREIGN KEY FOR ESCROW RELEASES TO PAYOUTS
-- =====================================================

-- Add foreign key for payout_id in escrow_releases
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'escrow_releases' AND column_name = 'payout_id'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'payouts'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_escrow_releases_payout'
    ) THEN
        ALTER TABLE escrow_releases ADD CONSTRAINT fk_escrow_releases_payout
            FOREIGN KEY (payout_id) REFERENCES payouts(id);
        RAISE NOTICE 'Added foreign key constraint: fk_escrow_releases_payout';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_escrow_releases_payout already exists or prerequisites not met';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add foreign key constraint fk_escrow_releases_payout: %', SQLERRM;
END $$;

-- 5. ADD FOREIGN KEY FOR PAYOUTS TO ESCROW RELEASES
-- =====================================================

-- Add foreign key for escrow_release_id in payouts
DO $$
BEGIN
    -- Check if both the column and target table exist
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payouts' AND column_name = 'escrow_release_id'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'escrow_releases'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_payouts_escrow_release'
    ) THEN
        ALTER TABLE payouts ADD CONSTRAINT fk_payouts_escrow_release
            FOREIGN KEY (escrow_release_id) REFERENCES escrow_releases(id);
        RAISE NOTICE 'Added foreign key constraint: fk_payouts_escrow_release';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_payouts_escrow_release already exists or prerequisites not met';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add foreign key constraint fk_payouts_escrow_release: %', SQLERRM;
END $$;

-- 6. VERIFY FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Query to verify all foreign key constraints were created successfully
SELECT
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.constraint_name IN (
        'fk_milestones_escrow_hold',
        'fk_escrow_holds_milestone',
        'fk_escrow_releases_milestone',
        'fk_escrow_releases_payout',
        'fk_payouts_escrow_release'
    )
ORDER BY tc.table_name, tc.constraint_name;
