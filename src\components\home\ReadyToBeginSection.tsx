"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "../ui/button";
import Link from "next/link";
import { MessageCircle, Send } from "lucide-react";

const ReadyToBeginSection = () => {
  const [chatOpen, setChatOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [name, setName] = useState("");
  const [chatMessages, setChatMessages] = useState<Array<{sender: string; text: string}>>([]);

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) return;

    // Add user message to chat
    setChatMessages([...chatMessages, { sender: "user", text: message }]);

    // Clear input
    setMessage("");

    // Simulate response (in a real app, this would come from your backend)
    setTimeout(() => {
      setChatMessages(prev => [
        ...prev,
        {
          sender: "bot",
          text: "Thanks for reaching out! One of our team members will get back to you shortly. In the meantime, would you like to schedule a consultation call?"
        }
      ]);
    }, 1000);
  };

  return (
    <section className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Left Column - Call to Action */}
            <motion.div
              className="bg-white p-12 shadow-lg"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to begin?</h2>
              <p className="text-gray-600 mb-8">
                Start your project or schedule a consultation with us.
              </p>

              <div className="space-y-6">
                <Link href="/sample-request">
                  <Button variant="default" size="lg" className="w-full py-6 text-lg">
                    Start Your Project
                  </Button>
                </Link>

                <a
                  href="https://calendly.com/seniorsarchifirm/consultation"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button variant="outline" size="lg" className="w-full py-6 text-lg">
                    Schedule a Call
                  </Button>
                </a>
              </div>
            </motion.div>

            {/* Right Column - Chat Widget */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              {chatOpen ? (
                <div className="bg-white shadow-lg h-full flex flex-col">
                  {/* Chat Header */}
                  <div className="bg-primary text-white p-4">
                    <h3 className="font-medium">Chat with us</h3>
                  </div>

                  {/* Chat Messages */}
                  <div className="flex-grow p-4 overflow-y-auto space-y-4">
                    {chatMessages.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        <p>Send us a message and we'll get back to you shortly.</p>
                      </div>
                    ) : (
                      chatMessages.map((msg, index) => (
                        <div
                          key={index}
                          className={`${
                            msg.sender === "user"
                              ? "ml-auto bg-primary/10 text-gray-800"
                              : "mr-auto bg-gray-100 text-gray-800"
                          } max-w-[80%] rounded-lg p-3`}
                        >
                          {msg.text}
                        </div>
                      ))
                    )}
                  </div>

                  {/* Chat Input */}
                  <form onSubmit={handleChatSubmit} className="border-t p-4">
                    {chatMessages.length === 0 && (
                      <div className="mb-3">
                        <input
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Name"
                          className="w-full p-3 border border-gray-300 mb-3"
                        />
                      </div>
                    )}
                    <div className="flex">
                      <input
                        type="text"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        placeholder="Message"
                        className="flex-grow p-3 border border-gray-300"
                      />
                      <button
                        type="submit"
                        className="bg-primary text-white p-3"
                      >
                        <Send className="h-5 w-5" />
                      </button>
                    </div>
                  </form>
                </div>
              ) : (
                <div className="h-full flex items-end justify-end">
                  <button
                    onClick={() => setChatOpen(true)}
                    className="bg-primary text-white p-4 rounded-full shadow-lg hover:bg-primary/90 transition-colors"
                  >
                    <MessageCircle className="h-6 w-6" />
                  </button>
                </div>
              )}
            </motion.div>
          </div>

          {/* Additional Contact Methods */}
          <motion.div
            className="mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <p className="text-gray-600">
              Prefer to reach us directly? Email us at{" "}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>{" "}
              or call{" "}
              <a href="tel:+966552552260" className="text-primary hover:underline">
                +966 55 255 2260
              </a>
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ReadyToBeginSection;
