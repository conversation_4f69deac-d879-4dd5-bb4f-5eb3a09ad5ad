"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import LiveChatButton from "@/components/LiveChatButton";
import CookieConsent from "@/components/CookieConsent";


export default function ClientBody({ children }: { children: React.ReactNode }) {
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();

  // Check if current page is a role-specific page
  const isRoleSpecificPage = () => {
    const roleRoutes = [
      '/client/',
      '/designer/',
      '/admin/',
      '/quality/',
      '/manager/'
    ];

    return roleRoutes.some(route => pathname.startsWith(route));
  };

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <AnimatePresence mode="wait">
      {loading ? (
        <motion.div
          key="loader"
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          className="fixed inset-0 bg-black flex items-center justify-center z-50"
        >
          <div className="text-center">
            {/* Animated Logo */}
            <motion.div
              className="mb-8"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{
                duration: 1.2,
                ease: "easeOut",
                type: "spring",
                stiffness: 100
              }}
            >
              <motion.div
                className="relative"
                animate={{
                  rotateY: [0, 360],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "linear",
                  delay: 1
                }}
              >
                <Image
                  src="/seniors-icon.svg"
                  alt="Senior's Archi-Firm"
                  width={128}
                  height={128}
                  className="h-24 w-24 md:h-32 md:w-32 mx-auto filter brightness-0 invert"
                  priority
                />
              </motion.div>
            </motion.div>

            {/* Animated Text */}
            <motion.h1
              className="text-2xl md:text-4xl font-bold text-white mb-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              SENIOR'S ARCHI-FIRM
            </motion.h1>

            {/* Animated Progress Bar */}
            <motion.div
              className="relative h-1 w-64 bg-gray-800 overflow-hidden mx-auto rounded-full"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              <motion.div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-primary to-primary/60 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ delay: 1, duration: 1.2, ease: "easeInOut" }}
              />
            </motion.div>

            {/* Loading Text */}
            <motion.p
              className="text-primary text-sm mt-4 font-medium"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.5, duration: 0.5 }}
            >
              Crafting your architectural experience...
            </motion.p>
          </div>
        </motion.div>
      ) : (
        <motion.div
          key="content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {children}
          {/* Only show LiveChatButton on non-role-specific pages */}
          {!isRoleSpecificPage() && <LiveChatButton />}
          <CookieConsent />
        </motion.div>
      )}
    </AnimatePresence>
  );
}
