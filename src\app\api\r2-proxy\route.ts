import { NextRequest, NextResponse } from 'next/server';
import { S3Client, GetObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const bucket = searchParams.get('bucket');
  const key = searchParams.get('key');

  if (!bucket || !key) {
    console.error('Missing required parameters:', { bucket, key });
    return NextResponse.json(
      { error: 'Bucket and key parameters are required' },
      { status: 400 }
    );
  }

  // Decode the key to handle special characters
  const decodedKey = decodeURIComponent(key);

  try {
    console.log(`Fetching file from R2: bucket=${bucket}, key=${decodedKey}`);

    // Verify R2 configuration
    if (!process.env.CLOUDFLARE_R2_ENDPOINT || !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY) {
      console.error('Missing R2 configuration in environment variables:', {
        endpoint: !!process.env.CLOUDFLARE_R2_ENDPOINT,
        accessKeyId: !!process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: !!process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY
      });
      return NextResponse.json(
        {
          error: 'Storage configuration error - missing R2 environment variables',
          details: 'Please check CLOUDFLARE_R2_ENDPOINT, CLOUDFLARE_R2_ACCESS_KEY_ID, and CLOUDFLARE_R2_SECRET_ACCESS_KEY'
        },
        { status: 500 }
      );
    }

    let response;

    try {
      const command = new GetObjectCommand({
        Bucket: bucket,
        Key: decodedKey,
      });

      response = await s3Client.send(command);
    } catch (error: any) {
      // If file not found, try to find the correct path using fallback logic
      if (error.name === 'NoSuchKey' && bucket === 'designer-applications') {
        console.log(`File not found with key: ${decodedKey}, attempting fallback search...`);

        const fallbackKey = await findCorrectFileKey(bucket, decodedKey);
        if (fallbackKey) {
          console.log(`Found fallback file: ${fallbackKey}`);
          const fallbackCommand = new GetObjectCommand({
            Bucket: bucket,
            Key: fallbackKey,
          });
          response = await s3Client.send(fallbackCommand);
        } else {
          throw error; // Re-throw if no fallback found
        }
      } else {
        throw error; // Re-throw for other errors
      }
    }

    if (!response.Body) {
      console.error(`File not found in R2: bucket=${bucket}, key=${decodedKey}`);
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Convert the readable stream to a buffer
    const chunks = [];
    for await (const chunk of response.Body as any) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);

    // Determine content type based on file extension if not provided
    let contentType = response.ContentType || 'application/octet-stream';

    if (!response.ContentType) {
      const fileExtension = decodedKey.split('.').pop()?.toLowerCase();
      switch (fileExtension) {
        case 'pdf':
          contentType = 'application/pdf';
          break;
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'gif':
          contentType = 'image/gif';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        default:
          contentType = 'application/octet-stream';
      }
    }

    console.log(`Successfully fetched file: ${decodedKey}, size: ${buffer.length} bytes, type: ${contentType}`);

    // Return the file with the appropriate content type and headers
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Length': buffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // 1 hour cache
        'Content-Disposition': contentType === 'application/pdf' ? 'inline' : 'attachment',
      },
    });
  } catch (error) {
    console.error('Error fetching file from R2:', error);

    // Provide more specific error information
    let errorMessage = 'Failed to fetch file from storage';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('NoSuchKey') || error.message.includes('NotFound')) {
        errorMessage = 'File not found in storage';
        statusCode = 404;
      } else if (error.message.includes('AccessDenied')) {
        errorMessage = 'Access denied to storage';
        statusCode = 403;
      } else if (error.message.includes('InvalidBucketName')) {
        errorMessage = 'Invalid storage bucket';
        statusCode = 400;
      }

      console.error('Detailed error:', {
        message: error.message,
        stack: error.stack,
        bucket,
        key: decodedKey
      });
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

/**
 * Fallback function to find the correct file key when the database path doesn't match R2
 */
async function findCorrectFileKey(bucket: string, dbKey: string): Promise<string | null> {
  try {
    // Extract the folder (application ID) from the database key
    const pathParts = dbKey.split('/');
    if (pathParts.length < 2) {
      return null;
    }

    const folder = pathParts[0]; // e.g., "temp-1748470033810-bqauz08uedr"
    const dbFileName = pathParts[1]; // e.g., "resume-Starting Project Request.pdf"

    // List all files in the folder
    const command = new ListObjectsV2Command({
      Bucket: bucket,
      Prefix: `${folder}/`,
      MaxKeys: 100
    });

    const response = await s3Client.send(command);

    if (!response.Contents || response.Contents.length === 0) {
      return null;
    }

    // Find the matching file based on name pattern
    for (const object of response.Contents) {
      if (!object.Key) continue;

      const fileName = object.Key.split('/').pop() || '';

      // Check if this is the file we're looking for
      if (dbFileName.includes('resume-') && fileName.includes('resume-')) {
        // For resume files, match the base name (ignoring timestamp and space/hyphen differences)
        const dbBaseName = dbFileName.replace('resume-', '').replace(/\s+/g, '-');
        const r2BaseName = fileName.replace(/^\d+-resume-/, ''); // Remove timestamp prefix

        if (r2BaseName === dbBaseName ||
            r2BaseName.replace(/-/g, ' ') === dbBaseName.replace(/-/g, ' ')) {
          return object.Key;
        }
      } else if (dbFileName.includes('portfolio-') && fileName.includes('portfolio-')) {
        // For portfolio files, match the base name (ignoring timestamp and index)
        const dbBaseName = dbFileName.replace(/^portfolio-\d+-/, '').replace(/\s+/g, '-');
        const r2BaseName = fileName.replace(/^\d+-portfolio-\d+-/, ''); // Remove timestamp and index prefix

        if (r2BaseName === dbBaseName ||
            r2BaseName.replace(/-/g, ' ') === dbBaseName.replace(/-/g, ' ')) {
          return object.Key;
        }
      }
    }

    return null;

  } catch (error) {
    console.error(`Error in fallback file search for ${dbKey}:`, error);
    return null;
  }
}
