"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  MessageSquare,
  Bell,
  CheckCircle,
  AlertTriangle,
  Info,
  ArrowRight,
  Clock
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface AdminMessage {
  id: string;
  title: string;
  content: string;
  message_type: 'info' | 'warning' | 'success' | 'announcement';
  priority: 'low' | 'normal' | 'high';
  is_read: boolean;
  created_at: string;
  sender_name: string;
  action_required: boolean;
}

export function AdminMessages() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<AdminMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (user) {
      fetchAdminMessages();
    }
  }, [user]);

  const fetchAdminMessages = async () => {
    try {
      // Fetch real admin messages from API
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        console.error('No session token available');
        return;
      }

      const response = await fetch('/api/user/admin-messages?limit=5', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch admin messages');
      }

      const data = await response.json();

      // Transform data to match component interface
      const transformedMessages: AdminMessage[] = data.messages.map((msg: any) => ({
        id: msg.id,
        title: msg.title,
        content: msg.content,
        message_type: msg.message_type,
        priority: msg.priority,
        is_read: !!msg.read_at,
        created_at: msg.created_at,
        sender_name: msg.created_by_name,
        action_required: msg.action_required
      }));

      setMessages(transformedMessages);
      setUnreadCount(data.unread_count || 0);

    } catch (error) {
      console.error('Error fetching admin messages:', error);
      // Fallback to empty state on error
      setMessages([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  const getMessageIcon = (type: AdminMessage['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'announcement':
        return <Bell className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getMessageColor = (type: AdminMessage['type']) => {
    switch (type) {
      case 'success':
        return 'border-l-green-400 bg-green-50';
      case 'warning':
        return 'border-l-yellow-400 bg-yellow-50';
      case 'announcement':
        return 'border-l-blue-400 bg-blue-50';
      default:
        return 'border-l-gray-400 bg-gray-50';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const markAsRead = async (messageId: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        console.error('No session token available');
        return;
      }

      const response = await fetch(`/api/admin/messages/${messageId}/mark-read`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to mark message as read');
      }

      // Update local state
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId ? { ...msg, is_read: true } : msg
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: 0.7 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-gray-900">Admin Messages</h3>
            {unreadCount > 0 && (
              <span className="ml-2 bg-red-500 text-white text-xs font-medium px-2 py-1 rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
          <Link href="/designer/messages">
            <Button variant="ghost" size="sm">
              View All <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </div>
      </div>
      
      <div className="p-6">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Messages</h4>
            <p className="text-gray-600">You're all caught up! No admin messages at the moment.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
                className={`border-l-4 p-4 rounded-r-lg cursor-pointer transition-all hover:shadow-sm ${getMessageColor(message.message_type)} ${
                  !message.is_read ? 'ring-2 ring-blue-100' : ''
                }`}
                onClick={() => !message.is_read && markAsRead(message.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center">
                    {getMessageIcon(message.message_type)}
                    <h4 className={`ml-2 text-sm font-medium ${!message.is_read ? 'text-gray-900' : 'text-gray-700'}`}>
                      {message.title}
                    </h4>
                  </div>
                  <div className="flex items-center space-x-2">
                    {message.action_required && (
                      <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
                        Action Required
                      </span>
                    )}
                    {!message.is_read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 mb-3">{message.content}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>From: {message.sender_name}</span>
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{formatTimeAgo(message.created_at)}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}
