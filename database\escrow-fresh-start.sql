-- =====================================================
-- ESCROW SYSTEM - FRESH START
-- Drops existing escrow tables and creates everything fresh
-- =====================================================

-- 1. CLEAN UP EXISTING ESCROW TABLES (IF ANY)
-- =====================================================

DO $$
BEGIN
    -- Drop tables in reverse dependency order
    DROP TABLE IF EXISTS escrow_activities CASCADE;
    DROP TABLE IF EXISTS escrow_disputes CASCADE;
    DROP TABLE IF EXISTS escrow_releases CASCADE;
    DROP TABLE IF EXISTS escrow_holds CASCADE;
    DROP TABLE IF EXISTS escrow_accounts CASCADE;
    
    RAISE NOTICE 'Dropped existing escrow tables (if any)';
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Error dropping tables: %', SQLERRM;
END $$;

-- 2. EXTEND EXISTING TABLES (SAFELY)
-- =====================================================

-- Add escrow_status to transactions table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN
        -- Drop column if it exists, then add it fresh
        BEGIN
            ALTER TABLE transactions DROP COLUMN IF EXISTS escrow_status;
            ALTER TABLE transactions ADD COLUMN escrow_status VARCHAR(50) DEFAULT 'none' 
                CHECK (escrow_status IN ('none', 'held', 'pending_release', 'released', 'disputed'));
            RAISE NOTICE '✅ Added escrow_status column to transactions table';
        EXCEPTION
            WHEN others THEN
                RAISE NOTICE '❌ Error adding escrow_status to transactions: %', SQLERRM;
        END;
    ELSE
        RAISE NOTICE '⚠️ transactions table does not exist - skipping';
    END IF;
END $$;

-- Add escrow_hold_id to project_milestones table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'project_milestones') THEN
        -- Drop column if it exists, then add it fresh
        BEGIN
            ALTER TABLE project_milestones DROP COLUMN IF EXISTS escrow_hold_id;
            ALTER TABLE project_milestones ADD COLUMN escrow_hold_id UUID;
            RAISE NOTICE '✅ Added escrow_hold_id column to project_milestones table';
        EXCEPTION
            WHEN others THEN
                RAISE NOTICE '❌ Error adding escrow_hold_id to project_milestones: %', SQLERRM;
        END;
    ELSE
        RAISE NOTICE '⚠️ project_milestones table does not exist - skipping';
    END IF;
END $$;

-- 3. CREATE OR RECREATE PAYOUTS TABLE
-- =====================================================

DROP TABLE IF EXISTS payouts CASCADE;

CREATE TABLE payouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID,
    designer_id UUID,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    payout_method VARCHAR(50) DEFAULT 'stripe_connect',
    external_payout_id TEXT,
    failure_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    escrow_release_id UUID
);

-- 4. CREATE ESCROW TABLES (FRESH)
-- =====================================================

-- Escrow Accounts
CREATE TABLE escrow_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    client_id UUID NOT NULL,
    designer_id UUID NOT NULL,
    manager_id UUID,
    account_number TEXT UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'closed', 'suspended')),
    total_held DECIMAL(10,2) DEFAULT 0.00,
    total_released DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    UNIQUE(project_id, client_id, designer_id)
);

-- Escrow Holds
CREATE TABLE escrow_holds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_account_id UUID NOT NULL,
    transaction_id UUID NOT NULL,
    milestone_id UUID,
    project_id UUID NOT NULL,
    gross_amount DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    processing_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    hold_reason VARCHAR(100) NOT NULL DEFAULT 'milestone_completion',
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'pending_release', 'released', 'disputed', 'cancelled')),
    requires_manager_approval BOOLEAN DEFAULT TRUE,
    requires_quality_approval BOOLEAN DEFAULT FALSE,
    manager_approved_at TIMESTAMP WITH TIME ZONE,
    manager_approved_by UUID,
    quality_approved_at TIMESTAMP WITH TIME ZONE,
    quality_approved_by UUID,
    held_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    release_requested_at TIMESTAMP WITH TIME ZONE,
    released_at TIMESTAMP WITH TIME ZONE,
    auto_release_date TIMESTAMP WITH TIME ZONE,
    hold_notes TEXT,
    release_notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Releases
CREATE TABLE escrow_releases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_hold_id UUID NOT NULL,
    escrow_account_id UUID NOT NULL,
    project_id UUID NOT NULL,
    milestone_id UUID,
    release_amount DECIMAL(10,2) NOT NULL,
    release_type VARCHAR(50) NOT NULL DEFAULT 'milestone_completion' 
        CHECK (release_type IN ('milestone_completion', 'project_completion', 'partial_release', 'dispute_resolution', 'cancellation')),
    requested_by UUID NOT NULL,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    manager_id UUID,
    manager_approval_status VARCHAR(50) DEFAULT 'pending' 
        CHECK (manager_approval_status IN ('pending', 'approved', 'rejected', 'not_required')),
    manager_approved_at TIMESTAMP WITH TIME ZONE,
    manager_notes TEXT,
    quality_approval_status VARCHAR(50) DEFAULT 'not_required' 
        CHECK (quality_approval_status IN ('pending', 'approved', 'rejected', 'not_required')),
    quality_approved_at TIMESTAMP WITH TIME ZONE,
    quality_approved_by UUID,
    quality_notes TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'processed', 'failed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID,
    payout_id UUID,
    payout_transaction_id TEXT,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Disputes
CREATE TABLE escrow_disputes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_hold_id UUID NOT NULL,
    project_id UUID NOT NULL,
    dispute_type VARCHAR(50) NOT NULL CHECK (dispute_type IN ('quality_issue', 'scope_change', 'timeline_delay', 'payment_dispute', 'other')),
    initiated_by UUID NOT NULL,
    initiated_against UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    evidence_files JSONB DEFAULT '[]',
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'mediation', 'resolved', 'closed')),
    assigned_mediator UUID,
    resolution_type VARCHAR(50) CHECK (resolution_type IN ('full_release', 'partial_release', 'full_refund', 'partial_refund', 'rework_required')),
    resolution_amount DECIMAL(10,2),
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Activities
CREATE TABLE escrow_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_account_id UUID,
    escrow_hold_id UUID,
    escrow_release_id UUID,
    project_id UUID NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    performed_by UUID NOT NULL,
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    previous_state JSONB,
    new_state JSONB,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}'
);

-- 5. CREATE INDEXES
-- =====================================================

-- Escrow Accounts indexes
CREATE INDEX idx_escrow_accounts_project_id ON escrow_accounts(project_id);
CREATE INDEX idx_escrow_accounts_client_id ON escrow_accounts(client_id);
CREATE INDEX idx_escrow_accounts_designer_id ON escrow_accounts(designer_id);
CREATE INDEX idx_escrow_accounts_manager_id ON escrow_accounts(manager_id);
CREATE INDEX idx_escrow_accounts_status ON escrow_accounts(status);

-- Escrow Holds indexes
CREATE INDEX idx_escrow_holds_account_id ON escrow_holds(escrow_account_id);
CREATE INDEX idx_escrow_holds_transaction_id ON escrow_holds(transaction_id);
CREATE INDEX idx_escrow_holds_milestone_id ON escrow_holds(milestone_id);
CREATE INDEX idx_escrow_holds_project_id ON escrow_holds(project_id);
CREATE INDEX idx_escrow_holds_status ON escrow_holds(status);

-- Escrow Releases indexes
CREATE INDEX idx_escrow_releases_hold_id ON escrow_releases(escrow_hold_id);
CREATE INDEX idx_escrow_releases_project_id ON escrow_releases(project_id);
CREATE INDEX idx_escrow_releases_manager_id ON escrow_releases(manager_id);
CREATE INDEX idx_escrow_releases_status ON escrow_releases(status);

-- Escrow Disputes indexes
CREATE INDEX idx_escrow_disputes_hold_id ON escrow_disputes(escrow_hold_id);
CREATE INDEX idx_escrow_disputes_project_id ON escrow_disputes(project_id);
CREATE INDEX idx_escrow_disputes_status ON escrow_disputes(status);

-- Escrow Activities indexes
CREATE INDEX idx_escrow_activities_project_id ON escrow_activities(project_id);
CREATE INDEX idx_escrow_activities_performed_by ON escrow_activities(performed_by);
CREATE INDEX idx_escrow_activities_performed_at ON escrow_activities(performed_at);

-- 6. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE escrow_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_holds ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_activities ENABLE ROW LEVEL SECURITY;

-- 7. SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ESCROW SYSTEM CREATED SUCCESSFULLY!';
    RAISE NOTICE '';
    RAISE NOTICE 'Tables created:';
    RAISE NOTICE '✅ escrow_accounts';
    RAISE NOTICE '✅ escrow_holds';
    RAISE NOTICE '✅ escrow_releases';
    RAISE NOTICE '✅ escrow_disputes';
    RAISE NOTICE '✅ escrow_activities';
    RAISE NOTICE '✅ payouts (recreated)';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Run escrow-foreign-keys.sql';
    RAISE NOTICE '2. Run escrow-rls-policies.sql';
    RAISE NOTICE '3. Run quality-workflow-functions.sql';
    RAISE NOTICE '';
END $$;
