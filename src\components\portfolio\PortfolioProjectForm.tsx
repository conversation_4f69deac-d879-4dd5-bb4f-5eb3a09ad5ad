'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import {
  createPortfolioProject,
  updatePortfolioProject
} from '@/lib/api/portfolio';
import { PortfolioProject } from '@/types/portfolio';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

const formSchema = z.object({
  title: z.string().min(3, {
    message: 'Title must be at least 3 characters.',
  }).max(100, {
    message: 'Title must not exceed 100 characters.',
  }),
  description: z.string().max(1000, {
    message: 'Description must not exceed 1000 characters.',
  }).optional().nullable(),
  category: z.string().optional().nullable(),
  client_name: z.string().max(100, {
    message: 'Client name must not exceed 100 characters.',
  }).optional().nullable(),
  completion_date: z.date().optional().nullable(),
  featured: z.boolean().default(false),
});

const CATEGORIES = [
  'Residential',
  'Commercial',
  'Institutional',
  'Industrial',
  'Hospitality',
  'Healthcare',
  'Educational',
  'Cultural',
  'Religious',
  'Mixed-Use',
  'Interior Design',
  'Landscape',
  'Urban Planning',
  'Renovation',
  'Concept',
  'Other',
];

interface PortfolioProjectFormProps {
  project?: PortfolioProject;
  onSuccess?: (project: PortfolioProject) => void;
  onCancel?: () => void;
}

export function PortfolioProjectForm({
  project,
  onSuccess,
  onCancel,
}: PortfolioProjectFormProps) {
  const { token } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!project;

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: project?.title || '',
      description: project?.description || '',
      category: project?.category || '',
      client_name: project?.client_name || '',
      completion_date: project?.completion_date ? new Date(project.completion_date) : undefined,
      featured: project?.featured || false,
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!token) {
      toast({
        title: 'Authentication Error',
        description: 'You must be logged in to create or edit portfolio projects.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      let result;

      if (isEditing && project) {
        result = await updatePortfolioProject(token, project.id, values);
        toast({
          title: 'Project Updated',
          description: 'Your portfolio project has been updated successfully.',
        });
      } else {
        result = await createPortfolioProject(token, values);
        toast({
          title: 'Project Created',
          description: 'Your portfolio project has been created successfully.',
        });
      }

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('Error saving portfolio project:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save portfolio project',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Project Title *</FormLabel>
              <FormControl>
                <Input placeholder="Enter project title" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value || undefined}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                Select the category that best describes this project
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe your project"
                  className="min-h-[120px]"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                Provide details about the project, your role, and any notable features
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="client_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Client Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter client name" {...field} value={field.value || ''} />
              </FormControl>
              <FormDescription>
                You can leave this blank if you prefer not to disclose the client
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="completion_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Completion Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className="w-full pl-3 text-left font-normal"
                    >
                      {field.value ? (
                        format(field.value, 'PPP')
                      ) : (
                        <span className="text-muted-foreground">Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value || undefined}
                    onSelect={field.onChange}
                    disabled={(date) => date > new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormDescription>
                When was this project completed?
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="featured"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Featured Project</FormLabel>
                <FormDescription>
                  Featured projects will be highlighted in your portfolio
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? isEditing ? 'Updating...' : 'Creating...'
              : isEditing ? 'Update Project' : 'Create Project'
            }
          </Button>
        </div>
      </form>
    </Form>
  );
}
