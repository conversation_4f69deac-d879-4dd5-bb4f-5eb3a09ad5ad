import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { EscrowManager } from '@/lib/escrow-manager';

/**
 * API endpoint for escrow releases management
 * Handles release requests, approvals, and processing
 */

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile and role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');
    const pendingApproval = searchParams.get('pendingApproval');

    // Build query with proper joins to get project info
    let query = supabase
      .from('paypal_escrow_releases')
      .select(`
        *,
        escrow_hold:paypal_escrow_holds(
          id,
          project_id,
          client_id,
          designer_id,
          gross_amount,
          status
        )
      `);

    // Apply filters based on role
    switch (profile.role) {
      case 'admin':
        // Admins can see all releases
        break;
      case 'manager':
        // Managers can see all releases (watchdog role)
        break;
      case 'quality_team':
        // Quality team can see releases pending their approval
        query = query.eq('quality_approval_status', 'pending');
        break;
      case 'client':
        // Clients can see releases for their projects (filter via escrow_hold)
        query = query.eq('escrow_hold.client_id', user.id);
        break;
      case 'designer':
        // Designers can see releases for their projects (filter via escrow_hold)
        query = query.eq('escrow_hold.designer_id', user.id);
        break;
      default:
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Apply additional filters
    if (projectId) {
      query = query.eq('escrow_hold.project_id', projectId);
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (pendingApproval === 'true') {
      if (profile.role === 'manager') {
        query = query.eq('manager_approval_status', 'pending');
      } else if (profile.role === 'quality_team') {
        query = query.eq('quality_approval_status', 'pending');
      }
    }

    const { data: releases, error } = await query
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching escrow releases:', error);
      return NextResponse.json({ error: 'Failed to fetch escrow releases' }, { status: 500 });
    }

    // Fetch related data for each release
    const enrichedReleases = await Promise.all(
      (releases || []).map(async (release) => {
        try {
          // Get project info from escrow_hold if not already included
          let project = null;
          const projectId = release.escrow_hold?.project_id;

          if (projectId) {
            const { data: projectData } = await supabase
              .from('projects')
              .select('id, title, status')
              .eq('id', projectId)
              .single();
            project = projectData;
          }

          // Get milestone info if exists
          let milestone = null;
          if (release.escrow_hold?.milestone_id) {
            const { data: milestoneData } = await supabase
              .from('project_milestones')
              .select('id, title')
              .eq('id', release.escrow_hold.milestone_id)
              .single();
            milestone = milestoneData;
          }

          // Get requester info
          const { data: requester } = await supabase
            .from('profiles')
            .select('id, full_name, email')
            .eq('id', release.requested_by)
            .single();

          return {
            ...release,
            project: project || { id: projectId, title: 'Unknown Project', status: 'unknown' },
            milestone,
            requester: requester || { id: release.requested_by, full_name: 'Unknown User', email: '' }
          };
        } catch (err) {
          console.error('Error enriching release data:', err);
          return release;
        }
      })
    );

    return NextResponse.json({
      success: true,
      releases: enrichedReleases
    });

  } catch (error) {
    console.error('Error in escrow releases GET API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, escrowHoldId, releaseId, releaseType, releaseAmount, notes, approverRole } = body;

    switch (action) {
      case 'request_release':
        // Request escrow release
        if (!escrowHoldId) {
          return NextResponse.json(
            { error: 'Escrow hold ID is required' },
            { status: 400 }
          );
        }

        const releaseResult = await EscrowManager.requestEscrowRelease({
          escrowHoldId,
          requestedBy: user.id,
          releaseType,
          releaseAmount,
          notes
        });

        if (!releaseResult.success) {
          return NextResponse.json(
            { error: releaseResult.error || 'Failed to request release' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          release: releaseResult.release,
          message: 'Release request created successfully'
        }, { status: 201 });

      case 'approve_release':
        // Approve escrow release
        if (!releaseId || !approverRole) {
          return NextResponse.json(
            { error: 'Release ID and approver role are required' },
            { status: 400 }
          );
        }

        // Verify user has permission to approve
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (!profile) {
          return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
        }

        const validApproverRoles = ['manager', 'quality_team', 'admin'];
        if (!validApproverRoles.includes(profile.role)) {
          return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
        }

        // Map profile role to approver role
        let mappedApproverRole = approverRole;
        if (profile.role === 'quality_team') {
          mappedApproverRole = 'quality';
        }

        const approvalResult = await EscrowManager.approveEscrowRelease({
          releaseId,
          approverId: user.id,
          approverRole: mappedApproverRole,
          notes
        });

        if (!approvalResult.success) {
          return NextResponse.json(
            { error: approvalResult.error || 'Failed to approve release' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          release: approvalResult.release,
          message: 'Release approved successfully'
        });

      case 'reject_release':
        // Reject escrow release
        if (!releaseId) {
          return NextResponse.json(
            { error: 'Release ID is required' },
            { status: 400 }
          );
        }

        // Update release status to rejected
        const { error: rejectError } = await supabase
          .from('paypal_escrow_releases')
          .update({
            status: 'rejected',
            manager_approval_status: 'rejected',
            manager_notes: notes,
            processed_at: new Date().toISOString()
          })
          .eq('id', releaseId);

        if (rejectError) {
          return NextResponse.json(
            { error: 'Failed to reject release' },
            { status: 500 }
          );
        }

        // Reset hold status back to held
        const { data: release } = await supabase
          .from('paypal_escrow_releases')
          .select('escrow_hold_id')
          .eq('id', releaseId)
          .single();

        if (release) {
          await supabase
            .from('paypal_escrow_holds')
            .update({ status: 'held' })
            .eq('id', release.escrow_hold_id);
        }

        return NextResponse.json({
          success: true,
          message: 'Release rejected successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in escrow releases POST API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
