# Tracking System Setup Guide

This guide explains how to set up the tracking system for the Sample Request and Vision Builder features.

## Database Setup

### 1. Create the Tracking Requests Table

Run the SQL script in `supabase/migrations/20240517_create_tracking_requests.sql` to create the `tracking_requests` table in your Supabase database.

You can run this script in the Supabase SQL Editor or use the Supabase CLI:

```bash
supabase db push
```

### 2. Verify the Table

After running the script, verify that the `tracking_requests` table has been created with all the required columns:

- id
- tracking_number
- request_type
- status
- name
- email
- project_type
- description
- vision_prompt
- service_category
- sample_type
- selected_style
- file_path
- file_name
- file_type
- file_size
- image_url
- created_at
- updated_at
- completed_at
- notes
- linked_user_id
- linked_project_id

## Cloudflare R2 Setup

### 1. Create R2 Buckets

Create two buckets in your Cloudflare R2 account:

1. `vision-builder-images` - For storing Vision Builder images
2. `sample-request-files` - For storing Sample Request files

### 2. Set Up Public Access (Optional but Recommended)

To make the uploaded files publicly accessible:

1. Log in to your Cloudflare dashboard
2. Go to R2 > Buckets
3. For each bucket:
   - Click on the bucket name
   - Go to "Settings" tab
   - Under "Public Access", toggle "Public Access" to "On"
   - Save changes

### 3. Set Up a Custom Domain (Optional)

You can use a custom domain for your R2 buckets:

1. In Cloudflare dashboard, go to R2 > Buckets
2. Click "Connect domain"
3. Select a domain you manage through Cloudflare
4. Choose a subdomain (e.g., `storage.yourdomain.com`)
5. Select the buckets you want to expose through this domain
6. Click "Connect"

### 4. Update Environment Variables

Add the following to your `.env.local` file:

```
# If using a custom domain
CLOUDFLARE_R2_PUBLIC_URL=https://storage.yourdomain.com

# Or if using the default R2 domain
CLOUDFLARE_R2_PUBLIC_URL=https://pub-[your-account-id].r2.dev
```

Where `[your-account-id]` is your Cloudflare account ID.

## Testing the Tracking System

### 1. Test the Sample Request Form

1. Go to `/sample-request`
2. Fill out the form and upload a file
3. Submit the form
4. Verify that a tracking number is generated
5. Check the Supabase database to ensure the record was created
6. Check the R2 bucket to ensure the file was uploaded

### 2. Test the Vision Builder Form

1. Go to `/vision-builder/[service]` (e.g., `/vision-builder/interior-design`)
2. Go through the steps to generate or upload an image
3. Fill out the form and submit
4. Verify that a tracking number is generated
5. Check the Supabase database to ensure the record was created
6. Check the R2 bucket to ensure the image was uploaded

### 3. Test the Tracking Page

1. Go to `/track`
2. Enter the tracking number from one of your test submissions
3. Verify that the tracking information is displayed correctly

## Troubleshooting

### Database Issues

If you encounter database issues:

1. Check the browser console for error messages
2. Verify that your Supabase credentials are correct in `.env.local`
3. Ensure the `tracking_requests` table exists in your Supabase database
4. Check the RLS policies to ensure they allow the necessary operations

### R2 Upload Issues

If you encounter R2 upload issues:

1. Check the browser console for error messages
2. Verify that your Cloudflare R2 credentials are correct in `.env.local`
3. Ensure the R2 buckets exist in your Cloudflare account
4. Check that the bucket names match the ones in the code
5. Verify that the R2 credentials have the necessary permissions

### Form Submission Issues

If the form submissions are not working:

1. Check the browser console for error messages
2. Verify that the API routes are working correctly
3. Check the network tab in the browser developer tools to see if the requests are being sent
4. Verify that the form data is being correctly formatted
