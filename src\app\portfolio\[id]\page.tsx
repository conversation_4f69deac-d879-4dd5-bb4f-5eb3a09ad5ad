'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { getPortfolioProject } from '@/lib/api/portfolio';
import { PortfolioProject } from '@/types/portfolio';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { PortfolioGallery } from '@/components/portfolio/PortfolioGallery';
import {
  ArrowLeftIcon,
  CalendarIcon,
  UserIcon,
  StarIcon,
  ExternalLinkIcon
} from 'lucide-react';
import { format } from 'date-fns';

export default function PublicPortfolioProjectPage() {
  const { id } = useParams();
  const router = useRouter();
  const [project, setProject] = useState<PortfolioProject | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProject();
  }, [id]);

  const fetchProject = async () => {
    if (!id) return;

    setLoading(true);
    try {
      // For public viewing, we don't need authentication token
      // This would need to be modified to use a public API endpoint
      const projectData = await getPortfolioProject('', id as string);
      setProject(projectData);
    } catch (error) {
      console.error('Error fetching portfolio project:', error);
      router.push('/');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <Skeleton className="h-10 w-40" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-60 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-40 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
        <p className="mb-6">The project you're looking for doesn't exist or is not publicly available.</p>
        <Button onClick={() => router.push('/')}>
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Home
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="w-fit"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back
        </Button>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/contact')}
          >
            <ExternalLinkIcon className="h-4 w-4 mr-2" />
            Contact Designer
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">{project.title}</h1>
            <div className="flex flex-wrap gap-2">
              {project.category && (
                <Badge variant="outline">{project.category}</Badge>
              )}
              {project.featured && (
                <Badge variant="default" className="bg-amber-500 hover:bg-amber-600">
                  <StarIcon className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
            </div>
          </div>

          {project.description && (
            <div className="prose max-w-none">
              <p>{project.description}</p>
            </div>
          )}

          <div>
            <h2 className="text-xl font-semibold mb-4">Project Gallery</h2>
            <PortfolioGallery
              projectId={project.id}
              isOwner={false}
              onImagesChange={() => {}}
              key={`public-gallery`}
            />
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-muted/40 rounded-lg p-6 space-y-4">
            <h2 className="text-xl font-semibold">Project Details</h2>

            {project.client_name && (
              <div className="flex items-start gap-2">
                <UserIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Client</p>
                  <p className="text-muted-foreground">{project.client_name}</p>
                </div>
              </div>
            )}

            {project.completion_date && (
              <div className="flex items-start gap-2">
                <CalendarIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Completion Date</p>
                  <p className="text-muted-foreground">
                    {format(new Date(project.completion_date), 'MMMM yyyy')}
                  </p>
                </div>
              </div>
            )}

            <div className="flex items-start gap-2">
              <CalendarIcon className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="text-sm font-medium">Added On</p>
                <p className="text-muted-foreground">
                  {format(new Date(project.created_at), 'MMMM d, yyyy')}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-muted/40 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Interested in Similar Work?</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Contact our team to discuss your project requirements and get a custom proposal.
            </p>
            <Button
              className="w-full"
              onClick={() => router.push('/contact')}
            >
              Get Started
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
