-- =====================================================
-- SCRIPT 15: CREATE MISSING MANAGER TABLES
-- =====================================================

-- Create project_assignments table if it doesn't exist
CREATE TABLE IF NOT EXISTS project_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'transferred')),
    priority VARCHAR(50) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    notes TEXT,
    created_by UUID REFERENCES profiles(id), -- Admin who made assignment
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create negotiation_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS negotiation_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES profiles(id),
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    session_type VARCHAR(50) DEFAULT 'pricing' CHECK (session_type IN ('pricing', 'timeline', 'scope', 'terms')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    initial_terms JSONB,
    final_terms JSONB,
    manager_notes TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create manager_activities table if it doesn't exist
CREATE TABLE IF NOT EXISTS manager_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    manager_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    activity_type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    participants JSONB DEFAULT '[]',
    outcome VARCHAR(100),
    time_spent_minutes INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_project_assignments_manager_id ON project_assignments(manager_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON project_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_status ON project_assignments(status);
CREATE INDEX IF NOT EXISTS idx_project_assignments_assigned_at ON project_assignments(assigned_at DESC);

CREATE INDEX IF NOT EXISTS idx_negotiation_sessions_manager_id ON negotiation_sessions(manager_id);
CREATE INDEX IF NOT EXISTS idx_negotiation_sessions_project_id ON negotiation_sessions(project_id);
CREATE INDEX IF NOT EXISTS idx_negotiation_sessions_status ON negotiation_sessions(status);
CREATE INDEX IF NOT EXISTS idx_negotiation_sessions_created_at ON negotiation_sessions(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_manager_activities_manager_id ON manager_activities(manager_id);
CREATE INDEX IF NOT EXISTS idx_manager_activities_project_id ON manager_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_manager_activities_activity_type ON manager_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_manager_activities_created_at ON manager_activities(created_at DESC);

-- Enable RLS
ALTER TABLE project_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE negotiation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE manager_activities ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for project_assignments
CREATE POLICY "Allow managers to see their assignments"
ON project_assignments FOR SELECT
TO authenticated
USING (
  manager_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin')
  )
);

CREATE POLICY "Allow admins to manage assignments"
ON project_assignments FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- Add RLS policies for negotiation_sessions
CREATE POLICY "Allow participants to view negotiations"
ON negotiation_sessions FOR SELECT
TO authenticated
USING (
  manager_id = auth.uid() OR
  client_id = auth.uid() OR
  designer_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin')
  )
);

CREATE POLICY "Allow managers and admins to manage negotiations"
ON negotiation_sessions FOR ALL
TO authenticated
USING (
  manager_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin')
  )
);

-- Add RLS policies for manager_activities
CREATE POLICY "Allow managers to see their activities"
ON manager_activities FOR SELECT
TO authenticated
USING (
  manager_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin')
  )
);

CREATE POLICY "Allow managers to manage their activities"
ON manager_activities FOR ALL
TO authenticated
USING (
  manager_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role IN ('admin')
  )
);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON project_assignments TO authenticated;
GRANT SELECT, INSERT, UPDATE ON negotiation_sessions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON manager_activities TO authenticated;

-- Verify completion
SELECT 'Script 15 completed: Missing manager tables created' as status;
