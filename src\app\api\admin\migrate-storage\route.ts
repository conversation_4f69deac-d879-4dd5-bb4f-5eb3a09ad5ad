import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { runCompleteStorageMigration } from '@/lib/storage-migration';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Run the migration
    const results = await runCompleteStorageMigration();

    return NextResponse.json({
      success: true,
      message: 'Storage migration completed',
      results
    });
  } catch (error) {
    console.error('Storage migration error:', error);
    return NextResponse.json(
      { 
        error: 'Migration failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Get migration status/preview
    const { data: deliverableFiles } = await supabase
      .from('project_milestones')
      .select('id, deliverable_files')
      .not('deliverable_files', 'is', null);

    const { data: conversationFiles } = await supabase
      .from('conversation_messages')
      .select('id, file_url')
      .not('file_url', 'is', null);

    const { data: portfolioFiles } = await supabase
      .from('designer_applications')
      .select('id, portfolio_files')
      .not('portfolio_files', 'is', null);

    const supabaseFileCount = {
      deliverables: deliverableFiles?.reduce((count, milestone) => {
        return count + (Array.isArray(milestone.deliverable_files) ? milestone.deliverable_files.length : 0);
      }, 0) || 0,
      conversations: conversationFiles?.length || 0,
      portfolios: portfolioFiles?.reduce((count, app) => {
        return count + (Array.isArray(app.portfolio_files) ? app.portfolio_files.length : 0);
      }, 0) || 0
    };

    return NextResponse.json({
      success: true,
      preview: {
        totalFilesToMigrate: supabaseFileCount.deliverables + supabaseFileCount.conversations + supabaseFileCount.portfolios,
        breakdown: supabaseFileCount
      }
    });
  } catch (error) {
    console.error('Migration preview error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get migration preview', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
