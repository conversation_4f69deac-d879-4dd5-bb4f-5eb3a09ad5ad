import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/designer/availability
 * Gets the current availability status for the authenticated designer
 */
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role and availability
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, availability')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Only designers can check availability
    if (profile.role !== 'designer') {
      return NextResponse.json(
        { error: 'Only designers can check availability' },
        { status: 403 }
      );
    }
    
    return NextResponse.json({ availability: profile.availability }, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/designer/availability:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/designer/availability
 * Updates the availability status for the authenticated designer
 * 
 * Request body:
 * {
 *   availability: boolean;
 * }
 */
export async function PATCH(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Only designers can update availability
    if (profile.role !== 'designer') {
      return NextResponse.json(
        { error: 'Only designers can update availability' },
        { status: 403 }
      );
    }
    
    const { availability } = await request.json();
    
    // Validate required fields
    if (availability === undefined) {
      return NextResponse.json(
        { error: 'Availability status is required' },
        { status: 400 }
      );
    }
    
    // Update the availability status
    const { data, error } = await supabase
      .from('profiles')
      .update({ availability })
      .eq('id', user.id)
      .select('availability')
      .single();
    
    if (error) {
      console.error('Error updating availability status:', error);
      return NextResponse.json(
        { error: 'Failed to update availability status' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error in PATCH /api/designer/availability:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
