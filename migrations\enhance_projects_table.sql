-- Enhance projects table with additional columns for better project management
-- Run this migration to add missing columns to the projects table

-- Add priority column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'priority') THEN
        ALTER TABLE projects ADD COLUMN priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
    END IF;
END $$;

-- Add tags column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'tags') THEN
        ALTER TABLE projects ADD COLUMN tags TEXT[] DEFAULT '{}';
    END IF;
END $$;

-- Add notes column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'notes') THEN
        ALTER TABLE projects ADD COLUMN notes TEXT;
    END IF;
END $$;

-- Add assigned_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'assigned_at') THEN
        ALTER TABLE projects ADD COLUMN assigned_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Add assigned_by column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'assigned_by') THEN
        ALTER TABLE projects ADD COLUMN assigned_by UUID REFERENCES profiles(id);
    END IF;
END $$;

-- Ensure submissions table exists with proper structure
CREATE TABLE IF NOT EXISTS submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'needs_revision', 'rejected')),
    revision_requested BOOLEAN DEFAULT FALSE,
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ensure submission_files table exists
CREATE TABLE IF NOT EXISTS submission_files (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    submission_id UUID NOT NULL REFERENCES submissions(id) ON DELETE CASCADE,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_type TEXT,
    file_size BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ensure submission_feedback table exists
CREATE TABLE IF NOT EXISTS submission_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    submission_id UUID NOT NULL REFERENCES submissions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_priority ON projects(priority);
CREATE INDEX IF NOT EXISTS idx_projects_tags ON projects USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_projects_assigned_at ON projects(assigned_at);
CREATE INDEX IF NOT EXISTS idx_projects_assigned_by ON projects(assigned_by);

CREATE INDEX IF NOT EXISTS idx_submissions_project_id ON submissions(project_id);
CREATE INDEX IF NOT EXISTS idx_submissions_designer_id ON submissions(designer_id);
CREATE INDEX IF NOT EXISTS idx_submissions_status ON submissions(status);
CREATE INDEX IF NOT EXISTS idx_submissions_created_at ON submissions(created_at);

CREATE INDEX IF NOT EXISTS idx_submission_files_submission_id ON submission_files(submission_id);
CREATE INDEX IF NOT EXISTS idx_submission_feedback_submission_id ON submission_feedback(submission_id);
CREATE INDEX IF NOT EXISTS idx_submission_feedback_user_id ON submission_feedback(user_id);

-- Enable RLS on new tables
ALTER TABLE submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE submission_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE submission_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies for submissions
CREATE POLICY "Users can view submissions for their projects" ON submissions
    FOR SELECT USING (
        project_id IN (
            SELECT id FROM projects 
            WHERE client_id = auth.uid() OR designer_id = auth.uid()
        )
        OR EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Designers can create submissions for their projects" ON submissions
    FOR INSERT WITH CHECK (
        designer_id = auth.uid() 
        AND project_id IN (
            SELECT id FROM projects WHERE designer_id = auth.uid()
        )
    );

CREATE POLICY "Designers can update their own submissions" ON submissions
    FOR UPDATE USING (designer_id = auth.uid());

CREATE POLICY "Admins can update any submission" ON submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for submission_files
CREATE POLICY "Users can view files for accessible submissions" ON submission_files
    FOR SELECT USING (
        submission_id IN (
            SELECT id FROM submissions 
            WHERE project_id IN (
                SELECT id FROM projects 
                WHERE client_id = auth.uid() OR designer_id = auth.uid()
            )
        )
        OR EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Designers can upload files to their submissions" ON submission_files
    FOR INSERT WITH CHECK (
        submission_id IN (
            SELECT id FROM submissions WHERE designer_id = auth.uid()
        )
    );

-- RLS Policies for submission_feedback
CREATE POLICY "Users can view feedback for accessible submissions" ON submission_feedback
    FOR SELECT USING (
        submission_id IN (
            SELECT id FROM submissions 
            WHERE project_id IN (
                SELECT id FROM projects 
                WHERE client_id = auth.uid() OR designer_id = auth.uid()
            )
        )
        OR EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Users can create feedback for accessible submissions" ON submission_feedback
    FOR INSERT WITH CHECK (
        user_id = auth.uid()
        AND (
            submission_id IN (
                SELECT id FROM submissions 
                WHERE project_id IN (
                    SELECT id FROM projects 
                    WHERE client_id = auth.uid()
                )
            )
            OR EXISTS (
                SELECT 1 FROM profiles 
                WHERE id = auth.uid() AND role = 'admin'
            )
        )
    );

-- Update existing projects to have default priority if null
UPDATE projects SET priority = 'medium' WHERE priority IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN projects.priority IS 'Project priority level: low, medium, high, urgent';
COMMENT ON COLUMN projects.tags IS 'Array of project tags for categorization';
COMMENT ON COLUMN projects.notes IS 'Internal notes for admin and team members';
COMMENT ON COLUMN projects.assigned_at IS 'Timestamp when project was assigned to designer';
COMMENT ON COLUMN projects.assigned_by IS 'Admin who assigned the project';

COMMENT ON TABLE submissions IS 'Designer submissions for project deliverables';
COMMENT ON TABLE submission_files IS 'Files attached to submissions';
COMMENT ON TABLE submission_feedback IS 'Feedback and reviews for submissions';
