"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Plus,
  Search,
  Edit,
  Trash2,
  Copy,
  FileText,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Bell,
  Eye,
  Users
} from "lucide-react";

interface MessageTemplate {
  id: string;
  name: string;
  description: string;
  title: string;
  content: string;
  message_type: 'info' | 'warning' | 'success' | 'urgent' | 'announcement';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  action_required: boolean;
  category: string;
  created_at: string;
  created_by: string;
  created_by_name?: string;
  usage_count?: number;
}

export default function MessageTemplatesPage() {
  const { user } = useOptimizedAuth();
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    if (user) {
      fetchTemplates();
    }
  }, [user]);

  const fetchTemplates = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // For now, we'll use mock data since we haven't created the templates table yet
      // In a real implementation, you'd fetch from admin_message_templates table
      const mockTemplates: MessageTemplate[] = [
        {
          id: '1',
          name: 'Welcome New Designer',
          description: 'Welcome message for newly approved designers',
          title: 'Welcome to the Platform!',
          content: 'Welcome to our design platform! We\'re excited to have you join our community of talented designers. Please complete your profile and upload your portfolio to start receiving project opportunities.',
          message_type: 'info',
          priority: 'normal',
          action_required: true,
          category: 'onboarding',
          created_at: new Date().toISOString(),
          created_by: user.id,
          created_by_name: 'Admin Team',
          usage_count: 15
        },
        {
          id: '2',
          name: 'Portfolio Review Complete',
          description: 'Notification when portfolio review is completed',
          title: 'Portfolio Review Complete',
          content: 'Your portfolio has been reviewed and approved! Your work showcases excellent design skills. You can now receive project assignments.',
          message_type: 'success',
          priority: 'normal',
          action_required: false,
          category: 'approval',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          created_by: user.id,
          created_by_name: 'Admin Team',
          usage_count: 8
        },
        {
          id: '3',
          name: 'Payment Method Required',
          description: 'Reminder to add payment method',
          title: 'Payment Method Update Required',
          content: 'Please update your payment method to ensure timely payments for completed projects. Go to Settings > Payment to add your preferred payment method.',
          message_type: 'warning',
          priority: 'high',
          action_required: true,
          category: 'payment',
          created_at: new Date(Date.now() - 172800000).toISOString(),
          created_by: user.id,
          created_by_name: 'Admin Team',
          usage_count: 23
        },
        {
          id: '4',
          name: 'Platform Maintenance',
          description: 'Scheduled maintenance notification',
          title: 'Scheduled Platform Maintenance',
          content: 'We will be performing scheduled maintenance on {date} from {start_time} to {end_time}. During this time, some features may be temporarily unavailable.',
          message_type: 'announcement',
          priority: 'normal',
          action_required: false,
          category: 'maintenance',
          created_at: new Date(Date.now() - 259200000).toISOString(),
          created_by: user.id,
          created_by_name: 'Admin Team',
          usage_count: 5
        },
        {
          id: '5',
          name: 'Urgent Project Update',
          description: 'Template for urgent project-related updates',
          title: 'Urgent: Project Update Required',
          content: 'Your immediate attention is required for project "{project_name}". Please review the client feedback and provide an updated proposal within 24 hours.',
          message_type: 'urgent',
          priority: 'urgent',
          action_required: true,
          category: 'project',
          created_at: new Date(Date.now() - 345600000).toISOString(),
          created_by: user.id,
          created_by_name: 'Admin Team',
          usage_count: 12
        }
      ];

      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error fetching message templates:', error);
      setError('Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const deleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    try {
      // In a real implementation, you'd delete from the database
      setTemplates(prev => prev.filter(t => t.id !== templateId));
      setSuccess('Template deleted successfully');
    } catch (error) {
      console.error('Error deleting template:', error);
      setError('Failed to delete template');
    }
  };

  const duplicateTemplate = async (template: MessageTemplate) => {
    try {
      const newTemplate: MessageTemplate = {
        ...template,
        id: Date.now().toString(),
        name: `${template.name} (Copy)`,
        created_at: new Date().toISOString(),
        usage_count: 0
      };

      setTemplates(prev => [newTemplate, ...prev]);
      setSuccess('Template duplicated successfully');
    } catch (error) {
      console.error('Error duplicating template:', error);
      setError('Failed to duplicate template');
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'urgent': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'announcement': return <Bell className="h-4 w-4 text-blue-500" />;
      default: return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'urgent': return 'bg-red-50 border-red-200 text-red-800';
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'success': return 'bg-green-50 border-green-200 text-green-800';
      case 'announcement': return 'bg-blue-50 border-blue-200 text-blue-800';
      default: return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      onboarding: 'bg-green-100 text-green-800',
      approval: 'bg-blue-100 text-blue-800',
      payment: 'bg-yellow-100 text-yellow-800',
      maintenance: 'bg-purple-100 text-purple-800',
      project: 'bg-orange-100 text-orange-800',
      general: 'bg-gray-100 text-gray-800'
    };
    return colors[category] || colors.general;
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
    const matchesType = typeFilter === 'all' || template.message_type === typeFilter;

    return matchesSearch && matchesCategory && matchesType;
  });

  const categories = [...new Set(templates.map(t => t.category))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/admin/admin-messages" className="mr-4">
            <Button variant="ghost" className="p-0 h-auto">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Message Templates</h1>
            <p className="text-gray-600">Create and manage reusable message templates</p>
          </div>
        </div>
        <Button 
          onClick={() => setShowCreateModal(true)}
          className="bg-brown-600 hover:bg-brown-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>

      {/* Alerts */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>

          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Types</option>
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="success">Success</option>
            <option value="urgent">Urgent</option>
            <option value="announcement">Announcement</option>
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      {filteredTemplates.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {templates.length === 0 ? 'No templates yet' : 'No templates match your filters'}
          </h3>
          <p className="text-gray-500 mb-4">
            {templates.length === 0
              ? 'Create your first message template to streamline communications'
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {templates.length === 0 && (
            <Button 
              onClick={() => setShowCreateModal(true)}
              className="bg-brown-600 hover:bg-brown-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create First Template
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2">
                  {getMessageTypeIcon(template.message_type)}
                  <h3 className="text-lg font-semibold text-gray-900 truncate">{template.name}</h3>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => duplicateTemplate(template)}
                    className="p-1 h-auto"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteTemplate(template.id)}
                    className="p-1 h-auto text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <p className="text-gray-600 text-sm mb-4">{template.description}</p>

              <div className="space-y-3 mb-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">Title:</h4>
                  <p className="text-sm text-gray-600 truncate">{template.title}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-1">Content Preview:</h4>
                  <p className="text-sm text-gray-600 line-clamp-3">{template.content}</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getMessageTypeColor(template.message_type)}`}>
                  {template.message_type.toUpperCase()}
                </span>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(template.priority)}`}>
                  {template.priority.toUpperCase()}
                </span>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(template.category)}`}>
                  {template.category.toUpperCase()}
                </span>
                {template.action_required && (
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                    ACTION REQUIRED
                  </span>
                )}
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  <span>Used {template.usage_count} times</span>
                </div>
                <Link href={`/admin/admin-messages/create?template=${template.id}`}>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Use Template
                  </Button>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
