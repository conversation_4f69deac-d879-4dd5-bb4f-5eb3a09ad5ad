import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

/**
 * API route for bulk actions on tracking requests
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Bulk action API called');
    
    const body = await request.json();
    const { action, requestIds, assignTo } = body;

    console.log('Bulk action request:', { action, requestIds: requestIds?.length, assignTo });

    // Validate required fields
    if (!action || !requestIds || !Array.isArray(requestIds) || requestIds.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: action and requestIds' },
        { status: 400 }
      );
    }

    // Validate action type
    if (!['assign', 'archive', 'delete'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be assign, archive, or delete' },
        { status: 400 }
      );
    }

    // For assign action, validate assignTo field
    if (action === 'assign' && !assignTo) {
      return NextResponse.json(
        { error: 'assignTo field is required for assign action' },
        { status: 400 }
      );
    }

    let result;
    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    try {
      switch (action) {
        case 'assign':
          console.log(`Assigning ${requestIds.length} requests to ${assignTo}`);
          
          const { data: assignData, error: assignError } = await supabaseServerClient
            .from('tracking_requests')
            .update({
              assigned_to: assignTo,
              internal_status: 'assigned',
              updated_at: new Date().toISOString()
            })
            .in('id', requestIds)
            .select();

          if (assignError) {
            console.error('Assign error:', assignError);
            return NextResponse.json(
              { error: 'Failed to assign requests', details: assignError.message },
              { status: 500 }
            );
          }

          successCount = assignData?.length || 0;
          result = { assigned: successCount, assignedTo: assignTo };
          break;

        case 'archive':
          console.log(`Archiving ${requestIds.length} requests`);
          
          const { data: archiveData, error: archiveError } = await supabaseServerClient
            .from('tracking_requests')
            .update({
              internal_status: 'archived',
              updated_at: new Date().toISOString()
            })
            .in('id', requestIds)
            .select();

          if (archiveError) {
            console.error('Archive error:', archiveError);
            return NextResponse.json(
              { error: 'Failed to archive requests', details: archiveError.message },
              { status: 500 }
            );
          }

          successCount = archiveData?.length || 0;
          result = { archived: successCount };
          break;

        case 'delete':
          console.log(`Deleting ${requestIds.length} requests`);
          
          const { data: deleteData, error: deleteError } = await supabaseServerClient
            .from('tracking_requests')
            .delete()
            .in('id', requestIds)
            .select();

          if (deleteError) {
            console.error('Delete error:', deleteError);
            return NextResponse.json(
              { error: 'Failed to delete requests', details: deleteError.message },
              { status: 500 }
            );
          }

          successCount = deleteData?.length || 0;
          result = { deleted: successCount };
          break;

        default:
          return NextResponse.json(
            { error: 'Invalid action' },
            { status: 400 }
          );
      }

      console.log(`Bulk ${action} completed:`, result);

      return NextResponse.json({
        success: true,
        action,
        result,
        successCount,
        errorCount,
        errors: errors.length > 0 ? errors : undefined
      });

    } catch (dbError) {
      console.error('Database error during bulk action:', dbError);
      
      // If database is not available, return mock success for development
      if (process.env.NODE_ENV === 'development') {
        console.warn('Database not available, returning mock success');
        return NextResponse.json({
          success: true,
          action,
          result: { [action]: requestIds.length },
          successCount: requestIds.length,
          errorCount: 0,
          mock: true
        });
      }

      return NextResponse.json(
        { error: 'Database operation failed', details: dbError instanceof Error ? dbError.message : 'Unknown error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in bulk action API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
