import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('Authorization');

    let supabase;
    let user;

    // Try to use service role key if available for server-side operations
    if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
      const { createClient } = await import('@supabase/supabase-js');
      supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            persistSession: false
          }
        }
      );

      // For service role, we need to get user from the auth header
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.split(' ')[1];
        const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(token);

        if (authError || !authUser) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        user = authUser;
      } else {
        return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
      }
    }
    // Fall back to SSR client with improved cookie handling
    else {
      const cookieStore = await cookies();

      supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get: (name: string) => {
              const cookie = cookieStore.get(name);
              return cookie?.value;
            },
            set: (name: string, value: string, options: any) => {
              try {
                cookieStore.set({ name, value, ...options });
              } catch (error) {
                console.error('Error setting cookie:', error);
              }
            },
            remove: (name: string, options: any) => {
              try {
                cookieStore.set({ name, value: '', ...options });
              } catch (error) {
                console.error('Error removing cookie:', error);
              }
            },
          },
        }
      );

      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

      if (authError || !authUser) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      user = authUser;
    }

    const { searchParams } = new URL(request.url);
    const user1 = searchParams.get('user1');
    const user2 = searchParams.get('user2');

    if (!user1 || !user2) {
      return NextResponse.json({ error: 'Both user1 and user2 parameters are required' }, { status: 400 });
    }

    // Find project where one user is client and other is designer
    const { data: project, error } = await supabase
      .from('projects')
      .select('id, title, status')
      .or(`and(client_id.eq.${user1},designer_id.eq.${user2}),and(client_id.eq.${user2},designer_id.eq.${user1})`)
      .eq('status', 'in_progress')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error finding project:', error);
      return NextResponse.json({ error: 'Failed to find project' }, { status: 500 });
    }

    return NextResponse.json(project || null);
  } catch (error) {
    console.error('Error in GET /api/projects/find-by-participants:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
