// Optimized PayPal SDK Manager for fast loading and caching
import { useState } from 'react';

declare global {
  interface Window {
    paypal?: {
      Buttons: (config: any) => {
        render: (container: HTMLElement) => void;
      };
      FUNDING: {
        PAYPAL: string;
        CARD: string;
        CREDIT: string;
        VENMO: string;
      };
    };
  }
}

interface PayPalSDKConfig {
  clientId: string;
  currency?: string;
  intent?: string;
  components?: string;
  disableFunding?: string;
  enableFunding?: string;
}

class PayPalSDKManager {
  private static instance: PayPalSDKManager;
  private scriptLoaded = false;
  private scriptLoading = false;
  private loadPromise: Promise<void> | null = null;
  private callbacks: Array<() => void> = [];

  private constructor() {}

  static getInstance(): PayPalSDKManager {
    if (!PayPalSDKManager.instance) {
      PayPalSDKManager.instance = new PayPalSDKManager();
    }
    return PayPalSDKManager.instance;
  }

  async loadSDK(config: PayPalSDKConfig): Promise<void> {
    // Return immediately if already loaded
    if (this.scriptLoaded && window.paypal) {
      return Promise.resolve();
    }

    // Return existing promise if currently loading
    if (this.scriptLoading && this.loadPromise) {
      return this.loadPromise;
    }

    // Create new load promise
    this.loadPromise = this.createLoadPromise(config);
    return this.loadPromise;
  }

  private createLoadPromise(config: PayPalSDKConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if script already exists
      const existingScript = document.querySelector('script[src*="paypal.com/sdk/js"]');
      if (existingScript && window.paypal) {
        this.scriptLoaded = true;
        resolve();
        return;
      }

      this.scriptLoading = true;

      // Build optimized SDK URL
      const params = new URLSearchParams({
        'client-id': config.clientId,
        currency: config.currency || 'USD',
        intent: config.intent || 'capture',
        components: config.components || 'buttons',
        'disable-funding': config.disableFunding || 'credit,card,venmo,sepa,bancontact,eps,giropay,ideal,mybank,p24,sofort',
        'enable-funding': config.enableFunding || 'paypal'
      });

      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?${params.toString()}`;
      script.async = true;
      script.defer = true; // Better performance than async alone

      script.onload = () => {
        this.scriptLoaded = true;
        this.scriptLoading = false;
        
        // Execute all pending callbacks
        this.callbacks.forEach(callback => callback());
        this.callbacks = [];
        
        resolve();
      };

      script.onerror = () => {
        this.scriptLoading = false;
        console.error('Failed to load PayPal SDK');
        reject(new Error('Failed to load PayPal SDK'));
      };

      // Remove existing script if any
      if (existingScript) {
        existingScript.remove();
      }

      document.head.appendChild(script);
    });
  }

  onReady(callback: () => void): void {
    if (this.scriptLoaded && window.paypal) {
      callback();
    } else {
      this.callbacks.push(callback);
    }
  }

  isLoaded(): boolean {
    return this.scriptLoaded && !!window.paypal;
  }
}

// Singleton instance
export const paypalSDK = PayPalSDKManager.getInstance();

// Optimized PayPal configuration
export const getOptimizedPayPalConfig = (): PayPalSDKConfig => {
  const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
  
  if (!clientId || clientId === 'YOUR_PAYPAL_CLIENT_ID_HERE') {
    throw new Error('PayPal Client ID is not configured');
  }

  return {
    clientId,
    currency: 'USD',
    intent: 'capture',
    components: 'buttons', // Only load buttons component
    disableFunding: 'credit,card,venmo,sepa,bancontact,eps,giropay,ideal,mybank,p24,sofort', // Disable unused funding sources
    enableFunding: 'paypal' // Only enable PayPal
  };
};

// Preload PayPal SDK for better performance
export const preloadPayPalSDK = async (): Promise<void> => {
  try {
    const config = getOptimizedPayPalConfig();
    await paypalSDK.loadSDK(config);
  } catch (error) {
    console.warn('PayPal SDK preload failed:', error);
  }
};

// Hook for React components
export const usePayPalSDK = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadSDK = async () => {
    if (paypalSDK.isLoaded()) {
      setIsLoaded(true);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const config = getOptimizedPayPalConfig();
      await paypalSDK.loadSDK(config);
      setIsLoaded(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load PayPal SDK');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoaded,
    isLoading,
    error,
    loadSDK
  };
};

// Cache for PayPal access tokens
class PayPalTokenCache {
  private static instance: PayPalTokenCache;
  private token: string | null = null;
  private tokenExpiry: number = 0;

  private constructor() {}

  static getInstance(): PayPalTokenCache {
    if (!PayPalTokenCache.instance) {
      PayPalTokenCache.instance = new PayPalTokenCache();
    }
    return PayPalTokenCache.instance;
  }

  setToken(token: string, expiresIn: number): void {
    this.token = token;
    // Set expiry to 90% of actual expiry for safety margin
    this.tokenExpiry = Date.now() + (expiresIn * 1000 * 0.9);
  }

  getToken(): string | null {
    if (this.token && Date.now() < this.tokenExpiry) {
      return this.token;
    }
    return null;
  }

  clearToken(): void {
    this.token = null;
    this.tokenExpiry = 0;
  }
}

export const paypalTokenCache = PayPalTokenCache.getInstance();
