-- =====================================================
-- SCRIPT 9: CREATE PERFORMANCE INDEXES
-- =====================================================

-- PayPal Escrow indexes
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_project_id ON paypal_escrow_holds(project_id);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_designer_id ON paypal_escrow_holds(designer_id);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_status ON paypal_escrow_holds(status);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_holds_created_at ON paypal_escrow_holds(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_status ON paypal_escrow_releases(status);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_hold_id ON paypal_escrow_releases(escrow_hold_id);
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_created_at ON paypal_escrow_releases(created_at DESC);

-- Project briefs indexes (using actual column names from existing table)
CREATE INDEX IF NOT EXISTS idx_project_briefs_status ON project_briefs(status);
CREATE INDEX IF NOT EXISTS idx_project_briefs_client_id ON project_briefs(client_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_assigned_designer_id ON project_briefs(assigned_designer_id);
CREATE INDEX IF NOT EXISTS idx_project_briefs_assigned_by ON project_briefs(assigned_by);
CREATE INDEX IF NOT EXISTS idx_project_briefs_created_at ON project_briefs(created_at DESC);

-- Platform revenue indexes
CREATE INDEX IF NOT EXISTS idx_platform_revenue_status ON platform_revenue(status);
CREATE INDEX IF NOT EXISTS idx_platform_revenue_project_id ON platform_revenue(project_id);
CREATE INDEX IF NOT EXISTS idx_platform_revenue_created_at ON platform_revenue(created_at DESC);

-- Enhanced proposal indexes (using correct table name)
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_manager_status ON project_proposals_enhanced(manager_status);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_priority ON project_proposals_enhanced(priority);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_status ON project_proposals_enhanced(status);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_designer_id ON project_proposals_enhanced(designer_id);
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_brief_id ON project_proposals_enhanced(brief_id);

-- Designer payouts indexes
CREATE INDEX IF NOT EXISTS idx_designer_payouts_designer_id ON designer_payouts(designer_id);
CREATE INDEX IF NOT EXISTS idx_designer_payouts_project_id ON designer_payouts(project_id);
CREATE INDEX IF NOT EXISTS idx_designer_payouts_status ON designer_payouts(status);
CREATE INDEX IF NOT EXISTS idx_designer_payouts_created_at ON designer_payouts(created_at DESC);

-- Verify completion
SELECT 'Script 9 completed: Performance indexes created' as status;
