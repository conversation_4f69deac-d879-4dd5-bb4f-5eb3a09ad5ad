'use client';

import { useSearchParams } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import UnifiedMessaging from '@/components/messaging/UnifiedMessaging';

export default function ClientMessages() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get('project');
  const otherUserId = searchParams.get('otherUserId');
  const conversationType = searchParams.get('type') as 'direct' | 'project' || 'project';
  const { user, profile, loading } = useOptimizedAuth();

  if (loading) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto py-10">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h1>
          <p className="text-gray-600">Please log in to access your messages.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Messages</h1>
        <p className="text-gray-600">Communicate with your designers</p>
      </div>

      <UnifiedMessaging
        projectId={projectId || undefined}
        otherUserId={otherUserId || undefined}
        conversationType={conversationType}
      />
    </div>
  );
}
