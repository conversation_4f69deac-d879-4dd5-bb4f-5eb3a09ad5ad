import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useOptimizedAuth } from './useOptimizedAuth';

interface ProjectAssignment {
  id: string;
  project_id: string;
  manager_id: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: string;
  assigned_at: string;
  notes?: string;
}

interface ManagerActivity {
  id: string;
  manager_id: string;
  project_id: string;
  activity_type: string;
  description: string;
  participants?: string[];
  outcome?: string;
  time_spent_minutes?: number;
  created_at: string;
}

interface NegotiationSession {
  id: string;
  project_id: string;
  manager_id: string;
  client_id: string;
  designer_id: string;
  session_type: 'pricing' | 'timeline' | 'scope' | 'terms';
  status: 'active' | 'completed' | 'cancelled';
  initial_terms?: any;
  final_terms?: any;
  manager_notes?: string;
  started_at: string;
  completed_at?: string;
}

export function useManagerWorkflow() {
  const { user, profile } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get manager's assigned projects
  const getAssignedProjects = async (filters?: {
    status?: string;
    priority?: string;
    limit?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('Authentication required');

      const params = new URLSearchParams();
      if (filters?.status) params.append('status', filters.status);
      if (filters?.priority) params.append('priority', filters.priority);
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/manager/projects?${params.toString()}`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch projects');
      }

      const data = await response.json();
      return data.projects;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch projects');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Create manager activity
  const createActivity = async (activityData: {
    project_id: string;
    activity_type: string;
    description: string;
    participants?: string[];
    outcome?: string;
    time_spent_minutes?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);

      const token = await user?.getIdToken();
      if (!token) throw new Error('Authentication required');

      const response = await fetch('/api/manager/activities', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(activityData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create activity');
      }

      const activity = await response.json();
      return activity;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create activity');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Start negotiation session
  const startNegotiation = async (negotiationData: {
    project_id: string;
    session_type: 'pricing' | 'timeline' | 'scope' | 'terms';
    initial_terms?: any;
    manager_notes?: string;
  }) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('Authentication required');

      const response = await fetch('/api/manager/negotiations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(negotiationData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start negotiation');
      }

      const session = await response.json();
      return session;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start negotiation');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get negotiation sessions
  const getNegotiations = async (filters?: {
    status?: string;
    session_type?: string;
    project_id?: string;
    limit?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('Authentication required');

      const params = new URLSearchParams();
      if (filters?.status) params.append('status', filters.status);
      if (filters?.session_type) params.append('session_type', filters.session_type);
      if (filters?.project_id) params.append('project_id', filters.project_id);
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/manager/negotiations?${params.toString()}`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch negotiations');
      }

      const data = await response.json();
      return data.sessions;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch negotiations');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get manager activities
  const getActivities = async (filters?: {
    project_id?: string;
    activity_type?: string;
    limit?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);

      if (!user) throw new Error('Authentication required');

      const params = new URLSearchParams();
      if (filters?.project_id) params.append('project_id', filters.project_id);
      if (filters?.activity_type) params.append('activity_type', filters.activity_type);
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const token = await user?.getIdToken();
      if (!token) throw new Error('Authentication required');

      const response = await fetch(`/api/manager/activities?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch activities');
      }

      const data = await response.json();
      return data.activities;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch activities');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Approve milestone (manager approval)
  const approveMilestone = async (milestoneId: string, notes?: string) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('project_milestones')
        .update({
          manager_approved_at: new Date().toISOString(),
          manager_approved_by: user?.id
        })
        .eq('id', milestoneId)
        .select()
        .single();

      if (error) throw error;

      // Create activity record
      await createActivity({
        project_id: data.project_id,
        activity_type: 'milestone_review',
        description: `Approved milestone: ${data.title}`,
        outcome: 'approved',
        participants: [user?.id || '']
      });

      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to approve milestone');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Escalate issue
  const escalateIssue = async (projectId: string, issueDescription: string, participants: string[]) => {
    try {
      setLoading(true);
      setError(null);

      // Create escalation activity
      const activity = await createActivity({
        project_id: projectId,
        activity_type: 'escalation',
        description: issueDescription,
        participants,
        outcome: 'escalated'
      });

      // Create notifications for admin and participants
      const notifications = participants.map(participantId => ({
        recipient_id: participantId,
        notification_type: 'manager_escalation',
        title: 'Issue Escalated',
        message: issueDescription,
        priority: 'urgent',
        metadata: { 
          activity_id: activity.id, 
          project_id: projectId,
          escalated_by: user?.id 
        }
      }));

      await supabase
        .from('workflow_notifications')
        .insert(notifications);

      return activity;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to escalate issue');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Check if user is assigned to project
  const checkProjectAssignment = async (projectId: string) => {
    try {
      const { data, error } = await supabase
        .from('project_assignments')
        .select('*')
        .eq('project_id', projectId)
        .eq('manager_id', user?.id)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check assignment');
      return null;
    }
  };

  return {
    loading,
    error,
    getAssignedProjects,
    createActivity,
    startNegotiation,
    getNegotiations,
    getActivities,
    approveMilestone,
    escalateIssue,
    checkProjectAssignment
  };
}
