-- Check conversation_messages table data
SELECT 
  COUNT(*) as total_messages,
  COUNT(CASE WHEN message_type = 'text' THEN 1 END) as text_messages,
  COUNT(CASE WHEN message_type = 'file' THEN 1 END) as file_messages,
  MIN(created_at) as oldest_message,
  MAX(created_at) as newest_message
FROM conversation_messages;

-- Sample messages
SELECT 
  id,
  conversation_id,
  sender_id,
  content,
  message_type,
  created_at
FROM conversation_messages 
ORDER BY created_at DESC 
LIMIT 5;
