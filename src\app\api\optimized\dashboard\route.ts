import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * Optimized Dashboard API
 * High-performance dashboard data with caching and minimal queries
 */

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const forceRefresh = searchParams.get('refresh') === 'true';

    // Get optimized dashboard data based on role
    let dashboardData: any = {};

    switch (profile.role) {
      case 'manager':
        dashboardData = await getOptimizedManagerDashboard(user.id, forceRefresh);
        break;
      case 'client':
        dashboardData = await getOptimizedClientDashboard(user.id, forceRefresh);
        break;
      case 'designer':
        dashboardData = await getOptimizedDesignerDashboard(user.id, forceRefresh);
        break;
      case 'admin':
        dashboardData = await getOptimizedAdminDashboard(user.id, forceRefresh);
        break;
      case 'quality_team':
        dashboardData = await getOptimizedQualityDashboard(user.id, forceRefresh);
        break;
      default:
        return NextResponse.json({ error: 'Invalid role' }, { status: 403 });
    }

    // Add cache metadata
    dashboardData.cache_info = {
      generated_at: new Date().toISOString(),
      user_role: profile.role,
      force_refresh: forceRefresh
    };

    return NextResponse.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Error in optimized dashboard API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Optimized Manager Dashboard
 */
async function getOptimizedManagerDashboard(managerId: string, forceRefresh: boolean) {
  try {
    // Get cached dashboard data
    let dashboardCache = null;
    if (!forceRefresh) {
      const { data: cache } = await supabase
        .from('manager_dashboard_cache')
        .select('*')
        .eq('manager_id', managerId)
        .single();
      
      // Use cache if it's fresh (less than 1 hour old)
      if (cache && new Date(cache.last_updated) > new Date(Date.now() - 60 * 60 * 1000)) {
        dashboardCache = cache;
      }
    }

    // If no fresh cache, get live data
    if (!dashboardCache) {
      // Refresh cache
      await supabase.rpc('refresh_manager_dashboard_cache', { target_manager_id: managerId });
      
      // Get refreshed cache
      const { data: cache } = await supabase
        .from('manager_dashboard_cache')
        .select('*')
        .eq('manager_id', managerId)
        .single();
      
      dashboardCache = cache;
    }

    // Get recent projects with metrics
    const { data: recentProjects } = await supabase
      .from('mv_project_overview')
      .select('*')
      .eq('manager_name', managerId)
      .order('created_at', { ascending: false })
      .limit(5);

    // Get recent activities
    const { data: recentActivities } = await supabase
      .from('manager_activities')
      .select(`
        id,
        activity_type,
        description,
        created_at,
        project:projects(title)
      `)
      .eq('manager_id', managerId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Get pending approvals
    const { data: pendingApprovals } = await supabase
      .from('paypal_escrow_releases')
      .select(`
        id,
        release_amount,
        release_type,
        created_at,
        escrow_hold:paypal_escrow_holds(
          project:projects(title)
        )
      `)
      .eq('manager_approval_status', 'pending')
      .order('created_at', { ascending: true })
      .limit(10);

    return {
      stats: dashboardCache || {},
      recent_projects: recentProjects || [],
      recent_activities: recentActivities || [],
      pending_approvals: pendingApprovals || [],
      performance_score: dashboardCache?.average_project_progress || 0,
      cache_age_minutes: dashboardCache 
        ? Math.round((Date.now() - new Date(dashboardCache.last_updated).getTime()) / (1000 * 60))
        : 0
    };

  } catch (error) {
    console.error('Error getting optimized manager dashboard:', error);
    throw error;
  }
}

/**
 * Optimized Client Dashboard
 */
async function getOptimizedClientDashboard(clientId: string, forceRefresh: boolean) {
  try {
    // Get client projects with cached metrics
    const { data: projects } = await supabase
      .from('mv_project_overview')
      .select('*')
      .eq('client_name', clientId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Get project statistics
    const stats = {
      total_projects: projects?.length || 0,
      active_projects: projects?.filter(p => p.status === 'active').length || 0,
      completed_projects: projects?.filter(p => p.status === 'completed').length || 0,
      average_progress: projects?.length 
        ? projects.reduce((sum, p) => sum + (p.progress_percentage || 0), 0) / projects.length
        : 0
    };

    // Get recent messages
    const { data: recentMessages } = await supabase
      .from('conversations')
      .select(`
        id,
        content,
        created_at,
        sender:profiles!sender_id(full_name),
        project:projects(title)
      `)
      .eq('client_id', clientId)
      .order('created_at', { ascending: false })
      .limit(5);

    return {
      stats,
      projects: projects || [],
      recent_messages: recentMessages || []
    };

  } catch (error) {
    console.error('Error getting optimized client dashboard:', error);
    throw error;
  }
}

/**
 * Optimized Designer Dashboard
 */
async function getOptimizedDesignerDashboard(designerId: string, forceRefresh: boolean) {
  try {
    // Get designer projects with cached metrics
    const { data: projects } = await supabase
      .from('mv_project_overview')
      .select('*')
      .eq('designer_name', designerId)
      .order('created_at', { ascending: false })
      .limit(10);

    // Get project statistics
    const stats = {
      total_projects: projects?.length || 0,
      active_projects: projects?.filter(p => p.status === 'active').length || 0,
      pending_reviews: projects?.filter(p => p.pending_reviews_count > 0).length || 0,
      average_quality_score: projects?.length 
        ? projects.reduce((sum, p) => sum + (p.quality_reviews_count || 0), 0) / projects.length
        : 0
    };

    // Get pending quality reviews
    const { data: pendingReviews } = await supabase
      .from('quality_reviews_new')
      .select(`
        id,
        submission_type,
        created_at,
        project:projects(title)
      `)
      .eq('designer_id', designerId)
      .in('status', ['pending', 'in_review'])
      .order('created_at', { ascending: true })
      .limit(5);

    return {
      stats,
      projects: projects || [],
      pending_reviews: pendingReviews || []
    };

  } catch (error) {
    console.error('Error getting optimized designer dashboard:', error);
    throw error;
  }
}

/**
 * Optimized Admin Dashboard
 */
async function getOptimizedAdminDashboard(adminId: string, forceRefresh: boolean) {
  try {
    // Get system-wide statistics
    const { data: systemStats } = await supabase
      .from('mv_manager_performance')
      .select('*')
      .order('avg_client_satisfaction', { ascending: false });

    // Get recent system activities
    const { data: systemActivities } = await supabase
      .from('system_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    // Calculate system metrics
    const stats = {
      total_managers: systemStats?.length || 0,
      total_projects: systemStats?.reduce((sum, m) => sum + m.total_projects, 0) || 0,
      average_satisfaction: systemStats?.length 
        ? systemStats.reduce((sum, m) => sum + (m.avg_client_satisfaction || 0), 0) / systemStats.length
        : 0,
      total_revenue: systemStats?.reduce((sum, m) => sum + (m.total_revenue_managed || 0), 0) || 0
    };

    return {
      stats,
      manager_performance: systemStats || [],
      system_activities: systemActivities || []
    };

  } catch (error) {
    console.error('Error getting optimized admin dashboard:', error);
    throw error;
  }
}

/**
 * Optimized Quality Dashboard
 */
async function getOptimizedQualityDashboard(qualityId: string, forceRefresh: boolean) {
  try {
    // Get assigned quality reviews
    const { data: assignedReviews } = await supabase
      .from('quality_reviews_new')
      .select(`
        id,
        submission_type,
        status,
        created_at,
        sla_deadline,
        project:projects(title),
        designer:profiles!designer_id(full_name)
      `)
      .eq('assigned_reviewer_id', qualityId)
      .in('status', ['pending', 'in_review'])
      .order('sla_deadline', { ascending: true })
      .limit(20);

    // Get quality team statistics
    const { data: qualityStats } = await supabase
      .rpc('get_quality_team_workload');

    // Calculate personal stats
    const stats = {
      assigned_reviews: assignedReviews?.length || 0,
      overdue_reviews: assignedReviews?.filter(r => new Date(r.sla_deadline) < new Date()).length || 0,
      reviews_today: assignedReviews?.filter(r => 
        new Date(r.created_at).toDateString() === new Date().toDateString()
      ).length || 0,
      average_completion_time: 0 // TODO: Calculate from completed reviews
    };

    return {
      stats,
      assigned_reviews: assignedReviews || [],
      team_workload: qualityStats || []
    };

  } catch (error) {
    console.error('Error getting optimized quality dashboard:', error);
    throw error;
  }
}
