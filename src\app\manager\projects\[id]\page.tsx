"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Users,
  DollarSign,
  Calendar,
  FileText,
  MessageSquare,
  Settings,
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle,
  Star,
  Upload,
  Download,
  Edit,
  Trash2,
  Plus,
  RefreshCw,
  Target,
  BarChart3
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  created_at: string;
  updated_at: string;
  deadline: string | null;
  quality_status: string;
  manager_id: string;
  client_id: string;
  designer_id: string;
  client: {
    full_name: string;
    email: string;
    phone: string;
  };
  designer: {
    full_name: string;
    email: string;
    phone: string;
  };
  milestones: ProjectMilestone[];
  files: ProjectFile[];
  activities: ProjectActivity[];
}

interface ProjectMilestone {
  id: string;
  title: string;
  description: string;
  due_date: string;
  status: string;
  completion_percentage: number;
  created_at: string;
}

interface ProjectFile {
  id: string;
  filename: string;
  file_url: string;
  file_type: string;
  file_size: number;
  uploaded_by: string;
  uploaded_at: string;
  uploader: {
    full_name: string;
  };
}

interface ProjectActivity {
  id: string;
  activity_type: string;
  description: string;
  created_at: string;
  user: {
    full_name: string;
  };
}

export default function ProjectDetailPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'milestones' | 'files' | 'activity'>('overview');

  useEffect(() => {
    if (user && profile?.role === 'manager' && projectId) {
      fetchProject();
    }
  }, [user, profile, projectId]);

  const fetchProject = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          client:profiles!projects_client_id_fkey(full_name, email, phone),
          designer:profiles!projects_designer_id_fkey(full_name, email, phone),
          milestones:project_milestones(*),
          files:project_files(*, uploader:profiles!project_files_uploaded_by_fkey(full_name)),
          activities:project_activities(*, user:profiles!project_activities_user_id_fkey(full_name))
        `)
        .eq('id', projectId)
        .eq('manager_id', user?.id)
        .single();

      if (error) throw error;
      setProject(data);
    } catch (error) {
      console.error('Error fetching project:', error);
      router.push('/manager/projects');
    } finally {
      setLoading(false);
    }
  };

  const updateProjectStatus = async (newStatus: string) => {
    if (!project) return;

    try {
      const { error } = await supabase
        .from('projects')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: projectId,
        activity_type: 'status_update',
        description: `Updated project status to ${newStatus}`,
        outcome: newStatus
      });

      fetchProject();
    } catch (error) {
      console.error('Error updating project status:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'on_hold':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'cancelled':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'on_hold':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getQualityStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (status) {
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'needs_revision':
        return `${baseClasses} bg-amber-100 text-amber-800`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'pending':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getMilestoneProgress = () => {
    if (!project?.milestones || project.milestones.length === 0) return 0;
    const totalProgress = project.milestones.reduce((sum, m) => sum + m.completion_percentage, 0);
    return Math.round(totalProgress / project.milestones.length);
  };

  const getOverdueMilestones = () => {
    if (!project?.milestones) return 0;
    return project.milestones.filter(m => 
      new Date(m.due_date) < new Date() && m.status !== 'completed'
    ).length;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Project Not Found</h2>
          <p className="text-gray-600 mb-4">The project could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">{project.title}</h1>
          <p className="text-gray-600 mt-1">{project.description}</p>
        </div>
      </div>

      {/* Status and Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            {getStatusIcon(project.status)}
            <div>
              <div className="flex items-center gap-3 mb-2">
                <span className={getStatusBadge(project.status)}>
                  {project.status.replace('_', ' ').toUpperCase()}
                </span>
                <span className={getQualityStatusBadge(project.quality_status)}>
                  Quality: {project.quality_status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
              <p className="text-sm text-gray-600">
                Created {new Date(project.created_at).toLocaleDateString()} •
                Last updated {new Date(project.updated_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => router.push(`/manager/projects/${projectId}/communication`)}
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              Communication
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push(`/manager/projects/${projectId}/milestones`)}
              className="flex items-center gap-2"
            >
              <Target className="h-4 w-4" />
              Milestones
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push(`/manager/projects/${projectId}/satisfaction`)}
              className="flex items-center gap-2"
            >
              <Star className="h-4 w-4" />
              Satisfaction
            </Button>
            <Button
              onClick={() => router.push(`/manager/projects/${projectId}/coordinate`)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Coordinate
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Budget</p>
              <p className="text-2xl font-bold text-green-600">${project.budget.toLocaleString()}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Progress</p>
              <p className="text-2xl font-bold text-blue-600">{getMilestoneProgress()}%</p>
              <p className="text-xs text-gray-500 mt-1">
                {project.milestones?.length || 0} milestones
              </p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Deadline</p>
              <p className="text-lg font-bold text-purple-600">
                {project.deadline ? new Date(project.deadline).toLocaleDateString() : 'Not set'}
              </p>
              {project.deadline && new Date(project.deadline) < new Date() && (
                <p className="text-xs text-red-500 mt-1">Overdue</p>
              )}
            </div>
            <Calendar className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Files</p>
              <p className="text-2xl font-bold text-amber-600">{project.files?.length || 0}</p>
              <p className="text-xs text-gray-500 mt-1">
                {getOverdueMilestones() > 0 && `${getOverdueMilestones()} overdue`}
              </p>
            </div>
            <FileText className="h-8 w-8 text-amber-500" />
          </div>
        </div>
      </div>

      {/* Team Information */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg">
            <Users className="h-10 w-10 text-blue-600" />
            <div>
              <h3 className="font-semibold text-blue-900">Client</h3>
              <p className="text-blue-800">{project.client.full_name}</p>
              <p className="text-sm text-blue-600">{project.client.email}</p>
              {project.client.phone && (
                <p className="text-sm text-blue-600">{project.client.phone}</p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg">
            <Users className="h-10 w-10 text-green-600" />
            <div>
              <h3 className="font-semibold text-green-900">Designer</h3>
              <p className="text-green-800">{project.designer.full_name}</p>
              <p className="text-sm text-green-600">{project.designer.email}</p>
              {project.designer.phone && (
                <p className="text-sm text-green-600">{project.designer.phone}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: Eye },
              { id: 'milestones', label: 'Milestones', icon: Target },
              { id: 'files', label: 'Files', icon: FileText },
              { id: 'activity', label: 'Activity', icon: Clock }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-brown-500 text-brown-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Project Description</h3>
                <p className="text-gray-700">{project.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Project Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className="font-medium">{project.status.replace('_', ' ')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Budget:</span>
                      <span className="font-medium">${project.budget.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Created:</span>
                      <span className="font-medium">{new Date(project.created_at).toLocaleDateString()}</span>
                    </div>
                    {project.deadline && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Deadline:</span>
                        <span className="font-medium">{new Date(project.deadline).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Quality Status</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Current Status:</span>
                      <span className={getQualityStatusBadge(project.quality_status)}>
                        {project.quality_status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Progress:</span>
                      <span className="font-medium">{getMilestoneProgress()}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Milestones:</span>
                      <span className="font-medium">{project.milestones?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Files:</span>
                      <span className="font-medium">{project.files?.length || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'milestones' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Project Milestones</h3>
                <Button
                  onClick={() => router.push(`/manager/projects/${projectId}/milestones`)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Manage Milestones
                </Button>
              </div>

              {project.milestones && project.milestones.length > 0 ? (
                <div className="space-y-3">
                  {project.milestones.map((milestone) => (
                    <div key={milestone.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{milestone.title}</h4>
                        <span className={getStatusBadge(milestone.status)}>
                          {milestone.status.toUpperCase()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{milestone.description}</p>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">
                          Due: {new Date(milestone.due_date).toLocaleDateString()}
                        </span>
                        <span className="font-medium">
                          {milestone.completion_percentage}% complete
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${milestone.completion_percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No milestones created yet</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'files' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Project Files</h3>
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  Upload File
                </Button>
              </div>

              {project.files && project.files.length > 0 ? (
                <div className="space-y-3">
                  {project.files.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-blue-500" />
                        <div>
                          <h4 className="font-medium text-gray-900">{file.filename}</h4>
                          <p className="text-sm text-gray-600">
                            Uploaded by {file.uploader.full_name} • {new Date(file.uploaded_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No files uploaded yet</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'activity' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>

              {project.activities && project.activities.length > 0 ? (
                <div className="space-y-3">
                  {project.activities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-4 border border-gray-200 rounded-lg">
                      <Clock className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm text-gray-900">{activity.description}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {activity.user.full_name} • {new Date(activity.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No recent activity</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
