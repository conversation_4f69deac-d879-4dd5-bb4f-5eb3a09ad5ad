"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Download,
  Eye,
  FileText,
  Image,
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Filter,
  Search,
  Calendar,
  User,
  Folder,
  ExternalLink,
  ThumbsUp,
  ThumbsDown,
  RotateCcw,
  Loader2,
  AlertCircle
} from "lucide-react";

interface Submission {
  id: string;
  project_id: string;
  designer_id: string;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'needs_revision' | 'rejected';
  revision_requested: boolean;
  feedback?: string;
  created_at: string;
  updated_at: string;
  designer: {
    full_name: string;
    email: string;
    avatar_url?: string;
  };
}

interface SubmissionFile {
  id: string;
  submission_id: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  created_at: string;
}

interface SubmissionFeedback {
  id: string;
  submission_id: string;
  content: string;
  is_approved: boolean;
  user_id: string;
  created_at: string;
  user: {
    full_name: string;
    role: string;
  };
}

interface Project {
  id: string;
  title: string;
  client: {
    full_name: string;
    email: string;
  };
}

export default function ProjectSubmissionsPage() {
  const { id: projectId } = useParams();
  const router = useRouter();
  const { user } = useOptimizedAuth();
  
  const [project, setProject] = useState<Project | null>(null);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<Submission[]>([]);
  const [submissionFiles, setSubmissionFiles] = useState<Record<string, SubmissionFile[]>>({});
  const [submissionFeedback, setSubmissionFeedback] = useState<Record<string, SubmissionFeedback[]>>({});
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackContent, setFeedbackContent] = useState('');
  const [feedbackType, setFeedbackType] = useState<'approve' | 'reject' | 'revision'>('approve');

  useEffect(() => {
    if (projectId) {
      fetchProjectData();
      fetchSubmissions();
    }
  }, [projectId]);

  useEffect(() => {
    filterSubmissions();
  }, [submissions, statusFilter, searchTerm]);

  const fetchProjectData = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          client:profiles!client_id(full_name, email)
        `)
        .eq('id', projectId)
        .single();

      if (error) throw error;
      setProject(data);
    } catch (error) {
      console.error('Error fetching project:', error);
      setError('Failed to load project data');
    }
  };

  const fetchSubmissions = async () => {
    setLoading(true);
    try {
      // Fetch submissions with designer info
      const { data: submissionsData, error: submissionsError } = await supabase
        .from('submissions')
        .select(`
          *,
          designer:profiles!designer_id(full_name, email, avatar_url)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (submissionsError) throw submissionsError;

      setSubmissions(submissionsData || []);

      // Fetch files for each submission
      if (submissionsData && submissionsData.length > 0) {
        const submissionIds = submissionsData.map(s => s.id);
        
        const { data: filesData, error: filesError } = await supabase
          .from('submission_files')
          .select('*')
          .in('submission_id', submissionIds);

        if (filesError) throw filesError;

        // Group files by submission_id
        const filesGrouped = (filesData || []).reduce((acc, file) => {
          if (!acc[file.submission_id]) {
            acc[file.submission_id] = [];
          }
          acc[file.submission_id].push(file);
          return acc;
        }, {} as Record<string, SubmissionFile[]>);

        setSubmissionFiles(filesGrouped);

        // Fetch feedback for each submission
        const { data: feedbackData, error: feedbackError } = await supabase
          .from('submission_feedback')
          .select(`
            *,
            user:profiles!user_id(full_name, role)
          `)
          .in('submission_id', submissionIds)
          .order('created_at', { ascending: true });

        if (feedbackError) throw feedbackError;

        // Group feedback by submission_id
        const feedbackGrouped = (feedbackData || []).reduce((acc, feedback) => {
          if (!acc[feedback.submission_id]) {
            acc[feedback.submission_id] = [];
          }
          acc[feedback.submission_id].push(feedback);
          return acc;
        }, {} as Record<string, SubmissionFeedback[]>);

        setSubmissionFeedback(feedbackGrouped);
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
      setError('Failed to load submissions');
    } finally {
      setLoading(false);
    }
  };

  const filterSubmissions = () => {
    let filtered = submissions;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(submission => submission.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(submission =>
        submission.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        submission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        submission.designer.full_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredSubmissions(filtered);
  };

  const handleSubmissionAction = async (submission: Submission, action: 'approve' | 'reject' | 'revision', feedback: string) => {
    setProcessing(true);
    setError(null);

    try {
      // Create feedback record
      const { error: feedbackError } = await supabase
        .from('submission_feedback')
        .insert({
          submission_id: submission.id,
          content: feedback,
          is_approved: action === 'approve',
          user_id: user?.id
        });

      if (feedbackError) throw feedbackError;

      // Update submission status
      let newStatus: string;
      switch (action) {
        case 'approve':
          newStatus = 'approved';
          break;
        case 'reject':
          newStatus = 'rejected';
          break;
        case 'revision':
          newStatus = 'needs_revision';
          break;
        default:
          newStatus = submission.status;
      }

      const { error: updateError } = await supabase
        .from('submissions')
        .update({
          status: newStatus,
          revision_requested: action === 'revision',
          feedback: feedback,
          updated_at: new Date().toISOString()
        })
        .eq('id', submission.id);

      if (updateError) throw updateError;

      // Create notification for designer
      await supabase
        .from('notifications')
        .insert({
          user_id: submission.designer_id,
          type: 'submission_feedback',
          title: `Submission ${action === 'approve' ? 'Approved' : action === 'reject' ? 'Rejected' : 'Needs Revision'}`,
          content: `Your submission "${submission.title}" has been ${action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'marked for revision'}. ${feedback}`,
          related_id: submission.id,
          read: false
        });

      setSuccess(`Submission ${action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'marked for revision'} successfully`);
      
      // Refresh submissions
      await fetchSubmissions();
      
      // Close modal
      setShowFeedbackModal(false);
      setSelectedSubmission(null);
      setFeedbackContent('');

      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error updating submission:', error);
      setError('Failed to update submission');
    } finally {
      setProcessing(false);
    }
  };

  const openFeedbackModal = (submission: Submission, type: 'approve' | 'reject' | 'revision') => {
    setSelectedSubmission(submission);
    setFeedbackType(type);
    setShowFeedbackModal(true);
    setFeedbackContent('');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'needs_revision':
        return 'bg-orange-100 text-orange-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'needs_revision':
        return <RotateCcw className="h-4 w-4 text-orange-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const downloadFile = async (file: SubmissionFile) => {
    try {
      const { data, error } = await supabase.storage
        .from('project-files')
        .download(file.file_path);

      if (error) throw error;

      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.file_name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      setError('Failed to download file');
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className="h-5 w-5 text-blue-600" />;
    } else if (fileType === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-600" />;
    } else {
      return <Folder className="h-5 w-5 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Loader2 className="animate-spin h-12 w-12 text-primary mx-auto mb-4" />
          <p className="text-gray-500">Loading project submissions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Link href={`/admin/projects/${projectId}`} className="mr-4">
            <Button variant="ghost" className="p-0 h-auto">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Project Submissions</h1>
            <p className="text-gray-500 mt-1">
              {project?.title} - {project?.client?.full_name}
            </p>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search Submissions
            </label>
            <div className="relative">
              <input
                type="text"
                id="search"
                placeholder="Search by title, description, or designer"
                className="w-full px-4 py-2 border rounded-md pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>
          
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status Filter
            </label>
            <select
              id="status"
              className="w-full px-4 py-2 border rounded-md"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Submissions</option>
              <option value="pending">Pending Review</option>
              <option value="approved">Approved</option>
              <option value="needs_revision">Needs Revision</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          
          <div className="flex items-end">
            <Button 
              variant="outline" 
              onClick={fetchSubmissions}
              className="flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
        
        <div className="mt-4">
          <p className="text-sm text-gray-500">
            Showing {filteredSubmissions.length} submissions
          </p>
        </div>
      </div>

      {/* Submissions List */}
      {filteredSubmissions.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <Folder className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-medium mb-2">No Submissions Found</h2>
          <p className="text-gray-500 mb-6">
            {submissions.length === 0 
              ? "No submissions have been made for this project yet."
              : "No submissions match your current filter criteria."
            }
          </p>
          {statusFilter !== 'all' && (
            <Button onClick={() => setStatusFilter('all')}>
              View All Submissions
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {filteredSubmissions.map((submission) => (
            <div key={submission.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Submission Header */}
              <div className="p-6 border-b">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex items-center mb-4 md:mb-0">
                    <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                      {submission.designer.avatar_url ? (
                        <img
                          src={submission.designer.avatar_url}
                          alt={submission.designer.full_name}
                          className="h-10 w-10 rounded-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">{submission.title}</h3>
                      <p className="text-sm text-gray-500">
                        by {submission.designer.full_name} • {formatDate(submission.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(submission.status)}
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}>
                      {submission.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Submission Content */}
              <div className="p-6">
                {/* Description */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Description</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">{submission.description}</p>
                </div>

                {/* Files */}
                {submissionFiles[submission.id] && submissionFiles[submission.id].length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Attached Files</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {submissionFiles[submission.id].map((file) => (
                        <div key={file.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              {getFileIcon(file.file_type)}
                              <span className="ml-2 text-sm font-medium truncate">{file.file_name}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => downloadFile(file)}
                                className="text-blue-600 hover:text-blue-700"
                                title="Download"
                              >
                                <Download className="h-4 w-4" />
                              </button>
                              {file.file_type.startsWith('image/') && (
                                <button
                                  onClick={() => {
                                    // Open image in new tab for preview
                                    supabase.storage
                                      .from('project-files')
                                      .getPublicUrl(file.file_path)
                                      .then(({ data }) => {
                                        window.open(data.publicUrl, '_blank');
                                      });
                                  }}
                                  className="text-blue-600 hover:text-blue-700"
                                  title="Preview"
                                >
                                  <Eye className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </div>
                          <p className="text-xs text-gray-500">{formatFileSize(file.file_size)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Feedback History */}
                {submissionFeedback[submission.id] && submissionFeedback[submission.id].length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Feedback History</h4>
                    <div className="space-y-3">
                      {submissionFeedback[submission.id].map((feedback) => (
                        <div key={feedback.id} className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              <span className="text-sm font-medium">{feedback.user.full_name}</span>
                              <span className="mx-2 text-gray-400">•</span>
                              <span className="text-xs text-gray-500">{formatDate(feedback.created_at)}</span>
                            </div>
                            {feedback.is_approved ? (
                              <span className="text-green-600 text-xs font-medium">APPROVED</span>
                            ) : (
                              <span className="text-red-600 text-xs font-medium">FEEDBACK</span>
                            )}
                          </div>
                          <p className="text-sm text-gray-700">{feedback.content}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end space-x-3">
                  {submission.status === 'pending' && (
                    <>
                      <Button
                        variant="outline"
                        onClick={() => openFeedbackModal(submission, 'revision')}
                        className="flex items-center text-orange-600 hover:text-orange-700 border-orange-200 hover:border-orange-300"
                        disabled={processing}
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Request Revision
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => openFeedbackModal(submission, 'reject')}
                        className="flex items-center text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                        disabled={processing}
                      >
                        <ThumbsDown className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                      <Button
                        onClick={() => openFeedbackModal(submission, 'approve')}
                        className="flex items-center"
                        disabled={processing}
                      >
                        <ThumbsUp className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                    </>
                  )}
                  
                  {submission.status === 'needs_revision' && (
                    <>
                      <Button
                        variant="outline"
                        onClick={() => openFeedbackModal(submission, 'reject')}
                        className="flex items-center text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                        disabled={processing}
                      >
                        <ThumbsDown className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                      <Button
                        onClick={() => openFeedbackModal(submission, 'approve')}
                        className="flex items-center"
                        disabled={processing}
                      >
                        <ThumbsUp className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Feedback Modal */}
      {showFeedbackModal && selectedSubmission && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {feedbackType === 'approve' ? 'Approve Submission' : 
                 feedbackType === 'reject' ? 'Reject Submission' : 'Request Revision'}
              </h2>
            </div>
            
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                {feedbackType === 'approve' 
                  ? `Are you sure you want to approve "${selectedSubmission.title}"?`
                  : feedbackType === 'reject'
                  ? `Are you sure you want to reject "${selectedSubmission.title}"?`
                  : `Request revisions for "${selectedSubmission.title}"`
                }
              </p>
              
              <div>
                <label htmlFor="feedback" className="block text-sm font-medium text-gray-700 mb-2">
                  {feedbackType === 'approve' ? 'Approval Message (Optional)' : 'Feedback Message'}
                </label>
                <textarea
                  id="feedback"
                  value={feedbackContent}
                  onChange={(e) => setFeedbackContent(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  placeholder={
                    feedbackType === 'approve' 
                      ? "Great work! This submission meets all requirements."
                      : feedbackType === 'reject'
                      ? "Please explain why this submission is being rejected..."
                      : "Please specify what changes are needed..."
                  }
                  required={feedbackType !== 'approve'}
                />
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowFeedbackModal(false);
                  setSelectedSubmission(null);
                  setFeedbackContent('');
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleSubmissionAction(selectedSubmission, feedbackType, feedbackContent)}
                disabled={processing || (feedbackType !== 'approve' && !feedbackContent.trim())}
                className={
                  feedbackType === 'approve' 
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : feedbackType === 'reject'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-orange-600 hover:bg-orange-700 text-white'
                }
              >
                {processing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  feedbackType === 'approve' ? 'Approve' : 
                  feedbackType === 'reject' ? 'Reject' : 'Request Revision'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
