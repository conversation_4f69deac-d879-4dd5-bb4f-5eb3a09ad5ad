"use client";

import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

export default function DebugAuth() {
  const { user, profile, loading } = useOptimizedAuth();
  const router = useRouter();

  const handleNavigateToClient = () => {
    router.push('/client/dashboard');
  };

  const handleNavigateWithReload = () => {
    window.location.href = '/client/dashboard';
  };

  if (loading) {
    return <div>Loading auth state...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Auth Debug Page</h1>
      
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">User Session:</h2>
          <pre className="bg-gray-100 p-2 rounded">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>

        <div>
          <h2 className="text-lg font-semibold">User Profile:</h2>
          <pre className="bg-gray-100 p-2 rounded">
            {JSON.stringify(profile, null, 2)}
          </pre>
        </div>

        <div className="space-x-4">
          <Button onClick={handleNavigateToClient}>
            Navigate to Client Dashboard (router.push)
          </Button>
          <Button onClick={handleNavigateWithReload}>
            Navigate to Client Dashboard (window.location.href)
          </Button>
        </div>
      </div>
    </div>
  );
}
