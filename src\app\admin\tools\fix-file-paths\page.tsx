"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  FileText,
  RefreshCw,
  Database
} from "lucide-react";

export default function FixFilePathsPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runMigration = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch('/api/fix-designer-file-paths', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Migration failed');
      }

      setResults(data.results);
    } catch (error) {
      console.error('Migration error:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Fix Designer File Paths</h1>
          <p className="text-gray-600">
            This tool fixes the mismatch between database file paths and actual R2 storage keys 
            for designer application files (resumes and portfolios).
          </p>
        </div>

        {/* Warning Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
          <div className="flex items-start">
            <AlertTriangle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                Important Notice
              </h3>
              <ul className="text-yellow-700 space-y-1 text-sm">
                <li>• This migration fixes file path mismatches between database and R2 storage</li>
                <li>• It will update the database to match actual file locations in R2</li>
                <li>• The process is safe and can be run multiple times</li>
                <li>• Make sure you have a database backup before running</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Migration Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Database className="h-6 w-6 text-blue-600 mr-3" />
              <div>
                <h3 className="text-lg font-semibold">Run File Path Migration</h3>
                <p className="text-gray-600 text-sm">
                  Fix mismatched file paths for all designer applications
                </p>
              </div>
            </div>
            
            <Button
              onClick={runMigration}
              disabled={loading}
              className="flex items-center"
            >
              {loading ? (
                <Loader2 className="animate-spin h-4 w-4 mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              {loading ? 'Running Migration...' : 'Run Migration'}
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div className="flex items-start">
              <XCircle className="h-6 w-6 text-red-600 mr-3 mt-0.5" />
              <div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">
                  Migration Failed
                </h3>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Results Display */}
        {results && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-start mb-4">
              <CheckCircle className="h-6 w-6 text-green-600 mr-3 mt-0.5" />
              <div>
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  Migration Completed Successfully
                </h3>
              </div>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="text-2xl font-bold text-blue-600">{results.processed}</div>
                <div className="text-sm text-gray-600">Applications Processed</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="text-2xl font-bold text-green-600">{results.fixed}</div>
                <div className="text-sm text-gray-600">Files Fixed</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="text-2xl font-bold text-red-600">{results.errors}</div>
                <div className="text-sm text-gray-600">Errors</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-green-200">
                <div className="text-2xl font-bold text-gray-600">
                  {results.processed - results.fixed - results.errors}
                </div>
                <div className="text-sm text-gray-600">No Changes Needed</div>
              </div>
            </div>

            {/* Detailed Results */}
            {results.details && results.details.length > 0 && (
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">
                  Detailed Results
                </h4>
                <div className="max-h-96 overflow-y-auto">
                  <div className="space-y-2">
                    {results.details.map((detail: any, index: number) => (
                      <div 
                        key={index} 
                        className={`p-3 rounded border text-sm ${
                          detail.status === 'fixed' 
                            ? 'bg-green-50 border-green-200' 
                            : detail.status === 'error'
                            ? 'bg-red-50 border-red-200'
                            : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium">Application: {detail.id}</span>
                          <span className={`px-2 py-1 rounded text-xs ${
                            detail.status === 'fixed' 
                              ? 'bg-green-100 text-green-800' 
                              : detail.status === 'error'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {detail.status.replace('_', ' ').toUpperCase()}
                          </span>
                        </div>
                        
                        {detail.status === 'fixed' && (
                          <div className="text-gray-600">
                            {detail.oldResumeUrl && (
                              <div>
                                <strong>Resume:</strong> {detail.oldResumeUrl} → {detail.newResumeUrl}
                              </div>
                            )}
                            {detail.oldPortfolioFiles && detail.oldPortfolioFiles.length > 0 && (
                              <div>
                                <strong>Portfolio files updated:</strong> {detail.oldPortfolioFiles.length} files
                              </div>
                            )}
                          </div>
                        )}
                        
                        {detail.status === 'error' && (
                          <div className="text-red-600">
                            <strong>Error:</strong> {detail.error}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <FileText className="h-6 w-6 text-blue-600 mr-3 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-blue-800 mb-2">
                What This Migration Does
              </h3>
              <ul className="text-blue-700 space-y-1 text-sm">
                <li>• Scans all designer applications with file uploads</li>
                <li>• Compares database file paths with actual R2 storage keys</li>
                <li>• Updates database paths to match the correct R2 file locations</li>
                <li>• Handles filename differences (spaces vs hyphens, timestamps, etc.)</li>
                <li>• Provides detailed results of all changes made</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
