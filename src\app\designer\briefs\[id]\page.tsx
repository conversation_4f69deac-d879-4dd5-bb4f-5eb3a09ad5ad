"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useParams, useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  MapPin,
  Clock,
  User,
  FileText,
  AlertCircle,
  CheckCircle,
  Briefcase,
  Target,
  Palette,
  MessageSquare,
  Eye,
  Building
} from "lucide-react";

interface ProjectBrief {
  id: string;
  title: string;
  description: string;
  requirements: string;
  preferred_style: string;
  budget_range: string;
  timeline_preference: string;
  location: string;
  project_type: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'assigned' | 'proposal_received' | 'accepted' | 'rejected';
  assigned_designer_id: string | null;
  client_id: string;
  client_name: string;
  client_avatar: string | null;

  created_at: string;
  updated_at: string;
}

export default function DesignerBriefDetails() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const [brief, setBrief] = useState<ProjectBrief | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user && params.id) {
      fetchBriefDetails();
    }
  }, [user, params.id]);

  const fetchBriefDetails = async () => {
    if (!user || !params.id) return;

    try {
      const { data: briefData, error: briefError } = await supabase
        .from('project_briefs')
        .select(`
          *,
          profiles!project_briefs_client_id_fkey(full_name, avatar_url)
        `)
        .eq('id', params.id)
        .or(`assigned_designer_id.eq.${user.id},and(assigned_designer_id.is.null,status.eq.pending)`)
        .single();

      if (briefError) throw briefError;

      const profile = Array.isArray(briefData.profiles) ? briefData.profiles[0] : briefData.profiles;

      setBrief({
        ...briefData,
        client_name: profile?.full_name || 'Unknown Client',
        client_avatar: profile?.avatar_url || null
      });
    } catch (error) {
      console.error('Error fetching brief details:', error);
      setError('Failed to load brief details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'proposal_received':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'assigned':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBudgetDisplay = (budgetRange: string) => {
    return budgetRange.replace(/_/g, ' - $').replace('k', 'K');
  };

  const isAssignedToMe = brief?.assigned_designer_id === user?.id;
  const isPlatformBrief = !brief?.assigned_designer_id && brief?.status === 'pending';

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (error || !brief) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <p className="text-red-700">{error || 'Brief not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/designer/briefs">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Briefs
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{brief.title}</h1>
            <p className="text-gray-600">Project Brief Details</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Link href={`/designer/messages?otherUserId=${brief.client_id}&type=direct`}>
            <Button variant="outline">
              <MessageSquare className="h-4 w-4 mr-2" />
              Message Client
            </Button>
          </Link>
          <Link href={`/designer/briefs/${brief.id}/proposal`}>
            <Button className="bg-brown-600 hover:bg-brown-700 text-white">
              <FileText className="h-4 w-4 mr-2" />
              Create Proposal
            </Button>
          </Link>
        </div>
      </div>

      {/* Status and Assignment Info */}
      <div className="flex items-center space-x-4">
        <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getStatusColor(brief.status)}`}>
          {brief.status.replace('_', ' ').toUpperCase()}
        </span>
        <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getUrgencyColor(brief.urgency)}`}>
          {brief.urgency.toUpperCase()} PRIORITY
        </span>
        {isAssignedToMe && (
          <span className="px-3 py-1 text-sm font-medium rounded-full bg-blue-50 text-blue-700 border border-blue-200">
            ASSIGNED TO YOU
          </span>
        )}
        {isPlatformBrief && (
          <span className="px-3 py-1 text-sm font-medium rounded-full bg-yellow-50 text-yellow-700 border border-yellow-200">
            PLATFORM ASSIGNMENT
          </span>
        )}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Brief Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Description</h3>
            <p className="text-gray-700 leading-relaxed">{brief.description}</p>
          </motion.div>

          {/* Requirements */}
          {brief.requirements && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Specific Requirements</h3>
              <p className="text-gray-700 leading-relaxed">{brief.requirements}</p>
            </motion.div>
          )}

          {/* Style Preferences */}
          {brief.preferred_style && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Style Preferences</h3>
              <p className="text-gray-700 leading-relaxed">{brief.preferred_style}</p>
            </motion.div>
          )}
        </div>

        {/* Right Column - Brief Info & Client */}
        <div className="space-y-6">
          {/* Client Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Client Information</h3>
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex-shrink-0">
                {brief.client_avatar ? (
                  <img
                    src={brief.client_avatar}
                    alt={brief.client_name}
                    className="h-12 w-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="h-6 w-6 text-gray-500" />
                  </div>
                )}
              </div>
              <div>
                <p className="font-medium text-gray-900">{brief.client_name}</p>
              </div>
            </div>
            <Link href={`/designer/messages?otherUserId=${brief.client_id}&type=direct`}>
              <Button size="sm" variant="outline" className="w-full">
                <MessageSquare className="h-4 w-4 mr-2" />
                Message Client
              </Button>
            </Link>
          </motion.div>

          {/* Project Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Information</h3>
            <div className="space-y-4">
              <div className="flex items-center text-sm">
                <DollarSign className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Budget:</span>
                <span className="ml-2 font-medium">{getBudgetDisplay(brief.budget_range)}</span>
              </div>
              <div className="flex items-center text-sm">
                <Clock className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Timeline:</span>
                <span className="ml-2 font-medium">{brief.timeline_preference.replace(/_/g, ' ')}</span>
              </div>
              <div className="flex items-center text-sm">
                <MapPin className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Location:</span>
                <span className="ml-2 font-medium">{brief.location}</span>
              </div>
              <div className="flex items-center text-sm">
                <Target className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Type:</span>
                <span className="ml-2 font-medium">{brief.project_type.replace(/_/g, ' ')}</span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Posted:</span>
                <span className="ml-2 font-medium">{formatDate(brief.created_at)}</span>
              </div>
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link href={`/designer/briefs/${brief.id}/proposal`}>
                <Button size="sm" className="w-full justify-start bg-brown-600 hover:bg-brown-700 text-white">
                  <FileText className="h-4 w-4 mr-3" />
                  Create Proposal
                </Button>
              </Link>
              <Link href={`/designer/messages?otherUserId=${brief.client_id}&type=direct`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 mr-3" />
                  Ask Questions
                </Button>
              </Link>
              <Link href="/designer/briefs">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Briefcase className="h-4 w-4 mr-3" />
                  View Other Briefs
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
