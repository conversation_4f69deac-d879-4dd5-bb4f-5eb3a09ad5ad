/**
 * Centralized Query Key Management for Messaging System
 * This ensures consistent cache invalidation across all messaging components
 */

// Centralized Messaging Query Keys
export const messagingKeys = {
  // Root messaging key
  all: ['messaging'] as const,
  
  // Conversations
  conversations: (userId: string) => [...messagingKeys.all, 'conversations', userId] as const,
  conversation: (conversationId: string) => [...messagingKeys.all, 'conversation', conversationId] as const,
  
  // Messages - standardized across all components
  messages: (conversationId: string) => [...messagingKeys.all, 'messages', conversationId] as const,
  
  // Project-specific messages
  projectMessages: (projectId: string) => [...messagingKeys.all, 'project-messages', projectId] as const,
  
  // Admin messages
  adminMessages: (userId: string) => [...messagingKeys.all, 'admin-messages', userId] as const,
  
  // Live chat
  liveChat: (sessionId: string) => [...messagingKeys.all, 'live-chat', sessionId] as const,
  liveChatSessions: () => [...messagingKeys.all, 'live-chat-sessions'] as const,
  
  // Dispute messages
  disputeMessages: (disputeId: string) => [...messagingKeys.all, 'dispute-messages', disputeId] as const,
  
  // Message attachments
  messageAttachments: (messageId: string) => [...messagingKeys.all, 'attachments', messageId] as const,
  
  // Read status
  readStatus: (conversationId: string, userId: string) => [...messagingKeys.all, 'read-status', conversationId, userId] as const,
  
  // Unread counts
  unreadCount: (userId: string) => [...messagingKeys.all, 'unread-count', userId] as const,
} as const;

// Cache invalidation helpers
export const messagingCacheUtils = {
  /**
   * Invalidate all messaging-related queries for a user
   */
  invalidateAllForUser: (queryClient: any, userId: string) => {
    queryClient.invalidateQueries({ queryKey: messagingKeys.conversations(userId) });
    queryClient.invalidateQueries({ queryKey: messagingKeys.adminMessages(userId) });
    queryClient.invalidateQueries({ queryKey: messagingKeys.unreadCount(userId) });
  },

  /**
   * Invalidate conversation and its messages
   */
  invalidateConversation: (queryClient: any, conversationId: string, userId: string) => {
    queryClient.invalidateQueries({ queryKey: messagingKeys.messages(conversationId) });
    queryClient.invalidateQueries({ queryKey: messagingKeys.conversation(conversationId) });
    queryClient.invalidateQueries({ queryKey: messagingKeys.conversations(userId) });
  },

  /**
   * Invalidate project messages
   */
  invalidateProjectMessages: (queryClient: any, projectId: string) => {
    queryClient.invalidateQueries({ queryKey: messagingKeys.projectMessages(projectId) });
  },

  /**
   * Optimistic update for new message
   */
  addOptimisticMessage: (queryClient: any, conversationId: string, message: any) => {
    const queryKey = messagingKeys.messages(conversationId);
    
    queryClient.setQueryData(queryKey, (oldMessages: any[] = []) => {
      const optimisticMessage = {
        ...message,
        id: `temp_${Date.now()}`,
        created_at: new Date().toISOString(),
        sending: true
      };
      
      return [...oldMessages, optimisticMessage];
    });

    // Return function to remove optimistic message
    return () => {
      queryClient.setQueryData(queryKey, (oldMessages: any[] = []) => {
        return oldMessages.filter((msg: any) => !msg.id?.startsWith('temp_'));
      });
    };
  },

  /**
   * Update optimistic message with real data
   */
  updateOptimisticMessage: (queryClient: any, conversationId: string, tempId: string, realMessage: any) => {
    const queryKey = messagingKeys.messages(conversationId);
    
    queryClient.setQueryData(queryKey, (oldMessages: any[] = []) => {
      return oldMessages.map((msg: any) => 
        msg.id === tempId ? { ...realMessage, sending: false } : msg
      );
    });
  },

  /**
   * Immediate cache refresh after message send
   */
  refreshAfterSend: async (queryClient: any, conversationId: string, userId: string) => {
    // Invalidate and immediately refetch
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: messagingKeys.messages(conversationId) }),
      queryClient.invalidateQueries({ queryKey: messagingKeys.conversations(userId) }),
    ]);
    
    // Force immediate refetch
    await Promise.all([
      queryClient.refetchQueries({ queryKey: messagingKeys.messages(conversationId) }),
      queryClient.refetchQueries({ queryKey: messagingKeys.conversations(userId) }),
    ]);
  }
};

// Optimized query configurations
export const messagingQueryConfig = {
  // Conversations - less frequent updates
  conversations: {
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchInterval: 30000, // 30 seconds
  },
  
  // Messages - more frequent updates for real-time feel
  messages: {
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5000, // 5 seconds
  },
  
  // Project messages - moderate frequency
  projectMessages: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 15000, // 15 seconds
  },
  
  // Admin messages - less frequent
  adminMessages: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchInterval: 60000, // 1 minute
  },
  
  // Live chat - most frequent
  liveChat: {
    staleTime: 10 * 1000, // 10 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 2000, // 2 seconds
  }
};

// Type definitions for better TypeScript support
export type MessagingQueryKey = ReturnType<typeof messagingKeys[keyof typeof messagingKeys]>;
export type MessagingCacheUtils = typeof messagingCacheUtils;
export type MessagingQueryConfig = typeof messagingQueryConfig;
