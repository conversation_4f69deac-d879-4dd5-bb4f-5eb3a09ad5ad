/**
 * Utility functions for environment-specific operations
 */

/**
 * Get the appropriate base URL based on the current environment
 * @returns The base URL for the current environment
 */
export function getBaseUrl(): string {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    // In development, use the current window location
    if (process.env.NODE_ENV === 'development') {
      return window.location.origin;
    }
    
    // In production, use the production domain
    return 'https://seniorsarchifirm.com';
  }
  
  // Server-side rendering - determine environment from env vars
  if (process.env.NODE_ENV === 'development') {
    // Use localhost with the appropriate port for development
    // Default to 3000 if PORT is not specified
    const port = process.env.PORT || 3000;
    return `http://localhost:${port}`;
  }
  
  // Default to production URL for server-side rendering in production
  return 'https://seniorsarchifirm.com';
}

/**
 * Generate a full URL for an invitation link
 * @param inviteCode The invitation code
 * @returns The full URL for the invitation
 */
export function generateInviteUrl(inviteCode: string): string {
  const baseUrl = getBaseUrl();
  return `${baseUrl}/auth/signup?invite=${inviteCode}`;
}
