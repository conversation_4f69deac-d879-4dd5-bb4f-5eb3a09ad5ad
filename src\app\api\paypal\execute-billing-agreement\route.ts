import { NextResponse } from 'next/server';
import { executeBillingAgreement, getBillingAgreement, savePayPalAccount } from '@/lib/paypal';

export async function POST(request: Request) {
  try {
    // Get request data
    const { token, userId, makeDefault } = await request.json();

    // Validate required fields
    if (!token || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Execute the billing agreement
    const executedAgreement = await executeBillingAgreement(token);
    
    // Get the agreement ID
    const agreementId = executedAgreement.id;
    
    if (!agreementId) {
      throw new Error('Agreement ID not found in PayPal response');
    }
    
    // Get the agreement details to extract the payer email
    const agreementDetails = await getBillingAgreement(agreementId);
    
    // Extract the payer email
    const payerEmail = agreementDetails.payer.payer_info.email;
    
    if (!payerEmail) {
      throw new Error('Payer email not found in PayPal response');
    }
    
    // Save the PayPal account to the database
    await savePayPalAccount(userId, payerEmail, agreementId, makeDefault || false);
    
    // Return success response
    return NextResponse.json({
      success: true,
      agreementId,
      payerEmail
    });
  } catch (error: unknown) {
    console.error('Error executing PayPal billing agreement:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
