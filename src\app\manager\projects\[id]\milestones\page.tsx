"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Target,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  BarChart3,
  Save,
  X,
  RefreshCw,
  Flag,
  TrendingUp
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  description: string;
  deadline: string | null;
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  due_date: string;
  status: string;
  completion_percentage: number;
  priority: string;
  created_at: string;
  updated_at: string;
}

export default function ProjectMilestonesPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingMilestone, setEditingMilestone] = useState<string | null>(null);
  const [showNewMilestoneForm, setShowNewMilestoneForm] = useState(false);
  
  const [newMilestone, setNewMilestone] = useState({
    title: '',
    description: '',
    due_date: '',
    priority: 'normal',
    completion_percentage: 0
  });

  useEffect(() => {
    if (user && profile?.role === 'manager' && projectId) {
      fetchProjectData();
    }
  }, [user, profile, projectId]);

  const fetchProjectData = async () => {
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('id, title, description, deadline')
        .eq('id', projectId)
        .eq('manager_id', user?.id)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // Fetch milestones
      const { data: milestonesData, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('*')
        .eq('project_id', projectId)
        .order('due_date', { ascending: true });

      if (milestonesError) throw milestonesError;
      setMilestones(milestonesData || []);

    } catch (error) {
      console.error('Error fetching project data:', error);
      router.push('/manager/projects');
    } finally {
      setLoading(false);
    }
  };

  const createMilestone = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMilestone.title.trim() || !newMilestone.due_date) return;

    try {
      const { error } = await supabase
        .from('project_milestones')
        .insert({
          project_id: projectId,
          title: newMilestone.title.trim(),
          description: newMilestone.description.trim(),
          due_date: newMilestone.due_date,
          priority: newMilestone.priority,
          completion_percentage: newMilestone.completion_percentage,
          status: 'pending'
        });

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: projectId,
        activity_type: 'milestone_created',
        description: `Created milestone: ${newMilestone.title}`,
        outcome: 'milestone_created'
      });

      setNewMilestone({
        title: '',
        description: '',
        due_date: '',
        priority: 'normal',
        completion_percentage: 0
      });
      setShowNewMilestoneForm(false);
      fetchProjectData();
    } catch (error) {
      console.error('Error creating milestone:', error);
    }
  };

  const updateMilestone = async (milestoneId: string, updates: Partial<Milestone>) => {
    try {
      const { error } = await supabase
        .from('project_milestones')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', milestoneId);

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: projectId,
        activity_type: 'milestone_updated',
        description: `Updated milestone`,
        outcome: 'milestone_updated'
      });

      fetchProjectData();
    } catch (error) {
      console.error('Error updating milestone:', error);
    }
  };

  const deleteMilestone = async (milestoneId: string) => {
    if (!confirm('Are you sure you want to delete this milestone?')) return;

    try {
      const { error } = await supabase
        .from('project_milestones')
        .delete()
        .eq('id', milestoneId);

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: projectId,
        activity_type: 'milestone_deleted',
        description: 'Deleted milestone',
        outcome: 'milestone_deleted'
      });

      fetchProjectData();
    } catch (error) {
      console.error('Error deleting milestone:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'overdue':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Target className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'in_progress':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'overdue':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'pending':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getOverallProgress = () => {
    if (milestones.length === 0) return 0;
    const totalProgress = milestones.reduce((sum, m) => sum + m.completion_percentage, 0);
    return Math.round(totalProgress / milestones.length);
  };

  const getCompletedMilestones = () => {
    return milestones.filter(m => m.status === 'completed').length;
  };

  const getOverdueMilestones = () => {
    return milestones.filter(m => 
      new Date(m.due_date) < new Date() && m.status !== 'completed'
    ).length;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Project Not Found</h2>
          <p className="text-gray-600 mb-4">The project could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <Target className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Project Milestones</h1>
            <p className="text-gray-600 mt-1">{project.title}</p>
          </div>
        </div>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Progress</p>
              <p className="text-2xl font-bold text-blue-600">{getOverallProgress()}%</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Milestones</p>
              <p className="text-2xl font-bold text-purple-600">{milestones.length}</p>
            </div>
            <Target className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{getCompletedMilestones()}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-red-600">{getOverdueMilestones()}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Milestones</h2>
        <Button
          onClick={() => setShowNewMilestoneForm(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Milestone
        </Button>
      </div>

      {/* New Milestone Form */}
      {showNewMilestoneForm && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Milestone</h3>
          <form onSubmit={createMilestone} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  value={newMilestone.title}
                  onChange={(e) => setNewMilestone(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Due Date *
                </label>
                <input
                  type="date"
                  value={newMilestone.due_date}
                  onChange={(e) => setNewMilestone(prev => ({ ...prev, due_date: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={newMilestone.description}
                onChange={(e) => setNewMilestone(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority
                </label>
                <select
                  value={newMilestone.priority}
                  onChange={(e) => setNewMilestone(prev => ({ ...prev, priority: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                >
                  <option value="low">Low</option>
                  <option value="normal">Normal</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Initial Progress (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={newMilestone.completion_percentage}
                  onChange={(e) => setNewMilestone(prev => ({ ...prev, completion_percentage: Number(e.target.value) }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                />
              </div>
            </div>

            <div className="flex gap-3">
              <Button type="submit" className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                Create Milestone
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowNewMilestoneForm(false)}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Milestones List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6">
          {milestones.length === 0 ? (
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No milestones created yet</p>
              <Button
                onClick={() => setShowNewMilestoneForm(true)}
                className="mt-4 flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create First Milestone
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {milestones.map((milestone) => (
                <div key={milestone.id} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(milestone.status)}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{milestone.title}</h3>
                        <p className="text-gray-600 mt-1">{milestone.description}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className={getStatusBadge(milestone.status)}>
                        {milestone.status.replace('_', ' ').toUpperCase()}
                      </span>
                      <span className={getPriorityBadge(milestone.priority)}>
                        {milestone.priority.toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>Due: {new Date(milestone.due_date).toLocaleDateString()}</span>
                      {new Date(milestone.due_date) < new Date() && milestone.status !== 'completed' && (
                        <span className="text-red-600 font-medium">(Overdue)</span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span>Created: {new Date(milestone.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      <span>Progress: {milestone.completion_percentage}%</span>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-700">Completion Progress</span>
                      <span className="text-sm font-semibold text-blue-600">{milestone.completion_percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${milestone.completion_percentage}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newProgress = prompt('Enter completion percentage (0-100):', milestone.completion_percentage.toString());
                        if (newProgress !== null) {
                          const progress = Math.max(0, Math.min(100, Number(newProgress)));
                          const newStatus = progress === 100 ? 'completed' : progress > 0 ? 'in_progress' : 'pending';
                          updateMilestone(milestone.id, {
                            completion_percentage: progress,
                            status: newStatus
                          });
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Update Progress
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newStatus = milestone.status === 'completed' ? 'in_progress' : 'completed';
                        const newProgress = newStatus === 'completed' ? 100 : milestone.completion_percentage;
                        updateMilestone(milestone.id, {
                          status: newStatus,
                          completion_percentage: newProgress
                        });
                      }}
                      className={`flex items-center gap-2 ${
                        milestone.status === 'completed'
                          ? 'text-amber-600 border-amber-200 hover:bg-amber-50'
                          : 'text-green-600 border-green-200 hover:bg-green-50'
                      }`}
                    >
                      <CheckCircle className="h-4 w-4" />
                      {milestone.status === 'completed' ? 'Mark Incomplete' : 'Mark Complete'}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteMilestone(milestone.id)}
                      className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Guidelines */}
      <div className="bg-green-50 rounded-xl p-6 border border-green-200">
        <div className="flex items-start gap-3">
          <Flag className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-green-900 mb-2">Milestone Management Best Practices</h3>
            <div className="text-green-800 space-y-2">
              <p>• <strong>Clear Objectives:</strong> Define specific, measurable goals for each milestone</p>
              <p>• <strong>Realistic Timelines:</strong> Set achievable deadlines with buffer time</p>
              <p>• <strong>Regular Updates:</strong> Track progress weekly and update completion percentages</p>
              <p>• <strong>Priority Management:</strong> Focus on high-priority milestones first</p>
              <p>• <strong>Team Communication:</strong> Keep all stakeholders informed of milestone progress</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
