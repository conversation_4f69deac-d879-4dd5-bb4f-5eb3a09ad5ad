"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { 
  Save, 
  AlertCircle, 
  CheckCircle, 
  Globe, 
  Mail, 
  Bell, 
  CreditCard,
  Shield,
  FileText,
  Database
} from "lucide-react";

export default function AdminSettings() {
  const { user } = useOptimizedAuth();
  const [activeTab, setActiveTab] = useState("general");
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // General Settings
  const [generalSettings, setGeneralSettings] = useState({
    site_name: "Seniors Architecture Firm",
    site_description: "Premium architectural design services",
    contact_email: "<EMAIL>",
    support_email: "<EMAIL>",
    enable_public_projects: true,
    enable_designer_applications: true
  });
  
  // Notification Settings
  const [notificationSettings, setNotificationSettings] = useState({
    email_notifications: true,
    admin_new_user: true,
    admin_new_project: true,
    admin_payment_received: true,
    admin_payment_failed: true,
    admin_designer_application: true
  });
  
  // Payment Settings - FIXED: Consistent with other settings
  const [paymentSettings, setPaymentSettings] = useState({
    platform_fee_percentage: 15, // FIXED: Was 10, now 15 for consistency
    minimum_project_amount: 500,
    default_payment_terms: 30,
    enable_automatic_payouts: true,
    payout_schedule: "weekly",
    require_milestone_approval: true
  });

  const handleGeneralChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setGeneralSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setNotificationSettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handlePaymentChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setPaymentSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' 
          ? parseFloat(value) 
          : value
    }));
  };

  const saveSettings = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      // In a real implementation, you would save these settings to your database
      // For now, we'll just simulate a successful save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess("Settings saved successfully");
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error: Error | unknown) {
      console.error('Error saving settings:', error);
      setError(error instanceof Error ? error.message : 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">System Settings</h1>
        <p className="text-gray-500">Configure global application settings</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="flex border-b">
          <button
            className={`px-6 py-4 font-medium text-sm focus:outline-none ${
              activeTab === "general" 
                ? "border-b-2 border-primary text-primary" 
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("general")}
          >
            General
          </button>
          <button
            className={`px-6 py-4 font-medium text-sm focus:outline-none ${
              activeTab === "notifications" 
                ? "border-b-2 border-primary text-primary" 
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("notifications")}
          >
            Notifications
          </button>
          <button
            className={`px-6 py-4 font-medium text-sm focus:outline-none ${
              activeTab === "payments" 
                ? "border-b-2 border-primary text-primary" 
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("payments")}
          >
            Payments
          </button>
          <button
            className={`px-6 py-4 font-medium text-sm focus:outline-none ${
              activeTab === "security" 
                ? "border-b-2 border-primary text-primary" 
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("security")}
          >
            Security
          </button>
        </div>

        <div className="p-6">
          {/* General Settings */}
          {activeTab === "general" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium mb-4 flex items-center">
                  <Globe className="h-5 w-5 mr-2 text-gray-400" />
                  General Settings
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="site_name" className="block text-sm font-medium text-gray-700 mb-1">
                      Site Name
                    </label>
                    <input
                      type="text"
                      id="site_name"
                      name="site_name"
                      value={generalSettings.site_name}
                      onChange={handleGeneralChange}
                      className="w-full px-4 py-2 border rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="site_description" className="block text-sm font-medium text-gray-700 mb-1">
                      Site Description
                    </label>
                    <input
                      type="text"
                      id="site_description"
                      name="site_description"
                      value={generalSettings.site_description}
                      onChange={handleGeneralChange}
                      className="w-full px-4 py-2 border rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-1">
                      Contact Email
                    </label>
                    <input
                      type="email"
                      id="contact_email"
                      name="contact_email"
                      value={generalSettings.contact_email}
                      onChange={handleGeneralChange}
                      className="w-full px-4 py-2 border rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="support_email" className="block text-sm font-medium text-gray-700 mb-1">
                      Support Email
                    </label>
                    <input
                      type="email"
                      id="support_email"
                      name="support_email"
                      value={generalSettings.support_email}
                      onChange={handleGeneralChange}
                      className="w-full px-4 py-2 border rounded-md"
                    />
                  </div>
                </div>
                
                <div className="mt-6 space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="enable_public_projects"
                      name="enable_public_projects"
                      checked={generalSettings.enable_public_projects}
                      onChange={handleGeneralChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="enable_public_projects" className="ml-2 block text-sm text-gray-700">
                      Enable public project showcase
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="enable_designer_applications"
                      name="enable_designer_applications"
                      checked={generalSettings.enable_designer_applications}
                      onChange={handleGeneralChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="enable_designer_applications" className="ml-2 block text-sm text-gray-700">
                      Enable designer applications
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notification Settings */}
          {activeTab === "notifications" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium mb-4 flex items-center">
                  <Bell className="h-5 w-5 mr-2 text-gray-400" />
                  Notification Settings
                </h2>
                
                <div className="mb-6">
                  <div className="flex items-center mb-4">
                    <input
                      type="checkbox"
                      id="email_notifications"
                      name="email_notifications"
                      checked={notificationSettings.email_notifications}
                      onChange={handleNotificationChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="email_notifications" className="ml-2 block text-sm font-medium text-gray-700">
                      Enable email notifications
                    </label>
                  </div>
                </div>
                
                <h3 className="text-md font-medium mb-3">Admin Notifications</h3>
                <div className="space-y-4 ml-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="admin_new_user"
                      name="admin_new_user"
                      checked={notificationSettings.admin_new_user}
                      onChange={handleNotificationChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="admin_new_user" className="ml-2 block text-sm text-gray-700">
                      New user registration
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="admin_new_project"
                      name="admin_new_project"
                      checked={notificationSettings.admin_new_project}
                      onChange={handleNotificationChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="admin_new_project" className="ml-2 block text-sm text-gray-700">
                      New project creation
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="admin_payment_received"
                      name="admin_payment_received"
                      checked={notificationSettings.admin_payment_received}
                      onChange={handleNotificationChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="admin_payment_received" className="ml-2 block text-sm text-gray-700">
                      Payment received
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="admin_payment_failed"
                      name="admin_payment_failed"
                      checked={notificationSettings.admin_payment_failed}
                      onChange={handleNotificationChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="admin_payment_failed" className="ml-2 block text-sm text-gray-700">
                      Payment failed
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="admin_designer_application"
                      name="admin_designer_application"
                      checked={notificationSettings.admin_designer_application}
                      onChange={handleNotificationChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="admin_designer_application" className="ml-2 block text-sm text-gray-700">
                      New designer application
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Payment Settings */}
          {activeTab === "payments" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium mb-4 flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-gray-400" />
                  Payment Settings
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="platform_fee_percentage" className="block text-sm font-medium text-gray-700 mb-1">
                      Platform Fee Percentage
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="platform_fee_percentage"
                        name="platform_fee_percentage"
                        value={paymentSettings.platform_fee_percentage}
                        onChange={handlePaymentChange}
                        min="0"
                        max="100"
                        className="w-full px-4 py-2 border rounded-md pr-8"
                      />
                      <span className="absolute right-3 top-2 text-gray-500">%</span>
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="minimum_project_amount" className="block text-sm font-medium text-gray-700 mb-1">
                      Minimum Project Amount
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-2 text-gray-500">$</span>
                      <input
                        type="number"
                        id="minimum_project_amount"
                        name="minimum_project_amount"
                        value={paymentSettings.minimum_project_amount}
                        onChange={handlePaymentChange}
                        min="0"
                        className="w-full px-4 py-2 border rounded-md pl-8"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="default_payment_terms" className="block text-sm font-medium text-gray-700 mb-1">
                      Default Payment Terms (days)
                    </label>
                    <input
                      type="number"
                      id="default_payment_terms"
                      name="default_payment_terms"
                      value={paymentSettings.default_payment_terms}
                      onChange={handlePaymentChange}
                      min="1"
                      className="w-full px-4 py-2 border rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="payout_schedule" className="block text-sm font-medium text-gray-700 mb-1">
                      Payout Schedule
                    </label>
                    <select
                      id="payout_schedule"
                      name="payout_schedule"
                      value={paymentSettings.payout_schedule}
                      onChange={handlePaymentChange}
                      className="w-full px-4 py-2 border rounded-md"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="biweekly">Bi-weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                </div>
                
                <div className="mt-6 space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="enable_automatic_payouts"
                      name="enable_automatic_payouts"
                      checked={paymentSettings.enable_automatic_payouts}
                      onChange={handlePaymentChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="enable_automatic_payouts" className="ml-2 block text-sm text-gray-700">
                      Enable automatic payouts to designers
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="require_milestone_approval"
                      name="require_milestone_approval"
                      checked={paymentSettings.require_milestone_approval}
                      onChange={handlePaymentChange}
                      className="h-4 w-4 text-primary border-gray-300 rounded"
                    />
                    <label htmlFor="require_milestone_approval" className="ml-2 block text-sm text-gray-700">
                      Require admin approval for milestone completion
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Security Settings */}
          {activeTab === "security" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium mb-4 flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-gray-400" />
                  Security Settings
                </h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-md font-medium mb-3">Authentication</h3>
                    <div className="space-y-4 ml-2">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="enable_2fa"
                          checked={true}
                          readOnly
                          className="h-4 w-4 text-primary border-gray-300 rounded"
                        />
                        <label htmlFor="enable_2fa" className="ml-2 block text-sm text-gray-700">
                          Require two-factor authentication for admins (enabled by default)
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="password_expiry"
                          checked={true}
                          readOnly
                          className="h-4 w-4 text-primary border-gray-300 rounded"
                        />
                        <label htmlFor="password_expiry" className="ml-2 block text-sm text-gray-700">
                          Enable password expiry (90 days) (enabled by default)
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-md font-medium mb-3">Session Management</h3>
                    <div className="space-y-4 ml-2">
                      <div>
                        <label htmlFor="session_timeout" className="block text-sm text-gray-700 mb-1">
                          Session timeout (minutes)
                        </label>
                        <input
                          type="number"
                          id="session_timeout"
                          value={60}
                          min="5"
                          readOnly
                          className="w-full md:w-1/3 px-4 py-2 border rounded-md bg-gray-50"
                        />
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="force_logout"
                          checked={true}
                          readOnly
                          className="h-4 w-4 text-primary border-gray-300 rounded"
                        />
                        <label htmlFor="force_logout" className="ml-2 block text-sm text-gray-700">
                          Force logout on password change (enabled by default)
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-md font-medium mb-3">Data Protection</h3>
                    <div className="space-y-4 ml-2">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="data_encryption"
                          checked={true}
                          disabled
                          className="h-4 w-4 text-primary border-gray-300 rounded"
                        />
                        <label htmlFor="data_encryption" className="ml-2 block text-sm text-gray-700">
                          Enable data encryption (required)
                        </label>
                      </div>
                      
                      <div>
                        <label htmlFor="data_retention" className="block text-sm text-gray-700 mb-1">
                          Data retention period (months)
                        </label>
                        <input
                          type="number"
                          id="data_retention"
                          value={36}
                          min="1"
                          readOnly
                          className="w-full md:w-1/3 px-4 py-2 border rounded-md bg-gray-50"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-8 flex justify-end">
            <Button 
              onClick={saveSettings} 
              disabled={saving} 
              className="flex items-center"
            >
              {saving ? (
                <span className="flex items-center">
                  <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                  Saving...
                </span>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
