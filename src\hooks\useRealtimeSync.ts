"use client";

import { useEffect, useCallback, useRef, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { dashboardKeys } from '@/hooks/useDashboardData';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { RealtimeChannel } from '@supabase/supabase-js';

interface RealtimeConfig {
  enabled: boolean;
  tables: string[];
  priority: 'high' | 'medium' | 'low';
}

const DEFAULT_CONFIG: RealtimeConfig = {
  enabled: true,
  tables: ['projects', 'proposals', 'conversations', 'conversation_messages', 'notifications'],
  priority: 'medium'
};

export function useRealtimeSync(config: Partial<RealtimeConfig> = {}) {
  const queryClient = useQueryClient();
  const { user, profile } = useOptimizedAuth();
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const channelsRef = useRef<Map<string, RealtimeChannel>>(new Map());

  // Enhanced channel setup with better error handling and presence
  const setupChannel = useCallback((table: string, filter?: string) => {
    if (!user?.id) return null;

    const channelKey = `${table}_${filter || 'all'}`;
    if (channelsRef.current.has(channelKey)) {
      return channelsRef.current.get(channelKey);
    }

    console.log(`Setting up realtime channel for ${table}${filter ? ` with filter: ${filter}` : ''}`);
    
    const channel = supabase.channel(`realtime_${channelKey}`, {
      config: {
        broadcast: { self: true },
        presence: { key: user.id }
      }
    });

    // Set up channel event handlers
    channel
      .on('presence', { event: 'sync' }, () => {
        console.log(`Presence sync for ${table}`);
        setConnectionState('connected');
      })
      .on('presence', { event: 'join' }, () => {
        console.log(`Joined presence for ${table}`);
        setConnectionState('connected');
      })
      .on('presence', { event: 'leave' }, () => {
        console.log(`Left presence for ${table}`);
        if (channelsRef.current.size === 0) {
          setConnectionState('disconnected');
        }
      })
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table, filter },
        (payload) => {
          console.log(`Realtime update for ${table}:`, payload);
          
          // Invalidate queries based on the table
          queryClient.invalidateQueries({ queryKey: [table] });
          
          // Additional invalidations for related data
          if (['projects', 'proposals'].includes(table)) {
            queryClient.invalidateQueries({ 
              queryKey: dashboardKeys.batchedStats(user.id, profile?.role || '')
            });
          }
        }
      );

    // Subscribe to the channel with automatic reconnection
    channel.subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        console.log(`✅ Successfully subscribed to ${table}`);
        channelsRef.current.set(channelKey, channel);
        setConnectionState('connected');
      } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
        console.log(`❌ Channel ${status} for ${table}, attempting reconnection...`);
        channelsRef.current.delete(channelKey);
        // Implement reconnection with exponential backoff
        setTimeout(() => {
          console.log(`Attempting to reconnect to ${table}...`);
          setupChannel(table, filter);
        }, 2000);
      }
    });

    return channel;
  }, [user, profile, queryClient]);

  const triggerManualSync = useCallback((tables: string[]) => {
    if (!user?.id || !profile) {
      console.log('Cannot sync - user or profile not available');
      return;
    }
    
    tables.forEach(table => {
      if (profile.role === 'client') {
        setupChannel(table, table === 'notifications' ? `user_id=eq.${user.id}` : `client_id=eq.${user.id}`);
      } else if (profile.role === 'designer') {
        setupChannel(table, table === 'notifications' ? `user_id=eq.${user.id}` : `designer_id=eq.${user.id}`);
      } else if (profile.role === 'admin') {
        setupChannel(table);
      }
    });
  }, [user, profile, setupChannel]);

  // Setup subscriptions based on user role
  useEffect(() => {
    if (!user?.id || !profile || !config.enabled) {
      console.log('Skipping realtime setup - missing requirements', {
        hasUser: !!user?.id,
        hasProfile: !!profile,
        enabled: config.enabled
      });
      return;
    }

    const cleanupFns: Array<() => void> = [];
    
    if (profile.role === 'client') {
      // Client-specific subscriptions
      cleanupFns.push(
        () => setupChannel('projects', `client_id=eq.${user.id}`)?.unsubscribe(),
        () => setupChannel('proposals', `client_id=eq.${user.id}`)?.unsubscribe(),
        () => setupChannel('conversations')?.unsubscribe(),
        () => setupChannel('notifications', `user_id=eq.${user.id}`)?.unsubscribe()
      );
    } else if (profile.role === 'designer') {
      // Designer-specific subscriptions
      cleanupFns.push(
        () => setupChannel('projects', `designer_id=eq.${user.id}`)?.unsubscribe(),
        () => setupChannel('proposals', `designer_id=eq.${user.id}`)?.unsubscribe(),
        () => setupChannel('conversations')?.unsubscribe(),
        () => setupChannel('notifications', `user_id=eq.${user.id}`)?.unsubscribe()
      );
    } else if (profile.role === 'admin') {
      // Admin subscriptions - no filters needed
      cleanupFns.push(
        () => setupChannel('projects')?.unsubscribe(),
        () => setupChannel('proposals')?.unsubscribe(),
        () => setupChannel('conversations')?.unsubscribe(),
        () => setupChannel('notifications')?.unsubscribe()
      );
    }

    // Cleanup function
    return () => {
      console.log('Cleaning up realtime subscriptions');
      cleanupFns.forEach(cleanup => cleanup?.());
      channelsRef.current.forEach(channel => {
        try {
          channel.unsubscribe();
        } catch (error) {
          console.warn('Error unsubscribing from channel:', error);
        }
      });
      channelsRef.current.clear();
      setConnectionState('disconnected');
    };
  }, [user, profile, config.enabled, setupChannel]);

  return {
    connectionState,
    isConnected: connectionState === 'connected',
    activeSubscriptions: channelsRef.current.size,
    triggerManualSync,
    reconnect: useCallback(() => {
      // Clear existing subscriptions and set up new ones
      channelsRef.current.forEach(channel => channel.unsubscribe());
      channelsRef.current.clear();
      setConnectionState('connecting');
      
      if (user?.id && profile) {
        if (profile.role === 'client') {
          setupChannel('projects', `client_id=eq.${user.id}`);
          setupChannel('proposals', `client_id=eq.${user.id}`);
          setupChannel('conversations');
          setupChannel('notifications', `user_id=eq.${user.id}`);
        } else if (profile.role === 'designer') {
          setupChannel('projects', `designer_id=eq.${user.id}`);
          setupChannel('proposals', `designer_id=eq.${user.id}`);
          setupChannel('conversations');
          setupChannel('notifications', `user_id=eq.${user.id}`);
        } else if (profile.role === 'admin') {
          setupChannel('projects');
          setupChannel('proposals');
          setupChannel('conversations');
          setupChannel('notifications');
        }
      }
    }, [user, profile, setupChannel])
  };
}
