"use client";

import React, { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { useNavigationPrefetch } from '@/hooks/useNavigationPrefetch';
import { useDataPersistence } from '@/hooks/useDataPersistence';
import { usePollingSync } from '@/hooks/usePollingSync';
import { usePerformanceMonitoring } from '@/hooks/usePerformanceMonitoring';
import { useBatchDashboardData } from '@/hooks/useDashboardData';

interface PerformanceOptimizerProps {
  children: React.ReactNode;
  enablePrefetching?: boolean;
  enableRealtimeSync?: boolean;
  enableDataPersistence?: boolean;
  enablePerformanceMonitoring?: boolean;
}

export function PerformanceOptimizer({
  children,
  enablePrefetching = true,
  enableRealtimeSync = true, // Now using polling-based sync for reliability
  enableDataPersistence = true,
  enablePerformanceMonitoring = true
}: PerformanceOptimizerProps) {
  const queryClient = useQueryClient();
  const { user, profile } = useOptimizedAuth();
  
  // Initialize all optimization hooks
  const { prefetchAllRoutes } = useNavigationPrefetch();
  const { restoreQueryCache, persistQueryCache } = useDataPersistence();
  const { triggerManualSync } = usePollingSync({
    enabled: enableRealtimeSync,
    batchUpdates: true,
    debounceMs: 300,
    intervals: {
      critical: 30 * 1000,     // 30 seconds for auth/profile
      important: 2 * 60 * 1000, // 2 minutes for dashboard stats
      normal: 5 * 60 * 1000,    // 5 minutes for projects/proposals
      background: 10 * 60 * 1000 // 10 minutes for messages/notifications
    }
  });
  const { getPerformanceAnalytics, autoOptimize } = usePerformanceMonitoring();

  // Batch load critical dashboard data
  const dashboardQueries = useBatchDashboardData(
    user?.id || '',
    profile?.role || 'client'
  );

  // Initialize performance optimization on mount
  useEffect(() => {
    if (!user || !profile) return;

    const initializeOptimizations = async () => {
      try {
        // 1. Restore cached data first for instant loading
        if (enableDataPersistence) {
          await restoreQueryCache();
        }

        // 2. Start prefetching critical routes
        if (enablePrefetching) {
          setTimeout(() => {
            prefetchAllRoutes();
          }, 100); // Small delay to avoid blocking initial render
        }

        // 3. Trigger manual sync for fresh data (polling-based)
        if (enableRealtimeSync) {
          setTimeout(() => {
            triggerManualSync(['projects', 'proposals', 'conversations', 'notifications']);
          }, 500);
        }

        // 4. Start performance monitoring
        if (enablePerformanceMonitoring) {
          setTimeout(() => {
            autoOptimize();
          }, 1000);
        }

        console.log('🚀 Performance optimizations initialized');
      } catch (error) {
        console.warn('Failed to initialize optimizations:', error);
      }
    };

    initializeOptimizations();
  }, [
    user,
    profile,
    enablePrefetching,
    enableRealtimeSync,
    enableDataPersistence,
    enablePerformanceMonitoring,
    restoreQueryCache,
    prefetchAllRoutes,
    triggerManualSync,
    autoOptimize
  ]);

  // Periodic optimization tasks
  useEffect(() => {
    if (!enableDataPersistence && !enablePerformanceMonitoring) return;

    const interval = setInterval(() => {
      // Persist cache every 2 minutes
      if (enableDataPersistence) {
        persistQueryCache();
      }

      // Run performance analysis every 5 minutes
      if (enablePerformanceMonitoring) {
        const analytics = getPerformanceAnalytics();
        
        // Log performance insights in development
        if (process.env.NODE_ENV === 'development') {
          console.log('📊 Performance Analytics:', {
            cacheHitRate: `${analytics.queries.cacheHitRate.toFixed(1)}%`,
            averageQueryTime: `${analytics.queries.averageTime.toFixed(2)}ms`,
            averageRouteLoadTime: `${analytics.routes.averageLoadTime.toFixed(2)}ms`,
            recommendations: analytics.recommendations.length
          });
        }

        // Auto-optimize based on performance metrics
        if (analytics.queries.cacheHitRate < 70) {
          console.log('🔧 Auto-optimization: Improving cache hit rate');
          // Could trigger additional prefetching here
        }
      }
    }, 2 * 60 * 1000); // Every 2 minutes

    return () => clearInterval(interval);
  }, [
    enableDataPersistence,
    enablePerformanceMonitoring,
    persistQueryCache,
    getPerformanceAnalytics
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (enableDataPersistence) {
        persistQueryCache();
      }
    };
  }, [enableDataPersistence, persistQueryCache]);

  // Performance monitoring for this component
  const componentStartTime = React.useRef(performance.now());
  
  useEffect(() => {
    const renderTime = performance.now() - componentStartTime.current;
    if (renderTime > 50) {
      console.warn(`PerformanceOptimizer slow render: ${renderTime.toFixed(2)}ms`);
    }
  });

  return (
    <>
      {children}
      
      {/* Development performance overlay */}
      {process.env.NODE_ENV === 'development' && enablePerformanceMonitoring && (
        <PerformanceDevOverlay />
      )}
    </>
  );
}

// Development overlay for performance metrics
function PerformanceDevOverlay() {
  const { getPerformanceAnalytics } = usePerformanceMonitoring();
  const [analytics, setAnalytics] = React.useState<any>(null);
  const [isVisible, setIsVisible] = React.useState(false);

  const updateAnalytics = useCallback(() => {
    setAnalytics(getPerformanceAnalytics());
  }, [getPerformanceAnalytics]);

  useEffect(() => {
    updateAnalytics();
    const interval = setInterval(updateAnalytics, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [updateAnalytics]);

  if (!analytics) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg hover:bg-blue-700 transition-colors"
      >
        📊 Perf
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <div className="space-y-3">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Query Performance</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Cache Hit Rate:</span>
                  <span className={analytics.queries.cacheHitRate > 80 ? 'text-green-600' : 'text-red-600'}>
                    {analytics.queries.cacheHitRate.toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Avg Query Time:</span>
                  <span className={analytics.queries.averageTime < 500 ? 'text-green-600' : 'text-red-600'}>
                    {analytics.queries.averageTime.toFixed(2)}ms
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Success Rate:</span>
                  <span className={analytics.queries.successRate > 95 ? 'text-green-600' : 'text-red-600'}>
                    {analytics.queries.successRate.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Route Performance</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Avg Load Time:</span>
                  <span className={analytics.routes.averageLoadTime < 1000 ? 'text-green-600' : 'text-red-600'}>
                    {analytics.routes.averageLoadTime.toFixed(2)}ms
                  </span>
                </div>
              </div>
            </div>

            {analytics.recommendations.length > 0 && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Recommendations</h3>
                <div className="space-y-1 text-xs">
                  {analytics.recommendations.slice(0, 3).map((rec: string, index: number) => (
                    <div key={index} className="text-amber-600 bg-amber-50 p-2 rounded">
                      {rec}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {analytics.queries.slowest.length > 0 && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Slowest Queries</h3>
                <div className="space-y-1 text-xs">
                  {analytics.queries.slowest.slice(0, 2).map((query: any, index: number) => (
                    <div key={index} className="text-red-600 bg-red-50 p-2 rounded">
                      <div className="font-mono truncate">{query.queryKey}</div>
                      <div>{query.duration.toFixed(2)}ms</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// HOC for wrapping pages with performance optimization
export function withPerformanceOptimization<P extends object>(
  Component: React.ComponentType<P>,
  options?: Partial<PerformanceOptimizerProps>
) {
  return function OptimizedComponent(props: P) {
    return (
      <PerformanceOptimizer {...options}>
        <Component {...props} />
      </PerformanceOptimizer>
    );
  };
}
