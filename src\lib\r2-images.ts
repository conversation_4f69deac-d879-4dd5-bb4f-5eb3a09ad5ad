/**
 * Utility functions for working with Cloudflare R2 images
 */
import { fromUrlSafeServiceId } from './service-utils';

/**
 * Helper function to check if a style ID is compound (contains slash or hyphen)
 * @param styleId The style ID (e.g., "artistic/handcrafted")
 * @returns True if the style ID is compound
 */
function isCompoundStyleId(styleId: string): boolean {
  return styleId.includes('/') || styleId.includes('-');
}

/**
 * Helper function to normalize style IDs for compound names with slashes
 * @param styleId The style ID (e.g., "artistic/handcrafted")
 * @returns A normalized style ID (e.g., "artistic-handcrafted")
 */
function normalizeStyleId(styleId: string): string {
  // Replace slashes with hyphens
  return styleId.replace(/\//g, '-');
}

/**
 * Helper function to split a compound style ID into individual terms
 * @param styleId The style ID (e.g., "artistic/handcrafted")
 * @returns An array of individual terms (e.g., ["artistic", "handcrafted"])
 */
function splitCompoundStyleId(styleId: string): string[] {
  // First normalize slashes to hyphens
  const normalizedId = normalizeStyleId(styleId);

  // Split by hyphen and trim each part
  return normalizedId.split('-').map(part => part.trim()).filter(part => part.length > 0);
}

/**
 * Helper function to get the first part of a compound style ID
 * @param styleId The style ID (e.g., "artistic/handcrafted")
 * @returns The first part of the style ID (e.g., "artistic")
 */
function getFirstPartOfStyleId(styleId: string): string {
  // If it's not a compound ID, return the original
  if (!isCompoundStyleId(styleId)) {
    return styleId;
  }

  // Split and return the first part
  const parts = splitCompoundStyleId(styleId);
  return parts.length > 0 ? parts[0] : styleId;
}

/**
 * Helper function to get the second part of a compound style ID
 * @param styleId The style ID (e.g., "artistic/handcrafted")
 * @returns The second part of the style ID (e.g., "handcrafted") or null if not compound
 */
function getSecondPartOfStyleId(styleId: string): string | null {
  // If it's not a compound ID, return null
  if (!isCompoundStyleId(styleId)) {
    return null;
  }

  // Split and return the second part if it exists
  const parts = splitCompoundStyleId(styleId);
  return parts.length > 1 ? parts[1] : null;
}

// Fallback images to use if R2 images are not available
export const fallbackImages = [
  "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
  "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2075&q=80",
  "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2053&q=80",
  "https://images.unsplash.com/photo-1583608205776-bfd35f0d9f83?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
];

// Cache for storing fetched image lists to reduce API calls
const imageCache: Record<string, string[]> = {};

/**
 * Fetches a list of available images for a specific service and style
 * @param serviceId The service ID (e.g., "creative-design-&-branding")
 * @param styleId The style ID (e.g., "minimalist")
 * @returns An array of image keys or an empty array if none are found
 */
export async function fetchImagesForStyle(serviceId: string, styleId: string): Promise<string[]> {
  // Ensure the service ID is properly decoded
  const decodedServiceId = fromUrlSafeServiceId(serviceId);

  // Normalize the style ID to handle compound names with slashes
  const normalizedStyleId = normalizeStyleId(styleId);

  // Get individual terms for logging
  const isCompound = isCompoundStyleId(styleId);
  const styleTerms = isCompound ? splitCompoundStyleId(styleId) : [styleId];
  const firstPart = getFirstPartOfStyleId(styleId);
  const secondPart = getSecondPartOfStyleId(styleId);

  // Check cache first
  const cacheKey = `${decodedServiceId}:${normalizedStyleId}`;
  if (imageCache[cacheKey]) {
    return imageCache[cacheKey];
  }

  try {
    console.log(`Fetching images for service: ${decodedServiceId}, style: ${normalizedStyleId} (original: ${styleId})`);
    console.log(`Style details: isCompound=${isCompound}, terms=${JSON.stringify(styleTerms)}, firstPart=${firstPart}, secondPart=${secondPart || 'none'}`);
    const response = await fetch(`/api/r2-images?service=${encodeURIComponent(decodedServiceId)}&style=${encodeURIComponent(normalizedStyleId)}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch images: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.fallback || !data.images || data.images.length === 0) {
      console.warn(`No R2 images found for ${serviceId}/${styleId}, using fallbacks`);
      return [];
    }

    // Store in cache
    imageCache[cacheKey] = data.images;
    return data.images;
  } catch (error) {
    console.error('Error fetching images:', error);
    return [];
  }
}

/**
 * Fetches a specific image from R2 by its key
 * @param imageKey The R2 object key
 * @returns A data URL containing the image data or null if it fails
 */
export async function fetchImageData(imageKey: string): Promise<string | null> {
  try {
    const response = await fetch('/api/r2-images', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ key: imageKey }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch image data: ${response.statusText}`);
    }

    const data = await response.json();
    return data.imageData || null;
  } catch (error) {
    console.error('Error fetching image data:', error);
    return null;
  }
}

/**
 * Gets a random image from the available images for a style
 * @param serviceId The service ID
 * @param styleId The style ID
 * @returns An image URL (either from R2 or a fallback)
 */
export async function getRandomImageForStyle(serviceId: string, styleId: string): Promise<string> {
  // Ensure the service ID is properly decoded
  const decodedServiceId = fromUrlSafeServiceId(serviceId);

  // Normalize the style ID to handle compound names with slashes
  const normalizedStyleId = normalizeStyleId(styleId);

  // Log style details
  console.log(`Getting random image for style: ${styleId} (normalized: ${normalizedStyleId})`);
  if (isCompoundStyleId(styleId)) {
    const firstPart = getFirstPartOfStyleId(styleId);
    const secondPart = getSecondPartOfStyleId(styleId);
    console.log(`Compound style detected: firstPart=${firstPart}, secondPart=${secondPart || 'none'}`);
  }

  const images = await fetchImagesForStyle(decodedServiceId, normalizedStyleId);

  if (images.length === 0) {
    // Return a random fallback image
    const randomIndex = Math.floor(Math.random() * fallbackImages.length);
    return fallbackImages[randomIndex];
  }

  // Get a random image from the available ones
  const randomIndex = Math.floor(Math.random() * images.length);
  const imageKey = images[randomIndex];

  // Fetch the actual image data
  const imageData = await fetchImageData(imageKey);

  if (!imageData) {
    // Return a fallback if we couldn't get the image data
    const fallbackIndex = Math.floor(Math.random() * fallbackImages.length);
    return fallbackImages[fallbackIndex];
  }

  return imageData;
}

/**
 * Gets the next image in sequence for a style (for regeneration)
 * @param serviceId The service ID
 * @param styleId The style ID
 * @param currentImageKey The current image key or null
 * @returns The next image URL in sequence
 */
export async function getNextImageInSequence(
  serviceId: string,
  styleId: string,
  currentImages: string[],
  currentIndex: number
): Promise<{ nextImage: string; nextIndex: number }> {
  // Ensure the service ID is properly decoded
  const decodedServiceId = fromUrlSafeServiceId(serviceId);

  // Normalize the style ID to handle compound names with slashes
  const normalizedStyleId = normalizeStyleId(styleId);

  // Log style details
  console.log(`Getting next image in sequence for style: ${styleId} (normalized: ${normalizedStyleId})`);
  if (isCompoundStyleId(styleId)) {
    const firstPart = getFirstPartOfStyleId(styleId);
    const secondPart = getSecondPartOfStyleId(styleId);
    console.log(`Compound style detected: firstPart=${firstPart}, secondPart=${secondPart || 'none'}`);
  }

  // If we don't have any images yet, fetch them
  if (currentImages.length === 0) {
    const images = await fetchImagesForStyle(decodedServiceId, normalizedStyleId);

    if (images.length === 0) {
      // No R2 images available, use fallbacks
      const nextIndex = (currentIndex + 1) % fallbackImages.length;
      return {
        nextImage: fallbackImages[nextIndex],
        nextIndex
      };
    }

    // We have R2 images, get the first one
    const imageData = await fetchImageData(images[0]);

    if (!imageData) {
      // Fallback if we couldn't get the image data
      return {
        nextImage: fallbackImages[0],
        nextIndex: 0
      };
    }

    return {
      nextImage: imageData,
      nextIndex: 0
    };
  }

  // We already have images, get the next one in sequence
  const nextIndex = (currentIndex + 1) % currentImages.length;
  const nextImageKey = currentImages[nextIndex];

  // If it's an R2 key, fetch the data
  if (nextImageKey.startsWith('data:')) {
    // It's already a data URL
    return {
      nextImage: nextImageKey,
      nextIndex
    };
  } else if (nextImageKey.startsWith('http')) {
    // It's a fallback URL
    return {
      nextImage: nextImageKey,
      nextIndex
    };
  } else {
    // It's an R2 key, fetch the data
    const imageData = await fetchImageData(nextImageKey);

    if (!imageData) {
      // Fallback if we couldn't get the image data
      const fallbackIndex = Math.floor(Math.random() * fallbackImages.length);
      return {
        nextImage: fallbackImages[fallbackIndex],
        nextIndex
      };
    }

    return {
      nextImage: imageData,
      nextIndex
    };
  }
}
