"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Save,
  Plus,
  Trash,
  AlertCircle,
  CheckCircle,
  Loader2,
  MoveUp,
  MoveDown,
  GripVertical,
} from "lucide-react";
import { motion } from "framer-motion";

type Milestone = {
  id: string;
  title: string;
  description: string;
  percentage: number;
  estimated_days: number;
  deliverables: string;
};

type Section = {
  id: string;
  title: string;
  content: string;
  section_type: string;
  order_index: number;
};

export default function CreateProposalTemplate() {
  const { user } = useAuth();
  const router = useRouter();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [projectType, setProjectType] = useState("");
  const [scopeTemplate, setScopeTemplate] = useState("");
  const [timelineTemplate, setTimelineTemplate] = useState("");
  const [isDefault, setIsDefault] = useState(false);
  const [milestones, setMilestones] = useState<Milestone[]>([
    {
      id: "initial",
      title: "Initial Deposit",
      description: "Initial payment to start the project",
      percentage: 30,
      estimated_days: 0,
      deliverables: ""
    },
    {
      id: "milestone-1",
      title: "Concept Design",
      description: "Completion of concept design phase",
      percentage: 30,
      estimated_days: 14,
      deliverables: "Concept sketches, mood boards, preliminary layouts"
    },
    {
      id: "milestone-2",
      title: "Final Delivery",
      description: "Completion of all deliverables",
      percentage: 40,
      estimated_days: 30,
      deliverables: "Final design files, documentation, presentation materials"
    }
  ]);
  const [sections, setSections] = useState<Section[]>([
    {
      id: "intro",
      title: "Introduction",
      content: "This proposal outlines the scope, timeline, and terms for the project.",
      section_type: "introduction",
      order_index: 0
    },
    {
      id: "terms",
      title: "Terms & Conditions",
      content: "Standard terms and conditions for the project agreement.",
      section_type: "terms",
      order_index: 1
    }
  ]);
  const [projectTypes, setProjectTypes] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchProjectTypes();
  }, []);

  const fetchProjectTypes = async () => {
    // This could be fetched from a lookup table or hardcoded based on your application
    setProjectTypes([
      "Residential - Single Family",
      "Residential - Multi-Family",
      "Commercial - Office",
      "Commercial - Retail",
      "Commercial - Hospitality",
      "Institutional",
      "Industrial",
      "Mixed-Use",
      "Landscape",
      "Interior Design",
      "Other"
    ]);
  };

  const handleMilestoneChange = (index: number, field: keyof Milestone, value: string | number) => {
    setMilestones(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        [field]: value
      };
      return updated;
    });
  };

  const addMilestone = () => {
    setMilestones(prev => [
      ...prev,
      {
        id: `milestone-${Date.now()}`,
        title: "New Milestone",
        description: "",
        percentage: 0,
        estimated_days: 0,
        deliverables: ""
      }
    ]);
  };

  const removeMilestone = (index: number) => {
    if (milestones.length <= 1) return;
    setMilestones(prev => prev.filter((_, i) => i !== index));
  };

  const moveMilestone = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === milestones.length - 1)
    ) {
      return;
    }

    setMilestones(prev => {
      const updated = [...prev];
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      [updated[index], updated[newIndex]] = [updated[newIndex], updated[index]];
      return updated;
    });
  };

  const handleSectionChange = (index: number, field: keyof Section, value: string | number) => {
    setSections(prev => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        [field]: value
      };
      return updated;
    });
  };

  const addSection = () => {
    setSections(prev => [
      ...prev,
      {
        id: `section-${Date.now()}`,
        title: "New Section",
        content: "",
        section_type: "custom",
        order_index: prev.length
      }
    ]);
  };

  const removeSection = (index: number) => {
    setSections(prev => prev.filter((_, i) => i !== index));
  };

  const moveSection = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === sections.length - 1)
    ) {
      return;
    }

    setSections(prev => {
      const updated = [...prev];
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      [updated[index], updated[newIndex]] = [updated[newIndex], updated[index]];
      
      // Update order_index values
      return updated.map((section, i) => ({
        ...section,
        order_index: i
      }));
    });
  };

  const validateForm = () => {
    if (!title.trim()) {
      setError("Please enter a template title");
      return false;
    }

    // Check if milestone percentages add up to 100%
    const totalPercentage = milestones.reduce((sum, m) => sum + m.percentage, 0);
    if (totalPercentage !== 100) {
      setError(`Milestone percentages must add up to 100%. Current total: ${totalPercentage}%`);
      return false;
    }

    // Check if all milestones have titles
    if (milestones.some(m => !m.title.trim())) {
      setError("All milestones must have titles");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setSubmitting(true);
    setError(null);

    try {
      // 1. Create the template
      const { data: template, error: templateError } = await supabase
        .from('proposal_templates')
        .insert({
          title,
          description,
          scope_template: scopeTemplate,
          timeline_template: timelineTemplate,
          project_type: projectType || null,
          created_by: user?.id,
          is_default: isDefault,
          is_active: true
        })
        .select()
        .single();

      if (templateError) throw templateError;

      // 2. Create template milestones
      for (let i = 0; i < milestones.length; i++) {
        const milestone = milestones[i];
        const { error: milestoneError } = await supabase
          .from('proposal_template_milestones')
          .insert({
            template_id: template.id,
            title: milestone.title,
            description: milestone.description,
            percentage: milestone.percentage,
            estimated_days: milestone.estimated_days,
            deliverables: milestone.deliverables,
            order_index: i
          });

        if (milestoneError) throw milestoneError;
      }

      // 3. Create template sections
      for (const section of sections) {
        const { error: sectionError } = await supabase
          .from('proposal_template_sections')
          .insert({
            template_id: template.id,
            title: section.title,
            content: section.content,
            section_type: section.section_type,
            order_index: section.order_index
          });

        if (sectionError) throw sectionError;
      }

      // Show success message and redirect
      setSuccess("Template created successfully");
      setTimeout(() => {
        router.push("/admin/proposal-templates");
      }, 2000);
    } catch (error) {
      console.error('Error creating template:', error);
      setError('Failed to create template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="p-6 max-w-5xl mx-auto">
      <div className="flex items-center mb-6">
        <Link href="/admin/proposal-templates">
          <Button variant="ghost" className="p-0 h-auto mr-4">
            <ArrowLeft className="h-5 w-5 text-gray-500" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Create Proposal Template</h1>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      {success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-green-50 border border-green-200 p-4 mb-6 flex items-start"
        >
          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-green-700">{success}</p>
        </motion.div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Template Information</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Template Title *
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                placeholder="e.g., Standard Residential Design Proposal"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                placeholder="Brief description of when to use this template"
              />
            </div>

            <div>
              <label htmlFor="projectType" className="block text-sm font-medium text-gray-700 mb-1">
                Project Type
              </label>
              <select
                id="projectType"
                value={projectType}
                onChange={(e) => setProjectType(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              >
                <option value="">Any Project Type</option>
                {projectTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                If selected, this template will be suggested for projects of this type
              </p>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isDefault"
                checked={isDefault}
                onChange={(e) => setIsDefault(e.target.checked)}
                className="h-4 w-4 text-brown-600 focus:ring-brown-500 border-gray-300 rounded"
              />
              <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700">
                Set as default template
              </label>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Scope & Timeline Templates</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="scopeTemplate" className="block text-sm font-medium text-gray-700 mb-1">
                Scope of Work Template
              </label>
              <textarea
                id="scopeTemplate"
                value={scopeTemplate}
                onChange={(e) => setScopeTemplate(e.target.value)}
                rows={5}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                placeholder="Template text for the scope of work section..."
              />
              <p className="mt-1 text-xs text-gray-500">
                Use placeholders like {'{PROJECT_NAME}'}, {'{CLIENT_NAME}'}, etc. that will be replaced when creating a proposal
              </p>
            </div>

            <div>
              <label htmlFor="timelineTemplate" className="block text-sm font-medium text-gray-700 mb-1">
                Timeline Template
              </label>
              <textarea
                id="timelineTemplate"
                value={timelineTemplate}
                onChange={(e) => setTimelineTemplate(e.target.value)}
                rows={5}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                placeholder="Template text for the project timeline section..."
              />
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Payment Milestones</h2>
            <div className="flex items-center text-sm text-gray-500">
              <span>Total: {milestones.reduce((sum, m) => sum + m.percentage, 0)}%</span>
            </div>
          </div>
          
          <div className="space-y-6">
            {milestones.map((milestone, index) => (
              <div key={milestone.id} className="border border-gray-200 rounded-md p-4">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <GripVertical className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="font-medium">Milestone {index + 1}</h3>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => moveMilestone(index, 'up')}
                      disabled={index === 0}
                      className="p-1 h-8 w-8"
                    >
                      <MoveUp className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => moveMilestone(index, 'down')}
                      disabled={index === milestones.length - 1}
                      className="p-1 h-8 w-8"
                    >
                      <MoveDown className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeMilestone(index)}
                      disabled={milestones.length <= 1}
                      className="p-1 h-8 w-8 text-red-500"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title *
                    </label>
                    <input
                      type="text"
                      value={milestone.title}
                      onChange={(e) => handleMilestoneChange(index, 'title', e.target.value)}
                      required
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Percentage (%) *
                      </label>
                      <input
                        type="number"
                        value={milestone.percentage}
                        onChange={(e) => handleMilestoneChange(index, 'percentage', Number(e.target.value))}
                        min="0"
                        max="100"
                        required
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Est. Days
                      </label>
                      <input
                        type="number"
                        value={milestone.estimated_days}
                        onChange={(e) => handleMilestoneChange(index, 'estimated_days', Number(e.target.value))}
                        min="0"
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={milestone.description}
                    onChange={(e) => handleMilestoneChange(index, 'description', e.target.value)}
                    rows={2}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Deliverables
                  </label>
                  <textarea
                    value={milestone.deliverables}
                    onChange={(e) => handleMilestoneChange(index, 'deliverables', e.target.value)}
                    rows={2}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    placeholder="List of deliverables for this milestone"
                  />
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={addMilestone}
              className="w-full flex items-center justify-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Milestone
            </Button>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Additional Sections</h2>
          </div>
          
          <div className="space-y-6">
            {sections.map((section, index) => (
              <div key={section.id} className="border border-gray-200 rounded-md p-4">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center">
                    <GripVertical className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="font-medium">Section {index + 1}</h3>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => moveSection(index, 'up')}
                      disabled={index === 0}
                      className="p-1 h-8 w-8"
                    >
                      <MoveUp className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => moveSection(index, 'down')}
                      disabled={index === sections.length - 1}
                      className="p-1 h-8 w-8"
                    >
                      <MoveDown className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeSection(index)}
                      className="p-1 h-8 w-8 text-red-500"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Section Title *
                    </label>
                    <input
                      type="text"
                      value={section.title}
                      onChange={(e) => handleSectionChange(index, 'title', e.target.value)}
                      required
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Section Type
                    </label>
                    <select
                      value={section.section_type}
                      onChange={(e) => handleSectionChange(index, 'section_type', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    >
                      <option value="introduction">Introduction</option>
                      <option value="process">Process</option>
                      <option value="terms">Terms & Conditions</option>
                      <option value="custom">Custom</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Content *
                  </label>
                  <textarea
                    value={section.content}
                    onChange={(e) => handleSectionChange(index, 'content', e.target.value)}
                    rows={4}
                    required
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  />
                </div>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={addSection}
              className="w-full flex items-center justify-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Section
            </Button>
          </div>
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={submitting}
            className="flex items-center bg-brown-600 hover:bg-brown-700 text-white border-0"
          >
            {submitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Template
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
