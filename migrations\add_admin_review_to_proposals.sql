-- Add admin review fields to project_proposals table

-- Add columns for admin review
ALTER TABLE project_proposals
ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS reviewed_by UUID REFERENCES profiles(id),
ADD COLUMN IF NOT EXISTS admin_feedback TEXT,
ADD COLUMN IF NOT EXISTS client_approved_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS client_approved_by UUID REFERENCES profiles(id),
ADD COLUMN IF NOT EXISTS client_rejected_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS client_rejected_by UUID REFERENCES profiles(id);

-- Update the status constraint to include new statuses
ALTER TABLE project_proposals
DROP CONSTRAINT IF EXISTS project_proposals_status_check;

ALTER TABLE project_proposals
ADD CONSTRAINT project_proposals_status_check
CHECK (status IN ('pending', 'approved', 'rejected', 'revision_requested', 'client_approved', 'client_rejected'));

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_project_proposals_status ON project_proposals(status);
CREATE INDEX IF NOT EXISTS idx_project_proposals_reviewed_by ON project_proposals(reviewed_by);
CREATE INDEX IF NOT EXISTS idx_project_proposals_reviewed_at ON project_proposals(reviewed_at);

-- Add RLS policy for admin access to proposals
CREATE POLICY "Admin can manage all proposals" ON project_proposals
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Comments for documentation
COMMENT ON COLUMN project_proposals.reviewed_at IS 'Timestamp when admin reviewed the proposal';
COMMENT ON COLUMN project_proposals.reviewed_by IS 'Admin user who reviewed the proposal';
COMMENT ON COLUMN project_proposals.admin_feedback IS 'Admin feedback on the proposal (approval/rejection reason)';
