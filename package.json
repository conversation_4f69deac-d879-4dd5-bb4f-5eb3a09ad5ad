{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 --turbopack", "build": "next build", "start": "next start", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write"}, "dependencies": {"@aws-sdk/client-s3": "^3.810.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@google/genai": "^0.14.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.80.3", "@tanstack/react-query-devtools": "^5.80.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "framer-motion": "^12.7.4", "gsap": "^3.12.7", "lucide-react": "^0.475.0", "next": "^15.2.0", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "resend": "^4.5.2", "stripe": "^18.1.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.29"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "supabase": "^2.22.6", "tailwindcss": "^3.4.1", "typescript": "^5"}}