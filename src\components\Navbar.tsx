"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "./ui/button";
import { useNavbarContext } from "@/contexts/NavbarContext";
import { toUrlSafeServiceId } from "@/lib/service-utils";

// Services data from ServicesSection - minimalistic version
const services = [
  "Creative Design & Branding",
  "Innovative Architectural Design",
  "Interior Design",
  "Urban & Architectural Planning",
  "Residential & Commercial Projects",
  "Landscape and Architecture Integration",
  "Educational & Community-Oriented Spaces",
];

const navItems = [
  { name: "Home", path: "/" },
  { name: "About Us", path: "/about" },
  { name: "Projects", path: "/projects" },
  { name: "Services", path: "/services", hasDropdown: true },
  { name: "Blog", path: "/blog" },
  { name: "Tracker", path: "/track" },
  { name: "FAQ", path: "/faq" },
  { name: "Join <PERSON>", path: "/join-us" },
  { name: "Contact", path: "/contact" },
];

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isServicesExpanded, setIsServicesExpanded] = useState(false);
  const pathname = usePathname();

  // Get navbar visibility from context
  const { navbarVisible } = useNavbarContext();

  // Handle scroll event - moved before any conditionals
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Toggle menu - moved before any conditionals
  const toggleMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    // Reset services dropdown when closing main menu
    if (isMobileMenuOpen) {
      setIsServicesExpanded(false);
    }

    // Prevent body scrolling when menu is open
    if (!isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  };

  // Toggle services dropdown
  const toggleServices = () => {
    setIsServicesExpanded(!isServicesExpanded);
  };

  // Close menu when route changes - moved before any conditionals
  useEffect(() => {
    setIsMobileMenuOpen(false);
    setIsServicesExpanded(false);
    document.body.style.overflow = '';
  }, [pathname]);

  // Cleanup overflow style when component unmounts - moved before any conditionals
  useEffect(() => {
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  // If navbar should be hidden (when modal is open), render nothing
  if (!navbarVisible) return null;

  const itemVariants = {
    closed: { opacity: 0, y: 20 },
    open: { opacity: 1, y: 0 }
  };

  return (
    <>
      {/* Fixed header */}
      <header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled ? "bg-white shadow-md py-2" : "bg-transparent py-4"
        }`}
      >
        <div className="container mx-auto flex justify-between items-center px-4">
          <Link href="/" className="z-50 pointer-events-auto">
            <motion.div
              className="flex items-center"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Image
                src="/seniors-wordmark.svg"
                alt="Senior's Archi-Firm Logo"
                width={200}
                height={40}
                className={`h-10 w-auto transition-opacity duration-300 ${
                  isMobileMenuOpen ? 'opacity-100' : isScrolled ? 'opacity-100' : 'opacity-100'
                }`}
                priority
              />
            </motion.div>
          </Link>

          {/* Mobile hamburger menu */}
          <button
            onClick={toggleMenu}
            className="p-2 rounded-full focus:outline-none z-50 pointer-events-auto"
            aria-label="Toggle menu"
          >
            <div className="relative w-10 h-10 flex justify-center items-center">
              <motion.div
                className={`block w-8 absolute h-0.5 ${isMobileMenuOpen ? 'bg-primary' : isScrolled ? 'bg-primary' : 'bg-primary'}`}
                animate={{
                  top: isMobileMenuOpen ? '50%' : '30%',
                  rotate: isMobileMenuOpen ? 45 : 0,
                  translateY: isMobileMenuOpen ? '-50%' : 0
                }}
                transition={{ duration: 0.3 }}
              />
              <motion.div
                className={`block w-8 absolute top-1/2 -translate-y-1/2 h-0.5 ${isMobileMenuOpen ? 'bg-primary' : isScrolled ? 'bg-primary' : 'bg-primary'}`}
                animate={{
                  opacity: isMobileMenuOpen ? 0 : 1
                }}
                transition={{ duration: 0.3 }}
              />
              <motion.div
                className={`block w-8 absolute h-0.5 ${isMobileMenuOpen ? 'bg-primary' : isScrolled ? 'bg-primary' : 'bg-primary'}`}
                animate={{
                  top: isMobileMenuOpen ? '50%' : '70%',
                  rotate: isMobileMenuOpen ? -45 : 0,
                  translateY: isMobileMenuOpen ? '-50%' : 0
                }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </button>
        </div>
      </header>

      {/* Separate full-screen menu overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="fixed inset-0 bg-white z-40 pointer-events-auto overflow-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="min-h-screen flex flex-col justify-center items-center py-20">
              <nav className="text-center">
                <motion.ul
                  className="space-y-6"
                  initial="closed"
                  animate="open"
                  exit="closed"
                  variants={{
                    open: {
                      transition: {
                        staggerChildren: 0.08,
                        delayChildren: 0.2,
                      }
                    },
                    closed: {
                      transition: {
                        staggerChildren: 0.05,
                        staggerDirection: -1
                      }
                    }
                  }}
                >
                  {navItems.map((item) => (
                    <motion.li
                      key={item.path}
                      className="overflow-hidden"
                      variants={itemVariants}
                      transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
                    >
                      {item.hasDropdown ? (
                        // Services with dropdown
                        <div className="space-y-4">
                          <motion.button
                            onClick={toggleServices}
                            className={`text-4xl md:text-5xl font-bold transition-colors duration-200 flex items-center justify-center gap-3 ${
                              pathname.startsWith('/services') ? 'text-primary' : 'text-black hover:text-primary'
                            }`}
                            whileTap={{ scale: 0.98 }}
                            transition={{ duration: 0.1 }}
                          >
                            {item.name}
                            <motion.div
                              animate={{ rotate: isServicesExpanded ? 180 : 0 }}
                              transition={{
                                duration: 0.3,
                                ease: [0.4, 0.0, 0.2, 1]
                              }}
                            >
                              <ChevronDown className="h-8 w-8 md:h-10 md:w-10" />
                            </motion.div>
                          </motion.button>

                          <AnimatePresence>
                            {isServicesExpanded && (
                              <motion.div
                                initial={{ opacity: 0, scaleY: 0 }}
                                animate={{ opacity: 1, scaleY: 1 }}
                                exit={{ opacity: 0, scaleY: 0 }}
                                transition={{
                                  duration: 0.3,
                                  ease: [0.4, 0.0, 0.2, 1],
                                  opacity: { duration: 0.2 }
                                }}
                                className="origin-top"
                              >
                                <div className="space-y-4 pt-6">
                                  {services.map((service, index) => (
                                    <motion.div
                                      key={service}
                                      initial={{ opacity: 0, y: 10 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      transition={{
                                        duration: 0.2,
                                        delay: index * 0.03,
                                        ease: [0.4, 0.0, 0.2, 1]
                                      }}
                                    >
                                      <Link
                                        href={`/services/${toUrlSafeServiceId(service)}`}
                                        className="block text-center text-2xl md:text-3xl font-medium text-gray-700 hover:text-primary transition-colors duration-200"
                                      >
                                        {service}
                                      </Link>
                                    </motion.div>
                                  ))}

                                  {/* View All Services Link */}
                                  <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{
                                      duration: 0.2,
                                      delay: services.length * 0.03 + 0.1,
                                      ease: [0.4, 0.0, 0.2, 1]
                                    }}
                                    className="pt-6 border-t border-gray-200 mt-8"
                                  >
                                    <Link
                                      href="/services"
                                      className="inline-flex items-center gap-2 text-lg font-medium text-primary hover:text-primary/80 transition-colors duration-200"
                                    >
                                      View All Services
                                      <ChevronRight className="h-5 w-5" />
                                    </Link>
                                  </motion.div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      ) : (
                        // Regular menu items
                        <Link
                          href={item.path}
                          className={`text-4xl md:text-5xl font-bold transition-colors duration-300 ${
                            pathname === item.path ? 'text-primary' : 'text-black hover:text-primary'
                          }`}
                        >
                          {item.name}
                        </Link>
                      )}
                    </motion.li>
                  ))}
                </motion.ul>
              </nav>

              {/* Login Button */}
              <motion.div
                className="mt-12"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Link href="/auth/login">
                  <span className="text-xl md:text-2xl font-semibold text-primary border-b-2 border-primary pb-1 hover:text-primary/80 transition-colors">
                    Login
                  </span>
                </Link>
              </motion.div>

              {/* Social Media Icons */}
              <motion.div
                className="mt-10 flex space-x-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                {/* Facebook */}
                <a href="#" className="text-gray-400 hover:text-primary transition-colors">
                  <span className="sr-only">Facebook</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </a>

                {/* Twitter */}
                <a href="#" className="text-gray-400 hover:text-primary transition-colors">
                  <span className="sr-only">Twitter</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>

                {/* LinkedIn */}
                <a href="#" className="text-gray-400 hover:text-primary transition-colors">
                  <span className="sr-only">LinkedIn</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                  </svg>
                </a>

                {/* Instagram */}
                <a href="#" className="text-gray-400 hover:text-primary transition-colors">
                  <span className="sr-only">Instagram</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                  </svg>
                </a>
              </motion.div>

              {/* Schedule a Call Button */}
              <motion.div
                className="mt-10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <a
                  href="https://calendly.com/seniorsarchifirm/consultation"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-primary text-white px-8 py-4 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-300 shadow-lg hover:shadow-xl"
                >
                  Schedule a Call
                </a>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navbar;
