"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Star,
  Eye,
  ArrowRight,
  Image as ImageIcon
} from "lucide-react";
import { Button } from "@/components/ui/Button";

interface PortfolioProject {
  id: string;
  title: string;
  category: string | null;
  featured: boolean;
  cover_image: string | null;
  images_count: number;
  completion_date: string | null;
}

export function PortfolioSection() {
  const { user } = useAuth();
  const [portfolioProjects, setPortfolioProjects] = useState<PortfolioProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalProjects: 0,
    featuredProjects: 0,
    averageRating: 4.8,
    totalViews: 0
  });

  useEffect(() => {
    if (user) {
      fetchPortfolioData();
    }
  }, [user]);

  const fetchPortfolioData = async () => {
    try {
      // Fetch portfolio projects with cover images
      const { data: projectsData, error: projectsError } = await supabase
        .from('portfolio_projects')
        .select(`
          id,
          title,
          category,
          featured,
          completion_date,
          portfolio_images!inner(image_url, is_cover)
        `)
        .eq('designer_id', user?.id)
        .eq('portfolio_images.is_cover', true)
        .order('featured', { ascending: false })
        .order('completion_date', { ascending: false })
        .limit(6);

      if (projectsError) throw projectsError;

      // Get total project count
      const { count: totalCount } = await supabase
        .from('portfolio_projects')
        .select('*', { count: 'exact', head: true })
        .eq('designer_id', user?.id);

      // Get featured project count
      const { count: featuredCount } = await supabase
        .from('portfolio_projects')
        .select('*', { count: 'exact', head: true })
        .eq('designer_id', user?.id)
        .eq('featured', true);

      // Process projects data
      const projects = (projectsData || []).map(project => ({
        id: project.id,
        title: project.title,
        category: project.category,
        featured: project.featured,
        cover_image: project.portfolio_images?.[0]?.image_url || null,
        images_count: 1, // We'll get this from a separate query if needed
        completion_date: project.completion_date
      }));

      setPortfolioProjects(projects);
      setStats({
        totalProjects: totalCount || 0,
        featuredProjects: featuredCount || 0,
        averageRating: 4.8, // This would come from reviews
        totalViews: Math.floor(Math.random() * 1000) + 500 // Mock data
      });

    } catch (error) {
      console.error('Error fetching portfolio data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="aspect-square bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.7 }}
      className="bg-white rounded-lg shadow-sm border"
    >
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Portfolio</h3>
            <div className="flex items-center space-x-4 mt-1">
              <div className="flex items-center text-sm text-gray-600">
                <Star className="h-4 w-4 text-yellow-400 mr-1" />
                <span>{stats.averageRating} rating</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Eye className="h-4 w-4 mr-1" />
                <span>{stats.totalViews} views</span>
              </div>
            </div>
          </div>
          <Link href="/designer/portfolio">
            <Button variant="ghost" size="sm">
              View All <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </div>
      </div>

      <div className="p-6">
        {portfolioProjects.length === 0 ? (
          <div className="text-center py-8">
            <ImageIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Portfolio Projects</h4>
            <p className="text-gray-600 mb-4">Start building your portfolio to showcase your work.</p>
            <Link href="/designer/portfolio">
              <Button variant="outline" className="border-brown-600 text-brown-600 hover:bg-brown-50">
                Manage Portfolio
              </Button>
            </Link>
          </div>
        ) : (
          <>
            {/* Portfolio Stats */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.totalProjects}</div>
                <div className="text-sm text-gray-600">Projects</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.featuredProjects}</div>
                <div className="text-sm text-gray-600">Featured</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{stats.totalViews}</div>
                <div className="text-sm text-gray-600">Views</div>
              </div>
            </div>

            {/* Portfolio Grid */}
            <div className="grid grid-cols-3 gap-3">
              {portfolioProjects.map((project) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                  className="relative group cursor-pointer"
                >
                  <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    {project.cover_image ? (
                      <img
                        src={project.cover_image}
                        alt={project.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <ImageIcon className="h-8 w-8 text-gray-400" />
                      </div>
                    )}

                    {/* Featured Badge */}
                    {project.featured && (
                      <div className="absolute top-2 left-2">
                        <div className="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                          <Star className="h-3 w-3 mr-1" />
                          Featured
                        </div>
                      </div>
                    )}

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Eye className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </div>

                  {/* Project Info */}
                  <div className="mt-2">
                    <h4 className="text-sm font-medium text-gray-900 truncate">{project.title}</h4>
                    {project.category && (
                      <p className="text-xs text-gray-500 truncate">{project.category}</p>
                    )}
                  </div>
                </motion.div>
              ))}

              {/* View More Button */}
              {portfolioProjects.length >= 6 && (
                <Link href="/designer/portfolio">
                  <div className="aspect-square bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center hover:border-brown-400 hover:bg-brown-50 transition-colors cursor-pointer group">
                    <div className="text-center">
                      <Eye className="h-8 w-8 text-gray-400 group-hover:text-brown-600 mx-auto mb-2" />
                      <span className="text-sm text-gray-500 group-hover:text-brown-600">View All</span>
                    </div>
                  </div>
                </Link>
              )}
            </div>
          </>
        )}
      </div>
    </motion.div>
  );
}
