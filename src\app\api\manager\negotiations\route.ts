import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/manager/negotiations
 * Get negotiation sessions for the authenticated manager
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const sessionType = searchParams.get('session_type') || 'all';
    const projectId = searchParams.get('project_id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is manager or admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['manager', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Build query
    let query = supabase
      .from('negotiation_sessions')
      .select(`
        id,
        project_id,
        manager_id,
        client_id,
        designer_id,
        session_type,
        status,
        initial_terms,
        final_terms,
        manager_notes,
        started_at,
        completed_at,
        created_at,
        projects:project_id (
          title,
          status,
          budget
        ),
        manager:manager_id (
          full_name,
          avatar_url
        ),
        client:client_id (
          full_name,
          avatar_url,
          email
        ),
        designer:designer_id (
          full_name,
          avatar_url,
          email
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by manager if not admin
    if (profile.role === 'manager') {
      query = query.eq('manager_id', user.id);
    }

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (sessionType !== 'all') {
      query = query.eq('session_type', sessionType);
    }

    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    const { data: sessions, error } = await query;

    if (error) {
      console.error('Error fetching negotiation sessions:', error);
      return NextResponse.json({ error: 'Failed to fetch sessions' }, { status: 500 });
    }

    // Calculate session duration and add metadata
    const sessionsWithMetadata = sessions?.map(session => {
      let duration = null;
      if (session.started_at && session.completed_at) {
        const start = new Date(session.started_at);
        const end = new Date(session.completed_at);
        duration = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60)); // hours
      }

      return {
        ...session,
        duration_hours: duration,
        is_overdue: session.status === 'active' && 
                   new Date(session.started_at) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days
      };
    });

    // Get total count for pagination
    let countQuery = supabase
      .from('negotiation_sessions')
      .select('id', { count: 'exact', head: true });

    if (profile.role === 'manager') {
      countQuery = countQuery.eq('manager_id', user.id);
    }

    if (status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    if (sessionType !== 'all') {
      countQuery = countQuery.eq('session_type', sessionType);
    }

    if (projectId) {
      countQuery = countQuery.eq('project_id', projectId);
    }

    const { count } = await countQuery;

    return NextResponse.json({
      sessions: sessionsWithMetadata,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in GET /api/manager/negotiations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/manager/negotiations
 * Create a new negotiation session
 */
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is manager or admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['manager', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const {
      project_id,
      session_type,
      initial_terms,
      manager_notes
    } = await request.json();

    if (!project_id || !session_type) {
      return NextResponse.json(
        { error: 'Project ID and session type are required' },
        { status: 400 }
      );
    }

    // Validate session type
    const validSessionTypes = ['pricing', 'timeline', 'scope', 'terms'];
    if (!validSessionTypes.includes(session_type)) {
      return NextResponse.json(
        { error: 'Invalid session type' },
        { status: 400 }
      );
    }

    // Get project details and check manager assignment
    const { data: project } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        client_id,
        designer_id,
        assigned_manager_id,
        project_assignments!inner (
          manager_id,
          status
        )
      `)
      .eq('id', project_id)
      .eq('project_assignments.status', 'active')
      .single();

    if (!project) {
      return NextResponse.json({ error: 'Project not found or not assigned' }, { status: 404 });
    }

    // Check if user is the assigned manager or admin
    const managerId = profile.role === 'admin' ? project.assigned_manager_id : user.id;
    if (profile.role === 'manager' && project.assigned_manager_id !== user.id) {
      return NextResponse.json({ error: 'Not assigned to this project' }, { status: 403 });
    }

    // Check for existing active session of same type
    const { data: existingSession } = await supabase
      .from('negotiation_sessions')
      .select('id')
      .eq('project_id', project_id)
      .eq('session_type', session_type)
      .eq('status', 'active')
      .single();

    if (existingSession) {
      return NextResponse.json(
        { error: `An active ${session_type} negotiation session already exists for this project` },
        { status: 400 }
      );
    }

    // Create negotiation session
    const { data: session, error } = await supabase
      .from('negotiation_sessions')
      .insert({
        project_id,
        manager_id: managerId,
        client_id: project.client_id,
        designer_id: project.designer_id,
        session_type,
        initial_terms,
        manager_notes,
        status: 'active'
      })
      .select(`
        *,
        projects:project_id (
          title,
          status
        ),
        manager:manager_id (
          full_name,
          avatar_url
        ),
        client:client_id (
          full_name,
          avatar_url,
          email
        ),
        designer:designer_id (
          full_name,
          avatar_url,
          email
        )
      `)
      .single();

    if (error) {
      console.error('Error creating negotiation session:', error);
      return NextResponse.json({ error: 'Failed to create session' }, { status: 500 });
    }

    // Create notifications for client and designer
    const notifications = [
      {
        recipient_id: project.client_id,
        notification_type: 'negotiation_started',
        title: 'Negotiation Session Started',
        message: `A ${session_type} negotiation session has been started for project: ${project.title}`,
        priority: 'normal',
        metadata: { 
          session_id: session.id, 
          project_id,
          session_type 
        }
      },
      {
        recipient_id: project.designer_id,
        notification_type: 'negotiation_started',
        title: 'Negotiation Session Started',
        message: `A ${session_type} negotiation session has been started for project: ${project.title}`,
        priority: 'normal',
        metadata: { 
          session_id: session.id, 
          project_id,
          session_type 
        }
      }
    ];

    await supabase
      .from('workflow_notifications')
      .insert(notifications);

    // Log manager activity
    await supabase
      .from('manager_activities')
      .insert({
        manager_id: managerId,
        project_id,
        activity_type: 'negotiation_guidance',
        description: `Started ${session_type} negotiation session`,
        participants: [managerId, project.client_id, project.designer_id],
        outcome: 'session_started'
      });

    return NextResponse.json(session, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/manager/negotiations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
