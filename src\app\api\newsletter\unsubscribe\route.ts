import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const { email, token } = await request.json();

    if (!email && !token) {
      return NextResponse.json(
        { error: 'Email address or unsubscribe token is required' },
        { status: 400 }
      );
    }

    let query = supabase.from('newsletter_subscribers');

    if (token) {
      // Unsubscribe using token (from email links)
      query = query.eq('confirmation_token', token);
    } else {
      // Unsubscribe using email
      query = query.eq('email', email.toLowerCase());
    }

    // Find the subscriber
    const { data: subscriber, error: findError } = await query
      .select('*')
      .single();

    if (findError || !subscriber) {
      return NextResponse.json(
        { error: 'Subscriber not found' },
        { status: 404 }
      );
    }

    if (subscriber.status === 'unsubscribed') {
      return NextResponse.json(
        { message: 'Email is already unsubscribed' },
        { status: 200 }
      );
    }

    // Update subscriber status to unsubscribed
    const { error: updateError } = await supabase
      .from('newsletter_subscribers')
      .update({
        status: 'unsubscribed',
        unsubscribed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriber.id);

    if (updateError) {
      console.error('Unsubscribe update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to unsubscribe' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Successfully unsubscribed from newsletter',
      email: subscriber.email
    });

  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const email = searchParams.get('email');

    if (!email && !token) {
      return NextResponse.json(
        { error: 'Email address or unsubscribe token is required' },
        { status: 400 }
      );
    }

    let query = supabase.from('newsletter_subscribers');

    if (token) {
      query = query.eq('confirmation_token', token);
    } else {
      query = query.eq('email', email?.toLowerCase());
    }

    // Find the subscriber
    const { data: subscriber, error: findError } = await query
      .select('*')
      .single();

    if (findError || !subscriber) {
      return NextResponse.json(
        { error: 'Subscriber not found' },
        { status: 404 }
      );
    }

    if (subscriber.status === 'unsubscribed') {
      return NextResponse.json(
        { message: 'Email is already unsubscribed' },
        { status: 200 }
      );
    }

    // Update subscriber status to unsubscribed
    const { error: updateError } = await supabase
      .from('newsletter_subscribers')
      .update({
        status: 'unsubscribed',
        unsubscribed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriber.id);

    if (updateError) {
      console.error('Unsubscribe update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to unsubscribe' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Successfully unsubscribed from newsletter',
      email: subscriber.email
    });

  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
