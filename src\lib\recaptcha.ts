/**
 * reCAPTCHA utility functions for client and server-side verification
 */

// Client-side: Load reCAPTCHA script
export const loadRecaptcha = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      reject(new Error('reCAPTCHA can only be loaded in browser environment'));
      return;
    }

    // Check if reCAPTCHA is already loaded
    if (window.grecaptcha) {
      resolve();
      return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = `https://www.google.com/recaptcha/api.js?render=${process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      // Wait for grecaptcha to be ready
      window.grecaptcha.ready(() => {
        resolve();
      });
    };

    script.onerror = () => {
      reject(new Error('Failed to load reCA<PERSON><PERSON><PERSON> script'));
    };

    document.head.appendChild(script);
  });
};

// Client-side: Execute reCAPTCHA
export const executeRecaptcha = async (action: string): Promise<string> => {
  if (typeof window === 'undefined') {
    throw new Error('reCAPTCHA can only be executed in browser environment');
  }

  if (!window.grecaptcha) {
    throw new Error('reCAPTCHA not loaded');
  }

  if (!process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY) {
    throw new Error('reCAPTCHA site key not configured');
  }

  return new Promise((resolve, reject) => {
    window.grecaptcha.ready(() => {
      window.grecaptcha
        .execute(process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY!, { action })
        .then((token: string) => {
          resolve(token);
        })
        .catch((error: any) => {
          reject(error);
        });
    });
  });
};

// Server-side: Verify reCAPTCHA token
export const verifyRecaptcha = async (token: string, expectedAction?: string): Promise<{
  success: boolean;
  score?: number;
  action?: string;
  error?: string;
}> => {
  if (!token) {
    return {
      success: false,
      error: 'No reCAPTCHA token provided'
    };
  }

  if (!process.env.RECAPTCHA_SECRET_KEY) {
    return {
      success: false,
      error: 'reCAPTCHA secret key not configured'
    };
  }

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret: process.env.RECAPTCHA_SECRET_KEY,
        response: token,
      }),
    });

    const data = await response.json();

    if (!data.success) {
      return {
        success: false,
        error: `reCAPTCHA verification failed: ${data['error-codes']?.join(', ') || 'Unknown error'}`
      };
    }

    // Check action if provided (more lenient in development)
    if (expectedAction && data.action !== expectedAction) {
      console.warn(`reCAPTCHA action mismatch. Expected: ${expectedAction}, Got: ${data.action}`);
      if (process.env.NODE_ENV === 'production') {
        return {
          success: false,
          error: `Action mismatch. Expected: ${expectedAction}, Got: ${data.action}`
        };
      }
      // In development, just warn but don't fail
    }

    // Check score (for v3, score should be > 0.5 for human-like behavior)
    // Be more lenient in development
    const minScore = process.env.NODE_ENV === 'production' ? 0.5 : 0.3;
    if (data.score !== undefined && data.score < minScore) {
      console.warn(`Low reCAPTCHA score: ${data.score} (minimum: ${minScore})`);
      if (process.env.NODE_ENV === 'production') {
        return {
          success: false,
          error: `Low reCAPTCHA score: ${data.score}`,
          score: data.score
        };
      }
      // In development, just warn but don't fail
    }

    return {
      success: true,
      score: data.score,
      action: data.action
    };

  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return {
      success: false,
      error: 'Failed to verify reCAPTCHA'
    };
  }
};

// React hook for reCAPTCHA
export const useRecaptcha = () => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  React.useEffect(() => {
    if (typeof window === 'undefined') return;

    const initRecaptcha = async () => {
      setIsLoading(true);
      try {
        await loadRecaptcha();
        setIsLoaded(true);
      } catch (error) {
        console.error('Failed to load reCAPTCHA:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initRecaptcha();
  }, []);

  const execute = React.useCallback(async (action: string) => {
    if (!isLoaded) {
      throw new Error('reCAPTCHA not loaded yet');
    }
    return executeRecaptcha(action);
  }, [isLoaded]);

  return {
    isLoaded,
    isLoading,
    execute
  };
};

// TypeScript declarations
declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (siteKey: string, options: { action: string }) => Promise<string>;
    };
  }
}

// Import React for the hook
import React from 'react';
