import { useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface PerformanceMetrics {
  conversationsLoadTime: number;
  messagesLoadTime: number;
  totalQueries: number;
  cacheHitRate: number;
}

export function useMessagingPerformance() {
  const queryClient = useQueryClient();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    conversationsLoadTime: 0,
    messagesLoadTime: 0,
    totalQueries: 0,
    cacheHitRate: 0
  });

  useEffect(() => {
    // Monitor query performance
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'updated') {
        const query = event.query;
        
        if (query.queryKey[0] === 'conversations') {
          setMetrics(prev => ({
            ...prev,
            conversationsLoadTime: query.state.dataUpdatedAt - (query.state.fetchFailureTime || 0),
            totalQueries: prev.totalQueries + 1
          }));
        }
        
        if (query.queryKey[0] === 'messages') {
          setMetrics(prev => ({
            ...prev,
            messagesLoadTime: query.state.dataUpdatedAt - (query.state.fetchFailureTime || 0),
            totalQueries: prev.totalQueries + 1
          }));
        }
      }
    });

    return unsubscribe;
  }, [queryClient]);

  const clearCache = () => {
    queryClient.clear();
  };

  const prefetchConversations = (userId: string) => {
    queryClient.prefetchQuery({
      queryKey: ['conversations', userId],
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  return {
    metrics,
    clearCache,
    prefetchConversations
  };
}
