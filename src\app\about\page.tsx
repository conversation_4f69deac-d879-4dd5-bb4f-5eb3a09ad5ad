import Layout from "@/components/Layout";
import Image from "next/image";

export default function AboutPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative h-[50vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <div
            className="absolute inset-0 bg-black bg-opacity-50 z-10"
            aria-hidden="true"
          />
          <img
            src="https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Architectural team working"
            className="object-cover w-full h-full"
          />
        </div>
        <div className="container mx-auto px-4 z-20">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-4">About Us</h1>
          <div className="bg-primary h-1 w-20" />
        </div>
      </section>

      {/* About the Firm */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">About the Firm</h2>
            <p className="text-lg text-gray-700 mb-6">
              Senior's Archi-firm is an international architecture studio dedicated to turning ambitious visions into iconic, sustainable realities.
              Our team blends artistic insight, technical excellence, and cultural awareness to deliver transformative designs across continents.
            </p>
            <p className="text-lg text-gray-700 mb-6">
              Founded on the principles of innovation and integrity, we approach each project with a fresh perspective,
              seeking to create spaces that not only meet practical needs but also inspire and elevate the human experience.
            </p>
            <p className="text-lg text-gray-700 mb-10">
              From residential masterpieces to large-scale public works, our portfolio reflects our commitment to
              quality, sustainability, and transformative design that shapes the built environment with intelligence and lasting impact.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mt-16">
              <div>
                <h3 className="text-2xl font-bold mb-4">Our Vision</h3>
                <p className="text-gray-700">
                  To shape the built environment with intelligence, elegance, and lasting impact, creating spaces
                  that elevate human experience while honoring context and culture.
                </p>
              </div>
              <div>
                <h3 className="text-2xl font-bold mb-4">Our Mission</h3>
                <p className="text-gray-700">
                  We create spaces that elevate human experience while honoring context and culture, blending artistic
                  insight with technical excellence to deliver transformative designs across continents.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How We Work */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">How We Work</h2>

            <div className="space-y-16">
              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="md:w-1/3">
                  <div className="bg-white p-6 shadow-lg rounded-full w-20 h-20 flex items-center justify-center text-primary text-3xl font-bold">
                    01
                  </div>
                </div>
                <div className="md:w-2/3">
                  <h3 className="text-2xl font-bold mb-4">Listen & Understand</h3>
                  <p className="text-gray-700">
                    We start by understanding your vision and purpose. Our process begins with deep listening and research
                    to fully grasp your objectives, constraints, and aspirations. This foundation ensures that our designs
                    authentically solve your unique challenges.
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="md:w-1/3">
                  <div className="bg-white p-6 shadow-lg rounded-full w-20 h-20 flex items-center justify-center text-primary text-3xl font-bold">
                    02
                  </div>
                </div>
                <div className="md:w-2/3">
                  <h3 className="text-2xl font-bold mb-4">Research & Conceptualize</h3>
                  <p className="text-gray-700">
                    We analyze site conditions and cultural context to develop bespoke concepts. Our team examines environmental
                    factors, local traditions, and contemporary needs to create designs that are both contextually appropriate
                    and forward-thinking.
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="md:w-1/3">
                  <div className="bg-white p-6 shadow-lg rounded-full w-20 h-20 flex items-center justify-center text-primary text-3xl font-bold">
                    03
                  </div>
                </div>
                <div className="md:w-2/3">
                  <h3 className="text-2xl font-bold mb-4">Design & Visualize</h3>
                  <p className="text-gray-700">
                    Through detailed drawings and immersive 3D renderings, we bring ideas to life. Our visualization
                    process allows clients to experience spaces before they're built, facilitating meaningful feedback
                    and fostering confidence in design decisions.
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-8 items-center">
                <div className="md:w-1/3">
                  <div className="bg-white p-6 shadow-lg rounded-full w-20 h-20 flex items-center justify-center text-primary text-3xl font-bold">
                    04
                  </div>
                </div>
                <div className="md:w-2/3">
                  <h3 className="text-2xl font-bold mb-4">Collaborate & Build</h3>
                  <p className="text-gray-700">
                    We work closely with clients and specialists to turn visions into built reality. Our collaborative
                    approach brings together engineers, contractors, and craftspeople to ensure seamless execution
                    and the highest quality standards in construction.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Global Network */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">Our Global Network</h2>
            <p className="text-lg text-gray-700 mb-12">
              We collaborate with renowned studios and independent professionals across architecture, interior design,
              landscaping, and engineering. This global network of expertise allows us to tackle projects of any scale or
              complexity, bringing together the perfect team for each unique challenge.
            </p>

            <div className="bg-gray-100 p-10">
              <h3 className="text-2xl font-bold mb-6">Global Presence</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="flex flex-col items-center">
                  <span className="text-primary text-xl font-bold mb-2">Nairobi</span>
                  <span className="text-gray-600">Global HQ</span>
                </div>
                <div className="flex flex-col items-center">
                  <span className="text-primary text-xl font-bold mb-2">Cape Town</span>
                  <span className="text-gray-600">Satellite Studio</span>
                </div>
                <div className="flex flex-col items-center">
                  <span className="text-primary text-xl font-bold mb-2">Barcelona</span>
                  <span className="text-gray-600">Satellite Studio</span>
                </div>
                <div className="flex flex-col items-center">
                  <span className="text-primary text-xl font-bold mb-2">Dubai</span>
                  <span className="text-gray-600">Satellite Studio</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
