"use client";

import { useState } from "react";
import {
  useStripe,
  useElements,
  PaymentElement,
  AddressElement,
} from "@stripe/react-stripe-js";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, CreditCard, Loader2 } from "lucide-react";
import { supabase } from "@/lib/supabase";

interface AddPaymentMethodFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function AddPaymentMethodForm({
  onSuccess,
  onCancel,
}: AddPaymentMethodFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [makeDefault, setMakeDefault] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't loaded yet
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Confirm the setup
      const { error: setupError, setupIntent } = await stripe.confirmSetup({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/client/payments?setup_success=true`,
        },
        redirect: "if_required",
      });

      if (setupError) {
        throw new Error(
          setupError.message || "Something went wrong with your payment method"
        );
      }

      if (!setupIntent || setupIntent.status !== "succeeded") {
        throw new Error("Failed to set up payment method");
      }

      // Get the payment method ID from the setupIntent
      const paymentMethodId = setupIntent.payment_method as string;

      if (!paymentMethodId) {
        throw new Error("No payment method was created");
      }

      console.log("Payment method ID:", paymentMethodId);

      // Get the current user
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      // Retrieve payment method details directly from Stripe via our API
      const response = await fetch("/api/get-payment-method", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ paymentMethodId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to retrieve payment method details"
        );
      }

      const paymentMethodData = await response.json();
      console.log("Payment method data:", paymentMethodData);

      // Now save the payment method to your database
      const { error: dbError } = await supabase.from("payment_methods").insert({
        user_id: user.id,
        card_brand: paymentMethodData.brand,
        last_four: paymentMethodData.last4,
        expiry_date: `${paymentMethodData.exp_month}/${paymentMethodData.exp_year}`,
        is_default: makeDefault,
        stripe_payment_method_id: paymentMethodId,
      });

      if (dbError) {
        console.error("Database error:", dbError);
        throw new Error(dbError.message || "Failed to save payment method");
      }

      // If this is set as default, update other payment methods
      if (makeDefault) {
        const { error: updateError } = await supabase
          .from("payment_methods")
          .update({ is_default: false })
          .neq("stripe_payment_method_id", paymentMethodId)
          .eq("user_id", user.id);

        if (updateError) {
          console.error("Error updating other payment methods:", updateError);
        }
      }

      setSuccess(true);
      setTimeout(() => {
        onSuccess();
      }, 2000);
    } catch (err: unknown) {
      if (err instanceof Error) {
        console.error("Payment method error:", err);
        setError(
          err instanceof Error ? err.message : "An unexpected error occurred"
        );
      } else {
        console.error("Unexpected error:", err);
        setError("An unexpected error occurred");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {success ? (
        <div className="text-center py-8">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Payment Method Added
          </h3>
          <p className="text-gray-500 mb-4">
            Your payment method has been successfully added.
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Card Details
            </label>
            <PaymentElement />
          </div>

          <div className="flex items-center">
            <input
              id="make-default"
              name="make-default"
              type="checkbox"
              checked={makeDefault}
              onChange={(e) => setMakeDefault(e.target.checked)}
              className="h-4 w-4 text-primary border-gray-300 rounded"
            />
            <label
              htmlFor="make-default"
              className="ml-2 block text-sm text-gray-700"
            >
              Make this my default payment method
            </label>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!stripe || !elements || loading}
              className="flex items-center"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Add Payment Method
                </>
              )}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}
