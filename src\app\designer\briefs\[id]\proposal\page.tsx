"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useParams, useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  FileText,
  DollarSign,
  Clock,
  Calendar,
  Plus,
  Trash2,
  Save,
  Send,
  AlertCircle,
  CheckCircle,
  Target,
  User
} from "lucide-react";

interface ProjectBrief {
  id: string;
  title: string;
  description: string;
  client_name: string;
  budget_range: string;
  timeline_preference: string;
}

interface BudgetRange {
  min: number;
  max: number;
  label: string;
}

interface Milestone {
  title: string;
  description: string;
  duration_weeks: number;
  cost: number;
}

interface ProposalFormData {
  title: string;
  description: string;
  total_budget: number;
  timeline_weeks: number;
  milestones: Milestone[];
  terms_and_conditions: string;
}

export default function CreateProposal() {
  const { user } = useAuth();
  const params = useParams();
  const router = useRouter();
  const [brief, setBrief] = useState<ProjectBrief | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState<ProposalFormData>({
    title: "",
    description: "",
    total_budget: 0,
    timeline_weeks: 0,
    milestones: [
      {
        title: "Initial Consultation & Planning",
        description: "Project kickoff, requirements gathering, and initial design concepts",
        duration_weeks: 1,
        cost: 0
      }
    ],
    terms_and_conditions: `Terms and Conditions:

1. Payment Schedule:
   - 30% deposit upon contract signing
   - 40% at project midpoint
   - 30% upon project completion

2. Scope Changes:
   - Any changes to the original scope will be documented and may incur additional costs
   - Client approval required for all scope changes

3. Timeline:
   - Timeline is subject to client feedback and approval times
   - Delays due to client feedback may extend the project timeline

4. Deliverables:
   - All design files and documentation will be provided upon final payment
   - Revisions included as specified in milestone descriptions

5. Cancellation:
   - Either party may cancel with 14 days written notice
   - Work completed to date will be billed accordingly`
  });

  useEffect(() => {
    if (user && params.id) {
      fetchBriefDetails();
    }
  }, [user, params.id]);

  const fetchBriefDetails = async () => {
    if (!user || !params.id) return;

    try {
      const { data: briefData, error: briefError } = await supabase
        .from('project_briefs')
        .select(`
          id,
          title,
          description,
          budget_range,
          timeline_preference,
          profiles!project_briefs_client_id_fkey(full_name)
        `)
        .eq('id', params.id)
        .or(`assigned_designer_id.eq.${user.id},and(assigned_designer_id.is.null,status.eq.pending)`)
        .single();

      if (briefError) throw briefError;

      const profile = Array.isArray(briefData.profiles) ? briefData.profiles[0] : briefData.profiles;

      setBrief({
        ...briefData,
        client_name: profile?.full_name || 'Unknown Client'
      });

      // Set initial proposal title
      setFormData(prev => ({
        ...prev,
        title: `Design Proposal for ${briefData.title}`
      }));
    } catch (error) {
      console.error('Error fetching brief details:', error);
      setError('Failed to load brief details');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'total_budget' || name === 'timeline_weeks' ? Number(value) : value
    }));
  };

  const handleMilestoneChange = (index: number, field: keyof Milestone, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones.map((milestone, i) =>
        i === index
          ? { ...milestone, [field]: field === 'cost' || field === 'duration_weeks' ? Number(value) : value }
          : milestone
      )
    }));
  };

  const addMilestone = () => {
    setFormData(prev => ({
      ...prev,
      milestones: [
        ...prev.milestones,
        {
          title: "",
          description: "",
          duration_weeks: 1,
          cost: 0
        }
      ]
    }));
  };

  const removeMilestone = (index: number) => {
    if (formData.milestones.length > 1) {
      setFormData(prev => ({
        ...prev,
        milestones: prev.milestones.filter((_, i) => i !== index)
      }));
    }
  };

  const calculateTotalFromMilestones = () => {
    return formData.milestones.reduce((total, milestone) => total + milestone.cost, 0);
  };

  const parseClientBudgetRange = (budgetRange: string): BudgetRange => {
    const ranges: { [key: string]: BudgetRange } = {
      'under_5k': { min: 0, max: 5000, label: 'Under $5K' },
      '5k_10k': { min: 5000, max: 10000, label: '$5K - $10K' },
      '10k_25k': { min: 10000, max: 25000, label: '$10K - $25K' },
      '25k_plus': { min: 25000, max: Infinity, label: '$25K+' },
      'flexible': { min: 0, max: Infinity, label: 'Flexible' }
    };

    return ranges[budgetRange] || { min: 0, max: Infinity, label: 'Not specified' };
  };

  const getBudgetComparison = () => {
    if (!brief || !formData.total_budget) return null;

    const clientRange = parseClientBudgetRange(brief.budget_range);
    const proposalBudget = formData.total_budget;

    if (clientRange.label === 'Flexible' || clientRange.label === 'Not specified') {
      return {
        status: 'neutral',
        message: `Client budget is ${clientRange.label.toLowerCase()}`,
        color: 'text-gray-600 bg-gray-50 border-gray-200',
        icon: '💡'
      };
    }

    if (proposalBudget >= clientRange.min && proposalBudget <= clientRange.max) {
      return {
        status: 'within',
        message: `Within client budget range (${clientRange.label})`,
        color: 'text-green-600 bg-green-50 border-green-200',
        icon: '✅'
      };
    } else if (proposalBudget > clientRange.max) {
      const percentOver = Math.round(((proposalBudget - clientRange.max) / clientRange.max) * 100);
      return {
        status: 'above',
        message: `${percentOver}% above client budget range (${clientRange.label}) - Consider justifying the higher cost`,
        color: 'text-amber-600 bg-amber-50 border-amber-200',
        icon: '⚠️'
      };
    } else {
      return {
        status: 'below',
        message: `Below client budget range (${clientRange.label})`,
        color: 'text-blue-600 bg-blue-50 border-blue-200',
        icon: 'ℹ️'
      };
    }
  };

  const getMilestoneValidation = () => {
    const milestoneTotal = calculateTotalFromMilestones();
    const proposalBudget = formData.total_budget;

    if (!proposalBudget || milestoneTotal === proposalBudget) return null;

    const difference = Math.abs(milestoneTotal - proposalBudget);
    const isOver = milestoneTotal > proposalBudget;

    return {
      message: `Milestone total ${isOver ? 'exceeds' : 'is less than'} proposal budget by $${difference.toLocaleString()}`,
      color: 'text-orange-600 bg-orange-50 border-orange-200'
    };
  };

  const validateForm = () => {
    if (!formData.title.trim()) return "Proposal title is required";
    if (!formData.description.trim()) return "Proposal description is required";
    if (formData.total_budget <= 0) return "Total budget must be greater than 0";
    if (formData.timeline_weeks <= 0) return "Timeline must be greater than 0";
    if (formData.milestones.some(m => !m.title.trim())) return "All milestones must have titles";
    return null;
  };

  const handleSubmit = async (status: 'draft' | 'submitted') => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const proposalData = {
        brief_id: params.id,
        designer_id: user?.id,
        title: formData.title,
        description: formData.description,
        total_budget: formData.total_budget,
        timeline_weeks: formData.timeline_weeks,
        milestones: formData.milestones,
        terms_and_conditions: formData.terms_and_conditions,
        status,
        submitted_at: status === 'submitted' ? new Date().toISOString() : null,
        expires_at: status === 'submitted' ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() : null // 30 days
      };

      const { data, error } = await supabase
        .from('project_proposals_enhanced')
        .insert(proposalData)
        .select()
        .single();

      if (error) throw error;

      // Update brief status if proposal is submitted
      if (status === 'submitted') {
        await supabase
          .from('project_briefs')
          .update({ status: 'proposal_received' })
          .eq('id', params.id);
      }

      setSuccess(true);

      if (status === 'submitted') {
        setTimeout(() => {
          router.push('/designer/proposals');
        }, 2000);
      }
    } catch (error) {
      console.error('Error creating proposal:', error);
      setError('Failed to create proposal. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (error && !brief) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
          <p className="text-green-700">Proposal created successfully! Redirecting...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href={`/designer/briefs/${params.id}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Brief
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create Proposal</h1>
            <p className="text-gray-600">For: {brief?.title}</p>
          </div>
        </div>
      </div>

      {/* Brief Summary */}
      {brief && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-3">Project Brief Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-3">
            <div>
              <span className="text-blue-700 font-medium">Client:</span>
              <span className="ml-2 text-blue-800">{brief.client_name}</span>
            </div>
            <div>
              <span className="text-blue-700 font-medium">Client Budget:</span>
              <span className="ml-2 text-blue-800 font-semibold">{parseClientBudgetRange(brief.budget_range).label}</span>
            </div>
            <div>
              <span className="text-blue-700 font-medium">Timeline:</span>
              <span className="ml-2 text-blue-800">{brief.timeline_preference.replace(/_/g, ' ')}</span>
            </div>
          </div>

          {/* Budget Comparison Alert */}
          {getBudgetComparison() && (
            <div className={`px-3 py-2 rounded-lg border text-sm flex items-center ${getBudgetComparison()?.color}`}>
              <span className="mr-2">{getBudgetComparison()?.icon}</span>
              <span>{getBudgetComparison()?.message}</span>
            </div>
          )}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Proposal Form */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Proposal Details</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Proposal Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="Enter proposal title"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Proposal Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={6}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                  placeholder="Describe your approach to this project, your understanding of the requirements, and what makes your proposal unique..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Budget <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="number"
                      name="total_budget"
                      value={formData.total_budget || ''}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      placeholder="0"
                      min="0"
                      step="100"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Timeline (weeks) <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="number"
                      name="timeline_weeks"
                      value={formData.timeline_weeks || ''}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      placeholder="0"
                      min="1"
                      step="1"
                    />
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Milestones */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Project Milestones</h3>
              <Button
                type="button"
                onClick={addMilestone}
                variant="outline"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Milestone
              </Button>
            </div>

            <div className="space-y-4">
              {formData.milestones.map((milestone, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">Milestone {index + 1}</h4>
                    {formData.milestones.length > 1 && (
                      <Button
                        type="button"
                        onClick={() => removeMilestone(index)}
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="space-y-3">
                    <input
                      type="text"
                      value={milestone.title}
                      onChange={(e) => handleMilestoneChange(index, 'title', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      placeholder="Milestone title"
                    />

                    <textarea
                      value={milestone.description}
                      onChange={(e) => handleMilestoneChange(index, 'description', e.target.value)}
                      rows={2}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                      placeholder="Milestone description and deliverables"
                    />

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">Duration (weeks)</label>
                        <input
                          type="number"
                          value={milestone.duration_weeks}
                          onChange={(e) => handleMilestoneChange(index, 'duration_weeks', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                          min="1"
                          step="1"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">Cost ($)</label>
                        <input
                          type="number"
                          value={milestone.cost}
                          onChange={(e) => handleMilestoneChange(index, 'cost', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                          min="0"
                          step="100"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Total from milestones:</span>
                <span className="font-medium">${calculateTotalFromMilestones().toLocaleString()}</span>
              </div>
              {getMilestoneValidation() && (
                <div className={`mt-2 px-2 py-1 rounded text-xs border ${getMilestoneValidation()?.color}`}>
                  {getMilestoneValidation()?.message}
                </div>
              )}
            </div>
          </motion.div>

          {/* Terms and Conditions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Terms and Conditions</h3>
            <textarea
              name="terms_and_conditions"
              value={formData.terms_and_conditions}
              onChange={handleInputChange}
              rows={12}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent text-sm"
              placeholder="Enter your terms and conditions..."
            />
          </motion.div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
            <div className="space-y-3">
              <Button
                onClick={() => handleSubmit('draft')}
                disabled={submitting}
                variant="outline"
                className="w-full justify-start"
              >
                <Save className="h-4 w-4 mr-3" />
                Save as Draft
              </Button>
              <Button
                onClick={() => handleSubmit('submitted')}
                disabled={submitting}
                className="w-full justify-start bg-brown-600 hover:bg-brown-700 text-white"
              >
                <Send className="h-4 w-4 mr-3" />
                {submitting ? 'Submitting...' : 'Submit Proposal'}
              </Button>
            </div>
          </motion.div>

          {/* Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Proposal Summary</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Budget:</span>
                <span className="font-medium">${formData.total_budget.toLocaleString()}</span>
              </div>
              {brief && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Client Budget:</span>
                  <span className="font-medium">{parseClientBudgetRange(brief.budget_range).label}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Timeline:</span>
                <span className="font-medium">{formData.timeline_weeks} weeks</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Milestones:</span>
                <span className="font-medium">{formData.milestones.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Avg. per week:</span>
                <span className="font-medium">
                  ${formData.timeline_weeks > 0 ? Math.round(formData.total_budget / formData.timeline_weeks).toLocaleString() : '0'}
                </span>
              </div>
            </div>

            {/* Budget Status */}
            {getBudgetComparison() && formData.total_budget > 0 && (
              <div className="mt-4 pt-3 border-t border-gray-200">
                <div className={`px-3 py-2 rounded-lg border text-xs ${getBudgetComparison()?.color}`}>
                  <div className="flex items-center">
                    <span className="mr-2">{getBudgetComparison()?.icon}</span>
                    <span className="font-medium">
                      {getBudgetComparison()?.status === 'within' && 'Within Budget'}
                      {getBudgetComparison()?.status === 'above' && 'Above Budget'}
                      {getBudgetComparison()?.status === 'below' && 'Below Budget'}
                      {getBudgetComparison()?.status === 'neutral' && 'Flexible Budget'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
