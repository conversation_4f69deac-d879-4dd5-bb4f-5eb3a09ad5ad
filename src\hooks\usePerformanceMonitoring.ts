"use client";

import { useEffect, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface PerformanceMetrics {
  pageLoadTime: number;
  queryTime: number;
  renderTime: number;
  navigationTime: number;
  cacheHitRate: number;
  errorRate: number;
}

// Performance monitoring hook for tracking loading times and optimization
export function usePerformanceMonitoring() {
  // Safely get query client
  let queryClient;
  try {
    queryClient = useQueryClient();
  } catch (error) {
    // QueryClient not available, provide fallback functionality
    queryClient = null;
  }
  const metricsRef = useRef<PerformanceMetrics>({
    pageLoadTime: 0,
    queryTime: 0,
    renderTime: 0,
    navigationTime: 0,
    cacheHitRate: 0,
    errorRate: 0,
  });

  // Track page load performance
  const trackPageLoad = useCallback(() => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        metricsRef.current.pageLoadTime = loadTime;
        
        // Log performance if it's slow
        if (loadTime > 2000) {
          console.warn(`Slow page load detected: ${loadTime}ms`);
        }
        
        // Store in localStorage for analytics
        try {
          const perfData = JSON.parse(localStorage.getItem('seniors_archi_perf') || '[]');
          perfData.push({
            timestamp: Date.now(),
            loadTime,
            url: window.location.pathname,
          });
          
          // Keep only last 50 entries
          if (perfData.length > 50) {
            perfData.splice(0, perfData.length - 50);
          }
          
          localStorage.setItem('seniors_archi_perf', JSON.stringify(perfData));
        } catch (error) {
          console.warn('Failed to store performance data:', error);
        }
      }
    }
  }, []);

  // Track query performance
  const trackQueryPerformance = useCallback((queryKey: any[], startTime: number, endTime: number, fromCache: boolean) => {
    const queryTime = endTime - startTime;
    metricsRef.current.queryTime = queryTime;

    // Update cache hit rate if queryClient is available
    if (queryClient) {
      const cache = queryClient.getQueryCache();
      const totalQueries = cache.getAll().length;
      const cachedQueries = cache.getAll().filter(query => query.state.data !== undefined).length;
      metricsRef.current.cacheHitRate = totalQueries > 0 ? (cachedQueries / totalQueries) * 100 : 0;
    }
    
    // Log slow queries
    if (queryTime > 1000 && !fromCache) {
      console.warn(`Slow query detected: ${JSON.stringify(queryKey)} took ${queryTime}ms`);
    }
    
    // Log cache performance
    if (process.env.NODE_ENV === 'development') {
      console.log(`Query ${fromCache ? '(cached)' : '(fresh)'}: ${JSON.stringify(queryKey)} - ${queryTime}ms`);
    }
  }, [queryClient]);

  // Track render performance
  const trackRenderPerformance = useCallback((componentName: string, renderTime: number) => {
    metricsRef.current.renderTime = renderTime;
    
    if (renderTime > 100) {
      console.warn(`Slow render detected in ${componentName}: ${renderTime}ms`);
    }
  }, []);

  // Track navigation performance
  const trackNavigation = useCallback((from: string, to: string, startTime: number, endTime: number) => {
    const navigationTime = endTime - startTime;
    metricsRef.current.navigationTime = navigationTime;
    
    if (navigationTime > 500) {
      console.warn(`Slow navigation from ${from} to ${to}: ${navigationTime}ms`);
    }
  }, []);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const summary = { ...metricsRef.current };
    
    // Add Web Vitals if available
    if (typeof window !== 'undefined' && 'performance' in window) {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          summary.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
        }
      } catch (error) {
        console.warn('Failed to get navigation timing:', error);
      }
    }
    
    return summary;
  }, []);

  // Monitor query cache performance
  const monitorQueryCache = useCallback(() => {
    if (!queryClient) return { totalQueries: 0, staleQueries: 0, loadingQueries: 0, errorQueries: 0, cachedQueries: 0 };

    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const stats = {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      loadingQueries: queries.filter(q => q.state.isFetching).length,
      errorQueries: queries.filter(q => q.state.isError).length,
      cachedQueries: queries.filter(q => q.state.data !== undefined).length,
    };
    
    // Update error rate
    metricsRef.current.errorRate = stats.totalQueries > 0 ? (stats.errorQueries / stats.totalQueries) * 100 : 0;
    
    // Log cache stats in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Query Cache Stats:', stats);
    }
    
    return stats;
  }, [queryClient]);

  // Setup performance observers
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Track page load
    const handleLoad = () => {
      trackPageLoad();
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
    }

    // Setup performance observer for long tasks (with throttling)
    if ('PerformanceObserver' in window) {
      try {
        let lastLongTaskWarning = 0;
        const THROTTLE_INTERVAL = 5000; // Only warn every 5 seconds

        const observer = new PerformanceObserver((list) => {
          const now = Date.now();

          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'longtask') {
              // Throttle warnings to avoid console spam
              if (now - lastLongTaskWarning > THROTTLE_INTERVAL) {
                console.warn(`🐌 Long task detected: ${entry.duration.toFixed(1)}ms - Consider optimizing heavy operations`);
                lastLongTaskWarning = now;

                // Add specific suggestions based on duration
                if (entry.duration > 2000) {
                  console.warn('💡 Suggestion: Break down large operations into smaller chunks or use Web Workers');
                } else if (entry.duration > 1000) {
                  console.warn('💡 Suggestion: Consider using React.useMemo() or React.useCallback() for expensive computations');
                }
              }
            }
          });
        });

        observer.observe({ entryTypes: ['longtask'] });

        return () => {
          observer.disconnect();
          window.removeEventListener('load', handleLoad);
        };
      } catch (error) {
        console.warn('Performance observer not supported:', error);
        return () => {
          window.removeEventListener('load', handleLoad);
        };
      }
    }

    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, []); // Remove trackPageLoad dependency to prevent infinite loop

  // Monitor query cache periodically
  useEffect(() => {
    const interval = setInterval(() => {
      monitorQueryCache();
    }, 30000); // Every 30 seconds
    return () => clearInterval(interval);
  }, []); // Remove monitorQueryCache dependency to prevent infinite loop

  // Monitor tab visibility changes for debugging
  useEffect(() => {
    let lastVisibilityChange = Date.now();

    const handleVisibilityChange = () => {
      const now = Date.now();
      const timeSinceLastChange = now - lastVisibilityChange;

      if (document.visibilityState === 'visible') {
        console.log(`👁️ Tab became visible after ${timeSinceLastChange}ms`);

        // Check if any queries are in loading state
        const cache = queryClient.getQueryCache();
        const loadingQueries = cache.getAll().filter(q => q.state.isFetching);

        if (loadingQueries.length > 0) {
          console.log(`⏳ ${loadingQueries.length} queries are loading after tab switch:`,
            loadingQueries.map(q => q.queryKey));
        }

        // Check availability query specifically
        const availabilityQuery = cache.find(['availability']);
        if (availabilityQuery) {
          console.log('🔄 Availability query state:', {
            isFetching: availabilityQuery.state.isFetching,
            isLoading: availabilityQuery.state.isLoading,
            hasData: !!availabilityQuery.state.data,
            dataUpdatedAt: availabilityQuery.state.dataUpdatedAt,
            age: now - (availabilityQuery.state.dataUpdatedAt || 0)
          });
        }
      } else {
        console.log(`👁️ Tab became hidden after ${timeSinceLastChange}ms visible`);
      }

      lastVisibilityChange = now;
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [queryClient]);

  // Get performance analytics (enhanced version of getPerformanceSummary)
  const getPerformanceAnalytics = useCallback(() => {
    const summary = getPerformanceSummary();
    const cacheStats = monitorQueryCache();

    return {
      queries: {
        total: cacheStats.totalQueries,
        successRate: cacheStats.totalQueries > 0 ? ((cacheStats.totalQueries - cacheStats.errorQueries) / cacheStats.totalQueries) * 100 : 100,
        cacheHitRate: summary.cacheHitRate,
        averageTime: summary.queryTime,
        slowest: [] // Will be populated by actual slow queries
      },
      routes: {
        averageLoadTime: summary.pageLoadTime,
        slowest: [] // Will be populated by actual slow routes
      },
      recommendations: summary.errorRate > 5 ? ['High error rate detected'] : []
    };
  }, [getPerformanceSummary, monitorQueryCache]);

  // Auto-optimization function
  const autoOptimize = useCallback(() => {
    const analytics = getPerformanceAnalytics();

    // Log optimization insights in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Auto-optimization running:', {
        cacheHitRate: `${analytics.queries.cacheHitRate.toFixed(1)}%`,
        averageQueryTime: `${analytics.queries.averageTime.toFixed(2)}ms`,
        averageRouteLoadTime: `${analytics.routes.averageLoadTime.toFixed(2)}ms`
      });
    }

    // Auto-optimize based on performance metrics
    if (analytics.queries.cacheHitRate < 70) {
      console.log('🔧 Auto-optimization: Low cache hit rate detected, consider increasing stale times');
    }

    if (analytics.routes.averageLoadTime > 1000) {
      console.log('🔧 Auto-optimization: Slow route loading detected, consider more aggressive prefetching');
    }
  }, [getPerformanceAnalytics]);

  return {
    trackQueryPerformance,
    trackRenderPerformance,
    trackNavigation,
    getPerformanceSummary,
    getPerformanceAnalytics,
    autoOptimize,
    monitorQueryCache,
    metrics: metricsRef.current,
  };
}

// Hook for measuring component render time
export function useRenderTime(componentName: string) {
  const { trackRenderPerformance } = usePerformanceMonitoring();
  const startTimeRef = useRef<number>(0);

  useEffect(() => {
    startTimeRef.current = performance.now();
  });

  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTimeRef.current;
    trackRenderPerformance(componentName, renderTime);
  });

  return {
    startTime: startTimeRef.current,
    measureRender: (name?: string) => {
      const endTime = performance.now();
      const renderTime = endTime - startTimeRef.current;
      trackRenderPerformance(name || componentName, renderTime);
      return renderTime;
    },
  };
}

// Hook for measuring navigation time
export function useNavigationTime() {
  const { trackNavigation } = usePerformanceMonitoring();
  const startTimeRef = useRef<number>(0);
  const fromRouteRef = useRef<string>('');

  const startNavigation = useCallback((from: string) => {
    startTimeRef.current = performance.now();
    fromRouteRef.current = from;
  }, []);

  const endNavigation = useCallback((to: string) => {
    const endTime = performance.now();
    trackNavigation(fromRouteRef.current, to, startTimeRef.current, endTime);
    return endTime - startTimeRef.current;
  }, [trackNavigation]);

  return {
    startNavigation,
    endNavigation,
  };
}

// Hook for performance alerts
export function usePerformanceAlerts() {
  const { getPerformanceSummary } = usePerformanceMonitoring();

  const checkPerformance = useCallback(() => {
    const summary = getPerformanceSummary();
    const alerts = [];

    if (summary.pageLoadTime > 1000) { // Updated target to 1 second
      alerts.push({
        type: 'warning',
        message: `Page load time is ${summary.pageLoadTime}ms (target: <1000ms)`,
        metric: 'pageLoadTime',
        value: summary.pageLoadTime,
      });
    }

    if (summary.cacheHitRate < 80) { // Updated target to 80%
      alerts.push({
        type: 'info',
        message: `Cache hit rate is ${summary.cacheHitRate.toFixed(1)}% (target: >80%)`,
        metric: 'cacheHitRate',
        value: summary.cacheHitRate,
      });
    }

    if (summary.errorRate > 2) { // Updated target to 2%
      alerts.push({
        type: 'error',
        message: `Error rate is ${summary.errorRate.toFixed(1)}% (target: <2%)`,
        metric: 'errorRate',
        value: summary.errorRate,
      });
    }

    return alerts;
  }, [getPerformanceSummary]);

  return { checkPerformance };
}

// Hook for tracking hook performance specifically
export function useHookPerformanceTracking() {
  const hookMetrics = useRef<Map<string, { calls: number; totalTime: number; errors: number }>>(new Map());

  const trackHookCall = useCallback((hookName: string, startTime: number, endTime: number, hasError: boolean = false) => {
    const duration = endTime - startTime;
    const current = hookMetrics.current.get(hookName) || { calls: 0, totalTime: 0, errors: 0 };

    hookMetrics.current.set(hookName, {
      calls: current.calls + 1,
      totalTime: current.totalTime + duration,
      errors: current.errors + (hasError ? 1 : 0)
    });

    // Log slow hooks in development
    if (process.env.NODE_ENV === 'development' && duration > 500) {
      console.warn(`🐌 Slow hook: ${hookName} took ${duration.toFixed(2)}ms`);
    }
  }, []);

  const getHookMetrics = useCallback(() => {
    const metrics: Record<string, { avgTime: number; calls: number; errorRate: number }> = {};

    hookMetrics.current.forEach((data, hookName) => {
      metrics[hookName] = {
        avgTime: data.totalTime / data.calls,
        calls: data.calls,
        errorRate: (data.errors / data.calls) * 100
      };
    });

    return metrics;
  }, []);

  const resetMetrics = useCallback(() => {
    hookMetrics.current.clear();
  }, []);

  return { trackHookCall, getHookMetrics, resetMetrics };
}

// Hook for optimization recommendations
export function useOptimizationRecommendations() {
  const { getPerformanceSummary } = usePerformanceMonitoring();
  const { getHookMetrics } = useHookPerformanceTracking();

  const getRecommendations = useCallback(() => {
    const performance = getPerformanceSummary();
    const hookMetrics = getHookMetrics();
    const recommendations = [];

    // Page load recommendations
    if (performance.pageLoadTime > 1000) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        title: 'Slow Page Load',
        description: `Page load time is ${performance.pageLoadTime}ms. Consider implementing more aggressive prefetching.`,
        action: 'Increase prefetching and reduce initial bundle size'
      });
    }

    // Cache recommendations
    if (performance.cacheHitRate < 80) {
      recommendations.push({
        type: 'caching',
        priority: 'medium',
        title: 'Low Cache Hit Rate',
        description: `Cache hit rate is ${performance.cacheHitRate.toFixed(1)}%. Consider increasing stale times.`,
        action: 'Review and optimize cache configuration'
      });
    }

    // Hook performance recommendations
    Object.entries(hookMetrics).forEach(([hookName, metrics]) => {
      if (metrics.avgTime > 300) {
        recommendations.push({
          type: 'hook',
          priority: 'medium',
          title: `Slow Hook: ${hookName}`,
          description: `Average execution time is ${metrics.avgTime.toFixed(2)}ms`,
          action: 'Optimize query or add better caching'
        });
      }

      if (metrics.errorRate > 5) {
        recommendations.push({
          type: 'reliability',
          priority: 'high',
          title: `High Error Rate: ${hookName}`,
          description: `Error rate is ${metrics.errorRate.toFixed(1)}%`,
          action: 'Review error handling and data validation'
        });
      }
    });

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority as keyof typeof priorityOrder] - priorityOrder[a.priority as keyof typeof priorityOrder];
    });
  }, [getPerformanceSummary, getHookMetrics]);

  return { getRecommendations };
}
