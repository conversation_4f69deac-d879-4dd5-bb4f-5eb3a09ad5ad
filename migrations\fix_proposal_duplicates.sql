-- =====================================================
-- FIX PROPOSAL DUPLICATES AND ENHANCE SYSTEM
-- Run this in Supabase Dashboard SQL Editor
-- =====================================================

-- 1. First, let's identify existing duplicates
-- Find duplicate proposals (same designer + brief)
DO $$
DECLARE
    duplicate_record RECORD;
    proposals_to_withdraw UUID[];
BEGIN
    -- Find and handle duplicates one by one
    FOR duplicate_record IN
        SELECT
            designer_id,
            brief_id,
            COUNT(*) as proposal_count,
            array_agg(id ORDER BY created_at DESC) as proposal_ids
        FROM project_proposals_enhanced
        WHERE brief_id IS NOT NULL
        GROUP BY designer_id, brief_id
        HAVING COUNT(*) > 1
    LOOP
        -- Keep the first (most recent) proposal, withdraw the rest
        proposals_to_withdraw := duplicate_record.proposal_ids[2:];

        -- Update the duplicate proposals to withdrawn status
        UPDATE project_proposals_enhanced
        SET
            status = 'withdrawn',
            updated_at = NOW()
        WHERE id = ANY(proposals_to_withdraw);

        RAISE NOTICE 'Handled % duplicate proposals for designer % and brief %',
            array_length(proposals_to_withdraw, 1),
            duplicate_record.designer_id,
            duplicate_record.brief_id;
    END LOOP;
END $$;

-- 2. Add unique constraint to prevent future duplicates
-- This will prevent the same designer from submitting multiple proposals to the same brief
DO $$
BEGIN
    -- Check if constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'unique_designer_brief_proposal'
        AND table_name = 'project_proposals_enhanced'
    ) THEN
        ALTER TABLE project_proposals_enhanced
        ADD CONSTRAINT unique_designer_brief_proposal
        UNIQUE (designer_id, brief_id);

        RAISE NOTICE 'Unique constraint added successfully';
    ELSE
        RAISE NOTICE 'Unique constraint already exists';
    END IF;
EXCEPTION
    WHEN unique_violation THEN
        RAISE NOTICE 'Still have duplicates - running cleanup again...';

        -- If we still have duplicates, handle them more aggressively
        UPDATE project_proposals_enhanced
        SET status = 'withdrawn', updated_at = NOW()
        WHERE id IN (
            SELECT id FROM (
                SELECT id,
                       ROW_NUMBER() OVER (PARTITION BY designer_id, brief_id ORDER BY created_at DESC) as rn
                FROM project_proposals_enhanced
                WHERE brief_id IS NOT NULL
            ) ranked
            WHERE rn > 1
        );

        -- Try adding constraint again
        ALTER TABLE project_proposals_enhanced
        ADD CONSTRAINT unique_designer_brief_proposal
        UNIQUE (designer_id, brief_id);

        RAISE NOTICE 'Duplicates cleaned up and constraint added';
END $$;

-- 4. Update change requests table to work with enhanced proposals
-- First check if the table exists and has the right structure
DO $$
BEGIN
  -- Check if proposal_change_requests table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'proposal_change_requests') THEN
    -- Add column for enhanced proposals if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns 
                   WHERE table_name = 'proposal_change_requests' 
                   AND column_name = 'enhanced_proposal_id') THEN
      ALTER TABLE proposal_change_requests 
      ADD COLUMN enhanced_proposal_id UUID REFERENCES project_proposals_enhanced(id) ON DELETE CASCADE;
    END IF;
    
    -- Update existing change requests to link to enhanced proposals
    -- This assumes you want to migrate existing change requests
    UPDATE proposal_change_requests 
    SET enhanced_proposal_id = (
      SELECT id FROM project_proposals_enhanced 
      WHERE project_proposals_enhanced.id = proposal_change_requests.proposal_id
      LIMIT 1
    )
    WHERE enhanced_proposal_id IS NULL;
    
  ELSE
    -- Create the table if it doesn't exist
    CREATE TABLE proposal_change_requests (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      proposal_id UUID REFERENCES project_proposals(id) ON DELETE CASCADE,
      enhanced_proposal_id UUID REFERENCES project_proposals_enhanced(id) ON DELETE CASCADE,
      requested_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
      status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'implemented')),
      section TEXT NOT NULL,
      description TEXT NOT NULL,
      response TEXT,
      responded_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
      responded_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Enable RLS
    ALTER TABLE proposal_change_requests ENABLE ROW LEVEL SECURITY;
    
    -- Add policies
    CREATE POLICY "Users can view change requests for their proposals" ON proposal_change_requests
      FOR SELECT USING (
        -- For regular proposals
        (proposal_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM project_proposals
          JOIN projects ON projects.id = project_proposals.project_id
          WHERE project_proposals.id = proposal_change_requests.proposal_id
          AND (projects.client_id = auth.uid() OR project_proposals.designer_id = auth.uid())
        )) OR
        -- For enhanced proposals
        (enhanced_proposal_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM project_proposals_enhanced
          JOIN project_briefs ON project_briefs.id = project_proposals_enhanced.brief_id
          WHERE project_proposals_enhanced.id = proposal_change_requests.enhanced_proposal_id
          AND (project_briefs.client_id = auth.uid() OR project_proposals_enhanced.designer_id = auth.uid())
        ))
      );
    
    CREATE POLICY "Clients can create change requests" ON proposal_change_requests
      FOR INSERT WITH CHECK (
        requested_by = auth.uid() AND (
          -- For regular proposals
          (proposal_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM project_proposals
            JOIN projects ON projects.id = project_proposals.project_id
            WHERE project_proposals.id = proposal_change_requests.proposal_id
            AND projects.client_id = auth.uid()
          )) OR
          -- For enhanced proposals
          (enhanced_proposal_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM project_proposals_enhanced
            JOIN project_briefs ON project_briefs.id = project_proposals_enhanced.brief_id
            WHERE project_proposals_enhanced.id = proposal_change_requests.enhanced_proposal_id
            AND project_briefs.client_id = auth.uid()
          ))
        )
      );
    
    CREATE POLICY "Designers can respond to change requests" ON proposal_change_requests
      FOR UPDATE USING (
        -- For regular proposals
        (proposal_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM project_proposals
          WHERE project_proposals.id = proposal_change_requests.proposal_id
          AND project_proposals.designer_id = auth.uid()
        )) OR
        -- For enhanced proposals
        (enhanced_proposal_id IS NOT NULL AND EXISTS (
          SELECT 1 FROM project_proposals_enhanced
          WHERE project_proposals_enhanced.id = proposal_change_requests.enhanced_proposal_id
          AND project_proposals_enhanced.designer_id = auth.uid()
        ))
      );
  END IF;
END $$;

-- 5. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_proposals_enhanced_designer_brief 
ON project_proposals_enhanced(designer_id, brief_id);

CREATE INDEX IF NOT EXISTS idx_proposal_change_requests_enhanced_proposal 
ON proposal_change_requests(enhanced_proposal_id);

-- 6. Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_proposal_change_requests_updated_at 
  BEFORE UPDATE ON proposal_change_requests 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Add some helpful views for proposal management
CREATE OR REPLACE VIEW designer_proposal_summary AS
SELECT 
  p.designer_id,
  prof.full_name as designer_name,
  COUNT(*) as total_proposals,
  COUNT(*) FILTER (WHERE p.status = 'submitted') as submitted_proposals,
  COUNT(*) FILTER (WHERE p.status = 'accepted') as accepted_proposals,
  COUNT(*) FILTER (WHERE p.status = 'rejected') as rejected_proposals,
  COUNT(*) FILTER (WHERE p.status = 'draft') as draft_proposals,
  COUNT(cr.id) as total_change_requests,
  COUNT(cr.id) FILTER (WHERE cr.status = 'pending') as pending_change_requests
FROM project_proposals_enhanced p
JOIN profiles prof ON prof.id = p.designer_id
LEFT JOIN proposal_change_requests cr ON cr.enhanced_proposal_id = p.id
GROUP BY p.designer_id, prof.full_name;

-- 8. Success message
SELECT 'Proposal system fixes applied successfully!' as status,
       'Duplicates handled, constraints added, change requests updated' as details;
