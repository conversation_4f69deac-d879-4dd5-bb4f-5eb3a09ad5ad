-- =====================================================
-- STEP 2: ADD UNIQUE CONSTRAINT
-- Run this AFTER step1_fix_duplicates.sql
-- =====================================================

-- Add unique constraint to prevent future duplicates
-- This will prevent the same designer from submitting multiple proposals to the same brief
ALTER TABLE project_proposals_enhanced 
ADD CONSTRAINT unique_designer_brief_proposal 
UNIQUE (designer_id, brief_id);

-- Verify the constraint was added
SELECT 
    constraint_name, 
    table_name, 
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'project_proposals_enhanced' 
  AND constraint_name = 'unique_designer_brief_proposal';

-- Test the constraint (this should fail with a unique violation error)
-- DO NOT RUN THIS - it's just for reference
-- INSERT INTO project_proposals_enhanced (designer_id, brief_id, title, status) 
-- VALUES (
--     (SELECT designer_id FROM project_proposals_enhanced LIMIT 1),
--     (SELECT brief_id FROM project_proposals_enhanced LIMIT 1),
--     'Test Duplicate',
--     'draft'
-- );
