-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create project_agreements table
CREATE TABLE IF NOT EXISTS project_agreements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  proposal_id UUID REFERENCES project_proposals(id) ON DELETE SET NULL,
  client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  agreed_at TIMESTAMP WITH TIME ZONE NOT NULL,
  agreement_type TEXT NOT NULL, -- 'proposal_approval', 'contract', 'amendment', etc.
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'terminated', 'completed')),
  termination_reason TEXT,
  terminated_at TIMESTAMP WITH TIME ZONE,
  terminated_by <PERSON><PERSON><PERSON> REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for project_agreements table
ALTER TABLE project_agreements ENABLE ROW LEVEL SECURITY;

-- Admins can see all agreements
CREATE POLICY "Admins can see all project agreements"
  ON project_agreements
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can see agreements they're involved in
CREATE POLICY "Users can see their project agreements"
  ON project_agreements
  FOR SELECT
  USING (
    client_id = auth.uid() OR designer_id = auth.uid()
  );

-- Admins can create agreements
CREATE POLICY "Admins can create project agreements"
  ON project_agreements
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Clients can create agreements for their projects
CREATE POLICY "Clients can create project agreements"
  ON project_agreements
  FOR INSERT
  WITH CHECK (
    client_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM projects
      WHERE projects.id = project_agreements.project_id
      AND projects.client_id = auth.uid()
    )
  );

-- Admins can update agreements
CREATE POLICY "Admins can update project agreements"
  ON project_agreements
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can update agreements they're involved in
CREATE POLICY "Users can update their project agreements"
  ON project_agreements
  FOR UPDATE
  USING (
    (client_id = auth.uid() OR designer_id = auth.uid()) AND
    status = 'active'
  );
