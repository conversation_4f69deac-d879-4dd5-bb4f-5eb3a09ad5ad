"use client";

import { useState, useCallback, useEffect } from 'react';
import { usePathname } from 'next/navigation';

interface UseMobileNavigationOptions {
  autoCloseOnRouteChange?: boolean;
  debounceCloseDelay?: number;
}

export function useMobileNavigation(options: UseMobileNavigationOptions = {}) {
  const {
    autoCloseOnRouteChange = true,
    debounceCloseDelay = 150
  } = options;

  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  // Stable toggle function
  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // Stable close function
  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  // Stable open function
  const open = useCallback(() => {
    setIsOpen(true);
  }, []);

  // Auto-close on route change with debounce - only when explicitly enabled
  useEffect(() => {
    if (!autoCloseOnRouteChange || !isOpen) return;

    const timeoutId = setTimeout(() => {
      close();
    }, debounceCloseDelay);

    return () => clearTimeout(timeoutId);
  }, [pathname, autoCloseOnRouteChange, isOpen, debounceCloseDelay, close]);

  return {
    isOpen,
    toggle,
    close,
    open,
  };
}

// Hook for responsive utilities with better performance
export function useResponsiveNavigation() {
  const [screenSize, setScreenSize] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    width: 0,
    height: 0,
  });

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        width,
        height,
      });
    };

    // Initial check
    updateScreenSize();

    // Debounced resize handler
    let timeoutId: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateScreenSize, 100);
    };

    window.addEventListener('resize', debouncedResize);
    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(timeoutId);
    };
  }, []);

  return screenSize;
}

// Hook for touch detection
export function useTouchDetection() {
  const [isTouch, setIsTouch] = useState(false);

  useEffect(() => {
    const checkTouch = () => {
      setIsTouch(
        'ontouchstart' in window || 
        navigator.maxTouchPoints > 0 ||
        // @ts-ignore - for older browsers
        navigator.msMaxTouchPoints > 0
      );
    };

    checkTouch();
    
    // Listen for touch events to detect touch capability
    const handleTouchStart = () => setIsTouch(true);
    
    window.addEventListener('touchstart', handleTouchStart, { once: true });
    
    return () => {
      window.removeEventListener('touchstart', handleTouchStart);
    };
  }, []);

  return isTouch;
}
