import Layout from "@/components/Layout";
import ContactSection from "@/components/home/<USER>";
import { MapPin, Clock, Phone, Mail } from "lucide-react";

export default function ContactPage() {
  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative h-[40vh] flex items-center">
        <div className="absolute inset-0 z-0">
          <div
            className="absolute inset-0 bg-black bg-opacity-50 z-10"
            aria-hidden="true"
          />
          <img
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72"
            alt="Modern office space"
            className="object-cover w-full h-full"
          />
        </div>
        <div className="container mx-auto px-4 z-20">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-4">Contact Us</h1>
          <div className="bg-primary h-1 w-20" />
        </div>
      </section>

      {/* Quick Contact Info */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="flex items-center space-x-4">
              <Phone className="h-6 w-6 text-primary" />
              <div>
                <h3 className="font-medium">Phone</h3>
                <p className="text-gray-600">+966 55 255 2260</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Mail className="h-6 w-6 text-primary" />
              <div>
                <h3 className="font-medium">Email</h3>
                <p className="text-gray-600"><EMAIL></p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <MapPin className="h-6 w-6 text-primary" />
              <div>
                <h3 className="font-medium">Address</h3>
                <p className="text-gray-600">King Fahd Road, Al Olaya District, Riyadh</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Clock className="h-6 w-6 text-primary" />
              <div>
                <h3 className="font-medium">Working Hours</h3>
                <p className="text-gray-600">Mon-Fri: 9am-6pm</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Office Location */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">Our Office</h2>

            {/* Single centered office card with enhanced design */}
            <div className="max-w-2xl mx-auto">
              <div className="bg-white p-10 md:p-12 shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 rounded-lg">
                <div className="text-center mb-8">
                  <h3 className="text-2xl md:text-3xl font-bold mb-2 text-primary">Riyadh</h3>
                  <p className="text-gray-700 text-lg font-medium">Global Headquarters</p>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <MapPin className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                    <address className="not-italic text-gray-600 text-lg leading-relaxed">
                      King Fahd Road<br />
                      Al Olaya District<br />
                      Riyadh, Saudi Arabia
                    </address>
                  </div>

                  <div className="flex items-center space-x-4">
                    <Phone className="h-6 w-6 text-primary flex-shrink-0" />
                    <p className="text-gray-700 text-lg">
                      <span className="font-medium">Phone:</span> +966 55 255 2260
                    </p>
                  </div>

                  <div className="flex items-center space-x-4">
                    <Mail className="h-6 w-6 text-primary flex-shrink-0" />
                    <p className="text-gray-700 text-lg">
                      <span className="font-medium">Email:</span> <EMAIL>
                    </p>
                  </div>

                  <div className="flex items-center space-x-4">
                    <Clock className="h-6 w-6 text-primary flex-shrink-0" />
                    <p className="text-gray-700 text-lg">
                      <span className="font-medium">Hours:</span> Monday-Friday, 9am-6pm
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional info section */}
            <div className="text-center mt-16">
              <div className="max-w-2xl mx-auto">
                <p className="text-lg text-gray-700 leading-relaxed">
                  Located in the heart of Riyadh's business district, our headquarters serves as the central hub for all our architectural projects and client consultations across the region.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="h-[400px] relative">
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.4054421714546!2d46.6745!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjTCsDQyJzQ5LjAiTiA0NsKwNDAnMjguMiJF!5e0!3m2!1sen!2ssa!4v1635000000000!5m2!1sen!2ssa"
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
        ></iframe>
      </section>

      {/* Contact Form Section */}
      <ContactSection />
    </Layout>
  );
}

