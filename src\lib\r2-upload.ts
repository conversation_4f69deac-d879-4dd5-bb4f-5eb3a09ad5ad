/**
 * Utility functions for uploading files to Cloudflare R2
 */
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

// Bucket names
const VISION_BUILDER_BUCKET = 'vision-builder-images';
const SAMPLE_REQUEST_BUCKET = 'sample-request-files';
const DESIGNER_APPLICATIONS_BUCKET = 'designer-applications';
const PROJECT_FILES_BUCKET = 'project-files';
const DELIVERABLES_BUCKET = 'deliverables';
const PORTFOLIO_BUCKET = 'portfolio-images';
const DISPUTE_ATTACHMENTS_BUCKET = 'dispute-attachments';

/**
 * Uploads a file to Cloudflare R2
 * @param file The file to upload (File object or Buffer)
 * @param fileName The name of the file
 * @param bucketName The name of the bucket to upload to
 * @param folderPath Optional folder path within the bucket
 * @returns The URL of the uploaded file
 */
export async function uploadFileToR2(
  file: File | Buffer,
  fileName: string,
  bucketName: string,
  folderPath?: string
): Promise<string> {
  try {
    // Generate a unique file name to avoid collisions
    const uniqueFileName = `${Date.now()}-${fileName.replace(/\s+/g, '-')}`;

    // Create the full key path
    const key = folderPath
      ? `${folderPath.replace(/\/+$/, '')}/${uniqueFileName}`
      : uniqueFileName;

    // Convert File to Buffer if needed
    let fileBuffer: Buffer;
    if (file instanceof Buffer) {
      fileBuffer = file;
    } else {
      const arrayBuffer = await file.arrayBuffer();
      fileBuffer = Buffer.from(arrayBuffer);
    }

    // Determine content type
    let contentType = 'application/octet-stream';
    if (fileName.match(/\.(jpg|jpeg)$/i)) contentType = 'image/jpeg';
    else if (fileName.match(/\.png$/i)) contentType = 'image/png';
    else if (fileName.match(/\.gif$/i)) contentType = 'image/gif';
    else if (fileName.match(/\.pdf$/i)) contentType = 'application/pdf';
    else if (fileName.match(/\.doc$/i)) contentType = 'application/msword';
    else if (fileName.match(/\.docx$/i)) contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    else if (fileName.match(/\.xls$/i)) contentType = 'application/vnd.ms-excel';
    else if (fileName.match(/\.xlsx$/i)) contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    else if (fileName.match(/\.ppt$/i)) contentType = 'application/vnd.ms-powerpoint';
    else if (fileName.match(/\.pptx$/i)) contentType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    else if (fileName.match(/\.txt$/i)) contentType = 'text/plain';
    else if (fileName.match(/\.csv$/i)) contentType = 'text/csv';
    else if (fileName.match(/\.zip$/i)) contentType = 'application/zip';
    else if (fileName.match(/\.rar$/i)) contentType = 'application/x-rar-compressed';
    else if (fileName.match(/\.7z$/i)) contentType = 'application/x-7z-compressed';
    else if (fileName.match(/\.mp4$/i)) contentType = 'video/mp4';
    else if (fileName.match(/\.mp3$/i)) contentType = 'audio/mpeg';
    else if (fileName.match(/\.wav$/i)) contentType = 'audio/wav';
    else if (fileName.match(/\.svg$/i)) contentType = 'image/svg+xml';

    // Upload the file to R2
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: fileBuffer,
      ContentType: contentType,
    });

    await s3Client.send(command);

    // Return the URL of the uploaded file
    // Note: This is not a public URL, just a reference to the file in R2
    return key;
  } catch (error) {
    console.error('Error uploading file to R2:', error);
    throw new Error('Failed to upload file to storage');
  }
}

/**
 * Uploads a file for a sample request
 * @param file The file to upload
 * @param fileName The name of the file
 * @param requestId Optional request ID to use as a folder
 * @returns The URL of the uploaded file
 */
export async function uploadSampleRequestFile(
  file: File | Buffer,
  fileName: string,
  requestId?: string
): Promise<string> {
  const folderPath = requestId ? `requests/${requestId}` : 'requests';
  return uploadFileToR2(file, fileName, SAMPLE_REQUEST_BUCKET, folderPath);
}

/**
 * Uploads an image for a vision builder request
 * @param file The file or image data to upload
 * @param fileName The name of the file
 * @param requestId Optional request ID to use as a folder
 * @returns The URL of the uploaded file
 */
export async function uploadVisionBuilderImage(
  file: File | Buffer,
  fileName: string,
  requestId?: string
): Promise<string> {
  const folderPath = requestId ? `requests/${requestId}` : 'requests';
  return uploadFileToR2(file, fileName, VISION_BUILDER_BUCKET, folderPath);
}

/**
 * Uploads an AI-generated image to R2
 * @param imageDataUrl The data URL of the image
 * @param requestId Optional request ID to use as a folder
 * @returns The URL of the uploaded file
 */
export async function uploadAiGeneratedImage(
  imageDataUrl: string,
  requestId?: string
): Promise<string> {
  try {
    // Extract the base64 data from the data URL
    const matches = imageDataUrl.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      throw new Error('Invalid data URL format');
    }

    const contentType = matches[1];
    const base64Data = matches[2];
    const buffer = Buffer.from(base64Data, 'base64');

    // Determine file extension based on content type
    let fileExtension = '.png';
    if (contentType === 'image/jpeg') fileExtension = '.jpg';
    else if (contentType === 'image/gif') fileExtension = '.gif';
    else if (contentType === 'image/svg+xml') fileExtension = '.svg';

    // Generate a unique file name
    const fileName = `ai-generated-${Date.now()}${fileExtension}`;

    // Upload to R2
    const folderPath = requestId ? `ai-generated/${requestId}` : 'ai-generated';
    return uploadFileToR2(buffer, fileName, VISION_BUILDER_BUCKET, folderPath);
  } catch (error) {
    console.error('Error uploading AI-generated image to R2:', error);
    throw new Error('Failed to upload AI-generated image to storage');
  }
}

/**
 * Uploads a resume file for a designer application
 * @param file The resume file to upload
 * @param fileName The name of the file
 * @param applicationId The application ID to use as a folder
 * @returns The key of the uploaded file
 */
export async function uploadDesignerResume(
  file: File | Buffer,
  fileName: string,
  applicationId: string
): Promise<string> {
  const folderPath = `${applicationId}`;
  return uploadFileToR2(file, `resume-${fileName}`, DESIGNER_APPLICATIONS_BUCKET, folderPath);
}

/**
 * Uploads portfolio files for a designer application
 * @param file The portfolio file to upload
 * @param fileName The name of the file
 * @param applicationId The application ID to use as a folder
 * @param fileIndex The index of the file in the portfolio
 * @returns The key of the uploaded file
 */
export async function uploadDesignerPortfolioFile(
  file: File | Buffer,
  fileName: string,
  applicationId: string,
  fileIndex: number
): Promise<string> {
  const folderPath = `${applicationId}`;
  return uploadFileToR2(file, `portfolio-${fileIndex}-${fileName}`, DESIGNER_APPLICATIONS_BUCKET, folderPath);
}

/**
 * Uploads project files (deliverables, attachments, etc.)
 * @param file The file to upload
 * @param fileName The name of the file
 * @param projectId The project ID to use as a folder
 * @param fileType The type of file (deliverable, attachment, etc.)
 * @returns The key of the uploaded file
 */
export async function uploadProjectFile(
  file: File | Buffer,
  fileName: string,
  projectId: string,
  fileType: 'deliverable' | 'attachment' | 'submission' | 'message' | 'inspiration'
): Promise<string> {
  const folderPath = `${projectId}/${fileType}`;
  return uploadFileToR2(file, fileName, PROJECT_FILES_BUCKET, folderPath);
}

/**
 * Uploads milestone deliverable files
 * @param file The deliverable file to upload
 * @param fileName The name of the file
 * @param projectId The project ID
 * @param milestoneId The milestone ID
 * @returns The key of the uploaded file
 */
export async function uploadMilestoneDeliverable(
  file: File | Buffer,
  fileName: string,
  projectId: string,
  milestoneId: string
): Promise<string> {
  const folderPath = `${projectId}/${milestoneId}`;
  return uploadFileToR2(file, fileName, DELIVERABLES_BUCKET, folderPath);
}

/**
 * Uploads portfolio images
 * @param file The image file to upload
 * @param fileName The name of the file
 * @param projectId The project ID (optional)
 * @returns The key of the uploaded file
 */
export async function uploadPortfolioImage(
  file: File | Buffer,
  fileName: string,
  projectId?: string
): Promise<string> {
  const folderPath = projectId ? `projects/${projectId}` : 'general';
  return uploadFileToR2(file, fileName, PORTFOLIO_BUCKET, folderPath);
}

/**
 * Uploads dispute attachment files
 * @param file The attachment file to upload
 * @param fileName The name of the file
 * @param disputeId The dispute ID
 * @returns The key of the uploaded file
 */
export async function uploadDisputeAttachment(
  file: File | Buffer,
  fileName: string,
  disputeId: string
): Promise<string> {
  const folderPath = `disputes/${disputeId}`;
  return uploadFileToR2(file, fileName, DISPUTE_ATTACHMENTS_BUCKET, folderPath);
}





/**
 * Uploads certificate files for a designer application
 * @param file The certificate file to upload
 * @param fileName The name of the file
 * @param applicationId The application ID to use as a folder
 * @param fileIndex The index of the file in the certificates
 * @returns The key of the uploaded file
 */
export async function uploadDesignerCertificate(
  file: File | Buffer,
  fileName: string,
  applicationId: string,
  fileIndex: number
): Promise<string> {
  const folderPath = `${applicationId}`;
  return uploadFileToR2(file, `certificate-${fileIndex}-${fileName}`, DESIGNER_APPLICATIONS_BUCKET, folderPath);
}

/**
 * Gets a public URL for a file in R2
 * @param key The key of the file in R2
 * @param bucketName The name of the bucket
 * @returns The public URL of the file
 */
export function getR2PublicUrl(key: string, bucketName: string): string {
  // Check if the public URL is set
  const publicBaseUrl = process.env.CLOUDFLARE_R2_PUBLIC_URL;

  if (!publicBaseUrl) {
    console.warn('CLOUDFLARE_R2_PUBLIC_URL is not set in environment variables');
    // Return a placeholder URL or a reference to a proxy API route
    return `/api/r2-proxy?bucket=${bucketName}&key=${encodeURIComponent(key)}`;
  }

  // Return the public URL
  return `${publicBaseUrl}/${bucketName}/${key}`;
}

/**
 * Gets a public URL for a designer application file
 * @param key The key of the file in R2
 * @returns The public URL of the file
 */
export function getDesignerApplicationFileUrl(key: string): string {
  return getR2PublicUrl(key, DESIGNER_APPLICATIONS_BUCKET);
}

/**
 * Gets a public URL for a sample request file
 * @param key The key of the file in R2
 * @returns The public URL of the file
 */
export function getSampleRequestFileUrl(key: string): string {
  return getR2PublicUrl(key, SAMPLE_REQUEST_BUCKET);
}

/**
 * Gets a public URL for a vision builder image
 * @param key The key of the file in R2
 * @returns The public URL of the file
 */
export function getVisionBuilderImageUrl(key: string): string {
  return getR2PublicUrl(key, VISION_BUILDER_BUCKET);
}

/**
 * Gets a public URL for a project file
 * @param key The key of the file in R2
 * @returns The public URL of the file
 */
export function getProjectFileUrl(key: string): string {
  return getR2PublicUrl(key, PROJECT_FILES_BUCKET);
}

/**
 * Gets a public URL for a deliverable file
 * @param key The key of the file in R2
 * @returns The public URL of the file
 */
export function getDeliverableFileUrl(key: string): string {
  return getR2PublicUrl(key, DELIVERABLES_BUCKET);
}

/**
 * Gets a public URL for a portfolio image
 * @param key The key of the file in R2
 * @returns The public URL of the file
 */
export function getPortfolioImageUrl(key: string): string {
  return getR2PublicUrl(key, PORTFOLIO_BUCKET);
}

/**
 * Gets a public URL for a dispute attachment
 * @param key The key of the file in R2
 * @returns The public URL of the file
 */
export function getDisputeAttachmentUrl(key: string): string {
  return getR2PublicUrl(key, DISPUTE_ATTACHMENTS_BUCKET);
}
