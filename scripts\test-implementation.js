/**
 * Test script to validate the implementation
 * Run with: node scripts/test-implementation.js
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Implementation...\n');

// Test 1: Check if all required files exist
const requiredFiles = [
  'src/lib/seo.ts',
  'src/lib/recaptcha.ts',
  'src/components/NewsletterSignup.tsx',
  'src/components/CookieConsent.tsx',
  'src/app/privacy-policy/page.tsx',
  'src/app/newsletter/confirm/page.tsx',
  'src/app/blog/page.tsx',
  'src/app/blog/[slug]/page.tsx',
  'src/app/admin/newsletter/page.tsx',
  'src/app/admin/blog/page.tsx',
  'src/app/api/newsletter/subscribe/route.ts',
  'src/app/api/cookie-consent/route.ts',
  'src/app/robots.txt/route.ts',
  'src/app/sitemap.ts',
  'migrations/create_newsletter_and_blog_tables.sql'
];

console.log('📁 Checking required files...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file}`);
    missingFiles.push(file);
  }
});

if (missingFiles.length === 0) {
  console.log('\n✅ All required files are present!\n');
} else {
  console.log(`\n❌ Missing ${missingFiles.length} files:\n`);
  missingFiles.forEach(file => console.log(`   - ${file}`));
  console.log('');
}

// Test 2: Check environment variables
console.log('🔧 Checking environment variables...');
const envFile = '.env.local';

if (fs.existsSync(envFile)) {
  const envContent = fs.readFileSync(envFile, 'utf8');
  
  const requiredEnvVars = [
    'NEXT_PUBLIC_RECAPTCHA_SITE_KEY',
    'RECAPTCHA_SECRET_KEY',
    'RESEND_API_KEY',
    'NEWSLETTER_FROM_EMAIL',
    'NEWSLETTER_FROM_NAME',
    'NEXT_PUBLIC_COOKIE_DOMAIN',
    'NEXT_PUBLIC_SITE_URL'
  ];

  let missingEnvVars = [];

  requiredEnvVars.forEach(envVar => {
    if (envContent.includes(envVar)) {
      console.log(`✅ ${envVar}`);
    } else {
      console.log(`❌ ${envVar}`);
      missingEnvVars.push(envVar);
    }
  });

  if (missingEnvVars.length === 0) {
    console.log('\n✅ All required environment variables are configured!\n');
  } else {
    console.log(`\n❌ Missing ${missingEnvVars.length} environment variables:\n`);
    missingEnvVars.forEach(envVar => console.log(`   - ${envVar}`));
    console.log('');
  }
} else {
  console.log('❌ .env.local file not found\n');
}

// Test 3: Check package.json dependencies
console.log('📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const requiredDependencies = [
  'resend',
  '@supabase/ssr',
  'framer-motion',
  'lucide-react'
];

let missingDeps = [];

requiredDependencies.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep}`);
  } else {
    console.log(`❌ ${dep}`);
    missingDeps.push(dep);
  }
});

if (missingDeps.length === 0) {
  console.log('\n✅ All required dependencies are installed!\n');
} else {
  console.log(`\n❌ Missing ${missingDeps.length} dependencies:\n`);
  missingDeps.forEach(dep => console.log(`   - ${dep}`));
  console.log('\nRun: npm install ' + missingDeps.join(' ') + '\n');
}

// Test 4: Check for deprecated imports
console.log('🔍 Checking for deprecated imports...');
const deprecatedImports = [
  '@supabase/auth-helpers-nextjs'
];

let foundDeprecated = [];

function checkFileForDeprecated(filePath) {
  if (fs.existsSync(filePath) && filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
    const content = fs.readFileSync(filePath, 'utf8');
    deprecatedImports.forEach(dep => {
      if (content.includes(dep)) {
        foundDeprecated.push({ file: filePath, import: dep });
      }
    });
  }
}

// Check API files
const apiDir = 'src/app/api';
if (fs.existsSync(apiDir)) {
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else {
        checkFileForDeprecated(filePath);
      }
    });
  }
  walkDir(apiDir);
}

if (foundDeprecated.length === 0) {
  console.log('✅ No deprecated imports found!\n');
} else {
  console.log('❌ Found deprecated imports:\n');
  foundDeprecated.forEach(item => {
    console.log(`   - ${item.import} in ${item.file}`);
  });
  console.log('');
}

// Summary
console.log('📊 Implementation Summary:');
console.log('========================');
console.log(`✅ Files: ${requiredFiles.length - missingFiles.length}/${requiredFiles.length}`);
console.log(`✅ Dependencies: ${requiredDependencies.length - missingDeps.length}/${requiredDependencies.length}`);
console.log(`✅ Clean imports: ${foundDeprecated.length === 0 ? 'Yes' : 'No'}`);

if (missingFiles.length === 0 && missingDeps.length === 0 && foundDeprecated.length === 0) {
  console.log('\n🎉 Implementation is complete and ready for testing!');
  console.log('\nNext steps:');
  console.log('1. Run the database migration');
  console.log('2. Test newsletter signup');
  console.log('3. Test reCAPTCHA on forms');
  console.log('4. Test admin interfaces');
  console.log('5. Check SEO meta tags');
} else {
  console.log('\n⚠️  Please fix the issues above before proceeding.');
}

console.log('\n🚀 Happy coding!');
