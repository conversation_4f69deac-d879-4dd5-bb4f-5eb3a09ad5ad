import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      persistSession: false
    }
  }
);

/**
 * API route to run the profiles table migration
 * Adds missing columns needed for designer applications
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      return NextResponse.json(
        { error: 'Service role key not configured' },
        { status: 500 }
      );
    }

    console.log('Starting profiles table migration...');

    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'migrations', 'add_missing_designer_columns_to_profiles.sql');
    
    if (!fs.existsSync(migrationPath)) {
      return NextResponse.json(
        { error: 'Migration file not found' },
        { status: 404 }
      );
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    const { data, error } = await supabaseAdmin.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (error) {
      console.error('Migration error:', error);
      
      // Try alternative approach - execute statements one by one
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (const statement of statements) {
        try {
          if (statement.toLowerCase().includes('alter table') || 
              statement.toLowerCase().includes('create index') ||
              statement.toLowerCase().includes('comment on')) {
            
            const { error: stmtError } = await supabaseAdmin.rpc('exec_sql', {
              sql: statement + ';'
            });

            if (stmtError) {
              console.warn(`Statement warning: ${statement}`, stmtError);
              results.push({
                statement: statement.substring(0, 100) + '...',
                status: 'warning',
                error: stmtError.message
              });
              errorCount++;
            } else {
              results.push({
                statement: statement.substring(0, 100) + '...',
                status: 'success'
              });
              successCount++;
            }
          }
        } catch (stmtError) {
          console.error(`Statement error: ${statement}`, stmtError);
          results.push({
            statement: statement.substring(0, 100) + '...',
            status: 'error',
            error: stmtError instanceof Error ? stmtError.message : 'Unknown error'
          });
          errorCount++;
        }
      }

      return NextResponse.json({
        success: successCount > 0,
        message: `Migration completed with ${successCount} successful statements and ${errorCount} warnings/errors`,
        results: results,
        summary: {
          total: statements.length,
          successful: successCount,
          errors: errorCount
        }
      }, { status: successCount > 0 ? 200 : 500 });
    }

    console.log('Migration completed successfully');

    return NextResponse.json({
      success: true,
      message: 'Profiles table migration completed successfully',
      data: data
    }, { status: 200 });

  } catch (error) {
    console.error('Error running migration:', error);
    return NextResponse.json(
      { 
        error: 'Migration failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check current profiles table schema
 */
export async function GET(request: NextRequest) {
  try {
    // Get current table schema
    const { data: columns, error } = await supabaseAdmin
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_name', 'profiles')
      .order('ordinal_position');

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch table schema' },
        { status: 500 }
      );
    }

    // Check which columns are missing
    const requiredColumns = [
      'bio', 'location', 'specialization', 'experience', 'years_experience',
      'portfolio_url', 'resume_url', 'portfolio_files', 'applied_at',
      'approved_by', 'rejected_by', 'temp_password', 'is_active',
      'availability', 'last_sign_in_at', 'stripe_customer_id',
      'invite_code', 'invited_by'
    ];

    const existingColumns = columns.map(col => col.column_name);
    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));

    return NextResponse.json({
      success: true,
      currentColumns: columns,
      missingColumns: missingColumns,
      needsMigration: missingColumns.length > 0
    }, { status: 200 });

  } catch (error) {
    console.error('Error checking schema:', error);
    return NextResponse.json(
      { error: 'Failed to check schema' },
      { status: 500 }
    );
  }
}
