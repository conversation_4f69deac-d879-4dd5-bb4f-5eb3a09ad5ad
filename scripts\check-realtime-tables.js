const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkRealtimeTables() {
  console.log('🔍 Checking database tables and realtime status...\n');

  try {
    // 1. Check what tables exist
    console.log('📋 Checking existing tables:');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');

    if (tablesError) {
      console.error('❌ Error fetching tables:', tablesError);
      return;
    }

    const tableNames = tables.map(t => t.table_name);
    console.log('✅ Found tables:', tableNames.join(', '));

    // 2. Check which tables we're trying to subscribe to
    const subscribedTables = [
      'projects',
      'project_proposals_enhanced', 
      'project_briefs',
      'messages',
      'notifications',
      'admin_messages',
      'portfolio_projects',
      'profiles',
      'transactions',
      'conversations',
      'conversation_messages',
      'project_messages'
    ];

    console.log('\n📡 Checking realtime subscription targets:');
    const missingTables = [];
    const existingTables = [];

    subscribedTables.forEach(table => {
      if (tableNames.includes(table)) {
        existingTables.push(table);
        console.log(`✅ ${table} - EXISTS`);
      } else {
        missingTables.push(table);
        console.log(`❌ ${table} - MISSING`);
      }
    });

    // 3. Test basic queries on existing tables
    console.log('\n🧪 Testing basic queries on existing tables:');
    for (const table of existingTables.slice(0, 5)) { // Test first 5 tables
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${table} - Query failed: ${error.message}`);
        } else {
          console.log(`✅ ${table} - Query successful (${data?.length || 0} rows)`);
        }
      } catch (err) {
        console.log(`❌ ${table} - Query error: ${err.message}`);
      }
    }

    // 4. Check RLS policies
    console.log('\n🔒 Checking RLS policies for key tables:');
    const keyTables = ['projects', 'notifications', 'messages'].filter(t => existingTables.includes(t));
    
    for (const table of keyTables) {
      try {
        const { data: policies, error } = await supabase
          .from('pg_policies')
          .select('policyname, permissive, roles, cmd, qual')
          .eq('tablename', table);
        
        if (error) {
          console.log(`❌ ${table} - Could not check RLS policies: ${error.message}`);
        } else {
          console.log(`📋 ${table} - Found ${policies?.length || 0} RLS policies`);
          if (policies && policies.length > 0) {
            policies.forEach(policy => {
              console.log(`   - ${policy.policyname} (${policy.cmd})`);
            });
          }
        }
      } catch (err) {
        console.log(`❌ ${table} - RLS check error: ${err.message}`);
      }
    }

    // 5. Summary
    console.log('\n📊 SUMMARY:');
    console.log(`✅ Existing tables: ${existingTables.length}/${subscribedTables.length}`);
    console.log(`❌ Missing tables: ${missingTables.length}`);
    
    if (missingTables.length > 0) {
      console.log('\n🚨 MISSING TABLES THAT NEED REALTIME:');
      missingTables.forEach(table => console.log(`   - ${table}`));
      console.log('\n💡 These tables need to be created before realtime subscriptions will work.');
    }

    if (existingTables.length > 0) {
      console.log('\n✅ TABLES READY FOR REALTIME:');
      existingTables.forEach(table => console.log(`   - ${table}`));
      console.log('\n💡 Make sure realtime is enabled for these tables in Supabase Dashboard > Database > Replication');
    }

  } catch (error) {
    console.error('❌ Error checking realtime tables:', error);
  }
}

checkRealtimeTables();
