-- =====================================================
-- DESIGNER PAYOUT METHODS SCHEMA
-- International support with multiple payment providers
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto"; -- For encryption

-- 1. Designer Payout Methods Table
CREATE TABLE IF NOT EXISTS designer_payout_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Method type and status
    method_type VARCHAR(50) NOT NULL CHECK (method_type IN ('bank_account', 'paypal', 'stripe_connect', 'wise', 'international_wire')),
    is_default BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'failed', 'requires_action')),
    verification_notes TEXT,
    
    -- Bank account details (some encrypted for security)
    bank_name TEXT,
    account_holder_name TEXT NOT NULL,
    routing_number TEXT, -- For US banks
    account_number_encrypted TEXT, -- Encrypted account number
    account_type VARCHAR(20) CHECK (account_type IN ('checking', 'savings', 'business')),
    
    -- International banking details
    swift_code TEXT, -- For international transfers
    iban TEXT, -- For European/international accounts
    bank_address TEXT,
    bank_country VARCHAR(3), -- ISO country code
    bank_currency VARCHAR(3) DEFAULT 'USD', -- ISO currency code
    
    -- PayPal details
    paypal_email TEXT,
    paypal_payer_id TEXT,
    
    -- Stripe Connect details
    stripe_account_id TEXT,
    stripe_account_status VARCHAR(20),
    
    -- Wise (formerly TransferWise) details
    wise_profile_id TEXT,
    wise_recipient_id TEXT,
    
    -- Payout preferences
    minimum_payout_amount DECIMAL(10,2) DEFAULT 100.00,
    payout_frequency VARCHAR(20) DEFAULT 'weekly' CHECK (payout_frequency IN ('daily', 'weekly', 'bi_weekly', 'monthly')),
    auto_payout_enabled BOOLEAN DEFAULT TRUE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE,
    last_verification_attempt TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    UNIQUE(designer_id, method_type, account_number_encrypted), -- Prevent duplicates
    CHECK (
        (method_type = 'bank_account' AND bank_name IS NOT NULL AND account_number_encrypted IS NOT NULL) OR
        (method_type = 'paypal' AND paypal_email IS NOT NULL) OR
        (method_type = 'stripe_connect' AND stripe_account_id IS NOT NULL) OR
        (method_type = 'wise' AND wise_profile_id IS NOT NULL) OR
        (method_type = 'international_wire' AND swift_code IS NOT NULL AND account_number_encrypted IS NOT NULL)
    )
);

-- 2. Payout Verification Attempts Table
CREATE TABLE IF NOT EXISTS payout_verification_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payout_method_id UUID NOT NULL REFERENCES designer_payout_methods(id) ON DELETE CASCADE,
    verification_type VARCHAR(50) NOT NULL, -- 'micro_deposit', 'document_upload', 'api_verification'
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'success', 'failed', 'expired')),
    verification_data JSONB, -- Store verification details
    error_message TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Enhanced Transactions Table (add payout tracking)
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payout_method_id UUID REFERENCES designer_payout_methods(id);
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payout_batch_id TEXT;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payout_fee DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS external_payout_id TEXT; -- Provider transaction ID
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payout_currency VARCHAR(3) DEFAULT 'USD';
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS exchange_rate DECIMAL(10,6) DEFAULT 1.000000;
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS payout_provider VARCHAR(50); -- 'stripe', 'paypal', 'wise', 'manual'

-- 4. Payout Batches Table (for batch processing)
CREATE TABLE IF NOT EXISTS payout_batches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id TEXT UNIQUE NOT NULL,
    provider VARCHAR(50) NOT NULL, -- 'stripe', 'paypal', 'wise', 'manual'
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    total_amount DECIMAL(10,2) NOT NULL,
    total_fee DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    transaction_count INTEGER DEFAULT 0,
    processed_by UUID REFERENCES profiles(id),
    processed_at TIMESTAMP WITH TIME ZONE,
    external_batch_id TEXT, -- Provider batch ID
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_designer_payout_methods_designer_id ON designer_payout_methods(designer_id);
CREATE INDEX IF NOT EXISTS idx_designer_payout_methods_default ON designer_payout_methods(designer_id, is_default) WHERE is_default = true;
CREATE INDEX IF NOT EXISTS idx_designer_payout_methods_verified ON designer_payout_methods(verification_status);
CREATE INDEX IF NOT EXISTS idx_transactions_payout_method ON transactions(payout_method_id);
CREATE INDEX IF NOT EXISTS idx_transactions_payout_batch ON transactions(payout_batch_id);
CREATE INDEX IF NOT EXISTS idx_payout_batches_status ON payout_batches(status);
CREATE INDEX IF NOT EXISTS idx_payout_batches_provider ON payout_batches(provider);

-- 6. Create functions for encryption/decryption
CREATE OR REPLACE FUNCTION encrypt_account_number(account_number TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN encode(pgp_sym_encrypt(account_number, current_setting('app.encryption_key', true)), 'base64');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION decrypt_account_number(encrypted_account_number TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN pgp_sym_decrypt(decode(encrypted_account_number, 'base64'), current_setting('app.encryption_key', true));
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL; -- Return NULL if decryption fails
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Row Level Security (RLS) policies
ALTER TABLE designer_payout_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE payout_verification_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payout_batches ENABLE ROW LEVEL SECURITY;

-- Designers can only see their own payout methods
CREATE POLICY "Designers can view own payout methods" ON designer_payout_methods
    FOR SELECT USING (
        designer_id = auth.uid() OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'quality', 'manager'))
    );

CREATE POLICY "Designers can insert own payout methods" ON designer_payout_methods
    FOR INSERT WITH CHECK (designer_id = auth.uid());

CREATE POLICY "Designers can update own payout methods" ON designer_payout_methods
    FOR UPDATE USING (designer_id = auth.uid());

-- Admins can view all payout methods
CREATE POLICY "Admins can manage all payout methods" ON designer_payout_methods
    FOR ALL USING (
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'quality', 'manager'))
    );

-- Similar policies for verification attempts
CREATE POLICY "Users can view own verification attempts" ON payout_verification_attempts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM designer_payout_methods dpm
            WHERE dpm.id = payout_method_id
            AND (dpm.designer_id = auth.uid() OR EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'quality', 'manager')))
        )
    );

-- Payout batches - admin only
CREATE POLICY "Admins can manage payout batches" ON payout_batches
    FOR ALL USING (
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'quality', 'manager'))
    );

-- 8. Insert default encryption key setting (should be set via environment variable)
-- This is just a placeholder - in production, set via environment variable
-- ALTER DATABASE your_database_name SET app.encryption_key = 'your-secret-encryption-key-here';

COMMENT ON TABLE designer_payout_methods IS 'Stores designer payout method information with encryption for sensitive data';
COMMENT ON TABLE payout_verification_attempts IS 'Tracks verification attempts for payout methods';
COMMENT ON TABLE payout_batches IS 'Manages batch payout processing for efficiency';
