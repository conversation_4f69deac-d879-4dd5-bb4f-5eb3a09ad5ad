-- =====================================================
-- COMPREHENSIVE SCHEMA ENHANCEMENTS
-- Run this in Supabase Dashboard SQL Editor
-- =====================================================

-- 1. ENHANCE DESIGNER APPLICATIONS TABLE
-- Add missing columns for better application management
ALTER TABLE designer_applications ADD COLUMN IF NOT EXISTS 
  interview_scheduled_at TIMESTAMP WITH TIME ZONE,
  interview_notes TEXT,
  admin_notes TEXT,
  rejection_reason TEXT,
  status_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status_updated_by UUID REFERENCES profiles(id),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  tags TEXT[], -- For categorization
  internal_notes TEXT; -- Private admin notes

-- Update status enum with more granular options
ALTER TABLE designer_applications 
  DROP CONSTRAINT IF EXISTS designer_applications_application_status_check;
ALTER TABLE designer_applications 
  ADD CONSTRAINT designer_applications_application_status_check 
  CHECK (application_status IN ('pending', 'under_review', 'interview_scheduled', 'approved', 'rejected', 'withdrawn', 'on_hold'));

-- Add trigger to update status_updated_at
CREATE OR REPLACE FUNCTION update_application_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.application_status IS DISTINCT FROM NEW.application_status THEN
    NEW.status_updated_at = NOW();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_designer_applications_status_timestamp
  BEFORE UPDATE ON designer_applications
  FOR EACH ROW
  EXECUTE FUNCTION update_application_status_timestamp();

-- 2. ENHANCE TRACKING REQUESTS TABLE
-- Add admin management and workflow fields
ALTER TABLE tracking_requests ADD COLUMN IF NOT EXISTS
  assigned_to UUID REFERENCES profiles(id),
  admin_notes TEXT,
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  estimated_completion_date DATE,
  client_notified_at TIMESTAMP WITH TIME ZONE,
  internal_status TEXT DEFAULT 'new' CHECK (internal_status IN ('new', 'assigned', 'in_progress', 'review', 'completed', 'cancelled')),
  tags TEXT[], -- For categorization
  follow_up_required BOOLEAN DEFAULT FALSE,
  next_follow_up_date DATE,
  completion_notes TEXT,
  client_satisfaction_rating INTEGER CHECK (client_satisfaction_rating >= 1 AND client_satisfaction_rating <= 5),
  client_feedback TEXT;

-- Add trigger to update updated_at when status changes
CREATE OR REPLACE FUNCTION update_tracking_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.status IS DISTINCT FROM NEW.status OR OLD.internal_status IS DISTINCT FROM NEW.internal_status THEN
    NEW.updated_at = NOW();
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
      NEW.completed_at = NOW();
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_tracking_requests_status_timestamp
  BEFORE UPDATE ON tracking_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_tracking_status_timestamp();

-- 3. CREATE COMMUNICATION TRACKING TABLE
-- Track all communications for applications and tracking requests
CREATE TABLE IF NOT EXISTS application_communications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  application_id UUID REFERENCES designer_applications(id) ON DELETE CASCADE,
  communication_type TEXT CHECK (communication_type IN ('email', 'sms', 'whatsapp', 'internal_note', 'phone_call')),
  subject TEXT,
  content TEXT NOT NULL,
  sent_by UUID REFERENCES profiles(id),
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  recipient_email TEXT,
  recipient_phone TEXT,
  status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'failed', 'bounced')),
  template_used TEXT,
  metadata JSONB, -- For storing additional data like email IDs, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS tracking_communications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tracking_request_id UUID REFERENCES tracking_requests(id) ON DELETE CASCADE,
  communication_type TEXT CHECK (communication_type IN ('email', 'sms', 'whatsapp', 'internal_note', 'phone_call')),
  subject TEXT,
  content TEXT NOT NULL,
  sent_by UUID REFERENCES profiles(id),
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  recipient_email TEXT,
  recipient_phone TEXT,
  status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'failed', 'bounced')),
  template_used TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. CREATE EMAIL TEMPLATES TABLE
-- Store reusable email templates for different scenarios
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  category TEXT CHECK (category IN ('designer_application', 'tracking_request', 'general')),
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  variables TEXT[], -- Available variables for template
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default email templates
INSERT INTO email_templates (name, category, subject, content, variables) VALUES
('application_under_review', 'designer_application', 'Your Designer Application is Under Review', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #8B4513;">Application Update</h2>
  <p>Dear {{full_name}},</p>
  <p>Thank you for your interest in joining Seniors Architecture Firm. We wanted to update you on the status of your designer application.</p>
  <p><strong>Current Status:</strong> Under Review</p>
  <p>Our team is currently reviewing your application and portfolio. We will contact you within the next {{review_timeframe}} with next steps.</p>
  <p>If you have any questions, please don''t hesitate to reach out.</p>
  <p>Best regards,<br>Seniors Architecture Firm Team</p>
</div>', 
ARRAY['full_name', 'review_timeframe']),

('tracking_status_update', 'tracking_request', 'Update on Your {{request_type}} Request', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #8B4513;">Request Update</h2>
  <p>Dear {{name}},</p>
  <p>We have an update on your {{request_type}} request (Tracking #{{tracking_number}}).</p>
  <p><strong>Current Status:</strong> {{status}}</p>
  <p>{{status_message}}</p>
  {{#estimated_completion}}
  <p><strong>Estimated Completion:</strong> {{estimated_completion}}</p>
  {{/estimated_completion}}
  <p>You can track your request at any time using this link: <a href="{{tracking_url}}">Track Request</a></p>
  <p>Best regards,<br>Seniors Architecture Firm Team</p>
</div>', 
ARRAY['name', 'request_type', 'tracking_number', 'status', 'status_message', 'estimated_completion', 'tracking_url']);

-- 5. ADD INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_designer_applications_status ON designer_applications(application_status);
CREATE INDEX IF NOT EXISTS idx_designer_applications_created_at ON designer_applications(created_at);
CREATE INDEX IF NOT EXISTS idx_designer_applications_priority ON designer_applications(priority);

CREATE INDEX IF NOT EXISTS idx_tracking_requests_status ON tracking_requests(status);
CREATE INDEX IF NOT EXISTS idx_tracking_requests_internal_status ON tracking_requests(internal_status);
CREATE INDEX IF NOT EXISTS idx_tracking_requests_assigned_to ON tracking_requests(assigned_to);
CREATE INDEX IF NOT EXISTS idx_tracking_requests_priority ON tracking_requests(priority);
CREATE INDEX IF NOT EXISTS idx_tracking_requests_created_at ON tracking_requests(created_at);

CREATE INDEX IF NOT EXISTS idx_application_communications_application_id ON application_communications(application_id);
CREATE INDEX IF NOT EXISTS idx_tracking_communications_tracking_request_id ON tracking_communications(tracking_request_id);

-- 6. ADD RLS POLICIES
-- Application Communications Policies
ALTER TABLE application_communications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage all application communications" ON application_communications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Tracking Communications Policies
ALTER TABLE tracking_communications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage all tracking communications" ON tracking_communications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Email Templates Policies
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage email templates" ON email_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- 7. CREATE HELPER FUNCTIONS
-- Function to get application statistics
CREATE OR REPLACE FUNCTION get_application_stats()
RETURNS TABLE (
  total_applications BIGINT,
  pending_applications BIGINT,
  under_review_applications BIGINT,
  approved_applications BIGINT,
  rejected_applications BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_applications,
    COUNT(*) FILTER (WHERE application_status = 'pending') as pending_applications,
    COUNT(*) FILTER (WHERE application_status = 'under_review') as under_review_applications,
    COUNT(*) FILTER (WHERE application_status = 'approved') as approved_applications,
    COUNT(*) FILTER (WHERE application_status = 'rejected') as rejected_applications
  FROM designer_applications;
END;
$$ LANGUAGE plpgsql;

-- Function to get tracking request statistics
CREATE OR REPLACE FUNCTION get_tracking_stats()
RETURNS TABLE (
  total_requests BIGINT,
  new_requests BIGINT,
  in_progress_requests BIGINT,
  completed_requests BIGINT,
  vision_builder_requests BIGINT,
  sample_requests BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_requests,
    COUNT(*) FILTER (WHERE internal_status = 'new') as new_requests,
    COUNT(*) FILTER (WHERE internal_status = 'in_progress') as in_progress_requests,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_requests,
    COUNT(*) FILTER (WHERE request_type = 'vision_builder') as vision_builder_requests,
    COUNT(*) FILTER (WHERE request_type = 'sample_request') as sample_requests
  FROM tracking_requests;
END;
$$ LANGUAGE plpgsql;
