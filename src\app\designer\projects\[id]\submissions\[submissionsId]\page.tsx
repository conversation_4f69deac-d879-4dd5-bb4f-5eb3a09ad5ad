"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import QualityReviewIntegration from "@/components/quality/QualityReviewIntegration";
import {
  ArrowLeft,
  Download,
  File,
  MessageSquare,
  Clock,
  AlertCircle,
  CheckCircle,
  Upload,
  FileText,
  ExternalLink
} from "lucide-react";

type Submission = {
  id: string;
  title: string;
  description: string;
  status: string;
  created_at: string;
  revision_requested: boolean;
  feedback: string | null;
  project_id: string;
  project_title: string;
};

type SubmissionFile = {
  id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
  created_at: string;
};

export default function SubmissionDetail() {
  const { id, submissionId } = useParams();
  const router = useRouter();
  const { user } = useOptimizedAuth();
  const [submission, setSubmission] = useState<Submission | null>(null);
  const [files, setFiles] = useState<SubmissionFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fileUrls, setFileUrls] = useState<Record<string, string>>({});

  useEffect(() => {
    if (user && id && submissionId) {
      fetchSubmissionData();
    }
  }, [user, id, submissionId]);

  const fetchSubmissionData = async () => {
    try {
      // Fetch submission details
      const { data: submissionData, error: submissionError } = await supabase
        .from('submissions')
        .select(`
          id,
          title,
          description,
          status,
          created_at,
          revision_requested,
          feedback,
          project_id,
          projects(title)
        `)
        .eq('id', submissionId)
        .eq('designer_id', user?.id)
        .single();

      if (submissionError) throw submissionError;
      
      if (!submissionData) {
        router.push(`/designer/projects/${id}`);
        return;
      }

      setSubmission({
        id: submissionData.id,
        title: submissionData.title,
        description: submissionData.description,
        status: submissionData.status,
        created_at: submissionData.created_at,
        revision_requested: submissionData.revision_requested,
        feedback: submissionData.feedback,
        project_id: submissionData.project_id,
        project_title: submissionData.projects?.[0]?.title || 'Unknown Project'
      });

      // Fetch submission files
      const { data: filesData, error: filesError } = await supabase
        .from('submission_files')
        .select('*')
        .eq('submission_id', submissionId)
        .order('created_at', { ascending: false });

      if (filesError) throw filesError;

      setFiles(filesData || []);

      // Generate download URLs for each file
      if (filesData && filesData.length > 0) {
        const urls: Record<string, string> = {};
        
        for (const file of filesData) {
          const { data } = await supabase.storage
            .from('project-files')
            .createSignedUrl(file.file_path, 60 * 60); // 1 hour expiry
            
          if (data) {
            urls[file.id] = data.signedUrl;
          }
        }
        
        setFileUrls(urls);
      }
    } catch (error: Error | unknown) {
      console.error('Error fetching submission data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load submission data');
    } finally {
      setLoading(false);
    }
  };  const formatDate = (dateString: string) => {    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'review':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatusLabel = (status: string) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('zip') || fileType.includes('archive')) return '🗜️';
    if (fileType.includes('photoshop')) return '🎨';
    if (fileType.includes('illustrator')) return '✏️';
    return '📁';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="bg-red-50 text-red-500 p-4 rounded-lg flex items-center">
        <AlertCircle className="h-5 w-5 mr-2" />
        <p>Submission not found or you don't have access to it.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-8">
        <Link href={`/designer/projects/${id}`}>
          <Button variant="ghost" className="mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{submission.title}</h1>
      </div>
      
      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <p>{error}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Submission Details */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-lg font-semibold">Submission Details</h2>
              <div className="flex items-center">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(submission.status)}`}>
                  {formatStatusLabel(submission.status)}
                </span>
                
                {submission.revision_requested && (
                  <span className="ml-2 bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                    Revision Requested
                  </span>
                )}
              </div>
            </div>
            
            <div className="p-6">
              <div className="flex items-center text-sm text-gray-500 mb-6">
                <Clock className="h-4 w-4 mr-1" />
                <span>Submitted: {formatDate(submission.created_at)}</span>
                
                <span className="mx-2">•</span>
                <span>Project: {submission.project_title}</span>
              </div>
              
              {submission.description && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                  <p className="text-gray-700 whitespace-pre-line">{submission.description}</p>
                </div>
              )}
              
              {/* Files Section */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-4">Files</h3>
                
                {files.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <File className="h-10 w-10 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500">No files were attached to this submission</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {files.map((file) => (
                      <div key={file.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                        <div className="flex items-center">
                          <div className="text-2xl mr-3">{getFileIcon(file.file_type)}</div>
                          <div>
                            <p className="font-medium text-sm">{file.file_name}</p>
                            <p className="text-xs text-gray-500">
                              {formatFileSize(file.file_size)} • {formatDate(file.created_at)}
                            </p>
                          </div>
                        </div>
                        
                        {fileUrls[file.id] && (
                          <div className="flex space-x-2">
                            <a 
                              href={fileUrls[file.id]} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-gray-500 hover:text-primary"
                            >
                              <ExternalLink className="h-5 w-5" />
                            </a>
                            <a 
                              href={fileUrls[file.id]} 
                              download={file.file_name}
                              className="text-gray-500 hover:text-primary"
                            >
                              <Download className="h-5 w-5" />
                            </a>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Feedback Section */}
          {submission.feedback && (
            <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
              <div className="p-6 border-b">
                <h2 className="text-lg font-semibold">Client Feedback</h2>
              </div>
              
              <div className="p-6">
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                  <FileText className="h-5 w-5 text-yellow-500 mb-2" />
                  <p className="text-gray-700 whitespace-pre-line">{submission.feedback}</p>
                </div>
                
                {submission.revision_requested && (
                  <div className="mt-6 text-center">
                    <p className="text-gray-500 mb-4">The client has requested revisions for this submission.</p>
                    <Link href={`/designer/projects/${id}/submissions/new`}>
                      <Button className="flex items-center">
                        <Upload className="h-4 w-4 mr-2" />
                        Submit Revision
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Quality Review Integration */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Quality Review</h2>
            </div>
            <div className="p-6">
              <QualityReviewIntegration
                submissionId={submission.id}
                projectId={submission.project_id}
                role="designer"
                compact={false}
              />
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          {/* Status Info */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Status</h2>
            </div>
            
            <div className="p-6">
              <div className="mb-4">
                <div className={`w-full h-2 rounded-full ${
                  submission.status === 'approved' ? 'bg-green-100' :
                  submission.status === 'rejected' ? 'bg-red-100' :
                  submission.status === 'review' ? 'bg-purple-100' :
                  'bg-yellow-100'
                }`}>
                  <div className={`h-2 rounded-full ${
                    submission.status === 'approved' ? 'bg-green-500 w-full' :
                    submission.status === 'rejected' ? 'bg-red-500 w-full' :
                    submission.status === 'review' ? 'bg-purple-500 w-3/4' :
                    'bg-yellow-500 w-1/2'
                  }`}></div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <CheckCircle className={`h-5 w-5 mr-2 ${
                    ['pending', 'review', 'approved', 'rejected'].includes(submission.status) 
                      ? 'text-green-500' 
                      : 'text-gray-300'
                  }`} />
                  <span className={`text-sm ${
                    ['pending', 'review', 'approved', 'rejected'].includes(submission.status) 
                      ? 'text-gray-700 font-medium' 
                      : 'text-gray-400'
                  }`}>Submitted</span>
                               </div>
                
                <div className="flex items-center">
                  <CheckCircle className={`h-5 w-5 mr-2 ${
                    ['approved', 'rejected'].includes(submission.status) 
                      ? 'text-green-500' 
                      : 'text-gray-300'
                  }`} />
                  <span className={`text-sm ${
                    ['approved', 'rejected'].includes(submission.status) 
                      ? 'text-gray-700 font-medium' 
                      : 'text-gray-400'
                  }`}>Decision Made</span>
                </div>
                
                {submission.revision_requested && (
                  <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
                    <span className="text-sm text-red-500 font-medium">Revision Requested</span>
                  </div>
                )}
              </div>
              
              <div className="mt-6 pt-6 border-t">
                <h3 className="text-sm font-medium text-gray-500 mb-2">What happens next?</h3>
                
                {submission.status === 'pending' && (
                  <p className="text-sm text-gray-600">
                    Your submission is waiting for the client to review. You'll be notified when they provide feedback.
                  </p>
                )}
                
                {submission.status === 'review' && (
                  <p className="text-sm text-gray-600">
                    The client is currently reviewing your submission. You'll receive feedback soon.
                  </p>
                )}
                
                {submission.status === 'approved' && (
                  <p className="text-sm text-gray-600">
                    Great job! The client has approved this submission. You can now proceed with the next steps of the project.
                  </p>
                )}
                
                {submission.status === 'rejected' && !submission.revision_requested && (
                  <p className="text-sm text-gray-600">
                    The client has rejected this submission. Please review their feedback and consider your next steps.
                  </p>
                )}
                
                {submission.revision_requested && (
                  <p className="text-sm text-gray-600">
                    The client has requested revisions. Please review their feedback and submit a revised version.
                  </p>
                )}
              </div>
            </div>
          </div>
          
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Quick Actions</h2>
            </div>
            
            <div className="p-6 space-y-4">
              <Link href={`/designer/projects/${id}/submissions/new`}>
                <Button className="w-full justify-between">
                  Submit New Version
                  <Upload className="h-4 w-4 ml-2" />
                </Button>
              </Link>
              
              <Link href={`/designer/messages?project=${id}`}>
                <Button variant="outline" className="w-full justify-between">
                  Message Client
                  <MessageSquare className="h-4 w-4 ml-2" />
                </Button>
              </Link>
              
              <Link href={`/designer/projects/${id}`}>
                <Button variant="outline" className="w-full justify-between">
                  View Project
                  <ArrowLeft className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
