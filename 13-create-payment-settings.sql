-- =====================================================
-- SCRIPT 13: CREATE MISSING PAYMENT SETTINGS TABLE
-- =====================================================

-- Create payment_settings table
CREATE TABLE IF NOT EXISTS payment_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_methods TEXT[] NOT NULL DEFAULT ARRAY['paypal', 'credit_card', 'bank_transfer'],
  default_payment_method TEXT NOT NULL DEFAULT 'paypal',
  paypal_enabled BOOLEAN NOT NULL DEFAULT true,
  stripe_enabled BOOLEAN NOT NULL DEFAULT true,
  bank_transfer_enabled BOOLEAN NOT NULL DEFAULT true,
  minimum_payment_amount DECIMAL(10,2) NOT NULL DEFAULT 50.0,
  maximum_payment_amount DECIMAL(10,2),
  payment_processing_timeout_minutes INTEGER NOT NULL DEFAULT 30,
  auto_refund_enabled BOOLEAN NOT NULL DEFAULT false,
  auto_refund_days INTEGER DEFAULT 7,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES profiles(id)
);

-- Insert default payment settings
INSERT INTO payment_settings (
  payment_methods,
  default_payment_method,
  paypal_enabled,
  stripe_enabled,
  bank_transfer_enabled
) VALUES (
  ARRAY['paypal', 'credit_card', 'bank_transfer'],
  'paypal',
  true,
  true,
  true
) ON CONFLICT DO NOTHING;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_payment_settings_updated_at ON payment_settings(updated_at DESC);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON payment_settings TO authenticated;

-- Verify completion
SELECT 'Script 13 completed: Payment settings table created' as status;
