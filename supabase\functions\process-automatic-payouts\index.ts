import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { designerId, force = false } = await req.json()

    // Get platform settings
    const { data: settings } = await supabase
      .from('platform_settings')
      .select('*')
      .single()

    const automaticPayoutsEnabled = settings?.enable_automatic_payouts ?? true
    const payoutSchedule = settings?.payout_schedule || 'weekly'
    const minimumPayoutAmount = settings?.minimum_payout_amount || 50

    if (!automaticPayoutsEnabled && !force) {
      return new Response(
        JSON.stringify({ message: 'Automatic payouts are disabled' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get designers eligible for payout
    let designerQuery = supabase
      .from('designer_payout_queue')
      .select(`
        designer_id,
        designer:profiles!designer_id(id, full_name, email),
        stripe_account:designer_stripe_accounts!designer_id(stripe_account_id, account_status),
        amount
      `)
      .eq('status', 'pending')

    if (designerId) {
      designerQuery = designerQuery.eq('designer_id', designerId)
    }

    const { data: payoutQueue, error: queueError } = await designerQuery

    if (queueError) {
      console.error('Error fetching payout queue:', queueError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch payout queue' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!payoutQueue || payoutQueue.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No pending payouts found' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Group by designer and calculate totals
    const designerPayouts = new Map()
    
    payoutQueue.forEach(item => {
      const designerId = item.designer_id
      if (!designerPayouts.has(designerId)) {
        designerPayouts.set(designerId, {
          designer: item.designer,
          stripeAccount: item.stripe_account,
          totalAmount: 0,
          items: []
        })
      }
      
      const payout = designerPayouts.get(designerId)
      payout.totalAmount += item.amount
      payout.items.push(item)
    })

    const results = []

    // Process payouts for each designer
    for (const [designerId, payoutData] of designerPayouts) {
      try {
        const { designer, stripeAccount, totalAmount, items } = payoutData

        // Check minimum payout amount
        if (totalAmount < minimumPayoutAmount && !force) {
          results.push({
            designerId,
            designerName: designer.full_name,
            status: 'skipped',
            reason: `Amount $${totalAmount} below minimum $${minimumPayoutAmount}`,
            amount: totalAmount
          })
          continue
        }

        // Check if designer has valid Stripe account
        if (!stripeAccount || !stripeAccount.stripe_account_id || stripeAccount.account_status !== 'active') {
          results.push({
            designerId,
            designerName: designer.full_name,
            status: 'failed',
            reason: 'No active Stripe Connect account',
            amount: totalAmount
          })
          continue
        }

        // Create Stripe transfer
        const transfer = await stripe.transfers.create({
          amount: Math.round(totalAmount * 100), // Convert to cents
          currency: 'usd',
          destination: stripeAccount.stripe_account_id,
          metadata: {
            designer_id: designerId,
            payout_items: items.length.toString(),
            platform: 'architecture-platform'
          }
        })

        // Create payout transaction record
        const { data: payoutTransaction, error: transactionError } = await supabase
          .from('transactions')
          .insert({
            transaction_id: transfer.id,
            amount: totalAmount,
            type: 'payout',
            status: 'completed',
            designer_id: designerId,
            stripe_transfer_id: transfer.id,
            payout_method: 'stripe_connect',
            notes: `Automatic payout via Stripe Connect - ${items.length} items`,
            processed_at: new Date().toISOString()
          })
          .select()
          .single()

        if (transactionError) {
          console.error('Error creating payout transaction:', transactionError)
          results.push({
            designerId,
            designerName: designer.full_name,
            status: 'failed',
            reason: 'Database error',
            amount: totalAmount
          })
          continue
        }

        // Update payout queue items as processed
        const queueItemIds = items.map(item => item.id)
        const { error: updateError } = await supabase
          .from('designer_payout_queue')
          .update({
            status: 'processed',
            payout_transaction_id: payoutTransaction.id,
            processed_at: new Date().toISOString()
          })
          .in('id', queueItemIds)

        if (updateError) {
          console.error('Error updating payout queue:', updateError)
        }

        // Update milestones as paid
        const milestoneIds = items
          .filter(item => item.milestone_id)
          .map(item => item.milestone_id)

        if (milestoneIds.length > 0) {
          const { error: milestoneError } = await supabase
            .from('project_milestones')
            .update({
              status: 'paid',
              paid_at: new Date().toISOString()
            })
            .in('id', milestoneIds)

          if (milestoneError) {
            console.error('Error updating milestones:', milestoneError)
          }
        }

        // Create notification for designer
        const { error: notificationError } = await supabase
          .from('notifications')
          .insert({
            user_id: designerId,
            type: 'payout',
            title: 'Payout Processed',
            content: `Your payout of $${totalAmount.toFixed(2)} has been processed and transferred to your account.`,
            link: '/designer/dashboard/earnings',
            read: false
          })

        if (notificationError) {
          console.error('Error creating notification:', notificationError)
        }

        results.push({
          designerId,
          designerName: designer.full_name,
          status: 'success',
          amount: totalAmount,
          transferId: transfer.id,
          itemsProcessed: items.length
        })

      } catch (error) {
        console.error(`Error processing payout for designer ${designerId}:`, error)
        results.push({
          designerId,
          designerName: payoutData.designer.full_name,
          status: 'failed',
          reason: error.message,
          amount: payoutData.totalAmount
        })
      }
    }

    // Create admin notification summary
    const successCount = results.filter(r => r.status === 'success').length
    const failedCount = results.filter(r => r.status === 'failed').length
    const totalPaidOut = results
      .filter(r => r.status === 'success')
      .reduce((sum, r) => sum + r.amount, 0)

    if (successCount > 0 || failedCount > 0) {
      const { data: admins } = await supabase
        .from('profiles')
        .select('id')
        .eq('role', 'admin')

      for (const admin of admins || []) {
        await supabase
          .from('notifications')
          .insert({
            user_id: admin.id,
            type: 'admin',
            title: 'Automatic Payout Summary',
            content: `Processed ${successCount} successful payouts ($${totalPaidOut.toFixed(2)}) and ${failedCount} failed attempts.`,
            link: '/admin/finance/payouts',
            read: false
          })
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        summary: {
          totalProcessed: results.length,
          successful: successCount,
          failed: failedCount,
          totalAmount: totalPaidOut
        },
        results
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in automatic payout processing:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
