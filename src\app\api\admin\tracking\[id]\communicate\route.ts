import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';
import { Resend } from 'resend';

const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

/**
 * API route for sending communications to tracking request clients
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { 
      subject, 
      content, 
      communication_type = 'email',
      template_id,
      update_status,
      send_copy_to_admin = false 
    } = await request.json();

    // TODO: Implement proper admin authentication
    console.log('Admin communicate API called');

    // Get tracking request details
    const { data: trackingRequest, error: trackingError } = await supabaseServerClient
      .from('tracking_requests')
      .select('*')
      .eq('id', id)
      .single();

    if (trackingError || !trackingRequest) {
      return NextResponse.json({ error: 'Tracking request not found' }, { status: 404 });
    }

    let emailContent = content;
    let emailSubject = subject;

    // If using a template, fetch and process it
    if (template_id) {
      const { data: template, error: templateError } = await supabaseServerClient
        .from('email_templates')
        .select('*')
        .eq('id', template_id)
        .single();

      if (!templateError && template) {
        emailSubject = template.subject;
        emailContent = template.content;

        // Replace template variables
        const variables = {
          name: trackingRequest.name,
          tracking_number: trackingRequest.tracking_number,
          request_type: trackingRequest.request_type === 'vision_builder' ? 'Vision Builder' : 'Sample Request',
          status: trackingRequest.status,
          status_message: getStatusMessage(trackingRequest.status),
          estimated_completion: trackingRequest.estimated_completion_date 
            ? new Date(trackingRequest.estimated_completion_date).toLocaleDateString()
            : '',
          tracking_url: `${process.env.NEXT_PUBLIC_SITE_URL}/track?tracking=${trackingRequest.tracking_number}`
        };

        Object.entries(variables).forEach(([key, value]) => {
          const regex = new RegExp(`{{${key}}}`, 'g');
          emailSubject = emailSubject.replace(regex, value || '');
          emailContent = emailContent.replace(regex, value || '');
        });
      }
    }

    // Send email
    let emailResult;
    if (communication_type === 'email') {
      const recipients = [trackingRequest.email];
      if (send_copy_to_admin) {
        recipients.push('<EMAIL>');
      }

      emailResult = await resend.emails.send({
        from: 'Seniors Architecture Firm <<EMAIL>>',
        to: recipients,
        subject: emailSubject,
        html: emailContent
      });
    }

    // Record communication in database
    const { data: communication, error: commError } = await supabaseServerClient
      .from('tracking_communications')
      .insert({
        tracking_request_id: id,
        communication_type,
        subject: emailSubject,
        content: emailContent,
        sent_by: 'admin', // TODO: Use actual admin user ID
        recipient_email: trackingRequest.email,
        status: emailResult?.error ? 'failed' : 'sent',
        template_used: template_id,
        metadata: emailResult ? { resend_id: emailResult.data?.id } : null
      })
      .select()
      .single();

    if (commError) {
      console.error('Error recording communication:', commError);
    }

    // Update tracking request status if specified
    if (update_status) {
      await supabaseServerClient
        .from('tracking_requests')
        .update({ 
          status: update_status,
          client_notified_at: new Date().toISOString()
        })
        .eq('id', id);
    }

    return NextResponse.json({
      success: true,
      communication_id: communication?.id,
      email_id: emailResult?.data?.id,
      message: 'Communication sent successfully'
    });

  } catch (error) {
    console.error('Error sending communication:', error);
    return NextResponse.json(
      { error: 'Failed to send communication' },
      { status: 500 }
    );
  }
}

/**
 * GET - Fetch communication history for a tracking request
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // TODO: Implement proper admin authentication
    console.log('Admin communicate GET API called');

    // Fetch communication history
    const { data: communications, error } = await supabaseServerClient
      .from('tracking_communications')
      .select(`
        *,
        sent_by_profile:profiles!sent_by(full_name, email)
      `)
      .eq('tracking_request_id', id)
      .order('sent_at', { ascending: false });

    if (error) {
      console.error('Error fetching communications:', error);
      return NextResponse.json({ error: 'Failed to fetch communications' }, { status: 500 });
    }

    return NextResponse.json({ communications });

  } catch (error) {
    console.error('Error in GET /api/admin/tracking/[id]/communicate:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getStatusMessage(status: string): string {
  switch (status) {
    case 'submitted':
      return 'Your request has been received and is in our queue for processing.';
    case 'processing':
      return 'Our team is currently working on your request.';
    case 'completed':
      return 'Your request has been completed successfully.';
    case 'cancelled':
      return 'Your request has been cancelled.';
    default:
      return 'Your request is being processed.';
  }
}
