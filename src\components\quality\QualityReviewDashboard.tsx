'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Clock, AlertTriangle, CheckCircle, XCircle, Eye, Star } from 'lucide-react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

interface QualityReview {
  id: string;
  project_id: string;
  designer_id: string;
  review_type: string;
  status: 'pending' | 'in_review' | 'approved' | 'needs_revision' | 'rejected';
  overall_score?: number;
  sla_deadline: string;
  created_at: string;
  reviewed_at?: string;
  sla_status: 'on_time' | 'due_soon' | 'overdue';
  projects: {
    title: string;
    status: string;
    client: {
      full_name: string;
      avatar_url?: string;
    };
  };
  designer: {
    full_name: string;
    avatar_url?: string;
    email: string;
  };
  project_milestones?: {
    title: string;
    description: string;
    amount: number;
  };
}

interface QualityReviewDashboardProps {
  className?: string;
}

export default function QualityReviewDashboard({ className }: QualityReviewDashboardProps) {
  const { user, profile } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    in_review: 0,
    overdue: 0,
    completed_today: 0
  });

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchReviews();
      fetchStats();
    }
  }, [user, profile, activeTab]);

  const fetchReviews = async () => {
    try {
      setLoading(true);

      const response = await fetch(`/api/quality/reviews?status=${activeTab}&limit=20`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setReviews(data.reviews || []);
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/quality/reviews?status=all&limit=1000', {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        const allReviews = data.reviews || [];

        const today = new Date().toDateString();

        setStats({
          total: allReviews.length,
          pending: allReviews.filter(r => r.status === 'pending').length,
          in_review: allReviews.filter(r => r.status === 'in_review').length,
          overdue: allReviews.filter(r => r.sla_status === 'overdue').length,
          completed_today: allReviews.filter(r =>
            r.reviewed_at && new Date(r.reviewed_at).toDateString() === today
          ).length
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getSLABadgeColor = (slaStatus: string) => {
    switch (slaStatus) {
      case 'overdue': return 'destructive';
      case 'due_soon': return 'warning';
      default: return 'secondary';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'needs_revision': return 'warning';
      case 'rejected': return 'destructive';
      case 'in_review': return 'default';
      default: return 'secondary';
    }
  };

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffHours = Math.ceil((deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffHours < 0) {
      return `${Math.abs(diffHours)}h overdue`;
    } else if (diffHours < 24) {
      return `${diffHours}h remaining`;
    } else {
      const diffDays = Math.ceil(diffHours / 24);
      return `${diffDays}d remaining`;
    }
  };

  const handleStartReview = async (reviewId: string) => {
    try {
      const response = await fetch(`/api/quality/reviews/${reviewId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: 'in_review' })
      });

      if (response.ok) {
        fetchReviews();
        fetchStats();
      }
    } catch (error) {
      console.error('Error starting review:', error);
    }
  };

  if (!profile || profile.role !== 'quality_team') {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Access denied. Quality team members only.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Total Reviews</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="text-sm font-medium">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">In Review</p>
                <p className="text-2xl font-bold text-blue-600">{stats.in_review}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{stats.overdue}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">Completed Today</p>
                <p className="text-2xl font-bold text-green-600">{stats.completed_today}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reviews Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pending">Pending ({stats.pending})</TabsTrigger>
          <TabsTrigger value="in_review">In Review ({stats.in_review})</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="needs_revision">Needs Revision</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : reviews.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">No reviews found for this status.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {reviews.map((review) => (
                <Card key={review.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-lg">{review.projects.title}</CardTitle>
                        <p className="text-sm text-muted-foreground">
                          Designer: {review.designer.full_name} • Client: {review.projects.client.full_name}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getSLABadgeColor(review.sla_status)}>
                          {formatTimeRemaining(review.sla_deadline)}
                        </Badge>
                        <Badge variant={getStatusBadgeColor(review.status)}>
                          {review.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm text-muted-foreground">
                          Type: <span className="font-medium">{review.review_type}</span>
                        </div>
                        {review.overall_score && (
                          <div className="flex items-center space-x-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="text-sm font-medium">{review.overall_score}/5</span>
                          </div>
                        )}
                        {review.project_milestones && (
                          <div className="text-sm text-muted-foreground">
                            Milestone: <span className="font-medium">{review.project_milestones.title}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {review.status === 'pending' && (
                          <Button 
                            size="sm" 
                            onClick={() => handleStartReview(review.id)}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            Start Review
                          </Button>
                        )}
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
