import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

interface ChatSession {
  id: string;
  session_id: string;
  user_id?: string;
  visitor_name?: string;
  visitor_email?: string;
  status: 'waiting' | 'active' | 'ended' | 'abandoned';
  admin_joined_at?: string;
  created_at: string;
  updated_at: string;
  total_messages: number;
}

interface ChatMessage {
  id: string;
  session_id: string;
  sender_type: 'visitor' | 'admin' | 'system';
  sender_id?: string;
  content: string;
  message_type: 'text' | 'system';
  is_read: boolean;
  created_at: string;
}

interface AdminStatus {
  is_online: boolean;
  status_message: string;
  last_activity: string;
}

export function useLiveChat() {
  const { user } = useAuth();
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [adminStatus, setAdminStatus] = useState<AdminStatus>({
    is_online: false,
    status_message: 'Available for chat',
    last_activity: new Date().toISOString()
  });
  const [isLoading, setIsLoading] = useState(true);
  const realtimeChannelRef = useRef<any>(null);

  // Fetch chat sessions
  const fetchSessions = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('live_chat_sessions')
        .select(`
          *,
          profiles:user_id (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSessions(data || []);
    } catch (error) {
      console.error('Error fetching sessions:', error);
    }
  }, []);

  // Fetch messages for a session
  const fetchMessages = useCallback(async (sessionId: string) => {
    try {
      const { data, error } = await supabase
        .from('live_chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      setMessages(data || []);

      // Mark visitor messages as read
      await supabase
        .from('live_chat_messages')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('session_id', sessionId)
        .eq('sender_type', 'visitor')
        .eq('is_read', false);

    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  }, []);

  // Get admin status
  const fetchAdminStatus = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('admin_chat_status')
        .select('*')
        .single();

      if (!error && data) {
        // Consider admin online if they were active in the last 5 minutes
        const lastActivity = new Date(data.last_activity);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        setAdminStatus({
          is_online: data.is_online && lastActivity > fiveMinutesAgo,
          status_message: data.status_message,
          last_activity: data.last_activity
        });
      }
    } catch (error) {
      console.error('Error fetching admin status:', error);
    }
  }, []);

  // Initialize admin status (for admin users)
  const initializeAdminStatus = useCallback(async () => {
    if (!user?.id) return;

    try {
      // Try to get existing status
      let { data, error } = await supabase
        .from('admin_chat_status')
        .select('*')
        .eq('admin_id', user.id)
        .single();

      if (error && error.code === 'PGRST116') {
        // No existing status, create one
        const { data: newStatus, error: createError } = await supabase
          .from('admin_chat_status')
          .insert({
            admin_id: user.id,
            is_online: true,
            status_message: 'Available for chat',
            last_activity: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) throw createError;
        data = newStatus;
      } else if (error) {
        throw error;
      }

      if (data) {
        setAdminStatus({
          is_online: data.is_online,
          status_message: data.status_message,
          last_activity: data.last_activity
        });

        // Update to online when initializing
        await supabase
          .from('admin_chat_status')
          .update({
            is_online: true,
            last_activity: new Date().toISOString()
          })
          .eq('admin_id', user.id);

        setAdminStatus(prev => ({ ...prev, is_online: true }));
      }
    } catch (error) {
      console.error('Error initializing admin status:', error);
    }
  }, [user?.id]);

  // Toggle admin online status
  const toggleAdminStatus = useCallback(async () => {
    if (!user?.id) return;

    const newStatus = !adminStatus.is_online;
    
    try {
      await supabase
        .from('admin_chat_status')
        .update({
          is_online: newStatus,
          last_activity: new Date().toISOString()
        })
        .eq('admin_id', user.id);

      setAdminStatus(prev => ({ ...prev, is_online: newStatus }));
    } catch (error) {
      console.error('Error updating admin status:', error);
    }
  }, [user?.id, adminStatus.is_online]);

  // Send message
  const sendMessage = useCallback(async (sessionId: string, content: string, senderType: 'visitor' | 'admin' = 'visitor') => {
    if (!content.trim()) return null;

    try {
      const messageData = {
        session_id: sessionId,
        sender_type: senderType,
        sender_id: senderType === 'admin' ? user?.id : null,
        content: content.trim(),
        message_type: 'text'
      };

      const { data, error } = await supabase
        .from('live_chat_messages')
        .insert([messageData])
        .select()
        .single();

      if (error) throw error;

      // Update local messages if this is the selected session
      if (selectedSession?.id === sessionId) {
        setMessages(prev => [...prev, data]);
      }

      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  }, [user?.id, selectedSession?.id]);

  // Create new chat session
  const createChatSession = useCallback(async (visitorInfo: { name?: string; email?: string; sessionId: string }) => {
    try {
      const sessionData = {
        session_id: visitorInfo.sessionId,
        user_id: user?.id || null,
        visitor_name: user ? null : visitorInfo.name || null,
        visitor_email: user ? null : visitorInfo.email || null,
        status: 'waiting',
        user_agent: navigator.userAgent,
        referrer_url: document.referrer,
        current_page: window.location.href
      };

      const { data: session, error } = await supabase
        .from('live_chat_sessions')
        .insert([sessionData])
        .select()
        .single();

      if (error) throw error;

      // Send welcome message
      const welcomeMessage = {
        session_id: session.id,
        sender_type: 'system',
        content: adminStatus.is_online 
          ? 'Hello! Welcome to Senior\'s Archi-firm. An admin will be with you shortly.'
          : 'Hello! Welcome to Senior\'s Archi-firm. Our admin is currently offline, but we\'ll respond to your message as soon as possible.',
        message_type: 'system'
      };

      await supabase
        .from('live_chat_messages')
        .insert([welcomeMessage]);

      return session;
    } catch (error) {
      console.error('Error creating chat session:', error);
      return null;
    }
  }, [user?.id, adminStatus.is_online]);

  // Setup realtime subscription
  const setupRealtimeSubscription = useCallback(() => {
    if (realtimeChannelRef.current) {
      supabase.removeChannel(realtimeChannelRef.current);
    }

    const channel = supabase
      .channel('live_chat_realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_chat_sessions'
        },
        () => {
          fetchSessions();
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'live_chat_sessions'
        },
        () => {
          fetchSessions();
        }
      );

    if (selectedSession) {
      channel.on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_chat_messages',
          filter: `session_id=eq.${selectedSession.id}`
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage;
          setMessages(prev => [...prev, newMessage]);
          
          // Auto-mark as read if admin is viewing
          if (newMessage.sender_type === 'visitor' && user?.id) {
            supabase
              .from('live_chat_messages')
              .update({ is_read: true, read_at: new Date().toISOString() })
              .eq('id', newMessage.id)
              .then(() => {});
          }
        }
      );
    }

    // Subscribe to admin status changes
    channel.on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'admin_chat_status'
      },
      () => {
        fetchAdminStatus();
      }
    );

    channel.subscribe();
    realtimeChannelRef.current = channel;
  }, [selectedSession?.id, fetchSessions, fetchAdminStatus, user?.id]);

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      await Promise.all([
        fetchSessions(),
        fetchAdminStatus()
      ]);
      setIsLoading(false);
    };

    initializeData();
    setupRealtimeSubscription();

    return () => {
      if (realtimeChannelRef.current) {
        supabase.removeChannel(realtimeChannelRef.current);
      }
    };
  }, [fetchSessions, fetchAdminStatus, setupRealtimeSubscription]);

  // Select session
  const selectSession = useCallback((session: ChatSession) => {
    setSelectedSession(session);
    fetchMessages(session.id);
  }, [fetchMessages]);

  return {
    // Data
    sessions,
    selectedSession,
    messages,
    adminStatus,
    isLoading,
    
    // Actions
    fetchSessions,
    fetchMessages,
    selectSession,
    sendMessage,
    createChatSession,
    initializeAdminStatus,
    toggleAdminStatus,
    
    // Computed values
    activeSessions: sessions.filter(s => s.status === 'active' || s.status === 'waiting'),
    waitingSessions: sessions.filter(s => s.status === 'waiting'),
    unreadCount: sessions.reduce((count, session) => {
      // This would need to be calculated based on unread messages
      return count;
    }, 0)
  };
}
