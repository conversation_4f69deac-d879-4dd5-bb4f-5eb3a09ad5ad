import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');

    let query = supabaseServerClient
      .from('live_chat_sessions')
      .select(`
        *,
        profiles:user_id (
          full_name,
          email
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching chat sessions:', error);
      return NextResponse.json(
        { error: 'Failed to fetch chat sessions' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
      count: data?.length || 0
    });

  } catch (error) {
    console.error('Error in chat sessions GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const {
      session_id,
      user_id,
      visitor_name,
      visitor_email,
      user_agent,
      referrer_url,
      current_page
    } = await request.json();

    if (!session_id) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Check if session already exists
    const { data: existingSession } = await supabaseServerClient
      .from('live_chat_sessions')
      .select('id')
      .eq('session_id', session_id)
      .single();

    if (existingSession) {
      return NextResponse.json(
        { error: 'Session already exists' },
        { status: 409 }
      );
    }

    // Create new session
    const sessionData = {
      session_id,
      user_id: user_id || null,
      visitor_name: visitor_name || null,
      visitor_email: visitor_email || null,
      status: 'waiting',
      user_agent: user_agent || null,
      referrer_url: referrer_url || null,
      current_page: current_page || null
    };

    const { data: session, error } = await supabaseServerClient
      .from('live_chat_sessions')
      .insert([sessionData])
      .select()
      .single();

    if (error) {
      console.error('Error creating chat session:', error);
      return NextResponse.json(
        { error: 'Failed to create chat session' },
        { status: 500 }
      );
    }

    // Check admin online status
    const { data: adminStatus } = await supabaseServerClient
      .from('admin_chat_status')
      .select('is_online, last_activity')
      .single();

    const isAdminOnline = adminStatus?.is_online && 
      new Date(adminStatus.last_activity) > new Date(Date.now() - 5 * 60 * 1000);

    // Send welcome message
    const welcomeMessage = {
      session_id: session.id,
      sender_type: 'system',
      content: isAdminOnline 
        ? 'Hello! Welcome to Senior\'s Archi-firm. An admin will be with you shortly.'
        : 'Hello! Welcome to Senior\'s Archi-firm. Our admin is currently offline, but we\'ll respond to your message as soon as possible.',
      message_type: 'system'
    };

    const { error: messageError } = await supabaseServerClient
      .from('live_chat_messages')
      .insert([welcomeMessage]);

    if (messageError) {
      console.error('Error creating welcome message:', messageError);
    }

    return NextResponse.json({
      success: true,
      data: session,
      admin_online: isAdminOnline
    });

  } catch (error) {
    console.error('Error in chat sessions POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
