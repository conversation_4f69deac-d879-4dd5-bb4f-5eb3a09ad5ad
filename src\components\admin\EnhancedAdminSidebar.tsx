"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, KeyboardEvent } from "react";
import { usePathname } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useNavigationPrefetch } from "@/hooks/useNavigationPrefetch";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import {
  LayoutDashboard,
  Users,
  FolderKanban,
  CreditCard,
  Settings,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  MessageSquare,
  MessageCircle,
  FileText,
  DollarSign,
  Briefcase,
  UserCheck,
  AlertTriangle,
  BarChart3,
  Calculator,
  Target,
  TrendingUp,
  UserPlus,
  Home,
  Search,
  HelpCircle,
  Plus,
  FileSearch,
  Sparkles,
  Star,
  Clock,
  Shield,
  Zap,
  Archive,
  Eye
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import WorkflowNotifications from "@/components/WorkflowNotifications";
import { LogoIcon } from "@/components/shared/LogoIcon";

interface NavigationItem {
  name: string;
  href: string;
  icon: any;
  active?: boolean;
  badge?: number;
  submenu?: SubMenuItem[];
  group?: string;
  description?: string;
  isNew?: boolean;
  isFavorite?: boolean;
}

interface SubMenuItem {
  name: string;
  href: string;
  icon?: any;
  active?: boolean;
  badge?: number;
  description?: string;
}

interface MenuGroup {
  name: string;
  icon: any;
  items: NavigationItem[];
  description?: string;
}

interface EnhancedAdminSidebarProps {
  isMobileMenuOpen?: boolean;
  setIsMobileMenuOpen?: (open: boolean) => void;
  isCollapsed?: boolean;
}

export function EnhancedAdminSidebar({ isCollapsed = false }: EnhancedAdminSidebarProps = {}) {
  const { user, profile, signOut, isSigningOut } = useOptimizedAuth();
  const { prefetchOnHover, prefetchRoute } = useNavigationPrefetch();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [favorites, setFavorites] = useState<string[]>([]);
  const [recentItems, setRecentItems] = useState<string[]>([]);
  const [quickStats, setQuickStats] = useState({
    totalUsers: 0,
    activeProjects: 0,
    pendingApplications: 0
  });

  useEffect(() => {
    if (user) {
      fetchNotifications();
      fetchQuickStats();
    }
  }, [user]);

  // Load favorites and recent items from localStorage
  useEffect(() => {
    const savedFavorites = localStorage.getItem('admin-favorites');
    const savedRecent = localStorage.getItem('admin-recent-items');

    if (savedFavorites) {
      try {
        setFavorites(JSON.parse(savedFavorites));
      } catch (error) {
        console.warn('Failed to parse saved favorites:', error);
        localStorage.removeItem('admin-favorites');
      }
    }
    if (savedRecent) {
      try {
        setRecentItems(JSON.parse(savedRecent));
      } catch (error) {
        console.warn('Failed to parse saved recent items:', error);
        localStorage.removeItem('admin-recent-items');
      }
    }
  }, []);

  // Detect reduced motion preference
  const prefersReducedMotion = useMemo(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }, []);

  useEffect(() => {
    // Auto-expand active menu groups
    const activeItem = navigationItems.find(item =>
      item.active || (item.submenu && item.submenu.some(sub => sub.active))
    );
    if (activeItem && activeItem.submenu) {
      setExpandedMenus(prev => [...new Set([...prev, activeItem.name])]);
    }
  }, [pathname]);

  const fetchNotifications = async () => {
    try {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user?.id)
        .eq('read', false);

      if (error && !error.message?.includes('does not exist')) throw error;
      setUnreadNotifications(count || 0);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  const fetchQuickStats = useCallback(async () => {
    try {
      // Batch all queries for better performance
      const [usersResult, projectsResult, applicationsResult] = await Promise.all([
        supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true }),
        supabase
          .from('projects')
          .select('*', { count: 'exact', head: true })
          .in('status', ['assigned', 'in_progress']),
        supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('role', 'designer')
          .eq('application_status', 'pending')
      ]);

      setQuickStats({
        totalUsers: usersResult.count || 0,
        activeProjects: projectsResult.count || 0,
        pendingApplications: applicationsResult.count || 0
      });
    } catch (error) {
      console.error('Error fetching quick stats:', error);
    }
  }, []);

  const navigationItems: NavigationItem[] = [
    {
      name: "Dashboard",
      href: "/admin/dashboard",
      icon: LayoutDashboard,
      active: pathname === "/admin/dashboard" || pathname === "/admin",
      group: "overview",
      description: "Main admin dashboard with key metrics",
      isFavorite: favorites.includes("dashboard")
    },
    {
      name: "User Management",
      href: "/admin/users",
      icon: Users,
      active: pathname.startsWith("/admin/users") || pathname.includes("/designers/applications"),
      group: "management",
      description: "Manage users, roles, and applications",
      submenu: [
        {
          name: "All Users",
          href: "/admin/users",
          icon: Users,
          active: pathname === "/admin/users",
          badge: quickStats.totalUsers,
          description: "View and manage all platform users"
        },
        {
          name: "Designer Applications",
          href: "/admin/designers/applications",
          icon: UserPlus,
          active: pathname === "/admin/designers/applications",
          badge: quickStats.pendingApplications > 0 ? quickStats.pendingApplications : undefined,
          description: "Review pending designer applications"
        },
        {
          name: "User Roles & Permissions",
          href: "/admin/users/roles",
          icon: Shield,
          active: pathname === "/admin/users/roles",
          description: "Manage user roles and permissions"
        }
      ]
    },
    {
      name: "Project Management",
      href: "/admin/projects",
      icon: FolderKanban,
      active: pathname.startsWith("/admin/projects"),
      group: "management",
      description: "Oversee all projects and proposals",
      submenu: [
        {
          name: "All Projects",
          href: "/admin/projects",
          icon: FolderKanban,
          active: pathname === "/admin/projects",
          description: "View all projects across the platform"
        },
        {
          name: "Proposal Review",
          href: "/admin/proposals",
          icon: FileText,
          active: pathname.startsWith("/admin/proposals"),
          description: "Review and approve project proposals"
        },
        {
          name: "Active Projects",
          href: "/admin/projects/active",
          icon: Zap,
          active: pathname === "/admin/projects/active",
          badge: quickStats.activeProjects > 0 ? quickStats.activeProjects : undefined,
          description: "Monitor currently active projects"
        },
        {
          name: "Milestones & Progress",
          href: "/admin/projects/milestones",
          icon: Target,
          active: pathname === "/admin/projects/milestones",
          description: "Track project milestones and progress"
        },
        {
          name: "Project Templates",
          href: "/admin/projects/templates",
          icon: Archive,
          active: pathname === "/admin/projects/templates",
          description: "Manage reusable project templates"
        }
      ]
    },
    {
      name: "Request Management",
      href: "/admin/tracking",
      icon: FileSearch,
      active: pathname.startsWith("/admin/tracking"),
      group: "management",
      description: "Handle client requests and tracking",
      submenu: [
        {
          name: "All Requests",
          href: "/admin/tracking",
          icon: FileSearch,
          active: pathname === "/admin/tracking",
          description: "View all client requests"
        },
        {
          name: "Vision Builder Requests",
          href: "/admin/tracking?request_type=vision_builder",
          icon: Sparkles,
          active: pathname === "/admin/tracking" && pathname.includes("vision_builder"),
          description: "Handle vision builder requests"
        },
        {
          name: "Sample Requests",
          href: "/admin/tracking?request_type=sample_request",
          icon: Eye,
          active: pathname === "/admin/tracking" && pathname.includes("sample_request"),
          description: "Manage sample and preview requests"
        }
      ]
    },
    {
      name: "Financial Operations",
      href: "/admin/finance",
      icon: CreditCard,
      active: pathname.startsWith("/admin/finance"),
      group: "financial",
      description: "Manage finances, payments, and reports",
      submenu: [
        {
          name: "Financial Overview",
          href: "/admin/finance",
          icon: TrendingUp,
          active: pathname === "/admin/finance",
          description: "View financial dashboard and metrics"
        },
        {
          name: "Reports & Analytics",
          href: "/admin/finance/reports",
          icon: BarChart3,
          active: pathname === "/admin/finance/reports",
          description: "Generate financial reports and analytics"
        },
        {
          name: "Fee Management",
          href: "/admin/finance/fees",
          icon: Calculator,
          active: pathname === "/admin/finance/fees",
          description: "Configure platform fees and pricing"
        },
        {
          name: "Payouts & Transactions",
          href: "/admin/finance/payouts",
          icon: DollarSign,
          active: pathname === "/admin/finance/payouts",
          description: "Process payouts and view transactions"
        }
      ]
    },
    {
      name: "Messages & Communication",
      href: "/admin/messages",
      icon: MessageSquare,
      active: pathname.startsWith("/admin/messages") && !pathname.startsWith("/admin/live-chat"),
      badge: unreadNotifications > 0 ? unreadNotifications : undefined,
      group: "communication",
      description: "Handle user communications and disputes"
    },
    {
      name: "Live Chat",
      href: "/admin/live-chat",
      icon: MessageCircle,
      active: pathname.startsWith("/admin/live-chat"),
      group: "communication",
      description: "Real-time chat with website visitors",
      isNew: true
    },
    {
      name: "Disputes & Issues",
      href: "/admin/disputes",
      icon: AlertTriangle,
      active: pathname.startsWith("/admin/disputes"),
      group: "communication",
      description: "Resolve disputes and platform issues"
    },
    {
      name: "Admin Announcements",
      href: "/admin/admin-messages",
      icon: Bell,
      active: pathname.startsWith("/admin/admin-messages"),
      group: "admin-tools",
      description: "Create and manage admin announcements",
      submenu: [
        {
          name: "All Announcements",
          href: "/admin/admin-messages",
          icon: Bell,
          active: pathname === "/admin/admin-messages",
          description: "View all admin announcements"
        },
        {
          name: "Create Announcement",
          href: "/admin/admin-messages/create",
          icon: Plus,
          active: pathname === "/admin/admin-messages/create",
          description: "Create new admin announcement"
        }
      ]
    },
    {
      name: "System Settings",
      href: "/admin/settings",
      icon: Settings,
      active: pathname.startsWith("/admin/settings"),
      group: "admin-tools",
      description: "Configure platform settings and preferences",
      submenu: [
        {
          name: "General Settings",
          href: "/admin/settings",
          icon: Settings,
          active: pathname === "/admin/settings",
          description: "Basic platform configuration"
        },
        {
          name: "Platform Configuration",
          href: "/admin/settings/platform",
          icon: Briefcase,
          active: pathname === "/admin/settings/platform",
          description: "Advanced platform settings"
        },
        {
          name: "Notification Settings",
          href: "/admin/settings/notifications",
          icon: Bell,
          active: pathname === "/admin/settings/notifications",
          description: "Configure notification preferences"
        }
      ]
    }
  ];

  const toggleMenu = (menuName: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuName)
        ? prev.filter(name => name !== menuName)
        : [...prev, menuName]
    );
  };

  const handleMenuClick = (item: NavigationItem) => {
    if (item.submenu) {
      toggleMenu(item.name);
    } else {
      setIsMobileMenuOpen(false);
    }
  };

  // Enhanced hover handler with prefetching
  const handleHover = useCallback((href: string) => {
    // Map href to prefetch key
    const prefetchKey = href.split('/').pop() || 'dashboard';
    prefetchOnHover(prefetchKey, 200); // 200ms delay
  }, [prefetchOnHover]);

  // Add to recent items when navigating
  const handleNavigation = useCallback((href: string) => {
    setRecentItems(prev => {
      const updated = [href, ...prev.filter(item => item !== href)].slice(0, 5);
      localStorage.setItem('admin-recent-items', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Toggle favorites
  const toggleFavorite = useCallback((href: string) => {
    setFavorites(prev => {
      const updated = prev.includes(href)
        ? prev.filter(item => item !== href)
        : [...prev, href];
      localStorage.setItem('admin-favorites', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Keyboard navigation handler
  const handleKeyDown = useCallback((e: KeyboardEvent, item: NavigationItem) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      if (item.submenu) {
        toggleMenu(item.name);
      } else {
        handleNavigation(item.href);
        window.location.href = item.href;
      }
    }
  }, [handleNavigation]);

  // Memoized menu groups with improved organization
  const menuGroups: MenuGroup[] = useMemo(() => [
    {
      name: "Overview",
      icon: LayoutDashboard,
      items: navigationItems.filter(item => item.group === "overview"),
      description: "Dashboard and key metrics"
    },
    {
      name: "User & Project Management",
      icon: Users,
      items: navigationItems.filter(item => item.group === "management"),
      description: "Manage users, projects, and requests"
    },
    {
      name: "Financial Operations",
      icon: CreditCard,
      items: navigationItems.filter(item => item.group === "financial"),
      description: "Financial management and reporting"
    },
    {
      name: "Communication Hub",
      icon: MessageSquare,
      items: navigationItems.filter(item => item.group === "communication"),
      description: "Messages, disputes, and communication"
    },
    {
      name: "Admin Tools",
      icon: Settings,
      items: navigationItems.filter(item => item.group === "admin-tools"),
      description: "System settings and admin features"
    }
  ], [navigationItems]);

  // Filtered items based on search
  const filteredMenuGroups = useMemo(() => {
    if (!searchQuery.trim()) return menuGroups;

    return menuGroups.map(group => ({
      ...group,
      items: group.items.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.submenu?.some(subItem =>
          subItem.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          subItem.description?.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    })).filter(group => group.items.length > 0);
  }, [menuGroups, searchQuery]);

  // Motion variants that respect reduced motion
  const motionVariants = useMemo(() => ({
    sidebar: {
      initial: { x: prefersReducedMotion ? 0 : -300 },
      animate: { x: 0 },
      exit: { x: prefersReducedMotion ? 0 : -300 },
      transition: { duration: prefersReducedMotion ? 0 : 0.3, ease: "easeInOut" }
    },
    menuItem: {
      initial: { opacity: prefersReducedMotion ? 1 : 0, x: prefersReducedMotion ? 0 : -20 },
      animate: { opacity: 1, x: 0 },
      transition: { duration: prefersReducedMotion ? 0 : 0.3 }
    },
    submenu: {
      initial: { height: 0, opacity: 0 },
      animate: { height: "auto", opacity: 1 },
      exit: { height: 0, opacity: 0 },
      transition: { duration: prefersReducedMotion ? 0 : 0.4, ease: "easeInOut" }
    }
  }), [prefersReducedMotion]);

  return (
    <>
      {/* Mobile Header */}
      <motion.div
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.3 }}
        className="lg:hidden bg-white border-b shadow-sm px-4 py-3 flex items-center justify-between fixed top-0 left-0 right-0 z-50"
      >
        <Link href="/admin/dashboard" className="flex items-center">
          <div className="w-8 h-8 bg-brown-600 rounded-lg flex items-center justify-center mr-3">
            <Home className="h-5 w-5 text-white" />
          </div>
          <span className="font-bold text-xl text-gray-900">Admin Portal</span>
        </Link>

        <div className="flex items-center space-x-2">
          <WorkflowNotifications />

          <Button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            variant="ghost"
            size="sm"
            className="p-2"
          >
            <motion.div
              animate={{ rotate: isMobileMenuOpen ? 90 : 0 }}
              transition={{ duration: 0.2 }}
            >
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </motion.div>
          </Button>
        </div>
      </motion.div>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.aside
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="lg:hidden fixed left-0 top-0 h-full w-full max-w-sm bg-white shadow-xl z-50 overflow-y-auto scrollbar-thin"
            style={{
              scrollBehavior: 'smooth',
              paddingBottom: 'env(safe-area-inset-bottom)'
            }}
          >
            <div className="p-6 border-b bg-brown-50">
              <div className="flex items-center">
                <LogoIcon size="sm" className="mr-3" />
                <div>
                  <h2 className="font-bold text-xl text-gray-900">Admin Portal</h2>
                  <p className="text-sm text-brown-600">Platform Management</p>
                </div>
              </div>
            </div>

            <nav className="p-4" role="navigation" aria-label="Mobile admin navigation">
              {/* Mobile Search */}
              <div className="mb-6">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-brown-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-brown-200 rounded-lg bg-white text-sm placeholder-brown-400 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    aria-label="Search navigation items"
                  />
                </div>
              </div>

              {filteredMenuGroups.map((group) => (
                group.items.length > 0 && (
                  <div key={group.name} className="mb-8">
                    <h3 className="text-xs font-semibold text-brown-700 uppercase tracking-wider mb-4 px-3 flex items-center">
                      <group.icon className="h-3 w-3 mr-2 text-brown-500" />
                      {group.name}
                    </h3>
                    <ul className="space-y-2">
                      {group.items.map((item) => (
                        <li key={item.name}>
                          <div>
                            <Link
                              href={item.submenu ? "#" : item.href}
                              onClick={() => {
                                if (item.submenu) {
                                  toggleMenu(item.name);
                                } else {
                                  handleNavigation(item.href);
                                  setIsMobileMenuOpen(false);
                                }
                              }}
                              onMouseEnter={() => !item.submenu && handleHover(item.href)}
                              className={`flex items-center px-4 py-3 rounded-xl transition-all duration-200 ${
                                item.active
                                  ? "bg-brown-600 text-white shadow-md"
                                  : "text-gray-700 hover:bg-brown-50 hover:text-brown-700"
                              }`}
                              role="menuitem"
                              aria-label={item.description || item.name}
                              aria-expanded={item.submenu ? expandedMenus.includes(item.name) : undefined}
                            >
                              <item.icon className={`h-5 w-5 mr-3 ${item.active ? "text-white" : "text-gray-500"}`} />
                              <div className="flex-1 min-w-0">
                                <span className="font-medium block">{item.name}</span>
                                {item.description && !item.active && (
                                  <span className="text-xs text-gray-500 block mt-0.5">
                                    {item.description}
                                  </span>
                                )}
                              </div>

                              {item.badge && (
                                <span className={`text-xs font-medium px-2 py-1 rounded-full ml-2 ${
                                  item.active
                                    ? "bg-white bg-opacity-20 text-white"
                                    : "bg-brown-100 text-brown-700"
                                }`}>
                                  {item.badge}
                                </span>
                              )}

                              {item.submenu && (
                                <motion.div
                                  animate={{ rotate: expandedMenus.includes(item.name) ? 90 : 0 }}
                                  transition={{ duration: 0.2 }}
                                >
                                  <ChevronRight className={`h-4 w-4 ${item.active ? "text-white" : "text-gray-400"}`} />
                                </motion.div>
                              )}
                            </Link>

                            <AnimatePresence>
                              {item.submenu && expandedMenus.includes(item.name) && (
                                <motion.ul
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.3 }}
                                  className="mt-2 ml-6 space-y-1 overflow-hidden bg-brown-25 rounded-lg p-2 border-l-2 border-brown-200"
                                  role="menu"
                                  aria-label={`${item.name} submenu`}
                                >
                                  {item.submenu.map((subItem) => (
                                    <motion.li
                                      key={subItem.name}
                                      initial={{ x: -20, opacity: 0 }}
                                      animate={{ x: 0, opacity: 1 }}
                                      transition={{ duration: 0.2 }}
                                    >
                                      <Link
                                        href={subItem.href}
                                        onClick={() => {
                                          handleNavigation(subItem.href);
                                          setIsMobileMenuOpen(false);
                                        }}
                                        onMouseEnter={() => handleHover(subItem.href)}
                                        className={`flex items-center px-3 py-2.5 rounded-lg text-sm transition-all duration-200 ${
                                          subItem.active
                                            ? "bg-brown-600 text-white font-medium"
                                            : "text-gray-700 hover:bg-brown-100 hover:text-brown-800"
                                        }`}
                                        role="menuitem"
                                        aria-label={subItem.description || subItem.name}
                                      >
                                        {subItem.icon && (
                                          <subItem.icon className={`h-4 w-4 mr-3 ${
                                            subItem.active ? "text-white" : "text-gray-500"
                                          }`} />
                                        )}
                                        <div className="flex-1 min-w-0">
                                          <span className="font-medium block">{subItem.name}</span>
                                          {subItem.description && !subItem.active && (
                                            <span className="text-xs text-gray-500 block mt-0.5">
                                              {subItem.description}
                                            </span>
                                          )}
                                        </div>
                                        {subItem.badge && (
                                          <span className={`text-xs font-medium px-2 py-1 rounded-full ml-2 ${
                                            subItem.active
                                              ? "bg-white bg-opacity-20 text-white"
                                              : "bg-brown-100 text-brown-700"
                                          }`}>
                                            {subItem.badge}
                                          </span>
                                        )}
                                      </Link>
                                    </motion.li>
                                  ))}
                                </motion.ul>
                              )}
                            </AnimatePresence>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )
              ))}
            </nav>

            {/* Mobile Profile Section */}
            <div className="absolute bottom-0 left-0 right-0 border-t bg-gray-50 p-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-brown-600 rounded-full flex items-center justify-center mr-3">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.full_name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-white font-medium">
                      {profile?.full_name?.charAt(0) || "A"}
                    </span>
                  )}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {profile?.full_name || "Admin User"}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {profile?.email || user?.email}
                  </p>
                </div>
                <Button
                  onClick={signOut}
                  variant="ghost"
                  size="sm"
                  disabled={isSigningOut}
                  className="text-gray-500 hover:text-red-600 disabled:opacity-50"
                >
                  {isSigningOut ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500" />
                  ) : (
                    <LogOut className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </motion.aside>
        )}
      </AnimatePresence>

      {/* Sidebar Content - No positioning, handled by CollapsibleSidebar */}
      <div className="flex flex-col h-full bg-white">
        {/* Header */}
        <div className={`flex-shrink-0 border-b border-gray-100 bg-gradient-to-r from-brown-50 to-brown-100 transition-all duration-300 ${
          isCollapsed ? 'p-2' : 'p-6'
        }`}>
          <div className={`${isCollapsed ? 'mb-2' : 'mb-4'}`}>
            <Link href="/admin/dashboard" className="flex items-center group">
              <LogoIcon size={isCollapsed ? "sm" : "md"} className={isCollapsed ? "" : "mr-4"} />
              {!isCollapsed && (
                <div>
                  <h2 className="font-bold text-xl text-gray-900 group-hover:text-brown-700 transition-colors">
                    Admin Portal
                  </h2>
                  <p className="text-sm text-brown-600 font-medium">Platform Management</p>
                </div>
              )}
            </Link>
          </div>

          {/* Search Bar - Hidden when collapsed */}
          {!isCollapsed && (
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-brown-400" />
              </div>
              <input
                type="text"
                placeholder="Search navigation..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-brown-200 rounded-lg bg-white/80 backdrop-blur-sm text-sm placeholder-brown-400 focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent transition-all duration-200"
                aria-label="Search navigation items"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4 text-brown-400 hover:text-brown-600" />
                </button>
              )}
            </div>
          )}
        </div>

        {/* Quick Stats - Hidden when collapsed */}
        {!isCollapsed && (
          <div className="flex-shrink-0 p-4 border-b border-gray-100 bg-gray-50">
            <div className="grid grid-cols-3 gap-3">
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white rounded-lg p-3 text-center shadow-sm border"
              >
                <div className="text-lg font-bold text-gray-900">{quickStats.totalUsers}</div>
                <div className="text-xs text-gray-500">Users</div>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white rounded-lg p-3 text-center shadow-sm border"
              >
                <div className="text-lg font-bold text-brown-600">{quickStats.activeProjects}</div>
                <div className="text-xs text-gray-500">Active</div>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white rounded-lg p-3 text-center shadow-sm border"
              >
                <div className="text-lg font-bold text-orange-600">{quickStats.pendingApplications}</div>
                <div className="text-xs text-gray-500">Pending</div>
              </motion.div>
            </div>
          </div>
        )}

        {/* Navigation - Scrollable Area */}
        <nav
          className={`flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-brown-300 scrollbar-track-gray-100 hover:scrollbar-thumb-brown-400 transition-all duration-300 ${
            isCollapsed ? 'p-2' : 'p-4'
          }`}
          style={{
            scrollBehavior: 'smooth',
            scrollbarWidth: 'thin'
          }}
          role="navigation"
          aria-label="Admin navigation"
        >
          <div className={`${isCollapsed ? 'space-y-2' : 'space-y-8'}`}>
            {/* Collapsed Navigation - Icons Only */}
            {isCollapsed ? (
              <div className="space-y-1">
                {navigationItems.slice(0, 8).map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => handleNavigation(item.href)}
                    className={`flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300 group relative ${
                      item.active
                        ? "bg-brown-600 text-white shadow-lg shadow-brown-600/25"
                        : "text-gray-700 hover:bg-brown-50 hover:text-brown-700"
                    }`}
                    title={item.name}
                    aria-label={item.name}
                  >
                    <item.icon className={`h-5 w-5 ${
                      item.active ? "text-white" : "text-gray-500 group-hover:text-brown-600"
                    }`} />
                    {item.badge && (
                      <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                        {item.badge > 99 ? '99+' : item.badge}
                      </span>
                    )}
                  </Link>
                ))}
              </div>
            ) : (
              <>
                {/* Favorites Section */}
                {favorites.length > 0 && !searchQuery && (
              <div className="bg-brown-25 rounded-xl p-4 border border-brown-100">
                <h3 className="text-xs font-semibold text-brown-700 uppercase tracking-wider mb-3 px-2 flex items-center">
                  <Star className="h-3 w-3 mr-2 text-brown-500" />
                  Favorites
                </h3>
                <ul className="space-y-1">
                  {navigationItems
                    .filter(item => favorites.includes(item.href))
                    .slice(0, 3)
                    .map((item) => (
                      <li key={`fav-${item.name}`}>
                        <Link
                          href={item.href}
                          onClick={() => handleNavigation(item.href)}
                          className="flex items-center px-3 py-2 rounded-lg text-sm transition-all duration-200 text-brown-700 hover:bg-brown-100 hover:text-brown-800"
                        >
                          <item.icon className="h-4 w-4 mr-3 text-brown-500" />
                          <span className="font-medium flex-1">{item.name}</span>
                          <Star className="h-3 w-3 text-brown-400 fill-current" />
                        </Link>
                      </li>
                    ))}
                </ul>
              </div>
            )}

            {/* Recent Items Section */}
            {recentItems.length > 0 && !searchQuery && (
              <div className="bg-gray-25 rounded-xl p-4 border border-gray-100">
                <h3 className="text-xs font-semibold text-gray-700 uppercase tracking-wider mb-3 px-2 flex items-center">
                  <Clock className="h-3 w-3 mr-2 text-gray-500" />
                  Recent
                </h3>
                <ul className="space-y-1">
                  {navigationItems
                    .filter(item => recentItems.includes(item.href))
                    .slice(0, 3)
                    .map((item) => (
                      <li key={`recent-${item.name}`}>
                        <Link
                          href={item.href}
                          onClick={() => handleNavigation(item.href)}
                          className="flex items-center px-3 py-2 rounded-lg text-sm transition-all duration-200 text-gray-700 hover:bg-gray-100 hover:text-gray-800"
                        >
                          <item.icon className="h-4 w-4 mr-3 text-gray-500" />
                          <span className="font-medium flex-1">{item.name}</span>
                        </Link>
                      </li>
                    ))}
                </ul>
              </div>
            )}

            {/* Main Navigation Groups */}
            {filteredMenuGroups.map((group) => (
              group.items.length > 0 && (
                <div key={group.name} className="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                  <h3 className="text-xs font-semibold text-gray-700 uppercase tracking-wider mb-4 px-2 flex items-center">
                    <group.icon className="h-4 w-4 mr-2 text-gray-500" />
                    {group.name}
                    {group.description && (
                      <span className="ml-2 text-xs font-normal text-gray-500 normal-case">
                        • {group.description}
                      </span>
                    )}
                  </h3>
                  <ul className="space-y-2">
                    {group.items.map((item) => (
                      <motion.li
                        key={item.name}
                        variants={motionVariants.menuItem}
                        initial="initial"
                        animate="animate"
                      >
                        <div className="relative">
                          <Link
                            href={item.submenu ? "#" : item.href}
                            onClick={() => {
                              if (item.submenu) {
                                toggleMenu(item.name);
                              } else {
                                handleNavigation(item.href);
                              }
                            }}
                            onMouseEnter={() => !item.submenu && handleHover(item.href)}
                            onKeyDown={(e) => handleKeyDown(e, item)}
                            className={`flex items-center px-4 py-3 rounded-xl transition-all duration-300 group relative ${
                              item.active
                                ? "bg-brown-600 text-white shadow-lg shadow-brown-600/25"
                                : "text-gray-700 hover:bg-brown-50 hover:text-brown-700"
                            }`}
                            role="menuitem"
                            aria-label={item.description || item.name}
                            aria-expanded={item.submenu ? expandedMenus.includes(item.name) : undefined}
                            tabIndex={0}
                          >
                            <motion.div
                              whileHover={{ scale: 1.1 }}
                              transition={{ duration: 0.2 }}
                            >
                              <item.icon className={`h-5 w-5 mr-3 ${
                                item.active ? "text-white" : "text-gray-500 group-hover:text-brown-600"
                              }`} />
                            </motion.div>

                            <div className="flex-1 min-w-0">
                              <span className="font-medium block leading-tight">{item.name}</span>
                              {item.description && !item.active && (
                                <span className="text-xs text-gray-500 block leading-tight mt-0.5 break-words">
                                  {item.description}
                                </span>
                              )}
                            </div>

                            <div className="flex items-center space-x-2 ml-2">
                              {/* Favorite toggle */}
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  toggleFavorite(item.href);
                                }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-brown-100 rounded"
                                aria-label={`${favorites.includes(item.href) ? 'Remove from' : 'Add to'} favorites`}
                              >
                                <Star className={`h-3 w-3 ${
                                  favorites.includes(item.href)
                                    ? 'text-brown-500 fill-current'
                                    : 'text-gray-400'
                                }`} />
                              </button>

                              {item.badge && (
                                <motion.span
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className={`text-xs font-bold px-2.5 py-1 rounded-full ${
                                    item.active
                                      ? "bg-white bg-opacity-20 text-white"
                                      : "bg-brown-100 text-brown-700"
                                  }`}
                                >
                                  {item.badge}
                                </motion.span>
                              )}

                              {item.submenu && (
                                <motion.div
                                  animate={{ rotate: expandedMenus.includes(item.name) ? 90 : 0 }}
                                  transition={{ duration: 0.3 }}
                                >
                                  <ChevronRight className={`h-4 w-4 ${
                                    item.active ? "text-white" : "text-gray-400 group-hover:text-brown-600"
                                  }`} />
                                </motion.div>
                              )}
                            </div>

                            {/* New item indicator */}
                            {item.isNew && (
                              <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                            )}
                          </Link>

                          <AnimatePresence>
                            {item.submenu && expandedMenus.includes(item.name) && (
                              <motion.ul
                                variants={motionVariants.submenu}
                                initial="initial"
                                animate="animate"
                                exit="exit"
                                className="mt-3 ml-6 space-y-1 overflow-hidden bg-brown-25 rounded-lg p-2 border-l-2 border-brown-200"
                                role="menu"
                                aria-label={`${item.name} submenu`}
                              >
                                {item.submenu.map((subItem, index) => (
                                  <motion.li
                                    key={subItem.name}
                                    initial={{ x: -20, opacity: 0 }}
                                    animate={{ x: 0, opacity: 1 }}
                                    transition={{ duration: 0.3, delay: index * 0.1 }}
                                  >
                                    <Link
                                      href={subItem.href}
                                      onClick={() => handleNavigation(subItem.href)}
                                      onMouseEnter={() => handleHover(subItem.href)}
                                      className={`flex items-center px-3 py-2.5 rounded-lg text-sm transition-all duration-200 group ${
                                        subItem.active
                                          ? "bg-brown-600 text-white font-medium shadow-sm"
                                          : "text-gray-700 hover:bg-brown-100 hover:text-brown-800"
                                      }`}
                                      role="menuitem"
                                      aria-label={subItem.description || subItem.name}
                                      tabIndex={0}
                                    >
                                      {subItem.icon && (
                                        <motion.div
                                          whileHover={{ scale: 1.1 }}
                                          transition={{ duration: 0.2 }}
                                        >
                                          <subItem.icon className={`h-4 w-4 mr-3 ${
                                            subItem.active
                                              ? "text-white"
                                              : "text-gray-500 group-hover:text-brown-600"
                                          }`} />
                                        </motion.div>
                                      )}
                                      <div className="flex-1 min-w-0">
                                        <span className="font-medium block leading-tight">{subItem.name}</span>
                                        {subItem.description && !subItem.active && (
                                          <span className="text-xs text-gray-500 block leading-tight mt-0.5 break-words">
                                            {subItem.description}
                                          </span>
                                        )}
                                      </div>
                                      {subItem.badge && (
                                        <motion.span
                                          initial={{ scale: 0 }}
                                          animate={{ scale: 1 }}
                                          className={`text-xs font-medium px-2 py-1 rounded-full ml-2 ${
                                            subItem.active
                                              ? "bg-white bg-opacity-20 text-white"
                                              : "bg-brown-100 text-brown-700"
                                          }`}
                                        >
                                          {subItem.badge}
                                        </motion.span>
                                      )}
                                    </Link>
                                  </motion.li>
                                ))}
                              </motion.ul>
                            )}
                          </AnimatePresence>
                        </div>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              )
            ))}
              </>
            )}
          </div>
        </nav>

        {/* Profile Section */}
        <div className={`flex-shrink-0 border-t border-gray-100 bg-gray-50 transition-all duration-300 ${
          isCollapsed ? 'p-2' : 'p-4'
        }`}>
          <div className="relative">
            {/* Profile Info and Dropdown Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              className={`flex items-center rounded-xl hover:bg-white transition-all duration-200 shadow-sm ${
                isCollapsed
                  ? 'p-1 justify-center w-10 h-10 mx-auto'
                  : 'px-3 py-3 w-full'
              }`}
            >
              <div className={`bg-brown-600 rounded-full flex items-center justify-center shadow-md transition-all duration-300 ${
                isCollapsed ? 'w-8 h-8' : 'w-10 h-10'
              }`}>
                {profile?.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.full_name}
                    className={`rounded-full object-cover transition-all duration-300 ${
                      isCollapsed ? 'w-8 h-8' : 'w-10 h-10'
                    }`}
                  />
                ) : (
                  <span className={`text-white font-bold transition-all duration-300 ${
                    isCollapsed ? 'text-sm' : 'text-base'
                  }`}>
                    {profile?.full_name?.charAt(0) || "A"}
                  </span>
                )}
              </div>
              {!isCollapsed && (
                <>
                  <div className="flex-1 text-left min-w-0 ml-3">
                    <p className="text-sm font-semibold text-gray-900 leading-tight break-words">
                      {profile?.full_name || "Admin User"}
                    </p>
                    <p className="text-xs text-gray-500 leading-tight break-words">
                      {profile?.email || user?.email}
                    </p>
                  </div>
                  <motion.div
                    animate={{ rotate: isProfileMenuOpen ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  </motion.div>
                </>
              )}
            </motion.button>

            <AnimatePresence>
              {isProfileMenuOpen && !isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute bottom-full mb-2 w-full bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden"
                >
                  <ul>
                    <li>
                      <Link
                        href="/admin/profile"
                        className="flex items-center px-4 py-3 hover:bg-gray-50 transition-colors"
                        onClick={() => setIsProfileMenuOpen(false)}
                      >
                        <Settings className="h-4 w-4 mr-3 text-gray-500" />
                        <span className="text-sm font-medium">Profile Settings</span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/admin/help"
                        className="flex items-center px-4 py-3 hover:bg-gray-50 transition-colors"
                        onClick={() => setIsProfileMenuOpen(false)}
                      >
                        <HelpCircle className="h-4 w-4 mr-3 text-gray-500" />
                        <span className="text-sm font-medium">Help & Support</span>
                      </Link>
                    </li>
                    <li className="border-t border-gray-100">
                      <button
                        onClick={signOut}
                        disabled={isSigningOut}
                        className="w-full flex items-center px-4 py-3 text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSigningOut ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3" />
                        ) : (
                          <LogOut className="h-4 w-4 mr-3" />
                        )}
                        <span className="text-sm font-medium">
                          {isSigningOut ? 'Signing Out...' : 'Sign Out'}
                        </span>
                      </button>
                    </li>
                  </ul>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </>
  );
}
