"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

interface RouteGuardProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  redirectTo?: string;
}

export function RouteGuard({ children, allowedRoles, redirectTo = '/auth/login' }: RouteGuardProps) {
  const { user, profile, loading } = useOptimizedAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading) return; // Wait for auth to load

    // If no user, redirect to login
    if (!user) {
      router.push(redirectTo);
      return;
    }

    // If allowedRoles specified, check user role
    if (allowedRoles && profile) {
      if (!allowedRoles.includes(profile.role)) {
        // Redirect to appropriate dashboard based on role
        switch (profile.role) {
          case 'admin':
            router.push('/admin/dashboard');
            break;
          case 'designer':
            router.push('/designer/dashboard');
            break;
          case 'client':
            router.push('/client/dashboard');
            break;
          case 'quality_team':
            router.push('/quality/dashboard');
            break;
          case 'manager':
            router.push('/manager/dashboard');
            break;
          default:
            router.push('/auth/login?error=invalid_role');
        }
        return;
      }
    }
  }, [user, profile, loading, router, allowedRoles, redirectTo]);

  // Show loading while checking auth
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  // Show nothing while redirecting
  if (!user || (allowedRoles && profile && !allowedRoles.includes(profile.role))) {
    return null;
  }

  return <>{children}</>;
}
