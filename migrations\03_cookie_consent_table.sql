-- <PERSON><PERSON>sent Table
CREATE TABLE IF NOT EXISTS cookie_consents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  consent_given BOOLEAN NOT NULL,
  consent_types J<PERSON>NB DEFAULT '{}',
  consent_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_cookie_consents_session ON cookie_consents(session_id);

-- Enable RLS
ALTER TABLE cookie_consents ENABLE ROW LEVEL SECURITY;
