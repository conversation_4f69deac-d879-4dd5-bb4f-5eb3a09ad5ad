import { NextRequest, NextResponse } from 'next/server';
import { encryptPaymentMethodData, decryptPaymentMethodData } from '@/lib/encryption';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];

    // Create Supabase client for server-side operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action, data } = await request.json();

    if (!action || !data) {
      return NextResponse.json(
        { error: 'Missing action or data' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'encrypt':
        try {
          const encryptedData = encryptPaymentMethodData(data);
          return NextResponse.json({ 
            success: true, 
            data: encryptedData 
          });
        } catch (error) {
          console.error('Encryption error:', error);
          return NextResponse.json(
            { error: 'Failed to encrypt data' },
            { status: 500 }
          );
        }

      case 'decrypt':
        try {
          const decryptedData = decryptPaymentMethodData(data);
          return NextResponse.json({ 
            success: true, 
            data: decryptedData 
          });
        } catch (error) {
          console.error('Decryption error:', error);
          return NextResponse.json(
            { error: 'Failed to decrypt data' },
            { status: 500 }
          );
        }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use "encrypt" or "decrypt"' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Optional: Add a GET endpoint to check encryption status
export async function GET(request: NextRequest) {
  try {
    // Check if encryption keys are properly configured
    const hasEncryptionKey = !!process.env.ENCRYPTION_KEY;
    const hasBackupKey = !!process.env.ENCRYPTION_KEY_BACKUP;
    
    return NextResponse.json({
      encryption_configured: hasEncryptionKey,
      backup_key_configured: hasBackupKey,
      status: hasEncryptionKey ? 'ready' : 'needs_configuration'
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to check encryption status' },
      { status: 500 }
    );
  }
}
