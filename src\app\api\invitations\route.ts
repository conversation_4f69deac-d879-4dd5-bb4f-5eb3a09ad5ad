import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { nanoid } from 'nanoid';

/**
 * POST /api/invitations
 * Creates a new invitation
 *
 * Request body:
 * {
 *   userId: string; // ID of the user creating the invitation
 *   role: 'client' | 'designer'; // Role for the invited user
 *   email?: string; // Optional email for the invited user
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const { userId, role, email } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (!role || !['client', 'designer'].includes(role)) {
      return NextResponse.json(
        { error: 'Valid role is required (client or designer)' },
        { status: 400 }
      );
    }

    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }

    // Check if the user already has an active invitation for the specified role
    // Designers can invite clients, Clients can invite designers
    if ((profile.role === 'designer' && role === 'client') || (profile.role === 'client' && role === 'designer')) {
      const { data: existingInvitations, error: invitationError } = await supabase
        .from('invitations')
        .select('id, invite_code, created_at, expires_at, status')
        .eq('created_by', userId)
        .eq('role', role)
        .in('status', ['pending'])
        .gt('expires_at', new Date().toISOString());

      if (invitationError) {
        console.error('Error checking existing invitations:', invitationError);
        return NextResponse.json(
          { error: 'Failed to check existing invitations' },
          { status: 500 }
        );
      }

      // If the user already has an active invitation, return it instead of creating a new one
      if (existingInvitations && existingInvitations.length > 0) {
        return NextResponse.json({
          invitation: existingInvitations[0],
          message: 'You already have an active invitation. Please use this one or wait until it expires.'
        }, { status: 200 });
      }
    }

    // Generate a unique invite code
    const inviteCode = nanoid(10);

    // Set expiration date (30 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Insert the invitation
    const { data, error } = await supabase
      .from('invitations')
      .insert({
        invite_code: inviteCode,
        created_by: userId,
        role,
        email,
        expires_at: expiresAt.toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating invitation:', error);
      return NextResponse.json(
        { error: 'Failed to create invitation' },
        { status: 500 }
      );
    }

    return NextResponse.json({ invitation: data }, { status: 201 });
  } catch (error) {
    console.error('Error creating invitation:', error);
    return NextResponse.json(
      { error: 'Failed to create invitation' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/invitations
 * Gets all invitations for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }

    let query = supabase
      .from('invitations')
      .select('*')
      .order('created_at', { ascending: false });

    // If admin, get all invitations
    // Otherwise, get only the user's invitations
    if (profile.role !== 'admin') {
      query = query.eq('created_by', user.id);
    }

    const { data, error } = await query;

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch invitations' },
        { status: 500 }
      );
    }

    return NextResponse.json({ invitations: data }, { status: 200 });
  } catch (error) {
    console.error('Error fetching invitations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invitations' },
      { status: 500 }
    );
  }
}
