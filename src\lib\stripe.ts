import { loadStripe } from '@stripe/stripe-js';
import { supabase } from './supabase';

// Make sure to set your Stripe publishable key in the environment variables
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

export interface PaymentIntent {
  id: string;
  client_secret: string;
  amount: number;
  currency: string;
  status: string;
}

export interface StripeCheckoutOptions {
  projectId: string;
  milestoneId?: string;
  amount: number;
  description: string;
  clientId: string;
  designerId?: string;
  paymentType: 'deposit' | 'milestone' | 'final';
  successUrl: string;
  cancelUrl: string;
}

export interface StripePaymentOptions {
  projectId: string;
  milestoneId?: string;
  amount: number;
  description: string;
  clientId: string;
  designerId?: string;
  paymentType: 'deposit' | 'milestone' | 'final';
}

/**
 * Create a Stripe Checkout Session for redirecting the user to Stripe's hosted checkout page
 */
export const createCheckoutSession = async (options: StripeCheckoutOptions): Promise<string | null> => {
  try {
    const { data, error } = await supabase.functions.invoke('create-checkout-session', {
      body: options
    });

    if (error) {
      console.error('Error creating checkout session:', error);
      return null;
    }

    return data.sessionId;
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return null;
  }
};

/**
 * Create a PaymentIntent for collecting payment details on your site
 */
export const createPaymentIntent = async (options: StripePaymentOptions): Promise<PaymentIntent | null> => {
  try {
    const { data, error } = await supabase.functions.invoke('create-payment-intent', {
      body: options
    });

    if (error) {
      console.error('Error creating payment intent:', error);
      return null;
    }

    return data.paymentIntent;
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return null;
  }
};

/**
 * Redirect to Stripe Checkout
 */
export const redirectToCheckout = async (sessionId: string): Promise<{ error?: Error }> => {
  const stripe = await stripePromise;
  if (!stripe) {
    return { error: new Error('Stripe failed to load') };
  }
  
  return stripe.redirectToCheckout({ sessionId });
};

/**
 * Get Stripe instance
 */
export const getStripe = async () => {
  return await stripePromise;
};

/**
 * Process a payment with an existing PaymentMethod
 */
export const processPaymentWithSavedMethod = async (
  paymentMethodId: string, 
  options: StripePaymentOptions
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { data, error } = await supabase.functions.invoke('process-payment', {
      body: {
        paymentMethodId,
        ...options
      }
    });

    if (error) {
      console.error('Error processing payment:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error processing payment:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Save a payment method for future use
 */
export const savePaymentMethod = async (
  paymentMethodId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { data, error } = await supabase.functions.invoke('save-payment-method', {
      body: {
        paymentMethodId,
        userId
      }
    });

    if (error) {
      console.error('Error saving payment method:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error saving payment method:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Get saved payment methods for a user
 */
export const getSavedPaymentMethods = async (
  userId: string
): Promise<{ id: string; brand: string; last4: string; expMonth: number; expYear: number }[] | null> => {
  try {
    const { data, error } = await supabase.functions.invoke('get-payment-methods', {
      body: { userId }
    });

    if (error) {
      console.error('Error getting payment methods:', error);
      return null;
    }

    return data.paymentMethods;
  } catch (error) {
    console.error('Error getting payment methods:', error);
    return null;
  }
};
