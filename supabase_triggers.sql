-- Function to create an invoice when a milestone is approved
CREATE OR REPLACE FUNCTION create_invoice_for_approved_milestone()
RETURNS TRIGGER AS $$
DECLARE
  project_title TEXT;
  client_id UUID;
  invoice_number TEXT;
  due_date TIMESTAMP WITH TIME ZONE;
  description TEXT;
BEGIN
  -- Only proceed if the milestone status changed to 'approved'
  IF (NEW.status = 'approved' AND (OLD.status IS NULL OR OLD.status != 'approved')) THEN
    -- Get project title and client_id
    SELECT projects.title, projects.client_id INTO project_title, client_id
    FROM projects
    WHERE projects.id = NEW.project_id;
    
    -- Generate invoice number
    invoice_number := 'INV-' || 
                      SUBSTRING(EXTRACT(EPOCH FROM NOW())::TEXT, LENGTH(EXTRACT(EPOCH FROM NOW())::TEXT)-3) || 
                      LPAD(FLOOR(RANDOM() * 90000 + 10000)::TEXT, 5, '0');
    
    -- Set due date to 30 days from now
    due_date := NOW() + INTERVAL '30 days';
    
    -- Create description
    description := 'Payment for milestone: ' || NEW.title || ' (' || project_title || ')';
    
    -- Insert the invoice
    INSERT INTO invoices (
      invoice_number,
      amount,
      status,
      due_date,
      issued_date,
      description,
      project_id,
      client_id
    ) VALUES (
      invoice_number,
      NEW.amount,
      'pending',
      due_date,
      NOW(),
      description,
      NEW.project_id,
      client_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_create_invoice_for_approved_milestone ON project_milestones;
CREATE TRIGGER trigger_create_invoice_for_approved_milestone
AFTER UPDATE ON project_milestones
FOR EACH ROW
EXECUTE FUNCTION create_invoice_for_approved_milestone();

-- Function to update invoice status when payment is made
CREATE OR REPLACE FUNCTION update_invoice_on_milestone_payment()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if the milestone paid_at field was updated
  IF (NEW.paid_at IS NOT NULL AND (OLD.paid_at IS NULL)) THEN
    -- Update any invoices related to this milestone
    UPDATE invoices
    SET status = 'paid'
    WHERE project_id = NEW.project_id
    AND description LIKE '%' || NEW.title || '%'
    AND status = 'pending';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_update_invoice_on_milestone_payment ON project_milestones;
CREATE TRIGGER trigger_update_invoice_on_milestone_payment
AFTER UPDATE ON project_milestones
FOR EACH ROW
EXECUTE FUNCTION update_invoice_on_milestone_payment();
