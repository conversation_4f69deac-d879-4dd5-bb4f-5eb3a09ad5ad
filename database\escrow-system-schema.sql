-- =====================================================
-- ESCROW SYSTEM DATABASE SCHEMA
-- Enhances existing payment system with escrow functionality
-- =====================================================

-- 1. EXTEND EXISTING TABLES
-- =====================================================

-- Add escrow status to existing transactions table (with safety check)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'transactions'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'transactions' AND column_name = 'escrow_status'
    ) THEN
        ALTER TABLE transactions ADD COLUMN escrow_status VARCHAR(50)
            DEFAULT 'none' CHECK (escrow_status IN ('none', 'held', 'pending_release', 'released', 'disputed'));
        RAISE NOTICE 'Added escrow_status column to transactions table';
    ELSE
        RAISE NOTICE 'Transactions table does not exist or escrow_status column already exists';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add escrow_status column to transactions: %', SQLERRM;
END $$;

-- Add escrow hold reference to existing project milestones (with safety check)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'project_milestones'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'project_milestones' AND column_name = 'escrow_hold_id'
    ) THEN
        ALTER TABLE project_milestones ADD COLUMN escrow_hold_id UUID;
        RAISE NOTICE 'Added escrow_hold_id column to project_milestones table';
    ELSE
        RAISE NOTICE 'Project_milestones table does not exist or escrow_hold_id column already exists';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add escrow_hold_id column to project_milestones: %', SQLERRM;
END $$;

-- Create payouts table if it doesn't exist (for escrow integration)
CREATE TABLE IF NOT EXISTS payouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID REFERENCES transactions(id),
    designer_id UUID NOT NULL REFERENCES profiles(id),
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    payout_method VARCHAR(50) DEFAULT 'stripe_connect',
    external_payout_id TEXT, -- Stripe Connect transfer ID, PayPal payout ID, etc.
    failure_reason TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Add escrow tracking to payouts table (with safety check)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'payouts'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payouts' AND column_name = 'escrow_release_id'
    ) THEN
        ALTER TABLE payouts ADD COLUMN escrow_release_id UUID;
        RAISE NOTICE 'Added escrow_release_id column to payouts table';
    ELSE
        RAISE NOTICE 'Payouts table does not exist or escrow_release_id column already exists';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not add escrow_release_id column to payouts: %', SQLERRM;
END $$;

-- 2. CREATE NEW ESCROW TABLES
-- =====================================================

-- Escrow Accounts - Virtual accounts for holding funds
CREATE TABLE IF NOT EXISTS escrow_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES profiles(id),
    designer_id UUID NOT NULL REFERENCES profiles(id),
    manager_id UUID REFERENCES profiles(id),
    account_number TEXT UNIQUE NOT NULL, -- Generated escrow account identifier
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'closed', 'suspended')),
    total_held DECIMAL(10,2) DEFAULT 0.00,
    total_released DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Indexes
    UNIQUE(project_id, client_id, designer_id)
);

-- Escrow Holds - Individual fund holds for milestones
CREATE TABLE IF NOT EXISTS escrow_holds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_account_id UUID NOT NULL REFERENCES escrow_accounts(id) ON DELETE CASCADE,
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    milestone_id UUID, -- Will add foreign key constraint later
    project_id UUID NOT NULL REFERENCES projects(id),
    
    -- Financial details
    gross_amount DECIMAL(10,2) NOT NULL, -- Original payment amount
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    processing_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL, -- Amount to be released to designer
    
    -- Hold details
    hold_reason VARCHAR(100) NOT NULL DEFAULT 'milestone_completion',
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'pending_release', 'released', 'disputed', 'cancelled')),
    
    -- Approval workflow
    requires_manager_approval BOOLEAN DEFAULT TRUE,
    requires_quality_approval BOOLEAN DEFAULT FALSE,
    manager_approved_at TIMESTAMP WITH TIME ZONE,
    manager_approved_by UUID REFERENCES profiles(id),
    quality_approved_at TIMESTAMP WITH TIME ZONE,
    quality_approved_by UUID REFERENCES profiles(id),
    
    -- Timing
    held_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    release_requested_at TIMESTAMP WITH TIME ZONE,
    released_at TIMESTAMP WITH TIME ZONE,
    auto_release_date TIMESTAMP WITH TIME ZONE, -- Automatic release date if no action
    
    -- Additional data
    hold_notes TEXT,
    release_notes TEXT,
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Releases - Release requests and approvals
CREATE TABLE IF NOT EXISTS escrow_releases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_hold_id UUID NOT NULL REFERENCES escrow_holds(id) ON DELETE CASCADE,
    escrow_account_id UUID NOT NULL REFERENCES escrow_accounts(id),
    project_id UUID NOT NULL REFERENCES projects(id),
    milestone_id UUID, -- Will add foreign key constraint later
    
    -- Release details
    release_amount DECIMAL(10,2) NOT NULL,
    release_type VARCHAR(50) NOT NULL DEFAULT 'milestone_completion' 
        CHECK (release_type IN ('milestone_completion', 'project_completion', 'partial_release', 'dispute_resolution', 'cancellation')),
    
    -- Approval workflow
    requested_by UUID NOT NULL REFERENCES profiles(id),
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    manager_id UUID REFERENCES profiles(id),
    manager_approval_status VARCHAR(50) DEFAULT 'pending' 
        CHECK (manager_approval_status IN ('pending', 'approved', 'rejected', 'not_required')),
    manager_approved_at TIMESTAMP WITH TIME ZONE,
    manager_notes TEXT,
    
    quality_approval_status VARCHAR(50) DEFAULT 'not_required' 
        CHECK (quality_approval_status IN ('pending', 'approved', 'rejected', 'not_required')),
    quality_approved_at TIMESTAMP WITH TIME ZONE,
    quality_approved_by UUID REFERENCES profiles(id),
    quality_notes TEXT,
    
    -- Final status
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'processed', 'failed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES profiles(id),
    
    -- Integration with existing payout system
    payout_id UUID, -- Will add foreign key constraint later
    payout_transaction_id TEXT, -- External payout reference (Stripe, PayPal, etc.)
    
    -- Failure handling
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Disputes - Dispute management
CREATE TABLE IF NOT EXISTS escrow_disputes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_hold_id UUID NOT NULL REFERENCES escrow_holds(id),
    project_id UUID NOT NULL REFERENCES projects(id),
    
    -- Dispute details
    dispute_type VARCHAR(50) NOT NULL CHECK (dispute_type IN ('quality_issue', 'scope_change', 'timeline_delay', 'payment_dispute', 'other')),
    initiated_by UUID NOT NULL REFERENCES profiles(id),
    initiated_against UUID NOT NULL REFERENCES profiles(id),
    
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    evidence_files JSONB DEFAULT '[]', -- Array of file references
    
    -- Resolution
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'mediation', 'resolved', 'closed')),
    assigned_mediator UUID REFERENCES profiles(id),
    resolution_type VARCHAR(50) CHECK (resolution_type IN ('full_release', 'partial_release', 'full_refund', 'partial_refund', 'rework_required')),
    resolution_amount DECIMAL(10,2),
    resolution_notes TEXT,
    
    -- Timing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    
    metadata JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow Activity Log - Audit trail for all escrow operations
CREATE TABLE IF NOT EXISTS escrow_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    escrow_account_id UUID REFERENCES escrow_accounts(id),
    escrow_hold_id UUID REFERENCES escrow_holds(id),
    escrow_release_id UUID REFERENCES escrow_releases(id),
    project_id UUID NOT NULL REFERENCES projects(id),
    
    -- Activity details
    activity_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    performed_by UUID NOT NULL REFERENCES profiles(id),
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Before/after state for audit
    previous_state JSONB,
    new_state JSONB,
    
    -- Additional context
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}'
);

-- 3. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Escrow Accounts indexes
CREATE INDEX IF NOT EXISTS idx_escrow_accounts_project_id ON escrow_accounts(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_accounts_client_id ON escrow_accounts(client_id);
CREATE INDEX IF NOT EXISTS idx_escrow_accounts_designer_id ON escrow_accounts(designer_id);
CREATE INDEX IF NOT EXISTS idx_escrow_accounts_manager_id ON escrow_accounts(manager_id);
CREATE INDEX IF NOT EXISTS idx_escrow_accounts_status ON escrow_accounts(status);

-- Escrow Holds indexes
CREATE INDEX IF NOT EXISTS idx_escrow_holds_account_id ON escrow_holds(escrow_account_id);
CREATE INDEX IF NOT EXISTS idx_escrow_holds_transaction_id ON escrow_holds(transaction_id);
CREATE INDEX IF NOT EXISTS idx_escrow_holds_milestone_id ON escrow_holds(milestone_id);
CREATE INDEX IF NOT EXISTS idx_escrow_holds_project_id ON escrow_holds(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_holds_status ON escrow_holds(status);
CREATE INDEX IF NOT EXISTS idx_escrow_holds_manager_approval ON escrow_holds(manager_approved_at, status);
CREATE INDEX IF NOT EXISTS idx_escrow_holds_auto_release ON escrow_holds(auto_release_date, status);

-- Escrow Releases indexes
CREATE INDEX IF NOT EXISTS idx_escrow_releases_hold_id ON escrow_releases(escrow_hold_id);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_project_id ON escrow_releases(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_manager_id ON escrow_releases(manager_id);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_status ON escrow_releases(status);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_approval_status ON escrow_releases(manager_approval_status, quality_approval_status);

-- Escrow Disputes indexes
CREATE INDEX IF NOT EXISTS idx_escrow_disputes_hold_id ON escrow_disputes(escrow_hold_id);
CREATE INDEX IF NOT EXISTS idx_escrow_disputes_project_id ON escrow_disputes(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_disputes_status ON escrow_disputes(status);
CREATE INDEX IF NOT EXISTS idx_escrow_disputes_mediator ON escrow_disputes(assigned_mediator);

-- Escrow Activities indexes
CREATE INDEX IF NOT EXISTS idx_escrow_activities_account_id ON escrow_activities(escrow_account_id);
CREATE INDEX IF NOT EXISTS idx_escrow_activities_project_id ON escrow_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_activities_performed_by ON escrow_activities(performed_by);
CREATE INDEX IF NOT EXISTS idx_escrow_activities_performed_at ON escrow_activities(performed_at);
CREATE INDEX IF NOT EXISTS idx_escrow_activities_type ON escrow_activities(activity_type);

-- 4. FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Note: Foreign key constraints are handled in a separate script
-- Run 'escrow-foreign-keys.sql' after this script completes successfully
-- This ensures proper dependency order and avoids constraint errors

-- 5. ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on all escrow tables
ALTER TABLE escrow_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_holds ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_disputes ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_activities ENABLE ROW LEVEL SECURITY;

-- RLS Policies will be created in separate file for better organization
