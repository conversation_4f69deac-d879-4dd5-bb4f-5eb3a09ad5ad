'use client';

import { supabase } from '@/lib/supabase';
import { QualityWorkflowManager } from './quality-workflow';

/**
 * SLA Monitoring and Alert System
 * Tracks review deadlines and sends alerts for overdue items
 */

export interface SLAStatus {
  reviewId: string;
  projectId: string;
  designerId: string;
  reviewerId?: string;
  deadline: string;
  status: 'on_time' | 'approaching' | 'overdue' | 'critical';
  hoursRemaining: number;
  escalationLevel: 0 | 1 | 2 | 3;
}

export interface SLAAlert {
  id: string;
  reviewId: string;
  alertType: 'approaching_deadline' | 'overdue' | 'critical_overdue';
  recipientId: string;
  recipientRole: 'reviewer' | 'manager' | 'admin';
  sentAt: string;
  acknowledged: boolean;
}

/**
 * SLA Monitor Class
 */
export class SLAMonitor {
  /**
   * Check all active reviews for SLA compliance
   */
  static async checkAllSLAs(): Promise<SLAStatus[]> {
    try {
      const { data: reviews, error } = await supabase
        .from('quality_reviews_new')
        .select(`
          id,
          project_id,
          designer_id,
          reviewer_id,
          sla_deadline,
          status,
          priority,
          created_at
        `)
        .in('status', ['pending', 'assigned', 'in_review']);

      if (error) throw error;

      const slaStatuses: SLAStatus[] = [];

      for (const review of reviews || []) {
        const status = this.calculateSLAStatus(review);
        slaStatuses.push(status);

        // Send alerts if needed
        await this.handleSLAAlert(status);
      }

      return slaStatuses;
    } catch (error) {
      console.error('Error checking SLAs:', error);
      return [];
    }
  }

  /**
   * Calculate SLA status for a single review
   */
  static calculateSLAStatus(review: any): SLAStatus {
    const now = new Date();
    const deadline = new Date(review.sla_deadline);
    const hoursRemaining = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);

    let status: SLAStatus['status'] = 'on_time';
    let escalationLevel: SLAStatus['escalationLevel'] = 0;

    if (hoursRemaining < 0) {
      // Overdue
      if (Math.abs(hoursRemaining) > 24) {
        status = 'critical';
        escalationLevel = 3;
      } else {
        status = 'overdue';
        escalationLevel = 2;
      }
    } else if (hoursRemaining <= 4) {
      // Approaching deadline
      status = 'approaching';
      escalationLevel = 1;
    }

    return {
      reviewId: review.id,
      projectId: review.project_id,
      designerId: review.designer_id,
      reviewerId: review.reviewer_id,
      deadline: review.sla_deadline,
      status,
      hoursRemaining: Math.round(hoursRemaining * 10) / 10,
      escalationLevel
    };
  }

  /**
   * Handle SLA alerts based on status
   */
  static async handleSLAAlert(slaStatus: SLAStatus): Promise<void> {
    try {
      // Check if we've already sent this type of alert recently
      const recentAlert = await this.getRecentAlert(slaStatus.reviewId, slaStatus.escalationLevel);
      if (recentAlert) return; // Don't spam alerts

      switch (slaStatus.escalationLevel) {
        case 1: // Approaching deadline
          await this.sendApproachingDeadlineAlert(slaStatus);
          break;
        case 2: // Overdue
          await this.sendOverdueAlert(slaStatus);
          break;
        case 3: // Critical overdue
          await this.sendCriticalOverdueAlert(slaStatus);
          break;
      }
    } catch (error) {
      console.error('Error handling SLA alert:', error);
    }
  }

  /**
   * Check for recent alerts to prevent spam
   */
  static async getRecentAlert(reviewId: string, escalationLevel: number): Promise<boolean> {
    try {
      const cutoffTime = new Date();
      cutoffTime.setHours(cutoffTime.getHours() - 2); // Don't send same alert within 2 hours

      const { data, error } = await supabase
        .from('sla_alerts')
        .select('id')
        .eq('review_id', reviewId)
        .eq('escalation_level', escalationLevel)
        .gte('sent_at', cutoffTime.toISOString())
        .limit(1);

      if (error) throw error;

      return (data || []).length > 0;
    } catch (error) {
      console.error('Error checking recent alerts:', error);
      return false;
    }
  }

  /**
   * Send approaching deadline alert
   */
  static async sendApproachingDeadlineAlert(slaStatus: SLAStatus): Promise<void> {
    const notifications = [];

    // Alert reviewer
    if (slaStatus.reviewerId) {
      notifications.push({
        recipient_id: slaStatus.reviewerId,
        notification_type: 'sla_approaching',
        title: 'Review Deadline Approaching',
        message: `Quality review deadline in ${Math.ceil(slaStatus.hoursRemaining)} hours`,
        priority: 'high',
        metadata: {
          review_id: slaStatus.reviewId,
          hours_remaining: slaStatus.hoursRemaining
        }
      });
    }

    await this.insertNotifications(notifications);
    await this.logAlert(slaStatus, 'approaching_deadline');
  }

  /**
   * Send overdue alert
   */
  static async sendOverdueAlert(slaStatus: SLAStatus): Promise<void> {
    const notifications = [];

    // Alert reviewer
    if (slaStatus.reviewerId) {
      notifications.push({
        recipient_id: slaStatus.reviewerId,
        notification_type: 'sla_overdue',
        title: 'Review Overdue',
        message: `Quality review is ${Math.abs(Math.ceil(slaStatus.hoursRemaining))} hours overdue`,
        priority: 'urgent',
        metadata: {
          review_id: slaStatus.reviewId,
          hours_overdue: Math.abs(slaStatus.hoursRemaining)
        }
      });
    }

    // Alert managers
    const managers = await this.getProjectManagers(slaStatus.projectId);
    for (const manager of managers) {
      notifications.push({
        recipient_id: manager.id,
        notification_type: 'sla_overdue_manager',
        title: 'Quality Review Overdue',
        message: `A quality review in your project is overdue`,
        priority: 'high',
        metadata: {
          review_id: slaStatus.reviewId,
          project_id: slaStatus.projectId
        }
      });
    }

    await this.insertNotifications(notifications);
    await this.logAlert(slaStatus, 'overdue');
  }

  /**
   * Send critical overdue alert
   */
  static async sendCriticalOverdueAlert(slaStatus: SLAStatus): Promise<void> {
    const notifications = [];

    // Alert all admins
    const admins = await this.getAdmins();
    for (const admin of admins) {
      notifications.push({
        recipient_id: admin.id,
        notification_type: 'sla_critical',
        title: 'Critical: Review Severely Overdue',
        message: `Quality review is ${Math.abs(Math.ceil(slaStatus.hoursRemaining))} hours overdue - immediate action required`,
        priority: 'urgent',
        metadata: {
          review_id: slaStatus.reviewId,
          project_id: slaStatus.projectId,
          hours_overdue: Math.abs(slaStatus.hoursRemaining)
        }
      });
    }

    // Auto-escalate to admin assignment
    await this.autoEscalateReview(slaStatus.reviewId);

    await this.insertNotifications(notifications);
    await this.logAlert(slaStatus, 'critical_overdue');
  }

  /**
   * Get project managers
   */
  static async getProjectManagers(projectId: string): Promise<{ id: string }[]> {
    try {
      const { data, error } = await supabase
        .from('project_assignments')
        .select('manager_id')
        .eq('project_id', projectId)
        .not('manager_id', 'is', null);

      if (error) throw error;

      return (data || []).map(assignment => ({ id: assignment.manager_id }));
    } catch (error) {
      console.error('Error getting project managers:', error);
      return [];
    }
  }

  /**
   * Get admin users
   */
  static async getAdmins(): Promise<{ id: string }[]> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('role', 'admin');

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting admins:', error);
      return [];
    }
  }

  /**
   * Auto-escalate severely overdue review
   */
  static async autoEscalateReview(reviewId: string): Promise<void> {
    try {
      // Mark review as escalated and assign to admin review
      const { error } = await supabase
        .from('quality_reviews_new')
        .update({
          status: 'escalated',
          escalated_at: new Date().toISOString(),
          priority: 'urgent'
        })
        .eq('id', reviewId);

      if (error) throw error;

      // Create escalation record
      await supabase
        .from('quality_escalations')
        .insert({
          review_id: reviewId,
          escalation_reason: 'sla_violation',
          escalated_at: new Date().toISOString(),
          escalation_type: 'automatic'
        });

    } catch (error) {
      console.error('Error auto-escalating review:', error);
    }
  }

  /**
   * Insert notifications
   */
  static async insertNotifications(notifications: any[]): Promise<void> {
    if (notifications.length === 0) return;

    try {
      const { error } = await supabase
        .from('workflow_notifications')
        .insert(notifications);

      if (error) throw error;
    } catch (error) {
      console.error('Error inserting notifications:', error);
    }
  }

  /**
   * Log alert for tracking
   */
  static async logAlert(slaStatus: SLAStatus, alertType: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('sla_alerts')
        .insert({
          review_id: slaStatus.reviewId,
          alert_type: alertType,
          escalation_level: slaStatus.escalationLevel,
          sent_at: new Date().toISOString(),
          metadata: {
            hours_remaining: slaStatus.hoursRemaining,
            deadline: slaStatus.deadline
          }
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error logging alert:', error);
    }
  }

  /**
   * Get SLA dashboard data
   */
  static async getSLADashboardData(): Promise<{
    totalReviews: number;
    onTime: number;
    approaching: number;
    overdue: number;
    critical: number;
    averageCompletionTime: number;
  }> {
    try {
      const slaStatuses = await this.checkAllSLAs();
      
      const stats = {
        totalReviews: slaStatuses.length,
        onTime: slaStatuses.filter(s => s.status === 'on_time').length,
        approaching: slaStatuses.filter(s => s.status === 'approaching').length,
        overdue: slaStatuses.filter(s => s.status === 'overdue').length,
        critical: slaStatuses.filter(s => s.status === 'critical').length,
        averageCompletionTime: 0 // TODO: Calculate from completed reviews
      };

      return stats;
    } catch (error) {
      console.error('Error getting SLA dashboard data:', error);
      return {
        totalReviews: 0,
        onTime: 0,
        approaching: 0,
        overdue: 0,
        critical: 0,
        averageCompletionTime: 0
      };
    }
  }
}

// Export convenience functions
export const checkAllSLAs = SLAMonitor.checkAllSLAs;
export const getSLADashboardData = SLAMonitor.getSLADashboardData;
