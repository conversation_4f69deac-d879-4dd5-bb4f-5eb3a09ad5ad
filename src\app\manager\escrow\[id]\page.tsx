"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  DollarSign,
  Shield,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  Download,
  Send,
  Eye,
  Lock,
  Unlock,
  CreditCard,
  RefreshCw,
  Calendar,
  User,
  Building
} from "lucide-react";

interface EscrowRelease {
  id: string;
  project_id: string;
  milestone_id: string | null;
  amount: number;
  currency: string;
  release_type: string;
  status: string;
  requested_by: string;
  approved_by: string | null;
  released_at: string | null;
  reason: string;
  conditions_met: boolean;
  documentation_verified: boolean;
  client_approval: boolean;
  manager_approval: boolean;
  created_at: string;
  updated_at: string;
  project: {
    title: string;
    budget: number;
    client: {
      full_name: string;
      email: string;
    };
    designer: {
      full_name: string;
      email: string;
    };
  };
  milestone: {
    title: string;
    description: string;
    completion_percentage: number;
  } | null;
  requester: {
    full_name: string;
    role: string;
  };
  approver: {
    full_name: string;
    role: string;
  } | null;
  documents: EscrowDocument[];
  activities: EscrowActivity[];
}

interface EscrowDocument {
  id: string;
  document_type: string;
  file_name: string;
  file_url: string;
  uploaded_by: string;
  uploaded_at: string;
  verified: boolean;
  uploader: {
    full_name: string;
  };
}

interface EscrowActivity {
  id: string;
  activity_type: string;
  description: string;
  performed_by: string;
  created_at: string;
  performer: {
    full_name: string;
    role: string;
  };
}

export default function EscrowReleaseDetailPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const escrowId = params.id as string;
  
  const [escrow, setEscrow] = useState<EscrowRelease | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [approvalNote, setApprovalNote] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager' && escrowId) {
      fetchEscrowData();
    }
  }, [user, profile, escrowId]);

  const fetchEscrowData = async () => {
    try {
      const { data, error } = await supabase
        .from('paypal_escrow_releases')
        .select(`
          *,
          escrow_hold:paypal_escrow_holds(
            *,
            project:projects(title, status),
            client:profiles!paypal_escrow_holds_client_id_fkey(full_name, email),
            designer:profiles!paypal_escrow_holds_designer_id_fkey(full_name, email)
          ),
          requested_by_profile:profiles!paypal_escrow_releases_requested_by_fkey(full_name, role)
        `)
        .eq('id', escrowId)
        .single();

      if (error) throw error;
      setEscrow(data);
    } catch (error) {
      console.error('Error fetching escrow data:', error);
      router.push('/manager/escrow');
    } finally {
      setLoading(false);
    }
  };

  const handleApproval = async (action: 'approve' | 'reject') => {
    if (!escrow) return;

    setProcessing(true);
    try {
      const updates: any = {
        manager_approval: action === 'approve',
        updated_at: new Date().toISOString()
      };

      if (action === 'approve') {
        updates.approved_by = user?.id;
        // Check if all conditions are met for release
        if (escrow.conditions_met && escrow.documentation_verified && escrow.client_approval) {
          updates.status = 'approved';
          updates.released_at = new Date().toISOString();
        }
      } else {
        updates.status = 'rejected';
      }

      const { error } = await supabase
        .from('escrow_releases')
        .update(updates)
        .eq('id', escrowId);

      if (error) throw error;

      // Log activity
      await supabase.from('escrow_activities').insert({
        escrow_release_id: escrowId,
        activity_type: action === 'approve' ? 'manager_approved' : 'manager_rejected',
        description: `Manager ${action}d escrow release${approvalNote ? `: ${approvalNote}` : ''}`,
        performed_by: user?.id
      });

      // Send notifications
      const notifications = [
        {
          user_id: escrow.requested_by,
          type: 'escrow_decision',
          title: `Escrow Release ${action === 'approve' ? 'Approved' : 'Rejected'}`,
          message: `Your escrow release request has been ${action}d by the manager`,
          data: { escrow_id: escrowId, action }
        }
      ];

      if (action === 'approve' && updates.status === 'approved') {
        notifications.push({
          user_id: escrow.project.client.id,
          type: 'escrow_released',
          title: 'Escrow Funds Released',
          message: `$${escrow.amount.toLocaleString()} has been released for ${escrow.project.title}`,
          data: { escrow_id: escrowId, amount: escrow.amount }
        });
      }

      await supabase.from('notifications').insert(notifications);

      // Log manager activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: escrow.project_id,
        activity_type: 'escrow_decision',
        description: `${action === 'approve' ? 'Approved' : 'Rejected'} escrow release of $${escrow.amount.toLocaleString()}`,
        outcome: action
      });

      setApprovalNote('');
      fetchEscrowData();
      alert(`Escrow release ${action}d successfully!`);
    } catch (error) {
      console.error('Error processing escrow approval:', error);
      alert('Error processing escrow approval. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-amber-500" />;
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'released':
        return <Unlock className="h-5 w-5 text-blue-500" />;
      default:
        return <Lock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case 'released':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getReleaseTypeIcon = (type: string) => {
    switch (type) {
      case 'milestone':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'partial':
        return <CreditCard className="h-5 w-5 text-blue-500" />;
      case 'final':
        return <DollarSign className="h-5 w-5 text-purple-500" />;
      default:
        return <Shield className="h-5 w-5 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!escrow) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Escrow Release Not Found</h2>
          <p className="text-gray-600 mb-4">The escrow release could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/escrow')}>
            Back to Escrow
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <Shield className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Escrow Release</h1>
            <p className="text-gray-600 mt-1">{escrow.project.title}</p>
          </div>
        </div>
      </div>

      {/* Status and Amount */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            {getStatusIcon(escrow.status)}
            <div>
              <div className="flex items-center gap-3 mb-2">
                <span className={getStatusBadge(escrow.status)}>
                  {escrow.status.toUpperCase()}
                </span>
                <div className="flex items-center gap-2">
                  {getReleaseTypeIcon(escrow.release_type)}
                  <span className="text-sm font-medium text-gray-700 capitalize">
                    {escrow.release_type} Release
                  </span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Requested by {escrow.requester.full_name} ({escrow.requester.role}) •
                {new Date(escrow.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="text-right">
            <p className="text-3xl font-bold text-green-600">
              ${escrow.amount.toLocaleString()} {escrow.currency}
            </p>
            <p className="text-sm text-gray-600">
              {((escrow.amount / escrow.project.budget) * 100).toFixed(1)}% of project budget
            </p>
          </div>
        </div>
      </div>

      {/* Manager Approval Section */}
      {escrow.status === 'pending' && !escrow.manager_approval && (
        <div className="bg-amber-50 rounded-xl p-6 border border-amber-200">
          <h2 className="text-xl font-semibold text-amber-900 mb-4">Manager Approval Required</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-amber-800 mb-2">
                Approval Notes (Optional)
              </label>
              <textarea
                value={approvalNote}
                onChange={(e) => setApprovalNote(e.target.value)}
                placeholder="Add any notes about this approval decision..."
                rows={3}
                className="w-full border border-amber-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              />
            </div>

            <div className="flex gap-4">
              <Button
                onClick={() => handleApproval('approve')}
                disabled={processing}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                {processing ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                Approve Release
              </Button>

              <Button
                onClick={() => handleApproval('reject')}
                disabled={processing}
                variant="outline"
                className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
              >
                {processing ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                ) : (
                  <AlertTriangle className="h-4 w-4" />
                )}
                Reject Release
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Release Reason */}
      {escrow.reason && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Release Reason</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            <p className="text-gray-700">{escrow.reason}</p>
          </div>
        </div>
      )}

      {/* Documents */}
      {escrow.documents && escrow.documents.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Supporting Documents</h3>

          <div className="space-y-3">
            {escrow.documents.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-6 w-6 text-blue-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">{doc.file_name}</h4>
                    <p className="text-sm text-gray-600">
                      {doc.document_type} • Uploaded by {doc.uploader.full_name} •
                      {new Date(doc.uploaded_at).toLocaleDateString()}
                    </p>
                  </div>
                  {doc.verified && (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  )}
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(doc.file_url, '_blank')}
                    className="flex items-center gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = doc.file_url;
                      link.download = doc.file_name;
                      link.click();
                    }}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Activity Timeline */}
      {escrow.activities && escrow.activities.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Timeline</h3>

          <div className="space-y-4">
            {escrow.activities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-4 p-4 border-l-4 border-blue-200 bg-blue-50">
                <Calendar className="h-5 w-5 text-blue-500 mt-0.5" />
                <div className="flex-1">
                  <p className="text-gray-900">{activity.description}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    {activity.performer.full_name} ({activity.performer.role}) •
                    {new Date(activity.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Release Information */}
      {escrow.released_at && (
        <div className="bg-green-50 rounded-xl p-6 border border-green-200">
          <div className="flex items-center gap-3 mb-3">
            <Unlock className="h-6 w-6 text-green-600" />
            <h3 className="text-lg font-semibold text-green-900">Funds Released</h3>
          </div>
          <p className="text-green-800">
            ${escrow.amount.toLocaleString()} {escrow.currency} was released on{' '}
            {new Date(escrow.released_at).toLocaleDateString()} at{' '}
            {new Date(escrow.released_at).toLocaleTimeString()}
          </p>
          {escrow.approver && (
            <p className="text-sm text-green-700 mt-2">
              Approved by: {escrow.approver.full_name} ({escrow.approver.role})
            </p>
          )}
        </div>
      )}
    </div>
  );
}
