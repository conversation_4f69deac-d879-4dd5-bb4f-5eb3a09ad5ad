"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  XCircle,
  ArrowLeft,
  Star,
  FileText,
  User,
  Calendar,
  AlertTriangle,
  MessageSquare,
  Save,
  Send,
  RefreshCw
} from "lucide-react";

interface QualityStandard {
  id: string;
  category: string;
  standard_name: string;
  description: string;
  criteria: string[];
  is_mandatory: boolean;
  weight: number;
}

interface QualityReview {
  id: string;
  project_id: string;
  designer_id: string;
  review_type: string;
  status: string;
  overall_score: number | null;
  feedback: string | null;
  revision_notes: string | null;
  revision_count: number;
  created_at: string;
  project: {
    title: string;
    description: string;
    budget: number;
    client: {
      full_name: string;
    };
  };
  designer: {
    full_name: string;
    email: string;
  };
}

interface QualityFeedback {
  standard_id: string;
  passed: boolean;
  score: number;
  comments: string;
  suggestions: string;
}

export default function QualityReviewPage({ params }: { params: { id: string } }) {
  const { user, profile } = useOptimizedAuth();
  const [review, setReview] = useState<QualityReview | null>(null);
  const [standards, setStandards] = useState<QualityStandard[]>([]);
  const [feedback, setFeedback] = useState<Record<string, QualityFeedback>>({});
  const [overallScore, setOverallScore] = useState<number>(3);
  const [generalFeedback, setGeneralFeedback] = useState<string>('');
  const [revisionNotes, setRevisionNotes] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchReview();
      fetchStandards();
    }
  }, [user, profile, params.id]);

  const fetchReview = async () => {
    try {
      const { data, error } = await supabase
        .from('quality_reviews')
        .select(`
          *,
          project:projects(title, description, budget, client:profiles!client_id(full_name)),
          designer:profiles!quality_reviews_designer_id_fkey(full_name, email)
        `)
        .eq('id', params.id)
        .single();

      if (error) throw error;
      setReview(data);
      
      if (data.feedback) setGeneralFeedback(data.feedback);
      if (data.revision_notes) setRevisionNotes(data.revision_notes);
      if (data.overall_score) setOverallScore(data.overall_score);

      // Fetch existing feedback
      const { data: existingFeedback } = await supabase
        .from('quality_feedback')
        .select('*')
        .eq('review_id', params.id);

      if (existingFeedback) {
        const feedbackMap: Record<string, QualityFeedback> = {};
        existingFeedback.forEach(item => {
          feedbackMap[item.standard_id] = {
            standard_id: item.standard_id,
            passed: item.passed,
            score: item.score,
            comments: item.comments || '',
            suggestions: item.suggestions || ''
          };
        });
        setFeedback(feedbackMap);
      }
    } catch (error) {
      console.error('Error fetching review:', error);
    }
  };

  const fetchStandards = async () => {
    try {
      const { data, error } = await supabase
        .from('quality_standards')
        .select('*')
        .order('category', { ascending: true })
        .order('weight', { ascending: false });

      if (error) throw error;
      setStandards(data || []);
    } catch (error) {
      console.error('Error fetching standards:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateFeedback = (standardId: string, updates: Partial<QualityFeedback>) => {
    setFeedback(prev => ({
      ...prev,
      [standardId]: {
        ...prev[standardId],
        standard_id: standardId,
        passed: prev[standardId]?.passed ?? true,
        score: prev[standardId]?.score ?? 3,
        comments: prev[standardId]?.comments ?? '',
        suggestions: prev[standardId]?.suggestions ?? '',
        ...updates
      }
    }));
  };

  const saveReview = async (status: 'approved' | 'rejected' | 'needs_revision') => {
    if (!review) return;
    
    setSubmitting(true);
    try {
      // Update review
      const { error: reviewError } = await supabase
        .from('quality_reviews')
        .update({
          status,
          overall_score: overallScore,
          feedback: generalFeedback,
          revision_notes: revisionNotes,
          reviewed_at: new Date().toISOString(),
          revision_count: status === 'needs_revision' ? review.revision_count + 1 : review.revision_count
        })
        .eq('id', params.id);

      if (reviewError) throw reviewError;

      // Save feedback for each standard
      for (const [standardId, feedbackItem] of Object.entries(feedback)) {
        const { error: feedbackError } = await supabase
          .from('quality_feedback')
          .upsert({
            review_id: params.id,
            standard_id: standardId,
            passed: feedbackItem.passed,
            score: feedbackItem.score,
            comments: feedbackItem.comments,
            suggestions: feedbackItem.suggestions
          });

        if (feedbackError) throw feedbackError;
      }

      // Create notifications based on status
      if (status === 'approved') {
        // Notify designer of approval
        await supabase.from('workflow_notifications').insert({
          recipient_id: review.designer_id,
          notification_type: 'quality_approved',
          title: 'Quality Review Approved',
          message: `Your submission for "${review.project.title}" has been approved by the quality team.`,
          priority: 'normal'
        });
      } else if (status === 'rejected' || status === 'needs_revision') {
        // Notify designer of rejection/revision needed
        await supabase.from('workflow_notifications').insert({
          recipient_id: review.designer_id,
          notification_type: 'quality_revision_needed',
          title: 'Revision Required',
          message: `Your submission for "${review.project.title}" requires revision. Please check the feedback and resubmit.`,
          priority: 'high'
        });
      }

      // Redirect back to dashboard
      window.location.href = '/quality/dashboard';
    } catch (error) {
      console.error('Error saving review:', error);
      alert('Error saving review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const calculateOverallProgress = () => {
    const totalStandards = standards.length;
    const reviewedStandards = Object.keys(feedback).length;
    return totalStandards > 0 ? (reviewedStandards / totalStandards) * 100 : 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!review) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Review Not Found</h1>
          <p className="text-gray-600 mb-4">The quality review you're looking for doesn't exist.</p>
          <Button onClick={() => window.location.href = '/quality/dashboard'}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => window.location.href = '/quality/dashboard'}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">Quality Review</h1>
          <p className="text-gray-600 mt-1">Review submission for quality standards compliance</p>
        </div>
      </div>

      {/* Project Info */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">{review.project.title}</h3>
            <p className="text-gray-600 mb-4">{review.project.description}</p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Client:</span> {review.project.client.full_name}
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Designer:</span> {review.designer.full_name}
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Submitted:</span> {new Date(review.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>
          <div>
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Review Progress</h4>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-brown-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${calculateOverallProgress()}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600">
                {Object.keys(feedback).length} of {standards.length} standards reviewed
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quality Standards Review */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Quality Standards Checklist</h2>
          <p className="text-gray-600 mt-1">Review each standard and provide feedback</p>
        </div>

        <div className="divide-y divide-gray-200">
          {standards.map((standard) => (
            <div key={standard.id} className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{standard.standard_name}</h3>
                    {standard.is_mandatory && (
                      <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                        Mandatory
                      </span>
                    )}
                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                      {standard.category}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-3">{standard.description}</p>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <h4 className="font-medium text-gray-900 mb-2">Criteria:</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                      {standard.criteria.map((criterion, index) => (
                        <li key={index}>{criterion}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pass/Fail Status
                  </label>
                  <div className="flex gap-3">
                    <Button
                      variant={feedback[standard.id]?.passed === true ? "default" : "outline"}
                      size="sm"
                      onClick={() => updateFeedback(standard.id, { passed: true })}
                      className="flex items-center gap-2"
                    >
                      <CheckCircle className="h-4 w-4" />
                      Pass
                    </Button>
                    <Button
                      variant={feedback[standard.id]?.passed === false ? "default" : "outline"}
                      size="sm"
                      onClick={() => updateFeedback(standard.id, { passed: false })}
                      className="flex items-center gap-2"
                    >
                      <XCircle className="h-4 w-4" />
                      Fail
                    </Button>
                  </div>

                  <label className="block text-sm font-medium text-gray-700 mt-4 mb-2">
                    Score (1-5)
                  </label>
                  <div className="flex gap-2">
                    {[1, 2, 3, 4, 5].map((score) => (
                      <Button
                        key={score}
                        variant={feedback[standard.id]?.score === score ? "default" : "outline"}
                        size="sm"
                        onClick={() => updateFeedback(standard.id, { score })}
                        className="w-10 h-10 p-0"
                      >
                        {score}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Comments
                    </label>
                    <textarea
                      value={feedback[standard.id]?.comments || ''}
                      onChange={(e) => updateFeedback(standard.id, { comments: e.target.value })}
                      placeholder="Provide specific feedback about this standard..."
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      rows={3}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Suggestions for Improvement
                    </label>
                    <textarea
                      value={feedback[standard.id]?.suggestions || ''}
                      onChange={(e) => updateFeedback(standard.id, { suggestions: e.target.value })}
                      placeholder="Suggest specific improvements..."
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Overall Review */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Overall Review</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Overall Score (1-5)
            </label>
            <div className="flex gap-2 mb-4">
              {[1, 2, 3, 4, 5].map((score) => (
                <Button
                  key={score}
                  variant={overallScore === score ? "default" : "outline"}
                  size="lg"
                  onClick={() => setOverallScore(score)}
                  className="w-12 h-12 p-0 flex items-center justify-center"
                >
                  <Star className={`h-5 w-5 ${overallScore >= score ? 'fill-current' : ''}`} />
                </Button>
              ))}
            </div>

            <label className="block text-sm font-medium text-gray-700 mb-2">
              General Feedback
            </label>
            <textarea
              value={generalFeedback}
              onChange={(e) => setGeneralFeedback(e.target.value)}
              placeholder="Provide overall feedback about the submission..."
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              rows={4}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Revision Notes (if applicable)
            </label>
            <textarea
              value={revisionNotes}
              onChange={(e) => setRevisionNotes(e.target.value)}
              placeholder="Specific notes for revision requirements..."
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              rows={6}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-end">
        <Button
          variant="outline"
          onClick={() => saveReview('needs_revision')}
          disabled={submitting}
          className="flex items-center gap-2 border-orange-200 text-orange-600 hover:bg-orange-50"
        >
          <MessageSquare className="h-4 w-4" />
          Request Revision
        </Button>
        
        <Button
          variant="outline"
          onClick={() => saveReview('rejected')}
          disabled={submitting}
          className="flex items-center gap-2 border-red-200 text-red-600 hover:bg-red-50"
        >
          <XCircle className="h-4 w-4" />
          Reject Submission
        </Button>
        
        <Button
          onClick={() => saveReview('approved')}
          disabled={submitting}
          className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="h-4 w-4" />
          Approve Submission
        </Button>
      </div>
    </div>
  );
}
