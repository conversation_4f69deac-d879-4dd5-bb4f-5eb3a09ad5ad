import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generates an invoice number with a specified prefix and random digits
 */
export const generateInvoiceNumber = (prefix: string = 'INV') => {
  const randomDigits = Math.floor(10000 + Math.random() * 90000); // 5-digit random number
  const timestamp = Date.now().toString().slice(-4); // Last 4 digits of timestamp
  return `${prefix}-${timestamp}${randomDigits}`;
};

/**
 * Creates an invoice for a milestone
 */
export const createInvoiceForMilestone = async (
  milestoneId: string,
  projectId: string,
  clientId: string,
  amount: number,
  description: string,
  dueDate: Date = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Default: 30 days from now
) => {
  try {
    const invoiceNumber = generateInvoiceNumber();
    
    const { data, error } = await supabase
      .from('invoices')
      .insert({
        id: uuidv4(),
        invoice_number: invoiceNumber,
        amount,
        status: 'pending',
        due_date: dueDate.toISOString(),
        issued_date: new Date().toISOString(),
        description,
        project_id: projectId,
        client_id: clientId
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating invoice:', error);
    throw error;
  }
};

/**
 * Updates an invoice status
 */
export const updateInvoiceStatus = async (invoiceId: string, status: 'pending' | 'paid' | 'overdue') => {
  try {
    const { data, error } = await supabase
      .from('invoices')
      .update({ status })
      .eq('id', invoiceId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating invoice status:', error);
    throw error;
  }
};

/**
 * Gets all invoices for a client
 */
export const getClientInvoices = async (clientId: string) => {
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        id,
        invoice_number,
        amount,
        status,
        due_date,
        issued_date,
        description,
        project_id,
        projects(title)
      `)
      .eq('client_id', clientId)
      .order('issued_date', { ascending: false });
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching client invoices:', error);
    throw error;
  }
};

/**
 * Gets all invoices for a project
 */
export const getProjectInvoices = async (projectId: string) => {
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .eq('project_id', projectId)
      .order('issued_date', { ascending: false });
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching project invoices:', error);
    throw error;
  }
};

/**
 * Checks for overdue invoices and updates their status
 */
export const checkAndUpdateOverdueInvoices = async () => {
  try {
    const today = new Date().toISOString();
    
    // Find all pending invoices that are past their due date
    const { data, error } = await supabase
      .from('invoices')
      .update({ status: 'overdue' })
      .eq('status', 'pending')
      .lt('due_date', today)
      .select();
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating overdue invoices:', error);
    throw error;
  }
};
