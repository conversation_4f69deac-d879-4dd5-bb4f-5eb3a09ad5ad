"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { 
  Bell, 
  AlertCircle, 
  Check, 
  Trash2, 
  FileText, 
  MessageSquare,
  FolderKanban,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw
} from "lucide-react";

type Notification = {
  id: string;
  type: string;
  title: string;
  message: string;
  read: boolean;
  created_at: string;
  data: {
    project_id?: string;
    submission_id?: string;
    message_id?: string;
    project_title?: string;
    submission_title?: string;
    sender_name?: string;
  };
};

export default function DesignerNotifications() {
  const { user } = useOptimizedAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<"all" | "unread">("all");

  useEffect(() => {
    if (user) {
      fetchNotifications();
    }
  }, [user, filter]);

  const fetchNotifications = async () => {
    try {
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });
      
      if (filter === "unread") {
        query = query.eq('read', false);
      }
      
      const { data, error } = await query;

      if (error) throw error;

      setNotifications(data || []);
    } catch (error: Error | unknown) {
      console.error('Error fetching notifications:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('Failed to load notifications');
      }
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (id: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id);

      if (error) throw error;

      setNotifications(notifications.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      ));
    } catch (error: Error | unknown) {
      console.error('Error marking notification as read:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('An unknown error occurred');
      }
    }
  };  const markAllAsRead = async () => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user?.id)
        .eq('read', false);

      if (error) throw error;

      setNotifications(notifications.map(notification => ({ ...notification, read: true })));
    } catch (error: Error | unknown) {
      console.error('Error marking all notifications as read:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('An unknown error occurred');
      }
    }
  };

  const deleteNotification = async (id: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setNotifications(notifications.filter(notification => notification.id !== id));
    } catch (error: Error | unknown) {
      console.error('Error deleting notification:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('An unknown error occurred');
      }
    }
  };

  const clearAllNotifications = async () => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', user?.id);

      if (error) throw error;

      setNotifications([]);
    } catch (error: Error | unknown) {
      console.error('Error clearing all notifications:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('An unknown error occurred');
      }
    }
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.round(diffMs / 1000);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);
    
    if (diffSec < 60) return `${diffSec} seconds ago`;
    if (diffMin < 60) return `${diffMin} minutes ago`;
    if (diffHour < 24) return `${diffHour} hours ago`;
    if (diffDay < 7) return `${diffDay} days ago`;
    
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'submission_approved':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'submission_rejected':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'revision_requested':
        return <RefreshCw className="h-6 w-6 text-yellow-500" />;
      case 'new_message':
        return <MessageSquare className="h-6 w-6 text-blue-500" />;
      case 'project_update':
        return <FolderKanban className="h-6 w-6 text-purple-500" />;
      case 'deadline_reminder':
        return <Clock className="h-6 w-6 text-orange-500" />;
      default:
        return <Bell className="h-6 w-6 text-gray-500" />;
    }
  };

  const getNotificationLink = (notification: Notification) => {
    const { type, data } = notification;
    
    switch (type) {
      case 'submission_approved':
      case 'submission_rejected':
      case 'revision_requested':
        return data.submission_id 
          ? `/designer/projects/${data.project_id}/submissions/${data.submission_id}`
          : `/designer/projects/${data.project_id}`;
      case 'new_message':
        return `/designer/messages${data.project_id ? `?project=${data.project_id}` : ''}`;
      case 'project_update':
      case 'deadline_reminder':
        return `/designer/projects/${data.project_id}`;
      default:
        return '/designer';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-bold">Notifications</h1>
        <div className="flex space-x-4">
          <div className="flex rounded-md overflow-hidden">
            <button
              onClick={() => setFilter("all")}
              className={`px-4 py-2 text-sm font-medium ${
                filter === "all"
                  ? "bg-primary text-white"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              All
            </button>
            <button
              onClick={() => setFilter("unread")}
              className={`px-4 py-2 text-sm font-medium ${
                filter === "unread"
                  ? "bg-primary text-white"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              Unread
            </button>
          </div>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={markAllAsRead}
            disabled={!notifications.some(n => !n.read)}
          >
            <Check className="h-4 w-4 mr-2" />
            Mark All Read
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={clearAllNotifications}
            disabled={notifications.length === 0}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All
          </Button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <p>{error}</p>
        </div>
      )}
      
      {notifications.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="font-medium mb-1">No notifications</h3>
          <p className="text-sm text-gray-500">
            {filter === "unread" 
              ? "You don't have any unread notifications." 
              : "You don't have any notifications yet."}
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="divide-y">
            {notifications.map((notification) => (
              <div 
                key={notification.id} 
                className={`p-6 hover:bg-gray-50 transition-colors ${!notification.read ? 'bg-blue-50 hover:bg-blue-50' : ''}`}
              >
                <div className="flex">
                  <div className="flex-shrink-0 mr-4">
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-grow">
                    <div className="flex justify-between items-start">
                      <Link href={getNotificationLink(notification)}>
                        <h3 className="font-medium hover:text-primary transition-colors">
                          {notification.title}
                        </h3>
                      </Link>
                      <span className="text-xs text-gray-500">
                        {getTimeAgo(notification.created_at)}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mt-1">{notification.message}</p>
                    
                    {notification.data && (
                      <div className="mt-2 text-sm">
                        {notification.data.project_title && (
                          <span className="text-gray-500">
                            Project: {notification.data.project_title}
                          </span>
                        )}
                        
                        {notification.data.submission_title && (
                          <>
                            <span className="mx-2">•</span>
                            <span className="text-gray-500">
                              Submission: {notification.data.submission_title}
                            </span>
                          </>
                        )}
                      </div>
                    )}
                    
                    <div className="mt-3 flex space-x-3">
                      <Link href={getNotificationLink(notification)}>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </Link>
                      
                      {!notification.read && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => markAsRead(notification.id)}
                        >
                          Mark as Read
                        </Button>
                      )}
                      
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => deleteNotification(notification.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}