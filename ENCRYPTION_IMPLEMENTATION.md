# Payment Method Encryption Implementation

## Overview

This document outlines the implementation of AES-256-GCM encryption for sensitive payment method data in the Senior's Archi-firm platform. This approach provides strong security without requiring database-level permissions.

## 🔐 Security Features

- **AES-256-GCM Encryption**: Industry-standard encryption algorithm
- **Unique IV per record**: Prevents pattern analysis attacks
- **Authentication tags**: Prevents data tampering
- **Base64 encoding**: Safe storage in text database fields
- **Key rotation support**: Seamless key updates every 3 months
- **Application-level encryption**: No special database permissions required

## 📁 Files Modified/Created

### New Files
- `src/lib/encryption.ts` - Core encryption utilities
- `src/app/api/encrypt-payment-data/route.ts` - Server-side encryption API
- `scripts/generate-encryption-keys.js` - Key generation utility
- `ENCRYPTION_IMPLEMENTATION.md` - This documentation

### Modified Files
- `src/components/designer/AddPayoutMethodForm.tsx` - Encrypt data before saving
- `src/components/designer/PayoutSettings.tsx` - Decrypt data for display
- `ENVIRONMENT_SETUP.md` - Added encryption configuration

## 🔑 Environment Variables Required

```bash
# Primary encryption key (64-character hex string)
ENCRYPTION_KEY=your-64-character-hex-encryption-key

# Backup encryption key for rotation
ENCRYPTION_KEY_BACKUP=your-backup-64-character-hex-encryption-key
```

## 🚀 Setup Instructions

### 1. Generate Encryption Keys
```bash
node scripts/generate-encryption-keys.js
```

### 2. Add Keys to Environment
Add the generated keys to your `.env.local` file:
```bash
ENCRYPTION_KEY=generated-primary-key-here
ENCRYPTION_KEY_BACKUP=generated-backup-key-here
```

### 3. Restart Development Server
```bash
npm run dev
```

## 🔒 What Gets Encrypted

The following sensitive fields are automatically encrypted:
- Account numbers
- Routing numbers
- IBAN codes
- SWIFT codes
- Sort codes
- BSB numbers

**Not encrypted** (for business functionality):
- Account holder names
- Bank names
- PayPal email addresses
- Bank countries/currencies

## 🛠 How It Works

### Encryption Process (Server-Side)
1. User enters payment method data in browser
2. Sensitive fields are sent to `/api/encrypt-payment-data` endpoint
3. Server validates user authentication
4. Each field is encrypted with AES-256-GCM on server
5. Encrypted data is base64 encoded
6. Returned to client and stored in database as text

### Decryption Process (Server-Side)
1. Encrypted data is retrieved from database
2. When user requests to view full numbers, data is sent to server
3. Server validates authentication and decrypts with AES-256-GCM
4. Decrypted data is returned to client
5. Displayed to authorized users (masked by default)

### Key Features
- **Server-side encryption**: Encryption keys never leave the server
- **Automatic encryption**: Happens transparently in forms
- **Masked display**: Shows only last 4 characters by default
- **Toggle visibility**: Users can reveal full numbers when needed
- **Authentication required**: All encryption/decryption requires valid user session
- **Error handling**: Graceful fallback if decryption fails

## 🔄 Key Rotation (Every 3 Months)

### Process
1. Generate new keys: `node scripts/generate-encryption-keys.js`
2. Set new primary key as `ENCRYPTION_KEY`
3. Keep old primary key as `ENCRYPTION_KEY_BACKUP`
4. Test that existing data can still be decrypted
5. Gradually re-encrypt data with new key (optional)
6. Remove old backup key after migration

### Migration Script (Future Enhancement)
```javascript
// Planned: scripts/rotate-encryption-keys.js
// Will re-encrypt all existing data with new keys
```

## 🧪 Testing

### Manual Testing
1. Add a new payment method with account details
2. Verify data is encrypted in database (should be base64 strings)
3. View payment methods list - should show masked numbers
4. Toggle visibility - should show full numbers
5. Edit payment method - should work with encrypted data

### API Testing
```bash
# Check encryption status
curl -X GET /api/encrypt-payment-data

# Test encryption (requires auth token)
curl -X POST /api/encrypt-payment-data \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"action": "encrypt", "data": {"account_number": "**********"}}'
```

## 🚨 Security Considerations

### Key Management
- **Never commit keys to version control**
- Store keys in secure environment variable systems
- Use different keys for development/staging/production
- Implement key rotation every 3 months

### Database Security
- Encrypted data is stored as base64 text
- No plaintext sensitive data in database
- Row Level Security (RLS) still applies
- Regular database backups include encrypted data

### Application Security
- Encryption/decryption only happens server-side
- Keys are never sent to client
- Masked display by default
- Audit logging for key access (future enhancement)

## 📊 Performance Impact

- **Encryption**: ~1ms per field
- **Decryption**: ~1ms per field
- **Storage**: ~33% larger (base64 encoding)
- **Database queries**: No impact on query performance
- **Memory**: Minimal additional usage

## 🐛 Troubleshooting

### Common Issues

**"****ERROR" displayed instead of account number**
- Check `ENCRYPTION_KEY` is set correctly
- Verify key is exactly 64 characters
- Check for typos in environment variables

**"Failed to encrypt sensitive data" error**
- Ensure encryption key is valid
- Check Node.js crypto module is available
- Verify environment variables are loaded

**Cannot decrypt existing data after key change**
- Set old key as `ENCRYPTION_KEY_BACKUP`
- Verify backup key is correct
- Check key rotation process

### Debug Mode
Add to your `.env.local` for debugging:
```bash
DEBUG_ENCRYPTION=true
```

## 🔮 Future Enhancements

1. **Automatic key rotation script**
2. **Audit logging for encryption/decryption**
3. **Hardware Security Module (HSM) integration**
4. **Field-level encryption for additional fields**
5. **Compliance reporting (PCI DSS, etc.)**

## 📞 Support

For encryption-related issues:
1. Check this documentation
2. Verify environment setup
3. Test with the key generation script
4. Check application logs for encryption errors
