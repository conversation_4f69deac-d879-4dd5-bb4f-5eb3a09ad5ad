"use client";

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>, Shield, BarChart3, Target } from 'lucide-react';

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  preferences: boolean;
}

const CookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always true, can't be disabled
    analytics: false,
    marketing: false,
    preferences: false,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('cookie-consent');
    if (!consent) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const saveConsent = async (acceptedPreferences: CookiePreferences) => {
    const consentData = {
      ...acceptedPreferences,
      timestamp: new Date().toISOString(),
      version: '1.0'
    };

    // Save to localStorage
    localStorage.setItem('cookie-consent', JSON.stringify(consentData));

    // Save to database for compliance tracking
    try {
      await fetch('/api/cookie-consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          consent_given: true,
          consent_types: acceptedPreferences,
          session_id: getSessionId(),
        }),
      });
    } catch (error) {
      console.error('Failed to save cookie consent:', error);
    }

    setShowBanner(false);
    setShowDetails(false);

    // Initialize analytics/marketing scripts based on consent
    initializeScripts(acceptedPreferences);
  };

  const acceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    };
    saveConsent(allAccepted);
  };

  const acceptNecessaryOnly = () => {
    const necessaryOnly = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    };
    saveConsent(necessaryOnly);
  };

  const acceptSelected = () => {
    saveConsent(preferences);
  };

  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('session-id');
    if (!sessionId) {
      sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);
      sessionStorage.setItem('session-id', sessionId);
    }
    return sessionId;
  };

  const initializeScripts = (prefs: CookiePreferences) => {
    // Initialize Google Analytics if analytics consent given
    if (prefs.analytics && typeof window !== 'undefined') {
      // Add Google Analytics initialization here
      console.log('Analytics consent given - initializing tracking');
    }

    // Initialize marketing scripts if marketing consent given
    if (prefs.marketing && typeof window !== 'undefined') {
      // Add marketing scripts here (Facebook Pixel, etc.)
      console.log('Marketing consent given - initializing marketing scripts');
    }
  };

  const cookieTypes = [
    {
      key: 'necessary' as keyof CookiePreferences,
      title: 'Necessary Cookies',
      description: 'Essential for the website to function properly. These cannot be disabled.',
      icon: <Shield className="h-5 w-5" />,
      required: true,
    },
    {
      key: 'analytics' as keyof CookiePreferences,
      title: 'Analytics Cookies',
      description: 'Help us understand how visitors interact with our website.',
      icon: <BarChart3 className="h-5 w-5" />,
      required: false,
    },
    {
      key: 'marketing' as keyof CookiePreferences,
      title: 'Marketing Cookies',
      description: 'Used to deliver personalized advertisements and track campaign performance.',
      icon: <Target className="h-5 w-5" />,
      required: false,
    },
    {
      key: 'preferences' as keyof CookiePreferences,
      title: 'Preference Cookies',
      description: 'Remember your settings and preferences for a better experience.',
      icon: <Cookie className="h-5 w-5" />,
      required: false,
    },
  ];

  return (
    <AnimatePresence>
      {showBanner && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
          className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg"
        >
          <div className="container mx-auto px-4 py-6">
            {!showDetails ? (
              // Simple Banner
              <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                <div className="flex items-start gap-3 flex-1">
                  <Cookie className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">We use cookies</h3>
                    <p className="text-sm text-gray-600 max-w-2xl">
                      We use cookies to enhance your browsing experience, serve personalized content, 
                      and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.
                    </p>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDetails(true)}
                    className="text-sm"
                  >
                    Customize
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={acceptNecessaryOnly}
                    className="text-sm"
                  >
                    Necessary Only
                  </Button>
                  <Button
                    size="sm"
                    onClick={acceptAll}
                    className="text-sm bg-primary hover:bg-primary/90"
                  >
                    Accept All
                  </Button>
                </div>
              </div>
            ) : (
              // Detailed Settings
              <div className="max-w-4xl mx-auto">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">Cookie Preferences</h3>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                <div className="grid gap-4 mb-6">
                  {cookieTypes.map((type) => (
                    <div
                      key={type.key}
                      className="flex items-start gap-4 p-4 border border-gray-200 rounded-lg"
                    >
                      <div className="text-primary mt-1">{type.icon}</div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-1">{type.title}</h4>
                        <p className="text-sm text-gray-600 mb-3">{type.description}</p>
                      </div>
                      <div className="flex-shrink-0">
                        {type.required ? (
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            Required
                          </span>
                        ) : (
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={preferences[type.key]}
                              onChange={(e) =>
                                setPreferences((prev) => ({
                                  ...prev,
                                  [type.key]: e.target.checked,
                                }))
                              }
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                          </label>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex flex-col sm:flex-row gap-3 justify-end">
                  <Button
                    variant="outline"
                    onClick={acceptNecessaryOnly}
                    className="text-sm"
                  >
                    Necessary Only
                  </Button>
                  <Button
                    onClick={acceptSelected}
                    className="text-sm bg-primary hover:bg-primary/90"
                  >
                    Save Preferences
                  </Button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CookieConsent;
