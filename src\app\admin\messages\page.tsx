"use client";

import { useState, useEffect, useCallback } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { useRealtimeMessages } from "@/hooks/useRealtimeMessages";
import { Button } from "@/components/ui/button";
import {
  Search,
  Filter,
  User,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  Flag,
  Eye,
  Calendar,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

type AdminConversation = {
  id: string;
  type: 'direct' | 'project';
  title: string;
  project_id?: string;
  project_title?: string;
  participants: {
    id: string;
    full_name: string;
    role: string;
    avatar_url?: string;
  }[];
  client_name?: string;
  designer_name?: string;
  last_message_at: string;
  unread_count: number;
  flagged_count: number;
  total_messages: number;
  is_selected: boolean;
  latest_message?: {
    content: string;
    sender_id: string;
    profiles?: {
      full_name: string;
    };
  };
};

type AdminMessage = {
  id: string;
  conversation_id: string;
  sender_id: string;
  sender_name: string;
  sender_role: string;
  content: string;
  message_type: string;
  created_at: string;
  is_flagged?: boolean;
  admin_reviewed?: boolean;
  admin_reviewed_at?: string | null;
  profiles: {
    id: string;
    full_name: string;
    avatar_url?: string;
    role: string;
  };
};

export default function AdminMessages() {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const projectFilter = searchParams.get('project');

  const [conversations, setConversations] = useState<AdminConversation[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<AdminConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<AdminConversation | null>(null);
  const [messages, setMessages] = useState<AdminMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<'all' | 'flagged' | 'unread'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const conversationsPerPage = 10;

  // Real-time message handlers
  const handleNewMessage = useCallback((message: any) => {
    // Refresh conversations to update counts
    fetchConversations();

    // If this message is for the currently selected conversation, refresh messages
    if (selectedConversation && message.conversation_id === selectedConversation.id) {
      fetchMessages(selectedConversation.id);
    }
  }, [selectedConversation]);

  const handleMessageUpdate = useCallback((message: any) => {
    // Update the specific message in the current view
    if (selectedConversation && message.conversation_id === selectedConversation.id) {
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === message.id
            ? {
                ...msg,
                ...message,
                sender_name: msg.sender_name, // Preserve existing sender info
                profiles: msg.profiles
              }
            : msg
        )
      );
    }

    // Refresh conversations to update counts
    fetchConversations();
  }, [selectedConversation]);

  const handleConversationUpdate = useCallback(() => {
    // Refresh conversations list
    fetchConversations();
  }, []);

  // Set up real-time subscriptions
  useRealtimeMessages({
    conversationId: selectedConversation?.id,
    onNewMessage: handleNewMessage,
    onMessageUpdate: handleMessageUpdate,
    onConversationUpdate: handleConversationUpdate,
    enabled: !!user
  });

  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user, projectFilter]);

  useEffect(() => {
    // Apply filters
    let result = conversations;

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        conversation =>
          conversation.title.toLowerCase().includes(term) ||
          (conversation.project_title && conversation.project_title.toLowerCase().includes(term)) ||
          (conversation.client_name && conversation.client_name.toLowerCase().includes(term)) ||
          (conversation.designer_name && conversation.designer_name.toLowerCase().includes(term)) ||
          conversation.participants.some(p => p.full_name.toLowerCase().includes(term))
      );
    }

    // Type filter
    if (filterType === 'flagged') {
      result = result.filter(conversation => conversation.flagged_count > 0);
    } else if (filterType === 'unread') {
      result = result.filter(conversation => conversation.unread_count > 0);
    }

    setFilteredConversations(result);
    setTotalPages(Math.ceil(result.length / conversationsPerPage));
    setCurrentPage(1); // Reset to first page when filters change
  }, [conversations, searchTerm, filterType]);

  const fetchConversations = async () => {
    setLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      const response = await fetch('/api/conversations', {
        headers
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversations');
      }

      const conversationsData = await response.json();

      // Transform unified messaging data to admin format
      const adminConversations: AdminConversation[] = conversationsData.map((conv: any) => {
        // Extract client and designer from participants
        const client = conv.participants?.find((p: any) => p[0]?.role === 'client')?.[0];
        const designer = conv.participants?.find((p: any) => p[0]?.role === 'designer')?.[0];

        return {
          id: conv.id,
          type: conv.type,
          title: conv.title,
          project_id: conv.project?.id,
          project_title: conv.project?.title || conv.title,
          participants: conv.participants?.map((p: any) => p[0]).filter(Boolean) || [],
          client_name: client?.full_name || 'Unknown Client',
          designer_name: designer?.full_name || 'Unknown Designer',
          last_message_at: conv.last_message_at || conv.created_at,
          unread_count: conv.unread_count || 0,
          flagged_count: conv.flagged_count || 0,
          total_messages: conv.total_messages || 0,
          is_selected: false,
          latest_message: conv.latest_message
        };
      });

      // If project filter is applied, only show that conversation
      let filteredConversations = adminConversations;
      if (projectFilter) {
        filteredConversations = adminConversations.filter(c => c.project_id === projectFilter);
      }

      // Sort by last message date (newest first)
      filteredConversations.sort((a, b) =>
        new Date(b.last_message_at).getTime() - new Date(a.last_message_at).getTime()
      );

      setConversations(filteredConversations);
      setFilteredConversations(filteredConversations);
      setTotalPages(Math.ceil(filteredConversations.length / conversationsPerPage));

      // If there's only one conversation or a project filter, select it automatically
      if (filteredConversations.length === 1 || projectFilter) {
        const conversation = filteredConversations[0];
        setSelectedConversation({
          ...conversation,
          is_selected: true
        });
        fetchMessages(conversation.id);
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching conversations:', error);
        setError(error.message || 'Failed to load conversations');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchMessages = async (conversationId: string) => {
    setLoadingMessages(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        headers
      });

      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }

      const data = await response.json();
      const messagesData = data.messages || [];

      // Transform unified messaging data to admin format
      const adminMessages: AdminMessage[] = messagesData.map((msg: any) => ({
        id: msg.id,
        conversation_id: conversationId,
        sender_id: msg.sender_id,
        sender_name: msg.profiles?.full_name || 'Unknown User',
        sender_role: msg.profiles?.role || 'unknown',
        content: msg.content,
        message_type: msg.message_type || 'text',
        created_at: msg.created_at,
        is_flagged: msg.is_flagged || false,
        admin_reviewed: msg.admin_reviewed || false,
        admin_reviewed_at: msg.admin_reviewed_at,
        admin_notes: msg.admin_notes,
        flagged_by: msg.flagged_by,
        flagged_at: msg.flagged_at,
        flag_reason: msg.flag_reason,
        profiles: msg.profiles
      }));

      // Sort by date (oldest first)
      adminMessages.sort((a, b) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      setMessages(adminMessages);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error fetching messages:', error);
        setError(error.message || 'Failed to load messages');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    } finally {
      setLoadingMessages(false);
    }
  };

  const handleConversationSelect = (conversation: AdminConversation) => {
    // Update selected conversation
    setConversations(conversations.map(c => ({
      ...c,
      is_selected: c.id === conversation.id
    })));

    setFilteredConversations(filteredConversations.map(c => ({
      ...c,
      is_selected: c.id === conversation.id
    })));

    setSelectedConversation({
      ...conversation,
      is_selected: true
    });

    // Fetch messages for the selected conversation
    fetchMessages(conversation.id);
  };

  const markMessagesAsReviewed = async () => {
    if (!selectedConversation) return;

    try {
      const { data: { session } } = await supabase.auth.getSession();

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      const response = await fetch('/api/admin/messages/review', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          conversation_id: selectedConversation.id
        })
      });

      if (!response.ok) {
        throw new Error('Failed to mark messages as reviewed');
      }

      // Update local state
      const updatedMessages = messages.map(message => ({
        ...message,
        admin_reviewed: true,
        admin_reviewed_at: new Date().toISOString()
      }));

      setMessages(updatedMessages);

      // Update conversation counts
      const updatedConversations = conversations.map(conversation => {
        if (conversation.id === selectedConversation.id) {
          return {
            ...conversation,
            unread_count: 0,
            flagged_count: 0
          };
        }
        return conversation;
      });

      setConversations(updatedConversations);
      setFilteredConversations(
        filteredConversations.map(conversation => {
          if (conversation.id === selectedConversation.id) {
            return {
              ...conversation,
              unread_count: 0,
              flagged_count: 0
            };
          }
          return conversation;
        })
      );

      setSelectedConversation({
        ...selectedConversation,
        unread_count: 0,
        flagged_count: 0
      });

      setSuccess('All messages marked as reviewed');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error marking messages as reviewed:', error);
        setError(error.message || 'Failed to mark messages as reviewed');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    }
  };

  const toggleMessageFlag = async (messageId: string, reason?: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      const response = await fetch('/api/admin/messages/flag', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          message_id: messageId,
          reason
        })
      });

      if (!response.ok) {
        throw new Error('Failed to toggle message flag');
      }

      const result = await response.json();

      // Update local state
      const updatedMessages = messages.map(message =>
        message.id === messageId
          ? {
              ...message,
              is_flagged: result.is_flagged,
              flagged_at: result.is_flagged ? new Date().toISOString() : null,
              flag_reason: result.is_flagged ? reason : null
            }
          : message
      );

      setMessages(updatedMessages);

      // Update conversation flagged count
      const flaggedCount = updatedMessages.filter(m => m.is_flagged).length;
      const updatedConversations = conversations.map(conversation =>
        conversation.id === selectedConversation?.id
          ? { ...conversation, flagged_count: flaggedCount }
          : conversation
      );

      setConversations(updatedConversations);
      setFilteredConversations(
        filteredConversations.map(conversation =>
          conversation.id === selectedConversation?.id
            ? { ...conversation, flagged_count: flaggedCount }
            : conversation
        )
      );

      if (selectedConversation) {
        setSelectedConversation({
          ...selectedConversation,
          flagged_count: flaggedCount
        });
      }

      setSuccess(result.message);
      setTimeout(() => setSuccess(null), 3000);

    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error('Error toggling message flag:', error);
        setError(error.message || 'Failed to toggle message flag');
      } else {
        console.error('Unexpected error:', error);
        setError('An unexpected error occurred');
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Today - show time
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffDays === 1) {
      // Yesterday
      return 'Yesterday';
    } else if (diffDays < 7) {
      // Within a week - show day name
      return date.toLocaleDateString('en-US', {
        weekday: 'short'
      });
    } else {
      // Older - show date
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const formatMessageDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCurrentPageConversations = () => {
    const startIndex = (currentPage - 1) * conversationsPerPage;
    const endIndex = startIndex + conversationsPerPage;
    return filteredConversations.slice(startIndex, endIndex);
  };

  if (loading && conversations.length === 0) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading conversations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Message Oversight</h1>
        <p className="text-gray-500">Monitor and review client-designer communications</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Conversations List */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b">
            <div className="relative">
              <input
                type="text"
                placeholder="Search conversations"
                className="w-full px-4 py-2 border rounded-md pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
            <div className="mt-4 flex space-x-2">
              <Button
                variant={filterType === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType('all')}
              >
                All
              </Button>
              <Button
                variant={filterType === 'flagged' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType('flagged')}
                className="flex items-center"
              >
                <Flag className="h-4 w-4 mr-1" />
                Flagged
              </Button>
              <Button
                variant={filterType === 'unread' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType('unread')}
                className="flex items-center"
              >
                <MessageSquare className="h-4 w-4 mr-1" />
                Unread
              </Button>
            </div>
          </div>

          <div className="h-[calc(100vh-300px)] overflow-y-auto">
            {getCurrentPageConversations().length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <MessageSquare className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                <p>No conversations found</p>
              </div>
            ) : (
              <div className="divide-y">
                {getCurrentPageConversations().map((conversation) => (
                  <div
                    key={conversation.id}
                    className={`p-4 cursor-pointer hover:bg-gray-50 ${conversation.is_selected ? 'bg-blue-50' : ''}`}
                    onClick={() => handleConversationSelect(conversation)}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="font-medium truncate pr-4">{conversation.title}</h3>
                      <span className="text-xs text-gray-500 whitespace-nowrap">
                        {formatDate(conversation.last_message_at)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-500 truncate">
                        {conversation.type === 'project'
                          ? `${conversation.client_name || 'Client'} & ${conversation.designer_name || 'Designer'}`
                          : conversation.participants.map(p => p.full_name).join(' & ')
                        }
                      </p>
                      <div className="flex space-x-1">
                        <span className="px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">
                          {conversation.total_messages}
                        </span>
                        {conversation.flagged_count > 0 && (
                          <span className="px-1.5 py-0.5 bg-red-100 text-red-800 rounded-full text-xs">
                            {conversation.flagged_count}
                          </span>
                        )}
                        {conversation.unread_count > 0 && (
                          <span className="px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs">
                            {conversation.unread_count}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-4 py-3 border-t flex items-center justify-between">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className={`p-1 rounded-md ${
                  currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-100'
                }`}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <span className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className={`p-1 rounded-md ${
                  currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-100'
                }`}
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>

        {/* Messages */}
        <div className="lg:col-span-2">
          {selectedConversation ? (
            <div className="bg-white rounded-lg shadow-md overflow-hidden h-full flex flex-col">
              <div className="px-6 py-4 border-b flex justify-between items-center">
                <div>
                  <h2 className="text-lg font-medium">{selectedConversation.title}</h2>
                  <p className="text-sm text-gray-500">
                    {selectedConversation.type === 'project'
                      ? `${selectedConversation.client_name || 'Client'} & ${selectedConversation.designer_name || 'Designer'}`
                      : selectedConversation.participants.map(p => p.full_name).join(' & ')
                    }
                  </p>
                </div>
                <div className="flex space-x-2">
                  {selectedConversation.project_id && (
                    <Link href={`/admin/projects/${selectedConversation.project_id}`}>
                      <Button variant="outline" size="sm" className="flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        View Project
                      </Button>
                    </Link>
                  )}
                  <Button
                    size="sm"
                    onClick={markMessagesAsReviewed}
                    disabled={messages.every(m => m.admin_reviewed)}
                    className="flex items-center"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Mark All as Reviewed
                  </Button>
                </div>
              </div>

              <div className="p-6 flex-1 overflow-y-auto h-[calc(100vh-300px)]">
                {loadingMessages ? (
                  <div className="flex justify-center items-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : messages.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <MessageSquare className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                    <p>No messages in this conversation</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.sender_role === 'client' ? 'justify-start' : 'justify-end'}`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg p-4 ${
                            message.sender_role === 'client'
                              ? 'bg-gray-100'
                              : 'bg-blue-100'
                          } ${
                            message.is_flagged ? 'border-2 border-red-300' : ''
                          } ${
                            !message.admin_reviewed ? 'border-l-4 border-l-yellow-400' : ''
                          }`}
                        >
                          <div className="flex justify-between items-center mb-1">
                            <span className="font-medium text-sm">
                              {message.sender_name} ({message.sender_role})
                            </span>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => toggleMessageFlag(message.id, 'Admin flagged for review')}
                                className={`p-1 rounded-md hover:bg-gray-100 ${
                                  message.is_flagged ? 'text-red-500' : 'text-gray-400'
                                }`}
                                title={message.is_flagged ? 'Unflag message' : 'Flag message'}
                              >
                                <Flag className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                          <p className="text-gray-800">{message.content}</p>
                          <div className="mt-2 flex justify-between items-center text-xs text-gray-500">
                            <span>{formatMessageDate(message.created_at)}</span>
                            <div className="flex items-center space-x-2">
                              {message.is_flagged && (
                                <span className="flex items-center text-red-600">
                                  <Flag className="h-3 w-3 mr-1" />
                                  Flagged
                                </span>
                              )}
                              {message.admin_reviewed && (
                                <span className="flex items-center text-green-600">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Reviewed
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-8 text-center h-full flex flex-col justify-center items-center">
              <MessageSquare className="h-16 w-16 text-gray-300 mb-4" />
              <h2 className="text-xl font-medium mb-2">No Conversation Selected</h2>
              <p className="text-gray-500 mb-6 max-w-md">
                Select a conversation from the list to view messages and provide oversight.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
