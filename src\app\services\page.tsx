"use client";

import Layout from "@/components/Layout";
import Image from "next/image";
import { <PERSON>R<PERSON>, CheckCircle, <PERSON><PERSON><PERSON>, Clock, Users, Award } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";
import { toUrlSafeServiceId } from "@/lib/service-utils";

const services = [
  {
    title: "Creative Design & Branding",
    subtitle: "Unique architectural identities",
    description: "Transform your vision into distinctive brand experiences that resonate with your audience.",
    image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    highlights: ["Brand Identity", "Visual Strategy", "Market Positioning"],
    stats: { projects: "150+", satisfaction: "98%", timeline: "2-4 weeks" }
  },
  {
    title: "Innovative Architectural Design",
    subtitle: "Cutting-edge structural solutions",
    description: "Push boundaries with architectural innovation that combines creativity with functionality.",
    image: "https://images.unsplash.com/photo-1577493340887-b7bfff550145?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    highlights: ["Parametric Design", "Sustainable Solutions", "Smart Integration"],
    stats: { projects: "200+", satisfaction: "99%", timeline: "3-6 months" }
  },
  {
    title: "Interior Design",
    subtitle: "Immersive interior spaces",
    description: "Create environments that inspire and function beautifully for everyday living and working.",
    image: "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    highlights: ["Space Planning", "Custom Design", "Material Selection"],
    stats: { projects: "300+", satisfaction: "97%", timeline: "4-8 weeks" }
  },
  {
    title: "Urban & Architectural Planning",
    subtitle: "Sustainable community solutions",
    description: "Develop comprehensive master plans that create livable, sustainable communities.",
    image: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    highlights: ["Master Planning", "Zoning Strategy", "Community Design"],
    stats: { projects: "75+", satisfaction: "100%", timeline: "6-12 months" }
  },
  {
    title: "Residential & Commercial Projects",
    subtitle: "Tailored architectural solutions",
    description: "Custom designs for homes and businesses that reflect your unique needs and aspirations.",
    image: "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    highlights: ["Custom Homes", "Commercial Spaces", "Mixed-Use"],
    stats: { projects: "400+", satisfaction: "98%", timeline: "3-8 months" }
  },
  {
    title: "Landscape and Architecture Integration",
    subtitle: "Harmonizing built & natural environments",
    description: "Seamlessly blend architecture with landscape for enhanced beauty and sustainability.",
    image: "https://images.unsplash.com/photo-1600573472550-8090b5e0745e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    highlights: ["Site Integration", "Environmental Design", "Outdoor Spaces"],
    stats: { projects: "180+", satisfaction: "99%", timeline: "2-5 months" }
  },
  {
    title: "Educational & Community-Oriented Spaces",
    subtitle: "Spaces that foster learning and community",
    description: "Design inclusive environments that promote education, engagement, and social connection.",
    image: "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    highlights: ["Educational Facilities", "Community Centers", "Accessible Design"],
    stats: { projects: "120+", satisfaction: "100%", timeline: "4-10 months" }
  }
];

const process = [
  {
    step: "01",
    title: "Discovery",
    description: "Understanding your vision, requirements, and constraints through detailed consultation."
  },
  {
    step: "02",
    title: "Concept Design",
    description: "Developing initial design concepts that align with your objectives and site conditions."
  },
  {
    step: "03",
    title: "Development",
    description: "Refining the chosen concept through detailed design development and documentation."
  },
  {
    step: "04",
    title: "Delivery",
    description: "Managing construction and ensuring quality execution of the design vision."
  }
];

export default function Services() {
  return (
    <Layout>
      {/* Hero Section - Mobile Optimized */}
      <section className="relative min-h-[60vh] sm:min-h-[65vh] md:min-h-[70vh] flex items-center">
        <Image
          src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c"
          alt="Services Hero"
          fill
          className="object-cover"
          priority
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-black/50" />
        <div className="container mx-auto px-4 relative z-10 text-white pt-24 sm:pt-28 md:pt-32 lg:pt-20 pb-8">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
            Our Services
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl max-w-2xl leading-relaxed">
            Comprehensive architectural solutions tailored to your vision and needs.
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Section Header */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Expertise</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover our comprehensive range of architectural services designed to bring your vision to life.
              </p>
            </motion.div>

            {/* Desktop: Grid Layout */}
            <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group"
                >
                  <div className="relative aspect-[4/3] overflow-hidden">
                    <Image
                      src={service.image}
                      alt={service.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between text-white text-sm">
                        <span className="flex items-center">
                          <Users className="h-4 w-4 mr-1" />
                          {service.stats.projects}
                        </span>
                        <span className="flex items-center">
                          <Award className="h-4 w-4 mr-1" />
                          {service.stats.satisfaction}
                        </span>
                        <span className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {service.stats.timeline}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">{service.subtitle}</p>
                    <p className="text-gray-700 mb-4 text-sm leading-relaxed">{service.description}</p>

                    <div className="flex flex-wrap gap-2 mb-6">
                      {service.highlights.map((highlight, idx) => (
                        <span key={idx} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                          {highlight}
                        </span>
                      ))}
                    </div>

                    <div className="space-y-3">
                      <Link href={`/services/${toUrlSafeServiceId(service.title)}`}>
                        <button className="w-full bg-brown-600 text-white px-6 py-2 rounded hover:bg-brown-700 transition-colors flex items-center justify-center">
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </button>
                      </Link>
                      <div className="flex gap-2">
                        <Link href={`/vision-builder/${toUrlSafeServiceId(service.title)}`} className="flex-1">
                          <button className="w-full border border-brown-600 text-brown-600 px-4 py-1 text-sm rounded hover:bg-brown-50 transition-colors flex items-center justify-center">
                            <Sparkles className="mr-1 h-3 w-3" />
                            Build Vision
                          </button>
                        </Link>
                        <Link href={`/sample-request?service=${toUrlSafeServiceId(service.title)}`} className="flex-1">
                          <button className="w-full border border-brown-600 text-brown-600 px-4 py-1 text-sm rounded hover:bg-brown-50 transition-colors">
                            Sample
                          </button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Mobile: Horizontal Scroll (1.5 cards visible) */}
            <div className="md:hidden">
              <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 px-4 -mx-4">
                {services.map((service, index) => (
                  <div key={index} className="flex-shrink-0 w-80 snap-center mr-4 last:mr-0 first:ml-4">
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-white rounded-lg shadow-lg overflow-hidden h-full"
                    >
                      <div className="relative aspect-[4/3] overflow-hidden">
                        <Image
                          src={service.image}
                          alt={service.title}
                          fill
                          className="object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                        <div className="absolute bottom-4 left-4 right-4">
                          <div className="flex items-center justify-between text-white text-xs">
                            <span className="flex items-center">
                              <Users className="h-3 w-3 mr-1" />
                              {service.stats.projects}
                            </span>
                            <span className="flex items-center">
                              <Award className="h-3 w-3 mr-1" />
                              {service.stats.satisfaction}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="p-4">
                        <h3 className="text-lg font-bold mb-2">{service.title}</h3>
                        <p className="text-gray-600 text-xs mb-2">{service.subtitle}</p>
                        <p className="text-gray-700 mb-3 text-sm leading-relaxed">{service.description}</p>

                        <div className="flex flex-wrap gap-1 mb-4">
                          {service.highlights.slice(0, 2).map((highlight, idx) => (
                            <span key={idx} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                              {highlight}
                            </span>
                          ))}
                        </div>

                        <div className="space-y-2">
                          <Link href={`/services/${toUrlSafeServiceId(service.title)}`}>
                            <button className="w-full bg-brown-600 text-white px-4 py-2 text-sm rounded hover:bg-brown-700 transition-colors flex items-center justify-center">
                              Learn More
                              <ArrowRight className="ml-2 h-3 w-3" />
                            </button>
                          </Link>
                          <div className="flex gap-2">
                            <Link href={`/vision-builder/${toUrlSafeServiceId(service.title)}`} className="flex-1">
                              <button className="w-full border border-brown-600 text-brown-600 px-2 py-1 text-xs rounded hover:bg-brown-50 transition-colors">
                                Build Vision
                              </button>
                            </Link>
                            <Link href={`/sample-request?service=${toUrlSafeServiceId(service.title)}`} className="flex-1">
                              <button className="w-full border border-brown-600 text-brown-600 px-2 py-1 text-xs rounded hover:bg-brown-50 transition-colors">
                                Sample
                              </button>
                            </Link>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                ))}
              </div>

              {/* Mobile scroll indicator */}
              <div className="flex justify-center mt-4 space-x-2">
                {services.map((_, index) => (
                  <div key={index} className="w-2 h-2 rounded-full bg-gray-300 opacity-60"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4">How We Work</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Our proven methodology ensures exceptional results for every project, from concept to completion.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {process.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4 shadow-lg">
                      {index + 1}
                    </div>
                    {index < process.length - 1 && (
                      <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gray-300 -z-10" />
                    )}
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-900">{step.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </motion.div>
              ))}
            </div>

            {/* CTA */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mt-16"
            >
              <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
              <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                Choose how you'd like to begin your architectural journey with us.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/vision-builder">
                  <button className="bg-brown-600 text-white px-8 py-3 rounded hover:bg-brown-700 transition-colors flex items-center justify-center">
                    <Sparkles className="mr-2 h-5 w-5" />
                    Build Your Vision
                  </button>
                </Link>
                <Link href="/sample-request">
                  <button className="border border-brown-600 text-brown-600 px-8 py-3 rounded hover:bg-brown-50 transition-colors flex items-center justify-center">
                    Request Free Sample
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
