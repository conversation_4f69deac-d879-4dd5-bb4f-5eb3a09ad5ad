"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Star,
  BarChart3,
  Users,
  Target,
  Award,
  RefreshCw,
  Calendar
} from "lucide-react";

interface PerformanceMetrics {
  totalReviews: number;
  approvalRate: number;
  averageScore: number;
  averageReviewTime: number;
  revisionRate: number;
  slaCompliance: number;
  weeklyTrend: number;
  monthlyTrend: number;
}

interface TeamMember {
  id: string;
  full_name: string;
  totalReviews: number;
  approvalRate: number;
  averageScore: number;
  averageTime: number;
  slaCompliance: number;
}

export default function QualityPerformancePage() {
  const { user, profile } = useOptimizedAuth();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    totalReviews: 0,
    approvalRate: 0,
    averageScore: 0,
    averageReviewTime: 0,
    revisionRate: 0,
    slaCompliance: 0,
    weeklyTrend: 0,
    monthlyTrend: 0
  });
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<string>('30');

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchPerformanceData();
    }
  }, [user, profile, timeRange]);

  const fetchPerformanceData = async () => {
    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(timeRange));

      // Fetch quality reviews data
      const { data: reviews, error } = await supabase
        .from('quality_reviews_new')
        .select(`
          *,
          reviewer:profiles!quality_reviews_new_reviewer_id_fkey(full_name)
        `)
        .gte('created_at', startDate.toISOString());

      if (error) throw error;

      // Calculate personal metrics
      const personalReviews = reviews?.filter(r => r.reviewer_id === user?.id) || [];
      const completedReviews = personalReviews.filter(r => r.reviewed_at);
      
      const totalReviews = personalReviews.length;
      const approvedReviews = personalReviews.filter(r => r.status === 'approved').length;
      const approvalRate = totalReviews > 0 ? (approvedReviews / totalReviews) * 100 : 0;
      
      const scoredReviews = personalReviews.filter(r => r.overall_score);
      const averageScore = scoredReviews.length > 0 
        ? scoredReviews.reduce((sum, r) => sum + (r.overall_score || 0), 0) / scoredReviews.length
        : 0;

      const revisionsNeeded = personalReviews.filter(r => r.revision_count > 0).length;
      const revisionRate = totalReviews > 0 ? (revisionsNeeded / totalReviews) * 100 : 0;

      // Calculate average review time (mock data for now)
      const averageReviewTime = 2.5; // hours

      // Calculate SLA compliance
      const slaCompliantReviews = personalReviews.filter(r => {
        if (!r.sla_deadline || !r.reviewed_at) return false;
        return new Date(r.reviewed_at) <= new Date(r.sla_deadline);
      }).length;
      const slaCompliance = completedReviews.length > 0 ? (slaCompliantReviews / completedReviews.length) * 100 : 0;

      // Calculate trends (mock data for now)
      const weeklyTrend = Math.random() * 20 - 10; // -10 to +10
      const monthlyTrend = Math.random() * 30 - 15; // -15 to +15

      setMetrics({
        totalReviews,
        approvalRate,
        averageScore,
        averageReviewTime,
        revisionRate,
        slaCompliance,
        weeklyTrend,
        monthlyTrend
      });

      // Calculate team performance
      const qualityTeamMembers = await supabase
        .from('profiles')
        .select('id, full_name')
        .eq('role', 'quality_team');

      if (qualityTeamMembers.data) {
        const teamPerformance = qualityTeamMembers.data.map(member => {
          const memberReviews = reviews?.filter(r => r.reviewer_id === member.id) || [];
          const memberCompleted = memberReviews.filter(r => r.reviewed_at);
          const memberApproved = memberReviews.filter(r => r.status === 'approved').length;
          const memberScored = memberReviews.filter(r => r.overall_score);
          
          return {
            id: member.id,
            full_name: member.full_name,
            totalReviews: memberReviews.length,
            approvalRate: memberReviews.length > 0 ? (memberApproved / memberReviews.length) * 100 : 0,
            averageScore: memberScored.length > 0 
              ? memberScored.reduce((sum, r) => sum + (r.overall_score || 0), 0) / memberScored.length
              : 0,
            averageTime: 2.5, // Mock data
            slaCompliance: memberCompleted.length > 0 ? 95 : 0 // Mock data
          };
        });

        setTeamMembers(teamPerformance);
      }

    } catch (error) {
      console.error('Error fetching performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (trend < 0) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    }
    return <BarChart3 className="h-4 w-4 text-gray-500" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return "text-green-600";
    if (trend < 0) return "text-red-600";
    return "text-gray-600";
  };

  const getPerformanceColor = (value: number, type: 'rate' | 'score' | 'time') => {
    if (type === 'rate') {
      if (value >= 90) return "text-green-600";
      if (value >= 70) return "text-yellow-600";
      return "text-red-600";
    }
    if (type === 'score') {
      if (value >= 4.5) return "text-green-600";
      if (value >= 3.5) return "text-yellow-600";
      return "text-red-600";
    }
    if (type === 'time') {
      if (value <= 2) return "text-green-600";
      if (value <= 4) return "text-yellow-600";
      return "text-red-600";
    }
    return "text-gray-600";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Team Performance</h1>
          <p className="text-gray-600 mt-2">Quality team performance metrics and analytics</p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
            <option value="365">Last year</option>
          </select>
          <Button
            onClick={fetchPerformanceData}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Personal Performance Metrics */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Your Performance</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Reviews</p>
                <p className="text-3xl font-bold text-blue-700">{metrics.totalReviews}</p>
                <div className="flex items-center gap-1 mt-1">
                  {getTrendIcon(metrics.weeklyTrend)}
                  <span className={`text-sm ${getTrendColor(metrics.weeklyTrend)}`}>
                    {metrics.weeklyTrend > 0 ? '+' : ''}{metrics.weeklyTrend.toFixed(1)}% this week
                  </span>
                </div>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Approval Rate</p>
                <p className={`text-3xl font-bold ${getPerformanceColor(metrics.approvalRate, 'rate')}`}>
                  {metrics.approvalRate.toFixed(1)}%
                </p>
                <div className="flex items-center gap-1 mt-1">
                  <Target className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-600">Target: 85%</span>
                </div>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Average Score</p>
                <p className={`text-3xl font-bold ${getPerformanceColor(metrics.averageScore, 'score')}`}>
                  {metrics.averageScore.toFixed(1)}/5
                </p>
                <div className="flex items-center gap-1 mt-1">
                  <Star className="h-4 w-4 text-purple-500" />
                  <span className="text-sm text-purple-600">Quality rating</span>
                </div>
              </div>
              <Star className="h-8 w-8 text-purple-500" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Avg Review Time</p>
                <p className={`text-3xl font-bold ${getPerformanceColor(metrics.averageReviewTime, 'time')}`}>
                  {metrics.averageReviewTime.toFixed(1)}h
                </p>
                <div className="flex items-center gap-1 mt-1">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <span className="text-sm text-orange-600">Target: <2h</span>
                </div>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="bg-gray-50 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">SLA Compliance</p>
                <p className={`text-2xl font-bold ${getPerformanceColor(metrics.slaCompliance, 'rate')}`}>
                  {metrics.slaCompliance.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-500 mt-1">Reviews completed on time</p>
              </div>
              <Award className="h-8 w-8 text-gray-500" />
            </div>
          </div>

          <div className="bg-gray-50 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Revision Rate</p>
                <p className={`text-2xl font-bold ${getPerformanceColor(100 - metrics.revisionRate, 'rate')}`}>
                  {metrics.revisionRate.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-500 mt-1">Reviews requiring revisions</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-gray-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Team Performance Comparison */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Team Performance Comparison</h2>
          <p className="text-gray-600 mt-1">Compare performance across quality team members</p>
        </div>

        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Team Member</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">Reviews</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">Approval Rate</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">Avg Score</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">Avg Time</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">SLA Compliance</th>
                </tr>
              </thead>
              <tbody>
                {teamMembers.map((member) => (
                  <tr key={member.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-brown-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-brown-600">
                            {member.full_name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <span className="font-medium text-gray-900">{member.full_name}</span>
                        {member.id === user?.id && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">You</span>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4 text-center text-gray-900">{member.totalReviews}</td>
                    <td className="py-4 px-4 text-center">
                      <span className={getPerformanceColor(member.approvalRate, 'rate')}>
                        {member.approvalRate.toFixed(1)}%
                      </span>
                    </td>
                    <td className="py-4 px-4 text-center">
                      <span className={getPerformanceColor(member.averageScore, 'score')}>
                        {member.averageScore.toFixed(1)}/5
                      </span>
                    </td>
                    <td className="py-4 px-4 text-center">
                      <span className={getPerformanceColor(member.averageTime, 'time')}>
                        {member.averageTime.toFixed(1)}h
                      </span>
                    </td>
                    <td className="py-4 px-4 text-center">
                      <span className={getPerformanceColor(member.slaCompliance, 'rate')}>
                        {member.slaCompliance.toFixed(1)}%
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
