"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useParams } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import { useAcceptProposal, useRejectProposal, useMarkProposalUnderReview } from "@/hooks/useDashboardData";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  FileText,
  DollarSign,
  Clock,
  Calendar,
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  Target
} from "lucide-react";

interface ProposalDetails {
  id: string;
  title: string;
  description: string;
  total_budget: number;
  timeline_weeks: number;
  milestones: {
    title: string;
    description: string;
    amount: number;
    due_date: string;
  }[];
  terms_and_conditions: string;
  status: string;
  submitted_at: string | null;
  expires_at: string | null;
  brief_id: string;
  brief_title: string;
  designer_id: string;
  designer_name: string;
  designer_avatar: string | null;
}
export default function ClientProposalDetails() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const [proposal, setProposal] = useState<ProposalDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use optimized hooks with correct parameters
  const acceptProposal = useAcceptProposal(user?.id || '');
  const rejectProposal = useRejectProposal();
  const markUnderReview = useMarkProposalUnderReview();

  useEffect(() => {
    if (user && params.id) {
      fetchProposalDetails();
    }
  }, [user, params.id]);

  const fetchProposalDetails = async () => {
    if (!user || !params.id) return;

    try {
      const { data, error } = await supabase
        .from('project_proposals_enhanced')
        .select(`
          *,
          project_briefs!project_proposals_enhanced_brief_id_fkey(
            id,
            title,
            client_id
          ),
          profiles!project_proposals_enhanced_designer_id_fkey(
            full_name,
            avatar_url
          )
        `)
        .eq('id', params.id)
        .single();

      if (error) throw error;

      const brief = data.project_briefs;
      if (brief?.client_id !== user.id) {
        throw new Error('Unauthorized');
      }

      const designer = Array.isArray(data.profiles) ? data.profiles[0] : data.profiles;

      const proposalData = {
        ...data,
        brief_id: brief?.id || '',
        brief_title: brief?.title || 'Unknown Brief',
        designer_name: designer?.full_name || 'Unknown Designer',
        designer_avatar: designer?.avatar_url || null,
        milestones: data.milestones || []
      };

      setProposal(proposalData);

      // Mark as under review if status is submitted
      if (proposalData.status === 'submitted') {
        markUnderReview.mutate({ proposalId: proposalData.id });
      }
    } catch (error) {
      console.error('Error fetching proposal:', error);
      setError('Failed to load proposal');
    } finally {
      setLoading(false);
    }
  };

  const handleProposalAction = async (action: 'accept' | 'reject') => {
    if (!proposal || !confirm(`Are you sure you want to ${action} this proposal?`)) {
      return;
    }

    try {
      if (action === 'accept') {
        await acceptProposal.mutateAsync({
          proposalId: proposal.id,
          briefId: proposal.brief_id
        });
        // Update local state for immediate feedback
        setProposal(prev => prev ? { ...prev, status: 'accepted' } : null);
      } else {
        await rejectProposal.mutateAsync({ proposalId: proposal.id, userId: user?.id || '' });
        // Update local state for immediate feedback
        setProposal(prev => prev ? { ...prev, status: 'rejected' } : null);
      }
    } catch (error) {
      console.error(`Error ${action}ing proposal:`, error);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const isExpired = (date: string | null) => {
    if (!date) return true;
    return new Date(date) < new Date();
  };

  // Enhanced status display for client perspective
  const getClientStatusDisplay = (status: string, expiresAt: string | null = null) => {
    const isExpired = expiresAt && new Date(expiresAt) < new Date();

    if (isExpired && (status === 'submitted' || status === 'under_review')) {
      return { text: "EXPIRED", color: "text-red-600 bg-red-50 border-red-200", icon: "expired" };
    }

    switch (status) {
      case "submitted": return { text: "NEW", color: "text-blue-600 bg-blue-50 border-blue-200", icon: "new" };
      case "under_review": return { text: "REVIEWING", color: "text-yellow-600 bg-yellow-50 border-yellow-200", icon: "review" };
      case "accepted": return { text: "ACCEPTED", color: "text-green-600 bg-green-50 border-green-200", icon: "accepted" };
      case "rejected": return { text: "REJECTED", color: "text-red-600 bg-red-50 border-red-200", icon: "rejected" };
      case "withdrawn": return { text: "WITHDRAWN", color: "text-gray-600 bg-gray-50 border-gray-200", icon: "withdrawn" };
      default: return { text: status.replace("_", " ").toUpperCase(), color: "text-gray-600 bg-gray-50 border-gray-200", icon: "default" };
    }
  };

  const canTakeAction = (proposal?.status === 'submitted' || proposal?.status === 'under_review') && !isExpired(proposal?.expires_at);
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  if (error || !proposal) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <p className="text-red-700">{error || 'Proposal not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/client/proposals">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Proposals
            </Button>
          </Link>
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{proposal.title}</h1>
              <span
                className={`px-3 py-1 text-sm font-medium rounded-full border flex items-center ${getClientStatusDisplay(proposal.status, proposal.expires_at).color}`}
              >
                {(() => {
                  const statusDisplay = getClientStatusDisplay(proposal.status, proposal.expires_at);
                  const getStatusIcon = (iconType: string) => {
                    switch (iconType) {
                      case "accepted": return <CheckCircle className="h-4 w-4" />;
                      case "new": return <FileText className="h-4 w-4" />;
                      case "review": return <Clock className="h-4 w-4" />;
                      case "rejected": return <XCircle className="h-4 w-4" />;
                      case "withdrawn": return <AlertCircle className="h-4 w-4" />;
                      case "expired": return <AlertCircle className="h-4 w-4" />;
                      default: return <Target className="h-4 w-4" />;
                    }
                  };
                  return (
                    <>
                      {getStatusIcon(statusDisplay.icon)}
                      <span className="ml-1">{statusDisplay.text}</span>
                    </>
                  );
                })()}
              </span>
            </div>
            <p className="text-gray-600">Proposal Details</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
        {canTakeAction && (
  <>
    <Button
      onClick={() => handleProposalAction('reject')}
      disabled={acceptProposal.isPending || rejectProposal.isPending}
      variant="outline"
      className="text-red-600 border-red-300 hover:bg-red-50"
    >
      <XCircle className="h-4 w-4 mr-2" />
      {rejectProposal.isPending ? 'Rejecting...' : 'Reject'}
    </Button>
    <Button
      onClick={() => handleProposalAction('accept')}
      disabled={acceptProposal.isPending || rejectProposal.isPending}
      className="bg-green-600 hover:bg-green-700 text-white"
    >
      <CheckCircle className="h-4 w-4 mr-2" />
      {acceptProposal.isPending ? 'Accepting...' : 'Accept'}
    </Button>
  </>
)}
{proposal.status === 'accepted' && (
  <div className="flex items-center text-green-600">
    <CheckCircle className="h-5 w-5 mr-2" />
    <span className="font-medium">Proposal Accepted</span>
  </div>
)}
{proposal.status === 'rejected' && (
  <div className="flex items-center text-red-600">
    <XCircle className="h-5 w-5 mr-2" />
    <span className="font-medium">Proposal Rejected</span>
  </div>
)}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Proposal Description</h3>
            <p className="text-gray-700 leading-relaxed">{proposal.description}</p>
          </motion.div>

          {proposal.milestones && proposal.milestones.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Milestones</h3>
              <div className="space-y-4">
                {proposal.milestones.map((milestone, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{milestone.title}</h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{Math.ceil((new Date(milestone.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24 * 7))} weeks</span>
                        <span className="font-medium">${milestone.amount?.toLocaleString()}</span>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm">{milestone.description}</p>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </div>

        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Designer</h3>
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex-shrink-0">
                {proposal.designer_avatar ? (
                  <img
                    src={proposal.designer_avatar}
                    alt={proposal.designer_name}
                    className="h-12 w-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="h-6 w-6 text-gray-500" />
                  </div>
                )}
              </div>
              <div>
                <p className="font-medium text-gray-900">{proposal.designer_name}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white border border-gray-200 rounded-lg p-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Summary</h3>
            <div className="space-y-4">
              <div className="flex items-center text-sm">
                <DollarSign className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Budget:</span>
                <span className="ml-2 font-medium">${proposal.total_budget?.toLocaleString()}</span>
              </div>
              <div className="flex items-center text-sm">
                <Clock className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Timeline:</span>
                <span className="ml-2 font-medium">{proposal.timeline_weeks} weeks</span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                <span className="text-gray-600">Submitted:</span>
                <span className="ml-2 font-medium">{formatDate(proposal.submitted_at)}</span>
              </div>
            </div>
          </motion.div>

          {canTakeAction && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white border border-gray-200 rounded-lg p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
              <div className="space-y-3">
                <Button
                  onClick={() => handleProposalAction('accept')}
                  disabled={acceptProposal.isPending || rejectProposal.isPending}
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                >
                  <CheckCircle className="h-4 w-4 mr-3" />
                  {acceptProposal.isPending ? 'Accepting...' : 'Accept Proposal'}
                </Button>
                <Button
                  onClick={() => handleProposalAction('reject')}
                  disabled={acceptProposal.isPending || rejectProposal.isPending}
                  variant="outline"
                  className="w-full text-red-600 border-red-300 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-3" />
                  {rejectProposal.isPending ? 'Rejecting...' : 'Reject Proposal'}
                </Button>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );}