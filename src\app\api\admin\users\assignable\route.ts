import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

/**
 * API route for fetching assignable users for tracking requests
 */
export async function GET(request: NextRequest) {
  try {
    console.log('Assignable users API called');

    // Fetch users who can be assigned to tracking requests
    // Include admins, managers, and quality team members
    const { data: users, error } = await supabaseServerClient
      .from('profiles')
      .select('id, full_name, email, role, avatar_url')
      .in('role', ['admin', 'manager', 'quality_team'])
      .eq('is_active', true)
      .order('full_name', { ascending: true });

    if (error) {
      console.error('Error fetching assignable users:', error);
      
      // If database is not available, return mock data for development
      if (process.env.NODE_ENV === 'development') {
        console.warn('Database not available, returning mock assignable users');
        return NextResponse.json({
          success: true,
          users: [
            {
              id: 'mock-admin-1',
              full_name: 'Admin User 1',
              email: '<EMAIL>',
              role: 'admin',
              avatar_url: null
            },
            {
              id: 'mock-admin-2',
              full_name: 'Admin User 2',
              email: '<EMAIL>',
              role: 'admin',
              avatar_url: null
            },
            {
              id: 'mock-manager-1',
              full_name: 'Manager User 1',
              email: '<EMAIL>',
              role: 'manager',
              avatar_url: null
            },
            {
              id: 'mock-quality-1',
              full_name: 'Quality Team Lead',
              email: '<EMAIL>',
              role: 'quality_team',
              avatar_url: null
            }
          ],
          mock: true
        });
      }

      return NextResponse.json(
        { error: 'Failed to fetch assignable users', details: error.message },
        { status: 500 }
      );
    }

    console.log(`Found ${users?.length || 0} assignable users`);

    return NextResponse.json({
      success: true,
      users: users || []
    });

  } catch (error) {
    console.error('Error in assignable users API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
