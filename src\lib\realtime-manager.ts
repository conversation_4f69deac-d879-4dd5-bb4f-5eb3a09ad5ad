/**
 * Optimized Realtime Subscription Manager
 * Handles Supabase realtime subscriptions with intelligent fallbacks
 */

import { createClient } from '@supabase/supabase-js';
import { RealtimeChannel } from '@supabase/supabase-js';

// Create a dedicated realtime client
const realtimeClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    realtime: {
      params: {
        eventsPerSecond: 10, // Limit events to prevent overwhelming
      },
    },
  }
);

interface SubscriptionConfig {
  table: string;
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  callback: (payload: any) => void;
  errorCallback?: (error: any) => void;
}

interface ManagedSubscription {
  id: string;
  channel: RealtimeChannel;
  config: SubscriptionConfig;
  retryCount: number;
  lastError?: any;
}

class RealtimeManager {
  private subscriptions: Map<string, ManagedSubscription> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private isConnected = false;

  constructor() {
    this.setupConnectionHandlers();
  }

  private setupConnectionHandlers() {
    // Monitor connection status
    realtimeClient.realtime.onOpen(() => {
      console.log('Realtime connection opened');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
    });

    realtimeClient.realtime.onClose(() => {
      console.log('Realtime connection closed');
      this.isConnected = false;
      this.handleReconnection();
    });

    realtimeClient.realtime.onError((error) => {
      console.error('Realtime connection error:', error);
      this.isConnected = false;
    });
  }

  private handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn('Max reconnection attempts reached. Switching to polling mode.');
      return;
    }

    setTimeout(() => {
      console.log(`Attempting to reconnect... (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
      this.reconnectAttempts++;
      this.reconnectDelay *= 2; // Exponential backoff
      
      // Resubscribe to all channels
      this.resubscribeAll();
    }, this.reconnectDelay);
  }

  private resubscribeAll() {
    for (const [id, subscription] of this.subscriptions) {
      this.unsubscribe(id);
      this.subscribe(subscription.config, id);
    }
  }

  /**
   * Subscribe to realtime events with intelligent error handling
   */
  subscribe(config: SubscriptionConfig, customId?: string): string {
    const id = customId || `${config.table}_${config.event}_${Date.now()}`;
    
    try {
      // Check if table exists and has RLS policies
      if (!this.isTableSupported(config.table)) {
        console.warn(`Table ${config.table} not supported for realtime. Using polling fallback.`);
        this.setupPollingFallback(config, id);
        return id;
      }

      const channel = realtimeClient
        .channel(`${config.table}_changes_${id}`)
        .on(
          'postgres_changes',
          {
            event: config.event,
            schema: 'public',
            table: config.table,
            filter: config.filter,
          },
          (payload) => {
            try {
              config.callback(payload);
            } catch (error) {
              console.error(`Error in subscription callback for ${config.table}:`, error);
              config.errorCallback?.(error);
            }
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Successfully subscribed to ${config.table}`);
          } else if (status === 'CHANNEL_ERROR') {
            console.error(`Failed to subscribe to ${config.table}`);
            this.handleSubscriptionError(id, config);
          }
        });

      const subscription: ManagedSubscription = {
        id,
        channel,
        config,
        retryCount: 0,
      };

      this.subscriptions.set(id, subscription);
      return id;
    } catch (error) {
      console.error(`Error creating subscription for ${config.table}:`, error);
      this.setupPollingFallback(config, id);
      return id;
    }
  }

  /**
   * Unsubscribe from realtime events
   */
  unsubscribe(id: string): void {
    const subscription = this.subscriptions.get(id);
    if (subscription) {
      subscription.channel.unsubscribe();
      this.subscriptions.delete(id);
      console.log(`Unsubscribed from ${subscription.config.table}`);
    }
  }

  /**
   * Unsubscribe from all active subscriptions
   */
  unsubscribeAll(): void {
    for (const [id] of this.subscriptions) {
      this.unsubscribe(id);
    }
  }

  /**
   * Handle subscription errors with retry logic
   */
  private handleSubscriptionError(id: string, config: SubscriptionConfig) {
    const subscription = this.subscriptions.get(id);
    if (!subscription) return;

    subscription.retryCount++;
    
    if (subscription.retryCount <= 3) {
      console.log(`Retrying subscription for ${config.table} (attempt ${subscription.retryCount})`);
      setTimeout(() => {
        this.unsubscribe(id);
        this.subscribe(config, id);
      }, 2000 * subscription.retryCount);
    } else {
      console.warn(`Max retries reached for ${config.table}. Switching to polling.`);
      this.unsubscribe(id);
      this.setupPollingFallback(config, id);
    }
  }

  /**
   * Setup polling fallback when realtime fails
   */
  private setupPollingFallback(config: SubscriptionConfig, id: string) {
    console.log(`Setting up polling fallback for ${config.table}`);
    
    // Store last known state for comparison
    let lastData: any[] = [];
    
    const pollInterval = setInterval(async () => {
      try {
        const { data, error } = await realtimeClient
          .from(config.table)
          .select('*')
          .order('created_at', { ascending: false })
          .limit(10);

        if (error) throw error;

        // Compare with last known state to detect changes
        if (JSON.stringify(data) !== JSON.stringify(lastData)) {
          // Simulate realtime payload for new/updated records
          const newRecords = data?.filter(record => 
            !lastData.some(lastRecord => lastRecord.id === record.id)
          ) || [];

          newRecords.forEach(record => {
            config.callback({
              eventType: 'INSERT',
              new: record,
              old: null,
              schema: 'public',
              table: config.table,
            });
          });

          lastData = data || [];
        }
      } catch (error) {
        console.error(`Polling error for ${config.table}:`, error);
        config.errorCallback?.(error);
      }
    }, 5000); // Poll every 5 seconds

    // Store polling interval for cleanup
    const pollingSubscription: ManagedSubscription = {
      id,
      channel: { unsubscribe: () => clearInterval(pollInterval) } as any,
      config,
      retryCount: 0,
    };

    this.subscriptions.set(id, pollingSubscription);
  }

  /**
   * Check if table is supported for realtime
   */
  private isTableSupported(tableName: string): boolean {
    // List of tables that are known to work with realtime
    const supportedTables = [
      'workflow_notifications',
      'conversation_messages',
      'project_submissions',
      'quality_reviews_new',
      'manager_activities',
      'project_assignments',
    ];

    return supportedTables.includes(tableName);
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): { connected: boolean; subscriptions: number } {
    return {
      connected: this.isConnected,
      subscriptions: this.subscriptions.size,
    };
  }

  /**
   * Force reconnection
   */
  forceReconnect(): void {
    this.reconnectAttempts = 0;
    realtimeClient.realtime.disconnect();
    setTimeout(() => {
      realtimeClient.realtime.connect();
    }, 1000);
  }
}

// Export singleton instance
export const realtimeManager = new RealtimeManager();

// Convenience functions for common subscriptions
export const subscribeToNotifications = (userId: string, callback: (notification: any) => void) => {
  return realtimeManager.subscribe({
    table: 'workflow_notifications',
    event: 'INSERT',
    filter: `recipient_id=eq.${userId}`,
    callback: (payload) => callback(payload.new),
    errorCallback: (error) => console.error('Notification subscription error:', error),
  });
};

export const subscribeToMessages = (conversationId: string, callback: (message: any) => void) => {
  return realtimeManager.subscribe({
    table: 'conversation_messages',
    event: 'INSERT',
    filter: `conversation_id=eq.${conversationId}`,
    callback: (payload) => callback(payload.new),
    errorCallback: (error) => console.error('Message subscription error:', error),
  });
};

export const subscribeToQualityReviews = (designerId: string, callback: (review: any) => void) => {
  return realtimeManager.subscribe({
    table: 'quality_reviews_new',
    event: '*',
    filter: `designer_id=eq.${designerId}`,
    callback: (payload) => callback(payload),
    errorCallback: (error) => console.error('Quality review subscription error:', error),
  });
};

export const subscribeToProjectUpdates = (projectId: string, callback: (update: any) => void) => {
  return realtimeManager.subscribe({
    table: 'project_submissions',
    event: '*',
    filter: `project_id=eq.${projectId}`,
    callback: (payload) => callback(payload),
    errorCallback: (error) => console.error('Project update subscription error:', error),
  });
};
