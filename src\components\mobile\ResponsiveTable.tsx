'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useResponsiveNavigation } from '@/hooks/useMobileNavigation';

interface TableColumn {
  key: string;
  label: string;
  render?: (value: any, row: any) => React.ReactNode;
  className?: string;
  mobileHide?: boolean;
}

interface ResponsiveTableProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  emptyMessage?: string;
  onRowClick?: (row: any) => void;
  actions?: (row: any) => React.ReactNode;
  className?: string;
}

export default function ResponsiveTable({
  columns,
  data,
  loading = false,
  emptyMessage = 'No data available',
  onRowClick,
  actions,
  className = ''
}: ResponsiveTableProps) {
  const { isMobile } = useResponsiveNavigation();

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">{emptyMessage}</p>
        </CardContent>
      </Card>
    );
  }

  if (isMobile) {
    // Mobile Card View
    return (
      <div className={`space-y-4 ${className}`}>
        {data.map((row, index) => (
          <Card 
            key={index} 
            className={`${onRowClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`}
            onClick={() => onRowClick?.(row)}
          >
            <CardContent className="p-4">
              <div className="space-y-3">
                {columns.filter(col => !col.mobileHide).map((column) => {
                  const value = row[column.key];
                  const renderedValue = column.render ? column.render(value, row) : value;
                  
                  return (
                    <div key={column.key} className="flex justify-between items-start">
                      <span className="text-sm font-medium text-gray-600 min-w-0 flex-shrink-0 mr-3">
                        {column.label}:
                      </span>
                      <div className="text-sm text-gray-900 text-right min-w-0 flex-1">
                        {renderedValue}
                      </div>
                    </div>
                  );
                })}
                
                {actions && (
                  <div className="pt-3 border-t border-gray-200">
                    <div className="flex justify-end space-x-2">
                      {actions(row)}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Desktop Table View
  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  scope="col"
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className || ''}`}
                >
                  {column.label}
                </th>
              ))}
              {actions && (
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((row, index) => (
              <tr
                key={index}
                className={`${onRowClick ? 'cursor-pointer hover:bg-gray-50' : ''}`}
                onClick={() => onRowClick?.(row)}
              >
                {columns.map((column) => {
                  const value = row[column.key];
                  const renderedValue = column.render ? column.render(value, row) : value;
                  
                  return (
                    <td
                      key={column.key}
                      className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${column.className || ''}`}
                    >
                      {renderedValue}
                    </td>
                  );
                })}
                
                {actions && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {actions(row)}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
}

// Utility component for common table actions
export function TableActions({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex items-center space-x-2">
      {children}
    </div>
  );
}

// Utility component for status badges
export function StatusBadge({ 
  status, 
  variant = 'default' 
}: { 
  status: string; 
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' 
}) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'info':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Badge className={`${getVariantClasses()} border text-xs font-medium`}>
      {status}
    </Badge>
  );
}

// Utility component for mobile-optimized buttons
export function MobileButton({ 
  children, 
  size = 'sm',
  variant = 'outline',
  className = '',
  ...props 
}: any) {
  const { isMobile } = useResponsiveNavigation();
  
  const mobileClasses = isMobile 
    ? 'min-h-[44px] min-w-[44px] px-4 py-2 text-sm' 
    : '';

  return (
    <Button
      size={size}
      variant={variant}
      className={`${mobileClasses} ${className}`}
      {...props}
    >
      {children}
    </Button>
  );
}

// Hook for responsive table utilities
export function useResponsiveTable() {
  const { isMobile, isTablet } = useResponsiveNavigation();

  const getColumnVisibility = (columns: TableColumn[]) => {
    if (isMobile) {
      return columns.filter(col => !col.mobileHide);
    }
    return columns;
  };

  const getTableClasses = () => {
    if (isMobile) {
      return 'space-y-4';
    }
    return 'overflow-hidden rounded-lg shadow';
  };

  return {
    isMobile,
    isTablet,
    getColumnVisibility,
    getTableClasses,
  };
}
