"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  MessageSquare,
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  Plus,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  Target,
  TrendingUp
} from "lucide-react";

interface NegotiationSession {
  id: string;
  project_id: string;
  session_type: string;
  status: string;
  initial_terms: any;
  final_terms: any;
  manager_notes: string | null;
  started_at: string;
  completed_at: string | null;
  project: {
    title: string;
    budget: number;
  };
  client: {
    full_name: string;
    email: string;
  };
  designer: {
    full_name: string;
    email: string;
  };
}

export default function ManagerNegotiationsPage() {
  const { user, profile } = useOptimizedAuth();
  const [negotiations, setNegotiations] = useState<NegotiationSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('active');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchNegotiations();
    }
  }, [user, profile, filter]);

  const fetchNegotiations = async () => {
    try {
      let query = supabase
        .from('negotiation_sessions')
        .select(`
          *,
          project:projects(title, budget),
          client:profiles!negotiation_sessions_client_id_fkey(full_name, email),
          designer:profiles!negotiation_sessions_designer_id_fkey(full_name, email)
        `)
        .eq('manager_id', user?.id);

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query
        .order('started_at', { ascending: false });

      if (error) throw error;
      setNegotiations(data || []);
    } catch (error) {
      console.error('Error fetching negotiations:', error);
    } finally {
      setLoading(false);
    }
  };

  const startNewNegotiation = async (type: string) => {
    // This would open a modal or navigate to a new negotiation form
    window.location.href = `/manager/negotiations/new?type=${type}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getSessionTypeBadge = (type: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (type) {
      case 'pricing':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'timeline':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'scope':
        return `${baseClasses} bg-purple-100 text-purple-800`;
      case 'terms':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredNegotiations = negotiations.filter(negotiation =>
    negotiation.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    negotiation.client?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    negotiation.designer?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Negotiations</h1>
          <p className="text-gray-600 mt-2">Manage and facilitate client-designer negotiations</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => startNewNegotiation('pricing')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            New Negotiation
          </Button>
          <Button
            onClick={fetchNegotiations}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Quick Start Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Start New Negotiation</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => startNewNegotiation('pricing')}
          >
            <DollarSign className="h-5 w-5" />
            Pricing Discussion
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => startNewNegotiation('timeline')}
          >
            <Clock className="h-5 w-5" />
            Timeline Negotiation
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => startNewNegotiation('scope')}
          >
            <Target className="h-5 w-5" />
            Scope Changes
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => startNewNegotiation('terms')}
          >
            <MessageSquare className="h-5 w-5" />
            Terms & Conditions
          </Button>
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Sessions</p>
              <p className="text-2xl font-bold text-blue-600">
                {negotiations.filter(n => n.status === 'active').length}
              </p>
            </div>
            <MessageSquare className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">
                {negotiations.filter(n => n.status === 'completed').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-purple-600">
                {negotiations.length > 0 
                  ? Math.round((negotiations.filter(n => n.status === 'completed').length / negotiations.length) * 100)
                  : 0}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-orange-600">
                {negotiations.filter(n => {
                  const sessionDate = new Date(n.started_at);
                  const now = new Date();
                  return sessionDate.getMonth() === now.getMonth() && 
                         sessionDate.getFullYear() === now.getFullYear();
                }).length}
              </p>
            </div>
            <Calendar className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="active">Active Sessions</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="all">All Sessions</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects, clients, or designers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Negotiations List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Negotiation Sessions</h2>
          <p className="text-gray-600 mt-1">Manage ongoing and completed negotiations</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredNegotiations.length === 0 ? (
            <div className="p-8 text-center">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No negotiation sessions found</p>
              <Button
                onClick={() => startNewNegotiation('pricing')}
                className="mt-4 flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Start First Negotiation
              </Button>
            </div>
          ) : (
            filteredNegotiations.map((negotiation) => (
              <div key={negotiation.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {getStatusIcon(negotiation.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {negotiation.project?.title || 'Project Negotiation'}
                      </h3>
                      <span className={getStatusBadge(negotiation.status)}>
                        {negotiation.status.toUpperCase()}
                      </span>
                      <span className={getSessionTypeBadge(negotiation.session_type)}>
                        {negotiation.session_type.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Client:</span> {negotiation.client?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">Designer:</span> {negotiation.designer?.full_name}
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        <span className="font-medium">Budget:</span> ${negotiation.project?.budget?.toLocaleString()}
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-sm mb-3">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Started: {new Date(negotiation.started_at).toLocaleDateString()}</span>
                      </div>
                      {negotiation.completed_at && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span>Completed: {new Date(negotiation.completed_at).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>

                    {negotiation.manager_notes && (
                      <div className="bg-blue-50 rounded-lg p-3">
                        <p className="text-sm text-blue-800">
                          <span className="font-medium">Notes:</span> {negotiation.manager_notes}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/negotiations/${negotiation.id}`}
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>
                    
                    {negotiation.status === 'active' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-brown-600 hover:bg-brown-700"
                        onClick={() => window.location.href = `/manager/negotiations/${negotiation.id}/facilitate`}
                      >
                        <MessageSquare className="h-4 w-4" />
                        Facilitate
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Guidelines */}
      <div className="bg-green-50 rounded-xl p-6 border border-green-200">
        <div className="flex items-start gap-3">
          <Target className="h-6 w-6 text-green-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-green-900 mb-2">Negotiation Best Practices</h3>
            <div className="text-green-800 space-y-2">
              <p>• <strong>Stay neutral:</strong> Facilitate fair discussions between both parties</p>
              <p>• <strong>Document everything:</strong> Keep detailed notes of all agreements</p>
              <p>• <strong>Set clear expectations:</strong> Ensure both parties understand terms</p>
              <p>• <strong>Focus on win-win:</strong> Look for solutions that benefit everyone</p>
              <p>• <strong>Follow up:</strong> Ensure agreements are implemented properly</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
