# PayPal Integration Guide

This document provides instructions for setting up and configuring PayPal integration for the Seniors Archi Firm application.

## Overview

The application integrates with PayPal in two ways:

1. **PayPal as a Payment Method**: Users can link their PayPal accounts to make future payments
2. **Direct PayPal Payments**: Users can pay directly with PayPal for project milestones

## Prerequisites

Before you can use the PayPal integration, you need to:

1. Create a PayPal Developer account at [developer.paypal.com](https://developer.paypal.com/)
2. Create a PayPal application to get API credentials
3. Configure the application with your PayPal credentials

## Setting Up PayPal Developer Account

1. Go to [PayPal Developer Dashboard](https://developer.paypal.com/dashboard/)
2. Sign up or log in to your PayPal account
3. Navigate to "My Apps & Credentials"

## Creating a PayPal Application

1. In the PayPal Developer Dashboard, click on "Create App" under the "REST API apps" section
2. Give your app a name (e.g., "Seniors Archi Firm")
3. Select "Merchant" as the app type
4. Click "Create App"
5. Once created, you'll see your Client ID and Secret

## Configuring Environment Variables

Add the following environment variables to your `.env.local` file:

```
# PayPal Configuration
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_secret_here
```

For production, use your live PayPal credentials. For development and testing, use sandbox credentials.

## Database Migration

Run the SQL migration to update your database schema:

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Create a new query
4. Copy and paste the contents of `migrations/update_payment_methods_for_paypal.sql`
5. Execute the query

This migration adds the necessary columns to the `payment_methods` table to support PayPal accounts.

## PayPal Sandbox Testing

For testing purposes, PayPal provides sandbox accounts:

1. Go to the [PayPal Developer Dashboard](https://developer.paypal.com/dashboard/)
2. Navigate to "Sandbox" > "Accounts"
3. You'll find pre-created sandbox accounts for both business (merchant) and personal (customer) use
4. Use these accounts for testing your PayPal integration

## Implementation Details

### API Endpoints

The application includes the following API endpoints for PayPal integration:

1. `/api/paypal/create-billing-agreement`: Creates a PayPal billing agreement
2. `/api/paypal/execute-billing-agreement`: Executes a PayPal billing agreement after user approval
3. `/api/paypal/create-order`: Creates a PayPal order for a payment
4. `/api/paypal/capture-order`: Captures a PayPal payment after user approval

### Components

The application includes the following components for PayPal integration:

1. `PayPalButton`: A component for making payments with PayPal
2. `PayPalAccountForm`: A component for linking a PayPal account as a payment method

## Troubleshooting

### Common Issues

1. **"Failed to load PayPal SDK"**: Check that your PayPal Client ID is correctly set in your environment variables.

2. **"ID not recognized"**: This error occurs when using a simulated token instead of a real PayPal token. Make sure you're using the real PayPal API.

3. **PayPal button not appearing**: Check the browser console for errors. Make sure the PayPal SDK is loading correctly.

4. **PayPal sandbox account not working**: Make sure you're using the correct sandbox account credentials. The email and password are different from your developer account.

### Debugging Tips

1. Check the browser console for errors
2. Look at the network requests to see if there are any failed requests to the PayPal API
3. Verify that your environment variables are correctly set
4. Make sure your PayPal application is properly configured in the PayPal Developer Dashboard

## Resources

- [PayPal Developer Documentation](https://developer.paypal.com/docs/api/overview/)
- [PayPal JavaScript SDK Documentation](https://developer.paypal.com/docs/business/javascript-sdk/javascript-sdk-reference/)
- [PayPal REST API Documentation](https://developer.paypal.com/api/rest/)
