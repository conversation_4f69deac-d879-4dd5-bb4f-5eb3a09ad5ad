'use client';

import React, { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  Star, 
  MessageSquare,
  RefreshCw,
  Eye,
  FileText,
  Calendar,
  User
} from 'lucide-react';

interface QualityReview {
  id: string;
  project_id: string;
  submission_id?: string;
  milestone_id?: string;
  status: 'pending' | 'assigned' | 'in_review' | 'approved' | 'needs_revision' | 'rejected';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  review_type: 'milestone' | 'final' | 'revision' | 'emergency';
  sla_deadline: string;
  overall_score?: number;
  feedback?: string;
  revision_count: number;
  created_at: string;
  assigned_at?: string;
  reviewed_at?: string;
  reviewer?: {
    full_name: string;
    email: string;
  };
  projects?: {
    title: string;
  };
}

interface QualityReviewIntegrationProps {
  submissionId?: string;
  projectId?: string;
  role: 'designer' | 'client' | 'admin' | 'quality' | 'manager';
  onReviewUpdate?: (review: QualityReview) => void;
  compact?: boolean;
}

export default function QualityReviewIntegration({ 
  submissionId, 
  projectId, 
  role, 
  onReviewUpdate,
  compact = false 
}: QualityReviewIntegrationProps) {
  const { user } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [triggering, setTriggering] = useState(false);

  useEffect(() => {
    if (user && (submissionId || projectId)) {
      fetchReviews();
    }
  }, [user, submissionId, projectId]);

  const fetchReviews = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const params = new URLSearchParams();
      if (submissionId) params.append('submissionId', submissionId);
      if (projectId) params.append('projectId', projectId);

      const response = await fetch(`/api/submissions/quality-review?${params}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setReviews(data.reviews || []);
      }
    } catch (error) {
      console.error('Error fetching quality reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const triggerQualityReview = async (reviewType: 'milestone' | 'final' | 'revision' = 'milestone') => {
    if (!submissionId || !projectId) return;

    setTriggering(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch('/api/submissions/quality-review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          submissionId,
          projectId,
          reviewType,
          priority: reviewType === 'final' ? 'high' : 'medium'
        })
      });

      if (response.ok) {
        const data = await response.json();
        await fetchReviews(); // Refresh reviews
        if (onReviewUpdate && data.review) {
          onReviewUpdate(data.review);
        }
      }
    } catch (error) {
      console.error('Error triggering quality review:', error);
    } finally {
      setTriggering(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'assigned':
        return <User className="h-4 w-4 text-blue-500" />;
      case 'in_review':
        return <Eye className="h-4 w-4 text-purple-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'needs_revision':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'rejected':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'assigned':
        return 'bg-blue-100 text-blue-800';
      case 'in_review':
        return 'bg-purple-100 text-purple-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'needs_revision':
        return 'bg-orange-100 text-orange-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-blue-100 text-blue-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const hoursRemaining = (deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursRemaining < 0) {
      return `${Math.abs(Math.ceil(hoursRemaining))}h overdue`;
    } else if (hoursRemaining < 24) {
      return `${Math.ceil(hoursRemaining)}h remaining`;
    } else {
      return `${Math.ceil(hoursRemaining / 24)}d remaining`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <RefreshCw className="h-5 w-5 animate-spin text-gray-500" />
        <span className="ml-2 text-sm text-gray-600">Loading quality reviews...</span>
      </div>
    );
  }

  if (compact && reviews.length === 0) {
    return null; // Don't show anything in compact mode if no reviews
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Star className="h-5 w-5 text-yellow-500" />
          <h3 className="font-semibold text-gray-900">
            Quality Review {compact ? '' : 'Status'}
          </h3>
        </div>
        
        {role === 'designer' && submissionId && reviews.length === 0 && (
          <Button
            onClick={() => triggerQualityReview()}
            disabled={triggering}
            size="sm"
            className="flex items-center gap-2"
          >
            {triggering ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Star className="h-4 w-4" />
            )}
            Request Review
          </Button>
        )}
        
        {!compact && (
          <Button
            variant="outline"
            size="sm"
            onClick={fetchReviews}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        )}
      </div>

      {/* Reviews List */}
      {reviews.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h4 className="font-medium text-gray-900 mb-2">No Quality Reviews</h4>
            <p className="text-sm text-gray-600 mb-4">
              {role === 'designer' 
                ? 'Submit your work to trigger a quality review'
                : 'Quality reviews will appear here when submissions are made'
              }
            </p>
            {role === 'designer' && submissionId && (
              <Button
                onClick={() => triggerQualityReview()}
                disabled={triggering}
                className="flex items-center gap-2"
              >
                {triggering ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Star className="h-4 w-4" />
                )}
                Request Quality Review
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {reviews.map((review) => (
            <Card key={review.id} className="hover:shadow-md transition-shadow">
              <CardContent className={compact ? "p-4" : "p-6"}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(review.status)}
                      <Badge className={getStatusColor(review.status)}>
                        {review.status.replace('_', ' ')}
                      </Badge>
                      <Badge className={getPriorityColor(review.priority)}>
                        {review.priority}
                      </Badge>
                      <Badge variant="outline">
                        {review.review_type}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">
                          {formatTimeRemaining(review.sla_deadline)}
                        </span>
                      </div>

                      {review.reviewer && (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">
                            {review.reviewer.full_name}
                          </span>
                        </div>
                      )}

                      {review.overall_score && (
                        <div className="flex items-center gap-2">
                          <Star className="h-4 w-4 text-yellow-400" />
                          <span className="text-gray-600">
                            Score: {review.overall_score}/10
                          </span>
                        </div>
                      )}

                      {review.revision_count > 0 && (
                        <div className="flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 text-orange-400" />
                          <span className="text-gray-600">
                            Revisions: {review.revision_count}
                          </span>
                        </div>
                      )}
                    </div>

                    {review.feedback && !compact && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-md">
                        <div className="flex items-center gap-2 mb-2">
                          <MessageSquare className="h-4 w-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-700">Feedback</span>
                        </div>
                        <p className="text-sm text-gray-600">{review.feedback}</p>
                      </div>
                    )}
                  </div>

                  {!compact && (
                    <div className="ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/quality/reviews/${review.id}`, '_blank')}
                        className="flex items-center gap-2"
                      >
                        <Eye className="h-4 w-4" />
                        View
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
