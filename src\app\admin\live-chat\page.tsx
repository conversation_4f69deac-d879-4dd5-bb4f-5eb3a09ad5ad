"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase, supabaseLiveChat } from "@/lib/supabase";
import { motion, AnimatePresence } from "framer-motion";
import {
  MessageSquare,
  Send,
  User,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Settings,
  Users,
  MessageCircle,
  Eye,
  X,
  Phone,
  Mail,
  Globe,
  Calendar
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";

interface ChatSession {
  id: string;
  session_id: string;
  user_id?: string;
  visitor_name?: string;
  visitor_email?: string;
  status: 'waiting' | 'active' | 'ended' | 'abandoned';
  admin_joined_at?: string;
  created_at: string;
  updated_at: string;
  total_messages: number;
  current_page?: string;
  user_agent?: string;
  profiles?: {
    full_name: string;
    email: string;
  };
}

interface ChatMessage {
  id: string;
  session_id: string;
  sender_type: 'visitor' | 'admin' | 'system';
  sender_id?: string;
  content: string;
  message_type: 'text' | 'system';
  is_read: boolean;
  created_at: string;
}

interface AdminStatus {
  is_online: boolean;
  status_message: string;
  last_activity: string;
}

export default function AdminLiveChatPage() {
  const { user, profile, isLoading } = useOptimizedAuth();
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [adminStatus, setAdminStatus] = useState<AdminStatus>({
    is_online: false,
    status_message: 'Available for chat',
    last_activity: new Date().toISOString()
  });
  const [loadingData, setLoadingData] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [tablesExist, setTablesExist] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const realtimeChannelRef = useRef<any>(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Fetch chat sessions
  const fetchSessions = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('live_chat_sessions')
        .select(`
          *,
          profiles:user_id (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        // Check if it's a table not found error
        if (error.code === '42P01' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.warn('Live chat tables not found. Please run the database migration first.');
          setSessions([]);
          setTablesExist(false);
          return;
        }
        throw error;
      }
      setSessions(data || []);
    } catch (error) {
      console.warn('Could not fetch chat sessions:', error);
      setSessions([]);
    }
  }, []);

  // Fetch messages for selected session
  const fetchMessages = useCallback(async (sessionId: string) => {
    try {
      const { data, error } = await supabase
        .from('live_chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true });

      if (error) {
        // Check if it's a table not found error
        if (error.code === '42P01' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.warn('Live chat tables not found. Please run the database migration first.');
          setMessages([]);
          return;
        }
        throw error;
      }
      setMessages(data || []);

      // Mark admin messages as read
      await supabase
        .from('live_chat_messages')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('session_id', sessionId)
        .eq('sender_type', 'visitor')
        .eq('is_read', false);

    } catch (error) {
      console.warn('Could not fetch messages:', error);
      setMessages([]);
    }
  }, []);

  // Get or create admin status
  const initializeAdminStatus = useCallback(async () => {
    if (!user?.id) return;

    try {
      // Try to get existing status
      let { data, error } = await supabase
        .from('admin_chat_status')
        .select('*')
        .eq('admin_id', user.id)
        .single();

      if (error && error.code === 'PGRST116') {
        // No existing status, create one
        const { data: newStatus, error: createError } = await supabase
          .from('admin_chat_status')
          .insert({
            admin_id: user.id,
            is_online: true,
            status_message: 'Available for chat',
            last_activity: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) {
          console.warn('Could not create admin status - tables may not exist yet:', createError.message);
          return;
        }
        data = newStatus;
      } else if (error) {
        // Check if it's a table not found error
        if (error.code === '42P01' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.warn('Live chat tables not found. Please run the database migration first.');
          setTablesExist(false);
          return;
        }
        throw error;
      }

      if (data) {
        setAdminStatus({
          is_online: data.is_online,
          status_message: data.status_message,
          last_activity: data.last_activity
        });

        // Update to online when page loads
        await supabase
          .from('admin_chat_status')
          .update({
            is_online: true,
            last_activity: new Date().toISOString()
          })
          .eq('admin_id', user.id);

        setAdminStatus(prev => ({ ...prev, is_online: true }));
      }
    } catch (error) {
      console.warn('Live chat system not available - please run database migration:', error);
    }
  }, [user?.id]);

  // Toggle admin online status
  const toggleOnlineStatus = async () => {
    if (!user?.id) return;

    const newStatus = !adminStatus.is_online;
    
    try {
      await supabase
        .from('admin_chat_status')
        .update({
          is_online: newStatus,
          last_activity: new Date().toISOString()
        })
        .eq('admin_id', user.id);

      setAdminStatus(prev => ({ ...prev, is_online: newStatus }));
    } catch (error) {
      console.error('Error updating admin status:', error);
    }
  };

  // Send message
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedSession || !user?.id) return;

    setIsSending(true);
    try {
      const messageData = {
        session_id: selectedSession.id,
        sender_type: 'admin',
        sender_id: user.id,
        content: newMessage.trim(),
        message_type: 'text'
      };

      const { data, error } = await supabase
        .from('live_chat_messages')
        .insert([messageData])
        .select()
        .single();

      if (error) throw error;

      setMessages(prev => [...prev, data]);
      setNewMessage("");

      // Update session status to active and set admin_joined_at if not already set
      if (selectedSession.status === 'waiting' || !selectedSession.admin_joined_at) {
        await supabase
          .from('live_chat_sessions')
          .update({
            status: 'active',
            admin_joined_at: new Date().toISOString()
          })
          .eq('id', selectedSession.id);

        setSelectedSession(prev => prev ? {
          ...prev,
          status: 'active',
          admin_joined_at: new Date().toISOString()
        } : null);

        // Refresh sessions list
        fetchSessions();
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  // Setup realtime subscription
  const setupRealtimeSubscription = useCallback(() => {
    if (realtimeChannelRef.current) {
      supabaseLiveChat.removeChannel(realtimeChannelRef.current);
    }

    const channel = supabaseLiveChat
      .channel('admin_live_chat')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_chat_sessions'
        },
        () => {
          fetchSessions();
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'live_chat_sessions'
        },
        () => {
          fetchSessions();
        }
      );

    if (selectedSession) {
      channel.on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_chat_messages',
          filter: `session_id=eq.${selectedSession.id}`
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage;
          if (newMessage.sender_type === 'visitor') {
            setMessages(prev => [...prev, newMessage]);
            
            // Mark as read immediately
            supabaseLiveChat
              .from('live_chat_messages')
              .update({ is_read: true, read_at: new Date().toISOString() })
              .eq('id', newMessage.id)
              .then(() => {});
          }
        }
      );
    }

    channel.subscribe();
    realtimeChannelRef.current = channel;
  }, [selectedSession?.id, fetchSessions]);

  // Initialize data
  useEffect(() => {
    if (user && profile?.role === 'admin') {
      const initializeData = async () => {
        setLoadingData(true);
        await Promise.all([
          fetchSessions(),
          initializeAdminStatus()
        ]);
        setLoadingData(false);
      };

      initializeData();
      setupRealtimeSubscription();
    }
  }, [user, profile, fetchSessions, initializeAdminStatus, setupRealtimeSubscription]);

  // Update activity periodically
  useEffect(() => {
    if (!user?.id || !adminStatus.is_online) return;

    const interval = setInterval(async () => {
      await supabase
        .from('admin_chat_status')
        .update({ last_activity: new Date().toISOString() })
        .eq('admin_id', user.id);
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [user?.id, adminStatus.is_online]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (realtimeChannelRef.current) {
        supabaseLiveChat.removeChannel(realtimeChannelRef.current);
      }

      // Set admin offline when leaving page
      if (user?.id) {
        supabase
          .from('admin_chat_status')
          .update({ is_online: false })
          .eq('admin_id', user.id)
          .then(() => {});
      }
    };
  }, [user?.id]);

  // Handle session selection
  const handleSessionSelect = (session: ChatSession) => {
    setSelectedSession(session);
    fetchMessages(session.id);
  };

  if (isLoading || loadingData) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user || profile?.role !== 'admin') {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
        <p className="text-gray-600">You need admin privileges to access this page.</p>
      </div>
    );
  }

  // Show setup message if tables don't exist
  if (!tablesExist) {
    return (
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <MessageSquare className="h-8 w-8 text-primary mr-3" />
                Live Chat Management
              </h1>
              <p className="text-gray-600 mt-1">
                Manage real-time conversations with website visitors
              </p>
            </div>
          </div>
        </div>

        {/* Setup Message */}
        <div className="flex-1 flex items-center justify-center bg-gray-50">
          <div className="text-center max-w-md mx-auto p-8">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Database Setup Required</h2>
            <p className="text-gray-600 mb-6">
              The live chat system requires database tables to be created. Please run the database migration to get started.
            </p>
            <div className="bg-gray-100 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-gray-900 mb-2">Setup Steps:</h3>
              <ol className="text-sm text-gray-600 text-left space-y-1">
                <li>1. Go to your Supabase Dashboard</li>
                <li>2. Navigate to SQL Editor</li>
                <li>3. Copy and paste the contents of <code className="bg-gray-200 px-1 rounded">database/live-chat-schema.sql</code></li>
                <li>4. Execute the query</li>
                <li>5. Enable realtime for the live chat tables</li>
              </ol>
            </div>
            <button
              onClick={() => {
                setTablesExist(true);
                fetchSessions();
                initializeAdminStatus();
              }}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
            >
              Check Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  const activeSessions = sessions.filter(s => s.status === 'active' || s.status === 'waiting');
  const waitingSessions = sessions.filter(s => s.status === 'waiting');

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <MessageSquare className="h-8 w-8 text-primary mr-3" />
              Live Chat Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage real-time conversations with website visitors
            </p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Admin Status Toggle */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Status:</span>
              <button
                onClick={toggleOnlineStatus}
                className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  adminStatus.is_online
                    ? 'bg-green-100 text-green-800 hover:bg-green-200'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
              >
                <div className={`w-2 h-2 rounded-full ${
                  adminStatus.is_online ? 'bg-green-500' : 'bg-gray-500'
                }`} />
                <span>{adminStatus.is_online ? 'Online' : 'Offline'}</span>
              </button>
            </div>

            {/* Stats */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{activeSessions.length} Active</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{waitingSessions.length} Waiting</span>
              </div>
            </div>

            <Button
              onClick={fetchSessions}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Sessions List */}
        <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="font-semibold text-gray-900">Chat Sessions</h2>
            <p className="text-sm text-gray-600">{sessions.length} total sessions</p>
          </div>

          <div className="flex-1 overflow-y-auto">
            {sessions.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <MessageCircle className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p className="font-medium">No chat sessions</p>
                <p className="text-sm">Sessions will appear here when visitors start chatting</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {sessions.map((session) => (
                  <motion.div
                    key={session.id}
                    className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedSession?.id === session.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                    }`}
                    onClick={() => handleSessionSelect(session)}
                    whileHover={{ x: 2 }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${
                            session.status === 'active' ? 'bg-green-500' :
                            session.status === 'waiting' ? 'bg-yellow-500' :
                            'bg-gray-500'
                          }`} />
                          <h3 className="font-medium text-gray-900 truncate">
                            {session.profiles?.full_name || session.visitor_name || 'Anonymous'}
                          </h3>
                        </div>

                        <p className="text-sm text-gray-600 truncate mt-1">
                          {session.profiles?.email || session.visitor_email || 'No email provided'}
                        </p>

                        <div className="flex items-center justify-between mt-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            session.status === 'active' ? 'bg-green-100 text-green-800' :
                            session.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {session.status}
                          </span>

                          <span className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(session.created_at), { addSuffix: true })}
                          </span>
                        </div>

                        {session.total_messages > 0 && (
                          <p className="text-xs text-gray-500 mt-1">
                            {session.total_messages} message{session.total_messages !== 1 ? 's' : ''}
                          </p>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedSession ? (
            <>
              {/* Chat Header */}
              <div className="bg-white border-b border-gray-200 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {selectedSession.profiles?.full_name || selectedSession.visitor_name || 'Anonymous Visitor'}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        {(selectedSession.profiles?.email || selectedSession.visitor_email) && (
                          <div className="flex items-center space-x-1">
                            <Mail className="h-3 w-3" />
                            <span>{selectedSession.profiles?.email || selectedSession.visitor_email}</span>
                          </div>
                        )}
                        {selectedSession.current_page && (
                          <div className="flex items-center space-x-1">
                            <Globe className="h-3 w-3" />
                            <span className="truncate max-w-xs">{selectedSession.current_page}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      selectedSession.status === 'active' ? 'bg-green-100 text-green-800' :
                      selectedSession.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedSession.status}
                    </span>

                    <button
                      onClick={() => setSelectedSession(null)}
                      className="text-gray-400 hover:text-gray-600 p-1"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.sender_type === 'admin' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-[80%] rounded-lg p-3 ${
                          message.sender_type === 'admin'
                            ? 'bg-primary text-white rounded-br-none'
                            : message.sender_type === 'system'
                            ? 'bg-blue-100 text-blue-800 rounded-lg text-center text-sm'
                            : 'bg-white shadow-md rounded-bl-none'
                        }`}
                      >
                        <p className={`${message.sender_type === 'system' ? 'text-xs' : 'text-sm'}`}>
                          {message.content}
                        </p>
                        {message.sender_type !== 'system' && (
                          <div className="flex items-center justify-between mt-1">
                            <p className={`text-xs ${
                              message.sender_type === 'admin' ? 'text-white/70' : 'text-gray-500'
                            }`}>
                              {new Date(message.created_at).toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                            {message.sender_type === 'admin' && (
                              <CheckCircle className="h-3 w-3 text-white/70 ml-2" />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <form onSubmit={sendMessage} className="bg-white border-t border-gray-200 p-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    disabled={isSending}
                  />
                  <Button
                    type="submit"
                    disabled={!newMessage.trim() || isSending}
                    className="px-6"
                  >
                    {isSending ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                <div className="mt-2 text-xs text-gray-500 flex items-center">
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    selectedSession.status === 'active' ? 'bg-green-400' : 'bg-yellow-400'
                  }`} />
                  {selectedSession.status === 'active'
                    ? 'Connected with visitor'
                    : 'Visitor is waiting for response'
                  }
                </div>
              </form>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50">
              <div className="text-center text-gray-500">
                <MessageSquare className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Chat Session</h3>
                <p>Choose a session from the left to start chatting with visitors</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
