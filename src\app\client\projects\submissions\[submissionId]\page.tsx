"use client";

import { useEffect, useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  ArrowLeft,
  Loader2,
  AlertCircle
} from "lucide-react";

export default function GlobalSubmissionRedirect() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const submissionId = params.submissionId as string;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user && submissionId) {
      redirectToProjectContext();
    }
  }, [user, submissionId]);

  const redirectToProjectContext = async () => {
    try {
      // Fetch submission to get project_id
      const { data: submission, error } = await supabase
        .from('submissions')
        .select('project_id')
        .eq('id', submissionId)
        .single();

      if (error) throw error;

      if (submission) {
        // Redirect to project-contextual route
        router.replace(`/client/projects/${submission.project_id}/submissions/${submissionId}`);
      } else {
        setError('Submission not found');
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching submission:', error);
      setError('Failed to load submission');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Loader2 className="animate-spin h-12 w-12 text-primary mx-auto mb-4" />
          <p className="text-gray-500">Redirecting to submission...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <div>
            <h3 className="font-medium">Error Loading Submission</h3>
            <p className="mt-1">{error}</p>
          </div>
        </div>
        <div className="mt-6">
          <Link href="/client/projects">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return null; // This should never render as we redirect immediately
}