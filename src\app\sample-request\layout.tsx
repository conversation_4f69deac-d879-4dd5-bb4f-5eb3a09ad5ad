import { Metadata } from 'next';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

export const metadata: Metadata = generateSEOMetadata({
  title: "Free Design Sample Request",
  description: "Get a free architectural design sample in 24-48 hours. Upload your sketches, photos, or ideas and see how we transform them into professional 3D visualizations. No cost, no commitment.",
  path: "/sample-request",
  keywords: [
    "free design sample",
    "architectural visualization",
    "3D rendering sample",
    "design consultation",
    "architectural sketches",
    "building design sample",
    "free architectural service",
    "design mockup",
    "architectural preview",
    "design transformation"
  ]
});

export default function SampleRequestLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
