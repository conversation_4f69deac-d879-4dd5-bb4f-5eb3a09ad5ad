import { Metadata } from 'next';
import Layout from "@/components/Layout";
import HeroSection from "@/components/home/<USER>";
import ServicesSection from "@/components/home/<USER>";
import WhyChooseUsSection from "@/components/home/<USER>";
import FeaturedProjectsSection from "@/components/home/<USER>";
import TestimonialsSection from "@/components/home/<USER>";
import CareersSection from "@/components/home/<USER>";
import OurProcessSection from "@/components/home/<USER>";
import FAQsSection from "@/components/home/<USER>";
import NewsletterSection from "@/components/home/<USER>";
import { generateMetadata as generateSEOMetadata, generateOrganizationSchema } from '@/lib/seo';
import StructuredData from '@/components/StructuredData';
import CombinedAboutSection from "@/components/home/<USER>";
import CombinedContactSection from "@/components/home/<USER>";
import { RootRedirect } from "@/components/auth/RootRedirect";

export const metadata: Metadata = generateSEOMetadata({
  title: "Leading Architectural Design Firm",
  description: "Transform your vision into reality with Senior's Archi-Firm. Specializing in innovative, sustainable, and client-focused architectural solutions. Get your free design sample in 24-48 hours.",
  keywords: [
    "architectural design firm",
    "building design",
    "architectural visualization",
    "sustainable architecture",
    "residential design",
    "commercial architecture",
    "interior design",
    "urban planning",
    "architectural services",
    "design consultation"
  ]
});

export default function Home() {
  const organizationSchema = generateOrganizationSchema();

  return (
    <Layout>
      <RootRedirect />
      <StructuredData data={organizationSchema} />
      <HeroSection />
      <ServicesSection />
      <OurProcessSection />
      <CombinedAboutSection />
      {/* WhyChooseUsSection is hidden but not deleted */}
      {/* <WhyChooseUsSection /> */}
      <FeaturedProjectsSection />
      <TestimonialsSection />
      {/* NewsletterSection is hidden - newsletter signup now available in footer */}
      {/* <NewsletterSection /> */}
      <FAQsSection />
      <CareersSection />
      <CombinedContactSection />
    </Layout>
  );
}
