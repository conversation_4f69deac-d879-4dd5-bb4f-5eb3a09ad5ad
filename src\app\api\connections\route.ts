import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/connections
 * Creates a new connection between a designer and a client
 * 
 * Request body:
 * {
 *   designerId: string; // ID of the designer
 *   clientId: string; // ID of the client
 *   createdBy: string; // ID of the user creating the connection (admin or designer)
 *   invitationId?: string; // Optional ID of the invitation that led to this connection
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const { designerId, clientId, createdBy, invitationId } = await request.json();
    
    if (!designerId || !clientId) {
      return NextResponse.json(
        { error: 'Designer ID and Client ID are required' },
        { status: 400 }
      );
    }
    
    // Verify that the designer exists and has the designer role
    const { data: designer, error: designerError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', designerId)
      .single();
    
    if (designerError || !designer || designer.role !== 'designer') {
      return NextResponse.json(
        { error: 'Invalid designer ID' },
        { status: 400 }
      );
    }
    
    // Verify that the client exists and has the client role
    const { data: client, error: clientError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', clientId)
      .single();
    
    if (clientError || !client || client.role !== 'client') {
      return NextResponse.json(
        { error: 'Invalid client ID' },
        { status: 400 }
      );
    }
    
    // Check if a connection already exists
    const { data: existingConnection, error: connectionError } = await supabase
      .from('connections')
      .select('id')
      .eq('designer_id', designerId)
      .eq('client_id', clientId)
      .maybeSingle();
    
    if (connectionError) {
      console.error('Error checking existing connection:', connectionError);
      return NextResponse.json(
        { error: 'Failed to check existing connection' },
        { status: 500 }
      );
    }
    
    if (existingConnection) {
      return NextResponse.json(
        { error: 'Connection already exists between these users' },
        { status: 409 }
      );
    }
    
    // Create the connection
    const { data, error } = await supabase
      .from('connections')
      .insert({
        designer_id: designerId,
        client_id: clientId,
        created_by: createdBy,
        invitation_id: invitationId
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating connection:', error);
      return NextResponse.json(
        { error: 'Failed to create connection' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ connection: data }, { status: 201 });
  } catch (error) {
    console.error('Error creating connection:', error);
    return NextResponse.json(
      { error: 'Failed to create connection' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/connections
 * Gets all connections for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    let query = supabase
      .from('connections')
      .select(`
        id,
        status,
        created_at,
        designer:designer_id (
          id,
          full_name,
          avatar_url,
          specialization
        ),
        client:client_id (
          id,
          full_name,
          avatar_url,
          company
        )
      `);
    
    // Filter based on user role
    if (profile.role === 'designer') {
      query = query.eq('designer_id', user.id);
    } else if (profile.role === 'client') {
      query = query.eq('client_id', user.id);
    }
    // If admin, get all connections (no filter)
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching connections:', error);
      return NextResponse.json(
        { error: 'Failed to fetch connections' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ connections: data }, { status: 200 });
  } catch (error) {
    console.error('Error fetching connections:', error);
    return NextResponse.json(
      { error: 'Failed to fetch connections' },
      { status: 500 }
    );
  }
}
