"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { 
  Search, 
  Filter, 
  Upload, 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  FolderOpen,
  Calendar,
  Download,
  Eye
} from "lucide-react";

type Submission = {
  id: string;
  title: string;
  description: string | null;
  status: string;
  project_id: string;
  project_title: string;
  client_name: string;
  created_at: string;
  file_url: string | null;
  file_type: string | null;
  feedback: string | null;
  revision_requested: boolean;
};

export default function DesignerSubmissions() {
  const { user } = useOptimizedAuth();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  useEffect(() => {
    if (user) {
      fetchSubmissions();
    }
  }, [user]);

  const fetchSubmissions = async () => {
    setLoading(true);
    try {
      const { data, error: submissionsError } = await supabase
        .from('submissions')
        .select(`
          id,
          title,
          description,
          status,
          project_id,
          projects(title, client_id, profiles:profiles(full_name)),
          created_at,
          file_url,
          file_type,
          feedback,
          revision_requested
        `)
        .eq('designer_id', user?.id)
        .order('created_at', { ascending: false });

      if (submissionsError) throw submissionsError;

      setSubmissions(
        (data || []).map(submission => ({
          id: submission.id,
          title: submission.title,
          description: submission.description,
          status: submission.status,
          project_id: submission.project_id,
          project_title: submission.projects?.[0]?.title || 'Unknown Project',
          client_name: submission.projects?.[0]?.profiles?.[0]?.full_name || 'Unknown Client',
          created_at: submission.created_at,
          file_url: submission.file_url,
          file_type: submission.file_type,
          feedback: submission.feedback,
          revision_requested: submission.revision_requested
        }))
      );
    } catch (error: unknown) {
      console.error('Error fetching submissions:', error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('Failed to load submissions');
      }
    } finally {
      setLoading(false);
    }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string, revisionRequested: boolean) => {
    if (revisionRequested) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          <AlertCircle className="h-3 w-3 mr-1" />
          Revision Requested
        </span>
      );
    }

    switch (status) {
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending Review
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertCircle className="h-3 w-3 mr-1" />
            Rejected
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  const getFileIcon = (fileType: string | null) => {
    if (!fileType) return <FileText className="h-6 w-6 text-gray-400" />;
    
    if (fileType.includes('image')) {
      return <img src="/icons/image-file.svg" alt="Image" className="h-6 w-6" />;
    } else if (fileType.includes('pdf')) {
      return <img src="/icons/pdf-file.svg" alt="PDF" className="h-6 w-6" />;
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return <img src="/icons/doc-file.svg" alt="Document" className="h-6 w-6" />;
    } else {
      return <FileText className="h-6 w-6 text-gray-400" />;
    }
  };

  // Filter submissions based on search query and status filter
  const filteredSubmissions = submissions.filter(submission => {
    const matchesSearch = 
      submission.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (submission.description && submission.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      submission.project_title.toLowerCase().includes(searchQuery.toLowerCase());
    
    let matchesStatus = true;
    if (statusFilter !== 'all') {
        if (statusFilter === 'revision_requested') {
            matchesStatus = submission.revision_requested;
          } else {
            matchesStatus = submission.status === statusFilter;
          }
        }
        
        return matchesSearch && matchesStatus;
      });
    
      if (loading && submissions.length === 0) {
        return (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        );
      }
    
      return (
        <div>
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-2xl font-bold">Design Submissions</h1>
            
            <Link href="/designer/submissions/new">
              <Button className="flex items-center">
                <Upload className="h-4 w-4 mr-2" />
                New Submission
              </Button>
            </Link>
          </div>
    
          {error && (
            <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg">
              <p>{error}</p>
            </div>
          )}
    
          {/* Filter and Search */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search submissions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 p-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="relative">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="pl-10 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent appearance-none pr-8"
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending Review</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="revision_requested">Revision Requested</option>
                </select>
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Filter className="h-5 w-5 text-gray-400" />
                </div>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
    
          {/* No submissions state */}
          {submissions.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <Upload className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h2 className="text-xl font-medium mb-2">No submissions yet</h2>
              <p className="text-gray-500 mb-6">
                Start by uploading your first design submission for a project.
              </p>
              <Link href="/designer/submissions/new">
                <Button>
                  <Upload className="h-4 w-4 mr-2" />
                  Create Submission
                </Button>
              </Link>
            </div>
          ) : filteredSubmissions.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <h2 className="text-xl font-medium mb-2">No matching submissions found</h2>
              <p className="text-gray-500">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Submission
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Project
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSubmissions.map((submission) => (
                      <tr key={submission.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0 mr-3 bg-gray-100 rounded flex items-center justify-center">
                              {getFileIcon(submission.file_type)}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{submission.title}</div>
                              {submission.description && (
                                <div className="text-gray-500 text-sm truncate max-w-xs">{submission.description}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <Link 
                            href={`/designer/projects/${submission.project_id}`}
                            className="flex items-center text-primary hover:underline"
                          >
                            <FolderOpen className="h-4 w-4 mr-1" />
                            {submission.project_title}
                          </Link>
                          <div className="text-gray-500 text-sm">{submission.client_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-gray-500">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(submission.created_at)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(submission.status, submission.revision_requested)}
                          {submission.feedback && (
                            <div className="mt-1 text-xs text-gray-500 max-w-xs truncate">
                              Feedback: {submission.feedback}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex space-x-2">
                            <Link href={`/designer/submissions/${submission.id}`}>
                              <Button variant="outline" size="sm" className="flex items-center">
                                <Eye className="h-3 w-3 mr-1" />
                                View
                              </Button>
                            </Link>
                            {submission.file_url && (
                              <a 
                                href={submission.file_url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                download
                              >
                                <Button variant="outline" size="sm" className="flex items-center">
                                  <Download className="h-3 w-3 mr-1" />
                                  Download
                                </Button>
                              </a>
                            )}
                            {submission.revision_requested && (
                              <Link href={`/designer/submissions/${submission.id}/revise`}>
                                <Button size="sm" className="flex items-center">
                                  <Upload className="h-3 w-3 mr-1" />
                                  Submit Revision
                                </Button>
                              </Link>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      );
    }
    