import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { PREDEFINED_SKILLS } from '@/types/portfolio';

/**
 * GET /api/designer/skills
 * Gets the skills for the authenticated designer or a specified designer
 */
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Determine which designer's skills to fetch
    const designerId = request.nextUrl.searchParams.get('designer_id') || user.id;
    
    // If not the user's own skills and not an admin, check if the requested designer is valid
    if (designerId !== user.id && profile.role !== 'admin') {
      const { data: designerProfile, error: designerError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', designerId)
        .eq('role', 'designer')
        .single();
      
      if (designerError || !designerProfile) {
        return NextResponse.json(
          { error: 'Designer not found' },
          { status: 404 }
        );
      }
    }
    
    // Get the designer's skills
    const { data: designerProfile, error: skillsError } = await supabase
      .from('profiles')
      .select('skills')
      .eq('id', designerId)
      .single();
    
    if (skillsError) {
      return NextResponse.json(
        { error: 'Failed to fetch designer skills' },
        { status: 500 }
      );
    }
    
    // Return the skills and predefined skills for reference
    return NextResponse.json({
      skills: designerProfile.skills || [],
      predefined_skills: PREDEFINED_SKILLS
    }, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/designer/skills:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/designer/skills
 * Updates the skills for the authenticated designer
 * 
 * Request body:
 * {
 *   skills: string[];
 * }
 */
export async function PATCH(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Only designers can update their skills
    if (profile.role !== 'designer') {
      return NextResponse.json(
        { error: 'Only designers can update their skills' },
        { status: 403 }
      );
    }
    
    const { skills } = await request.json();
    
    // Validate required fields
    if (!skills || !Array.isArray(skills)) {
      return NextResponse.json(
        { error: 'Skills must be an array' },
        { status: 400 }
      );
    }
    
    // Limit the number of skills
    if (skills.length > 20) {
      return NextResponse.json(
        { error: 'Maximum of 20 skills allowed' },
        { status: 400 }
      );
    }
    
    // Update the skills
    const { data, error } = await supabase
      .from('profiles')
      .update({ skills })
      .eq('id', user.id)
      .select('skills')
      .single();
    
    if (error) {
      console.error('Error updating skills:', error);
      return NextResponse.json(
        { error: 'Failed to update skills' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error in PATCH /api/designer/skills:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
