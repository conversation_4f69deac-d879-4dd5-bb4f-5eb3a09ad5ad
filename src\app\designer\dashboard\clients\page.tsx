"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import {
  Users,
  Mail,
  Phone,
  MapPin,
  Building,
  Calendar,
  MessageSquare,
  FolderKanban,
  Search,
  Filter,
  UserPlus,
  Eye,
  MoreVertical
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface Client {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
  avatar_url?: string;
  company?: string;
  location?: string;
  connected_at: string;
  last_active?: string;
  project_count: number;
  total_spent: number;
  status: 'active' | 'inactive';
}

export default function ClientsPage() {
  const { user } = useOptimizedAuth();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    if (user) {
      fetchClients();
    }
  }, [user]);

  const fetchClients = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log('🔍 Fetching clients for designer dashboard:', user.id);

      // Step 1: Get connections from the connections table (CORRECT APPROACH)
      const { data: connections, error: connectionsError } = await supabase
        .from('connections')
        .select(`
          id,
          client_id,
          status,
          created_at,
          updated_at
        `)
        .eq('designer_id', user.id)
        .eq('status', 'active');

      if (connectionsError) {
        console.error('❌ Connections query error:', {
          error: connectionsError,
          message: connectionsError?.message || 'Unknown error',
          code: connectionsError?.code || 'No code'
        });
        throw connectionsError;
      }

      if (!connections || connections.length === 0) {
        console.log('🔗 No active connections found for designer');
        setClients([]);
        return;
      }

      console.log(`🔗 Found ${connections.length} active connections`);
      const clientIds = connections.map(conn => conn.client_id);

      // Step 2: Get client profiles from profiles table (FIXED: removed non-existent columns)
      const { data: clientProfiles, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          avatar_url,
          role
        `)
        .in('id', clientIds)
        .eq('role', 'client');

      if (profilesError) {
        console.error('❌ Profiles query error:', {
          error: profilesError,
          message: profilesError?.message || 'Unknown error',
          code: profilesError?.code || 'No code',
          clientIds: clientIds
        });
        throw profilesError;
      }

      if (!clientProfiles || clientProfiles.length === 0) {
        console.warn('⚠️ No client profiles found for connection IDs:', clientIds);
        setClients([]);
        return;
      }

      console.log(`✅ Found ${clientProfiles.length} client profiles`);

      // Step 3: Get project counts and spending for each client
      const clientsWithStats = await Promise.all(
        clientProfiles.map(async (profile) => {
          const connection = connections.find(conn => conn.client_id === profile.id);

          // Get project count
          const { count: projectCount, error: projectError } = await supabase
            .from('projects')
            .select('*', { count: 'exact', head: true })
            .eq('client_id', profile.id)
            .eq('designer_id', user.id);

          if (projectError) {
            console.error('❌ Error fetching project count for client:', profile.id, projectError);
          }

          // Get total spending
          const { data: transactions, error: transactionError } = await supabase
            .from('transactions')
            .select('amount')
            .eq('client_id', profile.id)
            .eq('designer_id', user.id)
            .eq('status', 'completed');

          if (transactionError) {
            console.error('❌ Error fetching transactions for client:', profile.id, transactionError);
          }

          const totalSpent = transactions?.reduce((sum, t) => sum + (t.amount || 0), 0) || 0;

          return {
            id: profile.id,
            full_name: profile.full_name || '',
            email: profile.email || '',
            phone: undefined, // Column doesn't exist in profiles table
            avatar_url: profile.avatar_url,
            company: undefined, // Column doesn't exist in profiles table
            location: undefined, // Column doesn't exist in profiles table
            connected_at: connection?.created_at || '',
            project_count: projectCount || 0,
            total_spent: totalSpent,
            status: connection?.status === 'active' ? 'active' as const : 'inactive' as const
          };
        })
      );

      console.log('📊 Clients with stats processed:', {
        count: clientsWithStats.length,
        clients: clientsWithStats.map(c => ({
          id: c.id,
          name: c.full_name,
          projects: c.project_count,
          spent: c.total_spent
        }))
      });

      setClients(clientsWithStats);
    } catch (error) {
      console.error('❌ Error fetching clients:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        userId: user.id
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredClients = clients.filter(client => {
    const matchesSearch = 
      client.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.company?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || client.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: clients.length,
    active: clients.filter(c => c.status === 'active').length,
    totalProjects: clients.reduce((sum, c) => sum + c.project_count, 0),
    totalRevenue: clients.reduce((sum, c) => sum + c.total_spent, 0)
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Connected Clients</h1>
          <p className="text-gray-600">Clients who have accepted your invitations</p>
        </div>
        <Link href="/designer/dashboard/invitations">
          <Button className="bg-brown-600 hover:bg-brown-700">
            <UserPlus className="h-4 w-4 mr-2" />
            Invite New Client
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Clients</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Clients</p>
              <p className="text-2xl font-bold text-gray-900">{stats.active}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <Users className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalProjects}</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-full">
              <FolderKanban className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm border"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${stats.totalRevenue.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-orange-50 rounded-full">
              <Building className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search clients by name, email, or company..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brown-500 focus:border-transparent"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Clients List */}
      {filteredClients.length === 0 ? (
        <div className="text-center py-12">
          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {clients.length === 0 ? 'No connected clients yet' : 'No clients match your search'}
          </h3>
          <p className="text-gray-500 mb-4">
            {clients.length === 0 
              ? 'Start by inviting clients to connect with you' 
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {clients.length === 0 && (
            <Link href="/designer/dashboard/invitations">
              <Button className="bg-brown-600 hover:bg-brown-700">
                <UserPlus className="h-4 w-4 mr-2" />
                Send Your First Invitation
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.slice(0, 6).map((client, index) => (
            <motion.div
              key={client.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-3">
                    {client.avatar_url ? (
                      <img
                        src={client.avatar_url}
                        alt={client.full_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-sm font-medium text-gray-600">
                        {client.full_name.split(' ').map(n => n[0]).join('')}
                      </span>
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{client.full_name}</h3>
                    <p className="text-sm text-gray-500">{client.company || 'Individual'}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${client.status === 'active' ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                  <span className="text-xs text-gray-500 capitalize">{client.status}</span>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  <span className="truncate">{client.email}</span>
                </div>
                {client.phone && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="h-4 w-4 mr-2" />
                    <span>{client.phone}</span>
                  </div>
                )}
                {client.location && (
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span>{client.location}</span>
                  </div>
                )}
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>Connected: {new Date(client.connected_at).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-lg font-semibold text-gray-900">{client.project_count}</p>
                  <p className="text-xs text-gray-500">Projects</p>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-lg font-semibold text-gray-900">${client.total_spent.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">Total Spent</p>
                </div>
              </div>

              <div className="flex space-x-2">
                <Link href={`/designer/messages?client=${client.id}`} className="flex-1">
                  <Button size="sm" variant="outline" className="w-full">
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Message
                  </Button>
                </Link>
                <Link href={`/designer/projects?client=${client.id}`} className="flex-1">
                  <Button size="sm" variant="outline" className="w-full">
                    <FolderKanban className="h-4 w-4 mr-1" />
                    Projects
                  </Button>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
