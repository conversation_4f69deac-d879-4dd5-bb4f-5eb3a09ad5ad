"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  Calculator,
  DollarSign,
  Percent,
  Save,
  AlertCircle,
  CheckCircle,
  CreditCard,
  TrendingUp,
  Settings,
  Info,
  Edit,
  History
} from "lucide-react";

interface FeeStructure {
  id: string;
  name: string;
  description: string;
  platform_commission: number;
  payment_processing_fee: number;
  minimum_fee: number;
  maximum_fee: number;
  is_active: boolean;
  applies_to: string[];
  created_at: string;
  updated_at: string;
}

interface FeeCalculation {
  project_value: number;
  platform_commission: number;
  payment_processing: number;
  total_fees: number;
  designer_receives: number;
}

export default function FeeManagementPage() {
  const { user } = useAuth();
  const [feeStructures, setFeeStructures] = useState<FeeStructure[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'current' | 'calculator' | 'history'>('current');
  const [calculatorValue, setCalculatorValue] = useState<number>(10000);
  const [editingFee, setEditingFee] = useState<FeeStructure | null>(null);

  useEffect(() => {
    fetchFeeStructures();
  }, []);

  const fetchFeeStructures = async () => {
    try {
      // Mock fee structure data
      const mockFeeStructures: FeeStructure[] = [
        {
          id: "1",
          name: "Standard Commission",
          description: "Default commission structure for all projects",
          platform_commission: 15,
          payment_processing_fee: 2.9,
          minimum_fee: 50,
          maximum_fee: 5000,
          is_active: true,
          applies_to: ["residential", "commercial", "interior"],
          created_at: "2024-01-01",
          updated_at: "2024-01-15"
        },
        {
          id: "2",
          name: "Premium Projects",
          description: "Reduced commission for high-value projects",
          platform_commission: 12,
          payment_processing_fee: 2.9,
          minimum_fee: 100,
          maximum_fee: 10000,
          is_active: true,
          applies_to: ["luxury", "commercial"],
          created_at: "2024-01-01",
          updated_at: "2024-01-10"
        },
        {
          id: "3",
          name: "New Designer Incentive",
          description: "Reduced commission for new designers (first 3 months)",
          platform_commission: 10,
          payment_processing_fee: 2.9,
          minimum_fee: 25,
          maximum_fee: 2500,
          is_active: false,
          applies_to: ["new_designers"],
          created_at: "2024-01-01",
          updated_at: "2024-01-05"
        }
      ];

      setFeeStructures(mockFeeStructures);
    } catch (error) {
      console.error('Error fetching fee structures:', error);
      setError('Failed to load fee structures');
    } finally {
      setLoading(false);
    }
  };

  const calculateFees = (projectValue: number, feeStructure: FeeStructure): FeeCalculation => {
    const platformCommission = (projectValue * feeStructure.platform_commission) / 100;
    const paymentProcessing = (projectValue * feeStructure.payment_processing_fee) / 100;
    
    // Apply minimum and maximum fee limits
    const adjustedCommission = Math.max(
      feeStructure.minimum_fee,
      Math.min(platformCommission, feeStructure.maximum_fee)
    );
    
    const totalFees = adjustedCommission + paymentProcessing;
    const designerReceives = projectValue - totalFees;

    return {
      project_value: projectValue,
      platform_commission: adjustedCommission,
      payment_processing: paymentProcessing,
      total_fees: totalFees,
      designer_receives: designerReceives
    };
  };

  const handleUpdateFeeStructure = async (feeStructure: FeeStructure) => {
    setSaving(true);
    setError(null);

    try {
      // In a real implementation, this would update the database
      setFeeStructures(prev => 
        prev.map(fee => 
          fee.id === feeStructure.id 
            ? { ...feeStructure, updated_at: new Date().toISOString() }
            : fee
        )
      );
      setSuccess('Fee structure updated successfully');
      setEditingFee(null);
    } catch (error) {
      console.error('Error updating fee structure:', error);
      setError('Failed to update fee structure');
    } finally {
      setSaving(false);
    }
  };

  const toggleFeeStructureStatus = async (feeId: string) => {
    const feeStructure = feeStructures.find(f => f.id === feeId);
    if (!feeStructure) return;

    const updatedStructure = {
      ...feeStructure,
      is_active: !feeStructure.is_active,
      updated_at: new Date().toISOString()
    };

    await handleUpdateFeeStructure(updatedStructure);
  };

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const activeFeeStructure = feeStructures.find(f => f.is_active) || feeStructures[0];

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Fee Management</h1>
        <p className="text-gray-600">Configure platform commission rates and payment processing fees</p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('current')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'current'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Current Fee Structures
            </button>
            <button
              onClick={() => setActiveTab('calculator')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'calculator'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Fee Calculator
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'history'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Change History
            </button>
          </nav>
        </div>
      </div>

      {/* Current Fee Structures Tab */}
      {activeTab === 'current' && (
        <div className="space-y-6">
          {feeStructures.map((feeStructure) => (
            <div key={feeStructure.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 mr-3">{feeStructure.name}</h3>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                        feeStructure.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {feeStructure.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-4">{feeStructure.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Percent className="h-5 w-5 text-blue-600 mr-2" />
                          <span className="text-sm font-medium text-blue-900">Platform Commission</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-900">{feeStructure.platform_commission}%</p>
                      </div>
                      
                      <div className="bg-green-50 p-4 rounded-lg">
                        <div className="flex items-center mb-2">
                          <CreditCard className="h-5 w-5 text-green-600 mr-2" />
                          <span className="text-sm font-medium text-green-900">Payment Processing</span>
                        </div>
                        <p className="text-2xl font-bold text-green-900">{feeStructure.payment_processing_fee}%</p>
                      </div>
                      
                      <div className="bg-orange-50 p-4 rounded-lg">
                        <div className="flex items-center mb-2">
                          <DollarSign className="h-5 w-5 text-orange-600 mr-2" />
                          <span className="text-sm font-medium text-orange-900">Minimum Fee</span>
                        </div>
                        <p className="text-2xl font-bold text-orange-900">${feeStructure.minimum_fee}</p>
                      </div>
                      
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <div className="flex items-center mb-2">
                          <TrendingUp className="h-5 w-5 text-purple-600 mr-2" />
                          <span className="text-sm font-medium text-purple-900">Maximum Fee</span>
                        </div>
                        <p className="text-2xl font-bold text-purple-900">${feeStructure.maximum_fee}</p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Applies to:</h4>
                      <div className="flex flex-wrap gap-2">
                        {feeStructure.applies_to.map((category) => (
                          <span
                            key={category}
                            className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full"
                          >
                            {category.replace('_', ' ').toUpperCase()}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingFee(feeStructure)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleFeeStructureStatus(feeStructure.id)}
                      className={feeStructure.is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
                    >
                      {feeStructure.is_active ? 'Deactivate' : 'Activate'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Fee Calculator Tab */}
      {activeTab === 'calculator' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Fee Calculator</h2>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="projectValue" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Value ($)
                </label>
                <input
                  type="number"
                  id="projectValue"
                  value={calculatorValue}
                  onChange={(e) => setCalculatorValue(parseFloat(e.target.value) || 0)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  placeholder="Enter project value"
                  min="0"
                  step="100"
                />
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center mb-2">
                  <Info className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-sm font-medium text-gray-700">Using: {activeFeeStructure?.name}</span>
                </div>
                <p className="text-xs text-gray-600">{activeFeeStructure?.description}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Fee Breakdown</h2>
            
            {activeFeeStructure && (
              <div className="space-y-4">
                {(() => {
                  const calculation = calculateFees(calculatorValue, activeFeeStructure);
                  return (
                    <>
                      <div className="flex justify-between items-center py-2 border-b">
                        <span className="text-gray-600">Project Value</span>
                        <span className="font-semibold">${calculation.project_value.toLocaleString()}</span>
                      </div>
                      
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600">Platform Commission ({activeFeeStructure.platform_commission}%)</span>
                        <span className="text-red-600">-${calculation.platform_commission.toLocaleString()}</span>
                      </div>
                      
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600">Payment Processing ({activeFeeStructure.payment_processing_fee}%)</span>
                        <span className="text-red-600">-${calculation.payment_processing.toLocaleString()}</span>
                      </div>
                      
                      <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
                        <span className="text-gray-900">Total Fees</span>
                        <span className="text-red-600">${calculation.total_fees.toLocaleString()}</span>
                      </div>
                      
                      <div className="flex justify-between items-center py-2 bg-green-50 px-4 rounded-lg">
                        <span className="text-green-900 font-semibold">Designer Receives</span>
                        <span className="text-green-900 font-bold text-xl">${calculation.designer_receives.toLocaleString()}</span>
                      </div>
                      
                      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">Fee Percentage Breakdown</h4>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-blue-800">Platform keeps:</span>
                            <span className="text-blue-800">{((calculation.total_fees / calculation.project_value) * 100).toFixed(2)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-blue-800">Designer gets:</span>
                            <span className="text-blue-800">{((calculation.designer_receives / calculation.project_value) * 100).toFixed(2)}%</span>
                          </div>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Change History Tab */}
      {activeTab === 'history' && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Fee Structure Change History</h2>
          </div>
          
          <div className="p-6">
            <div className="space-y-4">
              {[
                {
                  date: "2024-01-15",
                  action: "Updated Standard Commission",
                  details: "Reduced platform commission from 18% to 15%",
                  user: "Admin User"
                },
                {
                  date: "2024-01-10",
                  action: "Updated Premium Projects",
                  details: "Increased maximum fee from $8,000 to $10,000",
                  user: "Admin User"
                },
                {
                  date: "2024-01-05",
                  action: "Deactivated New Designer Incentive",
                  details: "Promotional period ended",
                  user: "Admin User"
                },
                {
                  date: "2024-01-01",
                  action: "Created Initial Fee Structures",
                  details: "Set up standard, premium, and incentive fee structures",
                  user: "System"
                }
              ].map((change, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-brown-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <History className="h-5 w-5 text-brown-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-gray-900">{change.action}</h4>
                      <span className="text-sm text-gray-500">{new Date(change.date).toLocaleDateString()}</span>
                    </div>
                    <p className="text-gray-600 text-sm mb-1">{change.details}</p>
                    <p className="text-xs text-gray-500">Changed by: {change.user}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Edit Fee Structure Modal */}
      {editingFee && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Edit Fee Structure</h2>
              <button
                onClick={() => setEditingFee(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <Settings className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label htmlFor="editName" className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    id="editName"
                    value={editingFee.name}
                    onChange={(e) => setEditingFee({ ...editingFee, name: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="editDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="editDescription"
                    value={editingFee.description}
                    onChange={(e) => setEditingFee({ ...editingFee, description: e.target.value })}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="editCommission" className="block text-sm font-medium text-gray-700 mb-1">
                      Platform Commission (%)
                    </label>
                    <input
                      type="number"
                      id="editCommission"
                      value={editingFee.platform_commission}
                      onChange={(e) => setEditingFee({ ...editingFee, platform_commission: parseFloat(e.target.value) })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      min="0"
                      max="50"
                      step="0.1"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="editProcessing" className="block text-sm font-medium text-gray-700 mb-1">
                      Payment Processing (%)
                    </label>
                    <input
                      type="number"
                      id="editProcessing"
                      value={editingFee.payment_processing_fee}
                      onChange={(e) => setEditingFee({ ...editingFee, payment_processing_fee: parseFloat(e.target.value) })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      min="0"
                      max="10"
                      step="0.1"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="editMinFee" className="block text-sm font-medium text-gray-700 mb-1">
                      Minimum Fee ($)
                    </label>
                    <input
                      type="number"
                      id="editMinFee"
                      value={editingFee.minimum_fee}
                      onChange={(e) => setEditingFee({ ...editingFee, minimum_fee: parseFloat(e.target.value) })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      min="0"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="editMaxFee" className="block text-sm font-medium text-gray-700 mb-1">
                      Maximum Fee ($)
                    </label>
                    <input
                      type="number"
                      id="editMaxFee"
                      value={editingFee.maximum_fee}
                      onChange={(e) => setEditingFee({ ...editingFee, maximum_fee: parseFloat(e.target.value) })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                      min="0"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setEditingFee(null)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleUpdateFeeStructure(editingFee)}
                disabled={saving}
                className="flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
