"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  CreditCard,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Calendar,
  User,
  FileText,
  RefreshCw,
  Filter,
  Play,
  Pause,
  Settings,
  TrendingUp,
  Clock,
  Users,
  Zap,
  AlertTriangle,
  Download,
  Eye
} from "lucide-react";

interface PayoutSummary {
  totalPending: number;
  totalAmount: number;
  designersReady: number;
  nextPayoutDate: string;
  lastPayoutDate: string;
  automaticEnabled: boolean;
  payoutSchedule: string;
}

interface DesignerPayout {
  designer_id: string;
  designer_name: string;
  designer_email: string;
  total_amount: number;
  item_count: number;
  stripe_account_status: string;
  last_payout_date: string | null;
  pending_items: PayoutItem[];
}

interface PayoutItem {
  id: string;
  amount: number;
  transaction_id: string;
  milestone_id: string;
  project_title: string;
  created_at: string;
}

export default function EnhancedPayoutsPage() {
  const { user } = useOptimizedAuth();
  const [summary, setSummary] = useState<PayoutSummary | null>(null);
  const [designers, setDesigners] = useState<DesignerPayout[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedDesigners, setSelectedDesigners] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (user) {
      fetchPayoutData();
    }
  }, [user]);

  const fetchPayoutData = async () => {
    setLoading(true);
    try {
      // Fetch payout summary
      const { data: summaryData, error: summaryError } = await supabase
        .from('designer_payout_queue')
        .select(`
          amount,
          created_at,
          designer:profiles!designer_id(id, full_name)
        `)
        .eq('status', 'pending');

      if (summaryError) throw summaryError;

      // Fetch platform settings
      const { data: settings } = await supabase
        .from('platform_settings')
        .select('*')
        .single();

      // Calculate summary
      const totalAmount = summaryData?.reduce((sum, item) => sum + item.amount, 0) || 0;
      const designersReady = new Set(summaryData?.map(item => item.designer_id)).size;

      // Calculate next payout date
      const now = new Date();
      let nextPayoutDate = new Date();
      
      if (settings?.payout_schedule === 'weekly') {
        const daysUntilPayout = (settings.payout_day_of_week - now.getDay() + 7) % 7;
        nextPayoutDate.setDate(now.getDate() + (daysUntilPayout || 7));
      } else if (settings?.payout_schedule === 'monthly') {
        nextPayoutDate.setMonth(now.getMonth() + 1);
        nextPayoutDate.setDate(settings.payout_day_of_month || 1);
      } else {
        nextPayoutDate.setDate(now.getDate() + 1);
      }

      setSummary({
        totalPending: summaryData?.length || 0,
        totalAmount,
        designersReady,
        nextPayoutDate: nextPayoutDate.toISOString(),
        lastPayoutDate: '', // TODO: Get from last payout batch
        automaticEnabled: settings?.enable_automatic_payouts || false,
        payoutSchedule: settings?.payout_schedule || 'weekly'
      });

      // Fetch detailed designer data
      const { data: designerData, error: designerError } = await supabase
        .from('designer_payout_queue')
        .select(`
          designer_id,
          amount,
          id,
          transaction_id,
          milestone_id,
          created_at,
          designer:profiles!designer_id(id, full_name, email),
          stripe_account:designer_stripe_accounts!designer_id(stripe_account_id, account_status),
          transaction:transactions!transaction_id(project_id),
          project:transactions!transaction_id(projects!project_id(title))
        `)
        .eq('status', 'pending');

      if (designerError) throw designerError;

      // Group by designer
      const designerMap = new Map<string, DesignerPayout>();
      
      designerData?.forEach(item => {
        const designerId = item.designer_id;
        
        if (!designerMap.has(designerId)) {
          designerMap.set(designerId, {
            designer_id: designerId,
            designer_name: item.designer?.full_name || 'Unknown',
            designer_email: item.designer?.email || '',
            total_amount: 0,
            item_count: 0,
            stripe_account_status: item.stripe_account?.account_status || 'none',
            last_payout_date: null,
            pending_items: []
          });
        }

        const designer = designerMap.get(designerId)!;
        designer.total_amount += item.amount;
        designer.item_count += 1;
        designer.pending_items.push({
          id: item.id,
          amount: item.amount,
          transaction_id: item.transaction_id,
          milestone_id: item.milestone_id,
          project_title: item.project?.title || 'Unknown Project',
          created_at: item.created_at
        });
      });

      setDesigners(Array.from(designerMap.values()));

    } catch (error: any) {
      console.error('Error fetching payout data:', error);
      setError(error.message || 'Failed to fetch payout data');
    } finally {
      setLoading(false);
    }
  };

  const processAutomaticPayouts = async () => {
    setProcessing(true);
    setError(null);
    
    try {
      const { data, error } = await supabase.functions.invoke('process-automatic-payouts', {
        body: { force: true }
      });

      if (error) throw error;

      setSuccess(`Processed ${data.summary.successful} payouts totaling $${data.summary.totalAmount.toFixed(2)}`);
      await fetchPayoutData();
      
    } catch (error: any) {
      console.error('Error processing automatic payouts:', error);
      setError(error.message || 'Failed to process payouts');
    } finally {
      setProcessing(false);
    }
  };

  const processSelectedPayouts = async () => {
    if (selectedDesigners.size === 0) {
      setError('Please select at least one designer');
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      const results = [];
      
      for (const designerId of selectedDesigners) {
        const { data, error } = await supabase.functions.invoke('process-automatic-payouts', {
          body: { designerId, force: true }
        });

        if (error) {
          results.push({ designerId, status: 'failed', error: error.message });
        } else {
          results.push({ designerId, status: 'success', data });
        }
      }

      const successful = results.filter(r => r.status === 'success').length;
      const failed = results.filter(r => r.status === 'failed').length;

      setSuccess(`Processed ${successful} successful payouts${failed > 0 ? `, ${failed} failed` : ''}`);
      setSelectedDesigners(new Set());
      await fetchPayoutData();

    } catch (error: any) {
      console.error('Error processing selected payouts:', error);
      setError(error.message || 'Failed to process selected payouts');
    } finally {
      setProcessing(false);
    }
  };

  const toggleDesignerSelection = (designerId: string) => {
    const newSelection = new Set(selectedDesigners);
    if (newSelection.has(designerId)) {
      newSelection.delete(designerId);
    } else {
      newSelection.add(designerId);
    }
    setSelectedDesigners(newSelection);
  };

  const selectAllDesigners = () => {
    if (selectedDesigners.size === designers.length) {
      setSelectedDesigners(new Set());
    } else {
      setSelectedDesigners(new Set(designers.map(d => d.designer_id)));
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'pending': return 'text-yellow-600 bg-yellow-50';
      case 'restricted': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/admin/finance" className="mr-4">
            <Button variant="ghost" className="p-0 h-auto">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Enhanced Payout Management</h1>
            <p className="text-gray-500">Automated designer payouts with Stripe Connect</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={fetchPayoutData}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link href="/admin/finance/payouts/settings">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center">
              <div className="p-2 bg-blue-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Pending</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.totalAmount)}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center">
              <div className="p-2 bg-green-50 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Designers Ready</p>
                <p className="text-2xl font-bold text-gray-900">{summary.designersReady}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center">
              <div className="p-2 bg-purple-50 rounded-lg">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Next Payout</p>
                <p className="text-lg font-bold text-gray-900">
                  {new Date(summary.nextPayoutDate).toLocaleDateString()}
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${summary.automaticEnabled ? 'bg-green-50' : 'bg-red-50'}`}>
                <Zap className={`h-6 w-6 ${summary.automaticEnabled ? 'text-green-600' : 'text-red-600'}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Auto Payouts</p>
                <p className="text-lg font-bold text-gray-900">
                  {summary.automaticEnabled ? 'Enabled' : 'Disabled'}
                </p>
                <p className="text-xs text-gray-500 capitalize">{summary.payoutSchedule}</p>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold mb-2">Payout Actions</h3>
            <p className="text-gray-600">Process payouts manually or run automatic processing</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              onClick={processAutomaticPayouts}
              disabled={processing || designers.length === 0}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {processing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Process All Payouts
                </>
              )}
            </Button>
            
            <Button
              onClick={processSelectedPayouts}
              disabled={processing || selectedDesigners.size === 0}
              variant="outline"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Process Selected ({selectedDesigners.size})
            </Button>
          </div>
        </div>
      </div>

      {/* Designers List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Designers Ready for Payout</h3>
            <Button
              onClick={selectAllDesigners}
              variant="outline"
              size="sm"
            >
              {selectedDesigners.size === designers.length ? 'Deselect All' : 'Select All'}
            </Button>
          </div>
        </div>

        {designers.length === 0 ? (
          <div className="p-12 text-center">
            <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Payouts</h3>
            <p className="text-gray-500">All designers have been paid or no payouts are ready for processing.</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {designers.map((designer) => (
              <motion.div
                key={designer.designer_id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="p-6 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedDesigners.has(designer.designer_id)}
                      onChange={() => toggleDesignerSelection(designer.designer_id)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{designer.designer_name}</h4>
                      <p className="text-sm text-gray-500">{designer.designer_email}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-sm text-gray-600">
                          {designer.item_count} item{designer.item_count !== 1 ? 's' : ''}
                        </span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(designer.stripe_account_status)}`}>
                          {designer.stripe_account_status}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(designer.total_amount)}</p>
                    <p className="text-sm text-gray-500">Ready for payout</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
