import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { QualityWorkflowManager } from '@/lib/quality-workflow';

/**
 * API endpoint to trigger quality review when designer submits work
 * Integrates designer submissions with quality review workflow
 */

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { 
      submissionId, 
      projectId, 
      milestoneId, 
      reviewType = 'milestone',
      priority = 'medium',
      autoTrigger = true 
    } = body;

    // Validate required fields
    if (!submissionId || !projectId) {
      return NextResponse.json(
        { error: 'Submission ID and Project ID are required' },
        { status: 400 }
      );
    }

    // Verify the submission exists and belongs to the user
    const { data: submission, error: submissionError } = await supabase
      .from('project_submissions')
      .select(`
        id,
        project_id,
        designer_id,
        submission_type,
        status,
        projects:project_id (
          title,
          status,
          client_id
        )
      `)
      .eq('id', submissionId)
      .eq('designer_id', user.id)
      .single();

    if (submissionError || !submission) {
      return NextResponse.json(
        { error: 'Submission not found or access denied' },
        { status: 404 }
      );
    }

    // Check if quality review already exists for this submission
    const { data: existingReview } = await supabase
      .from('quality_reviews_new')
      .select('id, status')
      .eq('submission_id', submissionId)
      .single();

    if (existingReview) {
      return NextResponse.json({
        success: true,
        message: 'Quality review already exists',
        reviewId: existingReview.id,
        status: existingReview.status
      });
    }

    // Determine review type based on submission type and milestone
    let determinedReviewType = reviewType;
    if (submission.submission_type === 'final_deliverable') {
      determinedReviewType = 'final';
    } else if (submission.submission_type === 'revision') {
      determinedReviewType = 'revision';
    }

    // Determine priority based on project status and milestone
    let determinedPriority = priority;
    if (milestoneId) {
      // Check if this is the final milestone
      const { data: milestones } = await supabase
        .from('project_milestones')
        .select('id, milestone_order, is_final')
        .eq('project_id', projectId)
        .order('milestone_order', { ascending: false });

      if (milestones && milestones.length > 0) {
        const currentMilestone = milestones.find(m => m.id === milestoneId);
        if (currentMilestone?.is_final) {
          determinedPriority = 'high';
          determinedReviewType = 'final';
        }
      }
    }

    // Create quality review
    const reviewResult = await QualityWorkflowManager.createQualityReview({
      projectId,
      submissionId,
      milestoneId,
      designerId: user.id,
      reviewType: determinedReviewType,
      priority: determinedPriority
    });

    if (!reviewResult.success) {
      return NextResponse.json(
        { error: reviewResult.error || 'Failed to create quality review' },
        { status: 500 }
      );
    }

    // Update submission status to indicate it's under review
    await supabase
      .from('project_submissions')
      .update({
        status: 'under_quality_review',
        quality_review_id: reviewResult.reviewId
      })
      .eq('id', submissionId);

    // Get the created review details
    const { data: reviewDetails } = await supabase
      .from('quality_reviews_new')
      .select(`
        id,
        status,
        priority,
        sla_deadline,
        reviewer_id,
        reviewer:reviewer_id (
          full_name,
          email
        )
      `)
      .eq('id', reviewResult.reviewId)
      .single();

    // Create activity log
    await supabase
      .from('project_activities')
      .insert({
        project_id: projectId,
        user_id: user.id,
        activity_type: 'quality_review_initiated',
        description: `Quality review initiated for ${submission.submission_type}`,
        metadata: {
          submission_id: submissionId,
          review_id: reviewResult.reviewId,
          review_type: determinedReviewType,
          priority: determinedPriority
        }
      });

    // Send notification to client about quality review
    if (submission.projects?.client_id) {
      await supabase
        .from('workflow_notifications')
        .insert({
          recipient_id: submission.projects.client_id,
          notification_type: 'quality_review_started',
          title: 'Quality Review in Progress',
          message: `Your project "${submission.projects.title}" submission is being reviewed for quality`,
          priority: 'medium',
          metadata: {
            project_id: projectId,
            submission_id: submissionId,
            review_id: reviewResult.reviewId
          }
        });
    }

    return NextResponse.json({
      success: true,
      message: 'Quality review created successfully',
      reviewId: reviewResult.reviewId,
      review: reviewDetails,
      estimatedCompletionTime: reviewDetails?.sla_deadline
    }, { status: 201 });

  } catch (error) {
    console.error('Error in quality review API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check quality review status for a submission
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const submissionId = searchParams.get('submissionId');
    const projectId = searchParams.get('projectId');

    if (!submissionId && !projectId) {
      return NextResponse.json(
        { error: 'Either submissionId or projectId is required' },
        { status: 400 }
      );
    }

    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    let query = supabase
      .from('quality_reviews_new')
      .select(`
        id,
        project_id,
        submission_id,
        milestone_id,
        status,
        priority,
        review_type,
        sla_deadline,
        overall_score,
        feedback,
        revision_count,
        created_at,
        assigned_at,
        reviewed_at,
        reviewer:reviewer_id (
          full_name,
          email
        ),
        projects:project_id (
          title
        )
      `);

    if (submissionId) {
      query = query.eq('submission_id', submissionId);
    } else if (projectId) {
      query = query.eq('project_id', projectId);
    }

    const { data: reviews, error } = await query.order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: 'Failed to fetch reviews' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      reviews: reviews || []
    });

  } catch (error) {
    console.error('Error fetching quality reviews:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
