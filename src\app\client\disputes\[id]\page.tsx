'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { DisputeDetail } from '@/components/disputes/DisputeDetail';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from 'lucide-react';

interface ClientDisputePageProps {
  params: {
    id: string;
  };
}

export default function ClientDisputePage({ params }: ClientDisputePageProps) {
  const { id } = params;
  const { user, loading } = useOptimizedAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  if (loading || !user) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-64">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href="/client/disputes">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to My Disputes
          </Link>
        </Button>
      </div>

      <DisputeDetail disputeId={id} userRole="client" />
    </div>
  );
}
