const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTables() {
  console.log('Creating enhanced designer features tables...');

  // Create project_briefs table
  try {
    console.log('Creating project_briefs table...');
    const { error: briefsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS project_briefs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          requirements TEXT,
          preferred_style TEXT,
          budget_range TEXT CHECK (budget_range IN ('under_5k', '5k_10k', '10k_25k', '25k_plus', 'flexible')),
          timeline_preference TEXT,
          location TEXT,
          project_type TEXT,
          urgency TEXT DEFAULT 'medium' CHECK (urgency IN ('low', 'medium', 'high', 'urgent')),
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'assigned', 'proposal_received', 'accepted', 'rejected')),
          assigned_designer_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
          assigned_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
          assigned_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });
    if (briefsError) console.error('Error creating project_briefs:', briefsError);
    else console.log('✓ project_briefs table created');
  } catch (err) {
    console.error('Error creating project_briefs:', err);
  }

  // Create designer_availability table
  try {
    console.log('Creating designer_availability table...');
    const { error: availabilityError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS designer_availability (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          designer_id UUID UNIQUE NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          status TEXT DEFAULT 'available' CHECK (status IN ('available', 'busy', 'offline')),
          custom_message TEXT,
          auto_accept_briefs BOOLEAN DEFAULT FALSE,
          max_concurrent_projects INTEGER DEFAULT 5,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });
    if (availabilityError) console.error('Error creating designer_availability:', availabilityError);
    else console.log('✓ designer_availability table created');
  } catch (err) {
    console.error('Error creating designer_availability:', err);
  }

  // Create project_reviews table
  try {
    console.log('Creating project_reviews table...');
    const { error: reviewsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS project_reviews (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
          client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
          review_text TEXT,
          communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
          quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
          timeliness_rating INTEGER CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
          would_recommend BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(project_id, client_id)
        );
      `
    });
    if (reviewsError) console.error('Error creating project_reviews:', reviewsError);
    else console.log('✓ project_reviews table created');
  } catch (err) {
    console.error('Error creating project_reviews:', err);
  }

  // Create admin_messages table
  try {
    console.log('Creating admin_messages table...');
    const { error: messagesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS admin_messages (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          recipient_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
          recipient_role TEXT CHECK (recipient_role IN ('designer', 'client', 'all')),
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          message_type TEXT DEFAULT 'info' CHECK (message_type IN ('info', 'warning', 'success', 'urgent', 'announcement')),
          priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
          read_at TIMESTAMP WITH TIME ZONE,
          action_required BOOLEAN DEFAULT FALSE,
          action_url TEXT,
          expires_at TIMESTAMP WITH TIME ZONE,
          created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });
    if (messagesError) console.error('Error creating admin_messages:', messagesError);
    else console.log('✓ admin_messages table created');
  } catch (err) {
    console.error('Error creating admin_messages:', err);
  }

  // Create portfolio_images table
  try {
    console.log('Creating portfolio_images table...');
    const { error: imagesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS portfolio_images (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          portfolio_project_id UUID NOT NULL REFERENCES portfolio_projects(id) ON DELETE CASCADE,
          image_url TEXT NOT NULL,
          image_name TEXT,
          is_cover BOOLEAN DEFAULT FALSE,
          order_index INTEGER DEFAULT 0,
          alt_text TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });
    if (imagesError) console.error('Error creating portfolio_images:', imagesError);
    else console.log('✓ portfolio_images table created');
  } catch (err) {
    console.error('Error creating portfolio_images:', err);
  }

  console.log('Enhanced designer features tables creation completed!');
}

createTables();
