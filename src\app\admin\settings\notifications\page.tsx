"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/Button";
import {
  Bell,
  Save,
  AlertCircle,
  CheckCircle,
  Mail,
  MessageSquare,
  Users,
  CreditCard,
  FolderKanban,
  Settings,
  Eye,
  Edit,
  Send,
  Clock,
  Target
} from "lucide-react";

interface NotificationTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  body: string;
  type: 'email' | 'in_app' | 'both';
  trigger: string;
  enabled: boolean;
  variables: string[];
  created_at: string;
  updated_at: string;
}

interface NotificationSettings {
  // Email Notifications
  new_user_registration: boolean;
  designer_application_submitted: boolean;
  designer_application_approved: boolean;
  designer_application_rejected: boolean;
  project_created: boolean;
  project_assigned: boolean;
  project_completed: boolean;
  milestone_completed: boolean;
  payment_received: boolean;
  payment_failed: boolean;
  dispute_created: boolean;
  message_received: boolean;
  
  // In-App Notifications
  real_time_messages: boolean;
  project_updates: boolean;
  payment_notifications: boolean;
  system_alerts: boolean;
  
  // Admin Notifications
  admin_new_user: boolean;
  admin_new_project: boolean;
  admin_payment_issues: boolean;
  admin_disputes: boolean;
  admin_system_errors: boolean;
  
  // Frequency Settings
  digest_frequency: 'immediate' | 'daily' | 'weekly' | 'never';
  quiet_hours_start: string;
  quiet_hours_end: string;
  weekend_notifications: boolean;
}

export default function NotificationSettingsPage() {
  const { user } = useOptimizedAuth();
  const [settings, setSettings] = useState<NotificationSettings>({
    // Email Notifications
    new_user_registration: true,
    designer_application_submitted: true,
    designer_application_approved: true,
    designer_application_rejected: true,
    project_created: true,
    project_assigned: true,
    project_completed: true,
    milestone_completed: true,
    payment_received: true,
    payment_failed: true,
    dispute_created: true,
    message_received: false,
    
    // In-App Notifications
    real_time_messages: true,
    project_updates: true,
    payment_notifications: true,
    system_alerts: true,
    
    // Admin Notifications
    admin_new_user: true,
    admin_new_project: true,
    admin_payment_issues: true,
    admin_disputes: true,
    admin_system_errors: true,
    
    // Frequency Settings
    digest_frequency: 'daily',
    quiet_hours_start: '22:00',
    quiet_hours_end: '08:00',
    weekend_notifications: false
  });

  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'settings' | 'templates' | 'test'>('settings');
  const [editingTemplate, setEditingTemplate] = useState<NotificationTemplate | null>(null);
  const [testEmail, setTestEmail] = useState('');

  useEffect(() => {
    fetchNotificationTemplates();
    setLoading(false);
  }, []);

  const fetchNotificationTemplates = async () => {
    try {
      // Mock notification templates
      const mockTemplates: NotificationTemplate[] = [
        {
          id: "1",
          name: "Welcome Email",
          description: "Sent to new users after registration",
          subject: "Welcome to {{site_name}}!",
          body: "Hi {{user_name}},\n\nWelcome to {{site_name}}! We're excited to have you join our community of architects and clients.\n\nBest regards,\nThe {{site_name}} Team",
          type: "email",
          trigger: "user_registration",
          enabled: true,
          variables: ["site_name", "user_name"],
          created_at: "2024-01-01",
          updated_at: "2024-01-15"
        },
        {
          id: "2",
          name: "Designer Application Approved",
          description: "Sent when a designer application is approved",
          subject: "Your designer application has been approved!",
          body: "Congratulations {{designer_name}}!\n\nYour application to become a designer on {{site_name}} has been approved. You can now start accepting projects and building your portfolio.\n\nGet started: {{dashboard_url}}\n\nBest regards,\nThe {{site_name}} Team",
          type: "email",
          trigger: "designer_approved",
          enabled: true,
          variables: ["designer_name", "site_name", "dashboard_url"],
          created_at: "2024-01-01",
          updated_at: "2024-01-10"
        },
        {
          id: "3",
          name: "Project Assignment",
          description: "Sent when a project is assigned to a designer",
          subject: "New project assigned: {{project_title}}",
          body: "Hi {{designer_name}},\n\nYou have been assigned a new project:\n\nProject: {{project_title}}\nClient: {{client_name}}\nBudget: ${{project_budget}}\nDeadline: {{project_deadline}}\n\nView project details: {{project_url}}\n\nBest regards,\nThe {{site_name}} Team",
          type: "both",
          trigger: "project_assigned",
          enabled: true,
          variables: ["designer_name", "project_title", "client_name", "project_budget", "project_deadline", "project_url", "site_name"],
          created_at: "2024-01-01",
          updated_at: "2024-01-08"
        },
        {
          id: "4",
          name: "Payment Received",
          description: "Sent when a payment is successfully processed",
          subject: "Payment received for {{project_title}}",
          body: "Hi {{designer_name}},\n\nWe've received a payment for your project:\n\nProject: {{project_title}}\nAmount: ${{payment_amount}}\nMilestone: {{milestone_name}}\n\nThe payment will be processed according to your payout schedule.\n\nView details: {{payment_url}}\n\nBest regards,\nThe {{site_name}} Team",
          type: "email",
          trigger: "payment_received",
          enabled: true,
          variables: ["designer_name", "project_title", "payment_amount", "milestone_name", "payment_url", "site_name"],
          created_at: "2024-01-01",
          updated_at: "2024-01-05"
        }
      ];

      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error fetching notification templates:', error);
      setError('Failed to load notification templates');
    }
  };

  const handleSettingChange = (setting: keyof NotificationSettings, value: any) => {
    setSettings(prev => ({ ...prev, [setting]: value }));
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // In a real implementation, this would save to database
      setSuccess('Notification settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSaveTemplate = async (template: NotificationTemplate) => {
    setSaving(true);
    setError(null);

    try {
      setTemplates(prev => 
        prev.map(t => 
          t.id === template.id 
            ? { ...template, updated_at: new Date().toISOString() }
            : t
        )
      );
      setSuccess('Template updated successfully');
      setEditingTemplate(null);
    } catch (error) {
      console.error('Error saving template:', error);
      setError('Failed to save template');
    } finally {
      setSaving(false);
    }
  };

  const sendTestNotification = async () => {
    if (!testEmail) {
      setError('Please enter an email address');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      // In a real implementation, this would send a test email
      setSuccess(`Test notification sent to ${testEmail}`);
      setTestEmail('');
    } catch (error) {
      setError('Failed to send test notification');
    } finally {
      setSaving(false);
    }
  };

  const notificationCategories = [
    {
      title: "User Management",
      icon: Users,
      settings: [
        { key: 'new_user_registration', label: 'New User Registration', description: 'When a new user creates an account' },
        { key: 'designer_application_submitted', label: 'Designer Application Submitted', description: 'When a designer submits an application' },
        { key: 'designer_application_approved', label: 'Designer Application Approved', description: 'When a designer application is approved' },
        { key: 'designer_application_rejected', label: 'Designer Application Rejected', description: 'When a designer application is rejected' }
      ]
    },
    {
      title: "Project Management",
      icon: FolderKanban,
      settings: [
        { key: 'project_created', label: 'Project Created', description: 'When a new project is created' },
        { key: 'project_assigned', label: 'Project Assigned', description: 'When a project is assigned to a designer' },
        { key: 'project_completed', label: 'Project Completed', description: 'When a project is marked as completed' },
        { key: 'milestone_completed', label: 'Milestone Completed', description: 'When a project milestone is completed' }
      ]
    },
    {
      title: "Financial",
      icon: CreditCard,
      settings: [
        { key: 'payment_received', label: 'Payment Received', description: 'When a payment is successfully processed' },
        { key: 'payment_failed', label: 'Payment Failed', description: 'When a payment fails to process' }
      ]
    },
    {
      title: "Communication",
      icon: MessageSquare,
      settings: [
        { key: 'message_received', label: 'Message Received', description: 'When a new message is received' },
        { key: 'dispute_created', label: 'Dispute Created', description: 'When a dispute is created' }
      ]
    }
  ];

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Notification Settings</h1>
          <p className="text-gray-600">Configure email and in-app notification preferences</p>
        </div>
        <Button
          onClick={handleSaveSettings}
          disabled={saving}
          className="flex items-center"
        >
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('settings')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'settings'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Notification Settings
            </button>
            <button
              onClick={() => setActiveTab('templates')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'templates'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Email Templates
            </button>
            <button
              onClick={() => setActiveTab('test')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'test'
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Test Notifications
            </button>
          </nav>
        </div>
      </div>

      {/* Notification Settings Tab */}
      {activeTab === 'settings' && (
        <div className="space-y-8">
          {/* Email Notification Categories */}
          {notificationCategories.map((category) => (
            <div key={category.title} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center">
                  <category.icon className="h-5 w-5 text-brown-600 mr-3" />
                  <h2 className="text-lg font-semibold text-gray-900">{category.title}</h2>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {category.settings.map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{setting.label}</h3>
                        <p className="text-sm text-gray-600">{setting.description}</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings[setting.key as keyof NotificationSettings] as boolean}
                          onChange={(e) => handleSettingChange(setting.key as keyof NotificationSettings, e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brown-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brown-600"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}

          {/* General Settings */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center">
                <Settings className="h-5 w-5 text-brown-600 mr-3" />
                <h2 className="text-lg font-semibold text-gray-900">General Settings</h2>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Digest Frequency</label>
                  <select
                    value={settings.digest_frequency}
                    onChange={(e) => handleSettingChange('digest_frequency', e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  >
                    <option value="immediate">Immediate</option>
                    <option value="daily">Daily Digest</option>
                    <option value="weekly">Weekly Digest</option>
                    <option value="never">Never</option>
                  </select>
                </div>
                
                <div className="flex items-center">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.weekend_notifications}
                      onChange={(e) => handleSettingChange('weekend_notifications', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Send notifications on weekends</span>
                  </label>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quiet Hours Start</label>
                  <input
                    type="time"
                    value={settings.quiet_hours_start}
                    onChange={(e) => handleSettingChange('quiet_hours_start', e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quiet Hours End</label>
                  <input
                    type="time"
                    value={settings.quiet_hours_end}
                    onChange={(e) => handleSettingChange('quiet_hours_end', e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Email Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-4">
          {templates.map((template) => (
            <div key={template.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 mr-3">{template.name}</h3>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                        template.enabled 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {template.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                      <span className="ml-2 px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                        {template.type.toUpperCase()}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3">{template.description}</p>
                    
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Subject:</h4>
                      <p className="text-sm text-gray-900 mb-3">{template.subject}</p>
                      
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Body Preview:</h4>
                      <p className="text-sm text-gray-900 line-clamp-3">{template.body}</p>
                    </div>
                    
                    <div className="mt-3">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Available Variables:</h4>
                      <div className="flex flex-wrap gap-2">
                        {template.variables.map((variable) => (
                          <span
                            key={variable}
                            className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded"
                          >
                            {`{{${variable}}}`}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingTemplate(template)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Test Notifications Tab */}
      {activeTab === 'test' && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Test Notifications</h2>
          </div>
          <div className="p-6">
            <div className="max-w-md">
              <div className="space-y-4">
                <div>
                  <label htmlFor="testEmail" className="block text-sm font-medium text-gray-700 mb-1">
                    Test Email Address
                  </label>
                  <input
                    type="email"
                    id="testEmail"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    placeholder="Enter email address"
                  />
                </div>
                
                <Button
                  onClick={sendTestNotification}
                  disabled={saving || !testEmail}
                  className="flex items-center"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {saving ? 'Sending...' : 'Send Test Email'}
                </Button>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-start">
                  <Bell className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900">Test Email Content</h4>
                    <p className="text-sm text-blue-800 mt-1">
                      The test email will use the "Welcome Email" template with sample data to verify your email configuration is working correctly.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Template Modal */}
      {editingTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Edit Template: {editingTemplate.name}</h2>
              <button
                onClick={() => setEditingTemplate(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <Settings className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label htmlFor="templateSubject" className="block text-sm font-medium text-gray-700 mb-1">
                    Subject Line
                  </label>
                  <input
                    type="text"
                    id="templateSubject"
                    value={editingTemplate.subject}
                    onChange={(e) => setEditingTemplate({ ...editingTemplate, subject: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="templateBody" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Body
                  </label>
                  <textarea
                    id="templateBody"
                    value={editingTemplate.body}
                    onChange={(e) => setEditingTemplate({ ...editingTemplate, body: e.target.value })}
                    rows={12}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500 font-mono text-sm"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Notification Type</label>
                    <select
                      value={editingTemplate.type}
                      onChange={(e) => setEditingTemplate({ ...editingTemplate, type: e.target.value as 'email' | 'in_app' | 'both' })}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-brown-500 focus:border-brown-500"
                    >
                      <option value="email">Email Only</option>
                      <option value="in_app">In-App Only</option>
                      <option value="both">Both Email & In-App</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={editingTemplate.enabled}
                        onChange={(e) => setEditingTemplate({ ...editingTemplate, enabled: e.target.checked })}
                        className="mr-2"
                      />
                      <span className="text-sm font-medium text-gray-700">Template Enabled</span>
                    </label>
                  </div>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Available Variables:</h4>
                  <div className="flex flex-wrap gap-2">
                    {editingTemplate.variables.map((variable) => (
                      <span
                        key={variable}
                        className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded cursor-pointer hover:bg-purple-200"
                        onClick={() => {
                          const textarea = document.getElementById('templateBody') as HTMLTextAreaElement;
                          if (textarea) {
                            const cursorPos = textarea.selectionStart;
                            const textBefore = textarea.value.substring(0, cursorPos);
                            const textAfter = textarea.value.substring(cursorPos);
                            const newValue = textBefore + `{{${variable}}}` + textAfter;
                            setEditingTemplate({ ...editingTemplate, body: newValue });
                          }
                        }}
                      >
                        {`{{${variable}}}`}
                      </span>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 mt-2">Click on a variable to insert it at the cursor position</p>
                </div>
              </div>
            </div>
            
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setEditingTemplate(null)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleSaveTemplate(editingTemplate)}
                disabled={saving}
                className="flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save Template'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
