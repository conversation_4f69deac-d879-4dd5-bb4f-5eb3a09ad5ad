-- Create tracking_requests table
CREATE TABLE IF NOT EXISTS tracking_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tracking_number TEXT NOT NULL UNIQUE,
  request_type TEXT NOT NULL CHECK (request_type IN ('sample_request', 'vision_builder')),
  status TEXT NOT NULL DEFAULT 'submitted' CHECK (status IN ('submitted', 'processing', 'completed', 'cancelled')),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  project_type TEXT,
  description TEXT,
  vision_prompt TEXT,
  service_category TEXT,
  sample_type TEXT,
  selected_style TEXT,
  file_path TEXT,
  file_name TEXT,
  file_type TEXT,
  file_size INTEGER,
  image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  linked_user_id UUID REFERENCES auth.users(id),
  linked_project_id UUID
);

-- Create index on tracking_number for faster lookups
CREATE INDEX IF NOT EXISTS idx_tracking_requests_tracking_number ON tracking_requests(tracking_number);

-- Create index on request_type for filtering
CREATE INDEX IF NOT EXISTS idx_tracking_requests_request_type ON tracking_requests(request_type);

-- Create index on status for filtering
CREATE INDEX IF NOT EXISTS idx_tracking_requests_status ON tracking_requests(status);

-- Create index on email for user lookups
CREATE INDEX IF NOT EXISTS idx_tracking_requests_email ON tracking_requests(email);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_tracking_requests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_tracking_requests_updated_at
BEFORE UPDATE ON tracking_requests
FOR EACH ROW
EXECUTE FUNCTION update_tracking_requests_updated_at();

-- Add RLS policies
ALTER TABLE tracking_requests ENABLE ROW LEVEL SECURITY;

-- Policy for admins to see all tracking requests
CREATE POLICY "Admins can see all tracking requests"
  ON tracking_requests
  FOR SELECT
  TO authenticated
  USING (auth.jwt() ->> 'role' = 'admin');

-- Policy for users to see their own tracking requests
CREATE POLICY "Users can see their own tracking requests"
  ON tracking_requests
  FOR SELECT
  TO authenticated
  USING (email = auth.jwt() ->> 'email' OR linked_user_id = auth.uid());

-- Policy for anonymous users to see tracking requests by tracking number
CREATE POLICY "Anonymous users can see tracking requests by tracking number"
  ON tracking_requests
  FOR SELECT
  TO anon
  USING (true);

-- Policy for admins to insert tracking requests
CREATE POLICY "Admins can insert tracking requests"
  ON tracking_requests
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.jwt() ->> 'role' = 'admin');

-- Policy for anonymous users to insert tracking requests
CREATE POLICY "Anonymous users can insert tracking requests"
  ON tracking_requests
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Policy for admins to update tracking requests
CREATE POLICY "Admins can update tracking requests"
  ON tracking_requests
  FOR UPDATE
  TO authenticated
  USING (auth.jwt() ->> 'role' = 'admin');

-- Policy for users to update their own tracking requests
CREATE POLICY "Users can update their own tracking requests"
  ON tracking_requests
  FOR UPDATE
  TO authenticated
  USING (email = auth.jwt() ->> 'email' OR linked_user_id = auth.uid());
