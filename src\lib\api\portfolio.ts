import { 
  PortfolioProject, 
  PortfolioImage,
  CreatePortfolioProjectParams,
  UpdatePortfolioProjectParams,
  CreatePortfolioImageParams,
  UpdatePortfolioImageParams
} from '@/types/portfolio';

/**
 * Get all portfolio projects for the authenticated user or a specified designer
 */
export async function getPortfolioProjects(token: string, designerId?: string, category?: string, featured?: boolean): Promise<PortfolioProject[]> {
  let url = '/api/portfolio';
  const params = new URLSearchParams();
  
  if (designerId) {
    params.append('designer_id', designerId);
  }
  
  if (category) {
    params.append('category', category);
  }
  
  if (featured) {
    params.append('featured', 'true');
  }
  
  if (params.toString()) {
    url += `?${params.toString()}`;
  }
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch portfolio projects');
  }
  
  return response.json();
}

/**
 * Get a specific portfolio project by ID
 */
export async function getPortfolioProject(token: string, projectId: string): Promise<PortfolioProject> {
  const response = await fetch(`/api/portfolio/${projectId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch portfolio project');
  }
  
  return response.json();
}

/**
 * Create a new portfolio project
 */
export async function createPortfolioProject(token: string, params: CreatePortfolioProjectParams): Promise<PortfolioProject> {
  const response = await fetch('/api/portfolio', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create portfolio project');
  }
  
  return response.json();
}

/**
 * Update a portfolio project
 */
export async function updatePortfolioProject(token: string, projectId: string, params: UpdatePortfolioProjectParams): Promise<PortfolioProject> {
  const response = await fetch(`/api/portfolio/${projectId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update portfolio project');
  }
  
  return response.json();
}

/**
 * Delete a portfolio project
 */
export async function deletePortfolioProject(token: string, projectId: string): Promise<void> {
  const response = await fetch(`/api/portfolio/${projectId}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to delete portfolio project');
  }
}

/**
 * Get all images for a portfolio project
 */
export async function getPortfolioImages(token: string, projectId: string): Promise<PortfolioImage[]> {
  const response = await fetch(`/api/portfolio/${projectId}/images`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch portfolio images');
  }
  
  return response.json();
}

/**
 * Add an image to a portfolio project
 */
export async function addPortfolioImage(token: string, params: CreatePortfolioImageParams): Promise<PortfolioImage> {
  const response = await fetch(`/api/portfolio/${params.project_id}/images`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to add portfolio image');
  }
  
  return response.json();
}

/**
 * Update a portfolio image
 */
export async function updatePortfolioImage(token: string, imageId: string, params: UpdatePortfolioImageParams): Promise<PortfolioImage> {
  const response = await fetch(`/api/portfolio/images/${imageId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update portfolio image');
  }
  
  return response.json();
}

/**
 * Delete a portfolio image
 */
export async function deletePortfolioImage(token: string, imageId: string): Promise<void> {
  const response = await fetch(`/api/portfolio/images/${imageId}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to delete portfolio image');
  }
}
