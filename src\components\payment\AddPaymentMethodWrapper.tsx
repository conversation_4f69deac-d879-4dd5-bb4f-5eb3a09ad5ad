"use client";

import { useState, useEffect } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { AddPaymentMethodForm } from './AddPaymentMethodForm';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string);

interface AddPaymentMethodWrapperProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function AddPaymentMethodWrapper({ onSuccess, onCancel }: AddPaymentMethodWrapperProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Create a SetupIntent as soon as the component loads
    const createSetupIntent = async () => {
      try {
        setLoading(true);

        // Get the current user and session
        const { data: { user } } = await supabase.auth.getUser();
        const { data: { session } } = await supabase.auth.getSession();

        if (!user || !session) {
          throw new Error('You must be logged in to add a payment method');
        }

        const response = await fetch('/api/create-setup-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({ userId: user.id }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create setup intent');
        }

        const data = await response.json();
        setClientSecret(data.clientSecret);
      } catch (err: any) {
        console.error('Error creating setup intent:', err);
        setError(err.message || 'Failed to initialize payment form');
      } finally {
        setLoading(false);
      }
    };

    createSetupIntent();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-gray-600">Loading payment form...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        <p className="font-medium">Error</p>
        <p className="text-sm">{error}</p>
        <button
          onClick={onCancel}
          className="mt-2 text-sm text-red-700 underline"
        >
          Go back
        </button>
      </div>
    );
  }

  if (!clientSecret) {
    return null;
  }

  return (
    <Elements
      stripe={stripePromise}
      options={{
        clientSecret,
        appearance: {
          theme: 'stripe',
          variables: {
            colorPrimary: '#0f766e',
            colorBackground: '#ffffff',
            colorText: '#1f2937',
            colorDanger: '#ef4444',
            fontFamily: 'ui-sans-serif, system-ui, sans-serif',
            spacingUnit: '4px',
            borderRadius: '4px',
          },
        },
      }}
    >
      <AddPaymentMethodForm onSuccess={onSuccess} onCancel={onCancel} />
    </Elements>
  );
}
