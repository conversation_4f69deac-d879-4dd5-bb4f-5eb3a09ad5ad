'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';

interface AvailabilityToggleProps {
  onAvailabilityChange?: (available: boolean) => void;
  showLabel?: boolean;
  className?: string;
}

// Query key for availability
const availabilityKeys = {
  availability: (userId: string) => ['availability', userId] as const,
};

export function AvailabilityToggle({
  onAvailabilityChange,
  showLabel = true,
  className = ''
}: AvailabilityToggleProps) {
  const { user, profile } = useOptimizedAuth();
  const queryClient = useQueryClient();
  const [optimisticAvailable, setOptimisticAvailable] = useState<boolean | null>(null);

  // React Query for availability data with proper caching
  const { data: availability, isLoading, error } = useQuery({
    queryKey: availabilityKeys.availability(user?.id || ''),
    queryFn: async () => {
      if (!user) throw new Error('No user');

      const { data, error } = await supabase
        .from('profiles')
        .select('availability')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data.availability ?? true;
    },
    enabled: !!user && profile?.role === 'designer',
    staleTime: 10 * 60 * 1000, // 10 minutes - availability doesn't change often
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false, // Prevent refetch on tab switch
    refetchOnMount: false, // Use cached data when available
    retry: 2,
    // Call callback when data changes
    onSuccess: (data) => {
      if (onAvailabilityChange) {
        onAvailabilityChange(data);
      }
    }
  });

  // Mutation for updating availability with optimistic updates
  const updateAvailabilityMutation = useMutation({
    mutationFn: async (newAvailability: boolean) => {
      if (!user) throw new Error('No user');

      const { error } = await supabase
        .from('profiles')
        .update({ availability: newAvailability })
        .eq('id', user.id);

      if (error) throw error;
      return newAvailability;
    },
    onMutate: async (newAvailability) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: availabilityKeys.availability(user?.id || '') });

      // Snapshot the previous value
      const previousAvailability = queryClient.getQueryData(availabilityKeys.availability(user?.id || ''));

      // Optimistically update to the new value
      queryClient.setQueryData(availabilityKeys.availability(user?.id || ''), newAvailability);
      setOptimisticAvailable(newAvailability);

      // Call callback immediately for responsive UI
      if (onAvailabilityChange) {
        onAvailabilityChange(newAvailability);
      }

      // Return a context object with the snapshotted value
      return { previousAvailability };
    },
    onError: (err, newAvailability, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(
        availabilityKeys.availability(user?.id || ''),
        context?.previousAvailability
      );
      setOptimisticAvailable(null);

      // Revert callback
      if (onAvailabilityChange && context?.previousAvailability !== undefined) {
        onAvailabilityChange(context.previousAvailability);
      }

      toast({
        title: 'Error',
        description: 'Failed to update availability',
        variant: 'destructive'
      });
      console.error('Error updating availability:', err);
    },
    onSuccess: (newAvailability) => {
      setOptimisticAvailable(null);
      toast({
        title: 'Success',
        description: `Availability updated: ${newAvailability ? 'Available' : 'Unavailable'}`,
        variant: 'success'
      });
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: availabilityKeys.availability(user?.id || '') });
    },
  });

  const handleToggleAvailability = () => {
    const currentAvailability = optimisticAvailable ?? availability ?? true;
    updateAvailabilityMutation.mutate(!currentAvailability);
  };

  // Use optimistic value if available, otherwise use query data
  const currentAvailability = optimisticAvailable ?? availability;
  const isUpdating = updateAvailabilityMutation.isPending;

  // Show loading only on initial load, not on tab switches
  if (isLoading && currentAvailability === undefined) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {showLabel && <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>}
        <div className="h-6 w-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  // Don't render if user is not a designer
  if (profile?.role !== 'designer') {
    return null;
  }

  // Use default value if no data yet
  const displayAvailability = currentAvailability ?? true;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showLabel && (
        <div className="flex items-center space-x-2">
          <Label htmlFor="availability-toggle">Availability:</Label>
          <Badge variant={displayAvailability ? 'success' : 'secondary'}>
            {displayAvailability ? 'Available' : 'Unavailable'}
          </Badge>
        </div>
      )}
      <Switch
        id="availability-toggle"
        checked={displayAvailability}
        onCheckedChange={handleToggleAvailability}
        disabled={isUpdating}
        aria-label="Toggle availability"
      />
      {isUpdating && (
        <div className="w-4 h-4 border-2 border-brown-500 border-t-transparent rounded-full animate-spin" />
      )}
    </div>
  );
}
