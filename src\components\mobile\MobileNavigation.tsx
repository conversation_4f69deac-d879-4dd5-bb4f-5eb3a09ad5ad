"use client";

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import {
  Menu,
  X,
  Home,
  FolderKanban,
  Users,
  MessageSquare,
  FileText,
  Settings,
  User,
  LogOut,
  Bell,
  Search,
  ChevronRight,
  Calendar,
  BarChart3,
  Briefcase
} from 'lucide-react';

interface MobileNavigationProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
}

interface NavItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  children?: NavItem[];
}

export function MobileNavigation({ isOpen, onToggle, onClose }: MobileNavigationProps) {
  const pathname = usePathname();
  const { user, profile, signOut } = useAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Close menu when route changes - with delay to prevent snap-back
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 100); // Small delay to prevent immediate closure during navigation

    return () => clearTimeout(timer);
  }, [pathname, onClose]);

  // Prevent body scroll when menu is open - with better cleanup
  useEffect(() => {
    const originalOverflow = document.body.style.overflow;

    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = originalOverflow || 'unset';
    }

    return () => {
      document.body.style.overflow = originalOverflow || 'unset';
    };
  }, [isOpen]);

  const navigationItems: NavItem[] = [
    {
      label: 'Dashboard',
      href: '/designer/dashboard',
      icon: Home,
    },
    {
      label: 'Projects',
      href: '/designer/projects',
      icon: FolderKanban,
      children: [
        { label: 'All Projects', href: '/designer/projects', icon: FolderKanban },
        { label: 'Active Projects', href: '/designer/projects?status=active', icon: Briefcase },
        { label: 'Completed', href: '/designer/projects?status=completed', icon: BarChart3 },
      ]
    },
    {
      label: 'Clients',
      href: '/designer/clients',
      icon: Users,
    },
    {
      label: 'Messages',
      href: '/designer/messages',
      icon: MessageSquare,
      badge: 3, // TODO: Get from real data
    },
    {
      label: 'Proposals',
      href: '/designer/proposals',
      icon: FileText,
    },
    {
      label: 'Availability',
      href: '/designer/availability',
      icon: Calendar,
    },
  ];

  const toggleExpanded = (label: string) => {
    setExpandedItems(prev => 
      prev.includes(label) 
        ? prev.filter(item => item !== label)
        : [...prev, label]
    );
  };

  const handleSignOut = async () => {
    await signOut();
    onClose();
  };

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onToggle();
            }}
            className="p-2 -ml-2 rounded-lg hover:bg-gray-100 transition-colors"
            aria-label="Toggle navigation"
          >
            <Menu className="h-6 w-6 text-gray-700" />
          </button>
          
          <div className="flex items-center space-x-3">
            <button
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              onClick={() => {
                // TODO: Implement search functionality
                console.log('Search clicked - functionality coming soon');
              }}
              title="Search (Coming Soon)"
            >
              <Search className="h-5 w-5 text-gray-400" />
            </button>
            <button
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative"
              onClick={() => {
                // TODO: Implement notifications
                console.log('Notifications clicked - functionality coming soon');
              }}
              title="Notifications (Coming Soon)"
            >
              <Bell className="h-5 w-5 text-gray-400" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-gray-400 rounded-full text-xs text-white flex items-center justify-center opacity-50">
                !
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
          />
        )}
      </AnimatePresence>

      {/* Slide-out Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{ type: 'tween', duration: 0.3, ease: 'easeOut' }}
            className="lg:hidden fixed top-0 left-0 bottom-0 z-50 w-80 max-w-[85vw] bg-white shadow-xl"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-brown-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">D</span>
                </div>
                <span className="font-semibold text-gray-900">Designer Portal</span>
              </div>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onClose();
                }}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <X className="h-5 w-5 text-gray-600" />
              </button>
            </div>

            {/* User Profile Section */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                  {profile?.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.full_name || 'User'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-5 w-5 text-gray-600" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {profile?.full_name || 'Designer'}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {user?.email}
                  </p>
                </div>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 overflow-y-auto py-2">
              {navigationItems.map((item) => (
                <div key={item.label}>
                  {item.children ? (
                    <div>
                      <button
                        onClick={() => toggleExpanded(item.label)}
                        className="w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <item.icon className="h-5 w-5 text-gray-600" />
                          <span className="text-sm font-medium text-gray-900">
                            {item.label}
                          </span>
                          {item.badge && (
                            <span className="bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </div>
                        <ChevronRight 
                          className={`h-4 w-4 text-gray-400 transition-transform ${
                            expandedItems.includes(item.label) ? 'rotate-90' : ''
                          }`}
                        />
                      </button>
                      <AnimatePresence>
                        {expandedItems.includes(item.label) && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="overflow-hidden bg-gray-50"
                          >
                            {item.children.map((child) => (
                              <Link
                                key={child.href}
                                href={child.href}
                                className={`flex items-center space-x-3 px-4 py-2 pl-12 text-sm hover:bg-gray-100 transition-colors ${
                                  pathname === child.href
                                    ? 'text-brown-600 bg-brown-50 border-r-2 border-brown-600'
                                    : 'text-gray-700'
                                }`}
                              >
                                <child.icon className="h-4 w-4" />
                                <span>{child.label}</span>
                              </Link>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={`flex items-center justify-between px-4 py-3 hover:bg-gray-50 transition-colors ${
                        pathname === item.href
                          ? 'text-brown-600 bg-brown-50 border-r-2 border-brown-600'
                          : 'text-gray-700'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <item.icon className="h-5 w-5" />
                        <span className="text-sm font-medium">{item.label}</span>
                      </div>
                      {item.badge && (
                        <span className="bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )}
                </div>
              ))}
            </div>

            {/* Footer Actions */}
            <div className="border-t border-gray-200 p-4 space-y-2">
              <Link
                href="/designer/profile"
                className="flex items-center space-x-3 px-2 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Settings className="h-5 w-5 text-gray-600" />
                <span className="text-sm text-gray-700">Settings</span>
              </Link>
              <button
                onClick={handleSignOut}
                className="w-full flex items-center space-x-3 px-2 py-2 rounded-lg hover:bg-gray-50 transition-colors text-left"
              >
                <LogOut className="h-5 w-5 text-gray-600" />
                <span className="text-sm text-gray-700">Sign Out</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
