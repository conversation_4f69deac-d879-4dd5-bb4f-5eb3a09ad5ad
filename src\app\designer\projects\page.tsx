"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useResponsive } from "@/components/mobile/ResponsiveLayout";
import { MobileCard, MobileListItem, MobileButton } from "@/components/mobile/MobileCard";
import {
  Search,
  Filter,
  Grid,
  List,
  FolderOpen,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  MoreVertical,
  Eye
} from "lucide-react";

type Project = {
  id: string;
  title: string;
  description: string;
  status: string;
  client_id: string;
  client_name: string;
  created_at: string;
  due_date: string | null;
  progress: number;
  thumbnail_url: string | null;
};

export default function DesignerProjects() {
  const { user } = useOptimizedAuth();
  const { isMobile, isTablet } = useResponsive();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">(isMobile ? "list" : "grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    if (user) {
      fetchProjects();
    }
  }, [user]);

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const { data, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          status,
          client_id,
          clients:profiles!client_id(full_name),
          created_at,
          due_date,
          progress,
          thumbnail_url
        `)
        .eq('designer_id', user?.id)
        .order('created_at', { ascending: false });

      if (projectsError) throw projectsError;

      setProjects(
        (data || []).map(project => ({
          id: project.id,
          title: project.title,
          description: project.description || "",
          status: project.status,
          client_id: project.client_id,
          client_name: project.clients?.[0]?.full_name || 'Unknown Client',
          created_at: project.created_at,
          due_date: project.due_date,
          progress: project.progress || 0,
          thumbnail_url: project.thumbnail_url || null
        }))
      );
    } catch (error: unknown) {
      console.error('Error fetching projects:', error);
      setError(error instanceof Error ? error.message : 'Failed to load projects');
    } finally {
      setLoading(false);
    }
  };
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No due date';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </span>
        );
      case 'active':
      case 'in_progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Clock className="h-3 w-3 mr-1" />
            In Progress
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </span>
        );
      case 'overdue':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertCircle className="h-3 w-3 mr-1" />
            Overdue
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  // Filter projects based on search query and status filter
  const filteredProjects = projects.filter(project => {
    const matchesSearch =
      project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.client_name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  if (loading && projects.length === 0) {
    return (
      <div className="space-y-4 lg:space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brown-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6">
      {/* Header - Mobile Optimized */}
      <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'}`}>
        <h1 className={`font-bold ${isMobile ? 'text-xl' : 'text-2xl'}`}>
          Projects ({filteredProjects.length})
        </h1>

        {/* View Mode Toggle - Hidden on Mobile */}
        {!isMobile && (
          <div className="flex items-center space-x-2">
            <MobileButton
              variant={viewMode === "grid" ? "primary" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid className="h-4 w-4" />
            </MobileButton>
            <MobileButton
              variant={viewMode === "list" ? "primary" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </MobileButton>
          </div>
        )}
      </div>

      {error && (
        <MobileCard className="bg-red-50 border-red-200">
          <p className="text-red-600 text-sm">{error}</p>
        </MobileCard>
      )}

      {/* Search and Filter - Mobile Optimized */}
      <MobileCard padding="sm">
          <div className="space-y-3">
            {/* Search Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`input-mobile pl-10 w-full ${
                  isMobile ? 'text-base' : 'text-sm'
                }`}
              />
            </div>

            {/* Filter Row */}
            <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'items-center justify-between'}`}>
              <div className="relative">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className={`input-mobile pl-10 pr-8 appearance-none ${
                    isMobile ? 'w-full' : 'min-w-[160px]'
                  }`}
                >
                  <option value="all">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="active">Active</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="overdue">Overdue</option>
                </select>
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Filter className="h-5 w-5 text-gray-400" />
                </div>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>

              {/* View Toggle for Mobile */}
              {isMobile && (
                <div className="flex items-center space-x-2">
                  <MobileButton
                    variant={viewMode === "grid" ? "primary" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    fullWidth={false}
                  >
                    <Grid className="h-4 w-4 mr-2" />
                    Grid
                  </MobileButton>
                  <MobileButton
                    variant={viewMode === "list" ? "primary" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    fullWidth={false}
                  >
                    <List className="h-4 w-4 mr-2" />
                    List
                  </MobileButton>
                </div>
              )}
            </div>
          </div>
      </MobileCard>

      {/* No projects state */}
      {projects.length === 0 ? (
        <MobileCard className="text-center" padding="lg">
          <FolderOpen className={`text-gray-300 mx-auto mb-4 ${isMobile ? 'h-12 w-12' : 'h-16 w-16'}`} />
          <h2 className={`font-medium mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>No projects yet</h2>
          <p className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
            You don't have any projects assigned to you yet.
          </p>
        </MobileCard>
      ) : filteredProjects.length === 0 ? (
        <MobileCard className="text-center" padding="lg">
          <h2 className={`font-medium mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>No matching projects found</h2>
          <p className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
            Try adjusting your search or filter criteria.
          </p>
        </MobileCard>
      ) : (
        <>
          {/* Grid View - Mobile Optimized */}
          {viewMode === "grid" && (
            <div className={`grid gap-4 ${
              isMobile
                ? 'grid-cols-1'
                : isTablet
                  ? 'grid-cols-2'
                  : 'grid-cols-3'
            }`}>
              {filteredProjects.map((project) => (
                <Link
                  key={project.id}
                  href={`/designer/projects/${project.id}`}
                  className="block"
                >
                  <MobileCard hover className="overflow-hidden">
                    <div className={`bg-gray-100 relative ${isMobile ? 'h-32' : 'h-48'}`}>
                      <div className="flex items-center justify-center h-full">
                        <FolderOpen className={`text-gray-300 ${isMobile ? 'h-8 w-8' : 'h-12 w-12'}`} />
                      </div>
                      <div className="absolute top-2 right-2">
                        {getStatusBadge(project.status)}
                      </div>
                    </div>
                    <div className={isMobile ? 'p-3' : 'p-4'}>
                      <h3 className={`font-medium mb-1 truncate ${isMobile ? 'text-base' : 'text-lg'}`}>
                        {project.title}
                      </h3>
                      <p className={`text-gray-600 mb-3 line-clamp-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        {project.description}
                      </p>
                      <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'justify-between items-center'} text-gray-500 mb-3`}>
                        <span className={`flex items-center ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          <Users className="h-3 w-3 mr-1" />
                          {project.client_name}
                        </span>
                        <span className={`flex items-center ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(project.due_date)}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-brown-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                      <div className={`text-right text-gray-500 mt-1 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                        {project.progress}% Complete
                      </div>
                    </div>
                  </MobileCard>
                </Link>
              ))}
            </div>
          )}

          {/* List View - Mobile Optimized */}
          {viewMode === "list" && (
            <div className="space-y-3">
              {isMobile ? (
                // Mobile List View - Card-based
                filteredProjects.map((project) => (
                  <Link key={project.id} href={`/designer/projects/${project.id}`}>
                    <MobileListItem
                      leftElement={
                        <div className="h-10 w-10 rounded bg-gray-100 flex items-center justify-center">
                          <FolderOpen className="h-5 w-5 text-gray-400" />
                        </div>
                      }
                      rightElement={
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(project.status)}
                          <Eye className="h-4 w-4 text-gray-400" />
                        </div>
                      }
                    >
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">{project.title}</h3>
                        <p className="text-sm text-gray-500 truncate">{project.client_name}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-gray-400 flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {formatDate(project.due_date)}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-16 bg-gray-200 rounded-full h-1.5">
                              <div
                                className="bg-brown-600 h-1.5 rounded-full"
                                style={{ width: `${project.progress}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-gray-500">{project.progress}%</span>
                          </div>
                        </div>
                      </div>
                    </MobileListItem>
                  </Link>
                ))
              ) : (
                // Desktop Table View
                <MobileCard className="overflow-hidden" padding="sm">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Project
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Client
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Due Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Progress
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredProjects.map((project) => (
                          <tr
                            key={project.id}
                            className="hover:bg-gray-50 cursor-pointer"
                            onClick={() => window.location.href = `/designer/projects/${project.id}`}
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="h-10 w-10 flex-shrink-0 mr-3">
                                  <div className="h-10 w-10 rounded bg-gray-100 flex items-center justify-center">
                                    <FolderOpen className="h-5 w-5 text-gray-400" />
                                  </div>
                                </div>
                                <div>
                                  <div className="font-medium text-gray-900">{project.title}</div>
                                  <div className="text-gray-500 text-sm truncate max-w-xs">{project.description}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-gray-900">{project.client_name}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-gray-500">
                              {formatDate(project.due_date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {getStatusBadge(project.status)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2 max-w-[100px]">
                                  <div
                                    className="bg-brown-600 h-2.5 rounded-full"
                                    style={{ width: `${project.progress}%` }}
                                  ></div>
                                </div>
                                <span className="text-gray-500 text-sm">{project.progress}%</span>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </MobileCard>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
}
