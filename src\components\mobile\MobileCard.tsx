"use client";

import { motion } from 'framer-motion';
import { useResponsive } from './ResponsiveLayout';

interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  onClick?: () => void;
  delay?: number;
}

export function MobileCard({ 
  children, 
  className = '', 
  padding = 'md',
  hover = false,
  onClick,
  delay = 0
}: MobileCardProps) {
  const { isMobile } = useResponsive();

  const paddingClasses = {
    sm: isMobile ? 'p-3' : 'p-4',
    md: isMobile ? 'p-4' : 'p-6',
    lg: isMobile ? 'p-5' : 'p-8',
  };

  const baseClasses = `
    bg-white rounded-lg border border-gray-200 shadow-sm
    ${paddingClasses[padding]}
    ${hover ? 'hover:shadow-md transition-shadow duration-200' : ''}
    ${onClick ? 'cursor-pointer' : ''}
    ${className}
  `;

  const cardContent = (
    <div className={baseClasses} onClick={onClick}>
      {children}
    </div>
  );

  // Add animation on mobile for better feedback
  if (isMobile && (hover || onClick)) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay }}
        whileTap={onClick ? { scale: 0.98 } : undefined}
      >
        {cardContent}
      </motion.div>
    );
  }

  return cardContent;
}

interface MobileStatsCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  onClick?: () => void;
}

export function MobileStatsCard({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  color = 'blue',
  onClick 
}: MobileStatsCardProps) {
  const { isMobile } = useResponsive();

  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    purple: 'bg-purple-50 text-purple-600',
    orange: 'bg-orange-50 text-orange-600',
    red: 'bg-red-50 text-red-600',
  };

  return (
    <MobileCard hover onClick={onClick} className="relative overflow-hidden">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`text-xs font-medium text-gray-600 mb-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
            {title}
          </p>
          <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
            {value}
          </p>
          {trend && (
            <div className="flex items-center mt-1">
              <span className={`text-xs font-medium ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {trend.isPositive ? '+' : ''}{trend.value}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-2 rounded-lg ${colorClasses[color]} ${isMobile ? 'p-2' : 'p-3'}`}>
          <Icon className={`${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
        </div>
      </div>
    </MobileCard>
  );
}

interface MobileListItemProps {
  children: React.ReactNode;
  onClick?: () => void;
  rightElement?: React.ReactNode;
  leftElement?: React.ReactNode;
  className?: string;
}

export function MobileListItem({ 
  children, 
  onClick, 
  rightElement, 
  leftElement,
  className = ''
}: MobileListItemProps) {
  const { isMobile } = useResponsive();

  const content = (
    <div className={`
      flex items-center space-x-3 p-4 bg-white border-b border-gray-100 last:border-b-0
      ${onClick ? 'hover:bg-gray-50 cursor-pointer' : ''}
      ${isMobile ? 'min-h-[60px]' : 'min-h-[72px]'}
      ${className}
    `}>
      {leftElement && (
        <div className="flex-shrink-0">
          {leftElement}
        </div>
      )}
      <div className="flex-1 min-w-0">
        {children}
      </div>
      {rightElement && (
        <div className="flex-shrink-0">
          {rightElement}
        </div>
      )}
    </div>
  );

  if (isMobile && onClick) {
    return (
      <motion.div
        whileTap={{ scale: 0.98 }}
        onClick={onClick}
      >
        {content}
      </motion.div>
    );
  }

  return <div onClick={onClick}>{content}</div>;
}

interface MobileButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

export function MobileButton({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  loading = false,
  onClick,
  className = ''
}: MobileButtonProps) {
  const { isMobile } = useResponsive();

  const baseClasses = `
    inline-flex items-center justify-center font-medium rounded-lg
    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
  `;

  const sizeClasses = {
    sm: isMobile ? 'px-3 py-2 text-sm min-h-[40px]' : 'px-3 py-2 text-sm',
    md: isMobile ? 'px-4 py-3 text-sm min-h-[48px]' : 'px-4 py-2 text-sm',
    lg: isMobile ? 'px-6 py-4 text-base min-h-[52px]' : 'px-6 py-3 text-base',
  };

  const variantClasses = {
    primary: 'bg-brown-600 text-white hover:bg-brown-700 focus:ring-brown-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
  };

  const buttonContent = (
    <button
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {children}
    </button>
  );

  if (isMobile) {
    return (
      <motion.div
        whileTap={{ scale: disabled ? 1 : 0.98 }}
        className={fullWidth ? 'w-full' : 'inline-block'}
      >
        {buttonContent}
      </motion.div>
    );
  }

  return buttonContent;
}
