"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";

export default function Confirmation() {
  const searchParams = useSearchParams();
  const [inviteRole, setInviteRole] = useState<string | null>(null);
  const [inviterName, setInviterName] = useState<string | null>(null);

  useEffect(() => {
    // Check if the user signed up through an invitation
    const role = searchParams.get('role');
    const inviter = searchParams.get('inviter');

    if (role) {
      setInviteRole(role);
    }

    if (inviter) {
      setInviterName(decodeURIComponent(inviter));
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-black">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black bg-opacity-70 z-10" aria-hidden="true" />
        <img
          src="https://images.unsplash.com/photo-1600585154340-be6161a56a0c"
          alt="Architectural background"
          className="object-cover w-full h-full"
        />
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative z-10 bg-white p-10 border border-gray-200 shadow-2xl max-w-md w-full text-center"
      >
        <div className="mb-8">
          <Link href="/" className="inline-block">
            <h1 className="text-brown-600 font-bold text-2xl tracking-tight">SENIOR&apos;S ARCHI-FIRM</h1>
          </Link>
        </div>

        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="w-20 h-20 bg-green-50 border border-green-200 flex items-center justify-center mx-auto mb-6"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </motion.div>

        <h2 className="text-2xl font-bold mb-4">Email Confirmation Sent</h2>
        <p className="text-gray-600 mb-4">
          We&apos;ve sent a confirmation link to your email address. Please check your inbox and click the link to activate your account.
        </p>

        {inviteRole && inviterName && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-amber-50 border border-amber-200 p-4 mb-6 text-left"
          >
            <p className="text-amber-800 text-sm">
              <span className="font-medium">You&apos;ve been invited by {inviterName}!</span>
            </p>
            <p className="text-amber-700 text-xs mt-1">
              {inviteRole === 'client'
                ? "Once your account is confirmed, you&apos;ll be automatically connected with your designer."
                : "Once your account is confirmed, you&apos;ll be automatically connected with your client."}
            </p>
          </motion.div>
        )}

        <div className="space-y-4">
          <Link href="/auth/login">
            <Button variant="default" size="lg" className="w-full bg-brown-600 hover:bg-brown-700 text-white border-0">
              Go to Login
            </Button>
          </Link>

          <Link href="/">
            <Button variant="outline" size="lg" className="w-full border-brown-600 text-brown-600 hover:bg-brown-50">
              Return to Home
            </Button>
          </Link>
        </div>
      </motion.div>
    </div>
  );
}
