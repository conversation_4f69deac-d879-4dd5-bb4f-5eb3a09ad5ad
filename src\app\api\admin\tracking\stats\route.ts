import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

/**
 * API route for getting tracking request statistics
 */
export async function GET(request: NextRequest) {
  try {
    console.log('Admin tracking stats API called');

    // Get basic counts
    const { data: allRequests, error: allError } = await supabaseServerClient
      .from('tracking_requests')
      .select('request_type, status, internal_status');

    if (allError) {
      console.error('Error fetching tracking requests for stats:', allError);
      
      // If table doesn't exist, return mock stats
      if (allError.code === '42P01') {
        console.warn('tracking_requests table does not exist, returning mock stats');
        return NextResponse.json({
          total: 0,
          new: 0,
          in_progress: 0,
          completed: 0,
          vision_builder: 0,
          sample_requests: 0
        });
      }
      
      return NextResponse.json({ 
        error: 'Failed to fetch tracking statistics',
        details: allError.message 
      }, { status: 500 });
    }

    // Calculate stats from the data
    const stats = {
      total: allRequests?.length || 0,
      new: 0,
      in_progress: 0,
      completed: 0,
      vision_builder: 0,
      sample_requests: 0
    };

    if (allRequests) {
      allRequests.forEach(request => {
        // Count by request type
        if (request.request_type === 'vision_builder') {
          stats.vision_builder++;
        } else if (request.request_type === 'sample_request') {
          stats.sample_requests++;
        }

        // Count by status (prefer internal_status if available, but treat 'submitted' as 'new')
        const status = request.internal_status || request.status;
        switch (status) {
          case 'new':
          case 'submitted': // Newly submitted requests should count as 'new'
            stats.new++;
            break;
          case 'in_progress':
          case 'processing':
          case 'assigned':
            stats.in_progress++;
            break;
          case 'completed':
            stats.completed++;
            break;
          case 'cancelled':
            // Don't count cancelled requests in any active category
            break;
          default:
            // Unknown status, count as new for safety
            stats.new++;
            break;
        }
      });
    }

    console.log('Calculated stats:', stats);

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error in GET /api/admin/tracking/stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
