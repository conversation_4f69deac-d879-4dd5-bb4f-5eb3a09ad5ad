"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import {
  CreditCard,
  CheckCircle,
  AlertCircle,
  DollarSign,
  Calendar,
  User,
  FileText,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  Filter,
  Building2,
  Globe,
  Loader2,
  Send,
  Eye,
  Download,
  Clock,
  Settings
} from "lucide-react";

interface DesignerPayout {
  id: string;
  full_name: string;
  email: string;
  avatar_url: string | null;
  pending_amount: number;
  completed_projects: number;
  active_projects: number;
  last_payout_date: string | null;
  milestones: Milestone[];
  payout_methods: PayoutMethod[];
  is_expanded: boolean;
}

interface Milestone {
  id: string;
  title: string;
  amount: number;
  status: string;
  completed_at: string;
  approved_at: string;
  paid_at: string | null;
  project_id: string;
  project_title: string;
}

interface PayoutMethod {
  id: string;
  method_type: string;
  is_default: boolean;
  is_verified: boolean;
  verification_status: string;
  account_holder_name: string;
  bank_name?: string;
  paypal_email?: string;
  bank_country?: string;
  bank_currency?: string;
  minimum_payout_amount: number;
  payout_frequency: string;
  auto_payout_enabled: boolean;
}

interface PayoutBatch {
  id: string;
  batch_id: string;
  provider: string;
  status: string;
  total_amount: number;
  total_fee: number;
  currency: string;
  transaction_count: number;
  created_at: string;
  processed_at?: string;
  external_batch_id?: string;
}

export function EnhancedPayoutSystem() {
  const { user } = useAuth();
  const [designers, setDesigners] = useState<DesignerPayout[]>([]);
  const [payoutBatches, setPayoutBatches] = useState<PayoutBatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedMilestones, setSelectedMilestones] = useState<{[key: string]: Milestone[]}>({});
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'verified'>('all');
  const [activeTab, setActiveTab] = useState<'payouts' | 'batches' | 'settings'>('payouts');

  useEffect(() => {
    if (user) {
      fetchDesignersWithPayouts();
      fetchPayoutBatches();
    }
  }, [user]);

  const fetchDesignersWithPayouts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch designers with role 'designer'
      const { data: designersData, error: designersError } = await supabase
        .from('profiles')
        .select('id, full_name, email, avatar_url')
        .eq('role', 'designer')
        .eq('is_active', true);

      if (designersError) throw designersError;

      // For each designer, fetch their completed milestones and payout methods
      const designersWithPayouts = await Promise.all(
        designersData.map(async (designer) => {
          // Fetch completed milestones that are ready for payout
          const { data: milestones, error: milestonesError } = await supabase
            .from('project_milestones')
            .select(`
              id,
              title,
              amount,
              status,
              completed_at,
              approved_at,
              paid_at,
              project_id,
              projects(title, designer_id)
            `)
            .eq('status', 'approved')
            .is('paid_at', null)
            .not('completed_at', 'is', null)
            .not('approved_at', 'is', null);

          if (milestonesError) throw milestonesError;

          // Filter milestones for this designer
          const designerMilestones = milestones.filter(m => 
            m.projects && 
            m.projects.designer_id === designer.id
          ).map(m => ({
            ...m,
            project_title: m.projects?.title || 'Unknown Project'
          }));

          // Fetch designer's payout methods
          const { data: payoutMethods, error: payoutMethodsError } = await supabase
            .from('designer_payout_methods')
            .select('*')
            .eq('designer_id', designer.id)
            .order('is_default', { ascending: false });

          if (payoutMethodsError) throw payoutMethodsError;

          // Calculate pending amount
          const pendingAmount = designerMilestones.reduce((sum, m) => sum + m.amount, 0);

          // Get project counts
          const { data: projectCounts } = await supabase
            .from('projects')
            .select('status')
            .eq('designer_id', designer.id);

          const completedProjects = projectCounts?.filter(p => p.status === 'completed').length || 0;
          const activeProjects = projectCounts?.filter(p => ['in_progress', 'review'].includes(p.status)).length || 0;

          // Get last payout date
          const { data: lastPayout } = await supabase
            .from('transactions')
            .select('processed_at')
            .eq('designer_id', designer.id)
            .eq('type', 'payout')
            .eq('status', 'completed')
            .order('processed_at', { ascending: false })
            .limit(1);

          return {
            ...designer,
            pending_amount: pendingAmount,
            completed_projects: completedProjects,
            active_projects: activeProjects,
            last_payout_date: lastPayout?.[0]?.processed_at || null,
            milestones: designerMilestones,
            payout_methods: payoutMethods || [],
            is_expanded: false,
          };
        })
      );

      // Filter out designers with no pending payouts unless showing all
      const filteredDesigners = designersWithPayouts.filter(d => {
        if (filterStatus === 'all') return d.pending_amount > 0 || d.payout_methods.length > 0;
        if (filterStatus === 'pending') return d.pending_amount > 0;
        if (filterStatus === 'verified') return d.payout_methods.some(pm => pm.verification_status === 'verified');
        return true;
      });

      setDesigners(filteredDesigners);
    } catch (error: any) {
      console.error('Error fetching designers with payouts:', error);
      setError(error.message || 'Failed to load payout data');
    } finally {
      setLoading(false);
    }
  };

  const fetchPayoutBatches = async () => {
    try {
      const { data, error } = await supabase
        .from('payout_batches')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      setPayoutBatches(data || []);
    } catch (error) {
      console.error('Error fetching payout batches:', error);
    }
  };

  const toggleDesignerExpansion = (designerId: string) => {
    setDesigners(prev => prev.map(d => 
      d.id === designerId ? { ...d, is_expanded: !d.is_expanded } : d
    ));
  };

  const handleMilestoneSelection = (designerId: string, milestone: Milestone, selected: boolean) => {
    setSelectedMilestones(prev => {
      const current = prev[designerId] || [];
      if (selected) {
        return { ...prev, [designerId]: [...current, milestone] };
      } else {
        return { ...prev, [designerId]: current.filter(m => m.id !== milestone.id) };
      }
    });
  };

  const getDefaultPayoutMethod = (payoutMethods: PayoutMethod[]) => {
    return payoutMethods.find(pm => pm.is_default && pm.verification_status === 'verified') ||
           payoutMethods.find(pm => pm.verification_status === 'verified') ||
           payoutMethods[0];
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getMethodIcon = (methodType: string) => {
    switch (methodType) {
      case 'bank_account':
        return <Building2 className="h-4 w-4" />;
      case 'paypal':
        return <CreditCard className="h-4 w-4" />;
      case 'international_wire':
        return <Globe className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const getMethodDisplayName = (methodType: string) => {
    switch (methodType) {
      case 'bank_account':
        return 'Bank Account';
      case 'paypal':
        return 'PayPal';
      case 'international_wire':
        return 'International Wire';
      default:
        return methodType;
    }
  };

  const processIndividualPayout = async (designerId: string) => {
    const milestones = selectedMilestones[designerId];
    if (!milestones || milestones.length === 0) {
      setError('No milestones selected for payout');
      return;
    }

    const designer = designers.find(d => d.id === designerId);
    if (!designer) {
      setError('Designer not found');
      return;
    }

    const payoutMethod = getDefaultPayoutMethod(designer.payout_methods);
    if (!payoutMethod) {
      setError(`${designer.full_name} has no verified payout method`);
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      // Use the new automatic payout function which handles Stripe Connect transfers
      const { data, error } = await supabase.functions.invoke('process-automatic-payouts', {
        body: {
          designerId: designerId,
          force: true // Force processing even if below minimum or not scheduled
        }
      });

      if (error) throw error;

      if (data.results && data.results.length > 0) {
        const result = data.results[0];
        if (result.status === 'success') {
          setSuccess(`Payout of ${formatCurrency(result.amount)} processed successfully for ${designer.full_name}`);
        } else {
          throw new Error(result.reason || 'Payout failed');
        }
      } else {
        throw new Error('No payout result returned');
      }

      // Clear selected milestones for this designer
      setSelectedMilestones(prev => ({ ...prev, [designerId]: [] }));

      // Refresh data
      await fetchDesignersWithPayouts();

      setTimeout(() => setSuccess(null), 5000);

    } catch (error: any) {
      console.error('Error processing payout:', error);
      setError(error.message || 'Failed to process payout');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Enhanced Payout System</h2>
          <p className="text-gray-500">
            Manage designer payouts with integrated payment methods
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500"
          >
            <option value="all">All Designers</option>
            <option value="pending">With Pending Payouts</option>
            <option value="verified">With Verified Methods</option>
          </select>
          <Button
            onClick={fetchDesignersWithPayouts}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error/Success Messages */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start"
          >
            <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
          </motion.div>
        )}
        {success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start"
          >
            <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{success}</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'payouts', label: 'Designer Payouts', icon: DollarSign },
            { id: 'batches', label: 'Payout Batches', icon: FileText },
            { id: 'settings', label: 'Payout Settings', icon: Settings },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-brown-500 text-brown-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'payouts' && (
          <div className="space-y-6">
            {designers.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No designers found</h3>
                <p className="text-gray-500">
                  {filterStatus === 'pending'
                    ? 'No designers have pending payouts at this time'
                    : filterStatus === 'verified'
                    ? 'No designers have verified payout methods'
                    : 'No active designers found'
                  }
                </p>
              </div>
            ) : (
              designers.map((designer) => (
                <motion.div
                  key={designer.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white border rounded-lg shadow-sm overflow-hidden"
                >
                  {/* Designer Header */}
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          {designer.avatar_url ? (
                            <img
                              src={designer.avatar_url}
                              alt={designer.full_name}
                              className="h-12 w-12 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-12 w-12 rounded-full bg-brown-100 flex items-center justify-center">
                              <User className="h-6 w-6 text-brown-600" />
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {designer.full_name}
                          </h3>
                          <p className="text-sm text-gray-500">{designer.email}</p>
                          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <span>{designer.completed_projects} completed</span>
                            <span>{designer.active_projects} active</span>
                            {designer.last_payout_date && (
                              <span>Last payout: {formatDate(designer.last_payout_date)}</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900">
                            {formatCurrency(designer.pending_amount)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {designer.milestones.length} milestone{designer.milestones.length !== 1 ? 's' : ''}
                          </div>
                        </div>
                        <div className="flex flex-col space-y-2">
                          <Button
                            onClick={() => toggleDesignerExpansion(designer.id)}
                            variant="outline"
                            size="sm"
                          >
                            {designer.is_expanded ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </Button>
                          {designer.pending_amount > 0 && (
                            <Button
                              onClick={() => processIndividualPayout(designer.id)}
                              disabled={processing || !selectedMilestones[designer.id]?.length}
                              size="sm"
                              className="bg-brown-600 hover:bg-brown-700 text-white"
                            >
                              {processing ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Send className="h-4 w-4" />
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Expanded Content */}
                  <AnimatePresence>
                    {designer.is_expanded && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="p-6 bg-gray-50">
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Payout Methods */}
                            <div>
                              <h4 className="font-medium text-gray-900 mb-3">Payout Methods</h4>
                              {designer.payout_methods.length === 0 ? (
                                <div className="text-center py-4 bg-white rounded-lg border border-gray-200">
                                  <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                  <p className="text-sm text-gray-500">No payout methods configured</p>
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  {designer.payout_methods.map((method) => (
                                    <div
                                      key={method.id}
                                      className={`p-3 bg-white rounded-lg border ${
                                        method.is_default ? 'border-brown-200 bg-brown-50' : 'border-gray-200'
                                      }`}
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                          {getMethodIcon(method.method_type)}
                                          <div>
                                            <div className="flex items-center space-x-2">
                                              <span className="text-sm font-medium">
                                                {getMethodDisplayName(method.method_type)}
                                              </span>
                                              {method.is_default && (
                                                <span className="px-2 py-1 text-xs bg-brown-100 text-brown-800 rounded-full">
                                                  Default
                                                </span>
                                              )}
                                            </div>
                                            <p className="text-xs text-gray-500">
                                              {method.account_holder_name}
                                              {method.bank_name && ` • ${method.bank_name}`}
                                              {method.paypal_email && ` • ${method.paypal_email}`}
                                            </p>
                                          </div>
                                        </div>
                                        <span className={`px-2 py-1 text-xs rounded-full ${
                                          method.verification_status === 'verified'
                                            ? 'bg-green-100 text-green-800'
                                            : method.verification_status === 'failed'
                                            ? 'bg-red-100 text-red-800'
                                            : 'bg-yellow-100 text-yellow-800'
                                        }`}>
                                          {method.verification_status}
                                        </span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>

                            {/* Pending Milestones */}
                            <div>
                              <h4 className="font-medium text-gray-900 mb-3">Pending Milestones</h4>
                              {designer.milestones.length === 0 ? (
                                <div className="text-center py-4 bg-white rounded-lg border border-gray-200">
                                  <CheckCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                  <p className="text-sm text-gray-500">No pending milestones</p>
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  {designer.milestones.map((milestone) => (
                                    <div
                                      key={milestone.id}
                                      className="p-3 bg-white rounded-lg border border-gray-200"
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                          <input
                                            type="checkbox"
                                            checked={selectedMilestones[designer.id]?.some(m => m.id === milestone.id) || false}
                                            onChange={(e) => handleMilestoneSelection(designer.id, milestone, e.target.checked)}
                                            className="h-4 w-4 text-brown-600 focus:ring-brown-500 border-gray-300 rounded"
                                          />
                                          <div className="flex-1">
                                            <p className="text-sm font-medium text-gray-900">
                                              {milestone.title}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                              {milestone.project_title} • Completed {formatDate(milestone.completed_at)}
                                            </p>
                                          </div>
                                        </div>
                                        <div className="text-right">
                                          <p className="text-sm font-medium text-gray-900">
                                            {formatCurrency(milestone.amount)}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))
            )}
          </div>
        )}

        {activeTab === 'batches' && (
          <div className="space-y-4">
            <div className="bg-white border rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h4 className="font-medium text-gray-900">Recent Payout Batches</h4>
              </div>
              {payoutBatches.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No payout batches</h3>
                  <p className="text-gray-500">Payout batch history will appear here</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {payoutBatches.map((batch) => (
                    <div key={batch.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900">
                              Batch {batch.batch_id}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              batch.status === 'completed'
                                ? 'bg-green-100 text-green-800'
                                : batch.status === 'failed'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {batch.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">
                            {batch.transaction_count} transactions • {batch.provider}
                            {batch.processed_at && ` • Processed ${formatDate(batch.processed_at)}`}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">
                            {formatCurrency(batch.total_amount, batch.currency)}
                          </p>
                          {batch.total_fee > 0 && (
                            <p className="text-xs text-gray-500">
                              Fee: {formatCurrency(batch.total_fee, batch.currency)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="bg-white border rounded-lg p-6">
            <h4 className="font-medium text-gray-900 mb-4">Payout System Settings</h4>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Minimum Payout
                  </label>
                  <input
                    type="number"
                    defaultValue="100"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Auto Payout Schedule
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brown-500">
                    <option value="weekly">Weekly</option>
                    <option value="bi_weekly">Bi-weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enable_auto_payouts"
                  defaultChecked
                  className="h-4 w-4 text-brown-600 focus:ring-brown-500 border-gray-300 rounded"
                />
                <label htmlFor="enable_auto_payouts" className="ml-2 block text-sm text-gray-900">
                  Enable automatic payouts for verified methods
                </label>
              </div>

              <div className="pt-4">
                <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                  Save Settings
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
