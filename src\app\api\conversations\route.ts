import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile to check if admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    const isAdmin = profile?.role === 'admin';

    // Get user's conversations with latest message and unread count
    let conversationsQuery = supabase
      .from('conversations')
      .select(`
        id,
        type,
        title,
        project_id,
        created_at,
        updated_at,
        last_message_at,
        projects:project_id (
          title,
          status
        ),
        conversation_participants!inner (
          user_id,
          last_read_at,
          is_muted,
          profiles:user_id (
            id,
            full_name,
            avatar_url,
            role
          )
        )
      `)
      .eq('is_active', true)
      .order('last_message_at', { ascending: false });

    // If admin, get all conversations; otherwise, only user's conversations
    if (!isAdmin) {
      conversationsQuery = conversationsQuery.eq('conversation_participants.user_id', user.id);
    }

    const { data: conversations, error } = await conversationsQuery;

    if (error) {
      console.error('Error fetching conversations:', error);
      return NextResponse.json({ error: 'Failed to fetch conversations' }, { status: 500 });
    }

    // Get latest message, unread count, and admin stats for each conversation
    const conversationsWithDetails = await Promise.all(
      (conversations || []).map(async (conv) => {
        // Get latest message
        const { data: latestMessage } = await supabase
          .from('conversation_messages')
          .select(`
            id,
            content,
            message_type,
            created_at,
            sender_id,
            profiles:sender_id (
              full_name,
              avatar_url
            )
          `)
          .eq('conversation_id', conv.id)
          .is('deleted_at', null)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        // Get unread count for current user
        const userParticipant = conv.conversation_participants.find(p => p.user_id === user.id);
        const lastReadAt = userParticipant?.last_read_at || new Date(0).toISOString();

        const { count: unreadCount } = await supabase
          .from('conversation_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', conv.id)
          .neq('sender_id', user.id)
          .gt('created_at', lastReadAt)
          .is('deleted_at', null);

        // Get total message count
        const { count: totalMessages } = await supabase
          .from('conversation_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', conv.id)
          .is('deleted_at', null);

        // Get flagged message count (for admins)
        const { count: flaggedCount } = await supabase
          .from('conversation_messages')
          .select('*', { count: 'exact', head: true })
          .eq('conversation_id', conv.id)
          .eq('is_flagged', true)
          .is('deleted_at', null);

        // Get other participants (not current user for direct conversations)
        const otherParticipants = conv.conversation_participants
          .filter(p => p.user_id !== user.id)
          .map(p => p.profiles);

        // Generate conversation title
        let conversationTitle = conv.title;
        if (!conversationTitle) {
          if (conv.type === 'project' && Array.isArray(conv.projects) && conv.projects.length > 0) {
            conversationTitle = `Project: ${conv.projects[0]?.title}`;
          } else if (conv.type === 'direct' && otherParticipants.length > 0) {
            conversationTitle = `Chat with ${otherParticipants[0]?.[0]?.full_name || 'Unknown User'}`;
          } else {
            conversationTitle = 'Direct Message';
          }
        }

        return {
          id: conv.id,
          type: conv.type,
          title: conversationTitle,
          project: conv.projects,
          participants: otherParticipants,
          latest_message: latestMessage,
          unread_count: unreadCount || 0,
          total_messages: totalMessages || 0,
          flagged_count: flaggedCount || 0,
          is_muted: userParticipant?.is_muted || false,
          last_message_at: conv.last_message_at,
          created_at: conv.created_at
        };
      })
    );

    return NextResponse.json(conversationsWithDetails);

  } catch (error) {    console.error('Error in GET /api/conversations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { type = 'direct', title, project_id, participant_ids = [], other_user_id } = await request.json();

    // Handle both old and new parameter formats
    const otherUserId = other_user_id || participant_ids[0];

    // Validate required fields
    if (type === 'project' && !project_id) {
      return NextResponse.json({ error: 'Project ID is required for project conversations' }, { status: 400 });
    }

    if (type === 'direct' && !otherUserId) {
      return NextResponse.json({ error: 'Direct conversations require another participant' }, { status: 400 });
    }

    // For project conversations, verify user has access to the project
    if (type === 'project') {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('client_id, designer_id, title')
        .eq('id', project_id)
        .single();

      if (projectError || !project) {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 });
      }

      if (project.client_id !== user.id && project.designer_id !== user.id) {
        return NextResponse.json({ error: 'Unauthorized access to project' }, { status: 403 });
      }

      // Check if project conversation already exists
      const { data: existingProjectConv } = await supabase
        .from('conversations')
        .select('id')
        .eq('type', 'project')
        .eq('project_id', project_id)
        .single();

      if (existingProjectConv) {
        // Return existing project conversation
        const { data: fullConversation } = await supabase
          .from('conversations')
          .select(`
            id,
            title,
            type,
            project_id,
            created_at,
            updated_at,
            conversation_participants(
              user_id,
              profiles(id, full_name, avatar_url, role)
            )
          `)
          .eq('id', existingProjectConv.id)
          .single();

        return NextResponse.json(fullConversation);
      }
    }

    // For direct conversations, check if conversation already exists
    if (type === 'direct') {
      // Verify the other user exists and get their info
      const { data: otherUser, error: userError } = await supabase
        .from('profiles')
        .select('id, full_name, role')
        .eq('id', otherUserId)
        .single();

      if (userError || !otherUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      const { data: existingConversations } = await supabase
        .from('conversations')
        .select(`
          id,
          conversation_participants(user_id)
        `)
        .eq('type', 'direct')
        .is('project_id', null);

      if (existingConversations) {
        for (const conv of existingConversations) {
          const participantIds = conv.conversation_participants.map(p => p.user_id);
          if (participantIds.includes(user.id) && participantIds.includes(otherUserId) && participantIds.length === 2) {
            // Return existing conversation
            const { data: fullConversation } = await supabase
              .from('conversations')
              .select(`
                id,
                title,
                type,
                project_id,
                created_at,
                updated_at,
                conversation_participants(
                  user_id,
                  profiles(id, full_name, avatar_url, role)
                )
              `)
              .eq('id', conv.id)
              .single();

            return NextResponse.json(fullConversation);
          }
        }
      }
    }

    // Create new conversation
    let conversationTitle = title;
    if (!conversationTitle) {
      if (type === 'project') {
        const { data: project } = await supabase
          .from('projects')
          .select('title')
          .eq('id', project_id)
          .single();
        conversationTitle = `Project: ${project?.title || 'Unknown Project'}`;
      } else if (type === 'direct') {
        const { data: otherUser } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', otherUserId)
          .single();
        conversationTitle = `Chat with ${otherUser?.full_name || 'Unknown User'}`;
      }
    }

    // Create conversation
    const { data: conversation, error: convError } = await supabase
      .from('conversations')
      .insert({
        type,
        title: conversationTitle,
        project_id,
        created_by: user.id
      })
      .select()
      .single();

    if (convError) {
      console.error('Error creating conversation:', convError);
      return NextResponse.json({ error: 'Failed to create conversation' }, { status: 500 });
    }

    // Add participants
    let allParticipants = [user.id];

    if (type === 'project') {
      // For project conversations, add client and designer
      const { data: project } = await supabase
        .from('projects')
        .select('client_id, designer_id')
        .eq('id', project_id)
        .single();

      if (project) {
        allParticipants = [...new Set([project.client_id, project.designer_id])];
      }
    } else if (type === 'direct') {
      // For direct conversations, add the other user
      allParticipants = [user.id, otherUserId];
    } else {
      // For other types, use provided participant_ids
      allParticipants = [...new Set([user.id, ...participant_ids])];
    }

    const participantData = allParticipants.map(userId => ({
      conversation_id: conversation.id,
      user_id: userId,
      role: userId === user.id ? 'admin' : 'member'
    }));

    const { error: participantError } = await supabase
      .from('conversation_participants')
      .insert(participantData);

    if (participantError) {
      console.error('Error adding participants:', participantError);
      // Clean up conversation if participant addition fails
      await supabase.from('conversations').delete().eq('id', conversation.id);
      return NextResponse.json({ error: 'Failed to add participants' }, { status: 500 });
    }

    // Return the created conversation with participants
    const { data: fullConversation } = await supabase
      .from('conversations')
      .select(`
        id,
        title,
        type,
        project_id,
        created_at,
        updated_at,
        conversation_participants(
          user_id,
          profiles(id, full_name, avatar_url, role)
        )
      `)
      .eq('id', conversation.id)
      .single();

    return NextResponse.json(fullConversation, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/conversations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
