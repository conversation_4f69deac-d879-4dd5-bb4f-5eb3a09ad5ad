# Environment Setup Guide

## Required Environment Variables

### 1. Supabase Configuration
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

**How to get these:**
1. Go to your Supabase project dashboard
2. Navigate to Settings > API
3. Copy the Project URL and anon key
4. Copy the service_role key (needed for admin operations)

### 2. Cloudflare R2 Configuration
```bash
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_ACCESS_KEY_ID=your-access-key-id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-secret-access-key
CLOUDFLARE_R2_PUBLIC_URL=https://pub-your-account-id.r2.dev
```

**How to get these:**
1. Go to Cloudflare Dashboard > R2 Object Storage
2. Click "Manage R2 API tokens"
3. Create a new API token with R2 permissions
4. Copy the Access Key ID and Secret Access Key
5. The endpoint format is: `https://[account-id].r2.cloudflarestorage.com`
6. For public URL, either use the default R2 domain or set up a custom domain

### 3. Email Configuration (Resend)
```bash
RESEND_API_KEY=re_your-api-key
```

**How to get this:**
1. Sign up at https://resend.com
2. Go to API Keys section
3. Create a new API key
4. Copy the key (starts with "re_")

### 4. Payment Configuration (Optional)
```bash
# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-key
STRIPE_SECRET_KEY=sk_test_your-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# PayPal
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
```

### 5. Encryption Configuration (Required for Payment Methods)
```bash
# Primary encryption key for sensitive data (account numbers, etc.)
ENCRYPTION_KEY=your-64-character-hex-encryption-key

# Backup encryption key for key rotation
ENCRYPTION_KEY_BACKUP=your-backup-64-character-hex-encryption-key
```

**How to generate encryption keys:**
1. Use the built-in key generator (run once in Node.js console):
   ```javascript
   const crypto = require('crypto');
   console.log('Primary Key:', crypto.randomBytes(32).toString('hex'));
   console.log('Backup Key:', crypto.randomBytes(32).toString('hex'));
   ```
2. Or use online tools like: https://www.allkeysgenerator.com/Random/Security-Encryption-Key-Generator.aspx
3. **Important:** Store these keys securely and never commit them to version control

## Quick Setup

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Fill in your actual values in `.env.local`

3. Restart your development server:
   ```bash
   npm run dev
   ```

## Troubleshooting

### R2 File Access Issues
- Ensure all R2 environment variables are set
- Check that your R2 buckets exist: `designer-applications`, `sample-request-files`, `vision-builder-images`
- Verify API token has read/write permissions for R2

### User Creation Issues
- Ensure `SUPABASE_SERVICE_ROLE_KEY` is set (not the anon key)
- Check that the service role key has admin permissions
- Verify your Supabase project allows user creation

### Email Issues
- Ensure `RESEND_API_KEY` is set and valid
- Check that your domain is verified in Resend (for production)
- Verify email templates are properly configured

### Encryption Issues
- Ensure `ENCRYPTION_KEY` is set and is exactly 64 characters (32 bytes in hex)
- If you see "****ERROR" in payment methods, check encryption key validity
- For key rotation, set `ENCRYPTION_KEY_BACKUP` before changing primary key
- Never use weak keys (all same character, sequential patterns, etc.)
