'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { performanceCache, cacheKeys, invalidateUserCache, invalidateProjectCache } from '@/lib/performance-cache';
import { supabase } from '@/lib/supabase';

interface PerformanceOptions {
  enableCaching?: boolean;
  enablePreloading?: boolean;
  enableRealtime?: boolean;
  cacheTimeout?: number;
  retryAttempts?: number;
}

interface PerformanceMetrics {
  loadTime: number;
  cacheHitRate: number;
  errorRate: number;
  lastUpdate: Date | null;
}

export function usePerformanceOptimization(
  userId: string,
  userRole: string,
  options: PerformanceOptions = {}
) {
  const {
    enableCaching = true,
    enablePreloading = true,
    enableRealtime = true,
    cacheTimeout = 5 * 60 * 1000, // 5 minutes
    retryAttempts = 3
  } = options;

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    cacheHitRate: 0,
    errorRate: 0,
    lastUpdate: null
  });

  const [isOptimized, setIsOptimized] = useState(false);
  const retryCountRef = useRef(0);
  const startTimeRef = useRef<number>(0);

  // Initialize performance optimization
  useEffect(() => {
    if (userId && userRole) {
      initializeOptimization();
    }
  }, [userId, userRole]);

  const initializeOptimization = async () => {
    try {
      startTimeRef.current = performance.now();

      // Preload common data if enabled
      if (enablePreloading) {
        await performanceCache.preloadCommonData(userId, userRole);
      }

      // Setup real-time subscriptions if enabled
      if (enableRealtime) {
        setupRealtimeSubscriptions();
      }

      const loadTime = performance.now() - startTimeRef.current;
      const cacheStats = performanceCache.getStats();

      setMetrics({
        loadTime,
        cacheHitRate: cacheStats.hitRate,
        errorRate: 0,
        lastUpdate: new Date()
      });

      setIsOptimized(true);
    } catch (error) {
      console.error('Performance optimization initialization failed:', error);
      handleOptimizationError(error);
    }
  };

  // Optimized data fetching with caching and retry logic
  const fetchOptimizedData = useCallback(async <T>(
    key: string,
    fetchFunction: () => Promise<T>,
    customTimeout?: number
  ): Promise<T> => {
    if (!enableCaching) {
      return fetchFunction();
    }

    const startTime = performance.now();

    try {
      const data = await performanceCache.get(
        key,
        async () => {
          retryCountRef.current = 0;
          return await retryWithBackoff(fetchFunction);
        },
        customTimeout || cacheTimeout
      );

      // Update metrics
      const loadTime = performance.now() - startTime;
      const cacheStats = performanceCache.getStats();
      
      setMetrics(prev => ({
        ...prev,
        loadTime,
        cacheHitRate: cacheStats.hitRate,
        lastUpdate: new Date()
      }));

      return data;
    } catch (error) {
      handleOptimizationError(error);
      throw error;
    }
  }, [enableCaching, cacheTimeout]);

  // Retry logic with exponential backoff
  const retryWithBackoff = async <T>(fetchFunction: () => Promise<T>): Promise<T> => {
    try {
      return await fetchFunction();
    } catch (error) {
      retryCountRef.current++;
      
      if (retryCountRef.current >= retryAttempts) {
        throw error;
      }

      // Exponential backoff: 1s, 2s, 4s, etc.
      const delay = Math.pow(2, retryCountRef.current) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return retryWithBackoff(fetchFunction);
    }
  };

  // Handle optimization errors
  const handleOptimizationError = (error: any) => {
    console.error('Performance optimization error:', error);
    
    setMetrics(prev => ({
      ...prev,
      errorRate: prev.errorRate + 1,
      lastUpdate: new Date()
    }));
  };

  // Setup real-time subscriptions for cache invalidation
  const setupRealtimeSubscriptions = () => {
    if (!enableRealtime) return;

    // Subscribe to project changes
    const projectSubscription = supabase
      .channel('project_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'projects' },
        (payload) => {
          if (payload.new?.id) {
            invalidateProjectCache(payload.new.id);
          }
          if (payload.old?.id) {
            invalidateProjectCache(payload.old.id);
          }
        }
      )
      .subscribe();

    // Subscribe to user profile changes
    const profileSubscription = supabase
      .channel('profile_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'profiles' },
        (payload) => {
          if (payload.new?.id) {
            invalidateUserCache(payload.new.id);
          }
          if (payload.old?.id) {
            invalidateUserCache(payload.old.id);
          }
        }
      )
      .subscribe();

    // Cleanup subscriptions
    return () => {
      projectSubscription.unsubscribe();
      profileSubscription.unsubscribe();
    };
  };

  // Optimized project fetching
  const fetchOptimizedProjects = useCallback(async (filters?: any) => {
    const key = cacheKeys.projects(userRole, userId, JSON.stringify(filters));
    
    return fetchOptimizedData(key, async () => {
      return performanceCache.getOptimizedProjects({
        userId,
        userRole,
        ...filters
      });
    });
  }, [userId, userRole, fetchOptimizedData]);

  // Optimized escrow data fetching
  const fetchOptimizedEscrowData = useCallback(async (type: 'holds' | 'releases') => {
    const key = cacheKeys.escrow(type, userRole, userId);
    
    return fetchOptimizedData(key, async () => {
      return performanceCache.getOptimizedEscrowData({
        userId,
        userRole,
        type
      });
    });
  }, [userId, userRole, fetchOptimizedData]);

  // Optimized dashboard data fetching
  const fetchOptimizedDashboard = useCallback(async (forceRefresh = false) => {
    const key = cacheKeys.dashboard(userRole, userId);
    
    if (forceRefresh) {
      performanceCache.invalidate(key);
    }

    return fetchOptimizedData(key, async () => {
      const response = await fetch(`/api/optimized/dashboard?refresh=${forceRefresh}`, {
        headers: {
          'Authorization': `Bearer ${await supabase.auth.getSession().then(s => s.data.session?.access_token)}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Dashboard API error: ${response.status}`);
      }

      const result = await response.json();
      return result.data;
    }, forceRefresh ? 0 : undefined);
  }, [userId, userRole, fetchOptimizedData]);

  // Cache management functions
  const clearCache = useCallback(() => {
    performanceCache.clear();
    setMetrics(prev => ({
      ...prev,
      cacheHitRate: 0,
      lastUpdate: new Date()
    }));
  }, []);

  const invalidateCache = useCallback((pattern?: string) => {
    if (pattern) {
      performanceCache.invalidatePattern(pattern);
    } else {
      invalidateUserCache(userId);
    }
  }, [userId]);

  const getCacheStats = useCallback(() => {
    return performanceCache.getStats();
  }, []);

  // Performance monitoring
  const getPerformanceReport = useCallback(() => {
    const cacheStats = performanceCache.getStats();
    
    return {
      optimization_enabled: isOptimized,
      cache_performance: {
        hit_rate: cacheStats.hitRate,
        total_entries: cacheStats.totalEntries,
        memory_usage_kb: Math.round(cacheStats.memoryUsage / 1024)
      },
      load_performance: {
        average_load_time_ms: metrics.loadTime,
        error_rate: metrics.errorRate,
        last_update: metrics.lastUpdate
      },
      features: {
        caching_enabled: enableCaching,
        preloading_enabled: enablePreloading,
        realtime_enabled: enableRealtime
      }
    };
  }, [isOptimized, metrics, enableCaching, enablePreloading, enableRealtime]);

  return {
    // Data fetching functions
    fetchOptimizedProjects,
    fetchOptimizedEscrowData,
    fetchOptimizedDashboard,
    fetchOptimizedData,
    
    // Cache management
    clearCache,
    invalidateCache,
    getCacheStats,
    
    // Performance monitoring
    metrics,
    isOptimized,
    getPerformanceReport,
    
    // Utility functions
    retryWithBackoff
  };
}

// Hook for component-level performance optimization
export function useComponentPerformance(componentName: string) {
  const [renderTime, setRenderTime] = useState(0);
  const [renderCount, setRenderCount] = useState(0);
  const startTimeRef = useRef<number>(0);

  useEffect(() => {
    startTimeRef.current = performance.now();
    setRenderCount(prev => prev + 1);
  });

  useEffect(() => {
    const endTime = performance.now();
    const duration = endTime - startTimeRef.current;
    setRenderTime(duration);
  });

  const getComponentStats = useCallback(() => {
    return {
      component_name: componentName,
      render_count: renderCount,
      last_render_time_ms: renderTime,
      average_render_time_ms: renderTime / renderCount
    };
  }, [componentName, renderCount, renderTime]);

  return {
    renderTime,
    renderCount,
    getComponentStats
  };
}
