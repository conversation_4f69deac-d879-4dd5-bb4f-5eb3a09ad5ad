"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Settings,
  Users,
  Calendar,
  Target,
  MessageSquare,
  Save,
  X,
  Plus,
  Trash2,
  Clock,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  FileText,
  Send
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  status: string;
  client: {
    id: string;
    full_name: string;
    email: string;
  };
  designer: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface CoordinationTask {
  id: string;
  title: string;
  description: string;
  assigned_to: string;
  due_date: string;
  priority: string;
  status: string;
}

export default function NewCoordinationPage() {
  const { user, profile } = useOptimizedAuth();
  const router = useRouter();
  
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  const [coordinationData, setCoordinationData] = useState({
    project_id: '',
    coordination_type: 'project_management',
    title: '',
    description: '',
    priority: 'normal',
    scheduled_date: '',
    estimated_duration: 60,
    participants: [] as string[],
    objectives: [''],
    deliverables: [''],
    notes: ''
  });

  const [tasks, setTasks] = useState<Omit<CoordinationTask, 'id'>[]>([
    {
      title: '',
      description: '',
      assigned_to: '',
      due_date: '',
      priority: 'normal',
      status: 'pending'
    }
  ]);

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchProjects();
    }
  }, [user, profile]);

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id, title, status,
          client:profiles!projects_client_id_fkey(id, full_name, email),
          designer:profiles!projects_designer_id_fkey(id, full_name, email)
        `)
        .eq('manager_id', user?.id)
        .in('status', ['active', 'in_progress'])
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProjects(data || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const addObjective = () => {
    setCoordinationData(prev => ({
      ...prev,
      objectives: [...prev.objectives, '']
    }));
  };

  const removeObjective = (index: number) => {
    setCoordinationData(prev => ({
      ...prev,
      objectives: prev.objectives.filter((_, i) => i !== index)
    }));
  };

  const updateObjective = (index: number, value: string) => {
    setCoordinationData(prev => ({
      ...prev,
      objectives: prev.objectives.map((obj, i) => i === index ? value : obj)
    }));
  };

  const addDeliverable = () => {
    setCoordinationData(prev => ({
      ...prev,
      deliverables: [...prev.deliverables, '']
    }));
  };

  const removeDeliverable = (index: number) => {
    setCoordinationData(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter((_, i) => i !== index)
    }));
  };

  const updateDeliverable = (index: number, value: string) => {
    setCoordinationData(prev => ({
      ...prev,
      deliverables: prev.deliverables.map((del, i) => i === index ? value : del)
    }));
  };

  const addTask = () => {
    setTasks(prev => [...prev, {
      title: '',
      description: '',
      assigned_to: '',
      due_date: '',
      priority: 'normal',
      status: 'pending'
    }]);
  };

  const removeTask = (index: number) => {
    setTasks(prev => prev.filter((_, i) => i !== index));
  };

  const updateTask = (index: number, field: string, value: string) => {
    setTasks(prev => prev.map((task, i) => 
      i === index ? { ...task, [field]: value } : task
    ));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!coordinationData.project_id || !coordinationData.title.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    try {
      const selectedProject = projects.find(p => p.id === coordinationData.project_id);
      if (!selectedProject) throw new Error('Project not found');

      // Create coordination session
      const { data: coordination, error: coordinationError } = await supabase
        .from('coordination_sessions')
        .insert({
          project_id: coordinationData.project_id,
          manager_id: user?.id,
          coordination_type: coordinationData.coordination_type,
          title: coordinationData.title.trim(),
          description: coordinationData.description.trim(),
          priority: coordinationData.priority,
          scheduled_date: coordinationData.scheduled_date || null,
          estimated_duration: coordinationData.estimated_duration,
          participants: [
            user?.id,
            selectedProject.client.id,
            selectedProject.designer.id,
            ...coordinationData.participants
          ],
          objectives: coordinationData.objectives.filter(obj => obj.trim()),
          deliverables: coordinationData.deliverables.filter(del => del.trim()),
          notes: coordinationData.notes.trim(),
          status: 'scheduled'
        })
        .select()
        .single();

      if (coordinationError) throw coordinationError;

      // Create coordination tasks
      const validTasks = tasks.filter(task => task.title.trim());
      if (validTasks.length > 0) {
        const { error: tasksError } = await supabase
          .from('coordination_tasks')
          .insert(
            validTasks.map(task => ({
              coordination_id: coordination.id,
              title: task.title.trim(),
              description: task.description.trim(),
              assigned_to: task.assigned_to || selectedProject.designer.id,
              due_date: task.due_date,
              priority: task.priority,
              status: task.status,
              created_by: user?.id
            }))
          );

        if (tasksError) throw tasksError;
      }

      // Send notifications to participants
      const notifications = [
        {
          user_id: selectedProject.client.id,
          type: 'coordination_scheduled',
          title: 'New Coordination Session',
          message: `A coordination session has been scheduled for ${selectedProject.title}`,
          data: { coordination_id: coordination.id, project_id: coordinationData.project_id }
        },
        {
          user_id: selectedProject.designer.id,
          type: 'coordination_scheduled',
          title: 'New Coordination Session',
          message: `A coordination session has been scheduled for ${selectedProject.title}`,
          data: { coordination_id: coordination.id, project_id: coordinationData.project_id }
        }
      ];

      await supabase.from('notifications').insert(notifications);

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: coordinationData.project_id,
        activity_type: 'coordination_created',
        description: `Created coordination session: ${coordinationData.title}`,
        outcome: 'coordination_created'
      });

      alert('Coordination session created successfully!');
      router.push(`/manager/coordination/${coordination.id}`);
    } catch (error) {
      console.error('Error creating coordination:', error);
      alert('Error creating coordination session. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <Settings className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create Coordination Session</h1>
            <p className="text-gray-600 mt-1">Set up a new project coordination session</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project *
              </label>
              <select
                value={coordinationData.project_id}
                onChange={(e) => setCoordinationData(prev => ({ ...prev, project_id: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                required
              >
                <option value="">Select a project...</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.title} - {project.client.full_name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Coordination Type
              </label>
              <select
                value={coordinationData.coordination_type}
                onChange={(e) => setCoordinationData(prev => ({ ...prev, coordination_type: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              >
                <option value="project_management">Project Management</option>
                <option value="design_review">Design Review</option>
                <option value="milestone_planning">Milestone Planning</option>
                <option value="quality_assurance">Quality Assurance</option>
                <option value="client_meeting">Client Meeting</option>
                <option value="team_sync">Team Sync</option>
              </select>
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Session Title *
            </label>
            <input
              type="text"
              value={coordinationData.title}
              onChange={(e) => setCoordinationData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter coordination session title..."
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              required
            />
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={coordinationData.description}
              onChange={(e) => setCoordinationData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe the purpose and scope of this coordination session..."
              rows={4}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>

        {/* Scheduling */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Scheduling</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Scheduled Date
              </label>
              <input
                type="datetime-local"
                value={coordinationData.scheduled_date}
                onChange={(e) => setCoordinationData(prev => ({ ...prev, scheduled_date: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estimated Duration (minutes)
              </label>
              <input
                type="number"
                min="15"
                max="480"
                value={coordinationData.estimated_duration}
                onChange={(e) => setCoordinationData(prev => ({ ...prev, estimated_duration: Number(e.target.value) }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority Level
              </label>
              <select
                value={coordinationData.priority}
                onChange={(e) => setCoordinationData(prev => ({ ...prev, priority: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              >
                <option value="low">Low Priority</option>
                <option value="normal">Normal Priority</option>
                <option value="high">High Priority</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>
        </div>

        {/* Objectives */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Session Objectives</h2>
            <Button
              type="button"
              variant="outline"
              onClick={addObjective}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Objective
            </Button>
          </div>

          <div className="space-y-3">
            {coordinationData.objectives.map((objective, index) => (
              <div key={index} className="flex gap-3">
                <input
                  type="text"
                  value={objective}
                  onChange={(e) => updateObjective(index, e.target.value)}
                  placeholder={`Objective ${index + 1}...`}
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                />
                {coordinationData.objectives.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => removeObjective(index)}
                    className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Deliverables */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Expected Deliverables</h2>
            <Button
              type="button"
              variant="outline"
              onClick={addDeliverable}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Deliverable
            </Button>
          </div>

          <div className="space-y-3">
            {coordinationData.deliverables.map((deliverable, index) => (
              <div key={index} className="flex gap-3">
                <input
                  type="text"
                  value={deliverable}
                  onChange={(e) => updateDeliverable(index, e.target.value)}
                  placeholder={`Deliverable ${index + 1}...`}
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                />
                {coordinationData.deliverables.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => removeDeliverable(index)}
                    className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Tasks */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Coordination Tasks</h2>
            <Button
              type="button"
              variant="outline"
              onClick={addTask}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Task
            </Button>
          </div>

          <div className="space-y-6">
            {tasks.map((task, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-medium text-gray-900">Task {index + 1}</h3>
                  {tasks.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => removeTask(index)}
                      className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                      Remove
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Task Title
                    </label>
                    <input
                      type="text"
                      value={task.title}
                      onChange={(e) => updateTask(index, 'title', e.target.value)}
                      placeholder="Enter task title..."
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Due Date
                    </label>
                    <input
                      type="date"
                      value={task.due_date}
                      onChange={(e) => updateTask(index, 'due_date', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Task Description
                  </label>
                  <textarea
                    value={task.description}
                    onChange={(e) => updateTask(index, 'description', e.target.value)}
                    placeholder="Describe the task..."
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority
                    </label>
                    <select
                      value={task.priority}
                      onChange={(e) => updateTask(index, 'priority', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    >
                      <option value="low">Low</option>
                      <option value="normal">Normal</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Assigned To
                    </label>
                    <select
                      value={task.assigned_to}
                      onChange={(e) => updateTask(index, 'assigned_to', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    >
                      <option value="">Auto-assign to designer</option>
                      {coordinationData.project_id && projects.find(p => p.id === coordinationData.project_id) && (
                        <>
                          <option value={projects.find(p => p.id === coordinationData.project_id)?.client.id}>
                            {projects.find(p => p.id === coordinationData.project_id)?.client.full_name} (Client)
                          </option>
                          <option value={projects.find(p => p.id === coordinationData.project_id)?.designer.id}>
                            {projects.find(p => p.id === coordinationData.project_id)?.designer.full_name} (Designer)
                          </option>
                        </>
                      )}
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Notes */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Notes</h2>
          <textarea
            value={coordinationData.notes}
            onChange={(e) => setCoordinationData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Add any additional notes, requirements, or special instructions..."
            rows={4}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Cancel
          </Button>

          <Button
            type="submit"
            disabled={!coordinationData.project_id || !coordinationData.title.trim() || submitting}
            className="flex items-center gap-2"
          >
            {submitting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            Create Coordination Session
          </Button>
        </div>
      </form>
    </div>
  );
}
