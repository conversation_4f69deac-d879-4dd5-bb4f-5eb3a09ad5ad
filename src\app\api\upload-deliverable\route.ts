import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';

/**
 * POST /api/upload-deliverable
 * Uploads a deliverable file for a milestone
 * 
 * Request body: FormData with:
 * - file: File to upload
 * - milestoneId: ID of the milestone
 * - userId: ID of the user uploading the file
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const milestoneId = formData.get('milestoneId') as string;
    const userId = formData.get('userId') as string;
    
    if (!file) {
      return NextResponse.json(
        { error: 'File is required' },
        { status: 400 }
      );
    }
    
    if (!milestoneId) {
      return NextResponse.json(
        { error: 'Milestone ID is required' },
        { status: 400 }
      );
    }
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Get the milestone to check permissions
    const { data: milestone, error: milestoneError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        project_id,
        projects (
          designer_id
        )
      `)
      .eq('id', milestoneId)
      .single();
    
    if (milestoneError) {
      return NextResponse.json(
        { error: 'Failed to fetch milestone' },
        { status: 500 }
      );
    }
    
    // Check if the user has permission to upload a deliverable
    const isAdmin = profile.role === 'admin';
    const isDesigner = profile.role === 'designer' && userId === milestone.projects.designer_id;
    
    if (!isAdmin && !isDesigner) {
      return NextResponse.json(
        { error: 'You do not have permission to upload deliverables for this milestone' },
        { status: 403 }
      );
    }
    
    // Generate a unique file name
    const fileExtension = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExtension}`;
    const filePath = `deliverables/${milestone.project_id}/${milestoneId}/${fileName}`;
    
    // Convert the file to an ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = new Uint8Array(arrayBuffer);
    
    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('deliverables')
      .upload(filePath, fileBuffer, {
        contentType: file.type,
        upsert: true
      });
    
    if (error) {
      console.error('Error uploading file:', error);
      return NextResponse.json(
        { error: 'Failed to upload file' },
        { status: 500 }
      );
    }
    
    // Get the public URL for the file
    const { data: { publicUrl } } = supabase.storage
      .from('deliverables')
      .getPublicUrl(filePath);
    
    // Update the milestone with the deliverable URL
    const { error: updateError } = await supabase
      .from('project_milestones')
      .update({
        deliverable_url: publicUrl,
        status: 'completed',
        completed_at: new Date().toISOString(),
        completed_by: userId
      })
      .eq('id', milestoneId);
    
    if (updateError) {
      console.error('Error updating milestone:', updateError);
      return NextResponse.json(
        { error: 'Failed to update milestone with deliverable URL' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      url: publicUrl,
      message: 'Deliverable uploaded successfully'
    }, { status: 200 });
  } catch (error) {
    console.error('Error uploading deliverable:', error);
    return NextResponse.json(
      { error: 'Failed to upload deliverable' },
      { status: 500 }
    );
  }
}
