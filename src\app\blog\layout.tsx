import { Metadata } from 'next';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

export const metadata: Metadata = generateSEOMetadata({
  title: "Architecture Blog & Insights",
  description: "Explore the latest architectural trends, design insights, project showcases, and industry news. Stay updated with Senior's Archi-Firm's expert perspectives on modern architecture.",
  path: "/blog",
  keywords: [
    "architecture blog",
    "design insights",
    "architectural trends",
    "building design articles",
    "architecture news",
    "design inspiration",
    "architectural projects",
    "sustainable design",
    "modern architecture",
    "design tips"
  ]
});

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
