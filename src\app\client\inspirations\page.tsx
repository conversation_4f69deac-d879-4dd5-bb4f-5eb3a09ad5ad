"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Plus, Image, FolderOpen, Search, Grid, List } from "lucide-react";

type InspirationBoard = {
  id: string;
  title: string;
  description: string | null;
  created_at: string;
  project_id: string;
  project_title: string;
  image_count: number;
  thumbnail_url: string | null;
};

export default function ClientInspirations() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProject, setSelectedProject] = useState<string | "all">("all");

  // Fetch inspiration boards with React Query
  const { data: boards = [], isLoading: boardsLoading, error: boardsError } = useQuery({
    queryKey: ['inspiration-boards', user?.id],
    queryFn: async () => {
      if (!user) return [];

      // Get all inspiration boards for projects owned by the client
      const { data, error: boardsError } = await supabase
        .from('inspiration_boards')
        .select(`
          id,
          title,
          description,
          created_at,
          project_id,
          projects!inner(title, client_id)
        `)
        .eq('projects.client_id', user.id)
        .order('created_at', { ascending: false });

      if (boardsError) throw boardsError;

      // For each board, get the image count and a thumbnail
      const boardsWithDetails = await Promise.all(
        (data || []).map(async (board: any) => {
          // Get image count
          const { count, error: countError } = await supabase
            .from('inspiration_images')
            .select('id', { count: 'exact' })
            .eq('board_id', board.id);

          if (countError) throw countError;

          // Get first image as thumbnail
          const { data: imageData, error: imageError } = await supabase
            .from('inspiration_images')
            .select('image_url')
            .eq('board_id', board.id)
            .order('created_at', { ascending: true })
            .limit(1)
            .single();

          if (imageError && imageError.code !== 'PGRST116') {
            throw imageError;
          }

          return {
            id: board.id,
            title: board.title,
            description: board.description,
            created_at: board.created_at,
            project_id: board.project_id,
            project_title: board.projects?.title || 'Unknown Project',
            image_count: count || 0,
            thumbnail_url: imageData?.image_url || null
          };
        })
      );

      return boardsWithDetails;
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch projects with React Query
  const { data: projects = [] } = useQuery({
    queryKey: ['projects', user?.id],
    queryFn: async () => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('projects')
        .select('id, title')
        .eq('client_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });


  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter boards based on search query and selected project
  const filteredBoards = boards.filter(board => {
    const matchesSearch = board.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          (board.description && board.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
                          board.project_title.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesProject = selectedProject === "all" || board.project_id === selectedProject;

    return matchesSearch && matchesProject;
  });

  // Show loading only for initial load or auth loading
  const loading = authLoading || (boardsLoading && boards.length === 0);
  const error = boardsError?.message || null;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">Inspiration Boards</h1>

        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="icon"
            onClick={() => setViewMode("grid")}
            className="h-9 w-9"
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="icon"
            onClick={() => setViewMode("list")}
            className="h-9 w-9"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 rounded-lg">
          <p>{error}</p>
        </div>
      )}

      {/* Filter and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search inspiration boards..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 p-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>

        <select
          value={selectedProject}
          onChange={(e) => setSelectedProject(e.target.value)}
          className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        >
          <option value="all">All Projects</option>
          {projects.map(project => (
            <option key={project.id} value={project.id}>
              {project.title}
            </option>
          ))}
        </select>

        <Link href={projects.length > 0 ? `/client/projects/${projects[0].id}/inspirations/new` : "/client/projects"}>
          <Button className="whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            New Board
          </Button>
        </Link>
      </div>

      {/* No boards state */}
      {boards.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <Image className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-medium mb-2">No inspiration boards yet</h2>
          <p className="text-gray-500 mb-6">
            Create your first inspiration board to collect and organize design ideas for your projects.
          </p>
          {projects.length > 0 ? (
            <Link href={`/client/projects/${projects[0].id}/inspirations/new`}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Inspiration Board
              </Button>
            </Link>
          ) : (
            <div>
              <p className="text-sm text-gray-500 mb-4">You need to create a project first</p>
              <Link href="/client/projects/new">
                <Button>
                  <FolderOpen className="h-4 w-4 mr-2" />
                  Create Project
                </Button>
              </Link>
            </div>
          )}
        </div>
      ) : filteredBoards.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-xl font-medium mb-2">No matching boards found</h2>
          <p className="text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      ) : (
        <>
          {/* Grid View */}
          {viewMode === "grid" && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredBoards.map((board) => (
                <Link
                  key={board.id}
                  href={`/client/projects/${board.project_id}/inspirations/${board.id}`}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <div className="h-48 bg-gray-100 relative">
                    {board.thumbnail_url ? (
                      <img
                        src={board.thumbnail_url}
                        alt={board.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <Image className="h-12 w-12 text-gray-300" />
                      </div>
                    )}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                      <p className="text-white text-sm">{board.project_title}</p>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-lg mb-1 truncate">{board.title}</h3>
                    {board.description && (
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{board.description}</p>
                    )}
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>{board.image_count} images</span>
                      <span>{formatDate(board.created_at)}</span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}

          {/* List View */}
          {viewMode === "list" && (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Board
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Project
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Images
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredBoards.map((board) => (
                      <tr
                        key={board.id}
                        className="hover:bg-gray-50 cursor-pointer"
                        onClick={() => window.location.href = `/client/projects/${board.project_id}/inspirations/${board.id}`}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0 mr-3">
                              {board.thumbnail_url ? (
                                <img
                                  src={board.thumbnail_url}
                                  alt={board.title}
                                  className="h-10 w-10 rounded object-cover"
                                />
                              ) : (
                                <div className="h-10 w-10 rounded bg-gray-100 flex items-center justify-center">
                                  <Image className="h-5 w-5 text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{board.title}</div>
                              {board.description && (
                                <div className="text-gray-500 text-sm truncate max-w-xs">{board.description}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-gray-900">{board.project_title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-gray-900">{board.image_count}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-500">
                          {formatDate(board.created_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
