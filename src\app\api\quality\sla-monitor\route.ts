import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { SLAMonitor } from '@/lib/sla-monitor';

/**
 * API endpoint for SLA monitoring and alerts
 * Provides SLA status, dashboard data, and manual SLA checks
 */

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has permission to view SLA data
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['admin', 'quality_team', 'manager'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'dashboard';

    switch (action) {
      case 'dashboard':
        // Get SLA dashboard data
        const dashboardData = await SLAMonitor.getSLADashboardData();
        return NextResponse.json({
          success: true,
          data: dashboardData
        });

      case 'status':
        // Get detailed SLA status for all active reviews
        const slaStatuses = await SLAMonitor.checkAllSLAs();
        return NextResponse.json({
          success: true,
          data: slaStatuses
        });

      case 'alerts':
        // Get recent SLA alerts
        const { data: alerts, error: alertsError } = await supabase
          .from('sla_alerts')
          .select(`
            id,
            review_id,
            alert_type,
            escalation_level,
            sent_at,
            acknowledged,
            metadata
          `)
          .order('sent_at', { ascending: false })
          .limit(50);

        if (alertsError) throw alertsError;

        return NextResponse.json({
          success: true,
          data: alerts || []
        });

      case 'compliance':
        // Get SLA compliance statistics
        const startDate = searchParams.get('start_date');
        const endDate = searchParams.get('end_date');

        const { data: complianceData, error: complianceError } = await supabase
          .rpc('get_sla_compliance_stats', {
            start_date: startDate || undefined,
            end_date: endDate || undefined
          });

        if (complianceError) throw complianceError;

        return NextResponse.json({
          success: true,
          data: complianceData?.[0] || {
            total_reviews: 0,
            on_time_reviews: 0,
            overdue_reviews: 0,
            average_completion_hours: 0,
            sla_compliance_percentage: 0
          }
        });

      case 'monitor':
        // Run comprehensive SLA monitoring
        const { data: monitorResult, error: monitorError } = await supabase
          .rpc('monitor_quality_sla');

        if (monitorError) throw monitorError;

        return NextResponse.json({
          success: true,
          data: monitorResult?.[0] || {
            total_reviews: 0,
            overdue_reviews: 0,
            due_soon_reviews: 0,
            escalated_reviews: 0,
            warnings_sent: 0,
            escalations_created: 0,
            reassignments_made: 0
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in SLA monitor API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint for manual SLA operations
 */
export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has permission to perform SLA operations
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['admin', 'quality_team'].includes(profile.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { action, reviewId, alertId } = body;

    switch (action) {
      case 'check_all':
        // Manually trigger SLA check for all reviews
        const slaStatuses = await SLAMonitor.checkAllSLAs();
        return NextResponse.json({
          success: true,
          message: `Checked ${slaStatuses.length} reviews`,
          data: slaStatuses
        });

      case 'escalate_review':
        // Manually escalate a specific review
        if (!reviewId) {
          return NextResponse.json(
            { error: 'Review ID is required for escalation' },
            { status: 400 }
          );
        }

        // Update review status to escalated
        const { error: escalateError } = await supabase
          .from('quality_reviews_new')
          .update({
            status: 'escalated',
            escalated_at: new Date().toISOString(),
            priority: 'urgent'
          })
          .eq('id', reviewId);

        if (escalateError) throw escalateError;

        // Create escalation record
        await supabase
          .from('quality_escalations')
          .insert({
            review_id: reviewId,
            escalation_reason: 'manual_escalation',
            escalated_by: user.id,
            escalated_at: new Date().toISOString(),
            escalation_type: 'manual'
          });

        // Notify admins
        const { data: admins } = await supabase
          .from('profiles')
          .select('id')
          .eq('role', 'admin');

        if (admins && admins.length > 0) {
          const notifications = admins.map(admin => ({
            recipient_id: admin.id,
            notification_type: 'quality_escalation',
            title: 'Manual Review Escalation',
            message: `Quality review ${reviewId} has been manually escalated`,
            priority: 'urgent',
            metadata: {
              review_id: reviewId,
              escalated_by: user.id,
              escalation_reason: 'manual_escalation'
            }
          }));

          await supabase
            .from('workflow_notifications')
            .insert(notifications);
        }

        return NextResponse.json({
          success: true,
          message: 'Review escalated successfully'
        });

      case 'acknowledge_alert':
        // Acknowledge an SLA alert
        if (!alertId) {
          return NextResponse.json(
            { error: 'Alert ID is required' },
            { status: 400 }
          );
        }

        const { error: ackError } = await supabase
          .from('sla_alerts')
          .update({
            acknowledged: true,
            acknowledged_by: user.id,
            acknowledged_at: new Date().toISOString()
          })
          .eq('id', alertId);

        if (ackError) throw ackError;

        return NextResponse.json({
          success: true,
          message: 'Alert acknowledged'
        });

      case 'run_escalation_check':
        // Run the automatic escalation function
        const { data: escalationResult, error: escalationError } = await supabase
          .rpc('escalate_overdue_reviews');

        if (escalationError) throw escalationError;

        return NextResponse.json({
          success: true,
          message: `Escalated ${escalationResult || 0} overdue reviews`,
          escalated_count: escalationResult || 0
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in SLA monitor POST API:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
