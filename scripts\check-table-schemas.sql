-- Check Table Schemas for Realtime Compatibility
-- Run this in your Supabase Dashboard SQL Editor to see what tables exist

-- 1. List all tables in the public schema
SELECT 
  table_name,
  table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- 2. Check specific columns in messaging tables
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('messages', 'conversation_messages', 'project_messages')
ORDER BY table_name, ordinal_position;

-- 3. Check if realtime tables exist
SELECT 
  table_name,
  CASE 
    WHEN table_name IN (
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    ) THEN 'EXISTS'
    ELSE 'MISSING'
  END as status
FROM (
  VALUES 
    ('projects'),
    ('project_proposals_enhanced'),
    ('project_briefs'),
    ('messages'),
    ('conversation_messages'),
    ('notifications'),
    ('admin_messages'),
    ('portfolio_projects'),
    ('transactions'),
    ('profiles')
) AS t(table_name)
ORDER BY table_name;

-- 4. Check RLS status for key tables
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled,
  CASE 
    WHEN rowsecurity THEN 'RLS ENABLED'
    ELSE 'RLS DISABLED'
  END as rls_status
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN (
  'projects', 'messages', 'notifications', 'admin_messages', 
  'portfolio_projects', 'transactions', 'profiles'
)
ORDER BY tablename;

-- 5. Check for REPLICA IDENTITY (needed for realtime)
SELECT 
  c.relname as table_name,
  CASE c.relreplident
    WHEN 'd' THEN 'DEFAULT'
    WHEN 'n' THEN 'NOTHING'
    WHEN 'f' THEN 'FULL'
    WHEN 'i' THEN 'INDEX'
  END as replica_identity
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public'
AND c.relkind = 'r'
AND c.relname IN (
  'projects', 'messages', 'conversation_messages', 'notifications', 
  'admin_messages', 'portfolio_projects', 'transactions', 'profiles'
)
ORDER BY c.relname;
