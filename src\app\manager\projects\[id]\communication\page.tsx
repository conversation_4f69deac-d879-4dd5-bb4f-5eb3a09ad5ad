"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  MessageSquare,
  Send,
  Users,
  Phone,
  Video,
  Mail,
  Calendar,
  FileText,
  Plus,
  Search,
  Filter,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Settings,
  Archive
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  client: {
    id: string;
    full_name: string;
    email: string;
    phone: string;
  };
  designer: {
    id: string;
    full_name: string;
    email: string;
    phone: string;
  };
}

interface Message {
  id: string;
  sender_id: string;
  recipient_id: string;
  message: string;
  message_type: string;
  status: string;
  created_at: string;
  sender: {
    full_name: string;
    role: string;
  };
  recipient: {
    full_name: string;
    role: string;
  };
}

interface Meeting {
  id: string;
  title: string;
  description: string;
  scheduled_at: string;
  duration: number;
  meeting_type: string;
  status: string;
  participants: string[];
  meeting_url: string | null;
  created_at: string;
}

export default function ProjectCommunicationPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'messages' | 'meetings' | 'contacts'>('messages');
  const [newMessage, setNewMessage] = useState('');
  const [selectedRecipient, setSelectedRecipient] = useState<string>('');
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'manager' && projectId) {
      fetchProjectData();
    }
  }, [user, profile, projectId]);

  const fetchProjectData = async () => {
    try {
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          id, title,
          client:profiles!projects_client_id_fkey(id, full_name, email, phone),
          designer:profiles!projects_designer_id_fkey(id, full_name, email, phone)
        `)
        .eq('id', projectId)
        .eq('manager_id', user?.id)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // Fetch messages related to this project
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select(`
          *,
          sender:profiles!messages_sender_id_fkey(full_name, role),
          recipient:profiles!messages_recipient_id_fkey(full_name, role)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (messagesError) throw messagesError;
      setMessages(messagesData || []);

      // Fetch meetings for this project
      const { data: meetingsData, error: meetingsError } = await supabase
        .from('project_meetings')
        .select('*')
        .eq('project_id', projectId)
        .order('scheduled_at', { ascending: false });

      if (meetingsError) throw meetingsError;
      setMeetings(meetingsData || []);

    } catch (error) {
      console.error('Error fetching project data:', error);
      router.push('/manager/projects');
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedRecipient || !project) return;

    setSendingMessage(true);
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          sender_id: user?.id,
          recipient_id: selectedRecipient,
          project_id: projectId,
          message: newMessage.trim(),
          message_type: 'text',
          status: 'sent'
        });

      if (error) throw error;

      // Send notification
      await supabase.from('notifications').insert({
        user_id: selectedRecipient,
        type: 'new_message',
        title: 'New Project Message',
        message: `Manager sent you a message about ${project.title}`,
        data: { project_id: projectId, sender_id: user?.id }
      });

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: projectId,
        activity_type: 'message_sent',
        description: 'Sent message to project participant',
        outcome: 'message_sent'
      });

      setNewMessage('');
      setSelectedRecipient('');
      fetchProjectData();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSendingMessage(false);
    }
  };

  const scheduleMeeting = async (meetingData: any) => {
    try {
      const { error } = await supabase
        .from('project_meetings')
        .insert({
          project_id: projectId,
          title: meetingData.title,
          description: meetingData.description,
          scheduled_at: meetingData.scheduled_at,
          duration: meetingData.duration,
          meeting_type: meetingData.meeting_type,
          status: 'scheduled',
          participants: [user?.id, project?.client.id, project?.designer.id],
          created_by: user?.id
        });

      if (error) throw error;

      // Send notifications to participants
      if (project) {
        await supabase.from('notifications').insert([
          {
            user_id: project.client.id,
            type: 'meeting_scheduled',
            title: 'Meeting Scheduled',
            message: `A meeting has been scheduled for ${project.title}`,
            data: { project_id: projectId, meeting_type: meetingData.meeting_type }
          },
          {
            user_id: project.designer.id,
            type: 'meeting_scheduled',
            title: 'Meeting Scheduled',
            message: `A meeting has been scheduled for ${project.title}`,
            data: { project_id: projectId, meeting_type: meetingData.meeting_type }
          }
        ]);
      }

      fetchProjectData();
    } catch (error) {
      console.error('Error scheduling meeting:', error);
    }
  };

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'read':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getMeetingStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (status) {
      case 'scheduled':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'in_progress':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'completed':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Project Not Found</h2>
          <p className="text-gray-600 mb-4">The project could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <MessageSquare className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Project Communication</h1>
            <p className="text-gray-600 mt-1">{project.title}</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => setActiveTab('messages')}
          >
            <MessageSquare className="h-5 w-5" />
            Send Message
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => {
              const meetingData = {
                title: `${project.title} - Project Meeting`,
                description: 'Project discussion meeting',
                scheduled_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                duration: 60,
                meeting_type: 'video_call'
              };
              scheduleMeeting(meetingData);
            }}
          >
            <Video className="h-5 w-5" />
            Schedule Meeting
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => window.location.href = `mailto:${project.client.email},${project.designer.email}?subject=${encodeURIComponent(`Re: ${project.title}`)}`}
          >
            <Mail className="h-5 w-5" />
            Send Email
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-center p-4"
            onClick={() => setActiveTab('contacts')}
          >
            <Users className="h-5 w-5" />
            View Contacts
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'messages', label: 'Messages', icon: MessageSquare },
              { id: 'meetings', label: 'Meetings', icon: Calendar },
              { id: 'contacts', label: 'Contacts', icon: Users }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-brown-500 text-brown-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'messages' && (
            <div className="space-y-6">
              {/* Send Message Form */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Send New Message</h3>
                <form onSubmit={sendMessage} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Send to:
                    </label>
                    <select
                      value={selectedRecipient}
                      onChange={(e) => setSelectedRecipient(e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      required
                    >
                      <option value="">Select recipient...</option>
                      <option value={project.client.id}>
                        {project.client.full_name} (Client)
                      </option>
                      <option value={project.designer.id}>
                        {project.designer.full_name} (Designer)
                      </option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Message:
                    </label>
                    <textarea
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type your message..."
                      rows={4}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={!newMessage.trim() || !selectedRecipient || sendingMessage}
                    className="flex items-center gap-2"
                  >
                    {sendingMessage ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                    Send Message
                  </Button>
                </form>
              </div>

              {/* Messages List */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Messages</h3>
                {messages.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No messages yet</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div key={message.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <div>
                              <h4 className="font-medium text-gray-900">
                                {message.sender.full_name}
                              </h4>
                              <p className="text-sm text-gray-600">
                                To: {message.recipient.full_name} ({message.recipient.role})
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getMessageStatusIcon(message.status)}
                            <span className="text-xs text-gray-500">
                              {new Date(message.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <p className="text-gray-700">{message.message}</p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'meetings' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Scheduled Meetings</h3>
                <Button
                  onClick={() => {
                    const title = prompt('Meeting title:');
                    if (title) {
                      const meetingData = {
                        title,
                        description: 'Project meeting',
                        scheduled_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                        duration: 60,
                        meeting_type: 'video_call'
                      };
                      scheduleMeeting(meetingData);
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Schedule Meeting
                </Button>
              </div>

              {meetings.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No meetings scheduled</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {meetings.map((meeting) => (
                    <div key={meeting.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{meeting.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{meeting.description}</p>
                        </div>
                        <span className={getMeetingStatusBadge(meeting.status)}>
                          {meeting.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(meeting.scheduled_at).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          <span>{meeting.duration} minutes</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Video className="h-4 w-4" />
                          <span className="capitalize">{meeting.meeting_type.replace('_', ' ')}</span>
                        </div>
                      </div>

                      {meeting.meeting_url && (
                        <div className="mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(meeting.meeting_url!, '_blank')}
                            className="flex items-center gap-2"
                          >
                            <Video className="h-4 w-4" />
                            Join Meeting
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'contacts' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Project Team Contacts</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Client Contact */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{project.client.full_name}</h4>
                      <p className="text-sm text-gray-600">Client</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-700">{project.client.email}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `mailto:${project.client.email}`}
                      >
                        Email
                      </Button>
                    </div>

                    {project.client.phone && (
                      <div className="flex items-center gap-3">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-700">{project.client.phone}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.href = `tel:${project.client.phone}`}
                        >
                          Call
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Designer Contact */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                      <Users className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{project.designer.full_name}</h4>
                      <p className="text-sm text-gray-600">Designer</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-700">{project.designer.email}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `mailto:${project.designer.email}`}
                      >
                        Email
                      </Button>
                    </div>

                    {project.designer.phone && (
                      <div className="flex items-center gap-3">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-700">{project.designer.phone}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.href = `tel:${project.designer.phone}`}
                        >
                          Call
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Communication Guidelines */}
              <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                <h4 className="text-lg font-semibold text-blue-900 mb-3">Communication Guidelines</h4>
                <div className="text-blue-800 space-y-2 text-sm">
                  <p>• <strong>Response Time:</strong> Aim to respond to messages within 24 hours</p>
                  <p>• <strong>Meeting Etiquette:</strong> Send agenda 24 hours before scheduled meetings</p>
                  <p>• <strong>Escalation:</strong> Contact manager immediately for urgent issues</p>
                  <p>• <strong>Documentation:</strong> Keep records of all important decisions and changes</p>
                  <p>• <strong>Professional Tone:</strong> Maintain professional communication at all times</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
