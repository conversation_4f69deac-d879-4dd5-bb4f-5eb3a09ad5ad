import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/portfolio
 * Gets all portfolio projects for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Determine which projects to fetch based on role and query parameters
    let query = supabase
      .from('portfolio_projects')
      .select(`
        *,
        images:portfolio_images(*)
      `);
    
    // If admin and designer_id is provided, get that designer's projects
    const designerId = request.nextUrl.searchParams.get('designer_id');
    if (profile.role === 'admin' && designerId) {
      query = query.eq('designer_id', designerId);
    } else if (profile.role === 'designer') {
      // Designers can only see their own projects
      query = query.eq('designer_id', user.id);
    } else if (designerId) {
      // Clients can see specific designer's projects
      query = query.eq('designer_id', designerId);
    } else {
      // If no designer_id is provided and not an admin or designer, return error
      return NextResponse.json(
        { error: 'Designer ID is required' },
        { status: 400 }
      );
    }
    
    // Add any filters from query parameters
    const category = request.nextUrl.searchParams.get('category');
    if (category) {
      query = query.eq('category', category);
    }
    
    const featured = request.nextUrl.searchParams.get('featured');
    if (featured === 'true') {
      query = query.eq('featured', true);
    }
    
    // Order by created_at descending (newest first)
    query = query.order('created_at', { ascending: false });
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching portfolio projects:', error);
      return NextResponse.json(
        { error: 'Failed to fetch portfolio projects' },
        { status: 500 }
      );
    }
    
    // Process the data to find cover images
    const processedData = data.map(project => {
      const images = project.images || [];
      const coverImage = images.find(img => img.is_cover);
      
      return {
        ...project,
        cover_image: coverImage ? coverImage.image_url : (images.length > 0 ? images[0].image_url : null)
      };
    });
    
    return NextResponse.json(processedData, { status: 200 });
  } catch (error) {
    console.error('Error in GET /api/portfolio:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/portfolio
 * Creates a new portfolio project
 * 
 * Request body:
 * {
 *   title: string;
 *   description?: string;
 *   category?: string;
 *   client_name?: string;
 *   completion_date?: string;
 *   featured?: boolean;
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the user's profile to check their role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      );
    }
    
    // Only designers can create portfolio projects
    if (profile.role !== 'designer') {
      return NextResponse.json(
        { error: 'Only designers can create portfolio projects' },
        { status: 403 }
      );
    }
    
    const { title, description, category, client_name, completion_date, featured } = await request.json();
    
    // Validate required fields
    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }
    
    // Create the portfolio project
    const { data, error } = await supabase
      .from('portfolio_projects')
      .insert({
        designer_id: user.id,
        title,
        description: description || null,
        category: category || null,
        client_name: client_name || null,
        completion_date: completion_date || null,
        featured: featured || false
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating portfolio project:', error);
      return NextResponse.json(
        { error: 'Failed to create portfolio project' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/portfolio:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
