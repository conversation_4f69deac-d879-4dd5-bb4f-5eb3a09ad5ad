"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  ChevronRight,
  Home,
  LayoutDashboard,
  Users,
  FolderKanban,
  CreditCard,
  Settings,
  MessageSquare,
  AlertTriangle,
  Bell,
  BarChart3,
  Calculator,
  Target,
  TrendingUp,
  UserPlus,
  FileText,
  FileSearch
} from "lucide-react";

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: any;
}

export function AdminBreadcrumb() {
  const pathname = usePathname();

  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Admin', href: '/admin', icon: Home }
    ];

    // Map route segments to breadcrumb items
    const routeMap: Record<string, { label: string; icon?: any }> = {
      'dashboard': { label: 'Dashboard', icon: LayoutDashboard },
      'users': { label: 'User Management', icon: Users },
      'projects': { label: 'Project Management', icon: FolderKanban },
      'finance': { label: 'Financial Management', icon: CreditCard },
      'messages': { label: 'Messages', icon: MessageSquare },
      'disputes': { label: 'Dispute Management', icon: AlertTriangle },
      'settings': { label: 'Settings', icon: Settings },
      'notifications': { label: 'Notifications', icon: Bell },
      'designers': { label: 'Designers', icon: Users },
      'applications': { label: 'Applications', icon: UserPlus },
      'tracking': { label: 'Request Management', icon: FileSearch },
      'milestones': { label: 'Milestones', icon: Target },
      'templates': { label: 'Templates', icon: FileText },
      'reports': { label: 'Reports', icon: BarChart3 },
      'fees': { label: 'Fee Management', icon: Calculator },
      'payouts': { label: 'Payouts', icon: TrendingUp },
      'active': { label: 'Active Projects', icon: BarChart3 },
      'roles': { label: 'User Roles', icon: Users },
      'platform': { label: 'Platform Configuration', icon: Settings },
      'help': { label: 'Help & Support', icon: Settings },
      'profile': { label: 'Profile Settings', icon: Settings }
    };

    let currentPath = '';

    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;

      // Skip the 'admin' segment since we already have it as the root breadcrumb
      if (segment === 'admin') {
        return;
      }

      if (routeMap[segment]) {
        breadcrumbs.push({
          label: routeMap[segment].label,
          href: currentPath,
          icon: routeMap[segment].icon
        });
      } else {
        // Fallback for unknown segments
        breadcrumbs.push({
          label: segment.charAt(0).toUpperCase() + segment.slice(1),
          href: currentPath
        });
      }
    });

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs();

  // Don't show breadcrumbs on the main admin page
  if (pathname === '/admin' || pathname === '/admin/dashboard') {
    return null;
  }

  return (
    <motion.nav
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white border-b border-gray-200 px-6 py-4"
    >
      <ol className="flex items-center space-x-2 text-sm">
        {breadcrumbs.map((item, index) => {
          const isLast = index === breadcrumbs.length - 1;
          const Icon = item.icon;

          return (
            <li key={`breadcrumb-${index}-${item.href}`} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
              )}

              {isLast ? (
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2, delay: index * 0.1 }}
                  className="flex items-center text-brown-600 font-medium"
                >
                  {Icon && <Icon className="h-4 w-4 mr-2" />}
                  {item.label}
                </motion.span>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className="flex items-center text-gray-500 hover:text-brown-600 transition-colors duration-200"
                  >
                    {Icon && <Icon className="h-4 w-4 mr-2" />}
                    {item.label}
                  </Link>
                </motion.div>
              )}
            </li>
          );
        })}
      </ol>
    </motion.nav>
  );
}
