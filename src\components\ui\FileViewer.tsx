"use client";

import { useState, useCallback } from "react";
import { Button } from "@/components/ui/button";
import {
  X,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Loader2,
  AlertCircle,
  FileText,
  Image as ImageIcon,
  ExternalLink,
  Maximize2
} from "lucide-react";

interface FileViewerProps {
  isOpen: boolean;
  onClose: () => void;
  fileUrl: string;
  fileName: string;
  fileType: string;
}

export default function FileViewer({ isOpen, onClose, fileUrl, fileName, fileType }: FileViewerProps) {
  const [scale, setScale] = useState<number>(1.0);
  const [rotation, setRotation] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const isPDF = fileType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf');
  const isImage = fileType.startsWith('image/') || /\.(jpg|jpeg|png|gif|webp)$/i.test(fileName);

  const onImageLoad = useCallback(() => {
    setLoading(false);
    setError(null);
  }, []);

  const onImageError = useCallback(() => {
    setError('Failed to load image');
    setLoading(false);
  }, []);

  const onPDFLoad = useCallback(() => {
    setLoading(false);
    setError(null);
  }, []);

  const onPDFError = useCallback(() => {
    setError('Failed to load PDF document');
    setLoading(false);
  }, []);

  const changeScale = (scaleOffset: number) => {
    setScale(prevScale => {
      const newScale = prevScale + scaleOffset;
      return Math.max(0.5, Math.min(newScale, 3.0));
    });
  };

  const rotate = () => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  };

  const downloadFile = () => {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetView = () => {
    setScale(1.0);
    setRotation(0);
  };

  const openInNewTab = () => {
    window.open(fileUrl, '_blank');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl h-[85vh] w-full mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center">
            {isPDF ? (
              <FileText className="h-5 w-5 text-red-600 mr-2" />
            ) : (
              <ImageIcon className="h-5 w-5 text-blue-600 mr-2" />
            )}
            <h3 className="text-lg font-semibold truncate">{fileName}</h3>
          </div>

          <div className="flex items-center space-x-2">
            {/* Controls for images only */}
            {isImage && (
              <div className="flex items-center space-x-1 mr-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeScale(-0.1)}
                  disabled={scale <= 0.5}
                  title="Zoom Out"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>

                <span className="text-sm font-medium px-2 min-w-[60px] text-center">
                  {Math.round(scale * 100)}%
                </span>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changeScale(0.1)}
                  disabled={scale >= 3.0}
                  title="Zoom In"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={rotate}
                  title="Rotate"
                >
                  <RotateCw className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetView}
                  title="Reset View"
                >
                  Reset
                </Button>
              </div>
            )}

            {/* PDF controls */}
            {isPDF && (
              <div className="flex items-center space-x-1 mr-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={openInNewTab}
                  title="Open in New Tab"
                >
                  <ExternalLink className="h-4 w-4" />
                  Open in New Tab
                </Button>
              </div>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={downloadFile}
              title="Download"
            >
              <Download className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              title="Close"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-gray-100 relative">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="animate-spin h-12 w-12 text-primary mx-auto mb-4" />
                <p className="text-gray-600">Loading {isPDF ? 'PDF' : 'image'}...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 font-medium">{error}</p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setError(null);
                    setLoading(true);
                  }}
                  className="mt-4"
                >
                  Retry
                </Button>
              </div>
            </div>
          )}

          {!error && (
            <div className="flex items-center justify-center min-h-full p-4">
              {isPDF ? (
                <div className="w-full h-full">
                  <iframe
                    src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH`}
                    className="w-full h-full border-0 shadow-lg rounded"
                    title={fileName}
                    onLoad={onPDFLoad}
                    onError={onPDFError}
                    style={{ minHeight: '70vh' }}
                  />
                </div>
              ) : isImage ? (
                <img
                  src={fileUrl}
                  alt={fileName}
                  onLoad={onImageLoad}
                  onError={onImageError}
                  style={{
                    transform: `scale(${scale}) rotate(${rotation}deg)`,
                    transformOrigin: 'center center',
                    transition: 'transform 0.2s ease-in-out',
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain'
                  }}
                  className="shadow-lg"
                />
              ) : (
                <div className="text-center">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">File type not supported for preview</p>
                  <Button onClick={downloadFile} className="mt-4">
                    <Download className="h-4 w-4 mr-2" />
                    Download File
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>


      </div>
    </div>
  );
}
