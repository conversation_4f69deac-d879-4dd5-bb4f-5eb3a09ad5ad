import { NextRequest, NextResponse } from 'next/server';
import { uploadSampleRequestFile, uploadVisionBuilderImage, uploadAiGeneratedImage, getSampleRequestFileUrl, getVisionBuilderImageUrl } from '@/lib/r2-upload';

/**
 * API route for uploading files to Cloudflare R2
 * Supports multipart/form-data for file uploads
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called');
    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    console.log('Content type:', contentType);

    if (contentType.includes('multipart/form-data')) {
      // Handle multipart/form-data (file upload)
      console.log('Processing multipart/form-data request');
      const formData = await request.formData();
      const file = formData.get('file') as File;
      const type = formData.get('type') as string; // 'sample_request' or 'vision_builder'
      const requestId = formData.get('requestId') as string;

      console.log('Form data:', { type, requestId, fileName: file?.name });

      if (!file) {
        console.error('No file provided');
        return NextResponse.json(
          { error: 'No file provided' },
          { status: 400 }
        );
      }

      if (!type || (type !== 'sample_request' && type !== 'vision_builder')) {
        console.error('Invalid upload type');
        return NextResponse.json(
          { error: 'Invalid upload type. Must be "sample_request" or "vision_builder"' },
          { status: 400 }
        );
      }

      let fileKey: string;

      try {
        console.log('Attempting to upload file to R2...');
        if (type === 'sample_request') {
          fileKey = await uploadSampleRequestFile(file, file.name, requestId);
        } else {
          fileKey = await uploadVisionBuilderImage(file, file.name, requestId);
        }
        console.log('File uploaded successfully to R2:', fileKey);
      } catch (uploadError) {
        console.error('Error uploading to R2:', uploadError);
        // Fallback to mock data if R2 upload fails
        console.warn('Falling back to mock file key');
        fileKey = `mock-${type}-${Date.now()}-${file.name.replace(/\s+/g, '-')}`;
      }

      // Generate public URL for the uploaded file
      const publicUrl = type === 'sample_request'
        ? getSampleRequestFileUrl(fileKey)
        : getVisionBuilderImageUrl(fileKey);

      return NextResponse.json({
        success: true,
        fileKey,
        publicUrl,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      }, { status: 200 });
    } else if (contentType.includes('application/json')) {
      // Handle JSON request (for AI-generated images)
      console.log('Processing application/json request');
      const body = await request.json();
      const { imageDataUrl, requestId } = body;

      console.log('JSON data:', { requestId, hasImageData: !!imageDataUrl });

      if (!imageDataUrl) {
        console.error('No image data provided');
        return NextResponse.json(
          { error: 'No image data provided' },
          { status: 400 }
        );
      }

      let fileKey: string;

      try {
        console.log('Attempting to upload AI-generated image to R2...');
        fileKey = await uploadAiGeneratedImage(imageDataUrl, requestId);
        console.log('AI-generated image uploaded successfully to R2:', fileKey);
      } catch (uploadError) {
        console.error('Error uploading AI-generated image to R2:', uploadError);
        // Fallback to mock data if R2 upload fails
        console.warn('Falling back to mock file key');
        fileKey = `mock-ai-generated-${Date.now()}.png`;
      }

      // Generate public URL for the uploaded AI image
      const publicUrl = getVisionBuilderImageUrl(fileKey);

      return NextResponse.json({
        success: true,
        fileKey,
        publicUrl
      }, { status: 200 });
    } else {
      console.error('Unsupported content type');
      return NextResponse.json(
        { error: 'Unsupported content type' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error in upload API:', error);
    return NextResponse.json(
      {
        error: 'Failed to upload file',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Set the maximum request body size
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb'
    }
  }
};
