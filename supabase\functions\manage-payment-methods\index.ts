import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import Stripe from 'https://esm.sh/stripe@12.4.0?dts'

interface RequestBody {
  action: 'create' | 'update' | 'delete' | 'set-default'
  paymentMethodId?: string
  makeDefault?: boolean
}

serve(async (req) => {
  try {
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the service role for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized', details: userError }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
      apiVersion: '2023-10-16',
    })

    // Get user profile to get customer ID
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.stripe_customer_id) {
      return new Response(
        JSON.stringify({ error: 'Stripe customer not found', details: profileError }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const customerId = profile.stripe_customer_id

    // Parse the request body
    const { action, paymentMethodId, makeDefault }: RequestBody = await req.json()

    // Handle different actions
    switch (action) {
      case 'create': {
        // Create a SetupIntent
        const setupIntent = await stripe.setupIntents.create({
          customer: customerId,
          payment_method_types: ['card'],
          usage: 'off_session',
        })

        return new Response(
          JSON.stringify({ clientSecret: setupIntent.client_secret }),
          { status: 200, headers: { 'Content-Type': 'application/json' } }
        )
      }

      case 'update': {
        if (!paymentMethodId) {
          return new Response(
            JSON.stringify({ error: 'Payment method ID is required' }),
            { status: 400, headers: { 'Content-Type': 'application/json' } }
          )
        }

        // Update payment method in Stripe (attach to customer if not already)
        await stripe.paymentMethods.attach(paymentMethodId, {
          customer: customerId,
        })

        // If makeDefault is true, update the customer's default payment method
        if (makeDefault) {
          await stripe.customers.update(customerId, {
            invoice_settings: {
              default_payment_method: paymentMethodId,
            },
          })

          // Update all payment methods in the database
          await supabaseAdmin
            .from('payment_methods')
            .update({ is_default: false })
            .eq('user_id', user.id)

          // Set the current one as default
          await supabaseAdmin
            .from('payment_methods')
            .update({ is_default: true })
            .eq('stripe_payment_method_id', paymentMethodId)
            .eq('user_id', user.id)
        }

        return new Response(
          JSON.stringify({ success: true, message: 'Payment method updated' }),
          { status: 200, headers: { 'Content-Type': 'application/json' } }
        )
      }

      case 'delete': {
        if (!paymentMethodId) {
          return new Response(
            JSON.stringify({ error: 'Payment method ID is required' }),
            { status: 400, headers: { 'Content-Type': 'application/json' } }
          )
        }

        // Check if this payment method belongs to the user
        const { data: paymentMethod, error: pmError } = await supabaseAdmin
          .from('payment_methods')
          .select('id, is_default')
          .eq('stripe_payment_method_id', paymentMethodId)
          .eq('user_id', user.id)
          .single()

        if (pmError || !paymentMethod) {
          return new Response(
            JSON.stringify({ error: 'Payment method not found', details: pmError }),
            { status: 404, headers: { 'Content-Type': 'application/json' } }
          )
        }

        // Detach the payment method from the customer in Stripe
        await stripe.paymentMethods.detach(paymentMethodId)

        // Delete the payment method from the database
        await supabaseAdmin
          .from('payment_methods')
          .delete()
          .eq('stripe_payment_method_id', paymentMethodId)
          .eq('user_id', user.id)

        // If this was the default payment method, set another one as default
        if (paymentMethod.is_default) {
          const { data: otherPaymentMethods } = await supabaseAdmin
            .from('payment_methods')
            .select('id, stripe_payment_method_id')
            .eq('user_id', user.id)
            .limit(1)

          if (otherPaymentMethods && otherPaymentMethods.length > 0) {
            const newDefaultId = otherPaymentMethods[0].stripe_payment_method_id

            // Update in Stripe
            await stripe.customers.update(customerId, {
              invoice_settings: {
                default_payment_method: newDefaultId,
              },
            })

            // Update in database
            await supabaseAdmin
              .from('payment_methods')
              .update({ is_default: true })
              .eq('stripe_payment_method_id', newDefaultId)
              .eq('user_id', user.id)
          }
        }

        return new Response(
          JSON.stringify({ success: true, message: 'Payment method deleted' }),
          { status: 200, headers: { 'Content-Type': 'application/json' } }
        )
      }

      case 'set-default': {
        if (!paymentMethodId) {
          return new Response(
            JSON.stringify({ error: 'Payment method ID is required' }),
            { status: 400, headers: { 'Content-Type': 'application/json' } }
          )
        }

        // Check if this payment method belongs to the user
        const { data: paymentMethod, error: pmError } = await supabaseAdmin
          .from('payment_methods')
          .select('id')
          .eq('stripe_payment_method_id', paymentMethodId)
          .eq('user_id', user.id)
          .single()

        if (pmError || !paymentMethod) {
          return new Response(
            JSON.stringify({ error: 'Payment method not found', details: pmError }),
            { status: 404, headers: { 'Content-Type': 'application/json' } }
          )
        }

        // Update in Stripe
        await stripe.customers.update(customerId, {
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        })

        // Update all payment methods in the database
        await supabaseAdmin
          .from('payment_methods')
          .update({ is_default: false })
          .eq('user_id', user.id)

        // Set the current one as default
        await supabaseAdmin
          .from('payment_methods')
          .update({ is_default: true })
          .eq('stripe_payment_method_id', paymentMethodId)
          .eq('user_id', user.id)

        return new Response(
          JSON.stringify({ success: true, message: 'Default payment method updated' }),
          { status: 200, headers: { 'Content-Type': 'application/json' } }
        )
      }

      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Error managing payment methods:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
})
