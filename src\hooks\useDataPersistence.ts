"use client";

import { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { dashboardKeys } from './useDashboardData';

// Enhanced data persistence hook for maintaining state across sessions
export function useDataPersistence() {
  const queryClient = useQueryClient();

  // Save critical data to localStorage
  const saveToLocalStorage = useCallback((key: string, data: any) => {
    try {
      const serializedData = JSON.stringify({
        data,
        timestamp: Date.now(),
        version: '1.0'
      });
      localStorage.setItem(`seniors_archi_${key}`, serializedData);
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }, []);

  // Load data from localStorage
  const loadFromLocalStorage = useCallback((key: string, maxAge: number = 30 * 60 * 1000) => {
    try {
      const item = localStorage.getItem(`seniors_archi_${key}`);
      if (!item) return null;

      const parsed = JSON.parse(item);
      const age = Date.now() - parsed.timestamp;

      // Return data if it's not too old
      if (age < maxAge) {
        return parsed.data;
      } else {
        // Remove stale data
        localStorage.removeItem(`seniors_archi_${key}`);
        return null;
      }
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
      return null;
    }
  }, []);

  // Enhanced persist query cache with intelligent filtering
  const persistQueryCache = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    // Define critical data patterns with priority levels
    const criticalPatterns = {
      high: ['auth', 'profile', 'session'],
      medium: ['dashboard', 'stats', 'projects', 'proposals'],
      low: ['messages', 'notifications', 'portfolio']
    };

    // Filter and prioritize queries
    const criticalQueries = queries.filter(query => {
      const queryKeyStr = JSON.stringify(query.queryKey);
      return Object.values(criticalPatterns).flat().some(pattern =>
        queryKeyStr.includes(pattern)
      );
    });

    // Sort by priority and data freshness
    const sortedQueries = criticalQueries.sort((a, b) => {
      const aKeyStr = JSON.stringify(a.queryKey);
      const bKeyStr = JSON.stringify(b.queryKey);

      // Priority scoring
      const getPriority = (keyStr: string) => {
        if (criticalPatterns.high.some(p => keyStr.includes(p))) return 3;
        if (criticalPatterns.medium.some(p => keyStr.includes(p))) return 2;
        return 1;
      };

      const aPriority = getPriority(aKeyStr);
      const bPriority = getPriority(bKeyStr);

      if (aPriority !== bPriority) return bPriority - aPriority;

      // If same priority, sort by freshness
      return (b.state.dataUpdatedAt || 0) - (a.state.dataUpdatedAt || 0);
    });

    // Limit to top 50 queries to avoid localStorage bloat
    const persistData = sortedQueries.slice(0, 50).map(query => ({
      queryKey: query.queryKey,
      data: query.state.data,
      dataUpdatedAt: query.state.dataUpdatedAt,
      staleTime: query.options.staleTime || 0,
      priority: JSON.stringify(query.queryKey).includes('auth') ? 'high' : 'medium'
    }));

    saveToLocalStorage('query_cache', persistData);

    // Also persist user preferences and settings
    const userPreferences = {
      lastActiveRoute: window.location.pathname,
      timestamp: Date.now(),
      userAgent: navigator.userAgent.substring(0, 100) // Truncated for privacy
    };

    saveToLocalStorage('user_preferences', userPreferences);
  }, [queryClient, saveToLocalStorage]);

  // Restore query cache from localStorage
  const restoreQueryCache = useCallback(() => {
    const cachedData = loadFromLocalStorage('query_cache', 15 * 60 * 1000); // 15 minutes max age
    
    if (cachedData && Array.isArray(cachedData)) {
      cachedData.forEach(({ queryKey, data, dataUpdatedAt }) => {
        if (data && queryKey) {
          queryClient.setQueryData(queryKey, data, {
            updatedAt: dataUpdatedAt,
          });
        }
      });
    }
  }, [queryClient, loadFromLocalStorage]);

  // Enhanced background sync with intelligent prioritization and reduced aggressiveness
  const backgroundSync = useCallback(async () => {
    const now = Date.now();
    const lastSync = loadFromLocalStorage('last_background_sync', 0);

    // Only sync if enough time has passed (5 minutes minimum)
    if (now - lastSync < 5 * 60 * 1000) {
      console.log('⏭️ Skipping background sync - too recent');
      return;
    }

    try {
      console.log('🔄 Starting intelligent background sync...');

      // Get all cached queries
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();

      // Categorize queries by importance and staleness (more selective)
      const staleQueries = queries.filter(query => {
        const state = query.state;
        const staleTime = query.options.staleTime || 0;
        const age = now - (state.dataUpdatedAt || 0);
        const isActive = query.getObserversCount() > 0;
        const isImportant = ['auth', 'profile', 'availability', 'dashboard'].some(key =>
          JSON.stringify(query.queryKey).includes(key)
        );

        // Only sync CRITICAL queries that are VERY stale (30+ minutes)
        const isCritical = ['auth', 'profile'].some(key =>
          JSON.stringify(query.queryKey).includes(key)
        );

        // Much more conservative: only sync critical queries after 30 minutes, others after 60 minutes
        const criticalStaleTime = 30 * 60 * 1000; // 30 minutes
        const normalStaleTime = 60 * 60 * 1000; // 60 minutes

        const shouldSync = isCritical
          ? age > criticalStaleTime
          : age > normalStaleTime;

        return shouldSync &&
               state.data !== undefined &&
               !state.isFetching &&
               isActive;
      });

      if (staleQueries.length === 0) {
        console.log('✅ No important stale queries to sync');
        saveToLocalStorage('last_background_sync', now);
        return;
      }

      console.log(`🔄 Syncing ${staleQueries.length} important stale queries`);

      // Separate critical and non-critical queries
      const criticalQueries = staleQueries.filter(query => {
        const keyStr = JSON.stringify(query.queryKey);
        return keyStr.includes('auth') || keyStr.includes('profile') || keyStr.includes('availability');
      });

      const dashboardQueries = staleQueries.filter(query => {
        const keyStr = JSON.stringify(query.queryKey);
        return keyStr.includes('dashboard') || keyStr.includes('stats');
      });

      // Process critical queries first (auth, profile, availability) - BACKGROUND ONLY
      if (criticalQueries.length > 0) {
        console.log('🔄 Background syncing critical queries (auth, profile, availability)');
        const criticalPromises = criticalQueries.map(query =>
          queryClient.fetchQuery({
            queryKey: query.queryKey,
            staleTime: 0, // Force fresh fetch
          }).catch(error => {
            console.warn(`Failed to background sync critical query ${JSON.stringify(query.queryKey)}:`, error);
          })
        );

        await Promise.allSettled(criticalPromises);

        // Small delay before dashboard queries
        if (dashboardQueries.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Process dashboard queries (less critical, limit to 1) - BACKGROUND ONLY
      if (dashboardQueries.length > 0) {
        console.log('🔄 Background syncing dashboard queries');
        const limitedDashboardQueries = dashboardQueries.slice(0, 1); // Limit to 1 dashboard query
        const dashboardPromises = limitedDashboardQueries.map(query =>
          queryClient.fetchQuery({
            queryKey: query.queryKey,
            staleTime: 0, // Force fresh fetch
          }).catch(error => {
            console.warn(`Failed to background sync dashboard query ${JSON.stringify(query.queryKey)}:`, error);
          })
        );

        await Promise.allSettled(dashboardPromises);
      }

      // Update sync timestamp
      saveToLocalStorage('last_background_sync', now);
      console.log('✅ Background sync completed');

    } catch (error) {
      console.warn('❌ Background sync failed:', error);
    }
  }, [queryClient, saveToLocalStorage, loadFromLocalStorage]);

  // Setup persistence and sync
  useEffect(() => {
    // Restore cache on mount
    restoreQueryCache();

    // Setup periodic cache persistence
    const persistInterval = setInterval(persistQueryCache, 2 * 60 * 1000); // Every 2 minutes

    // Setup background sync (DISABLED - only sync on visibility change when truly needed)
    // const syncInterval = setInterval(backgroundSync, 5 * 60 * 1000); // Every 5 minutes

    // Persist cache before page unload
    const handleBeforeUnload = () => {
      persistQueryCache();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup
    return () => {
      clearInterval(persistInterval);
      // clearInterval(syncInterval); // Disabled
      window.removeEventListener('beforeunload', handleBeforeUnload);
      persistQueryCache(); // Final persist on cleanup
    };
  }, [persistQueryCache, restoreQueryCache, backgroundSync]);

  // Handle visibility change for background sync (FIXED - much less aggressive)
  useEffect(() => {
    let visibilityTimeout: NodeJS.Timeout;
    let lastVisibilityChange = 0;
    const DEBOUNCE_DELAY = 5000; // 5 seconds (increased)
    const MIN_HIDDEN_TIME = 5 * 60 * 1000; // 5 minutes minimum hidden time before sync (increased)

    const handleVisibilityChange = () => {
      const now = Date.now();

      // Clear any pending timeout
      if (visibilityTimeout) {
        clearTimeout(visibilityTimeout);
      }

      if (document.visibilityState === 'visible') {
        // Page became visible
        const hiddenDuration = now - lastVisibilityChange;

        // Only sync if page was hidden for a very long time AND data is very stale
        if (hiddenDuration > MIN_HIDDEN_TIME) {
          // Check if we actually need to sync by looking at data staleness
          const cache = queryClient.getQueryCache();
          const allQueries = cache.getAll();
          const veryStaleQueries = allQueries.filter(query => {
            const age = now - (query.state.dataUpdatedAt || 0);
            const isVeryStale = age > 15 * 60 * 1000; // 15 minutes
            const hasData = query.state.data !== undefined;
            return hasData && isVeryStale && !query.state.isFetching;
          });

          if (veryStaleQueries.length > 0) {
            visibilityTimeout = setTimeout(() => {
              console.log(`🔄 Page visible after ${Math.round(hiddenDuration / 1000)}s absence, syncing ${veryStaleQueries.length} very stale queries...`);
              backgroundSync();
            }, DEBOUNCE_DELAY);
          } else {
            console.log(`⏭️ Page visible after ${Math.round(hiddenDuration / 1000)}s absence, but data is fresh - skipping sync`);
          }
        } else {
          console.log(`⏭️ Page visible after ${Math.round(hiddenDuration / 1000)}s absence, skipping sync (too short)`);
        }
      } else {
        // Page became hidden
        lastVisibilityChange = now;

        // Only persist cache, don't trigger any refetching
        visibilityTimeout = setTimeout(() => {
          console.log('💾 Page hidden, persisting cache...');
          persistQueryCache();
        }, DEBOUNCE_DELAY);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (visibilityTimeout) {
        clearTimeout(visibilityTimeout);
      }
    };
  }, [backgroundSync, persistQueryCache, queryClient]);

  return {
    saveToLocalStorage,
    loadFromLocalStorage,
    persistQueryCache,
    restoreQueryCache,
    backgroundSync,
  };
}

// Hook for optimistic updates
export function useOptimisticUpdates() {
  const queryClient = useQueryClient();

  const optimisticUpdate = useCallback((
    queryKey: any[],
    updateFn: (oldData: any) => any,
    rollbackData?: any
  ) => {
    // Cancel outgoing refetches
    queryClient.cancelQueries({ queryKey });

    // Snapshot previous value
    const previousData = queryClient.getQueryData(queryKey);

    // Optimistically update
    queryClient.setQueryData(queryKey, updateFn);

    // Return rollback function
    return () => {
      queryClient.setQueryData(queryKey, rollbackData || previousData);
    };
  }, [queryClient]);

  const invalidateAndRefetch = useCallback((queryKey: any[]) => {
    queryClient.invalidateQueries({ queryKey });
    return queryClient.refetchQueries({ queryKey });
  }, [queryClient]);

  return {
    optimisticUpdate,
    invalidateAndRefetch,
  };
}

// Hook for smart query invalidation
export function useSmartInvalidation() {
  const queryClient = useQueryClient();

  const invalidateRelated = useCallback((entityType: string, entityId?: string) => {
    switch (entityType) {
      case 'project':
        queryClient.invalidateQueries({ queryKey: dashboardKeys.projects() });
        if (entityId) {
          queryClient.invalidateQueries({ queryKey: dashboardKeys.project(entityId) });
          queryClient.invalidateQueries({ queryKey: dashboardKeys.messagesByProject(entityId) });
        }
        break;
      
      case 'proposal':
        queryClient.invalidateQueries({ queryKey: dashboardKeys.proposals() });
        queryClient.invalidateQueries({ queryKey: dashboardKeys.stats() });
        break;
      
      case 'user':
        queryClient.invalidateQueries({ queryKey: dashboardKeys.users() });
        if (entityId) {
          queryClient.invalidateQueries({ queryKey: dashboardKeys.userProfile(entityId) });
        }
        break;
      
      case 'message':
        if (entityId) {
          queryClient.invalidateQueries({ queryKey: dashboardKeys.messagesByProject(entityId) });
          queryClient.invalidateQueries({ queryKey: dashboardKeys.unreadCount(entityId) });
        }
        break;
      
      default:
        // Fallback: invalidate all dashboard queries
        queryClient.invalidateQueries({ queryKey: dashboardKeys.all });
    }
  }, [queryClient]);

  return { invalidateRelated };
}
