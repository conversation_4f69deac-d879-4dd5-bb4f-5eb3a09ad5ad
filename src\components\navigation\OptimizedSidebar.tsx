"use client";

import React, { useCallback, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useNavigationPrefetch } from '@/hooks/useNavigationPrefetch';
import { useNavigationTime } from '@/hooks/usePerformanceMonitoring';
import {
  LayoutDashboard,
  FolderKanban,
  FileText,
  MessageSquare,
  Bell,
  Settings,
  User,
  Users,
  DollarSign,
  Calendar,
  Star,
  Mail,
  Briefcase,
  Image,
  Heart,
  AlertTriangle,
  BarChart3,
  CreditCard,
  FileSearch
} from 'lucide-react';

interface SidebarItem {
  href: string;
  label: string;
  icon: React.ReactNode;
  prefetchKey: string;
  badge?: number;
  isNew?: boolean;
}

interface OptimizedSidebarProps {
  items: SidebarItem[];
  role: 'admin' | 'client' | 'designer';
  className?: string;
}

export function OptimizedSidebar({ items, role, className }: OptimizedSidebarProps) {
  const pathname = usePathname();
  const { prefetchOnHover, prefetchRoute } = useNavigationPrefetch();
  const { startNavigation, endNavigation } = useNavigationTime();
  const hoverTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Enhanced hover handler with debouncing
  const handleMouseEnter = useCallback((prefetchKey: string) => {
    // Clear any existing timeout for this item
    const existingTimeout = hoverTimeouts.current.get(prefetchKey);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout for prefetching
    const timeout = setTimeout(() => {
      prefetchRoute(prefetchKey);
    }, 200); // 200ms delay to avoid excessive prefetching

    hoverTimeouts.current.set(prefetchKey, timeout);
  }, [prefetchRoute]);

  const handleMouseLeave = useCallback((prefetchKey: string) => {
    // Clear timeout if user moves away quickly
    const timeout = hoverTimeouts.current.get(prefetchKey);
    if (timeout) {
      clearTimeout(timeout);
      hoverTimeouts.current.delete(prefetchKey);
    }
  }, []);

  // Enhanced click handler with performance tracking
  const handleClick = useCallback((href: string, prefetchKey: string) => {
    startNavigation(pathname);

    // Immediate prefetch on click for instant navigation
    prefetchRoute(prefetchKey);

    // Track navigation completion after a short delay
    setTimeout(() => {
      endNavigation(href);
    }, 100);
  }, [pathname, startNavigation, endNavigation, prefetchRoute]);

  // Intersection observer for automatic prefetching when items come into view
  const observerRef = useCallback((node: HTMLElement | null, prefetchKey: string) => {
    if (!node) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Prefetch when item is 20% visible
            setTimeout(() => prefetchRoute(prefetchKey), 100);
            observer.unobserve(node);
          }
        });
      },
      { threshold: 0.2 }
    );

    observer.observe(node);

    return () => observer.disconnect();
  }, [prefetchRoute]);

  return (
    <nav className={cn("space-y-1", className)}>
      {items.map((item) => {
        const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
        
        return (
          <div
            key={item.href}
            ref={(node) => observerRef(node, item.prefetchKey)}
            onMouseEnter={() => handleMouseEnter(item.prefetchKey)}
            onMouseLeave={() => handleMouseLeave(item.prefetchKey)}
          >
            <Link
              href={item.href}
              onClick={() => handleClick(item.href, item.prefetchKey)}
              className={cn(
                "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 ease-in-out",
                "hover:bg-brown-50 hover:text-brown-900 focus:outline-none focus:ring-2 focus:ring-brown-500",
                isActive
                  ? "bg-brown-50 text-brown-700 border-r-2 border-brown-700"
                  : "text-gray-600 hover:text-brown-900"
              )}
              prefetch={true} // Enable Next.js prefetching as backup
            >
              <span className={cn(
                "mr-3 flex-shrink-0 transition-colors duration-200",
                isActive ? "text-brown-500" : "text-gray-400 group-hover:text-brown-500"
              )}>
                {item.icon}
              </span>
              
              <span className="flex-1 leading-tight break-words">
                {item.label}
              </span>
              
              {/* Badge for notifications/counts */}
              {item.badge && item.badge > 0 && (
                <span className={cn(
                  "ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full",
                  isActive
                    ? "bg-brown-100 text-brown-800"
                    : "bg-amber-100 text-amber-800"
                )}>
                  {item.badge > 99 ? '99+' : item.badge}
                </span>
              )}
              
              {/* New feature indicator */}
              {item.isNew && (
                <span className="ml-2 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  New
                </span>
              )}
            </Link>
          </div>
        );
      })}
    </nav>
  );
}

// Role-specific sidebar configurations
export const ADMIN_SIDEBAR_ITEMS: SidebarItem[] = [
  {
    href: '/admin/dashboard',
    label: 'Dashboard',
    icon: <LayoutDashboard className="w-5 h-5" />,
    prefetchKey: 'dashboard'
  },
  {
    href: '/admin/projects',
    label: 'Projects',
    icon: <FolderKanban className="w-5 h-5" />,
    prefetchKey: 'projects'
  },
  {
    href: '/admin/proposals',
    label: 'Proposals',
    icon: <FileText className="w-5 h-5" />,
    prefetchKey: 'proposals'
  },
  {
    href: '/admin/users',
    label: 'Users',
    icon: <Users className="w-5 h-5" />,
    prefetchKey: 'users'
  },
  {
    href: '/admin/designers',
    label: 'Designers',
    icon: <Briefcase className="w-5 h-5" />,
    prefetchKey: 'designers'
  },
  {
    href: '/admin/applications',
    label: 'Applications',
    icon: <FileText className="w-5 h-5" />,
    prefetchKey: 'applications'
  },
  {
    href: '/admin/tracking',
    label: 'Request Management',
    icon: <FileSearch className="w-5 h-5" />,
    prefetchKey: 'tracking'
  },
  {
    href: '/admin/messages',
    label: 'Messages',
    icon: <MessageSquare className="w-5 h-5" />,
    prefetchKey: 'messages'
  },
  {
    href: '/admin/finance',
    label: 'Finance',
    icon: <BarChart3 className="w-5 h-5" />,
    prefetchKey: 'finance'
  },
  {
    href: '/admin/payments',
    label: 'Payments',
    icon: <CreditCard className="w-5 h-5" />,
    prefetchKey: 'payments'
  },
  {
    href: '/admin/settings',
    label: 'Settings',
    icon: <Settings className="w-5 h-5" />,
    prefetchKey: 'settings'
  },
  {
    href: '/admin/profile',
    label: 'Profile',
    icon: <User className="w-5 h-5" />,
    prefetchKey: 'profile'
  }
];

export const DESIGNER_SIDEBAR_ITEMS: SidebarItem[] = [
  {
    href: '/designer/dashboard',
    label: 'Dashboard',
    icon: <LayoutDashboard className="w-5 h-5" />,
    prefetchKey: 'dashboard'
  },
  {
    href: '/designer/projects',
    label: 'Projects',
    icon: <FolderKanban className="w-5 h-5" />,
    prefetchKey: 'projects'
  },
  {
    href: '/designer/proposals',
    label: 'Proposals',
    icon: <FileText className="w-5 h-5" />,
    prefetchKey: 'proposals'
  },
  {
    href: '/designer/briefs',
    label: 'Project Briefs',
    icon: <Briefcase className="w-5 h-5" />,
    prefetchKey: 'briefs'
  },
  {
    href: '/designer/clients',
    label: 'Clients',
    icon: <Users className="w-5 h-5" />,
    prefetchKey: 'clients'
  },
  {
    href: '/designer/portfolio',
    label: 'Portfolio',
    icon: <Image className="w-5 h-5" />,
    prefetchKey: 'portfolio'
  },
  {
    href: '/designer/messages',
    label: 'Messages',
    icon: <MessageSquare className="w-5 h-5" />,
    prefetchKey: 'messages'
  },
  {
    href: '/designer/admin-messages',
    label: 'Admin Messages',
    icon: <Bell className="w-5 h-5" />,
    prefetchKey: 'admin-messages'
  },
  {
    href: '/designer/availability',
    label: 'Availability',
    icon: <Calendar className="w-5 h-5" />,
    prefetchKey: 'availability'
  },
  {
    href: '/designer/reviews',
    label: 'Reviews',
    icon: <Star className="w-5 h-5" />,
    prefetchKey: 'reviews'
  },
  {
    href: '/designer/settings',
    label: 'Settings',
    icon: <Settings className="w-5 h-5" />,
    prefetchKey: 'settings'
  },
  {
    href: '/designer/profile',
    label: 'Profile',
    icon: <User className="w-5 h-5" />,
    prefetchKey: 'profile'
  }
];

export const CLIENT_SIDEBAR_ITEMS: SidebarItem[] = [
  {
    href: '/client/dashboard',
    label: 'Dashboard',
    icon: <LayoutDashboard className="w-5 h-5" />,
    prefetchKey: 'dashboard'
  },
  {
    href: '/client/projects',
    label: 'Projects',
    icon: <FolderKanban className="w-5 h-5" />,
    prefetchKey: 'projects'
  },
  {
    href: '/client/proposals',
    label: 'Proposals',
    icon: <FileText className="w-5 h-5" />,
    prefetchKey: 'proposals'
  },
  {
    href: '/client/briefs',
    label: 'Project Briefs',
    icon: <Briefcase className="w-5 h-5" />,
    prefetchKey: 'briefs'
  },
  {
    href: '/client/designers',
    label: 'Designers',
    icon: <Users className="w-5 h-5" />,
    prefetchKey: 'designers'
  },
  {
    href: '/client/messages',
    label: 'Messages',
    icon: <MessageSquare className="w-5 h-5" />,
    prefetchKey: 'messages'
  },
  {
    href: '/client/inspirations',
    label: 'Inspirations',
    icon: <Heart className="w-5 h-5" />,
    prefetchKey: 'inspirations'
  },
  {
    href: '/client/disputes',
    label: 'Disputes',
    icon: <AlertTriangle className="w-5 h-5" />,
    prefetchKey: 'disputes'
  },
  {
    href: '/client/payments',
    label: 'Payments',
    icon: <CreditCard className="w-5 h-5" />,
    prefetchKey: 'payments'
  },
  {
    href: '/client/profile',
    label: 'Profile',
    icon: <User className="w-5 h-5" />,
    prefetchKey: 'profile'
  }
];


