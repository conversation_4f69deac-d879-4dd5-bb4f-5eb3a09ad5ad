-- Fix Realtime Tables for Subscription Compatibility
-- Run this in your Supabase Dashboard SQL Editor

-- 1. Create notifications table if it doesn't exist
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error', 'announcement')),
  read_at TIMESTAMP WITH TIME ZONE,
  action_url TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create admin_messages table if it doesn't exist (enhanced version)
CREATE TABLE IF NOT EXISTS admin_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  recipient_role TEXT CHECK (recipient_role IN ('designer', 'client', 'all')),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'info' CHECK (message_type IN ('info', 'warning', 'success', 'urgent', 'announcement')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  read_at TIMESTAMP WITH TIME ZONE,
  action_required BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create portfolio_projects table if it doesn't exist
CREATE TABLE IF NOT EXISTS portfolio_projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT,
  featured BOOLEAN DEFAULT FALSE,
  image_url TEXT,
  completion_date DATE,
  client_name TEXT,
  project_value DECIMAL(10,2),
  tags TEXT[],
  is_public BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create transactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  amount DECIMAL(10,2) NOT NULL,
  type VARCHAR(50) NOT NULL CHECK (type IN ('payment', 'payout', 'refund', 'fee')),
  status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  payment_method VARCHAR(50),
  stripe_payment_intent_id VARCHAR(255),
  paypal_order_id VARCHAR(255),
  project_id UUID REFERENCES projects(id),
  client_id UUID REFERENCES profiles(id),
  designer_id UUID REFERENCES profiles(id),
  platform_fee DECIMAL(10,2) DEFAULT 0.00,
  description TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_read_at ON notifications(read_at);

CREATE INDEX IF NOT EXISTS idx_admin_messages_recipient_id ON admin_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_admin_messages_recipient_role ON admin_messages(recipient_role);
CREATE INDEX IF NOT EXISTS idx_admin_messages_created_at ON admin_messages(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_portfolio_projects_designer_id ON portfolio_projects(designer_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_projects_featured ON portfolio_projects(featured);
CREATE INDEX IF NOT EXISTS idx_portfolio_projects_is_public ON portfolio_projects(is_public);

CREATE INDEX IF NOT EXISTS idx_transactions_project_id ON transactions(project_id);
CREATE INDEX IF NOT EXISTS idx_transactions_client_id ON transactions(client_id);
CREATE INDEX IF NOT EXISTS idx_transactions_designer_id ON transactions(designer_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);

-- 6. Enable Row Level Security
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS Policies

-- Notifications: Users can only see their own notifications
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (auth.uid() = user_id);

-- Admin Messages: Users can see messages for their role or all users
CREATE POLICY "Users can view relevant admin messages" ON admin_messages
  FOR SELECT USING (
    recipient_role = 'all' OR 
    recipient_id = auth.uid() OR
    (recipient_role = (SELECT role FROM profiles WHERE id = auth.uid()))
  );

-- Portfolio Projects: Public projects visible to all, private only to owner
CREATE POLICY "Public portfolio projects are visible to all" ON portfolio_projects
  FOR SELECT USING (is_public = true);

CREATE POLICY "Designers can view their own portfolio projects" ON portfolio_projects
  FOR SELECT USING (auth.uid() = designer_id);

CREATE POLICY "Designers can manage their own portfolio projects" ON portfolio_projects
  FOR ALL USING (auth.uid() = designer_id);

-- Transactions: Users can only see their own transactions
CREATE POLICY "Clients can view their transactions" ON transactions
  FOR SELECT USING (auth.uid() = client_id);

CREATE POLICY "Designers can view their transactions" ON transactions
  FOR SELECT USING (auth.uid() = designer_id);

-- 8. Enable realtime replication (set REPLICA IDENTITY)
ALTER TABLE notifications REPLICA IDENTITY FULL;
ALTER TABLE admin_messages REPLICA IDENTITY FULL;
ALTER TABLE portfolio_projects REPLICA IDENTITY FULL;
ALTER TABLE transactions REPLICA IDENTITY FULL;

-- Also enable for existing tables that might not have it
ALTER TABLE projects REPLICA IDENTITY FULL;
ALTER TABLE project_proposals_enhanced REPLICA IDENTITY FULL;
ALTER TABLE project_briefs REPLICA IDENTITY FULL;
ALTER TABLE messages REPLICA IDENTITY FULL;
ALTER TABLE conversation_messages REPLICA IDENTITY FULL;
ALTER TABLE profiles REPLICA IDENTITY FULL;

-- 9. Create some sample data for testing (optional)
-- Insert a test notification for the current user
INSERT INTO notifications (user_id, title, message, type)
SELECT 
  auth.uid(),
  'Welcome to the Platform!',
  'Your account has been set up successfully. Start exploring the features.',
  'info'
WHERE auth.uid() IS NOT NULL
ON CONFLICT DO NOTHING;

-- Success message
SELECT 'Realtime tables created and configured successfully!' as result;
