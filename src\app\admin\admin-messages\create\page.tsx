"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Send,
  Save,
  Users,
  User,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Bell,
  Calendar,
  ExternalLink
} from "lucide-react";

interface UserOption {
  id: string;
  full_name: string;
  email: string;
  role: string;
}

export default function CreateAdminMessage() {
  const { user } = useAuth();
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    recipient_type: 'all', // 'all', 'role', 'specific'
    recipient_role: 'designer',
    recipient_id: '',
    title: '',
    content: '',
    message_type: 'info',
    priority: 'normal',
    action_required: false,
    action_url: '',
    expires_at: '',
  });

  const [users, setUsers] = useState<UserOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .neq('role', 'admin')
        .order('full_name');

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent, isDraft = false) => {
    e.preventDefault();
    if (!user) return;

    setSaving(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        throw new Error('Title is required');
      }
      if (!formData.content.trim()) {
        throw new Error('Content is required');
      }
      if (formData.recipient_type === 'specific' && !formData.recipient_id) {
        throw new Error('Please select a specific recipient');
      }

      // Prepare message data
      const messageData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        message_type: formData.message_type,
        priority: formData.priority,
        action_required: formData.action_required,
        action_url: formData.action_url.trim() || null,
        expires_at: formData.expires_at ? new Date(formData.expires_at).toISOString() : null,
        created_by: user.id,
        recipient_id: formData.recipient_type === 'specific' ? formData.recipient_id : null,
        recipient_role: formData.recipient_type === 'role' ? formData.recipient_role : 
                       formData.recipient_type === 'all' ? 'all' : null,
      };

      if (formData.recipient_type === 'all' || formData.recipient_type === 'role') {
        // Create message for multiple recipients
        const { error } = await supabase
          .from('admin_messages')
          .insert([messageData]);

        if (error) throw error;
      } else {
        // Create message for specific recipient
        const { error } = await supabase
          .from('admin_messages')
          .insert([messageData]);

        if (error) throw error;
      }

      setSuccess(isDraft ? 'Message saved as draft' : 'Message sent successfully');
      
      // Redirect after a short delay
      setTimeout(() => {
        router.push('/admin/admin-messages');
      }, 1500);

    } catch (error: any) {
      console.error('Error creating message:', error);
      setError(error.message || 'Failed to create message');
    } finally {
      setSaving(false);
    }
  };

  const getRecipientCount = () => {
    if (formData.recipient_type === 'all') {
      return users.length;
    } else if (formData.recipient_type === 'role') {
      return users.filter(u => u.role === formData.recipient_role).length;
    } else {
      return 1;
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'urgent': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'announcement': return <Bell className="h-4 w-4 text-blue-500" />;
      default: return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex items-center">
        <Link href="/admin/admin-messages" className="mr-4">
          <Button variant="ghost" className="p-0 h-auto">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Create Admin Message</h1>
          <p className="text-gray-500">Send messages and announcements to users</p>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <form onSubmit={(e) => handleSubmit(e, false)} className="bg-white rounded-lg border border-gray-200 p-6">
            {/* Recipients */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">Recipients</label>
              
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recipient_type"
                    value="all"
                    checked={formData.recipient_type === 'all'}
                    onChange={(e) => setFormData({ ...formData, recipient_type: e.target.value })}
                    className="mr-3"
                  />
                  <Users className="h-4 w-4 mr-2" />
                  <span>All Users ({users.length} recipients)</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recipient_type"
                    value="role"
                    checked={formData.recipient_type === 'role'}
                    onChange={(e) => setFormData({ ...formData, recipient_type: e.target.value })}
                    className="mr-3"
                  />
                  <Users className="h-4 w-4 mr-2" />
                  <span>By Role</span>
                </label>

                {formData.recipient_type === 'role' && (
                  <div className="ml-8">
                    <select
                      value={formData.recipient_role}
                      onChange={(e) => setFormData({ ...formData, recipient_role: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                    >
                      <option value="designer">Designers ({users.filter(u => u.role === 'designer').length})</option>
                      <option value="client">Clients ({users.filter(u => u.role === 'client').length})</option>
                      <option value="quality_team">Quality Team ({users.filter(u => u.role === 'quality_team').length})</option>
                      <option value="manager">Managers ({users.filter(u => u.role === 'manager').length})</option>
                    </select>
                  </div>
                )}

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="recipient_type"
                    value="specific"
                    checked={formData.recipient_type === 'specific'}
                    onChange={(e) => setFormData({ ...formData, recipient_type: e.target.value })}
                    className="mr-3"
                  />
                  <User className="h-4 w-4 mr-2" />
                  <span>Specific User</span>
                </label>

                {formData.recipient_type === 'specific' && (
                  <div className="ml-8">
                    <select
                      value={formData.recipient_id}
                      onChange={(e) => setFormData({ ...formData, recipient_id: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                    >
                      <option value="">Select a user...</option>
                      {users.map((user) => (
                        <option key={user.id} value={user.id}>
                          {user.full_name} ({user.email}) - {user.role}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            </div>

            {/* Message Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Message Type</label>
                <select
                  value={formData.message_type}
                  onChange={(e) => setFormData({ ...formData, message_type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                >
                  <option value="info">Info</option>
                  <option value="announcement">Announcement</option>
                  <option value="warning">Warning</option>
                  <option value="success">Success</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                >
                  <option value="low">Low</option>
                  <option value="normal">Normal</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>

            {/* Title */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                placeholder="Enter message title..."
                required
              />
            </div>

            {/* Content */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Content *</label>
              <textarea
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                placeholder="Enter message content..."
                required
              />
            </div>

            {/* Action Required */}
            <div className="mb-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.action_required}
                  onChange={(e) => setFormData({ ...formData, action_required: e.target.checked })}
                  className="mr-3"
                />
                <span className="text-sm font-medium text-gray-700">Action Required</span>
              </label>
              <p className="text-sm text-gray-500 mt-1">Mark this message as requiring user action</p>
            </div>

            {/* Action URL */}
            {formData.action_required && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Action URL</label>
                <input
                  type="url"
                  value={formData.action_url}
                  onChange={(e) => setFormData({ ...formData, action_url: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
                  placeholder="https://example.com/action"
                />
                <p className="text-sm text-gray-500 mt-1">Optional URL for users to take action</p>
              </div>
            )}

            {/* Expiration */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Expiration Date</label>
              <input
                type="datetime-local"
                value={formData.expires_at}
                onChange={(e) => setFormData({ ...formData, expires_at: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
              />
              <p className="text-sm text-gray-500 mt-1">Optional expiration date for time-sensitive messages</p>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center space-x-4">
              <Button
                type="submit"
                disabled={saving}
                className="bg-brown-600 hover:bg-brown-700 text-white"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Message
                  </>
                )}
              </Button>

              <Link href="/admin/admin-messages">
                <Button variant="outline" type="button">
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </div>

        {/* Preview */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-8">
            <h3 className="text-lg font-semibold mb-4">Preview</h3>
            
            <div className="border border-gray-200 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-2 mb-3">
                {getMessageTypeIcon(formData.message_type)}
                <h4 className="font-medium">{formData.title || 'Message Title'}</h4>
              </div>
              
              <p className="text-gray-600 text-sm mb-3">
                {formData.content || 'Message content will appear here...'}
              </p>
              
              {formData.action_required && (
                <div className="flex items-center text-sm text-blue-600">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  <span>Action Required</span>
                </div>
              )}
            </div>

            <div className="space-y-2 text-sm text-gray-500">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                <span>{getRecipientCount()} recipient{getRecipientCount() !== 1 ? 's' : ''}</span>
              </div>
              
              {formData.expires_at && (
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>Expires: {new Date(formData.expires_at).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
