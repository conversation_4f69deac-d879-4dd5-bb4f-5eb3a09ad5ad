import { supabase } from './supabase';
import { feeSettingsManager } from './fee-settings';

export interface PayPalEscrowHold {
  id: string;
  paypal_order_id: string;
  paypal_capture_id: string;
  project_id: string;
  milestone_id?: string;
  client_id: string;
  designer_id: string;
  gross_amount: number;
  platform_fee: number;
  processing_fee: number;
  designer_amount: number;
  status: 'held' | 'released' | 'refunded';
  hold_reason: string;
  created_at: string;
  released_at?: string;
  release_approved_by?: string;
}

export interface PayPalEscrowRelease {
  id: string;
  escrow_hold_id: string;
  release_amount: number;
  release_type: 'milestone_completion' | 'project_completion' | 'partial_release';
  requested_by: string;
  manager_approval_status: 'pending' | 'approved' | 'rejected' | 'not_required';
  quality_approval_status: 'pending' | 'approved' | 'rejected' | 'not_required';
  status: 'pending' | 'approved' | 'processed' | 'rejected';
  notes?: string;
  created_at: string;
  processed_at?: string;
}

/**
 * PayPal Escrow Manager
 * Handles PayPal payments with proper escrow holding and release
 */
export class PayPalEscrowManager {
  private static instance: PayPalEscrowManager;

  static getInstance(): PayPalEscrowManager {
    if (!PayPalEscrowManager.instance) {
      PayPalEscrowManager.instance = new PayPalEscrowManager();
    }
    return PayPalEscrowManager.instance;
  }

  /**
   * Create escrow hold when PayPal payment is captured
   */
  async createEscrowHold(params: {
    paypalOrderId: string;
    paypalCaptureId: string;
    projectId: string;
    milestoneId?: string;
    clientId: string;
    designerId: string;
    grossAmount: number;
    holdReason?: string;
  }): Promise<{ success: boolean; hold?: PayPalEscrowHold; error?: string }> {
    try {
      const {
        paypalOrderId,
        paypalCaptureId,
        projectId,
        milestoneId,
        clientId,
        designerId,
        grossAmount,
        holdReason = 'milestone_payment'
      } = params;

      // Calculate fees using admin settings
      const feeCalculation = await feeSettingsManager.calculatePaymentBreakdown(grossAmount);

      // Create escrow hold record
      const { data: hold, error: holdError } = await supabase
        .from('paypal_escrow_holds')
        .insert({
          paypal_order_id: paypalOrderId,
          paypal_capture_id: paypalCaptureId,
          project_id: projectId,
          milestone_id: milestoneId,
          client_id: clientId,
          designer_id: designerId,
          gross_amount: grossAmount,
          platform_fee: feeCalculation.platform_fee,
          processing_fee: feeCalculation.processing_fee,
          designer_amount: feeCalculation.designer_payout,
          status: 'held',
          hold_reason: holdReason
        })
        .select()
        .single();

      if (holdError) throw holdError;

      // Create transaction record for tracking
      await supabase.from('transactions').insert({
        transaction_id: paypalCaptureId,
        amount: grossAmount,
        status: 'completed',
        type: 'payment',
        project_id: projectId,
        milestone_id: milestoneId,
        client_id: clientId,
        designer_id: designerId,
        platform_fee: feeCalculation.platform_fee,
        processing_fee: feeCalculation.processing_fee,
        notes: `PayPal payment held in escrow - ${holdReason}`,
        payment_method: 'paypal'
      });

      // Record platform revenue
      await supabase.from('platform_revenue').insert({
        transaction_id: paypalCaptureId,
        project_id: projectId,
        revenue_type: 'commission',
        amount: feeCalculation.platform_fee,
        source: 'paypal_escrow',
        status: 'pending_release'
      });

      return { success: true, hold };
    } catch (error) {
      console.error('Error creating PayPal escrow hold:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Request escrow release (when milestone is completed)
   */
  async requestEscrowRelease(params: {
    escrowHoldId: string;
    requestedBy: string;
    releaseType?: 'milestone_completion' | 'project_completion' | 'partial_release';
    releaseAmount?: number;
    notes?: string;
  }): Promise<{ success: boolean; release?: PayPalEscrowRelease; error?: string }> {
    try {
      const {
        escrowHoldId,
        requestedBy,
        releaseType = 'milestone_completion',
        releaseAmount,
        notes
      } = params;

      // Get escrow hold details
      const { data: hold, error: holdError } = await supabase
        .from('paypal_escrow_holds')
        .select('*')
        .eq('id', escrowHoldId)
        .single();

      if (holdError || !hold) {
        throw new Error('Escrow hold not found');
      }

      if (hold.status !== 'held') {
        throw new Error('Escrow hold is not active');
      }

      const finalReleaseAmount = releaseAmount || hold.designer_amount;

      if (finalReleaseAmount > hold.designer_amount) {
        throw new Error('Release amount cannot exceed designer amount');
      }

      // Get project manager for approval workflow
      const { data: projectAssignment } = await supabase
        .from('project_assignments')
        .select('manager_id')
        .eq('project_id', hold.project_id)
        .eq('status', 'active')
        .single();

      // Create release request
      const { data: release, error: releaseError } = await supabase
        .from('paypal_escrow_releases')
        .insert({
          escrow_hold_id: escrowHoldId,
          release_amount: finalReleaseAmount,
          release_type: releaseType,
          requested_by: requestedBy,
          manager_approval_status: projectAssignment?.manager_id ? 'pending' : 'not_required',
          quality_approval_status: 'not_required', // Can be configured per project
          status: 'pending',
          notes
        })
        .select()
        .single();

      if (releaseError) throw releaseError;

      // Notify manager if approval required
      if (projectAssignment?.manager_id) {
        await supabase.from('notifications').insert({
          user_id: projectAssignment.manager_id,
          type: 'escrow_release',
          title: 'Escrow Release Approval Required',
          content: `PayPal escrow release of $${finalReleaseAmount.toFixed(2)} requires your approval`,
          related_id: release.id,
          read: false
        });
      }

      return { success: true, release };
    } catch (error) {
      console.error('Error requesting PayPal escrow release:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Approve escrow release (manager/quality approval)
   */
  async approveEscrowRelease(params: {
    releaseId: string;
    approverId: string;
    approverRole: 'manager' | 'quality' | 'admin';
    notes?: string;
  }): Promise<{ success: boolean; release?: PayPalEscrowRelease; error?: string }> {
    try {
      const { releaseId, approverId, approverRole, notes } = params;

      // Get release details
      const { data: release, error: releaseError } = await supabase
        .from('paypal_escrow_releases')
        .select('*')
        .eq('id', releaseId)
        .single();

      if (releaseError || !release) {
        throw new Error('Release request not found');
      }

      if (release.status !== 'pending') {
        throw new Error('Release request is not pending');
      }

      // Update approval based on role
      let updateData: any = {};
      
      if (approverRole === 'manager') {
        updateData = {
          manager_approval_status: 'approved',
          manager_approved_at: new Date().toISOString(),
          manager_notes: notes
        };
      } else if (approverRole === 'quality') {
        updateData = {
          quality_approval_status: 'approved',
          quality_approved_at: new Date().toISOString(),
          quality_approved_by: approverId,
          quality_notes: notes
        };
      }

      const { data: updatedRelease, error: updateError } = await supabase
        .from('paypal_escrow_releases')
        .update(updateData)
        .eq('id', releaseId)
        .select()
        .single();

      if (updateError) throw updateError;

      // Check if all required approvals are complete
      const allApproved = (
        (updatedRelease.manager_approval_status === 'approved' || updatedRelease.manager_approval_status === 'not_required') &&
        (updatedRelease.quality_approval_status === 'approved' || updatedRelease.quality_approval_status === 'not_required')
      );

      if (allApproved) {
        // Mark release as approved and process it
        await supabase
          .from('paypal_escrow_releases')
          .update({ status: 'approved' })
          .eq('id', releaseId);

        // Process the release
        await this.processEscrowRelease(releaseId);
      }

      return { success: true, release: updatedRelease };
    } catch (error) {
      console.error('Error approving PayPal escrow release:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Process approved escrow release (create designer payout)
   */
  async processEscrowRelease(releaseId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get release details with hold information
      const { data: release, error: releaseError } = await supabase
        .from('paypal_escrow_releases')
        .select(`
          *,
          escrow_hold:paypal_escrow_holds(*)
        `)
        .eq('id', releaseId)
        .single();

      if (releaseError || !release) {
        throw new Error('Release not found');
      }

      if (release.status !== 'approved') {
        throw new Error('Release is not approved');
      }

      // Create designer payout record
      const { data: payout, error: payoutError } = await supabase
        .from('designer_payouts')
        .insert({
          designer_id: release.escrow_hold.designer_id,
          project_id: release.escrow_hold.project_id,
          milestone_id: release.escrow_hold.milestone_id,
          amount: release.release_amount,
          payout_method: 'paypal',
          status: 'pending',
          escrow_release_id: releaseId,
          notes: `PayPal escrow release: ${release.release_type}`
        })
        .select()
        .single();

      if (payoutError) throw payoutError;

      // Update escrow hold status
      await supabase
        .from('paypal_escrow_holds')
        .update({
          status: 'released',
          released_at: new Date().toISOString(),
          release_approved_by: release.requested_by
        })
        .eq('id', release.escrow_hold_id);

      // Update release status
      await supabase
        .from('paypal_escrow_releases')
        .update({
          status: 'processed',
          processed_at: new Date().toISOString()
        })
        .eq('id', releaseId);

      // Update platform revenue status
      await supabase
        .from('platform_revenue')
        .update({ status: 'released' })
        .eq('transaction_id', release.escrow_hold.paypal_capture_id);

      return { success: true };
    } catch (error) {
      console.error('Error processing PayPal escrow release:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get escrow holds for a project or designer
   */
  async getEscrowHolds(params: {
    projectId?: string;
    designerId?: string;
    status?: string;
  }): Promise<PayPalEscrowHold[]> {
    try {
      let query = supabase.from('paypal_escrow_holds').select('*');

      if (params.projectId) {
        query = query.eq('project_id', params.projectId);
      }

      if (params.designerId) {
        query = query.eq('designer_id', params.designerId);
      }

      if (params.status) {
        query = query.eq('status', params.status);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching PayPal escrow holds:', error);
      return [];
    }
  }
}

// Export singleton instance
export const paypalEscrowManager = PayPalEscrowManager.getInstance();
