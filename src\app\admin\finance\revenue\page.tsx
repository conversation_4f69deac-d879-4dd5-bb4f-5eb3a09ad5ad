"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  RefreshCw,
  PieChart,
  BarChart3,
  CreditCard,
  Users,
  Target,
  Activity
} from "lucide-react";

interface RevenueMetrics {
  totalRevenue: number;
  platformFees: number;
  processingFees: number;
  designerPayouts: number;
  netProfit: number;
  transactionCount: number;
  averageTransactionValue: number;
  monthlyGrowth: number;
}

interface MonthlyData {
  month: string;
  revenue: number;
  platformFees: number;
  processingFees: number;
  payouts: number;
  netProfit: number;
  transactionCount: number;
}

interface RevenueBreakdown {
  type: string;
  amount: number;
  percentage: number;
  color: string;
}

export default function PlatformRevenuePage() {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState<RevenueMetrics | null>(null);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [revenueBreakdown, setRevenueBreakdown] = useState<RevenueBreakdown[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30'); // days
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchRevenueData();
    }
  }, [user, dateRange]);

  const fetchRevenueData = async () => {
    setLoading(true);
    setError(null);

    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(dateRange));

      // Fetch all transactions in date range
      const { data: transactions, error: transactionsError } = await supabase
        .from('transactions')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .eq('status', 'completed');

      if (transactionsError) throw transactionsError;

      // Fetch platform revenue records
      const { data: platformRevenue, error: revenueError } = await supabase
        .from('platform_revenue')
        .select('*')
        .gte('date_recorded', startDate.toISOString())
        .lte('date_recorded', endDate.toISOString());

      if (revenueError) throw revenueError;

      // Calculate metrics
      const paymentTransactions = transactions?.filter(t => t.type === 'payment') || [];
      const payoutTransactions = transactions?.filter(t => t.type === 'payout') || [];

      const totalRevenue = paymentTransactions.reduce((sum, t) => sum + (t.amount || 0), 0);
      const totalPlatformFees = platformRevenue?.reduce((sum, r) => sum + (r.amount || 0), 0) || 0;
      const totalProcessingFees = paymentTransactions.reduce((sum, t) => sum + (t.processing_fee || 0), 0);
      const totalPayouts = payoutTransactions.reduce((sum, t) => sum + (t.amount || 0), 0);
      const netProfit = totalPlatformFees - totalProcessingFees;

      // Calculate previous period for growth
      const prevStartDate = new Date(startDate);
      prevStartDate.setDate(prevStartDate.getDate() - parseInt(dateRange));
      
      const { data: prevTransactions } = await supabase
        .from('transactions')
        .select('amount')
        .gte('created_at', prevStartDate.toISOString())
        .lt('created_at', startDate.toISOString())
        .eq('status', 'completed')
        .eq('type', 'payment');

      const prevRevenue = prevTransactions?.reduce((sum, t) => sum + (t.amount || 0), 0) || 0;
      const monthlyGrowth = prevRevenue > 0 ? ((totalRevenue - prevRevenue) / prevRevenue) * 100 : 0;

      setMetrics({
        totalRevenue,
        platformFees: totalPlatformFees,
        processingFees: totalProcessingFees,
        designerPayouts: totalPayouts,
        netProfit,
        transactionCount: paymentTransactions.length,
        averageTransactionValue: paymentTransactions.length > 0 ? totalRevenue / paymentTransactions.length : 0,
        monthlyGrowth
      });

      // Prepare revenue breakdown
      setRevenueBreakdown([
        {
          type: 'Platform Fees',
          amount: totalPlatformFees,
          percentage: totalRevenue > 0 ? (totalPlatformFees / totalRevenue) * 100 : 0,
          color: 'bg-blue-500'
        },
        {
          type: 'Processing Fees',
          amount: totalProcessingFees,
          percentage: totalRevenue > 0 ? (totalProcessingFees / totalRevenue) * 100 : 0,
          color: 'bg-yellow-500'
        },
        {
          type: 'Designer Payouts',
          amount: totalPayouts,
          percentage: totalRevenue > 0 ? (totalPayouts / totalRevenue) * 100 : 0,
          color: 'bg-green-500'
        }
      ]);

      // Prepare monthly data (last 12 months)
      const monthlyMap = new Map<string, MonthlyData>();
      
      for (let i = 11; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = date.toISOString().slice(0, 7); // YYYY-MM
        
        monthlyMap.set(monthKey, {
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          revenue: 0,
          platformFees: 0,
          processingFees: 0,
          payouts: 0,
          netProfit: 0,
          transactionCount: 0
        });
      }

      // Populate with actual data
      transactions?.forEach(transaction => {
        const monthKey = transaction.created_at.slice(0, 7);
        const monthData = monthlyMap.get(monthKey);
        
        if (monthData) {
          if (transaction.type === 'payment') {
            monthData.revenue += transaction.amount || 0;
            monthData.platformFees += transaction.platform_fee || 0;
            monthData.processingFees += transaction.processing_fee || 0;
            monthData.transactionCount += 1;
          } else if (transaction.type === 'payout') {
            monthData.payouts += transaction.amount || 0;
          }
          monthData.netProfit = monthData.platformFees - monthData.processingFees;
        }
      });

      setMonthlyData(Array.from(monthlyMap.values()));

    } catch (error: any) {
      console.error('Error fetching revenue data:', error);
      setError(error.message || 'Failed to fetch revenue data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/admin/finance" className="mr-4">
            <Button variant="ghost" className="p-0 h-auto">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Platform Revenue</h1>
            <p className="text-gray-500">Track platform fees, processing costs, and net profit</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="365">Last year</option>
          </select>
          <Button
            onClick={fetchRevenueData}
            variant="outline"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.totalRevenue)}</p>
                <div className="flex items-center mt-2">
                  {metrics.monthlyGrowth >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={`text-sm font-medium ${metrics.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(metrics.monthlyGrowth)}
                  </span>
                </div>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Platform Fees</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.platformFees)}</p>
                <p className="text-sm text-gray-600 mt-2">
                  {metrics.totalRevenue > 0 ? ((metrics.platformFees / metrics.totalRevenue) * 100).toFixed(1) : 0}% of revenue
                </p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <Target className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Net Profit</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.netProfit)}</p>
                <p className="text-sm text-gray-600 mt-2">
                  After processing fees
                </p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white p-6 rounded-lg border border-gray-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Transactions</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.transactionCount}</p>
                <p className="text-sm text-gray-600 mt-2">
                  Avg: {formatCurrency(metrics.averageTransactionValue)}
                </p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <Activity className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Revenue Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold mb-6 flex items-center">
            <PieChart className="h-5 w-5 mr-2 text-gray-400" />
            Revenue Breakdown
          </h3>
          
          <div className="space-y-4">
            {revenueBreakdown.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-4 h-4 rounded ${item.color}`}></div>
                  <span className="text-sm font-medium text-gray-700">{item.type}</span>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-gray-900">{formatCurrency(item.amount)}</p>
                  <p className="text-xs text-gray-500">{item.percentage.toFixed(1)}%</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <h3 className="text-lg font-semibold mb-6 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-gray-400" />
            Monthly Trends
          </h3>
          
          <div className="space-y-3">
            {monthlyData.slice(-6).map((month, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{month.month}</span>
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-gray-900">
                    {formatCurrency(month.revenue)}
                  </span>
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${Math.min((month.revenue / Math.max(...monthlyData.map(m => m.revenue))) * 100, 100)}%`
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Detailed Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-lg border border-gray-200 overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Detailed Metrics</h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h4 className="font-medium text-gray-900 mb-4">Revenue Sources</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Project Payments</span>
                  <span className="text-sm font-medium">{formatCurrency(metrics?.totalRevenue || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Platform Fees (15%)</span>
                  <span className="text-sm font-medium">{formatCurrency(metrics?.platformFees || 0)}</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-4">Costs</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Processing Fees (2.9%)</span>
                  <span className="text-sm font-medium">{formatCurrency(metrics?.processingFees || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Designer Payouts</span>
                  <span className="text-sm font-medium">{formatCurrency(metrics?.designerPayouts || 0)}</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-4">Performance</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Profit Margin</span>
                  <span className="text-sm font-medium">
                    {metrics?.totalRevenue ? ((metrics.netProfit / metrics.totalRevenue) * 100).toFixed(1) : 0}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Avg Transaction</span>
                  <span className="text-sm font-medium">{formatCurrency(metrics?.averageTransactionValue || 0)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
