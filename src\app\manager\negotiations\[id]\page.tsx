"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  MessageSquare,
  Users,
  Clock,
  DollarSign,
  FileText,
  Target,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Play,
  Pause,
  Settings,
  Send,
  Eye,
  Calendar,
  Star,
  RefreshCw
} from "lucide-react";

interface NegotiationSession {
  id: string;
  project_id: string;
  manager_id: string;
  client_id: string;
  designer_id: string;
  session_type: string;
  status: string;
  terms: any;
  priority: string;
  description: string;
  notes: string;
  created_at: string;
  updated_at: string;
  project: {
    title: string;
    description: string;
    budget: number;
    status: string;
  };
  client: {
    full_name: string;
    email: string;
  };
  designer: {
    full_name: string;
    email: string;
  };
  messages: NegotiationMessage[];
}

interface NegotiationMessage {
  id: string;
  session_id: string;
  sender_id: string;
  sender_role: string;
  message: string;
  message_type: string;
  created_at: string;
  sender: {
    full_name: string;
  };
}

export default function NegotiationDetailPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const sessionId = params.id as string;
  
  const [session, setSession] = useState<NegotiationSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'manager' && sessionId) {
      fetchNegotiationSession();
    }
  }, [user, profile, sessionId]);

  const fetchNegotiationSession = async () => {
    try {
      const { data, error } = await supabase
        .from('negotiation_sessions')
        .select(`
          *,
          project:projects(title, description, budget, status),
          client:profiles!negotiation_sessions_client_id_fkey(full_name, email),
          designer:profiles!negotiation_sessions_designer_id_fkey(full_name, email),
          messages:negotiation_messages(
            id, message, message_type, created_at, sender_role,
            sender:profiles!negotiation_messages_sender_id_fkey(full_name)
          )
        `)
        .eq('id', sessionId)
        .eq('manager_id', user?.id)
        .single();

      if (error) throw error;
      setSession(data);
    } catch (error) {
      console.error('Error fetching negotiation session:', error);
      router.push('/manager/negotiations');
    } finally {
      setLoading(false);
    }
  };

  const updateSessionStatus = async (newStatus: string) => {
    if (!session) return;

    try {
      const { error } = await supabase
        .from('negotiation_sessions')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: session.project_id,
        activity_type: 'negotiation_status_update',
        description: `Updated negotiation status to ${newStatus}`,
        outcome: newStatus
      });

      fetchNegotiationSession();
    } catch (error) {
      console.error('Error updating session status:', error);
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !session) return;

    setSendingMessage(true);
    try {
      const { error } = await supabase
        .from('negotiation_messages')
        .insert({
          session_id: sessionId,
          sender_id: user?.id,
          sender_role: 'manager',
          message: newMessage.trim(),
          message_type: 'text'
        });

      if (error) throw error;

      // Send notifications
      await supabase.from('notifications').insert([
        {
          user_id: session.client_id,
          type: 'negotiation_message',
          title: 'New Negotiation Message',
          message: `Manager sent a message in ${session.session_type} negotiation`,
          data: { session_id: sessionId, project_id: session.project_id }
        },
        {
          user_id: session.designer_id,
          type: 'negotiation_message',
          title: 'New Negotiation Message',
          message: `Manager sent a message in ${session.session_type} negotiation`,
          data: { session_id: sessionId, project_id: session.project_id }
        }
      ]);

      setNewMessage('');
      fetchNegotiationSession();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSendingMessage(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Play className="h-5 w-5 text-green-500" />;
      case 'paused':
        return <Pause className="h-5 w-5 text-amber-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'paused':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'completed':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case 'draft':
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'pricing':
        return <DollarSign className="h-5 w-5 text-green-500" />;
      case 'timeline':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'scope':
        return <Target className="h-5 w-5 text-purple-500" />;
      case 'terms':
        return <FileText className="h-5 w-5 text-amber-500" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Negotiation Not Found</h2>
          <p className="text-gray-600 mb-4">The negotiation session could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/negotiations')}>
            Back to Negotiations
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          {getTypeIcon(session.session_type)}
          <div>
            <h1 className="text-3xl font-bold text-gray-900 capitalize">
              {session.session_type} Negotiation
            </h1>
            <p className="text-gray-600 mt-1">{session.project.title}</p>
          </div>
        </div>
      </div>

      {/* Status and Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            {getStatusIcon(session.status)}
            <div>
              <div className="flex items-center gap-3 mb-2">
                <span className={getStatusBadge(session.status)}>
                  {session.status.toUpperCase()}
                </span>
                <span className={getPriorityBadge(session.priority)}>
                  {session.priority.toUpperCase()} PRIORITY
                </span>
              </div>
              <p className="text-sm text-gray-600">
                Created {new Date(session.created_at).toLocaleDateString()} •
                Last updated {new Date(session.updated_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex gap-3">
            {session.status === 'draft' && (
              <Button
                onClick={() => updateSessionStatus('active')}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                <Play className="h-4 w-4" />
                Start Negotiation
              </Button>
            )}

            {session.status === 'active' && (
              <>
                <Button
                  variant="outline"
                  onClick={() => updateSessionStatus('paused')}
                  className="flex items-center gap-2"
                >
                  <Pause className="h-4 w-4" />
                  Pause
                </Button>
                <Button
                  onClick={() => updateSessionStatus('completed')}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <CheckCircle className="h-4 w-4" />
                  Complete
                </Button>
              </>
            )}

            {session.status === 'paused' && (
              <Button
                onClick={() => updateSessionStatus('active')}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                <Play className="h-4 w-4" />
                Resume
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() => router.push(`/manager/negotiations/${sessionId}/facilitate`)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Facilitate
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Project Info */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Details</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">{session.project.title}</h3>
                <p className="text-gray-600 mt-1">{session.project.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Budget:</span>
                  <span className="ml-2 text-gray-600">${session.project.budget.toLocaleString()}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Status:</span>
                  <span className="ml-2 text-gray-600">{session.project.status}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Type:</span>
                  <span className="ml-2 text-gray-600 capitalize">{session.session_type}</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="font-medium text-gray-700">Client:</span>
                  <span className="text-gray-600">{session.client.full_name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="font-medium text-gray-700">Designer:</span>
                  <span className="text-gray-600">{session.designer.full_name}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Negotiation Terms */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Negotiation Terms</h2>

            {session.description && (
              <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-700">{session.description}</p>
              </div>
            )}

            <div className="space-y-4">
              {session.session_type === 'pricing' && session.terms?.pricing && (
                <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <h3 className="font-semibold text-green-900 mb-3 flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Pricing Terms
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-green-700">Current Budget:</span>
                      <span className="ml-2 text-green-600">${session.terms.pricing.current_budget?.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="font-medium text-green-700">Proposed Budget:</span>
                      <span className="ml-2 text-green-600">${session.terms.pricing.proposed_budget?.toLocaleString()}</span>
                    </div>
                  </div>
                  {session.terms.pricing.payment_schedule && (
                    <div className="mt-3">
                      <span className="font-medium text-green-700">Payment Schedule:</span>
                      <p className="text-green-600 mt-1">{session.terms.pricing.payment_schedule}</p>
                    </div>
                  )}
                </div>
              )}

              {session.session_type === 'timeline' && session.terms?.timeline && (
                <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <h3 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Timeline Terms
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-blue-700">Current Deadline:</span>
                      <span className="ml-2 text-blue-600">
                        {session.terms.timeline.current_deadline ?
                          new Date(session.terms.timeline.current_deadline).toLocaleDateString() :
                          'Not set'
                        }
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-700">Proposed Deadline:</span>
                      <span className="ml-2 text-blue-600">
                        {session.terms.timeline.proposed_deadline ?
                          new Date(session.terms.timeline.proposed_deadline).toLocaleDateString() :
                          'Not set'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {session.session_type === 'scope' && session.terms?.scope && (
                <div className="border border-purple-200 rounded-lg p-4 bg-purple-50">
                  <h3 className="font-semibold text-purple-900 mb-3 flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Scope Terms
                  </h3>
                  <div className="space-y-3 text-sm">
                    {session.terms.scope.current_scope && (
                      <div>
                        <span className="font-medium text-purple-700">Current Scope:</span>
                        <p className="text-purple-600 mt-1">{session.terms.scope.current_scope}</p>
                      </div>
                    )}
                    {session.terms.scope.proposed_changes && (
                      <div>
                        <span className="font-medium text-purple-700">Proposed Changes:</span>
                        <p className="text-purple-600 mt-1">{session.terms.scope.proposed_changes}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {session.notes && (
              <div className="mt-4 p-4 bg-amber-50 rounded-lg border border-amber-200">
                <h4 className="font-medium text-amber-900 mb-2">Manager Notes</h4>
                <p className="text-amber-800 text-sm">{session.notes}</p>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Communication */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Communication</h3>

            {/* Messages */}
            <div className="space-y-4 mb-4 max-h-96 overflow-y-auto">
              {session.messages && session.messages.length > 0 ? (
                session.messages.map((message) => (
                  <div key={message.id} className="p-3 rounded-lg bg-gray-50">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm text-gray-900">
                        {message.sender.full_name}
                      </span>
                      <span className="text-xs text-gray-500 capitalize">
                        ({message.sender_role})
                      </span>
                      <span className="text-xs text-gray-400">
                        {new Date(message.created_at).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700">{message.message}</p>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-sm text-center py-4">No messages yet</p>
              )}
            </div>

            {/* Message Form */}
            <form onSubmit={sendMessage} className="space-y-3">
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message..."
                rows={3}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500 text-sm"
              />
              <Button
                type="submit"
                disabled={!newMessage.trim() || sendingMessage}
                className="w-full flex items-center justify-center gap-2"
              >
                {sendingMessage ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Send className="h-4 w-4" />
                )}
                Send Message
              </Button>
            </form>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full flex items-center gap-2"
                onClick={() => router.push(`/manager/projects/${session.project_id}`)}
              >
                <Eye className="h-4 w-4" />
                View Project
              </Button>
              <Button
                variant="outline"
                className="w-full flex items-center gap-2"
                onClick={() => router.push(`/manager/negotiations/${sessionId}/facilitate`)}
              >
                <Settings className="h-4 w-4" />
                Facilitate Session
              </Button>
              <Button
                variant="outline"
                className="w-full flex items-center gap-2"
                onClick={() => window.print()}
              >
                <FileText className="h-4 w-4" />
                Export Summary
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
