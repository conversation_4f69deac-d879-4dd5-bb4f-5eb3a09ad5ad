"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Upload, X, Plus } from "lucide-react";
import Link from "next/link";

export default function NewInspirationBoard() {
  const { user } = useAuth();
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [inspirationFiles, setInspirationFiles] = useState<File[]>([]);
  const [filePreviewUrls, setFilePreviewUrls] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setInspirationFiles(prev => [...prev, ...newFiles]);

      // Create preview URLs
      const newPreviewUrls = newFiles.map(file => URL.createObjectURL(file));
      setFilePreviewUrls(prev => [...prev, ...newPreviewUrls]);
    }
  };

  const removeFile = (index: number) => {
    // Revoke the object URL to avoid memory leaks
    URL.revokeObjectURL(filePreviewUrls[index]);

    setInspirationFiles(prev => prev.filter((_, i) => i !== index));
    setFilePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate
      if (!title.trim()) {
        throw new Error("Board title is required");
      }

      // 1. Create the inspiration board
      const { data: boardData, error: boardError } = await supabase
        .from('inspiration_boards')
        .insert({
          project_id: projectId,
          title: title.trim(),
          description: description.trim() || null
        })
        .select()
        .single();

      if (boardError) throw boardError;

      // 2. Upload inspiration files if any
      if (inspirationFiles.length > 0) {
        for (const file of inspirationFiles) {
          // Upload file to storage
          const fileExt = file.name.split('.').pop();
          const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
          const filePath = `inspiration_images/${boardData.id}/${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from('project-files')
            .upload(filePath, file);

          if (uploadError) throw uploadError;

          // Get public URL
          const { data: urlData } = supabase.storage
            .from('project-files')
            .getPublicUrl(filePath);

          // Add image to inspiration_images table
          const { error: imageError } = await supabase
            .from('inspiration_images')
            .insert({
              board_id: boardData.id,
              image_url: urlData.publicUrl,
              caption: file.name
            });

          if (imageError) throw imageError;
        }
      }

      // Redirect to the board page
      router.push(`/client/projects/${projectId}/inspirations/${boardData.id}`);
    } catch (error: Error | unknown) {
      console.error('Error creating inspiration board:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while creating your inspiration board');
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-8">
        <Link href={`/client/projects/${projectId}`} className="inline-flex items-center text-gray-600 hover:text-primary">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Project
        </Link>
        <h1 className="text-2xl font-bold mt-4">New Inspiration Board</h1>
      </div>

      {error && (
        <div className="bg-red-50 text-red-500 p-4 mb-6 border-l-4 border-red-500">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
        <div className="space-y-6">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              Board Title <span className="text-red-500">*</span>
            </label>
            <input
              id="title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
              className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="e.g., Kitchen Inspirations"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Add a description for this inspiration board..."
            />
          </div>

          <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
              Inspiration Images
            </label>
            <div className="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center">
              <input
                type="file"
                id="inspirationFiles"
                multiple
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
              <label
                htmlFor="inspirationFiles"
                className="cursor-pointer flex flex-col items-center justify-center"
              >
                <Upload className="h-12 w-12 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 mb-1">
                  Drag and drop image files here, or click to select files
                </p>
                <p className="text-xs text-gray-400">
                  Upload images that inspire your design vision
                </p>
              </label>
            </div>
          </div>

          {filePreviewUrls.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Selected Images ({filePreviewUrls.length})
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {filePreviewUrls.map((url, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={url}
                      alt={`Inspiration ${index + 1}`}
                      className="h-32 w-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <Link href={`/client/projects/${projectId}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={loading || !title.trim()}
              className="flex items-center"
            >
              {loading ? "Creating Board..." : "Create Board"}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
