import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// PayPal API base URL - use sandbox for development, change to live for production
const PAYPAL_API_BASE = process.env.NODE_ENV === 'production'
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

// Function to get PayPal access token
async function getPayPalAccessToken() {
  const clientId = process.env.PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('PayPal credentials are not configured');
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
  
  const response = await fetch(`${PAYPAL_API_BASE}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${auth}`
    },
    body: 'grant_type=client_credentials'
  });

  if (!response.ok) {
    const errorData = await response.json();
    console.error('PayPal auth error:', errorData);
    throw new Error('Failed to authenticate with PayPal');
  }

  const data = await response.json();
  return data.access_token;
}

export async function POST(request: Request) {
  try {
    // Get request data
    const {
      orderId,
      projectId,
      milestoneId,
      clientId,
      paymentType
    } = await request.json();

    // Validate required fields
    if (!orderId || !projectId || !clientId || !paymentType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get PayPal access token
    const accessToken = await getPayPalAccessToken();

    // Capture the PayPal order
    const captureResponse = await fetch(`${PAYPAL_API_BASE}/v2/checkout/orders/${orderId}/capture`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!captureResponse.ok) {
      const errorData = await captureResponse.json();
      console.error('PayPal capture error:', errorData);
      return NextResponse.json(
        { error: 'Failed to capture PayPal payment' },
        { status: 500 }
      );
    }

    const captureData = await captureResponse.json();
    
    // Initialize Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          persistSession: false
        }
      }
    );

    // Get the payment amount from the capture data
    const captureAmount = captureData.purchase_units[0].payments.captures[0].amount.value;
    const captureId = captureData.purchase_units[0].payments.captures[0].id;
    
    // Record the payment in the database
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        project_id: projectId,
        milestone_id: milestoneId,
        client_id: clientId,
        amount: parseFloat(captureAmount) * 100, // Convert to cents
        payment_method: 'paypal',
        payment_id: captureId,
        status: 'completed',
        payment_type: paymentType,
        metadata: captureData
      });

    if (paymentError) {
      console.error('Error recording payment:', paymentError);
      // Continue anyway since the payment was successful
    }

    // If this is a milestone payment, update the milestone status
    if (milestoneId) {
      const { error: milestoneError } = await supabase
        .from('project_milestones')
        .update({
          status: 'paid',
          paid_at: new Date().toISOString()
        })
        .eq('id', milestoneId);

      if (milestoneError) {
        console.error('Error updating milestone:', milestoneError);
        // Continue anyway since the payment was successful
      }
    }

    // Return the capture details
    return NextResponse.json({
      id: captureData.id,
      status: captureData.status,
      payer: captureData.payer,
      amount: captureData.purchase_units[0].payments.captures[0].amount,
      capture_id: captureId
    });
  } catch (error: unknown) {
    console.error('Error capturing PayPal payment:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
