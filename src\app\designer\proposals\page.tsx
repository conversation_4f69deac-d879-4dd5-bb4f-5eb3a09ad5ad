"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  FileText,
  Clock,
  DollarSign,
  Calendar,
  User,
  Eye,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Plus
} from "lucide-react";

interface Proposal {
  id: string;
  title: string;
  description: string;
  total_budget: number;
  timeline_weeks: number;
  status: 'draft' | 'submitted' | 'under_review' | 'accepted' | 'rejected' | 'withdrawn';
  submitted_at: string | null;
  expires_at: string | null;
  created_at: string;
  brief_title: string;
  client_name: string;
  client_avatar: string | null;
}

export default function DesignerProposals() {
  const { user } = useOptimizedAuth();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    if (user) {
      fetchProposals();
    }
  }, [user]);

  const fetchProposals = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('project_proposals_enhanced')
        .select(`
          id,
          title,
          description,
          total_budget,
          timeline_weeks,
          status,
          submitted_at,
          expires_at,
          created_at,
          project_briefs!project_proposals_enhanced_brief_id_fkey(
            title,
            profiles!project_briefs_client_id_fkey(full_name, avatar_url)
          )
        `)
        .eq('designer_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedProposals: Proposal[] = (data || []).map(proposal => {
        const brief = proposal.project_briefs;
        const client = Array.isArray(brief?.profiles) ? brief.profiles[0] : brief?.profiles;
        
        return {
          id: proposal.id,
          title: proposal.title,
          description: proposal.description,
          total_budget: proposal.total_budget || 0,
          timeline_weeks: proposal.timeline_weeks || 0,
          status: proposal.status,
          submitted_at: proposal.submitted_at,
          expires_at: proposal.expires_at,
          created_at: proposal.created_at,
          brief_title: brief?.title || 'Unknown Brief',
          client_name: client?.full_name || 'Unknown Client',
          client_avatar: client?.avatar_url || null
        };
      });

      setProposals(formattedProposals);
    } catch (error) {
      console.error('Error fetching proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'submitted':
      case 'under_review':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'draft':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'rejected':
      case 'withdrawn':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
      case 'withdrawn':
        return <XCircle className="h-4 w-4" />;
      case 'submitted':
      case 'under_review':
        return <Clock className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not submitted';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const isExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  const filteredProposals = proposals.filter(proposal => {
    const matchesSearch = proposal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         proposal.brief_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         proposal.client_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || proposal.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Proposals</h1>
          <p className="text-gray-600">Manage your project proposals and track their status</p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500">
            {filteredProposals.length} proposal{filteredProposals.length !== 1 ? 's' : ''}
          </span>
          <Link href="/designer/briefs">
            <Button className="bg-brown-600 hover:bg-brown-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Create New Proposal
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search proposals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Statuses</option>
            <option value="draft">Draft</option>
            <option value="submitted">Submitted</option>
            <option value="under_review">Under Review</option>
            <option value="accepted">Accepted</option>
            <option value="rejected">Rejected</option>
            <option value="withdrawn">Withdrawn</option>
          </select>
        </div>
      </div>

      {/* Proposals List */}
      {filteredProposals.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {proposals.length === 0 ? 'No proposals yet' : 'No proposals match your filters'}
          </h3>
          <p className="text-gray-500 mb-4">
            {proposals.length === 0 
              ? 'Start by reviewing project briefs and creating your first proposal' 
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {proposals.length === 0 && (
            <Link href="/designer/briefs">
              <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Browse Project Briefs
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredProposals.map((proposal) => (
            <motion.div
              key={proposal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">{proposal.title}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border flex items-center ${getStatusColor(proposal.status)}`}>
                      {getStatusIcon(proposal.status)}
                      <span className="ml-1">{proposal.status.replace('_', ' ').toUpperCase()}</span>
                    </span>
                    {isExpired(proposal.expires_at) && proposal.status === 'submitted' && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 border border-red-200">
                        EXPIRED
                      </span>
                    )}
                  </div>

                  <p className="text-gray-600 mb-3">For: {proposal.brief_title}</p>
                  <p className="text-gray-600 mb-4 line-clamp-2">{proposal.description}</p>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="h-4 w-4 mr-2" />
                      {proposal.client_name}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <DollarSign className="h-4 w-4 mr-2" />
                      ${proposal.total_budget.toLocaleString()}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-2" />
                      {proposal.timeline_weeks} weeks
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-2" />
                      {formatDate(proposal.submitted_at)}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">
                      Created {formatDate(proposal.created_at)}
                      {proposal.expires_at && (
                        <span className="ml-2">
                          • Expires {formatDate(proposal.expires_at)}
                        </span>
                      )}
                    </span>
                    <div className="flex items-center space-x-2">
                      <Link href={`/designer/proposals/${proposal.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </Link>
                      {proposal.status === 'draft' && (
                        <Link href={`/designer/proposals/${proposal.id}/edit`}>
                          <Button size="sm" className="bg-brown-600 hover:bg-brown-700 text-white">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
