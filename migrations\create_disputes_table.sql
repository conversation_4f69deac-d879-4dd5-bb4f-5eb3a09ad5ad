-- Create disputes table
CREATE TABLE IF NOT EXISTS disputes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES profiles(id),
  designer_id UUID NOT NULL REFERENCES profiles(id),
  created_by UUID NOT NULL REFERENCES profiles(id),
  status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'under_review', 'resolved', 'closed')),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  resolution_notes TEXT,
  resolved_by UUID REFERENCES profiles(id),
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dispute_messages table for communication within a dispute
CREATE TABLE IF NOT EXISTS dispute_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  dispute_id UUID NOT NULL REFERENCES disputes(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES profiles(id),
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  attachment_url TEXT,
  attachment_name TEXT,
  attachment_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dispute_attachments table
CREATE TABLE IF NOT EXISTS dispute_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  dispute_id UUID NOT NULL REFERENCES disputes(id) ON DELETE CASCADE,
  file_url TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_type TEXT,
  file_size INTEGER,
  uploaded_by UUID NOT NULL REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_disputes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_disputes_updated_at
BEFORE UPDATE ON disputes
FOR EACH ROW
EXECUTE FUNCTION update_disputes_updated_at();

-- Add RLS policies for disputes table
ALTER TABLE disputes ENABLE ROW LEVEL SECURITY;

-- Admins can see all disputes
CREATE POLICY "Admins can see all disputes"
  ON disputes
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can see their own disputes
CREATE POLICY "Users can see their own disputes"
  ON disputes
  FOR SELECT
  USING (client_id = auth.uid() OR designer_id = auth.uid());

-- Users can create disputes for their own projects
CREATE POLICY "Users can create disputes"
  ON disputes
  FOR INSERT
  WITH CHECK (
    created_by = auth.uid() AND
    (client_id = auth.uid() OR designer_id = auth.uid())
  );

-- Only admins can update disputes
CREATE POLICY "Admins can update disputes"
  ON disputes
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Add RLS policies for dispute_messages table
ALTER TABLE dispute_messages ENABLE ROW LEVEL SECURITY;

-- Admins can see all dispute messages
CREATE POLICY "Admins can see all dispute messages"
  ON dispute_messages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can see messages for disputes they're involved in
CREATE POLICY "Users can see their own dispute messages"
  ON dispute_messages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM disputes
      WHERE disputes.id = dispute_messages.dispute_id
      AND (disputes.client_id = auth.uid() OR disputes.designer_id = auth.uid())
    )
  );

-- Users can create messages for disputes they're involved in
CREATE POLICY "Users can create dispute messages"
  ON dispute_messages
  FOR INSERT
  WITH CHECK (
    sender_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM disputes
      WHERE disputes.id = dispute_messages.dispute_id
      AND (disputes.client_id = auth.uid() OR disputes.designer_id = auth.uid() OR
           EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role = 'admin'))
    )
  );

-- Add RLS policies for dispute_attachments table
ALTER TABLE dispute_attachments ENABLE ROW LEVEL SECURITY;

-- Admins can see all dispute attachments
CREATE POLICY "Admins can see all dispute attachments"
  ON dispute_attachments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can see attachments for disputes they're involved in
CREATE POLICY "Users can see their own dispute attachments"
  ON dispute_attachments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM disputes
      WHERE disputes.id = dispute_attachments.dispute_id
      AND (disputes.client_id = auth.uid() OR disputes.designer_id = auth.uid())
    )
  );

-- Users can create attachments for disputes they're involved in
CREATE POLICY "Users can create dispute attachments"
  ON dispute_attachments
  FOR INSERT
  WITH CHECK (
    uploaded_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM disputes
      WHERE disputes.id = dispute_attachments.dispute_id
      AND (disputes.client_id = auth.uid() OR disputes.designer_id = auth.uid() OR
           EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role = 'admin'))
    )
  );
