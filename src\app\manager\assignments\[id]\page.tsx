"use client";

import { useState, useEffect } from "react";
import { useP<PERSON><PERSON>, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  UserPlus,
  Users,
  Calendar,
  Target,
  FileText,
  Edit,
  CheckCircle,
  Clock,
  AlertTriangle,
  BarChart3,
  RefreshCw,
  Send,
  MessageSquare,
  Star,
  TrendingUp,
  Flag
} from "lucide-react";

interface Assignment {
  id: string;
  project_id: string;
  manager_id: string;
  assigned_by: string;
  assignment_type: string;
  title: string;
  description: string;
  priority: string;
  due_date: string | null;
  estimated_hours: number;
  objectives: string[];
  deliverables: string[];
  notes: string;
  status: string;
  assigned_at: string;
  completed_at: string | null;
  project: {
    title: string;
    budget: number;
    status: string;
    client: {
      full_name: string;
      email: string;
    };
    designer: {
      full_name: string;
      email: string;
    };
  };
  assignee: {
    full_name: string;
    email: string;
    specialization: string;
  };
  assigner: {
    full_name: string;
    role: string;
  };
  tasks: AssignmentTask[];
  progress_reports: ProgressReport[];
}

interface AssignmentTask {
  id: string;
  title: string;
  description: string;
  due_date: string;
  priority: string;
  status: string;
  completion_percentage: number;
  created_at: string;
}

interface ProgressReport {
  id: string;
  report_date: string;
  progress_percentage: number;
  summary: string;
  challenges: string;
  next_steps: string;
  created_at: string;
}

export default function AssignmentDetailPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const assignmentId = params.id as string;
  
  const [assignment, setAssignment] = useState<Assignment | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'tasks' | 'progress' | 'communication'>('overview');

  useEffect(() => {
    if (user && profile?.role === 'manager' && assignmentId) {
      fetchAssignmentData();
    }
  }, [user, profile, assignmentId]);

  const fetchAssignmentData = async () => {
    try {
      const { data, error } = await supabase
        .from('project_assignments')
        .select(`
          *,
          project:projects(
            title, budget, status,
            client:profiles!projects_client_id_fkey(full_name, email),
            designer:profiles!projects_designer_id_fkey(full_name, email)
          ),
          assignee:profiles!project_assignments_manager_id_fkey(full_name, email, specialization),
          assigner:profiles!project_assignments_assigned_by_fkey(full_name, role),
          tasks:assignment_tasks(*),
          progress_reports:assignment_progress_reports(*)
        `)
        .eq('id', assignmentId)
        .single();

      if (error) throw error;
      setAssignment(data);
    } catch (error) {
      console.error('Error fetching assignment data:', error);
      router.push('/manager/assignments');
    } finally {
      setLoading(false);
    }
  };

  const updateAssignmentStatus = async (newStatus: string) => {
    if (!assignment) return;

    try {
      const updates: any = {
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      if (newStatus === 'completed') {
        updates.completed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('project_assignments')
        .update(updates)
        .eq('id', assignmentId);

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: assignment.project_id,
        activity_type: 'assignment_status_update',
        description: `Updated assignment status to ${newStatus}`,
        outcome: newStatus
      });

      fetchAssignmentData();
    } catch (error) {
      console.error('Error updating assignment status:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'on_hold':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'cancelled':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'on_hold':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'high':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'normal':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'low':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getTaskProgress = () => {
    if (!assignment?.tasks || assignment.tasks.length === 0) return 0;
    const totalProgress = assignment.tasks.reduce((sum, task) => sum + task.completion_percentage, 0);
    return Math.round(totalProgress / assignment.tasks.length);
  };

  const getCompletedTasks = () => {
    if (!assignment?.tasks) return 0;
    return assignment.tasks.filter(task => task.status === 'completed').length;
  };

  const getOverallProgress = () => {
    if (!assignment?.progress_reports || assignment.progress_reports.length === 0) return 0;
    const latestReport = assignment.progress_reports.sort((a, b) => 
      new Date(b.report_date).getTime() - new Date(a.report_date).getTime()
    )[0];
    return latestReport?.progress_percentage || 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!assignment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Assignment Not Found</h2>
          <p className="text-gray-600 mb-4">The assignment could not be found or you don't have access to it.</p>
          <Button onClick={() => router.push('/manager/assignments')}>
            Back to Assignments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <UserPlus className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{assignment.title}</h1>
            <p className="text-gray-600 mt-1">{assignment.project.title}</p>
          </div>
        </div>
      </div>

      {/* Status and Actions */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            {getStatusIcon(assignment.status)}
            <div>
              <div className="flex items-center gap-3 mb-2">
                <span className={getStatusBadge(assignment.status)}>
                  {assignment.status.replace('_', ' ').toUpperCase()}
                </span>
                <span className={getPriorityBadge(assignment.priority)}>
                  {assignment.priority.toUpperCase()} PRIORITY
                </span>
              </div>
              <p className="text-sm text-gray-600">
                Assigned to {assignment.assignee.full_name} •
                {new Date(assignment.assigned_at).toLocaleDateString()}
                {assignment.due_date && (
                  <> • Due {new Date(assignment.due_date).toLocaleDateString()}</>
                )}
              </p>
            </div>
          </div>

          <div className="flex gap-3">
            {assignment.status === 'active' && (
              <>
                <Button
                  variant="outline"
                  onClick={() => updateAssignmentStatus('on_hold')}
                  className="flex items-center gap-2"
                >
                  <AlertTriangle className="h-4 w-4" />
                  Put on Hold
                </Button>
                <Button
                  onClick={() => updateAssignmentStatus('completed')}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  Mark Complete
                </Button>
              </>
            )}

            {assignment.status === 'on_hold' && (
              <Button
                onClick={() => updateAssignmentStatus('active')}
                className="flex items-center gap-2"
              >
                <Clock className="h-4 w-4" />
                Resume
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() => router.push(`/manager/assignments/${assignmentId}/edit`)}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Progress</p>
              <p className="text-2xl font-bold text-blue-600">{getOverallProgress()}%</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Tasks</p>
              <p className="text-2xl font-bold text-purple-600">{assignment.tasks?.length || 0}</p>
            </div>
            <Target className="h-8 w-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{getCompletedTasks()}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Estimated Hours</p>
              <p className="text-2xl font-bold text-amber-600">{assignment.estimated_hours}</p>
            </div>
            <Clock className="h-8 w-8 text-amber-500" />
          </div>
        </div>
      </div>

      {/* Overview Content */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Assignment Overview</h2>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
            <p className="text-gray-700">{assignment.description || 'No description provided.'}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Assignment Objectives</h4>
              {assignment.objectives && assignment.objectives.length > 0 ? (
                <ul className="space-y-2">
                  {assignment.objectives.map((objective, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Target className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{objective}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-sm">No objectives defined</p>
              )}
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Expected Deliverables</h4>
              {assignment.deliverables && assignment.deliverables.length > 0 ? (
                <ul className="space-y-2">
                  {assignment.deliverables.map((deliverable, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{deliverable}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-sm">No deliverables defined</p>
              )}
            </div>
          </div>

          {assignment.notes && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Additional Notes</h4>
              <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
                <p className="text-amber-800">{assignment.notes}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}