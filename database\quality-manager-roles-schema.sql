-- =====================================================
-- QUALITY TEAM & MANAGER ROLES IMPLEMENTATION SCHEMA
-- Comprehensive implementation for workflow document requirements
-- =====================================================

-- 1. ADD NEW ROLES TO PROFILES TABLE
-- Update role enum to include new roles
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'quality_team';
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'manager';

-- If enum doesn't exist, create it
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('client', 'designer', 'admin', 'quality_team', 'manager');
        ALTER TABLE profiles ALTER COLUMN role TYPE user_role USING role::user_role;
    END IF;
END $$;

-- 2. QUALITY STANDARDS TABLE
-- Comprehensive quality checklist system
CREATE TABLE IF NOT EXISTS quality_standards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category VARCHAR(100) NOT NULL, -- 'design', 'branding', 'web', 'print', etc.
    standard_name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    criteria JSONB NOT NULL, -- Array of specific criteria to check
    is_mandatory BOOLEAN DEFAULT TRUE,
    weight INTEGER DEFAULT 1, -- Importance weight (1-5)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES profiles(id)
);

-- 3. QUALITY REVIEWS TABLE
-- Track all quality reviews for submissions
CREATE TABLE IF NOT EXISTS quality_reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    submission_id UUID, -- Reference to specific submission/deliverable
    reviewer_id UUID REFERENCES profiles(id), -- Quality team member
    designer_id UUID REFERENCES profiles(id),
    review_type VARCHAR(50) DEFAULT 'submission' CHECK (review_type IN ('proposal', 'submission', 'revision', 'final')),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'needs_revision')),
    overall_score INTEGER CHECK (overall_score >= 1 AND overall_score <= 5),
    standards_checked JSONB, -- Array of standard IDs checked
    feedback TEXT,
    revision_notes TEXT,
    revision_count INTEGER DEFAULT 0,
    time_spent_minutes INTEGER,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. QUALITY FEEDBACK TABLE
-- Detailed feedback for each quality standard
CREATE TABLE IF NOT EXISTS quality_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID REFERENCES quality_reviews(id) ON DELETE CASCADE,
    standard_id UUID REFERENCES quality_standards(id),
    passed BOOLEAN NOT NULL,
    score INTEGER CHECK (score >= 1 AND score <= 5),
    comments TEXT,
    suggestions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. PROJECT ASSIGNMENTS TABLE
-- Manager assignment and oversight
CREATE TABLE IF NOT EXISTS project_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'transferred')),
    priority VARCHAR(50) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    notes TEXT,
    created_by UUID REFERENCES profiles(id), -- Admin who made assignment
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. MANAGER ACTIVITIES TABLE
-- Track all manager activities and interventions
CREATE TABLE IF NOT EXISTS manager_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    manager_id UUID REFERENCES profiles(id),
    project_id UUID REFERENCES projects(id),
    activity_type VARCHAR(100) NOT NULL, -- 'negotiation_guidance', 'milestone_review', 'communication', 'escalation', etc.
    description TEXT NOT NULL,
    participants JSONB, -- Array of user IDs involved
    outcome VARCHAR(100),
    time_spent_minutes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. NEGOTIATION SESSIONS TABLE
-- Track manager-guided negotiations
CREATE TABLE IF NOT EXISTS negotiation_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES profiles(id),
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    session_type VARCHAR(50) DEFAULT 'pricing' CHECK (session_type IN ('pricing', 'timeline', 'scope', 'terms')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    initial_terms JSONB,
    final_terms JSONB,
    manager_notes TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. ESCROW MANAGEMENT TABLE
-- Enhanced escrow with manager coordination
CREATE TABLE IF NOT EXISTS escrow_releases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    milestone_id UUID REFERENCES project_milestones(id),
    manager_id UUID REFERENCES profiles(id),
    amount DECIMAL(10,2) NOT NULL,
    release_type VARCHAR(50) DEFAULT 'milestone' CHECK (release_type IN ('milestone', 'partial', 'final', 'dispute')),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'released', 'disputed', 'cancelled')),
    manager_approval_at TIMESTAMP WITH TIME ZONE,
    admin_approval_at TIMESTAMP WITH TIME ZONE,
    released_at TIMESTAMP WITH TIME ZONE,
    manager_notes TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. CLIENT SATISFACTION TRACKING
-- Manager-coordinated satisfaction collection
CREATE TABLE IF NOT EXISTS client_satisfaction (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    manager_id UUID REFERENCES profiles(id),
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
    communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
    quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
    timeline_rating INTEGER CHECK (timeline_rating >= 1 AND timeline_rating <= 5),
    value_rating INTEGER CHECK (value_rating >= 1 AND value_rating <= 5),
    feedback_text TEXT,
    would_recommend BOOLEAN,
    collected_by UUID REFERENCES profiles(id), -- Manager who collected feedback
    collected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. WORKFLOW NOTIFICATIONS TABLE
-- System notifications for new workflow
CREATE TABLE IF NOT EXISTS workflow_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    recipient_id UUID REFERENCES profiles(id),
    sender_id UUID REFERENCES profiles(id),
    notification_type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    action_url TEXT,
    priority VARCHAR(50) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. ADD INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_quality_reviews_project_id ON quality_reviews(project_id);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_reviewer_id ON quality_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_status ON quality_reviews(status);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_created_at ON quality_reviews(created_at);

CREATE INDEX IF NOT EXISTS idx_project_assignments_manager_id ON project_assignments(manager_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON project_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_status ON project_assignments(status);

CREATE INDEX IF NOT EXISTS idx_manager_activities_manager_id ON manager_activities(manager_id);
CREATE INDEX IF NOT EXISTS idx_manager_activities_project_id ON manager_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_manager_activities_activity_type ON manager_activities(activity_type);

CREATE INDEX IF NOT EXISTS idx_escrow_releases_project_id ON escrow_releases(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_manager_id ON escrow_releases(manager_id);
CREATE INDEX IF NOT EXISTS idx_escrow_releases_status ON escrow_releases(status);

CREATE INDEX IF NOT EXISTS idx_workflow_notifications_recipient_id ON workflow_notifications(recipient_id);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_read_at ON workflow_notifications(read_at);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_created_at ON workflow_notifications(created_at);

-- 12. ENABLE ROW LEVEL SECURITY
ALTER TABLE quality_standards ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE manager_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE negotiation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_satisfaction ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_notifications ENABLE ROW LEVEL SECURITY;

-- 13. ROW LEVEL SECURITY POLICIES

-- Quality Standards Policies
CREATE POLICY "Admin and Quality Team can manage quality standards" ON quality_standards
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team')
        )
    );

CREATE POLICY "All roles can view quality standards" ON quality_standards
    FOR SELECT USING (TRUE);

-- Quality Reviews Policies
CREATE POLICY "Quality Team can manage quality reviews" ON quality_reviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team')
        )
    );

CREATE POLICY "Designers can view their quality reviews" ON quality_reviews
    FOR SELECT USING (
        designer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team', 'manager')
        )
    );

-- Quality Feedback Policies
CREATE POLICY "Quality Team can manage feedback" ON quality_feedback
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team')
        )
    );

CREATE POLICY "Designers can view their feedback" ON quality_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM quality_reviews qr
            WHERE qr.id = quality_feedback.review_id
            AND qr.designer_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('admin', 'quality_team', 'manager')
        )
    );

-- Project Assignments Policies
CREATE POLICY "Admin can manage project assignments" ON project_assignments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Managers can view their assignments" ON project_assignments
    FOR SELECT USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Manager Activities Policies
CREATE POLICY "Managers can manage their activities" ON manager_activities
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Negotiation Sessions Policies
CREATE POLICY "Participants can view negotiation sessions" ON negotiation_sessions
    FOR SELECT USING (
        manager_id = auth.uid() OR
        client_id = auth.uid() OR
        designer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Managers can manage negotiation sessions" ON negotiation_sessions
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Escrow Releases Policies
CREATE POLICY "Managers and Admin can manage escrow releases" ON escrow_releases
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Project participants can view escrow releases" ON escrow_releases
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM projects p
            WHERE p.id = escrow_releases.project_id
            AND (p.client_id = auth.uid() OR p.designer_id = auth.uid())
        ) OR
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Client Satisfaction Policies
CREATE POLICY "Managers and Admin can manage satisfaction" ON client_satisfaction
    FOR ALL USING (
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Project participants can view satisfaction" ON client_satisfaction
    FOR SELECT USING (
        client_id = auth.uid() OR
        designer_id = auth.uid() OR
        manager_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Workflow Notifications Policies
CREATE POLICY "Users can view their notifications" ON workflow_notifications
    FOR SELECT USING (recipient_id = auth.uid());

CREATE POLICY "Users can update their notifications" ON workflow_notifications
    FOR UPDATE USING (recipient_id = auth.uid());

CREATE POLICY "System can create notifications" ON workflow_notifications
    FOR INSERT WITH CHECK (TRUE);

-- 14. COMPREHENSIVE QUALITY STANDARDS DATA
-- Insert comprehensive quality checklist for all design categories

INSERT INTO quality_standards (category, standard_name, description, criteria, is_mandatory, weight) VALUES
-- DESIGN FUNDAMENTALS
('design', 'Visual Hierarchy', 'Clear visual hierarchy with proper emphasis and flow',
 '["Clear focal points established", "Proper use of size, color, and contrast", "Logical reading flow", "Important elements stand out", "Secondary elements properly subdued"]',
 true, 5),

('design', 'Color Theory', 'Appropriate color usage and harmony',
 '["Colors align with brand guidelines", "Proper contrast ratios (WCAG compliant)", "Harmonious color palette", "Colors support the message", "Accessibility considerations met"]',
 true, 4),

('design', 'Typography', 'Professional typography choices and implementation',
 '["Readable font choices", "Proper font hierarchy", "Consistent spacing", "Appropriate font sizes", "Good line height and letter spacing"]',
 true, 4),

('design', 'Layout & Composition', 'Well-structured layout with proper spacing',
 '["Balanced composition", "Proper use of white space", "Aligned elements", "Consistent margins and padding", "Grid system followed"]',
 true, 5),

('design', 'Brand Consistency', 'Adherence to brand guidelines and identity',
 '["Logo usage correct", "Brand colors used properly", "Consistent with brand voice", "Style guide followed", "Brand elements properly placed"]',
 true, 5),

-- TECHNICAL QUALITY
('technical', 'File Quality', 'Technical specifications and file integrity',
 '["Correct file formats provided", "Appropriate resolution/DPI", "Clean file organization", "Proper naming conventions", "No corrupted files"]',
 true, 5),

('technical', 'Print Readiness', 'Print-specific technical requirements',
 '["CMYK color mode for print", "Proper bleed and margins", "High resolution (300 DPI minimum)", "Embedded fonts", "Print-safe colors used"]',
 true, 4),

('technical', 'Web Optimization', 'Web-specific technical requirements',
 '["RGB color mode for web", "Optimized file sizes", "Responsive design considerations", "Web-safe formats", "Proper compression applied"]',
 true, 4),

-- CONTENT QUALITY
('content', 'Spelling & Grammar', 'Text accuracy and professionalism',
 '["No spelling errors", "Proper grammar usage", "Consistent terminology", "Professional tone", "Accurate information"]',
 true, 3),

('content', 'Content Hierarchy', 'Logical content organization and flow',
 '["Clear information hierarchy", "Logical content flow", "Proper headings structure", "Scannable content", "Key messages prominent"]',
 true, 4),

-- USER EXPERIENCE
('ux', 'Usability', 'User-friendly design and navigation',
 '["Intuitive navigation", "Clear call-to-actions", "User-friendly interface", "Accessibility considerations", "Mobile-friendly design"]',
 true, 4),

('ux', 'Functionality', 'All interactive elements work properly',
 '["Links work correctly", "Forms function properly", "Interactive elements responsive", "No broken functionality", "Cross-browser compatibility"]',
 true, 5),

-- DELIVERABLE COMPLETENESS
('deliverable', 'Completeness', 'All required deliverables provided',
 '["All requested items delivered", "Proper file formats included", "Source files provided when required", "Documentation included", "Revision history maintained"]',
 true, 5),

('deliverable', 'Organization', 'Professional file organization and delivery',
 '["Files properly organized", "Clear folder structure", "Descriptive file names", "Version control maintained", "Easy to locate assets"]',
 true, 3);

-- 15. WORKFLOW AUTOMATION FUNCTIONS

-- Function to automatically assign quality review when designer submits work
CREATE OR REPLACE FUNCTION auto_assign_quality_review()
RETURNS TRIGGER AS $$
DECLARE
    available_reviewer UUID;
BEGIN
    -- Find available quality team member with least current workload
    SELECT p.id INTO available_reviewer
    FROM profiles p
    LEFT JOIN quality_reviews qr ON qr.reviewer_id = p.id AND qr.status IN ('pending', 'in_review')
    WHERE p.role = 'quality_team' AND p.is_active = true
    GROUP BY p.id
    ORDER BY COUNT(qr.id) ASC, RANDOM()
    LIMIT 1;

    -- Create quality review record
    IF available_reviewer IS NOT NULL THEN
        INSERT INTO quality_reviews (
            project_id, reviewer_id, designer_id, review_type, status
        ) VALUES (
            NEW.id, available_reviewer, NEW.designer_id, 'submission', 'pending'
        );

        -- Create notification for quality reviewer
        INSERT INTO workflow_notifications (
            recipient_id, notification_type, title, message, priority
        ) VALUES (
            available_reviewer, 'quality_review_assigned',
            'New Quality Review Assigned',
            'A new submission requires quality review for project: ' || NEW.title,
            'normal'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle quality review completion
CREATE OR REPLACE FUNCTION handle_quality_review_completion()
RETURNS TRIGGER AS $$
BEGIN
    -- If quality review is approved, notify manager
    IF NEW.status = 'approved' AND OLD.status != 'approved' THEN
        INSERT INTO workflow_notifications (
            recipient_id, notification_type, title, message, priority
        ) SELECT
            pa.manager_id, 'quality_approved',
            'Quality Review Approved',
            'Submission approved by quality team for project',
            'normal'
        FROM project_assignments pa
        WHERE pa.project_id = NEW.project_id AND pa.status = 'active';

    -- If quality review is rejected, notify designer and manager
    ELSIF NEW.status = 'rejected' AND OLD.status != 'rejected' THEN
        -- Notify designer
        INSERT INTO workflow_notifications (
            recipient_id, notification_type, title, message, priority
        ) VALUES (
            NEW.designer_id, 'quality_rejected',
            'Submission Requires Revision',
            'Your submission needs revision based on quality review feedback',
            'high'
        );

        -- Notify manager
        INSERT INTO workflow_notifications (
            recipient_id, notification_type, title, message, priority
        ) SELECT
            pa.manager_id, 'quality_rejected',
            'Quality Review Rejected',
            'Submission rejected by quality team - designer notified for revision',
            'normal'
        FROM project_assignments pa
        WHERE pa.project_id = NEW.project_id AND pa.status = 'active';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle escrow release coordination
CREATE OR REPLACE FUNCTION coordinate_escrow_release()
RETURNS TRIGGER AS $$
BEGIN
    -- When milestone is completed, create escrow release request
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        INSERT INTO escrow_releases (
            project_id, milestone_id, amount, manager_id, status
        ) SELECT
            NEW.project_id, NEW.id, NEW.amount, pa.manager_id, 'pending'
        FROM project_assignments pa
        WHERE pa.project_id = NEW.project_id AND pa.status = 'active';

        -- Notify manager
        INSERT INTO workflow_notifications (
            recipient_id, notification_type, title, message, priority
        ) SELECT
            pa.manager_id, 'escrow_release_pending',
            'Escrow Release Required',
            'Milestone completed - escrow release approval needed',
            'high'
        FROM project_assignments pa
        WHERE pa.project_id = NEW.project_id AND pa.status = 'active';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to track manager activities automatically
CREATE OR REPLACE FUNCTION log_manager_activity(
    p_manager_id UUID,
    p_project_id UUID,
    p_activity_type VARCHAR,
    p_description TEXT,
    p_participants JSONB DEFAULT NULL,
    p_outcome VARCHAR DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO manager_activities (
        manager_id, project_id, activity_type, description,
        participants, outcome
    ) VALUES (
        p_manager_id, p_project_id, p_activity_type, p_description,
        p_participants, p_outcome
    ) RETURNING id INTO activity_id;

    RETURN activity_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check revision limits and escalate
CREATE OR REPLACE FUNCTION check_revision_limits()
RETURNS TRIGGER AS $$
BEGIN
    -- If revision count reaches 3, escalate to manager and admin
    IF NEW.revision_count >= 3 AND OLD.revision_count < 3 THEN
        -- Notify manager
        INSERT INTO workflow_notifications (
            recipient_id, notification_type, title, message, priority
        ) SELECT
            pa.manager_id, 'quality_escalation',
            'Quality Review Escalation',
            'Designer has reached 3 revision limit - manager intervention required',
            'urgent'
        FROM project_assignments pa
        WHERE pa.project_id = NEW.project_id AND pa.status = 'active';

        -- Notify admin
        INSERT INTO workflow_notifications (
            recipient_id, notification_type, title, message, priority
        ) SELECT
            p.id, 'quality_escalation',
            'Quality Review Escalation',
            'Project requires admin attention - 3 revision limit reached',
            'urgent'
        FROM profiles p
        WHERE p.role = 'admin' AND p.is_active = true;

        -- Log manager activity
        PERFORM log_manager_activity(
            (SELECT pa.manager_id FROM project_assignments pa WHERE pa.project_id = NEW.project_id AND pa.status = 'active'),
            NEW.project_id,
            'quality_escalation',
            'Quality review escalated due to revision limit reached',
            jsonb_build_array(NEW.designer_id, NEW.reviewer_id),
            'escalated_to_admin'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 16. CREATE TRIGGERS

-- Trigger for quality review assignment (when project status changes to submitted)
-- Note: This will need to be connected to your existing project submission workflow

-- Trigger for quality review completion
CREATE TRIGGER trigger_quality_review_completion
    AFTER UPDATE ON quality_reviews
    FOR EACH ROW
    WHEN (OLD.status IS DISTINCT FROM NEW.status)
    EXECUTE FUNCTION handle_quality_review_completion();

-- Trigger for escrow release coordination
CREATE TRIGGER trigger_escrow_coordination
    AFTER UPDATE ON project_milestones
    FOR EACH ROW
    WHEN (OLD.status IS DISTINCT FROM NEW.status)
    EXECUTE FUNCTION coordinate_escrow_release();

-- Trigger for revision limit checking
CREATE TRIGGER trigger_revision_limits
    AFTER UPDATE ON quality_reviews
    FOR EACH ROW
    WHEN (OLD.revision_count IS DISTINCT FROM NEW.revision_count)
    EXECUTE FUNCTION check_revision_limits();

-- 17. UPDATE EXISTING TABLES FOR INTEGRATION

-- Add quality status to projects table if not exists
ALTER TABLE projects ADD COLUMN IF NOT EXISTS quality_status VARCHAR(50) DEFAULT 'pending'
    CHECK (quality_status IN ('pending', 'in_review', 'approved', 'rejected', 'revision_needed'));

-- Add manager assignment to projects table if not exists
ALTER TABLE projects ADD COLUMN IF NOT EXISTS assigned_manager_id UUID REFERENCES profiles(id);

-- Add quality review requirement flag
ALTER TABLE projects ADD COLUMN IF NOT EXISTS requires_quality_review BOOLEAN DEFAULT TRUE;

-- Update project milestones to include manager approval
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS manager_approved_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS manager_approved_by UUID REFERENCES profiles(id);

-- 18. COMMENTS FOR DOCUMENTATION
COMMENT ON TABLE quality_standards IS 'Comprehensive quality checklist standards for all design categories';
COMMENT ON TABLE quality_reviews IS 'Quality team reviews of all designer submissions with mandatory approval workflow';
COMMENT ON TABLE quality_feedback IS 'Detailed feedback for each quality standard checked during review';
COMMENT ON TABLE project_assignments IS 'Manager assignments to projects for oversight and coordination';
COMMENT ON TABLE manager_activities IS 'Comprehensive log of all manager activities and interventions';
COMMENT ON TABLE negotiation_sessions IS 'Manager-guided negotiation sessions between clients and designers';
COMMENT ON TABLE escrow_releases IS 'Manager-coordinated escrow releases with milestone-based approvals';
COMMENT ON TABLE client_satisfaction IS 'Manager-collected client satisfaction feedback and ratings';
COMMENT ON TABLE workflow_notifications IS 'System notifications for the enhanced workflow with quality and manager roles';
