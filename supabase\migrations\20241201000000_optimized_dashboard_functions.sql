-- Optimized dashboard stats functions for better performance

-- Function for client dashboard stats
CREATE OR REPLACE FUNCTION get_client_dashboard_stats(client_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH project_stats AS (
        SELECT 
            COUNT(*) as total_projects,
            COUNT(*) FILTER (WHERE status = 'in_progress') as active_projects,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_projects
        FROM projects 
        WHERE client_id = get_client_dashboard_stats.client_id
    ),
    connection_stats AS (
        SELECT COUNT(*) as connected_designers
        FROM connections 
        WHERE client_id = get_client_dashboard_stats.client_id 
        AND status = 'active'
    ),
    brief_stats AS (
        SELECT 
            COUNT(*) FILTER (WHERE status IN ('pending', 'assigned', 'proposal_received')) as active_briefs,
            array_agg(id) as brief_ids
        FROM project_briefs 
        WHERE client_id = get_client_dashboard_stats.client_id
    ),
    proposal_stats AS (
        SELECT COUNT(*) as pending_proposals
        FROM project_proposals_enhanced p
        JOIN brief_stats b ON p.brief_id = ANY(b.brief_ids)
        WHERE p.status IN ('submitted', 'under_review')
    )
    SELECT json_build_object(
        'totalProjects', COALESCE(ps.total_projects, 0),
        'activeProjects', COALESCE(ps.active_projects, 0),
        'completedProjects', COALESCE(ps.completed_projects, 0),
        'connectedDesigners', COALESCE(cs.connected_designers, 0),
        'activeBriefs', COALESCE(bs.active_briefs, 0),
        'pendingProposals', COALESCE(prs.pending_proposals, 0)
    ) INTO result
    FROM project_stats ps
    CROSS JOIN connection_stats cs
    CROSS JOIN brief_stats bs
    CROSS JOIN proposal_stats prs;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for designer dashboard stats
CREATE OR REPLACE FUNCTION get_designer_dashboard_stats(designer_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH project_stats AS (
        SELECT 
            COUNT(*) as total_projects,
            COUNT(*) FILTER (WHERE status = 'in_progress') as active_projects,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_projects
        FROM projects 
        WHERE designer_id = get_designer_dashboard_stats.designer_id
    ),
    proposal_stats AS (
        SELECT 
            COUNT(*) as total_proposals,
            COUNT(*) FILTER (WHERE status = 'submitted') as pending_proposals,
            COUNT(*) FILTER (WHERE status = 'accepted') as accepted_proposals
        FROM project_proposals_enhanced 
        WHERE designer_id = get_designer_dashboard_stats.designer_id
    ),
    review_stats AS (
        SELECT 
            COUNT(*) as total_reviews,
            COALESCE(AVG(rating), 0) as average_rating
        FROM project_reviews 
        WHERE designer_id = get_designer_dashboard_stats.designer_id
    ),
    earnings_stats AS (
        SELECT COALESCE(SUM(amount), 0) as total_earnings
        FROM transactions 
        WHERE designer_id = get_designer_dashboard_stats.designer_id 
        AND status = 'completed'
        AND type = 'payout'
    )
    SELECT json_build_object(
        'totalProjects', COALESCE(ps.total_projects, 0),
        'activeProjects', COALESCE(ps.active_projects, 0),
        'completedProjects', COALESCE(ps.completed_projects, 0),
        'totalProposals', COALESCE(prs.total_proposals, 0),
        'pendingProposals', COALESCE(prs.pending_proposals, 0),
        'acceptedProposals', COALESCE(prs.accepted_proposals, 0),
        'totalReviews', COALESCE(rs.total_reviews, 0),
        'averageRating', ROUND(COALESCE(rs.average_rating, 0), 2),
        'totalEarnings', COALESCE(es.total_earnings, 0)
    ) INTO result
    FROM project_stats ps
    CROSS JOIN proposal_stats prs
    CROSS JOIN review_stats rs
    CROSS JOIN earnings_stats es;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for admin dashboard stats
CREATE OR REPLACE FUNCTION get_admin_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH user_stats AS (
        SELECT 
            COUNT(*) as total_users,
            COUNT(*) FILTER (WHERE role = 'client') as total_clients,
            COUNT(*) FILTER (WHERE role = 'designer') as total_designers,
            COUNT(*) FILTER (WHERE role = 'admin') as total_admins
        FROM profiles 
        WHERE is_active = true
    ),
    project_stats AS (
        SELECT 
            COUNT(*) as total_projects,
            COUNT(*) FILTER (WHERE status = 'in_progress') as active_projects,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_projects,
            COUNT(*) FILTER (WHERE status = 'pending') as pending_projects
        FROM projects
    ),
    revenue_stats AS (
        SELECT 
            COALESCE(SUM(amount) FILTER (WHERE type = 'platform_fee' AND created_at >= CURRENT_DATE - INTERVAL '30 days'), 0) as monthly_revenue,
            COALESCE(SUM(amount) FILTER (WHERE type = 'platform_fee'), 0) as total_revenue
        FROM transactions 
        WHERE status = 'completed'
    ),
    activity_stats AS (
        SELECT 
            COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as new_users_week,
            COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_month
        FROM profiles
    )
    SELECT json_build_object(
        'totalUsers', COALESCE(us.total_users, 0),
        'totalClients', COALESCE(us.total_clients, 0),
        'totalDesigners', COALESCE(us.total_designers, 0),
        'totalAdmins', COALESCE(us.total_admins, 0),
        'totalProjects', COALESCE(ps.total_projects, 0),
        'activeProjects', COALESCE(ps.active_projects, 0),
        'completedProjects', COALESCE(ps.completed_projects, 0),
        'pendingProjects', COALESCE(ps.pending_projects, 0),
        'monthlyRevenue', COALESCE(rs.monthly_revenue, 0),
        'totalRevenue', COALESCE(rs.total_revenue, 0),
        'newUsersWeek', COALESCE(as.new_users_week, 0),
        'newUsersMonth', COALESCE(as.new_users_month, 0)
    ) INTO result
    FROM user_stats us
    CROSS JOIN project_stats ps
    CROSS JOIN revenue_stats rs
    CROSS JOIN activity_stats as;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_client_status ON projects(client_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_designer_status ON projects(designer_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_connections_client_status ON connections(client_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_briefs_client_status ON project_briefs(client_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proposals_designer_status ON project_proposals_enhanced(designer_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proposals_brief_status ON project_proposals_enhanced(brief_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_designer ON project_reviews(designer_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_designer_type ON transactions(designer_id, type, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_client_type ON transactions(client_id, type, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_role_active ON profiles(role, is_active);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_created_at_type ON transactions(created_at, type, status);

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_client_dashboard_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_designer_dashboard_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_admin_dashboard_stats() TO authenticated;
