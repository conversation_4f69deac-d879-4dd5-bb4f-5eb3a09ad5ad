"use client";

import { useRef } from "react";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import { Button } from "../ui/button";
import Link from "next/link";


const FAQsSection = () => {
  const sectionRef = useRef(null);

  return (
    <section
      ref={sectionRef}
      className="py-24 bg-gray-50"
    >


      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">FAQs</h2>
            <p className="text-lg text-gray-600 mb-8">
              Find answers to common questions about our process, services, design approach, and more.
            </p>

            <Link href="/faq">
              <Button
                variant="default"
                size="lg"
                className="inline-flex items-center"
              >
                View All FAQs
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default FAQsSection;
