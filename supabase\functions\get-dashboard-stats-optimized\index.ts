import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { user_id, user_role } = await req.json()

    if (!user_id || !user_role) {
      return new Response(
        JSON.stringify({ error: 'Missing user_id or user_role' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    let stats = {}

    if (user_role === 'client') {
      // Optimized client stats with single query
      const { data: clientStats, error } = await supabaseClient.rpc('get_client_dashboard_stats', {
        client_id: user_id
      })

      if (error) {
        // Fallback to individual queries
        const [projectsResult, connectionsResult, briefsResult] = await Promise.all([
          supabaseClient
            .from('projects')
            .select('status')
            .eq('client_id', user_id),
          supabaseClient
            .from('connections')
            .select('*', { count: 'exact', head: true })
            .eq('client_id', user_id)
            .eq('status', 'active'),
          supabaseClient
            .from('project_briefs')
            .select('status')
            .eq('client_id', user_id)
        ])

        const projects = projectsResult.data || []
        stats = {
          totalProjects: projects.length,
          activeProjects: projects.filter(p => p.status === 'in_progress').length,
          completedProjects: projects.filter(p => p.status === 'completed').length,
          connectedDesigners: connectionsResult.count || 0,
          activeBriefs: (briefsResult.data || []).filter(b => 
            ['pending', 'assigned', 'proposal_received'].includes(b.status)
          ).length
        }
      } else {
        stats = clientStats
      }

    } else if (user_role === 'designer') {
      // Optimized designer stats
      const { data: designerStats, error } = await supabaseClient.rpc('get_designer_dashboard_stats', {
        designer_id: user_id
      })

      if (error) {
        // Fallback to individual queries
        const [projectsResult, proposalsResult, reviewsResult] = await Promise.all([
          supabaseClient
            .from('projects')
            .select('status')
            .eq('designer_id', user_id),
          supabaseClient
            .from('project_proposals_enhanced')
            .select('status')
            .eq('designer_id', user_id),
          supabaseClient
            .from('project_reviews')
            .select('rating')
            .eq('designer_id', user_id)
        ])

        const projects = projectsResult.data || []
        const proposals = proposalsResult.data || []
        const reviews = reviewsResult.data || []

        stats = {
          totalProjects: projects.length,
          activeProjects: projects.filter(p => p.status === 'in_progress').length,
          completedProjects: projects.filter(p => p.status === 'completed').length,
          totalProposals: proposals.length,
          pendingProposals: proposals.filter(p => p.status === 'submitted').length,
          averageRating: reviews.length > 0 
            ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length 
            : 0
        }
      } else {
        stats = designerStats
      }

    } else if (user_role === 'admin') {
      // Optimized admin stats
      const { data: adminStats, error } = await supabaseClient.rpc('get_admin_dashboard_stats')

      if (error) {
        // Fallback to individual queries
        const [usersResult, projectsResult, revenueResult] = await Promise.all([
          supabaseClient
            .from('profiles')
            .select('role, is_active')
            .eq('is_active', true),
          supabaseClient
            .from('projects')
            .select('status, budget'),
          supabaseClient
            .from('transactions')
            .select('amount, type')
            .eq('status', 'completed')
            .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        ])

        const users = usersResult.data || []
        const projects = projectsResult.data || []
        const transactions = revenueResult.data || []

        stats = {
          totalUsers: users.length,
          totalClients: users.filter(u => u.role === 'client').length,
          totalDesigners: users.filter(u => u.role === 'designer').length,
          totalProjects: projects.length,
          activeProjects: projects.filter(p => p.status === 'in_progress').length,
          completedProjects: projects.filter(p => p.status === 'completed').length,
          monthlyRevenue: transactions
            .filter(t => t.type === 'platform_fee')
            .reduce((sum, t) => sum + (t.amount || 0), 0)
        }
      } else {
        stats = adminStats
      }
    }

    return new Response(
      JSON.stringify(stats),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in get-dashboard-stats-optimized:', error)
    
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
