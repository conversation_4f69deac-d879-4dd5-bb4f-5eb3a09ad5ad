"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';

export function RootRedirect() {
  const { user, profile, loading } = useOptimizedAuth();
  const router = useRouter();
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    // Only check once when auth is loaded and we haven't checked yet
    if (!loading && !hasChecked) {
      setHasChecked(true);

      if (user && profile) {
        // User is logged in, redirect to their dashboard
        switch (profile.role) {
          case 'admin':
            router.push('/admin/dashboard');
            break;
          case 'designer':
            router.push('/designer/dashboard');
            break;
          case 'client':
            router.push('/client/dashboard');
            break;
          case 'quality_team':
            router.push('/quality/dashboard');
            break;
          case 'manager':
            router.push('/manager/dashboard');
            break;
          default:
            // Invalid role, stay on home page
            break;
        }
      }
      // If no user, stay on home page
    }
  }, [user, profile, loading, router, hasChecked]);

  // Don't render anything, this is just for redirects
  return null;
}
