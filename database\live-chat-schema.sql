-- Live Chat System Schema
-- Comprehensive live chat system for website visitors and admin support

-- 1. Live Chat Sessions Table
-- Tracks individual chat sessions from website visitors
CREATE TABLE IF NOT EXISTS live_chat_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id TEXT NOT NULL UNIQUE, -- Browser session identifier
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL, -- NULL for anonymous users
    visitor_name TEXT, -- Name provided by anonymous users
    visitor_email TEXT, -- Email provided by anonymous users
    status TEXT DEFAULT 'waiting' CHECK (status IN ('waiting', 'active', 'ended', 'abandoned')),
    admin_joined_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Visitor info for better support
    user_agent TEXT,
    ip_address INET,
    referrer_url TEXT,
    current_page TEXT,
    
    -- Chat metadata
    total_messages INTEGER DEFAULT 0,
    admin_response_time INTERVAL, -- Average response time
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    feedback TEXT
);

-- 2. Live Chat Messages Table
-- Stores all messages in live chat sessions
CREATE TABLE IF NOT EXISTS live_chat_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID REFERENCES live_chat_sessions(id) ON DELETE CASCADE,
    sender_type TEXT NOT NULL CHECK (sender_type IN ('visitor', 'admin', 'system')),
    sender_id UUID REFERENCES profiles(id) ON DELETE SET NULL, -- NULL for anonymous visitors
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system')),
    
    -- Message metadata
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- File attachments (if any)
    attachment_url TEXT,
    attachment_name TEXT,
    attachment_type TEXT,
    attachment_size BIGINT
);

-- 3. Admin Online Status Table
-- Tracks when the admin is available for live chat
CREATE TABLE IF NOT EXISTS admin_chat_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    is_online BOOLEAN DEFAULT FALSE,
    status_message TEXT DEFAULT 'Available for chat',
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    auto_away_enabled BOOLEAN DEFAULT TRUE,
    away_message TEXT DEFAULT 'I''ll be back shortly',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(admin_id)
);

-- 4. Chat Templates Table
-- Pre-written responses for common questions
CREATE TABLE IF NOT EXISTS live_chat_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT DEFAULT 'general' CHECK (category IN ('greeting', 'general', 'services', 'pricing', 'closing')),
    shortcut TEXT, -- Quick access shortcut like /hello
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Chat Notifications Table
-- Track email notifications sent to admin when offline
CREATE TABLE IF NOT EXISTS live_chat_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID REFERENCES live_chat_sessions(id) ON DELETE CASCADE,
    notification_type TEXT CHECK (notification_type IN ('new_chat', 'offline_message', 'abandoned_chat')),
    recipient_email TEXT NOT NULL,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'failed', 'pending')),
    resend_id TEXT, -- Resend API message ID
    error_message TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_live_chat_sessions_status ON live_chat_sessions(status);
CREATE INDEX IF NOT EXISTS idx_live_chat_sessions_created_at ON live_chat_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_live_chat_sessions_user_id ON live_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_live_chat_messages_session_id ON live_chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_live_chat_messages_created_at ON live_chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_live_chat_messages_sender_type ON live_chat_messages(sender_type);
CREATE INDEX IF NOT EXISTS idx_admin_chat_status_admin_id ON admin_chat_status(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_chat_status_is_online ON admin_chat_status(is_online);
CREATE INDEX IF NOT EXISTS idx_live_chat_templates_category ON live_chat_templates(category);
CREATE INDEX IF NOT EXISTS idx_live_chat_templates_is_active ON live_chat_templates(is_active);

-- Row Level Security (RLS) Policies
ALTER TABLE live_chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE live_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_chat_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE live_chat_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE live_chat_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for live_chat_sessions
-- Admin can see all sessions, users can only see their own
CREATE POLICY "Admin can view all chat sessions" ON live_chat_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Users can view their own chat sessions" ON live_chat_sessions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin can update chat sessions" ON live_chat_sessions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- RLS Policies for live_chat_messages
-- Admin can see all messages, users can only see messages from their sessions
CREATE POLICY "Admin can view all chat messages" ON live_chat_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Users can view messages from their sessions" ON live_chat_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM live_chat_sessions
            WHERE live_chat_sessions.id = live_chat_messages.session_id
            AND live_chat_sessions.user_id = auth.uid()
        )
    );

CREATE POLICY "Admin can insert chat messages" ON live_chat_messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Anyone can insert visitor messages" ON live_chat_messages
    FOR INSERT WITH CHECK (sender_type = 'visitor');

-- RLS Policies for admin_chat_status
-- Only admin can manage their status
CREATE POLICY "Admin can manage their chat status" ON admin_chat_status
    FOR ALL USING (admin_id = auth.uid());

CREATE POLICY "Anyone can view admin online status" ON admin_chat_status
    FOR SELECT USING (true);

-- RLS Policies for live_chat_templates
-- Only admin can manage templates
CREATE POLICY "Admin can manage chat templates" ON live_chat_templates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- RLS Policies for live_chat_notifications
-- Only admin can view notifications
CREATE POLICY "Admin can view chat notifications" ON live_chat_notifications
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Insert default chat templates
INSERT INTO live_chat_templates (title, content, category, shortcut) VALUES
('Welcome Greeting', 'Hello! Welcome to Senior''s Archi-firm. How can I help you today?', 'greeting', '/hello'),
('Services Overview', 'We offer comprehensive architectural services including residential design, commercial projects, interior design, and project management. What type of project are you interested in?', 'services', '/services'),
('Consultation Info', 'We offer free initial consultations to discuss your project needs. Would you like to schedule a consultation?', 'general', '/consult'),
('Pricing Information', 'Our pricing varies based on project scope and complexity. I''d be happy to provide a detailed quote after understanding your specific requirements.', 'pricing', '/pricing'),
('Contact Information', 'You can reach <NAME_EMAIL> or through this chat. We typically respond within 24 hours to all inquiries.', 'general', '/contact'),
('Thank You', 'Thank you for contacting Senior''s Archi-firm! Is there anything else I can help you with today?', 'closing', '/thanks');

-- Create function to update session message count
CREATE OR REPLACE FUNCTION update_session_message_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE live_chat_sessions
    SET total_messages = total_messages + 1,
        updated_at = NOW()
    WHERE id = NEW.session_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update message count
CREATE TRIGGER trigger_update_session_message_count
    AFTER INSERT ON live_chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_session_message_count();

-- Create function to auto-update admin last activity
CREATE OR REPLACE FUNCTION update_admin_last_activity()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE admin_chat_status
    SET last_activity = NOW(),
        updated_at = NOW()
    WHERE admin_id = NEW.sender_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update admin activity when they send messages
CREATE TRIGGER trigger_update_admin_activity
    AFTER INSERT ON live_chat_messages
    FOR EACH ROW
    WHEN (NEW.sender_type = 'admin')
    EXECUTE FUNCTION update_admin_last_activity();

-- Comments for documentation
COMMENT ON TABLE live_chat_sessions IS 'Tracks individual live chat sessions from website visitors';
COMMENT ON TABLE live_chat_messages IS 'Stores all messages exchanged in live chat sessions';
COMMENT ON TABLE admin_chat_status IS 'Tracks admin availability and online status for live chat';
COMMENT ON TABLE live_chat_templates IS 'Pre-written response templates for common chat scenarios';
COMMENT ON TABLE live_chat_notifications IS 'Tracks email notifications sent to admin for offline chat events';
