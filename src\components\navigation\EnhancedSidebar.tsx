"use client";

import { useState, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { useNavigationPrefetch } from '@/hooks/useNavigationPrefetch';
import { cn } from '@/lib/utils';

interface SidebarItem {
  id: string;
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  prefetchRoute?: string;
}

interface EnhancedSidebarProps {
  items: SidebarItem[];
  className?: string;
  collapsed?: boolean;
  onItemClick?: (item: SidebarItem) => void;
}

export function EnhancedSidebar({ 
  items, 
  className, 
  collapsed = false,
  onItemClick 
}: EnhancedSidebarProps) {
  const pathname = usePathname();
  const { prefetchOnHover } = useNavigationPrefetch();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  const handleMouseEnter = useCallback((item: SidebarItem) => {
    setHoveredItem(item.id);
    
    // Prefetch route data on hover
    if (item.prefetchRoute) {
      const cleanup = prefetchOnHover(item.prefetchRoute);
      
      // Store cleanup function for potential cancellation
      return cleanup;
    }
  }, [prefetchOnHover]);

  const handleMouseLeave = useCallback(() => {
    setHoveredItem(null);
  }, []);

  const isActive = useCallback((href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  }, [pathname]);

  return (
    <nav className={cn("space-y-1", className)}>
      {items.map((item) => {
        const Icon = item.icon;
        const active = isActive(item.href);
        
        return (
          <motion.div
            key={item.id}
            initial={false}
            animate={{
              scale: hoveredItem === item.id ? 1.02 : 1,
            }}
            transition={{ duration: 0.2 }}
          >
            <Link
              href={item.href}
              onMouseEnter={() => handleMouseEnter(item)}
              onMouseLeave={handleMouseLeave}
              onClick={() => onItemClick?.(item)}
              className={cn(
                "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                active
                  ? "bg-brown-100 text-brown-900 border-r-2 border-brown-600"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                collapsed && "justify-center px-2"
              )}
            >
              <Icon
                className={cn(
                  "flex-shrink-0 h-5 w-5 transition-colors duration-200",
                  active ? "text-brown-600" : "text-gray-400 group-hover:text-gray-500",
                  !collapsed && "mr-3"
                )}
              />
              
              {!collapsed && (
                <>
                  <span className="flex-1 truncate">{item.label}</span>
                  
                  {item.badge && item.badge > 0 && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full"
                    >
                      {item.badge > 99 ? '99+' : item.badge}
                    </motion.span>
                  )}
                </>
              )}
              
              {/* Hover indicator */}
              {hoveredItem === item.id && (
                <motion.div
                  layoutId="sidebar-hover"
                  className="absolute inset-0 bg-brown-50 rounded-md -z-10"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                />
              )}
              
              {/* Active indicator */}
              {active && (
                <motion.div
                  layoutId="sidebar-active"
                  className="absolute inset-0 bg-brown-100 rounded-md -z-20"
                  initial={false}
                />
              )}
            </Link>
          </motion.div>
        );
      })}
    </nav>
  );
}

// Enhanced sidebar with role-specific items
interface RoleSidebarProps {
  role: 'admin' | 'designer' | 'client' | 'quality' | 'manager';
  className?: string;
  collapsed?: boolean;
  badges?: Record<string, number>;
}

export function RoleBasedSidebar({ role, className, collapsed, badges = {} }: RoleSidebarProps) {
  const sidebarItems = {
    admin: [
      { id: 'dashboard', label: 'Dashboard', href: '/admin/dashboard', prefetchRoute: 'dashboard' },
      { id: 'users', label: 'Users', href: '/admin/users', prefetchRoute: 'users' },
      { id: 'projects', label: 'Projects', href: '/admin/projects', prefetchRoute: 'projects' },
      { id: 'proposals', label: 'Proposals', href: '/admin/proposals', prefetchRoute: 'proposals' },
      { id: 'designers', label: 'Designers', href: '/admin/designers', prefetchRoute: 'designers' },
      { id: 'messages', label: 'Messages', href: '/admin/messages', prefetchRoute: 'messages', badge: badges.messages },
      { id: 'finance', label: 'Finance', href: '/admin/finance', prefetchRoute: 'finance' },
      { id: 'settings', label: 'Settings', href: '/admin/settings', prefetchRoute: 'profile' },
    ],
    designer: [
      { id: 'dashboard', label: 'Dashboard', href: '/designer/dashboard', prefetchRoute: 'dashboard' },
      { id: 'projects', label: 'Projects', href: '/designer/projects', prefetchRoute: 'projects' },
      { id: 'proposals', label: 'Proposals', href: '/designer/proposals', prefetchRoute: 'proposals' },
      { id: 'briefs', label: 'Briefs', href: '/designer/briefs', prefetchRoute: 'briefs', badge: badges.briefs },
      { id: 'clients', label: 'Clients', href: '/designer/clients', prefetchRoute: 'designers' },
      { id: 'portfolio', label: 'Portfolio', href: '/designer/portfolio', prefetchRoute: 'portfolio' },
      { id: 'messages', label: 'Messages', href: '/designer/messages', prefetchRoute: 'messages', badge: badges.messages },
      { id: 'settings', label: 'Settings', href: '/designer/settings', prefetchRoute: 'profile' },
    ],
    client: [
      { id: 'dashboard', label: 'Dashboard', href: '/client/dashboard', prefetchRoute: 'dashboard' },
      { id: 'projects', label: 'Projects', href: '/client/projects', prefetchRoute: 'projects' },
      { id: 'proposals', label: 'Proposals', href: '/client/proposals', prefetchRoute: 'proposals', badge: badges.proposals },
      { id: 'briefs', label: 'Briefs', href: '/client/briefs', prefetchRoute: 'briefs' },
      { id: 'designers', label: 'Designers', href: '/client/designers', prefetchRoute: 'designers' },
      { id: 'messages', label: 'Messages', href: '/client/messages', prefetchRoute: 'messages', badge: badges.messages },
      { id: 'payments', label: 'Payments', href: '/client/payments', prefetchRoute: 'payments' },
      { id: 'profile', label: 'Profile', href: '/client/profile', prefetchRoute: 'profile' },
    ],
    quality: [
      { id: 'dashboard', label: 'Dashboard', href: '/quality/dashboard', prefetchRoute: 'dashboard' },
      { id: 'reviews', label: 'Pending Reviews', href: '/quality/reviews', prefetchRoute: 'reviews', badge: badges.reviews },
      { id: 'standards', label: 'Quality Standards', href: '/quality/standards', prefetchRoute: 'standards' },
      { id: 'history', label: 'Review History', href: '/quality/history', prefetchRoute: 'history' },
      { id: 'analytics', label: 'Analytics', href: '/quality/analytics', prefetchRoute: 'analytics' },
      { id: 'performance', label: 'Team Performance', href: '/quality/performance', prefetchRoute: 'performance' },
      { id: 'settings', label: 'Settings', href: '/quality/settings', prefetchRoute: 'settings' },
    ],
    manager: [
      { id: 'dashboard', label: 'Dashboard', href: '/manager/dashboard', prefetchRoute: 'dashboard' },
      { id: 'projects', label: 'My Projects', href: '/manager/projects', prefetchRoute: 'projects' },
      { id: 'negotiations', label: 'Negotiations', href: '/manager/negotiations', prefetchRoute: 'negotiations', badge: badges.negotiations },
      { id: 'escrow', label: 'Escrow Management', href: '/manager/escrow', prefetchRoute: 'escrow' },
      { id: 'satisfaction', label: 'Client Satisfaction', href: '/manager/satisfaction', prefetchRoute: 'satisfaction' },
      { id: 'coordination', label: 'Team Coordination', href: '/manager/coordination', prefetchRoute: 'coordination' },
      { id: 'reports', label: 'Reports & Analytics', href: '/manager/reports', prefetchRoute: 'reports' },
      { id: 'calendar', label: 'Calendar', href: '/manager/calendar', prefetchRoute: 'calendar' },
      { id: 'settings', label: 'Settings', href: '/manager/settings', prefetchRoute: 'settings' },
    ],
  };

  // Import icons dynamically to avoid bundle bloat
  const getIcon = (id: string) => {
    const iconMap: Record<string, any> = {
      dashboard: () => import('lucide-react').then(mod => mod.BarChart3),
      users: () => import('lucide-react').then(mod => mod.Users),
      projects: () => import('lucide-react').then(mod => mod.FolderKanban),
      proposals: () => import('lucide-react').then(mod => mod.FileText),
      briefs: () => import('lucide-react').then(mod => mod.Briefcase),
      designers: () => import('lucide-react').then(mod => mod.Palette),
      clients: () => import('lucide-react').then(mod => mod.UserCheck),
      portfolio: () => import('lucide-react').then(mod => mod.Image),
      messages: () => import('lucide-react').then(mod => mod.MessageSquare),
      finance: () => import('lucide-react').then(mod => mod.DollarSign),
      payments: () => import('lucide-react').then(mod => mod.CreditCard),
      settings: () => import('lucide-react').then(mod => mod.Settings),
      profile: () => import('lucide-react').then(mod => mod.User),
      reviews: () => import('lucide-react').then(mod => mod.Clock),
      standards: () => import('lucide-react').then(mod => mod.Star),
      history: () => import('lucide-react').then(mod => mod.FileText),
      analytics: () => import('lucide-react').then(mod => mod.BarChart3),
      performance: () => import('lucide-react').then(mod => mod.TrendingUp),
      negotiations: () => import('lucide-react').then(mod => mod.MessageSquare),
      escrow: () => import('lucide-react').then(mod => mod.DollarSign),
      satisfaction: () => import('lucide-react').then(mod => mod.Star),
      coordination: () => import('lucide-react').then(mod => mod.Users),
      reports: () => import('lucide-react').then(mod => mod.BarChart3),
      calendar: () => import('lucide-react').then(mod => mod.Calendar),
    };

    return iconMap[id] || (() => import('lucide-react').then(mod => mod.Circle));
  };

  const items = sidebarItems[role].map(item => ({
    ...item,
    icon: getIcon(item.id),
  }));

  return (
    <EnhancedSidebar
      items={items}
      className={className}
      collapsed={collapsed}
    />
  );
}

// Sidebar with loading states
export function SidebarWithLoading({ 
  loading, 
  role, 
  className, 
  collapsed, 
  badges 
}: RoleSidebarProps & { loading: boolean }) {
  if (loading) {
    return (
      <div className={cn("space-y-2", className)}>
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className={cn(
              "h-10 bg-gray-200 rounded-md animate-pulse",
              collapsed ? "w-10" : "w-full"
            )}
          />
        ))}
      </div>
    );
  }

  return (
    <RoleBasedSidebar
      role={role}
      className={className}
      collapsed={collapsed}
      badges={badges}
    />
  );
}
