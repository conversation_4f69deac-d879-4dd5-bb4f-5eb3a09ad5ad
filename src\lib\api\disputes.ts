import { 
  Dispute, 
  DisputeWithDetails, 
  DisputeMessage, 
  DisputeAttachment,
  CreateDisputeParams,
  UpdateDisputeParams,
  CreateDisputeMessageParams,
  CreateDisputeAttachmentParams
} from '@/types/dispute';

/**
 * Get all disputes for the authenticated user
 */
export async function getDisputes(token: string, status?: string, projectId?: string): Promise<Dispute[]> {
  let url = '/api/disputes';
  const params = new URLSearchParams();
  
  if (status) {
    params.append('status', status);
  }
  
  if (projectId) {
    params.append('project_id', projectId);
  }
  
  if (params.toString()) {
    url += `?${params.toString()}`;
  }
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch disputes');
  }
  
  return response.json();
}

/**
 * Get a specific dispute by ID
 */
export async function getDispute(token: string, disputeId: string): Promise<DisputeWithDetails> {
  const response = await fetch(`/api/disputes/${disputeId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch dispute');
  }
  
  return response.json();
}

/**
 * Create a new dispute
 */
export async function createDispute(token: string, params: CreateDisputeParams): Promise<Dispute> {
  const response = await fetch('/api/disputes', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create dispute');
  }
  
  return response.json();
}

/**
 * Update a dispute
 */
export async function updateDispute(token: string, disputeId: string, params: UpdateDisputeParams): Promise<Dispute> {
  const response = await fetch(`/api/disputes/${disputeId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update dispute');
  }
  
  return response.json();
}

/**
 * Get all messages for a dispute
 */
export async function getDisputeMessages(token: string, disputeId: string): Promise<DisputeMessage[]> {
  const response = await fetch(`/api/disputes/${disputeId}/messages`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch dispute messages');
  }
  
  return response.json();
}

/**
 * Create a new message in a dispute
 */
export async function createDisputeMessage(
  token: string, 
  disputeId: string, 
  params: CreateDisputeMessageParams
): Promise<DisputeMessage> {
  const response = await fetch(`/api/disputes/${disputeId}/messages`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create dispute message');
  }
  
  return response.json();
}

/**
 * Get all attachments for a dispute
 */
export async function getDisputeAttachments(token: string, disputeId: string): Promise<DisputeAttachment[]> {
  const response = await fetch(`/api/disputes/${disputeId}/attachments`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch dispute attachments');
  }
  
  return response.json();
}

/**
 * Create a new attachment in a dispute
 */
export async function createDisputeAttachment(
  token: string, 
  disputeId: string, 
  params: CreateDisputeAttachmentParams
): Promise<DisputeAttachment> {
  const response = await fetch(`/api/disputes/${disputeId}/attachments`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create dispute attachment');
  }
  
  return response.json();
}
