"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useConnectedDesigners } from "@/hooks/useDashboardData";
import { motion } from "framer-motion";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useResponsive } from "@/components/mobile/ResponsiveLayout";
import {
  Users,
  Star,
  MessageSquare,
  Eye,
  Send,
  MapPin,
  Briefcase,
  Calendar,
  Award,
  Search,
  Filter,
  User,
  Plus,
  CheckCircle,
  Clock,
  Target
} from "lucide-react";

interface ConnectedDesigner {
  id: string;
  full_name: string;
  avatar_url: string | null;
  specialization: string;
  location: string;
  bio: string;
  rating: number;
  completed_projects: number;
  years_experience: number;
  availability_status: 'available' | 'busy' | 'offline';
  hourly_rate: number;
  skills: string[];
  portfolio_count: number;
  connection_date: string;
  last_project_date: string | null;
}

export default function ClientDesigners() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [availabilityFilter, setAvailabilityFilter] = useState<string>('all');
  const [specializationFilter, setSpecializationFilter] = useState<string>('all');
  const { isMobile, isTablet } = useResponsive();

  // Use optimized data fetching with correct parameters
  const { data: designers = [], isLoading, error } = useConnectedDesigners(user?.id || '', 'client');

  // Transform designers data for display
  const formattedDesigners: ConnectedDesigner[] = designers.map((designer) => ({
    id: designer.id,
    full_name: designer.full_name,
    avatar_url: designer.avatar_url,
    specialization: designer.specialization || 'Interior Design',
    location: designer.location || 'Location not specified',
    bio: designer.bio || 'Experienced interior designer with a passion for creating beautiful, functional spaces.',
    rating: designer.rating || 4.8,
    completed_projects: designer.completed_projects || 0,
    years_experience: designer.years_experience || 3,
    availability_status: designer.availability_status || 'available',
    hourly_rate: designer.hourly_rate || 100,
    skills: designer.skills || ['Interior Design', 'Space Planning'],
    portfolio_count: designer.portfolio_count || 0,
    connection_date: designer.connection_date || new Date().toISOString(),
    last_project_date: designer.last_project_date || null
  }));

  const getAvailabilityColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'busy':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'offline':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-sm text-gray-600 ml-2">({rating.toFixed(1)})</span>
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const filteredDesigners = formattedDesigners.filter(designer => {
    const matchesSearch = designer.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         designer.specialization.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         designer.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesAvailability = availabilityFilter === 'all' || designer.availability_status === availabilityFilter;
    const matchesSpecialization = specializationFilter === 'all' || designer.specialization.toLowerCase().includes(specializationFilter.toLowerCase());

    return matchesSearch && matchesAvailability && matchesSpecialization;
  });

  const uniqueSpecializations = Array.from(new Set(formattedDesigners.map(d => d.specialization)));

  // Show loading only for initial load or auth loading
  const loading = authLoading || (isLoading && formattedDesigners.length === 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6">
      {/* Header - Mobile Responsive */}
      <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'}`}>
        <div>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            My Designers
          </h1>
          <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}>
            Manage your connected designers and collaborate on projects
          </p>
        </div>
        <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'items-center space-x-3'}`}>
          <Link href="/client/briefs/new">
            <Button
              className={`bg-brown-600 hover:bg-brown-700 text-white ${
                isMobile ? 'w-full justify-center' : ''
              }`}
              size={isMobile ? "sm" : "default"}
            >
              <Send className="h-4 w-4 mr-2" />
              Send Brief
            </Button>
          </Link>
          <Link href="/client/designers/browse">
            <Button
              variant="outline"
              className={`border-brown-600 text-brown-600 hover:bg-brown-50 ${
                isMobile ? 'w-full justify-center' : ''
              }`}
              size={isMobile ? "sm" : "default"}
            >
              <Plus className="h-4 w-4 mr-2" />
              Find Designers
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards - Mobile Responsive */}
      <div className={`grid gap-4 ${
        isMobile
          ? 'grid-cols-2'
          : isTablet
            ? 'grid-cols-2'
            : 'grid-cols-4'
      }`}>
        <div className={`bg-white rounded-lg border border-gray-200 ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Connected Designers
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {formattedDesigners.length}
              </p>
            </div>
            <Users className={`text-blue-600 ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
          </div>
        </div>
        <div className={`bg-white rounded-lg border border-gray-200 ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Available Now
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {formattedDesigners.filter(d => d.availability_status === 'available').length}
              </p>
            </div>
            <CheckCircle className={`text-green-600 ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
          </div>
        </div>
        <div className={`bg-white rounded-lg border border-gray-200 ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Active Projects
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {formattedDesigners.filter(d => d.last_project_date && new Date(d.last_project_date) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length}
              </p>
            </div>
            <Target className={`text-purple-600 ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
          </div>
        </div>
        <div className={`bg-white rounded-lg border border-gray-200 ${isMobile ? 'p-4' : 'p-6'}`}>
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Avg Rating
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {formattedDesigners.length > 0 ? (formattedDesigners.reduce((sum, d) => sum + d.rating, 0) / formattedDesigners.length).toFixed(1) : '0.0'}
              </p>
            </div>
            <Star className={`text-yellow-600 ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search designers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
              />
            </div>
          </div>

          <select
            value={availabilityFilter}
            onChange={(e) => setAvailabilityFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Availability</option>
            <option value="available">Available</option>
            <option value="busy">Busy</option>
            <option value="offline">Offline</option>
          </select>

          <select
            value={specializationFilter}
            onChange={(e) => setSpecializationFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brown-500 focus:border-transparent"
          >
            <option value="all">All Specializations</option>
            {uniqueSpecializations.map(spec => (
              <option key={spec} value={spec}>{spec}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Designers List */}
      {filteredDesigners.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {formattedDesigners.length === 0 ? 'No connected designers yet' : 'No designers match your filters'}
          </h3>
          <p className="text-gray-500 mb-4">
            {formattedDesigners.length === 0
              ? 'Connect with designers to start collaborating on projects'
              : 'Try adjusting your search terms or filters'
            }
          </p>
          {formattedDesigners.length === 0 && (
            <Link href="/client/designers/browse">
              <Button className="bg-brown-600 hover:bg-brown-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Find Designers
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <div className={`grid gap-4 lg:gap-6 ${
          isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'
        }`}>
          {filteredDesigners.map((designer) => (
            <motion.div
              key={designer.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={`bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow ${
                isMobile ? 'p-4' : 'p-6'
              }`}
            >
              {/* Designer Header */}
              <div className={`flex items-start mb-4 ${isMobile ? 'space-x-3' : 'space-x-4'}`}>
                <div className="flex-shrink-0">
                  {designer.avatar_url ? (
                    <img
                      src={designer.avatar_url}
                      alt={designer.full_name}
                      className={`rounded-full object-cover ${
                        isMobile ? 'h-12 w-12' : 'h-16 w-16'
                      }`}
                    />
                  ) : (
                    <div className={`rounded-full bg-gray-200 flex items-center justify-center ${
                      isMobile ? 'h-12 w-12' : 'h-16 w-16'
                    }`}>
                      <User className={`text-gray-500 ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`} />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'items-center justify-between'} mb-1`}>
                    <h3 className={`font-semibold text-gray-900 truncate ${
                      isMobile ? 'text-base' : 'text-lg'
                    }`}>
                      {designer.full_name}
                    </h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getAvailabilityColor(designer.availability_status)} ${
                      isMobile ? 'self-start' : ''
                    }`}>
                      {designer.availability_status}
                    </span>
                  </div>
                  <p className={`text-gray-600 mb-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                    {designer.specialization}
                  </p>
                  {renderStars(designer.rating)}
                </div>
              </div>

              {/* Designer Info */}
              <div className={`mb-4 ${isMobile ? 'space-y-2' : 'space-y-3'}`}>
                <p className={`text-gray-600 line-clamp-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {designer.bio}
                </p>

                <div className={`grid gap-${isMobile ? '2' : '4'} text-gray-500 ${
                  isMobile ? 'grid-cols-1 text-xs' : 'grid-cols-2 text-sm'
                }`}>
                  <div className="flex items-center">
                    <MapPin className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                    <span className="truncate">{designer.location}</span>
                  </div>
                  <div className="flex items-center">
                    <Award className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                    <span>{designer.years_experience} years exp.</span>
                  </div>
                  <div className="flex items-center">
                    <Target className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                    <span>{designer.completed_projects} projects</span>
                  </div>
                  <div className="flex items-center">
                    <Briefcase className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                    <span>${designer.hourly_rate}/hour</span>
                  </div>
                </div>

                {/* Skills */}
                <div>
                  <p className={`font-medium text-gray-700 mb-1 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                    Skills:
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {designer.skills.slice(0, isMobile ? 2 : 3).map((skill, index) => (
                      <span
                        key={index}
                        className={`bg-gray-100 text-gray-600 rounded ${
                          isMobile ? 'px-1.5 py-0.5 text-xs' : 'px-2 py-1 text-xs'
                        }`}
                      >
                        {skill}
                      </span>
                    ))}
                    {designer.skills.length > (isMobile ? 2 : 3) && (
                      <span className={`bg-gray-100 text-gray-600 rounded ${
                        isMobile ? 'px-1.5 py-0.5 text-xs' : 'px-2 py-1 text-xs'
                      }`}>
                        +{designer.skills.length - (isMobile ? 2 : 3)} more
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Connection Info */}
              <div className={`text-gray-500 mb-4 bg-gray-50 rounded ${
                isMobile ? 'text-xs p-2' : 'text-xs p-2'
              }`}>
                <div className={isMobile ? 'space-y-1' : ''}>
                  <span>Connected since {formatDate(designer.connection_date)}</span>
                  {designer.last_project_date && (
                    <span className={isMobile ? 'block' : 'ml-2'}>
                      {isMobile ? '' : '• '}Last project: {formatDate(designer.last_project_date)}
                    </span>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className={`flex ${
                isMobile ? 'flex-col space-y-2' : 'items-center justify-between'
              }`}>
                <Link href={`/client/designers/${designer.id}`}>
                  <Button
                    variant="outline"
                    size="sm"
                    className={isMobile ? 'w-full justify-center' : ''}
                  >
                    <Eye className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                    View Profile
                  </Button>
                </Link>
                <div className={`flex ${
                  isMobile ? 'space-x-2' : 'items-center space-x-2'
                }`}>
                  <Link href={`/client/messages?otherUserId=${designer.id}&type=direct`}>
                    <Button
                      variant="outline"
                      size="sm"
                      className={isMobile ? 'flex-1' : ''}
                    >
                      <MessageSquare className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                      {isMobile ? 'Message' : 'Message'}
                    </Button>
                  </Link>
                  <Link href={`/client/briefs/new?designerId=${designer.id}`}>
                    <Button
                      size="sm"
                      className={`bg-brown-600 hover:bg-brown-700 text-white ${
                        isMobile ? 'flex-1' : ''
                      }`}
                    >
                      <Send className={`mr-2 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                      {isMobile ? 'Brief' : 'Send Brief'}
                    </Button>
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
