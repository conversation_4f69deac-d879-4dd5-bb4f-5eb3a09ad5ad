"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  User,
  FileText,
  DollarSign,
  Calendar,
  AlertCircle
} from "lucide-react";

interface Proposal {
  id: string;
  title: string;
  description: string;
  total_budget: number;
  timeline: string;
  status: string;
  created_at: string;
  project_id: string;
  designer_id: string;
  project: {
    title: string;
    client_id: string;
    profiles: {
      full_name: string;
    };
  };
  profiles: {
    full_name: string;
    avatar_url?: string;
  };
}

export default function AdminProposalsPage() {
  const { user, profile } = useOptimizedAuth();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [processingId, setProcessingId] = useState<string | null>(null);

  useEffect(() => {
    if (user && profile?.role === 'admin') {
      fetchProposals();
    }
  }, [user, profile, filter]);

  const fetchProposals = async () => {
    try {
      let query = supabase
        .from('project_proposals')
        .select(`
          id,
          title,
          description,
          total_budget,
          timeline,
          status,
          created_at,
          project_id,
          designer_id,
          projects:project_id (
            title,
            client_id,
            profiles:client_id (
              full_name
            )
          ),
          profiles:designer_id (
            full_name,
            avatar_url
          )
        `)
        .order('created_at', { ascending: false });

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query;

      if (error) throw error;

      setProposals(data || []);
    } catch (error) {
      console.error('Error fetching proposals:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProposalAction = async (proposalId: string, action: 'approve' | 'reject', feedback?: string) => {
    setProcessingId(proposalId);
    
    try {
      const proposal = proposals.find(p => p.id === proposalId);
      if (!proposal) return;

      // Update proposal status
      const { error: updateError } = await supabase
        .from('project_proposals')
        .update({
          status: action === 'approve' ? 'approved' : 'rejected',
          reviewed_at: new Date().toISOString(),
          reviewed_by: user?.id,
          admin_feedback: feedback || null
        })
        .eq('id', proposalId);

      if (updateError) throw updateError;

      if (action === 'approve') {
        // Create notification for client
        const { error: clientNotificationError } = await supabase
          .from('notifications')
          .insert({
            user_id: proposal.project.profiles.client_id,
            type: 'proposal',
            title: 'Proposal Approved',
            content: `Your project proposal for "${proposal.project.title}" has been approved and is ready for review`,
            related_id: proposalId,
            read: false
          });

        if (clientNotificationError) console.error('Error creating client notification:', clientNotificationError);
      }

      // Create notification for designer
      const { error: designerNotificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: proposal.designer_id,
          type: 'proposal',
          title: `Proposal ${action === 'approve' ? 'Approved' : 'Rejected'}`,
          content: `Your proposal for "${proposal.project.title}" has been ${action}d by admin`,
          related_id: proposalId,
          read: false
        });

      if (designerNotificationError) console.error('Error creating designer notification:', designerNotificationError);

      // Refresh proposals
      fetchProposals();
    } catch (error) {
      console.error(`Error ${action}ing proposal:`, error);
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Proposal Review</h1>
        <p className="text-gray-600">Review and approve designer proposals</p>
      </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'pending', label: 'Pending Review', count: proposals.filter(p => p.status === 'pending').length },
              { key: 'approved', label: 'Approved', count: proposals.filter(p => p.status === 'approved').length },
              { key: 'rejected', label: 'Rejected', count: proposals.filter(p => p.status === 'rejected').length },
              { key: 'all', label: 'All', count: proposals.length }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.key
                    ? 'border-brown-500 text-brown-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Proposals List */}
      <div className="space-y-6">
        {proposals.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No proposals found</h3>
            <p className="text-gray-500">
              {filter === 'pending' ? 'No proposals are currently pending review.' : `No ${filter} proposals found.`}
            </p>
          </div>
        ) : (
          proposals.map((proposal) => (
            <motion.div
              key={proposal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-md border border-gray-200 p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{proposal.title}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(proposal.status)}`}>
                      {getStatusIcon(proposal.status)}
                      <span className="ml-1 capitalize">{proposal.status}</span>
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">Project: {proposal.project.title}</p>
                  <p className="text-gray-700 mb-4">{proposal.description}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <User className="h-4 w-4 mr-2" />
                  <span>Designer: {proposal.profiles.full_name}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <DollarSign className="h-4 w-4 mr-2" />
                  <span>Budget: ${proposal.total_budget?.toLocaleString()}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>Timeline: {proposal.timeline}</span>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  Submitted {new Date(proposal.created_at).toLocaleDateString()}
                </div>
                <div className="flex space-x-3">
                  <Link
                    href={`/admin/proposals/${proposal.id}`}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Link>
                  {proposal.status === 'pending' && (
                    <>
                      <button
                        onClick={() => handleProposalAction(proposal.id, 'approve')}
                        disabled={processingId === proposal.id}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve
                      </button>
                      <button
                        onClick={() => handleProposalAction(proposal.id, 'reject')}
                        disabled={processingId === proposal.id}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </button>
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>
    </div>
  );
}
