import { supabase } from './supabase';

export interface FeeSettings {
  id?: string;
  platform_commission_rate: number;
  payment_processing_fee: number;
  designer_payout_rate: number;
  minimum_project_value: number;
  maximum_commission_cap?: number;
  minimum_payout_amount: number;
  updated_at?: string;
  updated_by?: string;
}

export interface PaymentCalculation {
  gross_amount: number;
  platform_fee: number;
  processing_fee: number;
  designer_payout: number;
  platform_revenue: number;
}

/**
 * Unified Fee Settings Manager
 * Single source of truth for all payment calculations
 */
export class FeeSettingsManager {
  private static instance: FeeSettingsManager;
  private cachedSettings: FeeSettings | null = null;
  private lastFetch: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static getInstance(): FeeSettingsManager {
    if (!FeeSettingsManager.instance) {
      FeeSettingsManager.instance = new FeeSettingsManager();
    }
    return FeeSettingsManager.instance;
  }

  /**
   * Get current fee settings with caching
   */
  async getFeeSettings(): Promise<FeeSettings> {
    const now = Date.now();
    
    // Return cached settings if still valid
    if (this.cachedSettings && (now - this.lastFetch) < this.CACHE_DURATION) {
      return this.cachedSettings;
    }

    try {
      // Try to get from platform_fee_settings first (most recent table)
      const { data: platformSettings, error: platformError } = await supabase
        .from('platform_fee_settings')
        .select('*')
        .single();

      if (platformSettings && !platformError) {
        this.cachedSettings = {
          id: platformSettings.id,
          platform_commission_rate: platformSettings.platform_commission_rate,
          payment_processing_fee: platformSettings.payment_processing_fee,
          designer_payout_rate: platformSettings.designer_payout_rate,
          minimum_project_value: platformSettings.minimum_project_value,
          maximum_commission_cap: platformSettings.maximum_commission_cap,
          minimum_payout_amount: platformSettings.minimum_payout_amount,
          updated_at: platformSettings.updated_at,
          updated_by: platformSettings.updated_by
        };
        this.lastFetch = now;
        return this.cachedSettings;
      }

      // Fallback to old fee_settings table (for backward compatibility)
      const { data: feeSettings, error: feeError } = await supabase
        .from('fee_settings')
        .select('*')
        .single();

      if (feeSettings && !feeError) {
        this.cachedSettings = {
          id: feeSettings.id,
          platform_commission_rate: feeSettings.platform_commission_rate,
          payment_processing_fee: feeSettings.payment_processing_fee,
          designer_payout_rate: feeSettings.designer_payout_rate,
          minimum_project_value: feeSettings.minimum_project_value,
          maximum_commission_cap: feeSettings.maximum_commission_cap,
          minimum_payout_amount: 50, // Default if not in old table
          updated_at: feeSettings.updated_at,
          updated_by: feeSettings.updated_by
        };
        this.lastFetch = now;
        return this.cachedSettings;
      }

      // Return default settings if no database settings found
      return this.getDefaultSettings();
    } catch (error) {
      console.error('Error fetching fee settings:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * Get default fee settings (FIXED MATH)
   */
  private getDefaultSettings(): FeeSettings {
    return {
      platform_commission_rate: 15.0,
      payment_processing_fee: 2.9,
      designer_payout_rate: 82.1, // FIXED: 100 - 15 - 2.9 = 82.1
      minimum_project_value: 100.0,
      maximum_commission_cap: null,
      minimum_payout_amount: 50.0
    };
  }

  /**
   * Update fee settings and clear cache
   */
  async updateFeeSettings(settings: Partial<FeeSettings>, updatedBy: string): Promise<FeeSettings> {
    try {
      // Ensure math is correct
      if (settings.platform_commission_rate !== undefined && settings.payment_processing_fee !== undefined) {
        settings.designer_payout_rate = 100 - settings.platform_commission_rate - settings.payment_processing_fee;
      }

      const updateData = {
        ...settings,
        updated_at: new Date().toISOString(),
        updated_by: updatedBy
      };

      // Update platform_fee_settings table (primary)
      const { data, error } = await supabase
        .from('platform_fee_settings')
        .upsert(updateData)
        .select()
        .single();

      if (error) throw error;

      // Clear cache to force refresh
      this.cachedSettings = null;
      this.lastFetch = 0;

      return await this.getFeeSettings();
    } catch (error) {
      console.error('Error updating fee settings:', error);
      throw error;
    }
  }

  /**
   * Calculate payment breakdown using current fee settings
   */
  async calculatePaymentBreakdown(grossAmount: number): Promise<PaymentCalculation> {
    const settings = await this.getFeeSettings();
    
    const platformFee = (grossAmount * settings.platform_commission_rate) / 100;
    const processingFee = (grossAmount * settings.payment_processing_fee) / 100;
    const designerPayout = grossAmount - platformFee - processingFee;
    
    return {
      gross_amount: grossAmount,
      platform_fee: platformFee,
      processing_fee: processingFee,
      designer_payout: designerPayout,
      platform_revenue: platformFee // Platform keeps commission, processing fee goes to payment processor
    };
  }

  /**
   * Validate payment calculation (ensure it adds up to 100%)
   */
  async validateCalculation(grossAmount: number): Promise<{ valid: boolean; total: number; breakdown: PaymentCalculation }> {
    const breakdown = await this.calculatePaymentBreakdown(grossAmount);
    const total = breakdown.platform_fee + breakdown.processing_fee + breakdown.designer_payout;
    const valid = Math.abs(total - grossAmount) < 0.01; // Allow for rounding errors
    
    return {
      valid,
      total,
      breakdown
    };
  }

  /**
   * Clear cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cachedSettings = null;
    this.lastFetch = 0;
  }
}

// Export singleton instance
export const feeSettingsManager = FeeSettingsManager.getInstance();

// Convenience functions
export const getFeeSettings = () => feeSettingsManager.getFeeSettings();
export const calculatePaymentBreakdown = (amount: number) => feeSettingsManager.calculatePaymentBreakdown(amount);
export const updateFeeSettings = (settings: Partial<FeeSettings>, updatedBy: string) => 
  feeSettingsManager.updateFeeSettings(settings, updatedBy);
