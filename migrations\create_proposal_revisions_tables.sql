-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add revision fields to project_proposals table
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS parent_id UUID REFERENCES project_proposals(id) ON DELETE SET NULL;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS revision_notes TEXT;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS revision_requested_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS revision_requested_by UUID REFERENCES profiles(id) ON DELETE SET NULL;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES profiles(id) ON DELETE SET NULL;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS rejected_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE project_proposals ADD COLUMN IF NOT EXISTS rejected_by UUID REFERENCES profiles(id) ON DELETE SET NULL;

-- Create proposal_revisions table to track changes
CREATE TABLE IF NOT EXISTS proposal_revisions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  proposal_id UUID NOT NULL REFERENCES project_proposals(id) ON DELETE CASCADE,
  field_name TEXT NOT NULL, -- 'title', 'description', 'scope', 'timeline', 'total_budget', etc.
  old_value TEXT,
  new_value TEXT,
  changed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create proposal_comments table for negotiation
CREATE TABLE IF NOT EXISTS proposal_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  proposal_id UUID NOT NULL REFERENCES project_proposals(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT FALSE, -- For comments visible only to the designer or admin
  parent_id UUID REFERENCES proposal_comments(id) ON DELETE SET NULL, -- For threaded comments
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create proposal_change_requests table
CREATE TABLE IF NOT EXISTS proposal_change_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  proposal_id UUID NOT NULL REFERENCES project_proposals(id) ON DELETE CASCADE,
  requested_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'implemented')),
  section TEXT NOT NULL, -- 'scope', 'timeline', 'budget', 'milestones', etc.
  description TEXT NOT NULL,
  response TEXT,
  responded_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  responded_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for proposal_revisions table
ALTER TABLE proposal_revisions ENABLE ROW LEVEL SECURITY;

-- Admins can see all revisions
CREATE POLICY "Admins can see all proposal revisions"
  ON proposal_revisions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can see revisions for proposals they're involved in
CREATE POLICY "Users can see revisions for their proposals"
  ON proposal_revisions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM project_proposals
      JOIN projects ON projects.id = project_proposals.project_id
      WHERE project_proposals.id = proposal_revisions.proposal_id
      AND (
        projects.client_id = auth.uid() OR 
        project_proposals.designer_id = auth.uid()
      )
    )
  );

-- Add RLS policies for proposal_comments table
ALTER TABLE proposal_comments ENABLE ROW LEVEL SECURITY;

-- Admins can see all comments
CREATE POLICY "Admins can see all proposal comments"
  ON proposal_comments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can see comments for proposals they're involved in (except internal comments)
CREATE POLICY "Users can see comments for their proposals"
  ON proposal_comments
  FOR SELECT
  USING (
    (is_internal = FALSE OR user_id = auth.uid()) AND
    EXISTS (
      SELECT 1 FROM project_proposals
      JOIN projects ON projects.id = project_proposals.project_id
      WHERE project_proposals.id = proposal_comments.proposal_id
      AND (
        projects.client_id = auth.uid() OR 
        project_proposals.designer_id = auth.uid()
      )
    )
  );

-- Users can create comments for proposals they're involved in
CREATE POLICY "Users can create comments for their proposals"
  ON proposal_comments
  FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM project_proposals
      JOIN projects ON projects.id = project_proposals.project_id
      WHERE project_proposals.id = proposal_comments.proposal_id
      AND (
        projects.client_id = auth.uid() OR 
        project_proposals.designer_id = auth.uid() OR
        EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role = 'admin')
      )
    )
  );

-- Add RLS policies for proposal_change_requests table
ALTER TABLE proposal_change_requests ENABLE ROW LEVEL SECURITY;

-- Admins can see all change requests
CREATE POLICY "Admins can see all proposal change requests"
  ON proposal_change_requests
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Users can see change requests for proposals they're involved in
CREATE POLICY "Users can see change requests for their proposals"
  ON proposal_change_requests
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM project_proposals
      JOIN projects ON projects.id = project_proposals.project_id
      WHERE project_proposals.id = proposal_change_requests.proposal_id
      AND (
        projects.client_id = auth.uid() OR 
        project_proposals.designer_id = auth.uid()
      )
    )
  );

-- Users can create change requests for proposals they're involved in
CREATE POLICY "Users can create change requests for their proposals"
  ON proposal_change_requests
  FOR INSERT
  WITH CHECK (
    requested_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM project_proposals
      JOIN projects ON projects.id = project_proposals.project_id
      WHERE project_proposals.id = proposal_change_requests.proposal_id
      AND (
        projects.client_id = auth.uid() OR
        EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.role = 'admin')
      )
    )
  );

-- Designers can respond to change requests
CREATE POLICY "Designers can respond to change requests"
  ON proposal_change_requests
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM project_proposals
      WHERE project_proposals.id = proposal_change_requests.proposal_id
      AND project_proposals.designer_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM project_proposals
      WHERE project_proposals.id = proposal_change_requests.proposal_id
      AND project_proposals.designer_id = auth.uid()
    )
  );
