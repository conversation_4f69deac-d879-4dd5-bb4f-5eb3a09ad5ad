"use client";

import { useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "../ui/button";
import Link from "next/link";
import { ArrowRight, Calendar, Mail, Phone, MapPin, Plus, X } from "lucide-react";

const CombinedContactSection = () => {
  const sectionRef = useRef(null);
  const [showContactForm, setShowContactForm] = useState(false);
  const [showWorkingHours, setShowWorkingHours] = useState(false);
  const [mobileContactOpen, setMobileContactOpen] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    message: ''
  });

  const contactInfo = [
    {
      icon: <Mail className="h-5 w-5 text-primary" />,
      label: "Email",
      value: "<EMAIL>",
      action: () => setShowContactForm(!showContactForm)
    },
    {
      icon: <Phone className="h-5 w-5 text-primary" />,
      label: "Phone",
      value: "+966 55 255 2260",
      action: () => window.open("tel:+966552552260", "_self")
    },
    {
      icon: <MapPin className="h-5 w-5 text-primary" />,
      label: "Office",
      value: "Dubai Design District, Building 7",
      action: () => window.open("https://maps.google.com/?q=Dubai+Design+District", "_blank")
    },
    {
      icon: <Calendar className="h-5 w-5 text-primary" />,
      label: "Hours",
      value: "Mon-Fri: 9am - 6pm",
      action: () => setShowWorkingHours(!showWorkingHours)
    }
  ];

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log('Form submitted:', formData);
    // Reset form and close
    setFormData({ fullName: '', email: '', message: '' });
    setShowContactForm(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section
      ref={sectionRef}
      className="py-16 md:py-24 bg-gray-50 relative"
    >
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          {/* Section header with consistent styling */}
          <div className="max-w-3xl mx-auto">
            <motion.div
              className="mb-16"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center mb-6 justify-center">
                <div className="h-[1px] w-12 bg-primary mr-4"></div>
                <span className="text-primary uppercase tracking-widest text-sm font-medium">Get in touch</span>
              </div>

              <motion.h2
                className="text-4xl md:text-5xl font-bold tracking-tight leading-tight text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                Let's Work Together
              </motion.h2>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            {/* Left Column - Get Started */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white p-6 md:p-8 shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col"
            >
              <h3 className="text-xl md:text-2xl font-bold mb-6 pb-4 border-b border-gray-100">Get Started</h3>

              <p className="text-gray-600 text-sm md:text-base mb-8">We're ready to support you with tailored design solutions. Whether you're exploring an idea or starting full development, our team is here to assist.</p>

              <div className="mt-auto flex flex-col items-center justify-center py-8">
                <Link href="/sample-request" className="block w-full">
                  <Button variant="default" size="lg" className="w-full py-4 md:py-5 text-base md:text-lg flex items-center justify-center group">
                    Start Your Project
                    <motion.div
                      className="ml-2"
                      initial={{ x: 0 }}
                      whileHover={{ x: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <ArrowRight className="h-5 w-5" />
                    </motion.div>
                  </Button>
                </Link>
              </div>
            </motion.div>

            {/* Right Column - Smart Contact Options */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white p-6 md:p-8 shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col"
            >
              <h3 className="text-xl md:text-2xl font-bold mb-6 pb-4 border-b border-gray-100">Contact Us</h3>

              {/* Desktop Contact Options */}
              <div className="hidden md:block space-y-4">
                {contactInfo.map((item, index) => (
                  <div key={index}>
                    <motion.button
                      onClick={item.action}
                      className="w-full flex items-center p-3 hover:bg-gray-50 transition-colors duration-300 group cursor-pointer"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="w-10 h-10 bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0 group-hover:bg-primary/20 transition-colors duration-300">
                        {item.icon}
                      </div>
                      <div className="text-left">
                        <p className="text-sm font-medium text-gray-800 group-hover:text-primary transition-colors duration-300">
                          {item.label}
                        </p>
                      </div>
                    </motion.button>

                    {/* Working Hours Dropdown */}
                    <AnimatePresence>
                      {item.label === "Hours" && showWorkingHours && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="ml-14 mt-2 p-3 bg-gray-50 text-sm text-gray-600"
                        >
                          {item.value}
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Contact Form Dropdown */}
                    <AnimatePresence>
                      {item.label === "Email" && showContactForm && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="ml-14 mt-2 p-4 bg-gray-50"
                        >
                          <form onSubmit={handleFormSubmit} className="space-y-3">
                            <input
                              type="text"
                              name="fullName"
                              placeholder="Full Name"
                              value={formData.fullName}
                              onChange={handleInputChange}
                              className="w-full p-2 border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                              required
                            />
                            <input
                              type="email"
                              name="email"
                              placeholder="Email"
                              value={formData.email}
                              onChange={handleInputChange}
                              className="w-full p-2 border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                              required
                            />
                            <textarea
                              name="message"
                              placeholder="Message"
                              value={formData.message}
                              onChange={handleInputChange}
                              rows={3}
                              className="w-full p-2 border border-gray-300 text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none"
                              required
                            />
                            <Button type="submit" className="w-full text-sm py-2">
                              Submit
                            </Button>
                          </form>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>

              {/* Mobile Contact Toggle */}
              <div className="md:hidden">
                <motion.button
                  onClick={() => setMobileContactOpen(!mobileContactOpen)}
                  className="w-full flex items-center justify-center p-4 bg-primary/10 hover:bg-primary/20 transition-colors duration-300 group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.div
                    animate={{ rotate: mobileContactOpen ? 45 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="mr-3"
                  >
                    <Plus className="h-5 w-5 text-primary" />
                  </motion.div>
                  <span className="text-primary font-medium">Contact</span>
                </motion.button>

                {/* Mobile Contact Options */}
                <AnimatePresence>
                  {mobileContactOpen && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-4 space-y-2 overflow-hidden"
                    >
                      {contactInfo.map((item, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <button
                            onClick={item.action}
                            className="w-full flex items-center p-3 hover:bg-gray-50 transition-colors duration-300 group"
                          >
                            <div className="w-8 h-8 bg-primary/10 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-primary/20 transition-colors duration-300">
                              {item.icon}
                            </div>
                            <span className="text-sm font-medium text-gray-800 group-hover:text-primary transition-colors duration-300">
                              {item.label}
                            </span>
                          </button>

                          {/* Mobile Working Hours */}
                          <AnimatePresence>
                            {item.label === "Hours" && showWorkingHours && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                                className="ml-11 mt-1 p-2 bg-gray-50 text-xs text-gray-600"
                              >
                                {item.value}
                              </motion.div>
                            )}
                          </AnimatePresence>

                          {/* Mobile Contact Form */}
                          <AnimatePresence>
                            {item.label === "Email" && showContactForm && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.3 }}
                                className="ml-11 mt-1 p-3 bg-gray-50"
                              >
                                <form onSubmit={handleFormSubmit} className="space-y-2">
                                  <input
                                    type="text"
                                    name="fullName"
                                    placeholder="Full Name"
                                    value={formData.fullName}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border border-gray-300 text-xs focus:outline-none focus:ring-2 focus:ring-primary/20"
                                    required
                                  />
                                  <input
                                    type="email"
                                    name="email"
                                    placeholder="Email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className="w-full p-2 border border-gray-300 text-xs focus:outline-none focus:ring-2 focus:ring-primary/20"
                                    required
                                  />
                                  <textarea
                                    name="message"
                                    placeholder="Message"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    rows={2}
                                    className="w-full p-2 border border-gray-300 text-xs focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none"
                                    required
                                  />
                                  <Button type="submit" className="w-full text-xs py-1">
                                    Submit
                                  </Button>
                                </form>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CombinedContactSection;
