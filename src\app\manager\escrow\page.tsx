"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useManagerSettings } from "@/hooks/useRealtimeSettings";
import { supabase } from "@/lib/supabase";
import { paypalEscrowManager } from "@/lib/paypal-escrow";
import { Button } from "@/components/ui/button";
import {
  DollarSign,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Eye,
  Filter,
  Search,
  RefreshCw,
  Calendar,
  FileText,
  User
} from "lucide-react";

interface EscrowRelease {
  id: string;
  project_id: string;
  milestone_id: string;
  amount: number;
  release_type: string;
  status: string;
  manager_approval_at: string | null;
  admin_approval_at: string | null;
  released_at: string | null;
  manager_notes: string | null;
  admin_notes: string | null;
  created_at: string;
  project: {
    title: string;
    client: {
      full_name: string;
    };
    designer: {
      full_name: string;
    };
  };
  milestone: {
    title: string;
    description: string;
    percentage: number;
  };
}

export default function ManagerEscrowPage() {
  const { user, profile } = useOptimizedAuth();
  const settings = useManagerSettings();
  const [escrowReleases, setEscrowReleases] = useState<EscrowRelease[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('pending');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (user && profile?.role === 'manager') {
      fetchEscrowReleases();
    }
  }, [user, profile, filter]);

  const fetchEscrowReleases = async () => {
    try {
      // MANAGERS SEE ALL ESCROW RELEASES - Watchdog role
      let query = supabase
        .from('paypal_escrow_releases')
        .select(`
          *,
          escrow_hold:paypal_escrow_holds(
            *,
            project:projects(title, status),
            client:profiles!paypal_escrow_holds_client_id_fkey(full_name),
            designer:profiles!paypal_escrow_holds_designer_id_fkey(full_name)
          ),
          requested_by_profile:profiles!paypal_escrow_releases_requested_by_fkey(full_name)
        `);

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false });

      if (error) throw error;
      setEscrowReleases(data || []);
    } catch (error) {
      console.error('Error fetching escrow releases:', error);
    } finally {
      setLoading(false);
    }
  };

  const approveEscrowRelease = async (releaseId: string, notes?: string) => {
    try {
      const { error } = await supabase
        .from('escrow_releases')
        .update({
          status: 'approved',
          manager_approval_at: new Date().toISOString(),
          manager_notes: notes
        })
        .eq('id', releaseId);

      if (error) throw error;

      // Log manager activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: escrowReleases.find(r => r.id === releaseId)?.project_id,
        activity_type: 'escrow_approval',
        description: 'Approved escrow release for milestone completion',
        outcome: 'approved'
      });

      fetchEscrowReleases();
    } catch (error) {
      console.error('Error approving escrow release:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'released':
        return <DollarSign className="h-4 w-4 text-blue-500" />;
      case 'disputed':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full";
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800 border border-amber-200`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case 'released':
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case 'disputed':
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case 'cancelled':
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  };

  const getReleaseTypeBadge = (type: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (type) {
      case 'milestone':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'partial':
        return `${baseClasses} bg-orange-100 text-orange-800`;
      case 'final':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'dispute':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const filteredReleases = escrowReleases.filter(release =>
    release.project?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    release.project?.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    release.milestone?.title?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Escrow Management</h1>
          <p className="text-gray-600 mt-2">Manage milestone-based escrow releases</p>
        </div>
        <Button
          onClick={fetchEscrowReleases}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Approval</p>
              <p className="text-2xl font-bold text-amber-600">
                {escrowReleases.filter(r => r.status === 'pending').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-amber-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">
                {escrowReleases.filter(r => r.status === 'approved').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Released</p>
              <p className="text-2xl font-bold text-blue-600">
                {escrowReleases.filter(r => r.status === 'released').length}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Amount</p>
              <p className="text-2xl font-bold text-purple-600">
                ${escrowReleases.reduce((sum, r) => sum + r.amount, 0).toLocaleString()}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            >
              <option value="pending">Pending Approval</option>
              <option value="approved">Approved</option>
              <option value="released">Released</option>
              <option value="disputed">Disputed</option>
              <option value="cancelled">Cancelled</option>
              <option value="all">All Releases</option>
            </select>
          </div>

          <div className="flex items-center gap-2 flex-1">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects or milestones..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
            />
          </div>
        </div>
      </div>

      {/* Escrow Releases List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Escrow Releases</h2>
          <p className="text-gray-600 mt-1">Review and approve milestone-based payments</p>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredReleases.length === 0 ? (
            <div className="p-8 text-center">
              <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No escrow releases found</p>
            </div>
          ) : (
            filteredReleases.map((release) => (
              <div key={release.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      {getStatusIcon(release.status)}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {release.milestone?.title || 'Milestone Payment'}
                      </h3>
                      <span className={getStatusBadge(release.status)}>
                        {release.status.toUpperCase()}
                      </span>
                      <span className={getReleaseTypeBadge(release.release_type)}>
                        {release.release_type.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                      <div>
                        <span className="font-medium">Project:</span> {release.project?.title}
                      </div>
                      <div>
                        <span className="font-medium">Client:</span> {release.project?.client?.full_name}
                      </div>
                      <div>
                        <span className="font-medium">Designer:</span> {release.project?.designer?.full_name}
                      </div>
                    </div>

                    <div className="flex items-center gap-6 text-sm mb-3">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-green-500" />
                        <span className="font-semibold text-lg text-green-600">
                          ${release.amount.toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium">Milestone:</span> {release.milestone?.percentage}% completion
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Requested: {new Date(release.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {release.milestone?.description && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-3">
                        <p className="text-sm text-gray-700">
                          <span className="font-medium">Milestone Description:</span> {release.milestone.description}
                        </p>
                      </div>
                    )}

                    {release.manager_notes && (
                      <div className="bg-blue-50 rounded-lg p-3 mb-3">
                        <p className="text-sm text-blue-800">
                          <span className="font-medium">Manager Notes:</span> {release.manager_notes}
                        </p>
                      </div>
                    )}

                    {release.admin_notes && (
                      <div className="bg-green-50 rounded-lg p-3">
                        <p className="text-sm text-green-800">
                          <span className="font-medium">Admin Notes:</span> {release.admin_notes}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => window.location.href = `/manager/escrow/${release.id}`}
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>
                    
                    {release.status === 'pending' && (
                      <Button
                        size="sm"
                        className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                        onClick={() => {
                          const notes = prompt('Add approval notes (optional):');
                          approveEscrowRelease(release.id, notes || undefined);
                        }}
                      >
                        <CheckCircle className="h-4 w-4" />
                        Approve Release
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Guidelines */}
      <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start gap-3">
          <DollarSign className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Escrow Release Guidelines</h3>
            <div className="text-blue-800 space-y-2">
              <p>• <strong>Milestone-based:</strong> Releases are tied to completed project milestones</p>
              <p>• <strong>Manager approval:</strong> Required before admin can process payment</p>
              <p>• <strong>Quality check:</strong> Ensure work meets quality standards before approval</p>
              <p>• <strong>Client satisfaction:</strong> Verify client acceptance of deliverables</p>
              <p>• <strong>Documentation:</strong> Add notes for transparency and record keeping</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
