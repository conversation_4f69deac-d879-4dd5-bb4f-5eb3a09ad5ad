-- Create designer_applications table for anonymous applications
CREATE TABLE IF NOT EXISTS designer_applications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL UNIQUE,
  full_name TEXT NOT NULL,
  phone TEXT,
  location TEXT,
  specialization TEXT,
  experience TEXT,
  portfolio_url TEXT,
  bio TEXT,
  resume_url TEXT, -- Cloudflare URL
  portfolio_files TEXT[], -- Array of Cloudflare URLs
  certificates TEXT[], -- Array of certificate/certification file URLs
  application_status TEXT DEFAULT 'pending' CHECK (application_status IN ('pending', 'approved', 'rejected')),
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  approved_by UUID REFERENCES profiles(id),
  rejected_by UUID REFERENCES profiles(id),
  created_user_id UUID REFERENCES auth.users(id), -- Links to created account after approval
  rejection_reason TEXT, -- Optional reason for rejection
  notes TEXT, -- Admin notes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_designer_applications_email ON designer_applications(email);
CREATE INDEX IF NOT EXISTS idx_designer_applications_status ON designer_applications(application_status);
CREATE INDEX IF NOT EXISTS idx_designer_applications_applied_at ON designer_applications(applied_at);

-- Add RLS policies
ALTER TABLE designer_applications ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can insert (for anonymous applications)
CREATE POLICY "Anyone can submit applications" ON designer_applications
  FOR INSERT WITH CHECK (true);

-- Policy: Users can view their own applications by email
CREATE POLICY "Users can view own applications" ON designer_applications
  FOR SELECT USING (
    email = (SELECT email FROM auth.users WHERE id = auth.uid())
  );

-- Policy: Admins can view all applications
CREATE POLICY "Admins can view all applications" ON designer_applications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Policy: Admins can update applications
CREATE POLICY "Admins can update applications" ON designer_applications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_designer_applications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_designer_applications_updated_at
    BEFORE UPDATE ON designer_applications
    FOR EACH ROW
    EXECUTE FUNCTION update_designer_applications_updated_at();

-- Add missing columns to profiles table for approved designers
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS specialization TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS experience TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS years_experience INTEGER;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS resume_url TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS portfolio_files TEXT[];
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS certificates TEXT[];
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS applied_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES profiles(id);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS rejected_by UUID REFERENCES profiles(id);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS temp_password BOOLEAN DEFAULT FALSE; -- Flag for temporary password
