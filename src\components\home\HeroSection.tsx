"use client";

import { <PERSON><PERSON> } from "../ui/button";
import { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import NextImage from "next/image";
import { ArrowRight, FileText, Image, BarChart3, X, ChevronLeft, ChevronRight } from "lucide-react";
import { useNavbarContext } from "../../contexts/NavbarContext";

// Onboarding options
const onboardingOptions = [
  {
    id: "build-vision",
    title: "Build Your Vision",
    description: "Generate architectural designs using AI with simple text prompts",
    icon: <FileText className="h-10 w-10 text-primary" />,
    action: "/vision-builder" // Updated to point to the new AI visualization page
  },
  {
    id: "sample-request",
    title: "Sample Request",
    description: "Preview our services with a complimentary sample",
    icon: <Image className="h-10 w-10 text-primary" />,
    action: "/sample-request"
  },
  {
    id: "project-tracker",
    title: "Project Tracker",
    description: "Monitor the progress of your ongoing projects",
    icon: <BarChart3 className="h-10 w-10 text-primary" />,
    action: "/auth/signup?redirect=project-tracker"
  }
];

const HeroSection = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showOnboardingModal, setShowOnboardingModal] = useState(false);
  const [activeWordIndex, setActiveWordIndex] = useState(0);
  const [activeOptionIndex, setActiveOptionIndex] = useState(0);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const optionsContainerRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Get navbar context to hide/show navbar
  const { setNavbarVisible } = useNavbarContext();

  const sectionRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const ctaButtonRef = useRef<HTMLDivElement>(null);

  // Title words for animation
  const titleWords = ["DESIGNING", "STORIES,", "NOT", "JUST", "SPACES."];

  // Set loaded state after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Word animation effect
  useEffect(() => {
    if (!isLoaded) return;

    // Cycle through words
    const wordInterval = setInterval(() => {
      setActiveWordIndex((prev) => (prev + 1) % titleWords.length);
    }, 2000); // Change word every 2 seconds

    return () => clearInterval(wordInterval);
  }, [isLoaded, titleWords.length]);

  // Video handling effects
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleVideoLoad = () => {
      setVideoLoaded(true);
      setVideoError(false);
    };

    const handleVideoError = () => {
      setVideoError(true);
      setVideoLoaded(false);
    };

    video.addEventListener('loadeddata', handleVideoLoad);
    video.addEventListener('error', handleVideoError);

    // Try to play the video
    const playVideo = async () => {
      try {
        await video.play();
      } catch (error) {
        console.log('Video autoplay failed:', error);
        setVideoError(true);
      }
    };

    if (video.readyState >= 3) {
      handleVideoLoad();
    }

    playVideo();

    return () => {
      video.removeEventListener('loadeddata', handleVideoLoad);
      video.removeEventListener('error', handleVideoError);
    };
  }, []);



  // Handle scroll to services section
  const scrollToServices = () => {
    const servicesSection = document.getElementById('services-section');
    servicesSection?.scrollIntoView({ behavior: 'smooth' });
  };

  // Open onboarding modal - keeping this for reference but not using it directly
  const openOnboardingModal = () => {
    setShowOnboardingModal(true);
    setNavbarVisible(false); // Hide navbar when modal opens
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
  };

  // Close onboarding modal
  const closeOnboardingModal = () => {
    setShowOnboardingModal(false);
    setNavbarVisible(true); // Show navbar when modal closes
    document.body.style.overflow = ''; // Restore scrolling
  };

  // Scroll to next option in mobile view
  const scrollToNextOption = () => {
    if (!optionsContainerRef.current) return;

    const newIndex = Math.min(activeOptionIndex + 1, onboardingOptions.length - 1);
    setActiveOptionIndex(newIndex);

    const optionWidth = optionsContainerRef.current.scrollWidth / onboardingOptions.length;
    optionsContainerRef.current.scrollTo({
      left: newIndex * optionWidth,
      behavior: 'smooth'
    });
  };

  // Scroll to previous option in mobile view
  const scrollToPrevOption = () => {
    if (!optionsContainerRef.current) return;

    const newIndex = Math.max(activeOptionIndex - 1, 0);
    setActiveOptionIndex(newIndex);

    const optionWidth = optionsContainerRef.current.scrollWidth / onboardingOptions.length;
    optionsContainerRef.current.scrollTo({
      left: newIndex * optionWidth,
      behavior: 'smooth'
    });
  };

  // Handle scroll event in options container
  const handleOptionsScroll = () => {
    if (!optionsContainerRef.current) return;

    const containerWidth = optionsContainerRef.current.clientWidth;
    const scrollPosition = optionsContainerRef.current.scrollLeft;
    const optionWidth = optionsContainerRef.current.scrollWidth / onboardingOptions.length;

    const newIndex = Math.round(scrollPosition / optionWidth);
    if (newIndex !== activeOptionIndex) {
      setActiveOptionIndex(newIndex);
    }
  };

  return (
    <section
      ref={sectionRef}
      className="relative h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Background with video and image fallback */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <div className="absolute inset-0 z-10">
          {/* Video Background */}
          <video
            ref={videoRef}
            className={`object-cover w-full h-full transition-opacity duration-1000 ${
              videoLoaded && !videoError ? 'opacity-100' : 'opacity-0'
            }`}
            autoPlay
            muted
            loop
            playsInline
            preload="metadata"
          >
            <source src="/hero-video.mp4" type="video/mp4" />
          </video>

          {/* Image Fallback */}
          <NextImage
            src="/hero-sketch.jpg"
            alt="Modern architectural building"
            fill
            className={`object-cover transition-opacity duration-1000 ${
              !videoLoaded || videoError ? 'opacity-100' : 'opacity-0'
            }`}
            priority
            sizes="100vw"
          />

          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/70" aria-hidden="true" />
        </div>
      </div>

      {/* Content container */}
      <div
        ref={contentRef}
        className="container mx-auto px-4 z-20 text-white text-center relative flex flex-col justify-center items-center h-full pt-0 md:pt-12"
      >
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-6 md:space-y-8"
          >
            <motion.h2
              className="text-2xl md:text-4xl font-medium tracking-wide text-white uppercase"
              initial={{ y: 100 }}
              animate={{ y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
            >
              Designing Stories,
            </motion.h2>

            <div className="h-20 md:h-32 flex items-center justify-center overflow-hidden">
              <AnimatePresence mode="wait">
                {titleWords.map((word, index) => (
                  activeWordIndex === index && (
                    <motion.h1
                      key={word}
                      className="text-5xl md:text-8xl font-black leading-tight text-primary drop-shadow-lg"
                      initial={{ y: 100, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      exit={{ y: -100, opacity: 0 }}
                      transition={{ duration: 0.5, ease: "easeOut" }}
                    >
                      {word}
                    </motion.h1>
                  )
                ))}
              </AnimatePresence>
            </div>

            <motion.p
              className="text-xl md:text-2xl text-white max-w-xl mx-auto font-light leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.8 }}
            >
              At Senior's Archi-firm, we craft architecture that breathes identity, telling stories through form, space, and experience.
            </motion.p>

            {/* CTA Button - Updated to link to services section */}
            <motion.div
              ref={ctaButtonRef}
              className="pt-6 md:pt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 20 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 1 }}
            >
              <Button
                variant="default"
                size="lg"
                className="font-semibold px-8 py-4 md:px-12 md:py-6 text-base md:text-lg"
                onClick={scrollToServices}
              >
                Start Your Journey
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>



      {/* Visual elements for design flair */}
      <div className="absolute bottom-0 left-0 w-full h-48 bg-gradient-to-t from-black/40 to-transparent z-10" />

      {/* Improved Onboarding Modal with horizontal scroll on mobile */}
      <AnimatePresence>
        {showOnboardingModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-80 z-[200] flex items-center justify-center p-4"
            onClick={closeOnboardingModal}
          >
            <motion.div
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              transition={{ type: "spring", damping: 25 }}
              className="bg-white rounded-lg shadow-2xl w-full max-w-[95%] md:max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
              onClick={(e) => e.stopPropagation()}
            >


              <div className="flex justify-between items-center p-4 md:p-6 border-b">
                <h2 className="text-xl md:text-2xl font-bold text-gray-800">How would you like to start?</h2>
                <button
                  onClick={closeOnboardingModal}
                  className="text-gray-500 hover:text-gray-700 transition-colors"
                  aria-label="Close modal"
                >
                  <X className="h-5 w-5 md:h-6 md:w-6" />
                </button>
              </div>

              <div className="p-4 md:p-6 overflow-y-auto flex-grow">
                <p className="text-gray-600 mb-6 md:mb-8 text-sm md:text-base">
                  Select an option below to begin your architectural journey with us. At the end, you'll receive a tracking number to monitor your request.
                </p>

                {/* Desktop view: Grid layout */}
                <div className="hidden md:grid md:grid-cols-3 gap-6">
                  {onboardingOptions.map((option) => (
                    <Link
                      href={option.action}
                      key={option.id}
                      className="no-underline"
                    >
                      <motion.div
                        whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
                        className="bg-gray-50 border border-gray-200 rounded-lg p-6 h-full flex flex-col items-center text-center cursor-pointer transition-all"
                      >
                        <div className="bg-primary/10 p-4 rounded-full mb-4">
                          {option.icon}
                        </div>
                        <h3 className="text-xl font-bold text-gray-800 mb-2">{option.title}</h3>
                        <p className="text-gray-600 mb-4 flex-grow">{option.description}</p>
                        <div className="flex items-center text-primary font-medium">
                          Get Started <ArrowRight className="ml-1 h-4 w-4" />
                        </div>
                      </motion.div>
                    </Link>
                  ))}
                </div>

                {/* Mobile view: Horizontal scroll with pagination */}
                <div className="md:hidden relative">
                  {/* Horizontal scrollable container */}
                  <div
                    ref={optionsContainerRef}
                    className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide"
                    onScroll={handleOptionsScroll}
                    style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                  >
                    {onboardingOptions.map((option, index) => (
                      <div
                        key={option.id}
                        className="flex-shrink-0 w-full snap-center px-2 first:pl-0 last:pr-0"
                      >
                        <Link
                          href={option.action}
                          className="no-underline block"
                        >
                          <motion.div
                            whileTap={{ scale: 0.98 }}
                            className="bg-gray-50 border border-gray-200 rounded-lg p-5 flex flex-col items-center text-center h-full"
                          >
                            <div className="bg-primary/10 p-4 rounded-full mb-4">
                              {option.icon}
                            </div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">{option.title}</h3>
                            <p className="text-gray-600 mb-4">{option.description}</p>
                            <div className="flex items-center text-primary font-medium mt-auto">
                              Get Started <ArrowRight className="ml-1 h-4 w-4" />
                            </div>
                          </motion.div>
                        </Link>
                      </div>
                    ))}
                  </div>

                  {/* Navigation buttons */}
                  {activeOptionIndex > 0 && (
                    <button
                      onClick={scrollToPrevOption}
                      className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 bg-white rounded-full p-1 shadow-lg z-10"
                      aria-label="Previous option"
                    >
                      <ChevronLeft className="h-6 w-6 text-gray-600" />
                    </button>
                  )}

                  {activeOptionIndex < onboardingOptions.length - 1 && (
                    <button
                      onClick={scrollToNextOption}
                      className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1 bg-white rounded-full p-1 shadow-lg z-10"
                      aria-label="Next option"
                    >
                      <ChevronRight className="h-6 w-6 text-gray-600" />
                    </button>
                  )}

                  {/* Pagination dots */}
                  <div className="flex justify-center mt-4 space-x-2">
                    {onboardingOptions.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full transition-colors ${
                          index === activeOptionIndex ? 'bg-primary' : 'bg-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <div className="mt-6 md:mt-8 text-center text-gray-500 text-xs md:text-sm">
                  Already have an account? <Link href="/auth/login" className="text-primary font-medium">Log in</Link>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default HeroSection;
