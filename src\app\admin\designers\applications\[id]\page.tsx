"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import FileViewer from "@/components/ui/FileViewer";
import { getDesignerApplicationFileUrl } from "@/lib/r2-upload";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Calendar,
  FileText,
  ExternalLink,
  Download,
  Eye,
  Loader2
} from "lucide-react";

type DesignerApplication = {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
  location?: string;
  specialization: string;
  experience: string;
  portfolio_url?: string;
  bio: string;
  resume_url?: string;
  portfolio_files?: string[];
  certificates?: string[];
  application_status: 'pending' | 'under_review' | 'interview_scheduled' | 'approved' | 'rejected' | 'withdrawn' | 'on_hold';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  admin_notes?: string;
  tags?: string[];
  interview_scheduled_at?: string;
  interview_type?: string;
  interview_duration?: number;
  meeting_link?: string;
  phone_number?: string;
  interview_address?: string;
  interview_agenda?: string;
  interviewer_name?: string;
  interviewer_email?: string;
  interview_notes?: string;
  rejection_reason?: string;
  status_updated_at?: string;
  status_updated_by?: string;
  applied_at: string;
  approved_at?: string;
  rejected_at?: string;
  approved_by?: string;
  rejected_by?: string;
  notes?: string;
  created_at: string;
};

export default function DesignerApplicationDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { user } = useOptimizedAuth();
  const [application, setApplication] = useState<DesignerApplication | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [applicationId, setApplicationId] = useState<string | null>(null);

  // File viewer state
  const [fileViewerOpen, setFileViewerOpen] = useState(false);
  const [currentFile, setCurrentFile] = useState<{
    url: string;
    name: string;
    type: string;
  } | null>(null);

  useEffect(() => {
    const initializeParams = async () => {
      const resolvedParams = await params;
      setApplicationId(resolvedParams.id);
    };
    initializeParams();
  }, [params]);

  useEffect(() => {
    if (user && applicationId) {
      checkAdminAccess();
      fetchApplication();
    }
  }, [user, applicationId]);

  const checkAdminAccess = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      if (data.role !== 'admin') {
        setError('You do not have permission to access this page');
        setLoading(false);
      }
    } catch (error) {
      console.error('Error checking admin access:', error);
      setError('Failed to verify admin access');
      setLoading(false);
    }
  };

  const fetchApplication = async () => {
    if (!applicationId) return;

    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('designer_applications')
        .select(`
          id,
          full_name,
          email,
          phone,
          location,
          specialization,
          experience,
          portfolio_url,
          bio,
          resume_url,
          portfolio_files,
          certificates,
          application_status,
          priority,
          admin_notes,
          tags,
          interview_scheduled_at,
          interview_type,
          interview_duration,
          meeting_link,
          phone_number,
          interview_address,
          interview_agenda,
          interviewer_name,
          interviewer_email,
          interview_notes,
          rejection_reason,
          status_updated_at,
          status_updated_by,
          applied_at,
          approved_at,
          rejected_at,
          approved_by,
          rejected_by,
          notes,
          created_at
        `)
        .eq('id', applicationId)
        .single();

      if (error) throw error;

      setApplication(data);
    } catch (error) {
      console.error('Error fetching application:', error);
      setError('Failed to load designer application');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!application) return;

    setProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      // Update designer application
      const { error } = await supabase
        .from('designer_applications')
        .update({
          application_status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: user?.id,
          status_updated_by: user?.id,
          status_updated_at: new Date().toISOString()
        })
        .eq('id', application.id);

      if (error) throw error;

      setSuccess('Designer application approved successfully');

      // Refresh application data
      await fetchApplication();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error approving application:', error);
      setError('Failed to approve application');
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!application) return;

    setProcessing(true);
    setError(null);
    setSuccess(null);

    try {
      // Update designer application
      const { error } = await supabase
        .from('designer_applications')
        .update({
          application_status: 'rejected',
          rejected_at: new Date().toISOString(),
          rejected_by: user?.id,
          status_updated_by: user?.id,
          status_updated_at: new Date().toISOString()
        })
        .eq('id', application.id);

      if (error) throw error;

      setSuccess('Designer application rejected successfully');

      // Refresh application data
      await fetchApplication();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error rejecting application:', error);
      setError('Failed to reject application');
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-amber-50 border border-amber-200 text-amber-800 rounded-full">
            Pending Review
          </span>
        );
      case 'under_review':
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-blue-50 border border-blue-200 text-blue-800 rounded-full">
            Under Review
          </span>
        );
      case 'interview_scheduled':
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-purple-50 border border-purple-200 text-purple-800 rounded-full">
            Interview Scheduled
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-green-50 border border-green-200 text-green-800 rounded-full">
            Approved
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-red-50 border border-red-200 text-red-800 rounded-full">
            Rejected
          </span>
        );
      case 'on_hold':
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-gray-50 border border-gray-200 text-gray-800 rounded-full">
            On Hold
          </span>
        );
      case 'withdrawn':
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-orange-50 border border-orange-200 text-orange-800 rounded-full">
            Withdrawn
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-gray-50 border border-gray-200 text-gray-800 rounded-full">
            {status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
        );
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleViewFile = (fileKey: string, fileName: string) => {
    const fileUrl = getDesignerApplicationFileUrl(fileKey);
    const fileType = getFileType(fileName);

    setCurrentFile({
      url: fileUrl,
      name: fileName,
      type: fileType
    });
    setFileViewerOpen(true);
  };

  const handleDownloadFile = (fileKey: string, fileName: string) => {
    const fileUrl = getDesignerApplicationFileUrl(fileKey);
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileType = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'application/octet-stream';
    }
  };

  if (error && !application) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6 flex justify-center items-center min-h-[60vh]">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <div className="h-12 w-12 border-t-2 border-b-2 border-brown-600"></div>
        </motion.div>
      </div>
    );
  }

  if (!application) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white border border-gray-200 p-12 text-center">
          <AlertCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-medium text-gray-900 mb-1">Application Not Found</h2>
          <p className="text-gray-500 mb-6">
            The designer application you are looking for does not exist or has been removed.
          </p>
          <Link href="/admin/designers/applications">
            <Button className="bg-brown-600 hover:bg-brown-700 text-white border-0">
              Back to Applications
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-4 lg:p-6 min-h-screen">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-8">
        <div className="flex items-center mb-4 sm:mb-0">
          <Link href="/admin/designers/applications">
            <Button
              variant="ghost"
              className="p-2 h-auto mr-4 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 text-gray-500" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Designer Application</h1>
            <p className="text-sm text-gray-500 mt-1">Review and manage application details</p>
          </div>
        </div>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      {success && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-green-50 border border-green-200 p-4 mb-6 flex items-start"
        >
          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-green-700">{success}</p>
        </motion.div>
      )}

      {/* Main Application Card */}
      <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6 lg:p-8 mb-8">
        {/* Applicant Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="flex items-center mb-6 lg:mb-0">
            <div className="w-20 h-20 bg-gradient-to-br from-brown-100 to-brown-200 flex items-center justify-center rounded-xl border border-brown-200 mr-6">
              <User className="h-10 w-10 text-brown-600" />
            </div>
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">{application.full_name}</h2>
              <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                <div className="flex items-center text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">{application.email}</span>
                </div>
                {application.phone && (
                  <div className="flex items-center text-gray-600">
                    <Phone className="h-4 w-4 mr-2" />
                    <span className="text-sm font-medium">{application.phone}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex flex-col items-start lg:items-end space-y-3">
            <div className="flex flex-wrap gap-2">
              {getStatusBadge(application.application_status)}
              {application.priority && (
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(application.priority)}`}>
                  {application.priority.toUpperCase()}
                </span>
              )}
            </div>
            <div className="text-sm text-gray-500 flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Applied on {formatDate(application.applied_at || application.created_at)}</span>
            </div>
          </div>
        </div>

        {/* Information Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Personal Information */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
              <User className="h-6 w-6 mr-3 text-brown-600" />
              Personal Information
            </h3>

            <div className="space-y-6">
              <div className="flex items-start">
                <Phone className="h-5 w-5 text-gray-400 mt-1 mr-4 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 mb-1">Phone Number</p>
                  <p className="text-base font-semibold text-gray-900">{application.phone || 'Not provided'}</p>
                </div>
              </div>

              <div className="flex items-start">
                <MapPin className="h-5 w-5 text-gray-400 mt-1 mr-4 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 mb-1">Location</p>
                  <p className="text-base font-semibold text-gray-900">{application.location || 'Not provided'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
              <Briefcase className="h-6 w-6 mr-3 text-brown-600" />
              Professional Information
            </h3>

            <div className="space-y-6">
              <div className="flex items-start">
                <Briefcase className="h-5 w-5 text-gray-400 mt-1 mr-4 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 mb-1">Specialization</p>
                  <p className="text-base font-semibold text-gray-900">{application.specialization || 'Not provided'}</p>
                </div>
              </div>

              <div className="flex items-start">
                <Calendar className="h-5 w-5 text-gray-400 mt-1 mr-4 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 mb-1">Experience</p>
                  <p className="text-base font-semibold text-gray-900">{application.experience || 'Not provided'}</p>
                </div>
              </div>

              {application.portfolio_url && (
                <div className="flex items-start">
                  <ExternalLink className="h-5 w-5 text-gray-400 mt-1 mr-4 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Portfolio URL</p>
                    <a
                      href={application.portfolio_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-base font-semibold text-blue-600 hover:text-blue-700 hover:underline transition-colors duration-200 break-all"
                      title={application.portfolio_url}
                    >
                      {application.portfolio_url.length > 40
                        ? `${application.portfolio_url.substring(0, 40)}...`
                        : application.portfolio_url}
                    </a>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Professional Bio */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
            <FileText className="h-6 w-6 mr-3 text-brown-600" />
            Professional Bio
          </h3>

          <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-xl p-6">
            <p className="text-base leading-relaxed text-gray-800 whitespace-pre-line">
              {application.bio || 'No bio provided'}
            </p>
          </div>
        </div>

        {/* Files and Documents Section */}
        <div className="space-y-8 mb-8">
          {application.resume_url && (
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
                <FileText className="h-6 w-6 mr-3 text-brown-600" />
                Resume/CV
              </h3>

              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                <div className="flex items-center flex-1 min-w-0">
                  <FileText className="h-8 w-8 text-gray-400 mr-4 flex-shrink-0" />
                  <div className="min-w-0">
                    <p className="text-lg font-medium text-gray-900 truncate">Resume.pdf</p>
                    <p className="text-sm text-gray-500">Professional resume document</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                  <Button
                    variant="outline"
                    size="lg"
                    className="flex items-center justify-center px-6 py-3 text-sm font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200"
                    onClick={() => handleViewFile(application.resume_url, 'Resume.pdf')}
                  >
                    <Eye className="h-5 w-5 mr-2" />
                    View Resume
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="flex items-center justify-center px-6 py-3 text-sm font-medium hover:bg-green-50 hover:border-green-300 hover:text-green-700 transition-all duration-200"
                    onClick={() => handleDownloadFile(application.resume_url, 'Resume.pdf')}
                  >
                    <Download className="h-5 w-5 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
            </div>
          )}

          {application.portfolio_files && application.portfolio_files.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
                <FileText className="h-6 w-6 mr-3 text-brown-600" />
                Portfolio Samples ({application.portfolio_files.length})
              </h3>

              <div className="space-y-4">
                {application.portfolio_files.map((file, index) => {
                  const fileName = file.split('/').pop() || `Portfolio-${index + 1}`;
                  return (
                    <div key={index} className="flex flex-col lg:flex-row lg:items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors duration-200">
                      <div className="flex items-center mb-4 lg:mb-0 flex-1 min-w-0">
                        <FileText className="h-6 w-6 text-gray-400 mr-4 flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <p className="text-base font-medium text-gray-900 truncate" title={fileName}>
                            {fileName}
                          </p>
                          <p className="text-sm text-gray-500">Portfolio file {index + 1}</p>
                        </div>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center justify-center px-4 py-2 text-sm font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200"
                          onClick={() => handleViewFile(file, fileName)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View File
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center justify-center px-4 py-2 text-sm font-medium hover:bg-green-50 hover:border-green-300 hover:text-green-700 transition-all duration-200"
                          onClick={() => handleDownloadFile(file, fileName)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {application.certificates && application.certificates.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="text-xl font-semibold mb-6 flex items-center text-gray-900">
                <FileText className="h-6 w-6 mr-3 text-brown-600" />
                Certificates & Certifications ({application.certificates.length})
              </h3>

              <div className="space-y-4">
                {application.certificates.map((file, index) => {
                  const fileName = file.split('/').pop() || `Certificate-${index + 1}`;
                  return (
                    <div key={index} className="flex flex-col lg:flex-row lg:items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors duration-200">
                      <div className="flex items-center mb-4 lg:mb-0 flex-1 min-w-0">
                        <FileText className="h-6 w-6 text-gray-400 mr-4 flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <p className="text-base font-medium text-gray-900 truncate" title={fileName}>
                            {fileName}
                          </p>
                          <p className="text-sm text-gray-500">Certificate {index + 1}</p>
                        </div>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center justify-center px-4 py-2 text-sm font-medium hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-all duration-200"
                          onClick={() => handleViewFile(file, fileName)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Certificate
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center justify-center px-4 py-2 text-sm font-medium hover:bg-green-50 hover:border-green-300 hover:text-green-700 transition-all duration-200"
                          onClick={() => handleDownloadFile(file, fileName)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons Section */}
        <div className="border-t border-gray-200 pt-8 mt-8">
          <div className="flex flex-col sm:flex-row gap-4 sm:justify-end">
            {application.application_status === 'pending' && (
              <>
                <Button
                  variant="outline"
                  size="lg"
                  className="flex items-center justify-center px-6 py-3 text-base font-medium border-red-200 text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
                  onClick={handleReject}
                  disabled={processing}
                >
                  {processing ? (
                    <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                  ) : (
                    <XCircle className="h-5 w-5 mr-3" />
                  )}
                  <span>Reject Application</span>
                </Button>

                <Button
                  size="lg"
                  className="flex items-center justify-center px-8 py-3 text-base font-medium bg-brown-600 hover:bg-brown-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                  onClick={handleApprove}
                  disabled={processing}
                >
                  {processing ? (
                    <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                  ) : (
                    <CheckCircle className="h-5 w-5 mr-3" />
                  )}
                  <span>Approve Application</span>
                </Button>
              </>
            )}

            {application.application_status === 'approved' && (
              <Button
                variant="outline"
                size="lg"
                className="flex items-center justify-center px-6 py-3 text-base font-medium border-red-200 text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
                onClick={handleReject}
                disabled={processing}
              >
                {processing ? (
                  <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                ) : (
                  <XCircle className="h-5 w-5 mr-3" />
                )}
                <span>Revoke Approval</span>
              </Button>
            )}

            {application.application_status === 'rejected' && (
              <Button
                size="lg"
                className="flex items-center justify-center px-8 py-3 text-base font-medium bg-brown-600 hover:bg-brown-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                onClick={handleApprove}
                disabled={processing}
              >
                {processing ? (
                  <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                ) : (
                  <CheckCircle className="h-5 w-5 mr-3" />
                )}
                <span>Approve Application</span>
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* File Viewer Modal */}
      {currentFile && (
        <FileViewer
          isOpen={fileViewerOpen}
          onClose={() => {
            setFileViewerOpen(false);
            setCurrentFile(null);
          }}
          fileUrl={currentFile.url}
          fileName={currentFile.name}
          fileType={currentFile.type}
        />
      )}
    </div>
  );
}