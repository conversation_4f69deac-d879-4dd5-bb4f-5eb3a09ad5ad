'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Dispute, DisputeStatus } from '@/types/dispute';
import { getDisputes } from '@/lib/api/disputes';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';

interface DisputesListProps {
  projectId?: string;
  userRole?: 'client' | 'designer' | 'admin';
}

export function DisputesList({ projectId, userRole }: DisputesListProps) {
  const { token, user } = useOptimizedAuth();
  const router = useRouter();
  const [disputes, setDisputes] = useState<Dispute[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<DisputeStatus | 'all'>('all');

  useEffect(() => {
    if (!token) return;

    const fetchDisputes = async () => {
      setLoading(true);
      try {
        const status = statusFilter !== 'all' ? statusFilter : undefined;
        const fetchedDisputes = await getDisputes(token, status, projectId);
        setDisputes(fetchedDisputes);
      } catch (error) {
        console.error('Error fetching disputes:', error);
        toast({
          title: 'Error',
          description: 'Failed to load disputes',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDisputes();
  }, [token, statusFilter, projectId]);

  const getStatusBadgeVariant = (status: DisputeStatus) => {
    switch (status) {
      case 'open':
        return 'destructive';
      case 'under_review':
        return 'warning';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getOtherParty = (dispute: Dispute) => {
    if (!user) return null;

    if (user.id === dispute.client_id) {
      return {
        name: dispute.designer.full_name,
        avatar: dispute.designer.avatar_url,
        role: 'Designer',
      };
    } else {
      return {
        name: dispute.client.full_name,
        avatar: dispute.client.avatar_url,
        role: 'Client',
      };
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="w-full">
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-10 w-24" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (disputes.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>No Disputes Found</CardTitle>
          <CardDescription>
            {statusFilter !== 'all'
              ? `No disputes with status "${statusFilter}" found.`
              : projectId
              ? 'No disputes found for this project.'
              : 'You have no active disputes.'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            {projectId
              ? 'If you are experiencing issues with this project, you can create a new dispute.'
              : 'Disputes are created when there are issues with a project that need resolution.'}
          </p>
        </CardContent>
        {projectId && (
          <CardFooter>
            <Button onClick={() => router.push(`/projects/${projectId}/disputes/new`)}>
              Create Dispute
            </Button>
          </CardFooter>
        )}
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">
          {projectId ? 'Project Disputes' : 'All Disputes'}
        </h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Filter:</span>
          <Select
            value={statusFilter}
            onValueChange={(value) => setStatusFilter(value as DisputeStatus | 'all')}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="under_review">Under Review</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-4">
        {disputes.map((dispute) => {
          const otherParty = getOtherParty(dispute);
          const createdAt = new Date(dispute.created_at);
          const timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });

          return (
            <Card key={dispute.id} className="w-full">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{dispute.title}</CardTitle>
                    <CardDescription>
                      Project: {dispute.projects.title}
                    </CardDescription>
                  </div>
                  <Badge variant={getStatusBadgeVariant(dispute.status)}>
                    {dispute.status.replace('_', ' ').toUpperCase()}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4 mb-4">
                  {otherParty && (
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={otherParty.avatar || undefined} alt={otherParty.name} />
                        <AvatarFallback>
                          {otherParty.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{otherParty.name}</p>
                        <p className="text-xs text-muted-foreground">{otherParty.role}</p>
                      </div>
                    </div>
                  )}
                  <div className="text-xs text-muted-foreground">
                    Created {timeAgo} by {dispute.creator.full_name}
                  </div>
                </div>
                <p className="text-sm line-clamp-2">{dispute.description}</p>
              </CardContent>
              <CardFooter>
                <Button asChild>
                  <Link href={`/${userRole || 'client'}/disputes/${dispute.id}`}>View Details</Link>
                </Button>
              </CardFooter>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
