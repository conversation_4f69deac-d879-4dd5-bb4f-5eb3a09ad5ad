-- =====================================================
-- NEGOTIATION SYSTEM DATABASE SCHEMA
-- Advanced negotiation management for manager oversight
-- =====================================================

-- 1. NEGOTIATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS negotiations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    initiated_by UUID NOT NULL,
    negotiation_type VARCHAR(50) NOT NULL CHECK (negotiation_type IN (
        'scope_change', 'timeline_extension', 'budget_adjustment', 
        'milestone_modification', 'quality_revision', 'contract_amendment'
    )),
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN (
        'draft', 'pending', 'in_progress', 'accepted', 'rejected', 'expired', 'cancelled'
    )),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- Basic information
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    
    -- Terms comparison
    current_terms JSONB NOT NULL DEFAULT '{}',
    proposed_terms JSONB NOT NULL DEFAULT '{}',
    final_terms JSONB DEFAULT '{}',
    
    -- Approval workflow
    manager_id UUID,
    client_approval_required BOOLEAN DEFAULT TRUE,
    designer_approval_required BOOLEAN DEFAULT TRUE,
    manager_approval_required BOOLEAN DEFAULT TRUE,
    
    -- Approval timestamps
    client_approved_at TIMESTAMP WITH TIME ZONE,
    client_approved_by UUID,
    designer_approved_at TIMESTAMP WITH TIME ZONE,
    designer_approved_by UUID,
    manager_approved_at TIMESTAMP WITH TIME ZONE,
    manager_approved_by UUID,
    
    -- Timing
    deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional data
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT fk_negotiations_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    CONSTRAINT fk_negotiations_initiator FOREIGN KEY (initiated_by) REFERENCES profiles(id),
    CONSTRAINT fk_negotiations_manager FOREIGN KEY (manager_id) REFERENCES profiles(id),
    CONSTRAINT fk_negotiations_client_approver FOREIGN KEY (client_approved_by) REFERENCES profiles(id),
    CONSTRAINT fk_negotiations_designer_approver FOREIGN KEY (designer_approved_by) REFERENCES profiles(id),
    CONSTRAINT fk_negotiations_manager_approver FOREIGN KEY (manager_approved_by) REFERENCES profiles(id)
);

-- 2. NEGOTIATION MESSAGES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS negotiation_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    negotiation_id UUID NOT NULL,
    sender_id UUID NOT NULL,
    message_type VARCHAR(50) NOT NULL CHECK (message_type IN (
        'proposal', 'counter_proposal', 'clarification', 'approval', 
        'rejection', 'comment', 'terms_update', 'deadline_extension'
    )),
    
    -- Message content
    content TEXT NOT NULL,
    attachments JSONB DEFAULT '[]', -- Array of file references
    terms_changes JSONB DEFAULT '{}', -- Changes to terms if applicable
    
    -- Timing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    edited_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional data
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT fk_negotiation_messages_negotiation FOREIGN KEY (negotiation_id) REFERENCES negotiations(id) ON DELETE CASCADE,
    CONSTRAINT fk_negotiation_messages_sender FOREIGN KEY (sender_id) REFERENCES profiles(id)
);

-- 3. NEGOTIATION TEMPLATES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS negotiation_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    negotiation_type VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- Template content
    template_content JSONB NOT NULL DEFAULT '{}',
    default_terms JSONB DEFAULT '{}',
    approval_workflow JSONB DEFAULT '{}',
    
    -- Template settings
    is_active BOOLEAN DEFAULT TRUE,
    is_system_template BOOLEAN DEFAULT FALSE,
    created_by UUID NOT NULL,
    
    -- Timing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Additional data
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT fk_negotiation_templates_creator FOREIGN KEY (created_by) REFERENCES profiles(id)
);

-- 4. NEGOTIATION ACTIVITIES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS negotiation_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    negotiation_id UUID NOT NULL,
    project_id UUID NOT NULL,
    
    -- Activity details
    activity_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    performed_by UUID NOT NULL,
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- State tracking
    previous_state JSONB,
    new_state JSONB,
    
    -- Additional context
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT fk_negotiation_activities_negotiation FOREIGN KEY (negotiation_id) REFERENCES negotiations(id) ON DELETE CASCADE,
    CONSTRAINT fk_negotiation_activities_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    CONSTRAINT fk_negotiation_activities_performer FOREIGN KEY (performed_by) REFERENCES profiles(id)
);

-- 5. CLIENT SATISFACTION TRACKING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS client_satisfaction (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    client_id UUID NOT NULL,
    manager_id UUID,
    
    -- Satisfaction metrics
    overall_rating INTEGER CHECK (overall_rating BETWEEN 1 AND 10),
    communication_rating INTEGER CHECK (communication_rating BETWEEN 1 AND 10),
    quality_rating INTEGER CHECK (quality_rating BETWEEN 1 AND 10),
    timeline_rating INTEGER CHECK (timeline_rating BETWEEN 1 AND 10),
    value_rating INTEGER CHECK (value_rating BETWEEN 1 AND 10),
    
    -- Feedback
    feedback_text TEXT,
    improvement_suggestions TEXT,
    would_recommend BOOLEAN,
    would_work_again BOOLEAN,
    
    -- Survey details
    survey_type VARCHAR(50) DEFAULT 'milestone' CHECK (survey_type IN (
        'milestone', 'project_completion', 'periodic', 'issue_resolution'
    )),
    milestone_id UUID,
    
    -- Timing
    survey_sent_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Additional data
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT fk_client_satisfaction_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    CONSTRAINT fk_client_satisfaction_client FOREIGN KEY (client_id) REFERENCES profiles(id),
    CONSTRAINT fk_client_satisfaction_manager FOREIGN KEY (manager_id) REFERENCES profiles(id),
    CONSTRAINT fk_client_satisfaction_milestone FOREIGN KEY (milestone_id) REFERENCES project_milestones(id)
);

-- 6. PROJECT PERFORMANCE METRICS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS project_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    manager_id UUID,
    
    -- Timeline metrics
    planned_duration_days INTEGER,
    actual_duration_days INTEGER,
    timeline_variance_percentage DECIMAL(5,2),
    
    -- Budget metrics
    planned_budget DECIMAL(10,2),
    actual_budget DECIMAL(10,2),
    budget_variance_percentage DECIMAL(5,2),
    
    -- Quality metrics
    revision_count INTEGER DEFAULT 0,
    quality_score DECIMAL(3,2), -- Average quality review score
    client_satisfaction_score DECIMAL(3,2),
    
    -- Communication metrics
    total_messages INTEGER DEFAULT 0,
    response_time_hours DECIMAL(5,2),
    negotiation_count INTEGER DEFAULT 0,
    
    -- Milestone metrics
    milestones_on_time INTEGER DEFAULT 0,
    milestones_delayed INTEGER DEFAULT 0,
    milestone_completion_rate DECIMAL(5,2),
    
    -- Overall performance
    performance_score DECIMAL(3,2), -- Calculated composite score
    performance_grade VARCHAR(2), -- A, B, C, D, F
    
    -- Timing
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    period_start DATE,
    period_end DATE,
    
    -- Additional data
    metadata JSONB DEFAULT '{}',
    
    -- Constraints
    CONSTRAINT fk_project_performance_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    CONSTRAINT fk_project_performance_manager FOREIGN KEY (manager_id) REFERENCES profiles(id)
);

-- 7. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Negotiations indexes
CREATE INDEX IF NOT EXISTS idx_negotiations_project_id ON negotiations(project_id);
CREATE INDEX IF NOT EXISTS idx_negotiations_manager_id ON negotiations(manager_id);
CREATE INDEX IF NOT EXISTS idx_negotiations_status ON negotiations(status);
CREATE INDEX IF NOT EXISTS idx_negotiations_priority ON negotiations(priority);
CREATE INDEX IF NOT EXISTS idx_negotiations_deadline ON negotiations(deadline);
CREATE INDEX IF NOT EXISTS idx_negotiations_type ON negotiations(negotiation_type);

-- Negotiation Messages indexes
CREATE INDEX IF NOT EXISTS idx_negotiation_messages_negotiation_id ON negotiation_messages(negotiation_id);
CREATE INDEX IF NOT EXISTS idx_negotiation_messages_sender_id ON negotiation_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_negotiation_messages_created_at ON negotiation_messages(created_at);

-- Negotiation Activities indexes
CREATE INDEX IF NOT EXISTS idx_negotiation_activities_negotiation_id ON negotiation_activities(negotiation_id);
CREATE INDEX IF NOT EXISTS idx_negotiation_activities_project_id ON negotiation_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_negotiation_activities_performed_by ON negotiation_activities(performed_by);
CREATE INDEX IF NOT EXISTS idx_negotiation_activities_performed_at ON negotiation_activities(performed_at);

-- Client Satisfaction indexes
CREATE INDEX IF NOT EXISTS idx_client_satisfaction_project_id ON client_satisfaction(project_id);
CREATE INDEX IF NOT EXISTS idx_client_satisfaction_client_id ON client_satisfaction(client_id);
CREATE INDEX IF NOT EXISTS idx_client_satisfaction_manager_id ON client_satisfaction(manager_id);
CREATE INDEX IF NOT EXISTS idx_client_satisfaction_completed_at ON client_satisfaction(completed_at);

-- Project Performance indexes
CREATE INDEX IF NOT EXISTS idx_project_performance_project_id ON project_performance_metrics(project_id);
CREATE INDEX IF NOT EXISTS idx_project_performance_manager_id ON project_performance_metrics(manager_id);
CREATE INDEX IF NOT EXISTS idx_project_performance_calculated_at ON project_performance_metrics(calculated_at);
CREATE INDEX IF NOT EXISTS idx_project_performance_score ON project_performance_metrics(performance_score);

-- 8. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE negotiations ENABLE ROW LEVEL SECURITY;
ALTER TABLE negotiation_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE negotiation_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE negotiation_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_satisfaction ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_performance_metrics ENABLE ROW LEVEL SECURITY;

-- 9. SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 NEGOTIATION SYSTEM SCHEMA CREATED SUCCESSFULLY!';
    RAISE NOTICE '';
    RAISE NOTICE 'Tables created:';
    RAISE NOTICE '✅ negotiations';
    RAISE NOTICE '✅ negotiation_messages';
    RAISE NOTICE '✅ negotiation_templates';
    RAISE NOTICE '✅ negotiation_activities';
    RAISE NOTICE '✅ client_satisfaction';
    RAISE NOTICE '✅ project_performance_metrics';
    RAISE NOTICE '';
    RAISE NOTICE 'Next: Run negotiation-rls-policies.sql for security';
    RAISE NOTICE '';
END $$;
