'use client';

import { supabase } from '@/lib/supabase';

/**
 * Client Satisfaction Management System
 * Handles client feedback collection, analysis, and reporting
 */

export interface ClientSatisfactionSurvey {
  id: string;
  project_id: string;
  client_id: string;
  manager_id?: string;
  overall_rating: number;
  communication_rating: number;
  quality_rating: number;
  timeline_rating: number;
  value_rating: number;
  feedback_text?: string;
  improvement_suggestions?: string;
  would_recommend: boolean;
  would_work_again: boolean;
  survey_type: 'milestone' | 'project_completion' | 'periodic' | 'issue_resolution';
  milestone_id?: string;
  survey_sent_at?: string;
  completed_at: string;
  metadata: any;
}

export interface SatisfactionMetrics {
  overall_average: number;
  communication_average: number;
  quality_average: number;
  timeline_average: number;
  value_average: number;
  total_responses: number;
  recommendation_rate: number;
  retention_rate: number;
  trend_direction: 'up' | 'down' | 'stable';
  trend_percentage: number;
}

export interface SatisfactionInsight {
  category: string;
  insight: string;
  impact: 'positive' | 'negative' | 'neutral';
  priority: 'high' | 'medium' | 'low';
  suggested_action: string;
}

/**
 * Client Satisfaction Manager Class
 */
export class ClientSatisfactionManager {
  /**
   * Create and send satisfaction survey
   */
  static async createSatisfactionSurvey(params: {
    projectId: string;
    clientId: string;
    managerId?: string;
    surveyType: string;
    milestoneId?: string;
    customQuestions?: any[];
  }): Promise<{ success: boolean; surveyId?: string; error?: string }> {
    try {
      const { projectId, clientId, managerId, surveyType, milestoneId, customQuestions } = params;

      // Create survey record
      const { data: survey, error } = await supabase
        .from('client_satisfaction_surveys')
        .insert({
          project_id: projectId,
          client_id: clientId,
          manager_id: managerId,
          survey_type: surveyType,
          milestone_id: milestoneId,
          status: 'sent',
          sent_at: new Date().toISOString(),
          custom_questions: customQuestions || [],
          metadata: {
            created_by: 'system',
            survey_version: '1.0'
          }
        })
        .select()
        .single();

      if (error) throw error;

      // Send survey notification to client
      await this.sendSurveyNotification(survey);

      // Log activity
      await this.logSatisfactionActivity({
        survey_id: survey.id,
        project_id: projectId,
        activity_type: 'survey_sent',
        description: `${surveyType} satisfaction survey sent to client`,
        performed_by: 'system',
        metadata: { survey_type: surveyType }
      });

      return { success: true, surveyId: survey.id };
    } catch (error) {
      console.error('Error creating satisfaction survey:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Submit satisfaction survey response
   */
  static async submitSatisfactionResponse(params: {
    surveyId: string;
    ratings: {
      overall_rating: number;
      communication_rating: number;
      quality_rating: number;
      timeline_rating: number;
      value_rating: number;
    };
    feedback: {
      feedback_text?: string;
      improvement_suggestions?: string;
      would_recommend: boolean;
      would_work_again: boolean;
    };
    customResponses?: any;
  }): Promise<{ success: boolean; satisfaction?: ClientSatisfactionSurvey; error?: string }> {
    try {
      const { surveyId, ratings, feedback, customResponses } = params;

      // Update survey with response
      const { data: satisfaction, error } = await supabase
        .from('client_satisfaction')
        .insert({
          survey_id: surveyId,
          ...ratings,
          ...feedback,
          custom_responses: customResponses || {},
          completed_at: new Date().toISOString(),
          metadata: {
            response_time: new Date().toISOString(),
            user_agent: typeof window !== 'undefined' ? navigator.userAgent : null
          }
        })
        .select()
        .single();

      if (error) throw error;

      // Update survey status
      await supabase
        .from('client_satisfaction_surveys')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', surveyId);

      // Analyze response and generate insights
      await this.analyzeResponse(satisfaction);

      // Send thank you notification
      await this.sendThankYouNotification(satisfaction);

      return { success: true, satisfaction };
    } catch (error) {
      console.error('Error submitting satisfaction response:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get satisfaction metrics for manager
   */
  static async getSatisfactionMetrics(params: {
    managerId?: string;
    projectId?: string;
    timeframe?: 'week' | 'month' | 'quarter' | 'year';
    startDate?: string;
    endDate?: string;
  }): Promise<{ success: boolean; metrics?: SatisfactionMetrics; error?: string }> {
    try {
      const { managerId, projectId, timeframe = 'month', startDate, endDate } = params;

      // Calculate date range
      const now = new Date();
      let fromDate = startDate ? new Date(startDate) : new Date();
      let toDate = endDate ? new Date(endDate) : now;

      if (!startDate) {
        switch (timeframe) {
          case 'week':
            fromDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            fromDate.setMonth(now.getMonth() - 1);
            break;
          case 'quarter':
            fromDate.setMonth(now.getMonth() - 3);
            break;
          case 'year':
            fromDate.setFullYear(now.getFullYear() - 1);
            break;
        }
      }

      // Build query
      let query = supabase
        .from('client_satisfaction')
        .select('*')
        .gte('completed_at', fromDate.toISOString())
        .lte('completed_at', toDate.toISOString());

      if (managerId) {
        query = query.eq('manager_id', managerId);
      }

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data: responses, error } = await query;

      if (error) throw error;

      if (!responses || responses.length === 0) {
        return {
          success: true,
          metrics: {
            overall_average: 0,
            communication_average: 0,
            quality_average: 0,
            timeline_average: 0,
            value_average: 0,
            total_responses: 0,
            recommendation_rate: 0,
            retention_rate: 0,
            trend_direction: 'stable',
            trend_percentage: 0
          }
        };
      }

      // Calculate metrics
      const totalResponses = responses.length;
      const overallAvg = responses.reduce((sum, r) => sum + r.overall_rating, 0) / totalResponses;
      const communicationAvg = responses.reduce((sum, r) => sum + r.communication_rating, 0) / totalResponses;
      const qualityAvg = responses.reduce((sum, r) => sum + r.quality_rating, 0) / totalResponses;
      const timelineAvg = responses.reduce((sum, r) => sum + r.timeline_rating, 0) / totalResponses;
      const valueAvg = responses.reduce((sum, r) => sum + r.value_rating, 0) / totalResponses;

      const recommendationRate = (responses.filter(r => r.would_recommend).length / totalResponses) * 100;
      const retentionRate = (responses.filter(r => r.would_work_again).length / totalResponses) * 100;

      // Calculate trend (compare with previous period)
      const previousFromDate = new Date(fromDate);
      const previousToDate = new Date(toDate);
      const periodDiff = toDate.getTime() - fromDate.getTime();
      previousFromDate.setTime(fromDate.getTime() - periodDiff);
      previousToDate.setTime(toDate.getTime() - periodDiff);

      let previousQuery = supabase
        .from('client_satisfaction')
        .select('overall_rating')
        .gte('completed_at', previousFromDate.toISOString())
        .lte('completed_at', previousToDate.toISOString());

      if (managerId) {
        previousQuery = previousQuery.eq('manager_id', managerId);
      }

      const { data: previousResponses } = await previousQuery;
      
      let trendDirection: 'up' | 'down' | 'stable' = 'stable';
      let trendPercentage = 0;

      if (previousResponses && previousResponses.length > 0) {
        const previousAvg = previousResponses.reduce((sum, r) => sum + r.overall_rating, 0) / previousResponses.length;
        const change = ((overallAvg - previousAvg) / previousAvg) * 100;
        
        if (Math.abs(change) > 5) {
          trendDirection = change > 0 ? 'up' : 'down';
          trendPercentage = Math.abs(change);
        }
      }

      const metrics: SatisfactionMetrics = {
        overall_average: Math.round(overallAvg * 10) / 10,
        communication_average: Math.round(communicationAvg * 10) / 10,
        quality_average: Math.round(qualityAvg * 10) / 10,
        timeline_average: Math.round(timelineAvg * 10) / 10,
        value_average: Math.round(valueAvg * 10) / 10,
        total_responses: totalResponses,
        recommendation_rate: Math.round(recommendationRate * 10) / 10,
        retention_rate: Math.round(retentionRate * 10) / 10,
        trend_direction: trendDirection,
        trend_percentage: Math.round(trendPercentage * 10) / 10
      };

      return { success: true, metrics };
    } catch (error) {
      console.error('Error getting satisfaction metrics:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Generate satisfaction insights
   */
  static async generateSatisfactionInsights(params: {
    managerId?: string;
    timeframe?: string;
  }): Promise<{ success: boolean; insights?: SatisfactionInsight[]; error?: string }> {
    try {
      const { managerId, timeframe = 'month' } = params;

      // Get satisfaction metrics
      const metricsResult = await this.getSatisfactionMetrics({ managerId, timeframe });
      
      if (!metricsResult.success || !metricsResult.metrics) {
        throw new Error('Failed to get satisfaction metrics');
      }

      const metrics = metricsResult.metrics;
      const insights: SatisfactionInsight[] = [];

      // Overall satisfaction insights
      if (metrics.overall_average >= 8) {
        insights.push({
          category: 'Overall Satisfaction',
          insight: 'Excellent client satisfaction scores indicate strong project management',
          impact: 'positive',
          priority: 'low',
          suggested_action: 'Continue current practices and document successful strategies'
        });
      } else if (metrics.overall_average < 6) {
        insights.push({
          category: 'Overall Satisfaction',
          insight: 'Low satisfaction scores require immediate attention',
          impact: 'negative',
          priority: 'high',
          suggested_action: 'Schedule client meetings to address concerns and improve processes'
        });
      }

      // Communication insights
      if (metrics.communication_average < metrics.overall_average - 1) {
        insights.push({
          category: 'Communication',
          insight: 'Communication scores are below overall satisfaction',
          impact: 'negative',
          priority: 'medium',
          suggested_action: 'Implement more frequent client updates and improve response times'
        });
      }

      // Quality insights
      if (metrics.quality_average >= 8) {
        insights.push({
          category: 'Quality',
          insight: 'High quality ratings demonstrate effective quality control',
          impact: 'positive',
          priority: 'low',
          suggested_action: 'Maintain quality standards and share best practices with team'
        });
      }

      // Timeline insights
      if (metrics.timeline_average < 7) {
        insights.push({
          category: 'Timeline Management',
          insight: 'Timeline satisfaction needs improvement',
          impact: 'negative',
          priority: 'medium',
          suggested_action: 'Review project planning and implement better deadline management'
        });
      }

      // Recommendation rate insights
      if (metrics.recommendation_rate < 70) {
        insights.push({
          category: 'Client Advocacy',
          insight: 'Low recommendation rate may impact business growth',
          impact: 'negative',
          priority: 'high',
          suggested_action: 'Focus on exceeding client expectations and follow up on completed projects'
        });
      }

      // Trend insights
      if (metrics.trend_direction === 'down' && metrics.trend_percentage > 10) {
        insights.push({
          category: 'Satisfaction Trend',
          insight: `Satisfaction scores have declined by ${metrics.trend_percentage}%`,
          impact: 'negative',
          priority: 'high',
          suggested_action: 'Investigate recent changes and implement corrective measures immediately'
        });
      } else if (metrics.trend_direction === 'up' && metrics.trend_percentage > 10) {
        insights.push({
          category: 'Satisfaction Trend',
          insight: `Satisfaction scores have improved by ${metrics.trend_percentage}%`,
          impact: 'positive',
          priority: 'low',
          suggested_action: 'Document and replicate successful improvements across all projects'
        });
      }

      return { success: true, insights };
    } catch (error) {
      console.error('Error generating satisfaction insights:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Analyze individual response
   */
  static async analyzeResponse(satisfaction: any): Promise<void> {
    try {
      // Generate automatic insights based on response
      const insights = [];

      if (satisfaction.overall_rating <= 5) {
        insights.push('Low satisfaction score requires immediate manager attention');
      }

      if (satisfaction.communication_rating <= 4) {
        insights.push('Communication issues identified - improve response times');
      }

      if (satisfaction.quality_rating <= 4) {
        insights.push('Quality concerns raised - review deliverables and processes');
      }

      if (!satisfaction.would_recommend) {
        insights.push('Client would not recommend - critical issue requiring escalation');
      }

      // Store insights if any critical issues found
      if (insights.length > 0) {
        await supabase
          .from('satisfaction_alerts')
          .insert({
            satisfaction_id: satisfaction.id,
            project_id: satisfaction.project_id,
            alert_type: 'low_satisfaction',
            priority: satisfaction.overall_rating <= 3 ? 'urgent' : 'high',
            insights: insights,
            created_at: new Date().toISOString()
          });

        // Notify manager if critical
        if (satisfaction.overall_rating <= 3) {
          await this.sendCriticalSatisfactionAlert(satisfaction);
        }
      }
    } catch (error) {
      console.error('Error analyzing response:', error);
    }
  }

  /**
   * Send survey notification to client
   */
  static async sendSurveyNotification(survey: any): Promise<void> {
    try {
      await supabase
        .from('workflow_notifications')
        .insert({
          recipient_id: survey.client_id,
          notification_type: 'satisfaction_survey',
          title: 'Your Feedback is Important',
          message: 'Please take a moment to share your experience with our service',
          priority: 'medium',
          metadata: {
            survey_id: survey.id,
            project_id: survey.project_id,
            survey_type: survey.survey_type
          }
        });
    } catch (error) {
      console.error('Error sending survey notification:', error);
    }
  }

  /**
   * Send thank you notification
   */
  static async sendThankYouNotification(satisfaction: any): Promise<void> {
    try {
      await supabase
        .from('workflow_notifications')
        .insert({
          recipient_id: satisfaction.client_id,
          notification_type: 'survey_thank_you',
          title: 'Thank You for Your Feedback',
          message: 'We appreciate you taking the time to share your experience',
          priority: 'low',
          metadata: {
            satisfaction_id: satisfaction.id,
            project_id: satisfaction.project_id
          }
        });
    } catch (error) {
      console.error('Error sending thank you notification:', error);
    }
  }

  /**
   * Send critical satisfaction alert
   */
  static async sendCriticalSatisfactionAlert(satisfaction: any): Promise<void> {
    try {
      // Notify manager and admin
      const recipients = [satisfaction.manager_id];
      
      // Get admin users
      const { data: admins } = await supabase
        .from('profiles')
        .select('id')
        .eq('role', 'admin');

      if (admins) {
        recipients.push(...admins.map(admin => admin.id));
      }

      const notifications = recipients.filter(id => id).map(recipientId => ({
        recipient_id: recipientId,
        notification_type: 'critical_satisfaction_alert',
        title: 'Critical: Low Client Satisfaction',
        message: `Client satisfaction score of ${satisfaction.overall_rating}/10 requires immediate attention`,
        priority: 'urgent',
        metadata: {
          satisfaction_id: satisfaction.id,
          project_id: satisfaction.project_id,
          overall_rating: satisfaction.overall_rating
        }
      }));

      if (notifications.length > 0) {
        await supabase
          .from('workflow_notifications')
          .insert(notifications);
      }
    } catch (error) {
      console.error('Error sending critical satisfaction alert:', error);
    }
  }

  /**
   * Log satisfaction activity
   */
  static async logSatisfactionActivity(params: {
    survey_id?: string;
    satisfaction_id?: string;
    project_id: string;
    activity_type: string;
    description: string;
    performed_by: string;
    metadata?: any;
  }): Promise<void> {
    try {
      await supabase
        .from('satisfaction_activities')
        .insert({
          survey_id: params.survey_id,
          satisfaction_id: params.satisfaction_id,
          project_id: params.project_id,
          activity_type: params.activity_type,
          description: params.description,
          performed_by: params.performed_by,
          metadata: params.metadata || {}
        });
    } catch (error) {
      console.error('Error logging satisfaction activity:', error);
    }
  }
}

// Export convenience functions
export const createSatisfactionSurvey = ClientSatisfactionManager.createSatisfactionSurvey;
export const submitSatisfactionResponse = ClientSatisfactionManager.submitSatisfactionResponse;
export const getSatisfactionMetrics = ClientSatisfactionManager.getSatisfactionMetrics;
