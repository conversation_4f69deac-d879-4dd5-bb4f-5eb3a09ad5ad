-- =====================================================
-- STEP 1: FIX DUPLICATE PROPOSALS
-- Run this FIRST in Supabase Dashboard SQL Editor
-- =====================================================

-- First, let's see what duplicates we have
SELECT 
    designer_id, 
    brief_id, 
    COUNT(*) as proposal_count,
    array_agg(id ORDER BY created_at DESC) as proposal_ids,
    array_agg(status ORDER BY created_at DESC) as statuses
FROM project_proposals_enhanced 
WHERE brief_id IS NOT NULL
GROUP BY designer_id, brief_id 
HAVING COUNT(*) > 1
ORDER BY proposal_count DESC;

-- Now fix the duplicates by keeping the most recent and withdrawing others
UPDATE project_proposals_enhanced 
SET 
    status = 'withdrawn',
    updated_at = NOW()
WHERE id IN (
    SELECT id FROM (
        SELECT id, 
               ROW_NUMBER() OVER (PARTITION BY designer_id, brief_id ORDER BY created_at DESC) as rn
        FROM project_proposals_enhanced 
        WHERE brief_id IS NOT NULL
    ) ranked 
    WHERE rn > 1
);

-- Verify no duplicates remain
SELECT 
    designer_id, 
    brief_id, 
    COUNT(*) as proposal_count
FROM project_proposals_enhanced 
WHERE brief_id IS NOT NULL
  AND status != 'withdrawn'  -- Exclude withdrawn proposals
GROUP BY designer_id, brief_id 
HAVING COUNT(*) > 1;

-- This should return no rows if successful
