import type { Metada<PERSON> } from "next";
import "./globals.css";
import { NavbarProvider } from "@/contexts/NavbarContext";
import { PayPalProvider } from "@/components/providers/PayPalProvider";
import { QueryProvider } from "@/providers/QueryProvider";
import ClientBody from "./ClientBody";

export const metadata: Metadata = {
  title: "Senior's Archi-firm | Architecture that transcends boundaries",
  description: "We craft architecture that transcends boundaries, telling stories through form, space, and experience.",
  manifest: '/site.webmanifest',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* PayPal Performance Optimization */}
        <link rel="dns-prefetch" href="//www.paypal.com" />
        <link rel="preconnect" href="https://www.paypal.com" />
        <link rel="preconnect" href="https://api-m.sandbox.paypal.com" />
        <link rel="preconnect" href="https://api-m.paypal.com" />
      </head>
      <body className="antialiased">
        <ClientBody>
          <QueryProvider>
            <PayPalProvider>
              <NavbarProvider>
                {children}
              </NavbarProvider>
            </PayPalProvider>
          </QueryProvider>
        </ClientBody>
      </body>
    </html>
  );
}
