import { Metadata } from 'next';
import { supabase } from '@/lib/supabase';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

interface BlogPost {
  title: string;
  slug: string;
  excerpt: string;
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  tags: string[];
}

async function getBlogPost(slug: string): Promise<BlogPost | null> {
  const { data: post, error } = await supabase
    .from('blog_posts')
    .select('title, slug, excerpt, featured_image_url, meta_title, meta_description, tags')
    .eq('slug', slug)
    .eq('status', 'published')
    .single();

  if (error || !post) {
    return null;
  }

  return post;
}

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const post = await getBlogPost(params.slug);

  if (!post) {
    return generateSEOMetadata({
      title: "Post Not Found",
      description: "The requested blog post could not be found."
    });
  }

  return generateSEOMetadata({
    title: post.meta_title || post.title,
    description: post.meta_description || post.excerpt,
    path: `/blog/${post.slug}`,
    image: post.featured_image_url,
    keywords: post.tags,
    type: 'article'
  });
}

export default function BlogPostLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
