-- Create portfolio_projects table for designer portfolios
CREATE TABLE IF NOT EXISTS portfolio_projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  designer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT,
  client_name TEXT,
  completion_date DATE,
  featured BOOLEA<PERSON> DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create portfolio_images table for project images
CREATE TABLE IF NOT EXISTS portfolio_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES portfolio_projects(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  caption TEXT,
  display_order INTEGER DEFAULT 0,
  is_cover BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create portfolio_tags table for project tags
CREATE TABLE IF NOT EXISTS portfolio_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE
);

-- Create portfolio_project_tags junction table
CREATE TABLE IF NOT EXISTS portfolio_project_tags (
  project_id UUID NOT NULL REFERENCES portfolio_projects(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES portfolio_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (project_id, tag_id)
);

-- Add function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_portfolio_projects_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_portfolio_projects_updated_at
BEFORE UPDATE ON portfolio_projects
FOR EACH ROW
EXECUTE FUNCTION update_portfolio_projects_updated_at();

-- Add RLS policies for portfolio_projects table
ALTER TABLE portfolio_projects ENABLE ROW LEVEL SECURITY;

-- Designers can see their own portfolio projects
CREATE POLICY "Designers can see their own portfolio projects"
  ON portfolio_projects
  FOR SELECT
  USING (designer_id = auth.uid());

-- Designers can create their own portfolio projects
CREATE POLICY "Designers can create their own portfolio projects"
  ON portfolio_projects
  FOR INSERT
  WITH CHECK (designer_id = auth.uid());

-- Designers can update their own portfolio projects
CREATE POLICY "Designers can update their own portfolio projects"
  ON portfolio_projects
  FOR UPDATE
  USING (designer_id = auth.uid());

-- Designers can delete their own portfolio projects
CREATE POLICY "Designers can delete their own portfolio projects"
  ON portfolio_projects
  FOR DELETE
  USING (designer_id = auth.uid());

-- Admins can see all portfolio projects
CREATE POLICY "Admins can see all portfolio projects"
  ON portfolio_projects
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Admins can update all portfolio projects
CREATE POLICY "Admins can update all portfolio projects"
  ON portfolio_projects
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Clients can see portfolio projects
CREATE POLICY "Clients can see portfolio projects"
  ON portfolio_projects
  FOR SELECT
  USING (true);

-- Add RLS policies for portfolio_images table
ALTER TABLE portfolio_images ENABLE ROW LEVEL SECURITY;

-- Designers can see images for their own portfolio projects
CREATE POLICY "Designers can see their own portfolio images"
  ON portfolio_images
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE portfolio_projects.id = portfolio_images.project_id
      AND portfolio_projects.designer_id = auth.uid()
    )
  );

-- Designers can create images for their own portfolio projects
CREATE POLICY "Designers can create images for their own portfolio projects"
  ON portfolio_images
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE portfolio_projects.id = portfolio_images.project_id
      AND portfolio_projects.designer_id = auth.uid()
    )
  );

-- Designers can update images for their own portfolio projects
CREATE POLICY "Designers can update their own portfolio images"
  ON portfolio_images
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE portfolio_projects.id = portfolio_images.project_id
      AND portfolio_projects.designer_id = auth.uid()
    )
  );

-- Designers can delete images for their own portfolio projects
CREATE POLICY "Designers can delete their own portfolio images"
  ON portfolio_images
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE portfolio_projects.id = portfolio_images.project_id
      AND portfolio_projects.designer_id = auth.uid()
    )
  );

-- Admins can see all portfolio images
CREATE POLICY "Admins can see all portfolio images"
  ON portfolio_images
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Clients can see portfolio images
CREATE POLICY "Clients can see portfolio images"
  ON portfolio_images
  FOR SELECT
  USING (true);

-- Add RLS policies for portfolio_tags table
ALTER TABLE portfolio_tags ENABLE ROW LEVEL SECURITY;

-- Everyone can see tags
CREATE POLICY "Everyone can see tags"
  ON portfolio_tags
  FOR SELECT
  USING (true);

-- Only admins can create, update, or delete tags
CREATE POLICY "Admins can manage tags"
  ON portfolio_tags
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Add RLS policies for portfolio_project_tags table
ALTER TABLE portfolio_project_tags ENABLE ROW LEVEL SECURITY;

-- Designers can see tags for their own portfolio projects
CREATE POLICY "Designers can see tags for their own portfolio projects"
  ON portfolio_project_tags
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE portfolio_projects.id = portfolio_project_tags.project_id
      AND portfolio_projects.designer_id = auth.uid()
    )
  );

-- Designers can add tags to their own portfolio projects
CREATE POLICY "Designers can add tags to their own portfolio projects"
  ON portfolio_project_tags
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE portfolio_projects.id = portfolio_project_tags.project_id
      AND portfolio_projects.designer_id = auth.uid()
    )
  );

-- Designers can remove tags from their own portfolio projects
CREATE POLICY "Designers can remove tags from their own portfolio projects"
  ON portfolio_project_tags
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM portfolio_projects
      WHERE portfolio_projects.id = portfolio_project_tags.project_id
      AND portfolio_projects.designer_id = auth.uid()
    )
  );

-- Admins can see all portfolio project tags
CREATE POLICY "Admins can see all portfolio project tags"
  ON portfolio_project_tags
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Everyone else can see portfolio project tags
CREATE POLICY "Everyone can see portfolio project tags"
  ON portfolio_project_tags
  FOR SELECT
  USING (true);

-- Add availability toggle to profiles table if it doesn't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS availability BOOLEAN DEFAULT true;

-- Add predefined skills array to profiles table if it doesn't exist
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS predefined_skills TEXT[] DEFAULT NULL;
