import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile to determine role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const unreadOnly = url.searchParams.get('unread_only') === 'true';

    // Fetch admin messages for this user
    let query = supabase
      .from('admin_messages')
      .select(`
        id,
        title,
        content,
        message_type,
        priority,
        action_required,
        action_url,
        expires_at,
        created_at,
        read_at,
        created_by_profile:profiles!created_by(full_name)
      `)
      .or(`recipient_id.eq.${user.id},recipient_role.eq.${profile.role},recipient_role.eq.all`)
      .or(`expires_at.is.null,expires_at.gt.${new Date().toISOString()}`)
      .order('created_at', { ascending: false });

    if (unreadOnly) {
      query = query.is('read_at', null);
    }

    query = query.limit(limit);

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching admin messages:', error);
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      );
    }

    // Transform data
    const messages = (data || []).map(msg => ({
      id: msg.id,
      title: msg.title,
      content: msg.content,
      message_type: msg.message_type,
      priority: msg.priority,
      action_required: msg.action_required,
      action_url: msg.action_url,
      expires_at: msg.expires_at,
      created_at: msg.created_at,
      read_at: msg.read_at,
      created_by_name: Array.isArray(msg.created_by_profile) 
        ? msg.created_by_profile[0]?.full_name 
        : msg.created_by_profile?.full_name || 'Admin Team'
    }));

    return NextResponse.json({
      messages,
      unread_count: messages.filter(m => !m.read_at).length
    });

  } catch (error) {
    console.error('Error in user admin messages API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
