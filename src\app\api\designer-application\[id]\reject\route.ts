import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

/**
 * API route for rejecting designer applications
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { rejected_by, rejection_reason } = await request.json();

    if (!rejected_by) {
      return NextResponse.json(
        { error: 'rejected_by is required' },
        { status: 400 }
      );
    }

    // Get the application
    const { data: application, error: fetchError } = await supabase
      .from('designer_applications')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    if (application.application_status !== 'pending') {
      return NextResponse.json(
        { error: 'Application has already been processed' },
        { status: 400 }
      );
    }

    // Update application status
    const { error: updateError } = await supabase
      .from('designer_applications')
      .update({
        application_status: 'rejected',
        rejected_at: new Date().toISOString(),
        rejected_by: rejected_by,
        rejection_reason: rejection_reason || 'Application did not meet our current requirements'
      })
      .eq('id', id);

    if (updateError) {
      console.error('Error updating application:', updateError);
      return NextResponse.json(
        { error: 'Failed to update application status' },
        { status: 500 }
      );
    }

    // Send rejection email
    try {
      await resend.emails.send({
        from: 'Seniors Architecture Firm <<EMAIL>>',
        to: [application.email],
        subject: 'Update on Your Designer Application - Seniors Architecture Firm',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #8B4513;">Thank You for Your Interest</h2>

            <p>Dear ${application.full_name},</p>

            <p>Thank you for taking the time to apply for a designer position with Seniors Architecture Firm. We appreciate your interest in joining our team.</p>

            <p>After careful consideration of your application and portfolio, we have decided not to move forward with your application at this time.</p>

            ${rejection_reason ? `
              <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #6c757d;">
                <h4 style="margin-top: 0; color: #495057;">Feedback:</h4>
                <p style="margin-bottom: 0; color: #6c757d;">${rejection_reason}</p>
              </div>
            ` : ''}

            <p>This decision was not made lightly, and we recognize the effort you put into your application. While we cannot offer you a position at this time, we encourage you to:</p>

            <ul>
              <li>Continue developing your skills and portfolio</li>
              <li>Consider applying again in the future as our needs evolve</li>
              <li>Follow our company updates for new opportunities</li>
            </ul>

            <p>We wish you the best of luck in your career endeavors and hope you find an opportunity that's a perfect fit for your talents.</p>

            <p>Thank you again for your interest in Seniors Architecture Firm.</p>

            <p>Best regards,<br>
            The Seniors Architecture Firm Team</p>

            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
            <p style="font-size: 12px; color: #666;">
              This is an automated message. Please do not reply to this email. If you have questions, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
            </p>
          </div>
        `
      });
      console.log('Rejection email sent successfully');
    } catch (emailError) {
      console.error('Error sending rejection email:', emailError);
      // Don't fail the rejection if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Application rejected successfully'
    }, { status: 200 });

  } catch (error) {
    console.error('Error in reject application API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
