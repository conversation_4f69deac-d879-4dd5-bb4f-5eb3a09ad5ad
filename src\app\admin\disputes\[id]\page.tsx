'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { DisputeDetail } from '@/components/disputes/DisputeDetail';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from 'lucide-react';

interface AdminDisputePageProps {
  params: {
    id: string;
  };
}

export default function AdminDisputePage({ params }: AdminDisputePageProps) {
  const { user, profile, loading } = useOptimizedAuth();
  const router = useRouter();
  const { id } = params;

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    } else if (!loading && profile?.role !== 'admin') {
      router.push('/');
    }
  }, [user, profile, loading, router]);

  if (loading || !user) {
    return (
      <div className="p-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brown-600"></div>
        </div>
      </div>
    );
  }

  if (profile?.role !== 'admin') {
    return (
      <div className="p-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href="/admin/disputes">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to All Disputes
          </Link>
        </Button>
      </div>

      <DisputeDetail disputeId={id} userRole="admin" />
    </div>
  );
}
