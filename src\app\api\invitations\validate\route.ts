import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/invitations/validate
 * Validates an invitation code
 * 
 * Request body:
 * {
 *   inviteCode: string; // The invitation code to validate
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const { inviteCode } = await request.json();
    
    if (!inviteCode) {
      return NextResponse.json(
        { error: 'Invite code is required' },
        { status: 400 }
      );
    }
    
    // Check if the invitation exists, is pending, and has not expired
    const { data, error } = await supabase
      .from('invitations')
      .select(`
        id,
        invite_code,
        role,
        created_by,
        created_at,
        expires_at,
        profiles:created_by (
          id,
          full_name,
          role,
          avatar_url
        )
      `)
      .eq('invite_code', inviteCode)
      .eq('status', 'pending')
      .gt('expires_at', new Date().toISOString())
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned - invalid or expired invite
        return NextResponse.json(
          { valid: false, error: 'Invalid or expired invitation code' },
          { status: 404 }
        );
      }
      
      console.error('Error validating invitation:', error);
      return NextResponse.json(
        { valid: false, error: 'Failed to validate invitation' },
        { status: 500 }
      );
    }
    
    // Return the invitation details
    return NextResponse.json({
      valid: true,
      invitation: {
        id: data.id,
        inviteCode: data.invite_code,
        role: data.role,
        createdBy: data.created_by,
        createdAt: data.created_at,
        expiresAt: data.expires_at,
        inviter: data.profiles
      }
    }, { status: 200 });
  } catch (error) {
    console.error('Error validating invitation:', error);
    return NextResponse.json(
      { valid: false, error: 'Failed to validate invitation' },
      { status: 500 }
    );
  }
}
