import { createClient } from '@supabase/supabase-js';

// PayPal API base URL - use sandbox for development, change to live for production
const PAYPAL_API_BASE = process.env.NODE_ENV === 'production'
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

// Function to get PayPal access token
export async function getPayPalAccessToken() {
  const clientId = process.env.PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('PayPal credentials are not configured');
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
  
  const response = await fetch(`${PAYPAL_API_BASE}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${auth}`
    },
    body: 'grant_type=client_credentials'
  });

  if (!response.ok) {
    const errorData = await response.json();
    console.error('PayPal auth error:', errorData);
    throw new Error('Failed to authenticate with PayPal');
  }

  const data = await response.json();
  return data.access_token;
}

// Create a billing agreement with PayPal
export async function createBillingAgreement(userId: string, description: string) {
  const accessToken = await getPayPalAccessToken();
  
  const currentDate = new Date();
  const startDate = new Date(currentDate);
  startDate.setMinutes(currentDate.getMinutes() + 5); // Start date 5 minutes from now
  
  const payload = {
    description: description,
    payer: {
      payment_method: 'PAYPAL'
    },
    plan: {
      type: 'MERCHANT_INITIATED_BILLING',
      merchant_preferences: {
        return_url: `${process.env.NEXT_PUBLIC_SITE_URL}/client/payments?success=true`,
        cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/client/payments?canceled=true`,
        accepted_pymt_type: 'INSTANT',
        skip_shipping_address: true,
        immutable_shipping_address: true
      }
    }
  };
  
  const response = await fetch(`${PAYPAL_API_BASE}/v1/billing-agreements/agreements`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    console.error('PayPal billing agreement error:', errorData);
    throw new Error('Failed to create PayPal billing agreement');
  }
  
  const data = await response.json();
  return data;
}

// Execute a billing agreement after user approval
export async function executeBillingAgreement(token: string) {
  const accessToken = await getPayPalAccessToken();
  
  const response = await fetch(`${PAYPAL_API_BASE}/v1/billing-agreements/agreements/${token}/agreement-execute`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    }
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    console.error('PayPal execute billing agreement error:', errorData);
    throw new Error('Failed to execute PayPal billing agreement');
  }
  
  const data = await response.json();
  return data;
}

// Get billing agreement details
export async function getBillingAgreement(agreementId: string) {
  const accessToken = await getPayPalAccessToken();
  
  const response = await fetch(`${PAYPAL_API_BASE}/v1/billing-agreements/agreements/${agreementId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    }
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    console.error('PayPal get billing agreement error:', errorData);
    throw new Error('Failed to get PayPal billing agreement details');
  }
  
  const data = await response.json();
  return data;
}

// Save PayPal account to database
export async function savePayPalAccount(userId: string, paypalEmail: string, agreementId: string, isDefault: boolean) {
  // Initialize Supabase client with service role key
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false
      }
    }
  );
  
  // Check if this PayPal account is already saved
  const { data: existingMethods, error: fetchError } = await supabase
    .from('payment_methods')
    .select('id')
    .eq('user_id', userId)
    .eq('payment_type', 'paypal')
    .eq('paypal_email', paypalEmail);
    
  if (fetchError) {
    throw new Error(fetchError.message || 'Failed to check existing payment methods');
  }
  
  // If this PayPal account is already saved, don't add it again
  if (existingMethods && existingMethods.length > 0) {
    throw new Error('This PayPal account is already linked to your account');
  }
  
  // Save the PayPal account as a payment method
  const { error: insertError } = await supabase
    .from('payment_methods')
    .insert({
      user_id: userId,
      payment_type: 'paypal',
      paypal_email: paypalEmail,
      paypal_agreement_id: agreementId,
      is_default: isDefault,
      last_used_at: new Date().toISOString()
    });
    
  if (insertError) {
    throw new Error(insertError.message || 'Failed to save PayPal account');
  }
  
  // If this is set as default, update other payment methods
  if (isDefault) {
    const { error: updateError } = await supabase
      .from('payment_methods')
      .update({ is_default: false })
      .neq('paypal_email', paypalEmail)
      .eq('user_id', userId);
    
    if (updateError) {
      console.error('Error updating other payment methods:', updateError);
    }
  }
  
  return { success: true };
}
