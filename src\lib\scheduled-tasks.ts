'use client';

import { SLAMonitor } from './sla-monitor';
import { supabase } from './supabase';

/**
 * Scheduled Tasks Manager
 * Handles automated background tasks for quality workflow
 */

export class ScheduledTasksManager {
  private static intervals: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Start all scheduled tasks
   */
  static startAllTasks() {
    this.startSLAMonitoring();
    this.startWorkloadBalancing();
    this.startNotificationCleanup();
    
    console.log('✅ All scheduled tasks started');
  }

  /**
   * Stop all scheduled tasks
   */
  static stopAllTasks() {
    this.intervals.forEach((interval, taskName) => {
      clearInterval(interval);
      console.log(`🛑 Stopped task: ${taskName}`);
    });
    this.intervals.clear();
    
    console.log('🛑 All scheduled tasks stopped');
  }

  /**
   * Start SLA monitoring task (runs every 15 minutes)
   */
  static startSLAMonitoring() {
    const taskName = 'sla-monitoring';
    
    // Clear existing interval if any
    if (this.intervals.has(taskName)) {
      clearInterval(this.intervals.get(taskName)!);
    }

    const runSLACheck = async () => {
      try {
        console.log('🔍 Running SLA monitoring check...');
        
        // Check all SLAs and send alerts
        const slaStatuses = await SLAMonitor.checkAllSLAs();
        
        // Count different status types
        const stats = {
          total: slaStatuses.length,
          onTime: slaStatuses.filter(s => s.status === 'on_time').length,
          approaching: slaStatuses.filter(s => s.status === 'approaching').length,
          overdue: slaStatuses.filter(s => s.status === 'overdue').length,
          critical: slaStatuses.filter(s => s.status === 'critical').length
        };

        console.log('📊 SLA Check Results:', stats);

        // Run escalation check for severely overdue reviews
        if (stats.critical > 0) {
          try {
            const { data: escalationResult } = await supabase
              .rpc('escalate_overdue_reviews');
            
            if (escalationResult && escalationResult > 0) {
              console.log(`🚨 Escalated ${escalationResult} critical reviews`);
            }
          } catch (escalationError) {
            console.error('Error running escalation check:', escalationError);
          }
        }

        // Log task completion
        await this.logTaskExecution(taskName, 'success', {
          ...stats,
          escalations: stats.critical
        });

      } catch (error) {
        console.error('❌ SLA monitoring task failed:', error);
        await this.logTaskExecution(taskName, 'error', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };

    // Run immediately, then every 15 minutes
    runSLACheck();
    const interval = setInterval(runSLACheck, 15 * 60 * 1000);
    this.intervals.set(taskName, interval);
    
    console.log('✅ SLA monitoring task started (15 min intervals)');
  }

  /**
   * Start workload balancing task (runs every hour)
   */
  static startWorkloadBalancing() {
    const taskName = 'workload-balancing';
    
    // Clear existing interval if any
    if (this.intervals.has(taskName)) {
      clearInterval(this.intervals.get(taskName)!);
    }

    const runWorkloadBalancing = async () => {
      try {
        console.log('⚖️ Running workload balancing...');
        
        // Get current workload stats
        const { data: workloadStats, error } = await supabase
          .rpc('get_quality_team_workload');

        if (error) throw error;

        if (!workloadStats || workloadStats.length === 0) {
          console.log('ℹ️ No quality team members found');
          return;
        }

        // Check for unassigned reviews
        const { data: unassignedReviews, error: reviewsError } = await supabase
          .from('quality_reviews_new')
          .select('id, priority, review_type, created_at')
          .eq('status', 'pending')
          .is('reviewer_id', null)
          .order('created_at', { ascending: true });

        if (reviewsError) throw reviewsError;

        let reassignedCount = 0;

        // Try to assign unassigned reviews
        if (unassignedReviews && unassignedReviews.length > 0) {
          for (const review of unassignedReviews) {
            try {
              const { data: assignedReviewer } = await supabase
                .rpc('auto_assign_quality_review', {
                  review_id: review.id,
                  review_type: review.review_type,
                  urgency: review.priority
                });

              if (assignedReviewer) {
                reassignedCount++;
              }
            } catch (assignError) {
              console.error(`Failed to assign review ${review.id}:`, assignError);
            }
          }
        }

        // Calculate workload distribution
        const totalWorkload = workloadStats.reduce((sum: number, member: any) => 
          sum + member.current_workload, 0);
        const averageWorkload = totalWorkload / workloadStats.length;
        const maxWorkload = Math.max(...workloadStats.map((m: any) => m.current_workload));
        const minWorkload = Math.min(...workloadStats.map((m: any) => m.current_workload));

        console.log('📊 Workload Distribution:', {
          totalReviews: totalWorkload,
          averagePerReviewer: Math.round(averageWorkload * 10) / 10,
          maxWorkload,
          minWorkload,
          unassignedReviews: unassignedReviews?.length || 0,
          reassignedCount
        });

        // Log task completion
        await this.logTaskExecution(taskName, 'success', {
          totalReviews: totalWorkload,
          averageWorkload: Math.round(averageWorkload * 10) / 10,
          unassignedReviews: unassignedReviews?.length || 0,
          reassignedCount
        });

      } catch (error) {
        console.error('❌ Workload balancing task failed:', error);
        await this.logTaskExecution(taskName, 'error', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };

    // Run immediately, then every hour
    runWorkloadBalancing();
    const interval = setInterval(runWorkloadBalancing, 60 * 60 * 1000);
    this.intervals.set(taskName, interval);
    
    console.log('✅ Workload balancing task started (1 hour intervals)');
  }

  /**
   * Start notification cleanup task (runs daily)
   */
  static startNotificationCleanup() {
    const taskName = 'notification-cleanup';
    
    // Clear existing interval if any
    if (this.intervals.has(taskName)) {
      clearInterval(this.intervals.get(taskName)!);
    }

    const runNotificationCleanup = async () => {
      try {
        console.log('🧹 Running notification cleanup...');
        
        // Delete old notifications (older than 30 days)
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 30);

        const { data: deletedNotifications, error: notifError } = await supabase
          .from('workflow_notifications')
          .delete()
          .lt('created_at', cutoffDate.toISOString())
          .select('id');

        if (notifError) throw notifError;

        // Delete old SLA alerts (older than 7 days)
        const alertCutoffDate = new Date();
        alertCutoffDate.setDate(alertCutoffDate.getDate() - 7);

        const { data: deletedAlerts, error: alertError } = await supabase
          .from('sla_alerts')
          .delete()
          .lt('sent_at', alertCutoffDate.toISOString())
          .select('id');

        if (alertError) throw alertError;

        console.log('🧹 Cleanup Results:', {
          deletedNotifications: deletedNotifications?.length || 0,
          deletedAlerts: deletedAlerts?.length || 0
        });

        // Log task completion
        await this.logTaskExecution(taskName, 'success', {
          deletedNotifications: deletedNotifications?.length || 0,
          deletedAlerts: deletedAlerts?.length || 0
        });

      } catch (error) {
        console.error('❌ Notification cleanup task failed:', error);
        await this.logTaskExecution(taskName, 'error', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };

    // Run immediately, then every 24 hours
    runNotificationCleanup();
    const interval = setInterval(runNotificationCleanup, 24 * 60 * 60 * 1000);
    this.intervals.set(taskName, interval);
    
    console.log('✅ Notification cleanup task started (24 hour intervals)');
  }

  /**
   * Log task execution for monitoring
   */
  static async logTaskExecution(
    taskName: string, 
    status: 'success' | 'error', 
    metadata: any
  ) {
    try {
      await supabase
        .from('scheduled_task_logs')
        .insert({
          task_name: taskName,
          status,
          executed_at: new Date().toISOString(),
          metadata
        });
    } catch (error) {
      console.error('Failed to log task execution:', error);
    }
  }

  /**
   * Get task execution history
   */
  static async getTaskHistory(taskName?: string, limit: number = 50) {
    try {
      let query = supabase
        .from('scheduled_task_logs')
        .select('*')
        .order('executed_at', { ascending: false })
        .limit(limit);

      if (taskName) {
        query = query.eq('task_name', taskName);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error fetching task history:', error);
      return [];
    }
  }

  /**
   * Check if tasks are running
   */
  static getRunningTasks(): string[] {
    return Array.from(this.intervals.keys());
  }

  /**
   * Restart a specific task
   */
  static restartTask(taskName: string) {
    switch (taskName) {
      case 'sla-monitoring':
        this.startSLAMonitoring();
        break;
      case 'workload-balancing':
        this.startWorkloadBalancing();
        break;
      case 'notification-cleanup':
        this.startNotificationCleanup();
        break;
      default:
        console.error(`Unknown task: ${taskName}`);
    }
  }
}

// Auto-start tasks when module is imported (client-side only)
if (typeof window !== 'undefined') {
  // Start tasks after a short delay to ensure everything is initialized
  setTimeout(() => {
    ScheduledTasksManager.startAllTasks();
  }, 5000);

  // Stop tasks when page is unloaded
  window.addEventListener('beforeunload', () => {
    ScheduledTasksManager.stopAllTasks();
  });
}

// Export convenience functions
export const startScheduledTasks = ScheduledTasksManager.startAllTasks;
export const stopScheduledTasks = ScheduledTasksManager.stopAllTasks;
export const getTaskHistory = ScheduledTasksManager.getTaskHistory;
