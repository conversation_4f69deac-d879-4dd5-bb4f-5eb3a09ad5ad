import crypto from 'crypto';

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 12; // For GCM, recommended is 12 bytes
const TAG_LENGTH = 16; // GCM authentication tag length
const KEY_LENGTH = 32; // 256 bits

/**
 * Get the current encryption key from environment variables
 * Falls back to backup key if primary is not available
 */
function getEncryptionKey(): Buffer {
  const primaryKey = process.env.ENCRYPTION_KEY;
  const backupKey = process.env.ENCRYPTION_KEY_BACKUP;
  
  if (!primaryKey && !backupKey) {
    throw new Error('No encryption key found in environment variables');
  }
  
  // Use primary key if available, otherwise backup
  const keyString = primaryKey || backupKey;
  
  // Ensure key is exactly 32 bytes (256 bits)
  const key = crypto.createHash('sha256').update(keyString!).digest();
  return key;
}

/**
 * Encrypt sensitive data using AES-256-GCM
 * Returns base64 encoded string with format: iv:tag:encryptedData
 */
export function encryptSensitiveData(plaintext: string): string {
  if (!plaintext || plaintext.trim() === '') {
    return '';
  }

  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    cipher.setAAD(Buffer.from('payment-data')); // Additional authenticated data

    let encrypted = cipher.update(plaintext, 'utf8');
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    const tag = cipher.getAuthTag();

    // Combine iv, tag, and encrypted data
    const combined = Buffer.concat([iv, tag, encrypted]);
    return combined.toString('base64');

  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt sensitive data');
  }
}

/**
 * Decrypt sensitive data using AES-256-GCM
 * Expects base64 encoded string with format: iv:tag:encryptedData
 */
export function decryptSensitiveData(encryptedData: string): string {
  if (!encryptedData || encryptedData.trim() === '') {
    return '';
  }

  try {
    const key = getEncryptionKey();
    const combined = Buffer.from(encryptedData, 'base64');

    // Extract components
    const iv = combined.subarray(0, IV_LENGTH);
    const tag = combined.subarray(IV_LENGTH, IV_LENGTH + TAG_LENGTH);
    const encrypted = combined.subarray(IV_LENGTH + TAG_LENGTH);

    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAuthTag(tag);
    decipher.setAAD(Buffer.from('payment-data')); // Same AAD used in encryption

    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;

  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt sensitive data');
  }
}

/**
 * Mask sensitive data for display purposes
 * Shows only last 4 characters with asterisks
 */
export function maskSensitiveData(data: string, visibleChars: number = 4): string {
  if (!data || data.length <= visibleChars) {
    return '*'.repeat(data?.length || 4);
  }
  
  const masked = '*'.repeat(data.length - visibleChars);
  const visible = data.slice(-visibleChars);
  return masked + visible;
}

/**
 * Validate encryption key strength
 */
export function validateEncryptionKey(key: string): boolean {
  if (!key || key.length < 32) {
    return false;
  }
  
  // Check for common weak patterns
  const weakPatterns = [
    /^(.)\1+$/, // All same character
    /^(012|123|234|345|456|567|678|789|890)+/, // Sequential numbers
    /^(abc|def|ghi|jkl|mno|pqr|stu|vwx|yz)+/i, // Sequential letters
  ];
  
  return !weakPatterns.some(pattern => pattern.test(key));
}

/**
 * Generate a secure encryption key for environment setup
 * This should only be used during initial setup
 */
export function generateSecureKey(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Encrypt multiple fields in a payment method object
 */
export function encryptPaymentMethodData(data: {
  account_number?: string;
  routing_number?: string;
  iban?: string;
  swift_code?: string;
  sort_code?: string;
  bsb_number?: string;
  account_holder_name?: string;
}): typeof data {
  const encrypted = { ...data };

  // Fields that need encryption
  const sensitiveFields = [
    'account_number',
    'routing_number',
    'iban',
    'swift_code',
    'sort_code',
    'bsb_number'
  ] as const;

  sensitiveFields.forEach(field => {
    if (encrypted[field]) {
      encrypted[field] = encryptSensitiveData(encrypted[field]!);
    }
  });

  return encrypted;
}

/**
 * Decrypt multiple fields in a payment method object
 */
export function decryptPaymentMethodData(data: {
  account_number?: string;
  routing_number?: string;
  iban?: string;
  swift_code?: string;
  sort_code?: string;
  bsb_number?: string;
  account_holder_name?: string;
}): typeof data {
  const decrypted = { ...data };

  // Fields that need decryption
  const sensitiveFields = [
    'account_number',
    'routing_number',
    'iban',
    'swift_code',
    'sort_code',
    'bsb_number'
  ] as const;

  sensitiveFields.forEach(field => {
    if (decrypted[field]) {
      try {
        decrypted[field] = decryptSensitiveData(decrypted[field]!);
      } catch (error) {
        console.error(`Failed to decrypt ${field}:`, error);
        // Keep encrypted value if decryption fails
      }
    }
  });

  return decrypted;
}
