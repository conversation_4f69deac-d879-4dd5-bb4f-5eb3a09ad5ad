import { NextRequest, NextResponse } from 'next/server';
import { supabaseServerClient } from '@/lib/supabase-server';

/**
 * API route for managing tracking requests (admin)
 */
export async function GET(request: NextRequest) {
  try {
    // TODO: Implement proper admin authentication
    console.log('Admin tracking API called');

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const request_type = searchParams.get('request_type');
    const assigned_to = searchParams.get('assigned_to');
    const priority = searchParams.get('priority');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Query parameters:', {
      status,
      request_type,
      assigned_to,
      priority,
      limit,
      offset
    });

    // Build query - start with basic fields to avoid join issues
    console.log('Building query for tracking_requests table...');

    let query = supabaseServerClient
      .from('tracking_requests')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status && status !== 'all') {
      console.log('Filtering by status:', status);

      // Handle special case: 'new' filter should include 'submitted' requests
      if (status === 'new') {
        // Show requests that are either:
        // 1. internal_status = 'new', OR
        // 2. status = 'submitted' (newly submitted requests), OR
        // 3. status = 'new' (if any exist)
        query = query.or(`internal_status.eq.new,status.eq.submitted,status.eq.new`);
      } else {
        // For other statuses, check both status and internal_status fields
        query = query.or(`status.eq.${status},internal_status.eq.${status}`);
      }
    }
    if (request_type && request_type !== 'all') {
      console.log('Filtering by request_type:', request_type);
      query = query.eq('request_type', request_type);
    }
    if (assigned_to && assigned_to !== 'all') {
      console.log('Filtering by assigned_to:', assigned_to);
      query = query.eq('assigned_to', assigned_to);
    }
    if (priority && priority !== 'all') {
      console.log('Filtering by priority:', priority);
      query = query.eq('priority', priority);
    }

    console.log('Executing query...');
    const { data: requests, error } = await query;

    if (error) {
      console.error('Error fetching tracking requests:', error);
      console.error('Error details:', error.message, error.details, error.hint);

      // If table doesn't exist, return empty results
      if (error.code === '42P01') {
        console.warn('tracking_requests table does not exist, returning empty results');
        return NextResponse.json({
          requests: [],
          total: 0,
          limit,
          offset,
          message: 'Database table not found - using mock data'
        });
      }

      return NextResponse.json({
        error: 'Failed to fetch tracking requests',
        details: error.message
      }, { status: 500 });
    }

    console.log(`Found ${requests?.length || 0} tracking requests`);

    // Get total count for pagination
    console.log('Getting total count...');
    let countQuery = supabaseServerClient
      .from('tracking_requests')
      .select('*', { count: 'exact', head: true });

    // Apply same filters for count
    if (status && status !== 'all') {
      // Handle special case: 'new' filter should include 'submitted' requests
      if (status === 'new') {
        countQuery = countQuery.or(`internal_status.eq.new,status.eq.submitted,status.eq.new`);
      } else {
        countQuery = countQuery.or(`status.eq.${status},internal_status.eq.${status}`);
      }
    }
    if (request_type && request_type !== 'all') {
      countQuery = countQuery.eq('request_type', request_type);
    }
    if (assigned_to && assigned_to !== 'all') {
      countQuery = countQuery.eq('assigned_to', assigned_to);
    }
    if (priority && priority !== 'all') {
      countQuery = countQuery.eq('priority', priority);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error getting count:', countError);
      // Continue with requests even if count fails
    }

    console.log(`Total count: ${count || 0}`);

    return NextResponse.json({
      requests,
      total: count,
      limit,
      offset
    });

  } catch (error) {
    console.error('Error in GET /api/admin/tracking:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH - Update tracking request
 */
export async function PATCH(request: NextRequest) {
  try {
    const {
      tracking_id,
      status,
      internal_status,
      assigned_to,
      priority,
      admin_notes,
      estimated_completion_date,
      follow_up_required,
      next_follow_up_date,
      tags
    } = await request.json();

    // TODO: Implement proper admin authentication
    console.log('Admin tracking PATCH API called');

    if (!tracking_id) {
      return NextResponse.json({ error: 'Tracking ID is required' }, { status: 400 });
    }

    // Build update object
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (status !== undefined) updateData.status = status;
    if (internal_status !== undefined) updateData.internal_status = internal_status;
    if (assigned_to !== undefined) updateData.assigned_to = assigned_to;
    if (priority !== undefined) updateData.priority = priority;
    if (admin_notes !== undefined) updateData.admin_notes = admin_notes;
    if (estimated_completion_date !== undefined) updateData.estimated_completion_date = estimated_completion_date;
    if (follow_up_required !== undefined) updateData.follow_up_required = follow_up_required;
    if (next_follow_up_date !== undefined) updateData.next_follow_up_date = next_follow_up_date;
    if (tags !== undefined) updateData.tags = tags;

    // Update tracking request
    const { data: updatedRequest, error: updateError } = await supabaseServerClient
      .from('tracking_requests')
      .update(updateData)
      .eq('id', tracking_id)
      .select(`
        *,
        assigned_to_profile:profiles!assigned_to(full_name, email)
      `)
      .single();

    if (updateError) {
      console.error('Error updating tracking request:', updateError);
      return NextResponse.json({ error: 'Failed to update tracking request' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      request: updatedRequest,
      message: 'Tracking request updated successfully'
    });

  } catch (error) {
    console.error('Error in PATCH /api/admin/tracking:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
