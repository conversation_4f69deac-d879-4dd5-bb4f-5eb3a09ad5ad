-- =====================================================
-- SCRIPT 19: COMPREHENSIVE QUALITY TEAM FIXES
-- =====================================================

-- 1. STANDARDIZE TABLE NAMES AND ADD MISSING COLUMNS
-- =====================================================

-- Check if quality_reviews_new exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quality_reviews_new') THEN
        -- Create quality_reviews_new table with all required columns
        CREATE TABLE quality_reviews_new (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
            submission_id UUID,
            milestone_id UUID REFERENCES project_milestones(id) ON DELETE CASCADE,
            reviewer_id UUID REFERENCES profiles(id),
            designer_id UUID REFERENCES profiles(id),
            review_type VARCHAR(50) DEFAULT 'submission' CHECK (review_type IN ('proposal', 'submission', 'revision', 'final')),
            status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'assigned', 'in_review', 'approved', 'rejected', 'needs_revision', 'escalated')),
            priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
            overall_score INTEGER CHECK (overall_score >= 1 AND overall_score <= 5),
            standards_checked JSONB,
            feedback TEXT,
            revision_notes TEXT,
            revision_count INTEGER DEFAULT 0,
            time_spent_minutes INTEGER,
            sla_deadline TIMESTAMP WITH TIME ZONE,
            escalated_at TIMESTAMP WITH TIME ZONE,
            assigned_at TIMESTAMP WITH TIME ZONE,
            reviewed_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        RAISE NOTICE '✅ Created quality_reviews_new table with all required columns';
    ELSE
        RAISE NOTICE '✅ quality_reviews_new table already exists';
    END IF;
END $$;

-- Add missing columns to existing quality_reviews_new table
DO $$
BEGIN
    -- Add priority column if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'quality_reviews_new' AND column_name = 'priority'
    ) THEN
        ALTER TABLE quality_reviews_new 
        ADD COLUMN priority VARCHAR(20) DEFAULT 'normal' 
        CHECK (priority IN ('low', 'normal', 'high', 'urgent'));
        
        RAISE NOTICE '✅ Added priority column to quality_reviews_new';
    ELSE
        RAISE NOTICE '✅ priority column already exists in quality_reviews_new';
    END IF;
    
    -- Add escalated_at column if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'quality_reviews_new' AND column_name = 'escalated_at'
    ) THEN
        ALTER TABLE quality_reviews_new 
        ADD COLUMN escalated_at TIMESTAMP WITH TIME ZONE;
        
        RAISE NOTICE '✅ Added escalated_at column to quality_reviews_new';
    ELSE
        RAISE NOTICE '✅ escalated_at column already exists in quality_reviews_new';
    END IF;
    
    -- Add assigned_at column if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'quality_reviews_new' AND column_name = 'assigned_at'
    ) THEN
        ALTER TABLE quality_reviews_new 
        ADD COLUMN assigned_at TIMESTAMP WITH TIME ZONE;
        
        RAISE NOTICE '✅ Added assigned_at column to quality_reviews_new';
    ELSE
        RAISE NOTICE '✅ assigned_at column already exists in quality_reviews_new';
    END IF;
    
    -- Update status constraint to include 'escalated'
    BEGIN
        ALTER TABLE quality_reviews_new DROP CONSTRAINT IF EXISTS quality_reviews_new_status_check;
        ALTER TABLE quality_reviews_new 
        ADD CONSTRAINT quality_reviews_new_status_check 
        CHECK (status IN ('pending', 'assigned', 'in_review', 'approved', 'rejected', 'needs_revision', 'escalated'));
        
        RAISE NOTICE '✅ Updated status constraint to include escalated';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ Status constraint update failed or already exists';
    END;
END $$;

-- 2. CREATE QUALITY FEEDBACK TABLE IF MISSING
-- =====================================================

CREATE TABLE IF NOT EXISTS quality_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID REFERENCES quality_reviews_new(id) ON DELETE CASCADE,
    standard_id UUID REFERENCES quality_standards(id),
    passed BOOLEAN NOT NULL,
    score INTEGER CHECK (score >= 1 AND score <= 5),
    comments TEXT,
    suggestions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. CREATE QUALITY STANDARDS TABLE IF MISSING
-- =====================================================

CREATE TABLE IF NOT EXISTS quality_standards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    standard_name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    criteria JSONB NOT NULL,
    is_mandatory BOOLEAN DEFAULT TRUE,
    weight INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES profiles(id)
);

-- 4. ADD QUALITY TEAM COLUMNS TO PROFILES TABLE
-- =====================================================

DO $$
BEGIN
    -- Add quality team specific columns to profiles
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'quality_specializations'
    ) THEN
        ALTER TABLE profiles ADD COLUMN quality_specializations JSONB DEFAULT '[]';
        RAISE NOTICE '✅ Added quality_specializations column to profiles';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'quality_current_workload'
    ) THEN
        ALTER TABLE profiles ADD COLUMN quality_current_workload INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Added quality_current_workload column to profiles';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'quality_max_workload'
    ) THEN
        ALTER TABLE profiles ADD COLUMN quality_max_workload INTEGER DEFAULT 5;
        RAISE NOTICE '✅ Added quality_max_workload column to profiles';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'quality_is_available'
    ) THEN
        ALTER TABLE profiles ADD COLUMN quality_is_available BOOLEAN DEFAULT TRUE;
        RAISE NOTICE '✅ Added quality_is_available column to profiles';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'quality_last_assignment'
    ) THEN
        ALTER TABLE profiles ADD COLUMN quality_last_assignment TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE '✅ Added quality_last_assignment column to profiles';
    END IF;
END $$;

-- 5. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_project_id ON quality_reviews_new(project_id);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_reviewer_id ON quality_reviews_new(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_designer_id ON quality_reviews_new(designer_id);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_status ON quality_reviews_new(status);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_priority ON quality_reviews_new(priority);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_created_at ON quality_reviews_new(created_at);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_sla_deadline ON quality_reviews_new(sla_deadline);
CREATE INDEX IF NOT EXISTS idx_quality_reviews_new_escalated_at ON quality_reviews_new(escalated_at);

CREATE INDEX IF NOT EXISTS idx_quality_feedback_review_id ON quality_feedback(review_id);
CREATE INDEX IF NOT EXISTS idx_quality_standards_category ON quality_standards(category);

CREATE INDEX IF NOT EXISTS idx_profiles_quality_team ON profiles(role) WHERE role = 'quality_team';
CREATE INDEX IF NOT EXISTS idx_profiles_quality_available ON profiles(quality_is_available) WHERE quality_is_available = true;

-- 6. DISABLE RLS AND GRANT PERMISSIONS
-- =====================================================

ALTER TABLE quality_reviews_new DISABLE ROW LEVEL SECURITY;
ALTER TABLE quality_feedback DISABLE ROW LEVEL SECURITY;
ALTER TABLE quality_standards DISABLE ROW LEVEL SECURITY;

GRANT ALL ON quality_reviews_new TO authenticated;
GRANT ALL ON quality_feedback TO authenticated;
GRANT ALL ON quality_standards TO authenticated;

-- 7. INSERT DEFAULT QUALITY STANDARDS
-- =====================================================

INSERT INTO quality_standards (category, standard_name, description, criteria, is_mandatory, weight) VALUES
('design', 'Visual Hierarchy', 'Clear visual hierarchy and information architecture', '["Clear focal points", "Logical flow", "Appropriate typography scale"]', true, 5),
('design', 'Brand Consistency', 'Adherence to brand guidelines and style', '["Correct colors", "Proper fonts", "Brand voice consistency"]', true, 5),
('design', 'Technical Quality', 'Technical execution and file quality', '["Proper resolution", "Clean vectors", "Organized layers"]', true, 4),
('web', 'Responsive Design', 'Mobile and tablet compatibility', '["Mobile responsive", "Touch-friendly", "Cross-browser compatible"]', true, 5),
('web', 'Performance', 'Loading speed and optimization', '["Optimized images", "Clean code", "Fast loading"]', true, 4),
('print', 'Print Specifications', 'Correct print setup and specifications', '["CMYK colors", "Proper bleeds", "High resolution"]', true, 5)
ON CONFLICT DO NOTHING;

-- 8. CREATE/UPDATE QUALITY WORKFLOW FUNCTIONS
-- =====================================================

-- Function to assign quality review to available reviewer
CREATE OR REPLACE FUNCTION assign_quality_review(review_id UUID)
RETURNS UUID AS $$
DECLARE
  selected_reviewer_id UUID;
  reviewer_record RECORD;
  best_score NUMERIC := 0;
  current_score NUMERIC;
BEGIN
  -- Find the best available reviewer
  FOR reviewer_record IN
    SELECT
      id,
      quality_current_workload,
      quality_max_workload,
      quality_specializations,
      quality_last_assignment
    FROM profiles
    WHERE role = 'quality_team'
      AND quality_is_available = true
      AND COALESCE(quality_current_workload, 0) < COALESCE(quality_max_workload, 5)
  LOOP
    -- Calculate score for this reviewer
    current_score := 0;

    -- Workload factor (prefer less loaded reviewers)
    current_score := current_score + (1.0 - (COALESCE(reviewer_record.quality_current_workload, 0)::NUMERIC / COALESCE(reviewer_record.quality_max_workload, 5)::NUMERIC)) * 40;

    -- Time since last assignment (prefer reviewers who haven't been assigned recently)
    IF reviewer_record.quality_last_assignment IS NULL THEN
      current_score := current_score + 30;
    ELSE
      current_score := current_score + LEAST(30, EXTRACT(EPOCH FROM (NOW() - reviewer_record.quality_last_assignment)) / 3600);
    END IF;

    -- Random factor for fairness
    current_score := current_score + (RANDOM() * 10);

    -- Select best reviewer
    IF current_score > best_score THEN
      best_score := current_score;
      selected_reviewer_id := reviewer_record.id;
    END IF;
  END LOOP;

  -- If we found a reviewer, assign the review
  IF selected_reviewer_id IS NOT NULL THEN
    UPDATE quality_reviews_new
    SET
      reviewer_id = selected_reviewer_id,
      status = 'assigned',
      assigned_at = NOW(),
      sla_deadline = NOW() + INTERVAL '24 hours'
    WHERE id = review_id;

    -- Update reviewer workload and last assignment
    UPDATE profiles
    SET
      quality_current_workload = COALESCE(quality_current_workload, 0) + 1,
      quality_last_assignment = NOW()
    WHERE id = selected_reviewer_id;
  END IF;

  RETURN selected_reviewer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete quality review
CREATE OR REPLACE FUNCTION complete_quality_review(
  review_id UUID,
  reviewer_id UUID,
  overall_score INTEGER,
  feedback_text TEXT,
  review_status TEXT DEFAULT 'approved'
)
RETURNS BOOLEAN AS $$
DECLARE
  review_record RECORD;
BEGIN
  -- Get the review record
  SELECT * INTO review_record FROM quality_reviews_new WHERE id = review_id;

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Update the review
  UPDATE quality_reviews_new
  SET
    status = review_status,
    overall_score = overall_score,
    feedback = feedback_text,
    reviewed_at = NOW(),
    updated_at = NOW()
  WHERE id = review_id AND reviewer_id = reviewer_id;

  -- Decrement reviewer workload
  UPDATE profiles
  SET quality_current_workload = GREATEST(0, COALESCE(quality_current_workload, 1) - 1)
  WHERE id = reviewer_id;

  -- If revision needed, increment revision count
  IF review_status = 'needs_revision' THEN
    UPDATE quality_reviews_new
    SET revision_count = revision_count + 1
    WHERE id = review_id;
  END IF;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to escalate overdue reviews
CREATE OR REPLACE FUNCTION escalate_overdue_reviews()
RETURNS INTEGER AS $$
DECLARE
  escalated_count INTEGER := 0;
  review_record RECORD;
BEGIN
  -- Find reviews that are overdue by more than 24 hours
  FOR review_record IN
    SELECT id, project_id, designer_id, reviewer_id
    FROM quality_reviews_new
    WHERE status IN ('pending', 'assigned', 'in_review')
      AND sla_deadline < NOW() - INTERVAL '24 hours'
      AND escalated_at IS NULL
  LOOP
    -- Mark as escalated
    UPDATE quality_reviews_new
    SET
      status = 'escalated',
      escalated_at = NOW(),
      priority = 'urgent',
      updated_at = NOW()
    WHERE id = review_record.id;

    escalated_count := escalated_count + 1;
  END LOOP;

  RETURN escalated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function for auto-assignment
CREATE OR REPLACE FUNCTION trigger_auto_assign_review()
RETURNS TRIGGER AS $$
BEGIN
  -- Auto-assign review to available reviewer
  PERFORM assign_quality_review(NEW.id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for auto-assignment
DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review ON quality_reviews_new;
CREATE TRIGGER trigger_auto_assign_quality_review
  AFTER INSERT ON quality_reviews_new
  FOR EACH ROW
  EXECUTE FUNCTION trigger_auto_assign_review();

-- 9. CREATE ENHANCED ESCALATION SYSTEM
-- =====================================================

-- Function to check and escalate overdue reviews
CREATE OR REPLACE FUNCTION check_and_escalate_overdue_reviews()
RETURNS TABLE(escalated_count INTEGER, notification_count INTEGER) AS $$
DECLARE
  escalated_reviews INTEGER := 0;
  notifications_sent INTEGER := 0;
  review_record RECORD;
  manager_id UUID;
BEGIN
  -- Find reviews that are overdue by more than 4 hours
  FOR review_record IN
    SELECT id, project_id, designer_id, reviewer_id, sla_deadline, priority
    FROM quality_reviews_new
    WHERE status IN ('pending', 'assigned', 'in_review')
      AND sla_deadline < NOW() - INTERVAL '4 hours'
      AND escalated_at IS NULL
  LOOP
    -- Mark as escalated
    UPDATE quality_reviews_new
    SET
      status = 'escalated',
      escalated_at = NOW(),
      priority = 'urgent',
      updated_at = NOW()
    WHERE id = review_record.id;

    escalated_reviews := escalated_reviews + 1;

    -- Find assigned manager for this project
    SELECT pa.manager_id INTO manager_id
    FROM project_assignments pa
    WHERE pa.project_id = review_record.project_id
      AND pa.status = 'active'
    LIMIT 1;

    -- Create notification for manager if found
    IF manager_id IS NOT NULL THEN
      INSERT INTO workflow_notifications (
        recipient_id,
        notification_type,
        title,
        message,
        priority,
        data
      ) VALUES (
        manager_id,
        'quality_escalation',
        'Quality Review Escalated',
        'A quality review has been escalated due to SLA breach. Immediate attention required.',
        'urgent',
        jsonb_build_object(
          'review_id', review_record.id,
          'project_id', review_record.project_id,
          'designer_id', review_record.designer_id,
          'reviewer_id', review_record.reviewer_id
        )
      );

      notifications_sent := notifications_sent + 1;
    END IF;

    -- Also notify admin users
    INSERT INTO workflow_notifications (
      recipient_id,
      notification_type,
      title,
      message,
      priority,
      data
    )
    SELECT
      p.id,
      'quality_escalation',
      'Quality Review Escalated',
      'A quality review has been escalated due to SLA breach.',
      'urgent',
      jsonb_build_object(
        'review_id', review_record.id,
        'project_id', review_record.project_id,
        'designer_id', review_record.designer_id,
        'reviewer_id', review_record.reviewer_id
      )
    FROM profiles p
    WHERE p.role = 'admin';

    notifications_sent := notifications_sent + (SELECT COUNT(*) FROM profiles WHERE role = 'admin');
  END LOOP;

  RETURN QUERY SELECT escalated_reviews, notifications_sent;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send SLA warning notifications
CREATE OR REPLACE FUNCTION send_sla_warnings()
RETURNS INTEGER AS $$
DECLARE
  warnings_sent INTEGER := 0;
  review_record RECORD;
BEGIN
  -- Find reviews that will be due in 2 hours
  FOR review_record IN
    SELECT id, project_id, designer_id, reviewer_id, sla_deadline
    FROM quality_reviews_new
    WHERE status IN ('pending', 'assigned', 'in_review')
      AND sla_deadline BETWEEN NOW() AND NOW() + INTERVAL '2 hours'
      AND escalated_at IS NULL
  LOOP
    -- Send warning to reviewer
    IF review_record.reviewer_id IS NOT NULL THEN
      INSERT INTO workflow_notifications (
        recipient_id,
        notification_type,
        title,
        message,
        priority,
        data
      ) VALUES (
        review_record.reviewer_id,
        'sla_warning',
        'Quality Review Due Soon',
        'You have a quality review that is due within 2 hours. Please complete it to avoid SLA breach.',
        'high',
        jsonb_build_object(
          'review_id', review_record.id,
          'project_id', review_record.project_id,
          'sla_deadline', review_record.sla_deadline
        )
      );

      warnings_sent := warnings_sent + 1;
    END IF;
  END LOOP;

  RETURN warnings_sent;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to auto-reassign escalated reviews
CREATE OR REPLACE FUNCTION auto_reassign_escalated_reviews()
RETURNS INTEGER AS $$
DECLARE
  reassigned_count INTEGER := 0;
  review_record RECORD;
  new_reviewer_id UUID;
BEGIN
  -- Find escalated reviews that haven't been reassigned
  FOR review_record IN
    SELECT id, project_id, designer_id, reviewer_id
    FROM quality_reviews_new
    WHERE status = 'escalated'
      AND escalated_at < NOW() - INTERVAL '1 hour'
      AND reviewer_id IS NOT NULL
  LOOP
    -- Try to find a different available reviewer
    SELECT id INTO new_reviewer_id
    FROM profiles
    WHERE role = 'quality_team'
      AND quality_is_available = true
      AND id != review_record.reviewer_id
      AND COALESCE(quality_current_workload, 0) < COALESCE(quality_max_workload, 5)
    ORDER BY COALESCE(quality_current_workload, 0) ASC, quality_last_assignment ASC NULLS FIRST
    LIMIT 1;

    -- If we found a new reviewer, reassign
    IF new_reviewer_id IS NOT NULL THEN
      -- Update old reviewer workload
      UPDATE profiles
      SET quality_current_workload = GREATEST(0, COALESCE(quality_current_workload, 1) - 1)
      WHERE id = review_record.reviewer_id;

      -- Update new reviewer workload
      UPDATE profiles
      SET
        quality_current_workload = COALESCE(quality_current_workload, 0) + 1,
        quality_last_assignment = NOW()
      WHERE id = new_reviewer_id;

      -- Update review
      UPDATE quality_reviews_new
      SET
        reviewer_id = new_reviewer_id,
        status = 'assigned',
        assigned_at = NOW(),
        sla_deadline = NOW() + INTERVAL '12 hours', -- Shorter deadline for escalated
        updated_at = NOW()
      WHERE id = review_record.id;

      reassigned_count := reassigned_count + 1;

      -- Notify new reviewer
      INSERT INTO workflow_notifications (
        recipient_id,
        notification_type,
        title,
        message,
        priority,
        data
      ) VALUES (
        new_reviewer_id,
        'review_reassignment',
        'Escalated Review Assigned',
        'You have been assigned an escalated quality review. Please complete it urgently.',
        'urgent',
        jsonb_build_object(
          'review_id', review_record.id,
          'project_id', review_record.project_id,
          'escalated', true
        )
      );
    END IF;
  END LOOP;

  RETURN reassigned_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a comprehensive SLA monitoring function
CREATE OR REPLACE FUNCTION monitor_quality_sla()
RETURNS TABLE(
  total_reviews INTEGER,
  overdue_reviews INTEGER,
  due_soon_reviews INTEGER,
  escalated_reviews INTEGER,
  warnings_sent INTEGER,
  escalations_created INTEGER,
  reassignments_made INTEGER
) AS $$
DECLARE
  result_warnings INTEGER;
  result_escalated INTEGER;
  result_notifications INTEGER;
  result_reassignments INTEGER;
BEGIN
  -- Send SLA warnings
  SELECT send_sla_warnings() INTO result_warnings;

  -- Check and escalate overdue reviews
  SELECT escalated_count, notification_count
  INTO result_escalated, result_notifications
  FROM check_and_escalate_overdue_reviews();

  -- Auto-reassign escalated reviews
  SELECT auto_reassign_escalated_reviews() INTO result_reassignments;

  -- Return comprehensive stats
  RETURN QUERY
  SELECT
    (SELECT COUNT(*)::INTEGER FROM quality_reviews_new WHERE status IN ('pending', 'assigned', 'in_review', 'escalated')),
    (SELECT COUNT(*)::INTEGER FROM quality_reviews_new WHERE status IN ('pending', 'assigned', 'in_review') AND sla_deadline < NOW()),
    (SELECT COUNT(*)::INTEGER FROM quality_reviews_new WHERE status IN ('pending', 'assigned', 'in_review') AND sla_deadline BETWEEN NOW() AND NOW() + INTERVAL '4 hours'),
    (SELECT COUNT(*)::INTEGER FROM quality_reviews_new WHERE status = 'escalated'),
    result_warnings,
    result_escalated,
    result_reassignments;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

SELECT 'Quality Team comprehensive escalation system completed successfully!' as status;
