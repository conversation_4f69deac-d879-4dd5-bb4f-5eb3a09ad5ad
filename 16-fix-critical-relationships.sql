-- =====================================================
-- SCRIPT 16: FIX CRITICAL RELATIONSHIP ISSUES
-- =====================================================

-- 1. Add missing escrow_hold_id column to paypal_escrow_releases
ALTER TABLE paypal_escrow_releases 
ADD COLUMN IF NOT EXISTS escrow_hold_id UUID REFERENCES paypal_escrow_holds(id) ON DELETE CASCADE;

-- 2. Clean up duplicate RLS policies on platform_fee_settings
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage fee settings" ON platform_fee_settings;

-- 3. Create platform_settings table if it doesn't exist (to fix 406 error)
CREATE TABLE IF NOT EXISTS platform_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  platform_name TEXT NOT NULL DEFAULT 'Design Platform',
  maintenance_mode BOOLEAN NOT NULL DEFAULT false,
  registration_enabled BOOLEAN NOT NULL DEFAULT true,
  email_notifications_enabled BOOLEAN NOT NULL DEFAULT true,
  auto_assignment_enabled BOOLEAN NOT NULL DEFAULT false,
  max_projects_per_designer INTEGER DEFAULT 5,
  default_project_timeline_days INTEGER DEFAULT 30,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES profiles(id)
);

-- Insert default platform settings (using default values)
INSERT INTO platform_settings DEFAULT VALUES
ON CONFLICT DO NOTHING;

-- 4. Enable RLS and add policies for platform_settings
ALTER TABLE platform_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated users to read platform settings"
ON platform_settings FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Allow admins to manage platform settings"
ON platform_settings FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- 5. Grant permissions
GRANT SELECT, INSERT, UPDATE ON platform_settings TO authenticated;

-- 6. Create indexes
CREATE INDEX IF NOT EXISTS idx_paypal_escrow_releases_escrow_hold_id ON paypal_escrow_releases(escrow_hold_id);
CREATE INDEX IF NOT EXISTS idx_platform_settings_updated_at ON platform_settings(updated_at DESC);

-- Verify completion
SELECT 'Script 16 completed: Critical relationships fixed' as status;
