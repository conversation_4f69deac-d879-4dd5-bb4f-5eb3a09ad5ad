-- =====================================================
-- SCRIPT 10: CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update designer_payout_rate when commission or processing fee changes
CREATE OR REPLACE FUNCTION update_designer_payout_rate()
RETURNS TRIGGER AS $$
BEGIN
  NEW.designer_payout_rate := 100.0 - NEW.platform_commission_rate - NEW.payment_processing_fee;
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_designer_payout_rate
  BEFORE UPDATE ON platform_fee_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_designer_payout_rate();

-- Update project_briefs updated_at timestamp
CREATE OR REPLACE FUNCTION update_project_briefs_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_project_briefs_timestamp
  BEFORE UPDATE ON project_briefs
  FOR EACH ROW
  EXECUTE FUNCTION update_project_briefs_timestamp();

-- Update designer_payouts updated_at timestamp
CREATE OR REPLACE FUNCTION update_designer_payouts_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_designer_payouts_timestamp
  BEFORE UPDATE ON designer_payouts
  FOR EACH ROW
  EXECUTE FUNCTION update_designer_payouts_timestamp();

-- Verify completion
SELECT 'Script 10 completed: Triggers created' as status;
