-- =====================================================
-- QUALITY WORKFLOW FUNCTIONS - SUPABASE COMPATIBLE
-- Simplified functions that work with Supabase permissions
-- =====================================================

-- Note: In Supabase, some functions may need to be created through the Dashboard
-- or with elevated permissions. This script contains the essential functions.

-- 1. BASIC UTILITY FUNCTIONS
-- =====================================================

-- Function to increment reviewer workload (simplified)
CREATE OR REPLACE FUNCTION increment_reviewer_workload(
  reviewer_id UUID,
  increment_by INTEGER DEFAULT 1
)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles 
  SET quality_current_workload = COALESCE(quality_current_workload, 0) + increment_by
  WHERE id = reviewer_id AND role = 'quality_team';
END;
$$ LANGUAGE plpgsql;

-- Function to decrement reviewer workload (simplified)
CREATE OR REPLACE FUNCTION decrement_reviewer_workload(
  reviewer_id UUID,
  decrement_by INTEGER DEFAULT 1
)
RETURNS VOID AS $$
BEGIN
  UPDATE profiles 
  SET quality_current_workload = GREATEST(0, COALESCE(quality_current_workload, 0) - decrement_by)
  WHERE id = reviewer_id AND role = 'quality_team';
END;
$$ LANGUAGE plpgsql;

-- 2. ESCROW UTILITY FUNCTIONS
-- =====================================================

-- Function to get escrow account summary (simplified)
CREATE OR REPLACE FUNCTION get_escrow_account_summary(
  input_account_id UUID
)
RETURNS TABLE(
  account_id UUID,
  project_title TEXT,
  client_name TEXT,
  designer_name TEXT,
  manager_name TEXT,
  total_held DECIMAL(10,2),
  total_released DECIMAL(10,2),
  active_holds_count INTEGER,
  pending_releases_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    ea.id,
    COALESCE(p.title, 'Unknown Project'),
    COALESCE(c.full_name, 'Unknown Client'),
    COALESCE(d.full_name, 'Unknown Designer'),
    COALESCE(m.full_name, 'No Manager'),
    ea.total_held,
    ea.total_released,
    (SELECT COUNT(*)::INTEGER FROM escrow_holds WHERE escrow_account_id = ea.id AND status = 'active'),
    (SELECT COUNT(*)::INTEGER FROM escrow_releases WHERE escrow_account_id = ea.id AND status = 'pending')
  FROM escrow_accounts ea
  LEFT JOIN projects p ON ea.project_id = p.id
  LEFT JOIN profiles c ON ea.client_id = c.id
  LEFT JOIN profiles d ON ea.designer_id = d.id
  LEFT JOIN profiles m ON ea.manager_id = m.id
  WHERE ea.id = input_account_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check if escrow release can be processed (simplified)
CREATE OR REPLACE FUNCTION can_process_escrow_release(
  release_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  release_record RECORD;
BEGIN
  SELECT 
    manager_approval_status,
    quality_approval_status,
    status
  INTO release_record
  FROM escrow_releases 
  WHERE id = release_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Check if all required approvals are complete
  RETURN (
    release_record.status = 'pending' AND
    (release_record.manager_approval_status = 'approved' OR release_record.manager_approval_status = 'not_required') AND
    (release_record.quality_approval_status = 'approved' OR release_record.quality_approval_status = 'not_required')
  );
END;
$$ LANGUAGE plpgsql;

-- 3. DASHBOARD DATA FUNCTIONS
-- =====================================================

-- Function to get manager escrow dashboard data (simplified)
CREATE OR REPLACE FUNCTION get_manager_escrow_dashboard(
  input_manager_id UUID
)
RETURNS TABLE(
  total_projects INTEGER,
  total_held_amount DECIMAL(10,2),
  pending_approvals INTEGER,
  processed_releases INTEGER,
  disputed_holds INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(DISTINCT ea.project_id)::INTEGER,
    COALESCE(SUM(ea.total_held), 0),
    COUNT(CASE WHEN er.manager_approval_status = 'pending' THEN 1 END)::INTEGER,
    COUNT(CASE WHEN er.status = 'processed' THEN 1 END)::INTEGER,
    COUNT(CASE WHEN eh.status = 'disputed' THEN 1 END)::INTEGER
  FROM escrow_accounts ea
  LEFT JOIN escrow_releases er ON ea.id = er.escrow_account_id
  LEFT JOIN escrow_holds eh ON ea.id = eh.escrow_account_id
  WHERE ea.manager_id = input_manager_id OR er.manager_id = input_manager_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get quality team workload stats (simplified)
CREATE OR REPLACE FUNCTION get_quality_team_workload()
RETURNS TABLE(
  reviewer_id UUID,
  full_name TEXT,
  current_workload INTEGER,
  max_workload INTEGER,
  workload_percentage NUMERIC,
  is_available BOOLEAN,
  specializations TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    COALESCE(p.full_name, 'Unknown'),
    COALESCE(p.quality_current_workload, 0),
    COALESCE(p.quality_max_workload, 5),
    ROUND((COALESCE(p.quality_current_workload, 0)::NUMERIC / COALESCE(p.quality_max_workload, 5)::NUMERIC) * 100, 1),
    COALESCE(p.quality_is_available, false),
    COALESCE(p.quality_specializations, ARRAY[]::TEXT[])
  FROM profiles p
  WHERE p.role = 'quality_team'
  ORDER BY p.full_name;
END;
$$ LANGUAGE plpgsql;

-- 4. SLA COMPLIANCE FUNCTION (SIMPLIFIED)
-- =====================================================

-- Function to get SLA compliance stats (simplified)
CREATE OR REPLACE FUNCTION get_sla_compliance_stats(
  start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
  end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
  total_reviews INTEGER,
  on_time_reviews INTEGER,
  overdue_reviews INTEGER,
  average_completion_hours NUMERIC,
  sla_compliance_percentage NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_reviews,
    COUNT(CASE WHEN reviewed_at <= sla_deadline THEN 1 END)::INTEGER as on_time_reviews,
    COUNT(CASE WHEN reviewed_at > sla_deadline OR (reviewed_at IS NULL AND sla_deadline < NOW()) THEN 1 END)::INTEGER as overdue_reviews,
    ROUND(AVG(EXTRACT(EPOCH FROM (COALESCE(reviewed_at, NOW()) - created_at)) / 3600), 1) as average_completion_hours,
    ROUND(
      (COUNT(CASE WHEN reviewed_at <= sla_deadline THEN 1 END)::NUMERIC / NULLIF(COUNT(*), 0)) * 100, 
      1
    ) as sla_compliance_percentage
  FROM quality_reviews_new
  WHERE created_at::DATE BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- 5. GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions to authenticated users
DO $$
BEGIN
    -- Grant permissions on functions
    GRANT EXECUTE ON FUNCTION increment_reviewer_workload TO authenticated;
    GRANT EXECUTE ON FUNCTION decrement_reviewer_workload TO authenticated;
    GRANT EXECUTE ON FUNCTION get_escrow_account_summary TO authenticated;
    GRANT EXECUTE ON FUNCTION can_process_escrow_release TO authenticated;
    GRANT EXECUTE ON FUNCTION get_manager_escrow_dashboard TO authenticated;
    GRANT EXECUTE ON FUNCTION get_quality_team_workload TO authenticated;
    GRANT EXECUTE ON FUNCTION get_sla_compliance_stats TO authenticated;
    
    RAISE NOTICE '✅ Granted execute permissions on all functions';
EXCEPTION
    WHEN others THEN
        RAISE NOTICE '⚠️ Could not grant some permissions: %', SQLERRM;
END $$;

-- 6. SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 QUALITY WORKFLOW FUNCTIONS CREATED SUCCESSFULLY!';
    RAISE NOTICE '';
    RAISE NOTICE 'Functions created:';
    RAISE NOTICE '✅ increment_reviewer_workload';
    RAISE NOTICE '✅ decrement_reviewer_workload';
    RAISE NOTICE '✅ get_escrow_account_summary';
    RAISE NOTICE '✅ can_process_escrow_release';
    RAISE NOTICE '✅ get_manager_escrow_dashboard';
    RAISE NOTICE '✅ get_quality_team_workload';
    RAISE NOTICE '✅ get_sla_compliance_stats';
    RAISE NOTICE '';
    RAISE NOTICE 'Note: Some advanced functions may need to be created';
    RAISE NOTICE 'through the Supabase Dashboard with elevated permissions.';
    RAISE NOTICE '';
END $$;
