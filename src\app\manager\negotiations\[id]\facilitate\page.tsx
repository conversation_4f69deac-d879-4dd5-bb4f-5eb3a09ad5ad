"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Users,
  MessageSquare,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  Target,
  FileText,
  Send,
  AlertTriangle,
  ThumbsUp,
  ThumbsDown,
  Settings,
  Gavel,
  RefreshCw
} from "lucide-react";

interface NegotiationSession {
  id: string;
  project_id: string;
  session_type: string;
  status: string;
  terms: any;
  priority: string;
  description: string;
  project: {
    title: string;
    budget: number;
  };
  client: {
    id: string;
    full_name: string;
    email: string;
  };
  designer: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface Proposal {
  id: string;
  session_id: string;
  proposer_id: string;
  proposer_role: string;
  proposal_type: string;
  proposal_data: any;
  status: string;
  created_at: string;
  proposer: {
    full_name: string;
  };
}

export default function FacilitateNegotiationPage() {
  const { user, profile } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const sessionId = params.id as string;
  
  const [session, setSession] = useState<NegotiationSession | null>(null);
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [facilitationNote, setFacilitationNote] = useState('');
  const [sendingNote, setSendingNote] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'manager' && sessionId) {
      fetchSessionData();
    }
  }, [user, profile, sessionId]);

  const fetchSessionData = async () => {
    try {
      // Fetch session
      const { data: sessionData, error: sessionError } = await supabase
        .from('negotiation_sessions')
        .select(`
          *,
          project:projects(title, budget),
          client:profiles!negotiation_sessions_client_id_fkey(id, full_name, email),
          designer:profiles!negotiation_sessions_designer_id_fkey(id, full_name, email)
        `)
        .eq('id', sessionId)
        .eq('manager_id', user?.id)
        .single();

      if (sessionError) throw sessionError;
      setSession(sessionData);

      // Fetch proposals
      const { data: proposalsData, error: proposalsError } = await supabase
        .from('negotiation_proposals')
        .select(`
          *,
          proposer:profiles!negotiation_proposals_proposer_id_fkey(full_name)
        `)
        .eq('session_id', sessionId)
        .order('created_at', { ascending: false });

      if (proposalsError) throw proposalsError;
      setProposals(proposalsData || []);

    } catch (error) {
      console.error('Error fetching session data:', error);
      router.push('/manager/negotiations');
    } finally {
      setLoading(false);
    }
  };

  const handleProposal = async (proposalId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      const { error } = await supabase
        .from('negotiation_proposals')
        .update({
          status: action === 'approve' ? 'approved' : 'rejected',
          manager_notes: reason,
          reviewed_at: new Date().toISOString(),
          reviewed_by: user?.id
        })
        .eq('id', proposalId);

      if (error) throw error;

      // Log activity
      await supabase.from('manager_activities').insert({
        manager_id: user?.id,
        project_id: session?.project_id,
        activity_type: 'proposal_review',
        description: `${action === 'approve' ? 'Approved' : 'Rejected'} negotiation proposal`,
        outcome: action
      });

      // Send notification to proposer
      const proposal = proposals.find(p => p.id === proposalId);
      if (proposal) {
        await supabase.from('notifications').insert({
          user_id: proposal.proposer_id,
          type: 'proposal_reviewed',
          title: `Proposal ${action === 'approve' ? 'Approved' : 'Rejected'}`,
          message: `Your ${proposal.proposal_type} proposal has been ${action}d by the manager`,
          data: { session_id: sessionId, proposal_id: proposalId }
        });
      }

      fetchSessionData();
    } catch (error) {
      console.error('Error handling proposal:', error);
    }
  };

  const sendFacilitationNote = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!facilitationNote.trim() || !session) return;

    setSendingNote(true);
    try {
      // Send as negotiation message
      const { error } = await supabase
        .from('negotiation_messages')
        .insert({
          session_id: sessionId,
          sender_id: user?.id,
          sender_role: 'manager',
          message: facilitationNote.trim(),
          message_type: 'facilitation'
        });

      if (error) throw error;

      // Send notifications
      await supabase.from('notifications').insert([
        {
          user_id: session.client.id,
          type: 'facilitation_note',
          title: 'Manager Facilitation Note',
          message: 'The manager has provided guidance for the negotiation',
          data: { session_id: sessionId }
        },
        {
          user_id: session.designer.id,
          type: 'facilitation_note',
          title: 'Manager Facilitation Note',
          message: 'The manager has provided guidance for the negotiation',
          data: { session_id: sessionId }
        }
      ]);

      setFacilitationNote('');
      alert('Facilitation note sent successfully!');
    } catch (error) {
      console.error('Error sending facilitation note:', error);
    } finally {
      setSendingNote(false);
    }
  };

  const createCounterProposal = async (originalProposal: Proposal) => {
    const counterData = prompt('Enter counter-proposal details:');
    if (!counterData) return;

    try {
      const { error } = await supabase
        .from('negotiation_proposals')
        .insert({
          session_id: sessionId,
          proposer_id: user?.id,
          proposer_role: 'manager',
          proposal_type: originalProposal.proposal_type,
          proposal_data: { 
            ...originalProposal.proposal_data,
            counter_proposal: counterData,
            original_proposal_id: originalProposal.id
          },
          status: 'pending'
        });

      if (error) throw error;

      // Notify participants
      await supabase.from('notifications').insert([
        {
          user_id: session?.client.id,
          type: 'counter_proposal',
          title: 'Manager Counter-Proposal',
          message: `Manager has made a counter-proposal for ${originalProposal.proposal_type}`,
          data: { session_id: sessionId }
        },
        {
          user_id: session?.designer.id,
          type: 'counter_proposal',
          title: 'Manager Counter-Proposal',
          message: `Manager has made a counter-proposal for ${originalProposal.proposal_type}`,
          data: { session_id: sessionId }
        }
      ]);

      fetchSessionData();
    } catch (error) {
      console.error('Error creating counter-proposal:', error);
    }
  };

  const getProposalIcon = (type: string) => {
    switch (type) {
      case 'pricing':
        return <DollarSign className="h-5 w-5 text-green-500" />;
      case 'timeline':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'scope':
        return <Target className="h-5 w-5 text-purple-500" />;
      case 'terms':
        return <FileText className="h-5 w-5 text-amber-500" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded";
    switch (status) {
      case 'pending':
        return `${baseClasses} bg-amber-100 text-amber-800`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Session Not Found</h2>
          <p className="text-gray-600 mb-4">The negotiation session could not be found.</p>
          <Button onClick={() => router.push('/manager/negotiations')}>
            Back to Negotiations
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-3">
          <Gavel className="h-8 w-8 text-brown-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Facilitate Negotiation</h1>
            <p className="text-gray-600 mt-1">{session.project.title} - {session.session_type} negotiation</p>
          </div>
        </div>
      </div>

      {/* Session Overview */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Session Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-semibold text-blue-900">Participants</h3>
            <p className="text-sm text-blue-700 mt-1">
              {session.client.full_name} (Client)<br />
              {session.designer.full_name} (Designer)
            </p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <h3 className="font-semibold text-green-900">Project Budget</h3>
            <p className="text-lg font-bold text-green-700">${session.project.budget.toLocaleString()}</p>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <Target className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <h3 className="font-semibold text-purple-900">Session Type</h3>
            <p className="text-lg font-bold text-purple-700 capitalize">{session.session_type}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Proposals */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Active Proposals</h2>

            {proposals.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No proposals submitted yet</p>
                <p className="text-sm text-gray-400 mt-1">
                  Participants will submit proposals that you can review and facilitate
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {proposals.map((proposal) => (
                  <div key={proposal.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getProposalIcon(proposal.proposal_type)}
                        <div>
                          <h3 className="font-semibold text-gray-900 capitalize">
                            {proposal.proposal_type} Proposal
                          </h3>
                          <p className="text-sm text-gray-600">
                            By {proposal.proposer.full_name} ({proposal.proposer_role})
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={getStatusBadge(proposal.status)}>
                          {proposal.status.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(proposal.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>

                    {/* Proposal Details */}
                    <div className="bg-gray-50 rounded-lg p-3 mb-3">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(proposal.proposal_data, null, 2)}
                      </pre>
                    </div>

                    {/* Actions */}
                    {proposal.status === 'pending' && (
                      <div className="flex gap-3">
                        <Button
                          size="sm"
                          onClick={() => handleProposal(proposal.id, 'approve')}
                          className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                        >
                          <ThumbsUp className="h-4 w-4" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const reason = prompt('Reason for rejection (optional):');
                            handleProposal(proposal.id, 'reject', reason || undefined);
                          }}
                          className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <ThumbsDown className="h-4 w-4" />
                          Reject
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => createCounterProposal(proposal)}
                          className="flex items-center gap-2"
                        >
                          <MessageSquare className="h-4 w-4" />
                          Counter-Propose
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Facilitation Tools */}
        <div className="space-y-6">
          {/* Send Facilitation Note */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Send Guidance</h3>
            <form onSubmit={sendFacilitationNote} className="space-y-4">
              <textarea
                value={facilitationNote}
                onChange={(e) => setFacilitationNote(e.target.value)}
                placeholder="Provide guidance, suggestions, or mediation to help resolve the negotiation..."
                rows={4}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
              />
              <Button
                type="submit"
                disabled={!facilitationNote.trim() || sendingNote}
                className="w-full flex items-center justify-center gap-2"
              >
                {sendingNote ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Send className="h-4 w-4" />
                )}
                Send Guidance
              </Button>
            </form>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full flex items-center gap-2"
                onClick={() => router.push(`/manager/negotiations/${sessionId}`)}
              >
                <MessageSquare className="h-4 w-4" />
                View Full Session
              </Button>
              <Button
                variant="outline"
                className="w-full flex items-center gap-2"
                onClick={() => router.push(`/manager/projects/${session.project_id}`)}
              >
                <FileText className="h-4 w-4" />
                View Project
              </Button>
              <Button
                variant="outline"
                className="w-full flex items-center gap-2"
                onClick={() => {
                  if (confirm('Are you sure you want to close this negotiation session?')) {
                    // Handle session closure
                  }
                }}
              >
                <XCircle className="h-4 w-4" />
                Close Session
              </Button>
            </div>
          </div>

          {/* Facilitation Tips */}
          <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">Facilitation Tips</h3>
            <div className="text-blue-800 space-y-2 text-sm">
              <p>• <strong>Stay Neutral:</strong> Focus on finding mutually beneficial solutions</p>
              <p>• <strong>Ask Questions:</strong> Help clarify positions and underlying interests</p>
              <p>• <strong>Suggest Alternatives:</strong> Propose creative solutions when parties are stuck</p>
              <p>• <strong>Set Deadlines:</strong> Keep negotiations moving forward with clear timelines</p>
              <p>• <strong>Document Agreements:</strong> Ensure all decisions are clearly recorded</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
